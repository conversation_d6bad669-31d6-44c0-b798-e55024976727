import {
  pluginId
} from "./chunk-H4GPAAVJ.js";
import {
  ForwardRef$1 as ForwardRef$12,
  ForwardRef$11 as ForwardRef$112,
  ForwardRef$13 as ForwardRef$132,
  ForwardRef$3,
  ForwardRef$5 as ForwardRef$52,
  ForwardRef$9 as ForwardRef$92,
  ForwardRef$D as ForwardRef$D2,
  ForwardRef$N as ForwardRef$N2,
  ForwardRef$P as ForwardRef$P2,
  ForwardRef$V as ForwardRef$V2,
  ForwardRef$X as ForwardRef$X2,
  ForwardRef$Z,
  ForwardRef$b as ForwardRef$b2,
  ForwardRef$h as ForwardRef$h2,
  ForwardRef$j,
  ForwardRef$l as ForwardRef$l2,
  ForwardRef$n as ForwardRef$n2,
  ForwardRef$p as ForwardRef$p2,
  ForwardRef$r as ForwardRef$r2
} from "./chunk-7LKLOY7A.js";
import {
  useStrapiApp
} from "./chunk-ODQFI753.js";
import {
  Badge,
  Box,
  Typography
} from "./chunk-7GC3Y62Q.js";
import {
  ForwardRef$$,
  ForwardRef$1,
  ForwardRef$11,
  ForwardRef$13,
  ForwardRef$15,
  ForwardRef$17,
  ForwardRef$19,
  ForwardRef$1B,
  ForwardRef$1H,
  ForwardRef$1J,
  ForwardRef$1L,
  ForwardRef$1N,
  ForwardRef$1R,
  ForwardRef$1X,
  ForwardRef$1Z,
  ForwardRef$1b,
  ForwardRef$1d,
  ForwardRef$1h,
  ForwardRef$1j,
  ForwardRef$1l,
  ForwardRef$1n,
  ForwardRef$1p,
  ForwardRef$1r,
  ForwardRef$1t,
  ForwardRef$1v,
  ForwardRef$1z,
  ForwardRef$23,
  ForwardRef$25,
  ForwardRef$27,
  ForwardRef$29,
  ForwardRef$2B,
  ForwardRef$2D,
  ForwardRef$2J,
  ForwardRef$2L,
  ForwardRef$2N,
  ForwardRef$2P,
  ForwardRef$2R,
  ForwardRef$2b,
  ForwardRef$2d,
  ForwardRef$2f,
  ForwardRef$2h,
  ForwardRef$2n,
  ForwardRef$2r,
  ForwardRef$2v,
  ForwardRef$2x,
  ForwardRef$2z,
  ForwardRef$3$,
  ForwardRef$33,
  ForwardRef$35,
  ForwardRef$37,
  ForwardRef$39,
  ForwardRef$3D,
  ForwardRef$3H,
  ForwardRef$3J,
  ForwardRef$3L,
  ForwardRef$3P,
  ForwardRef$3X,
  ForwardRef$3Z,
  ForwardRef$3d,
  ForwardRef$3f,
  ForwardRef$3h,
  ForwardRef$3j,
  ForwardRef$3p,
  ForwardRef$3r,
  ForwardRef$3v,
  ForwardRef$3x,
  ForwardRef$3z,
  ForwardRef$4$,
  ForwardRef$41,
  ForwardRef$47,
  ForwardRef$49,
  ForwardRef$4F,
  ForwardRef$4H,
  ForwardRef$4J,
  ForwardRef$4L,
  ForwardRef$4N,
  ForwardRef$4P,
  ForwardRef$4V,
  ForwardRef$4X,
  ForwardRef$4Z,
  ForwardRef$4b,
  ForwardRef$4d,
  ForwardRef$4f,
  ForwardRef$4j,
  ForwardRef$4n,
  ForwardRef$4r,
  ForwardRef$5,
  ForwardRef$51,
  ForwardRef$53,
  ForwardRef$55,
  ForwardRef$59,
  ForwardRef$5b,
  ForwardRef$5d,
  ForwardRef$5j,
  ForwardRef$5l,
  ForwardRef$5n,
  ForwardRef$5p,
  ForwardRef$5r,
  ForwardRef$7,
  ForwardRef$9,
  ForwardRef$B,
  ForwardRef$D,
  ForwardRef$F,
  ForwardRef$H,
  ForwardRef$N,
  ForwardRef$P,
  ForwardRef$R,
  ForwardRef$T,
  ForwardRef$V,
  ForwardRef$X,
  ForwardRef$b,
  ForwardRef$f,
  ForwardRef$h,
  ForwardRef$l,
  ForwardRef$n,
  ForwardRef$p,
  ForwardRef$r,
  ForwardRef$t,
  ForwardRef$v,
  ForwardRef$x
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-type-builder/dist/admin/utils/getTrad.mjs
var getTrad = (id) => `${pluginId}.${id}`;

// node_modules/@strapi/content-type-builder/dist/admin/components/DataManager/useDataManager.mjs
var import_react2 = __toESM(require_react(), 1);

// node_modules/@strapi/content-type-builder/dist/admin/components/DataManager/DataManagerContext.mjs
var import_react = __toESM(require_react(), 1);
var DataManagerContext = (0, import_react.createContext)();

// node_modules/@strapi/content-type-builder/dist/admin/components/DataManager/useDataManager.mjs
var useDataManager = () => (0, import_react2.useContext)(DataManagerContext);

// node_modules/@strapi/content-type-builder/dist/admin/components/FormModalNavigation/useFormModalNavigation.mjs
var import_react3 = __toESM(require_react(), 1);

// node_modules/@strapi/content-type-builder/dist/admin/components/FormModalNavigation/FormModalNavigationContext.mjs
var React = __toESM(require_react(), 1);
var FormModalNavigationContext = React.createContext();

// node_modules/@strapi/content-type-builder/dist/admin/components/FormModalNavigation/useFormModalNavigation.mjs
var useFormModalNavigation = () => (0, import_react3.useContext)(FormModalNavigationContext);

// node_modules/@strapi/content-type-builder/dist/admin/components/Status.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var Status = ({ status }) => {
  switch (status) {
    case "UNCHANGED":
      return null;
    case "CHANGED":
      return (0, import_jsx_runtime.jsx)(Typography, {
        fontWeight: "semiBold",
        textColor: "alternative500",
        children: "M"
      });
    case "REMOVED":
      return (0, import_jsx_runtime.jsx)(Typography, {
        fontWeight: "semiBold",
        textColor: "danger500",
        children: "D"
      });
    case "NEW":
      return (0, import_jsx_runtime.jsx)(Typography, {
        fontWeight: "semiBold",
        textColor: "success500",
        children: "N"
      });
  }
};
var StatusBadge = ({ status }) => {
  switch (status) {
    case "CHANGED":
      return (0, import_jsx_runtime.jsx)(Badge, {
        fontWeight: "bold",
        textColor: "alternative600",
        backgroundColor: "alternative100",
        borderColor: "alternative200",
        children: "Modified"
      });
    case "REMOVED":
      return (0, import_jsx_runtime.jsx)(Badge, {
        fontWeight: "bold",
        textColor: "danger600",
        backgroundColor: "danger100",
        borderColor: "danger200",
        children: "Deleted"
      });
    case "NEW":
      return (0, import_jsx_runtime.jsx)(Badge, {
        fontWeight: "bold",
        textColor: "success600",
        backgroundColor: "success100",
        borderColor: "success200",
        children: "New"
      });
    case "UNCHANGED":
    default:
      return (0, import_jsx_runtime.jsx)(Badge, {
        style: {
          visibility: "hidden"
        },
        fontWeight: "bold",
        textColor: "warning600",
        backgroundColor: "warning100",
        borderColor: "warning200",
        children: "Unchanged"
      });
  }
};

// node_modules/@strapi/content-type-builder/dist/admin/components/AttributeIcon.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var iconByTypes = {
  biginteger: ForwardRef$j,
  blocks: ForwardRef$132,
  boolean: ForwardRef$112,
  collectionType: ForwardRef$Z,
  component: ForwardRef$X2,
  contentType: ForwardRef$Z,
  date: ForwardRef$V2,
  datetime: ForwardRef$V2,
  decimal: ForwardRef$j,
  dynamiczone: ForwardRef$P2,
  email: ForwardRef$N2,
  enum: ForwardRef$D2,
  enumeration: ForwardRef$D2,
  file: ForwardRef$n2,
  files: ForwardRef$n2,
  float: ForwardRef$j,
  integer: ForwardRef$j,
  json: ForwardRef$r2,
  JSON: ForwardRef$r2,
  media: ForwardRef$n2,
  number: ForwardRef$j,
  password: ForwardRef$h2,
  relation: ForwardRef$b2,
  richtext: ForwardRef$p2,
  singleType: ForwardRef$92,
  string: ForwardRef$52,
  text: ForwardRef$52,
  time: ForwardRef$V2,
  timestamp: ForwardRef$V2,
  uid: ForwardRef$3
};
var IconBox = dt(Box)`
  svg {
    height: 100%;
    width: 100%;
  }
`;
var AttributeIcon = ({ type, customField = null, ...rest }) => {
  const getCustomField = useStrapiApp("AttributeIcon", (state) => state.customFields.get);
  let Compo = iconByTypes[type];
  if (customField) {
    const customFieldObject = getCustomField(customField);
    const icon = customFieldObject == null ? void 0 : customFieldObject.icon;
    if (icon) {
      Compo = icon;
    }
  }
  if (!iconByTypes[type]) {
    return null;
  }
  return (0, import_jsx_runtime2.jsx)(IconBox, {
    width: "3.2rem",
    height: "3.2rem",
    shrink: 0,
    ...rest,
    "aria-hidden": true,
    children: (0, import_jsx_runtime2.jsx)(Box, {
      tag: Compo
    })
  });
};

// node_modules/@strapi/content-type-builder/dist/admin/components/IconPicker/constants.mjs
var COMPONENT_ICONS = {
  alien: ForwardRef$5r,
  apps: ForwardRef$37,
  archive: ForwardRef$5p,
  arrowDown: ForwardRef$5l,
  arrowLeft: ForwardRef$5j,
  arrowRight: ForwardRef$5d,
  arrowUp: ForwardRef$5b,
  attachment: ForwardRef$1z,
  bell: ForwardRef$55,
  bold: ForwardRef$53,
  book: ForwardRef$51,
  briefcase: ForwardRef$4$,
  brush: ForwardRef$1H,
  bulletList: ForwardRef$4Z,
  calendar: ForwardRef$4X,
  car: ForwardRef$4V,
  cast: ForwardRef$4P,
  chartBubble: ForwardRef$4L,
  chartCircle: ForwardRef$4J,
  chartPie: ForwardRef$4H,
  check: ForwardRef$4F,
  clock: ForwardRef$4r,
  cloud: ForwardRef$4n,
  code: ForwardRef$4j,
  cog: ForwardRef$4d,
  collapse: ForwardRef$4b,
  command: ForwardRef$49,
  connector: ForwardRef$3z,
  crop: ForwardRef$47,
  crown: ForwardRef$41,
  cup: ForwardRef$4f,
  cursor: ForwardRef$3$,
  dashboard: ForwardRef$H,
  database: ForwardRef$3Z,
  discuss: ForwardRef$3X,
  doctor: ForwardRef$B,
  earth: ForwardRef$3P,
  emotionHappy: ForwardRef$3L,
  emotionUnhappy: ForwardRef$3J,
  envelop: ForwardRef$2d,
  exit: ForwardRef$N,
  expand: ForwardRef$3H,
  eye: ForwardRef$3D,
  feather: ForwardRef$3x,
  file: ForwardRef$3v,
  fileError: ForwardRef$3r,
  filePdf: ForwardRef$3p,
  filter: ForwardRef$3j,
  folder: ForwardRef$3h,
  gate: ForwardRef$4N,
  gift: ForwardRef$3f,
  globe: ForwardRef$3d,
  grid: ForwardRef$39,
  handHeart: ForwardRef$35,
  hashtag: ForwardRef$33,
  headphone: ForwardRef$2R,
  heart: ForwardRef$2P,
  house: ForwardRef$2N,
  information: ForwardRef$2D,
  italic: ForwardRef$2B,
  key: ForwardRef$2z,
  landscape: ForwardRef$2J,
  layer: ForwardRef$2n,
  layout: ForwardRef$2x,
  lightbulb: ForwardRef$2v,
  link: ForwardRef$2r,
  lock: ForwardRef$2h,
  magic: ForwardRef$2f,
  manyToMany: ForwardRef$2b,
  manyToOne: ForwardRef$29,
  manyWays: ForwardRef$27,
  medium: ForwardRef$l2,
  message: ForwardRef$25,
  microphone: ForwardRef$23,
  monitor: ForwardRef$1Z,
  moon: ForwardRef$1X,
  music: ForwardRef$1R,
  oneToMany: ForwardRef$1N,
  oneToOne: ForwardRef$1L,
  oneWay: ForwardRef$1J,
  paint: ForwardRef$1H,
  paintBrush: ForwardRef$1H,
  paperPlane: ForwardRef$1B,
  pencil: ForwardRef$1v,
  phone: ForwardRef$1t,
  picture: ForwardRef$2L,
  pin: ForwardRef$1r,
  pinMap: ForwardRef$1p,
  plane: ForwardRef$1n,
  play: ForwardRef$1j,
  plus: ForwardRef$1h,
  priceTag: ForwardRef$1b,
  puzzle: ForwardRef$19,
  question: ForwardRef$17,
  quote: ForwardRef$15,
  refresh: ForwardRef$5n,
  restaurant: ForwardRef$13,
  rocket: ForwardRef$11,
  rotate: ForwardRef$59,
  scissors: ForwardRef$$,
  search: ForwardRef$X,
  seed: ForwardRef$1l,
  server: ForwardRef$V,
  shield: ForwardRef$T,
  shirt: ForwardRef$R,
  shoppingCart: ForwardRef$P,
  slideshow: ForwardRef$1d,
  stack: ForwardRef$F,
  star: ForwardRef$D,
  store: ForwardRef$x,
  strikeThrough: ForwardRef$v,
  sun: ForwardRef$t,
  television: ForwardRef$r,
  thumbDown: ForwardRef$p,
  thumbUp: ForwardRef$n,
  train: ForwardRef$l,
  twitter: ForwardRef$12,
  typhoon: ForwardRef$h,
  underline: ForwardRef$f,
  user: ForwardRef$b,
  volumeMute: ForwardRef$9,
  volumeUp: ForwardRef$7,
  walk: ForwardRef$5,
  wheelchair: ForwardRef$1,
  write: ForwardRef$3x
};

export {
  getTrad,
  DataManagerContext,
  useDataManager,
  Status,
  StatusBadge,
  FormModalNavigationContext,
  useFormModalNavigation,
  AttributeIcon,
  COMPONENT_ICONS
};
//# sourceMappingURL=chunk-WVL7LKFO.js.map
