{"version": 3, "sources": ["../../../@strapi/upload/admin/src/pages/SettingsPage/init.ts", "../../../@strapi/upload/admin/src/pages/SettingsPage/reducer.ts", "../../../@strapi/upload/admin/src/pages/SettingsPage/SettingsPage.tsx"], "sourcesContent": ["import type { InitialState } from './reducer';\n\nexport const init = (initialState: InitialState) => {\n  return initialState;\n};\n", "import { produce } from 'immer';\nimport set from 'lodash/set';\n\nexport type InitialState = {\n  initialData: {\n    responsiveDimensions?: boolean;\n    sizeOptimization?: boolean;\n    autoOrientation?: boolean;\n    videoPreview?: boolean;\n  } | null;\n  modifiedData: {\n    responsiveDimensions?: boolean;\n    sizeOptimization?: boolean;\n    autoOrientation?: boolean;\n    videoPreview?: boolean;\n  } | null;\n};\n\ninterface ActionGetDataSucceeded {\n  type: 'GET_DATA_SUCCEEDED';\n  data: InitialState['initialData'];\n}\n\ninterface ActionOnChange {\n  type: 'ON_CHANGE';\n  keys: keyof NonNullable<InitialState['initialData']>;\n  value: boolean;\n}\n\nexport type Action = ActionGetDataSucceeded | ActionOnChange;\n\nconst initialState: InitialState = {\n  initialData: {\n    responsiveDimensions: true,\n    sizeOptimization: true,\n    autoOrientation: false,\n    videoPreview: false,\n  },\n  modifiedData: {\n    responsiveDimensions: true,\n    sizeOptimization: true,\n    autoOrientation: false,\n    videoPreview: false,\n  },\n};\n\nconst reducer = (state: InitialState, action: Action) =>\n  produce(state, (drafState) => {\n    switch (action.type) {\n      case 'GET_DATA_SUCCEEDED': {\n        drafState.initialData = action.data;\n        drafState.modifiedData = action.data;\n        break;\n      }\n      case 'ON_CHANGE': {\n        set(drafState, ['modifiedData', ...action.keys.split('.')], action.value);\n        break;\n      }\n      default:\n        return state;\n    }\n  });\n\nexport { initialState, reducer };\n", "// TODO: find a better naming convention for the file that was an index file before\nimport * as React from 'react';\n\nimport { Page, useNotification, useFetchClient, Layouts } from '@strapi/admin/strapi-admin';\nimport { Box, Button, Flex, Grid, Toggle, Typography, Field } from '@strapi/design-system';\nimport { Check } from '@strapi/icons';\nimport isEqual from 'lodash/isEqual';\nimport { useIntl } from 'react-intl';\nimport { useMutation, useQuery } from 'react-query';\n\nimport { UpdateSettings } from '../../../../shared/contracts/settings';\nimport { PERMISSIONS } from '../../constants';\nimport { getTrad } from '../../utils';\n\nimport { init } from './init';\nimport { initialState, reducer } from './reducer';\n\nimport type { InitialState } from './reducer';\n\nexport const SettingsPage = () => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { get, put } = useFetchClient();\n\n  const [{ initialData, modifiedData }, dispatch] = React.useReducer(reducer, initialState, init);\n\n  const { data, isLoading, refetch } = useQuery({\n    queryKey: ['upload', 'settings'],\n    async queryFn() {\n      const {\n        data: { data },\n      } = await get('/upload/settings');\n\n      return data;\n    },\n  });\n\n  React.useEffect(() => {\n    if (data) {\n      dispatch({\n        type: 'GET_DATA_SUCCEEDED',\n        data,\n      });\n    }\n  }, [data]);\n\n  const isSaveButtonDisabled = isEqual(initialData, modifiedData);\n\n  const { mutateAsync, isLoading: isSubmitting } = useMutation<\n    UpdateSettings.Response['data'],\n    UpdateSettings.Response['error'],\n    UpdateSettings.Request['body']\n  >(\n    async (body) => {\n      const { data } = await put('/upload/settings', body);\n\n      return data;\n    },\n    {\n      onSuccess() {\n        refetch();\n\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({ id: 'notification.form.success.fields' }),\n        });\n      },\n      onError(err) {\n        console.error(err);\n      },\n    }\n  );\n\n  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n\n    if (isSaveButtonDisabled) {\n      return;\n    }\n\n    await mutateAsync(modifiedData!);\n  };\n\n  const handleChange = ({\n    target: { name, value },\n  }: {\n    target: { name: keyof NonNullable<InitialState['initialData']>; value: boolean };\n  }) => {\n    dispatch({\n      type: 'ON_CHANGE',\n      keys: name,\n      value,\n    });\n  };\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Page.Main tabIndex={-1}>\n      <Page.Title>\n        {formatMessage({\n          id: getTrad('page.title'),\n          defaultMessage: 'Settings - Media Libray',\n        })}\n      </Page.Title>\n      <form onSubmit={handleSubmit}>\n        <Layouts.Header\n          title={formatMessage({\n            id: getTrad('settings.header.label'),\n            defaultMessage: 'Media Library',\n          })}\n          primaryAction={\n            <Button\n              disabled={isSaveButtonDisabled}\n              loading={isSubmitting}\n              type=\"submit\"\n              startIcon={<Check />}\n              size=\"S\"\n            >\n              {formatMessage({\n                id: 'global.save',\n                defaultMessage: 'Save',\n              })}\n            </Button>\n          }\n          subtitle={formatMessage({\n            id: getTrad('settings.sub-header.label'),\n            defaultMessage: 'Configure the settings for the Media Library',\n          })}\n        />\n        <Layouts.Content>\n          <Layouts.Root>\n            <Flex direction=\"column\" alignItems=\"stretch\" gap={12}>\n              <Box background=\"neutral0\" padding={6} shadow=\"filterShadow\" hasRadius>\n                <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n                  <Flex>\n                    <Typography variant=\"delta\" tag=\"h2\">\n                      {formatMessage({\n                        id: getTrad('settings.blockTitle'),\n                        defaultMessage: 'Asset management',\n                      })}\n                    </Typography>\n                  </Flex>\n                  <Grid.Root gap={6}>\n                    <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"stretch\">\n                      <Field.Root\n                        hint={formatMessage({\n                          id: getTrad('settings.form.responsiveDimensions.description'),\n                          defaultMessage:\n                            'Enabling this option will generate multiple formats (small, medium and large) of the uploaded asset.',\n                        })}\n                        name=\"responsiveDimensions\"\n                      >\n                        <Field.Label>\n                          {formatMessage({\n                            id: getTrad('settings.form.responsiveDimensions.label'),\n                            defaultMessage: 'Responsive friendly upload',\n                          })}\n                        </Field.Label>\n                        <Toggle\n                          checked={modifiedData?.responsiveDimensions}\n                          offLabel={formatMessage({\n                            id: 'app.components.ToggleCheckbox.off-label',\n                            defaultMessage: 'Off',\n                          })}\n                          onLabel={formatMessage({\n                            id: 'app.components.ToggleCheckbox.on-label',\n                            defaultMessage: 'On',\n                          })}\n                          onChange={(e) => {\n                            handleChange({\n                              target: { name: 'responsiveDimensions', value: e.target.checked },\n                            });\n                          }}\n                        />\n                        <Field.Hint />\n                      </Field.Root>\n                    </Grid.Item>\n                    <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"stretch\">\n                      <Field.Root\n                        hint={formatMessage({\n                          id: getTrad('settings.form.sizeOptimization.description'),\n                          defaultMessage:\n                            'Enabling this option will reduce the image size and slightly reduce its quality.',\n                        })}\n                        name=\"sizeOptimization\"\n                      >\n                        <Field.Label>\n                          {formatMessage({\n                            id: getTrad('settings.form.sizeOptimization.label'),\n                            defaultMessage: 'Size optimization',\n                          })}\n                        </Field.Label>\n                        <Toggle\n                          checked={modifiedData?.sizeOptimization}\n                          offLabel={formatMessage({\n                            id: 'app.components.ToggleCheckbox.off-label',\n                            defaultMessage: 'Off',\n                          })}\n                          onLabel={formatMessage({\n                            id: 'app.components.ToggleCheckbox.on-label',\n                            defaultMessage: 'On',\n                          })}\n                          onChange={(e) => {\n                            handleChange({\n                              target: { name: 'sizeOptimization', value: e.target.checked },\n                            });\n                          }}\n                        />\n                        <Field.Hint />\n                      </Field.Root>\n                    </Grid.Item>\n                    <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"stretch\">\n                      <Field.Root\n                        hint={formatMessage({\n                          id: getTrad('settings.form.autoOrientation.description'),\n                          defaultMessage:\n                            'Enabling this option will automatically rotate the image according to EXIF orientation tag.',\n                        })}\n                        name=\"autoOrientation\"\n                      >\n                        <Field.Label>\n                          {formatMessage({\n                            id: getTrad('settings.form.autoOrientation.label'),\n                            defaultMessage: 'Auto orientation',\n                          })}\n                        </Field.Label>\n                        <Toggle\n                          checked={modifiedData?.autoOrientation}\n                          offLabel={formatMessage({\n                            id: 'app.components.ToggleCheckbox.off-label',\n                            defaultMessage: 'Off',\n                          })}\n                          onLabel={formatMessage({\n                            id: 'app.components.ToggleCheckbox.on-label',\n                            defaultMessage: 'On',\n                          })}\n                          onChange={(e) => {\n                            handleChange({\n                              target: { name: 'autoOrientation', value: e.target.checked },\n                            });\n                          }}\n                        />\n                        <Field.Hint />\n                      </Field.Root>\n                    </Grid.Item>\n                  </Grid.Root>\n                </Flex>\n              </Box>\n            </Flex>\n          </Layouts.Root>\n        </Layouts.Content>\n      </form>\n    </Page.Main>\n  );\n};\n\nexport const ProtectedSettingsPage = () => (\n  <Page.Protect permissions={PERMISSIONS.settings}>\n    <SettingsPage />\n  </Page.Protect>\n);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,IAAMA,OAAO,CAACC,kBAAAA;AACnB,SAAOA;AACT;;;;AC2BA,IAAMC,eAA6B;EACjCC,aAAa;IACXC,sBAAsB;IACtBC,kBAAkB;IAClBC,iBAAiB;IACjBC,cAAc;EAChB;EACAC,cAAc;IACZJ,sBAAsB;IACtBC,kBAAkB;IAClBC,iBAAiB;IACjBC,cAAc;EAChB;AACF;AAEA,IAAME,UAAU,CAACC,OAAqBC,WACpCC,GAAQF,OAAO,CAACG,cAAAA;AACd,UAAQF,OAAOG,MAAI;IACjB,KAAK,sBAAsB;AACzBD,gBAAUV,cAAcQ,OAAOI;AAC/BF,gBAAUL,eAAeG,OAAOI;AAChC;IACF;IACA,KAAK,aAAa;AAChBC,qBAAAA,SAAIH,WAAW;QAAC;WAAmBF,OAAOM,KAAKC,MAAM,GAAA;MAAK,GAAEP,OAAOQ,KAAK;AACxE;IACF;IACA;AACE,aAAOT;EACX;AACF,CAAA;;;IC1CWU,eAAe,MAAA;AAC1B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,KAAKC,IAAG,IAAKC,eAAAA;AAErB,QAAM,CAAC,EAAEC,aAAaC,aAAY,GAAIC,QAAAA,IAAkBC,iBAAWC,SAASC,cAAcC,IAAAA;AAE1F,QAAM,EAAEC,MAAMC,WAAWC,QAAO,IAAKC,SAAS;IAC5CC,UAAU;MAAC;MAAU;IAAW;IAChC,MAAMC,UAAAA;AACJ,YAAM,EACJL,MAAM,EAAEA,MAAAA,MAAI,EAAE,IACZ,MAAMV,IAAI,kBAAA;AAEd,aAAOU;IACT;EACF,CAAA;AAEAM,EAAMC,gBAAU,MAAA;AACd,QAAIP,MAAM;AACRL,eAAS;QACPa,MAAM;QACNR;MACF,CAAA;IACF;KACC;IAACA;EAAK,CAAA;AAET,QAAMS,2BAAuBC,eAAAA,SAAQjB,aAAaC,YAAAA;AAElD,QAAM,EAAEiB,aAAaV,WAAWW,aAAY,IAAKC,YAK/C,OAAOC,SAAAA;AACL,UAAM,EAAEd,MAAAA,MAAI,IAAK,MAAMT,IAAI,oBAAoBuB,IAAAA;AAE/C,WAAOd;KAET;IACEe,YAAAA;AACEb,cAAAA;AAEAd,yBAAmB;QACjBoB,MAAM;QACNQ,SAAS9B,cAAc;UAAE+B,IAAI;QAAmC,CAAA;MAClE,CAAA;IACF;IACAC,QAAQC,KAAG;AACTC,cAAQC,MAAMF,GAAAA;IAChB;EACF,CAAA;AAGF,QAAMG,eAAe,OAAOC,MAAAA;AAC1BA,MAAEC,eAAc;AAEhB,QAAIf,sBAAsB;AACxB;IACF;AAEA,UAAME,YAAYjB,YAAAA;EACpB;AAEA,QAAM+B,eAAe,CAAC,EACpBC,QAAQ,EAAEC,MAAMC,MAAK,EAAE,MAGxB;AACCjC,aAAS;MACPa,MAAM;MACNqB,MAAMF;MACNC;IACF,CAAA;EACF;AAEA,MAAI3B,WAAW;AACb,eAAO6B,wBAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,aACEC,yBAACF,KAAKG,MAAI;IAACC,UAAU;;UACnBL,wBAACC,KAAKK,OAAK;kBACRlD,cAAc;UACb+B,IAAIoB,QAAQ,YAAA;UACZC,gBAAgB;QAClB,CAAA;;UAEFL,yBAACM,QAAAA;QAAKC,UAAUlB;;cACdQ,wBAACW,QAAQC,QAAM;YACbC,OAAOzD,cAAc;cACnB+B,IAAIoB,QAAQ,uBAAA;cACZC,gBAAgB;YAClB,CAAA;YACAM,mBACEd,wBAACe,QAAAA;cACCC,UAAUrC;cACVsC,SAASnC;cACTJ,MAAK;cACLwC,eAAWlB,wBAACmB,eAAAA,CAAAA,CAAAA;cACZC,MAAK;wBAEJhE,cAAc;gBACb+B,IAAI;gBACJqB,gBAAgB;cAClB,CAAA;;YAGJa,UAAUjE,cAAc;cACtB+B,IAAIoB,QAAQ,2BAAA;cACZC,gBAAgB;YAClB,CAAA;;cAEFR,wBAACW,QAAQW,SAAO;0BACdtB,wBAACW,QAAQY,MAAI;cACX,cAAAvB,wBAACwB,MAAAA;gBAAKC,WAAU;gBAASC,YAAW;gBAAUC,KAAK;gBACjD,cAAA3B,wBAAC4B,KAAAA;kBAAIC,YAAW;kBAAWC,SAAS;kBAAGC,QAAO;kBAAeC,WAAS;kBACpE,cAAA7B,yBAACqB,MAAAA;oBAAKC,WAAU;oBAASC,YAAW;oBAAUC,KAAK;;0BACjD3B,wBAACwB,MAAAA;wBACC,cAAAxB,wBAACiC,YAAAA;0BAAWC,SAAQ;0BAAQC,KAAI;oCAC7B/E,cAAc;4BACb+B,IAAIoB,QAAQ,qBAAA;4BACZC,gBAAgB;0BAClB,CAAA;;;0BAGJL,yBAACiC,KAAKb,MAAI;wBAACI,KAAK;;8BACd3B,wBAACoC,KAAKC,MAAI;4BAACC,KAAK;4BAAGC,GAAG;4BAAId,WAAU;4BAASC,YAAW;0CACtDvB,yBAACqC,MAAMjB,MAAI;8BACTkB,MAAMrF,cAAc;gCAClB+B,IAAIoB,QAAQ,gDAAA;gCACZC,gBACE;8BACJ,CAAA;8BACAX,MAAK;;oCAELG,wBAACwC,MAAME,OAAK;4CACTtF,cAAc;oCACb+B,IAAIoB,QAAQ,0CAAA;oCACZC,gBAAgB;kCAClB,CAAA;;oCAEFR,wBAAC2C,QAAAA;kCACCC,SAAShF,6CAAciF;kCACvBC,UAAU1F,cAAc;oCACtB+B,IAAI;oCACJqB,gBAAgB;kCAClB,CAAA;kCACAuC,SAAS3F,cAAc;oCACrB+B,IAAI;oCACJqB,gBAAgB;kCAClB,CAAA;kCACAwC,UAAU,CAACvD,MAAAA;AACTE,iDAAa;sCACXC,QAAQ;wCAAEC,MAAM;wCAAwBC,OAAOL,EAAEG,OAAOgD;sCAAQ;oCAClE,CAAA;kCACF;;oCAEF5C,wBAACwC,MAAMS,MAAI,CAAA,CAAA;;;;8BAGfjD,wBAACoC,KAAKC,MAAI;4BAACC,KAAK;4BAAGC,GAAG;4BAAId,WAAU;4BAASC,YAAW;0CACtDvB,yBAACqC,MAAMjB,MAAI;8BACTkB,MAAMrF,cAAc;gCAClB+B,IAAIoB,QAAQ,4CAAA;gCACZC,gBACE;8BACJ,CAAA;8BACAX,MAAK;;oCAELG,wBAACwC,MAAME,OAAK;4CACTtF,cAAc;oCACb+B,IAAIoB,QAAQ,sCAAA;oCACZC,gBAAgB;kCAClB,CAAA;;oCAEFR,wBAAC2C,QAAAA;kCACCC,SAAShF,6CAAcsF;kCACvBJ,UAAU1F,cAAc;oCACtB+B,IAAI;oCACJqB,gBAAgB;kCAClB,CAAA;kCACAuC,SAAS3F,cAAc;oCACrB+B,IAAI;oCACJqB,gBAAgB;kCAClB,CAAA;kCACAwC,UAAU,CAACvD,MAAAA;AACTE,iDAAa;sCACXC,QAAQ;wCAAEC,MAAM;wCAAoBC,OAAOL,EAAEG,OAAOgD;sCAAQ;oCAC9D,CAAA;kCACF;;oCAEF5C,wBAACwC,MAAMS,MAAI,CAAA,CAAA;;;;8BAGfjD,wBAACoC,KAAKC,MAAI;4BAACC,KAAK;4BAAGC,GAAG;4BAAId,WAAU;4BAASC,YAAW;0CACtDvB,yBAACqC,MAAMjB,MAAI;8BACTkB,MAAMrF,cAAc;gCAClB+B,IAAIoB,QAAQ,2CAAA;gCACZC,gBACE;8BACJ,CAAA;8BACAX,MAAK;;oCAELG,wBAACwC,MAAME,OAAK;4CACTtF,cAAc;oCACb+B,IAAIoB,QAAQ,qCAAA;oCACZC,gBAAgB;kCAClB,CAAA;;oCAEFR,wBAAC2C,QAAAA;kCACCC,SAAShF,6CAAcuF;kCACvBL,UAAU1F,cAAc;oCACtB+B,IAAI;oCACJqB,gBAAgB;kCAClB,CAAA;kCACAuC,SAAS3F,cAAc;oCACrB+B,IAAI;oCACJqB,gBAAgB;kCAClB,CAAA;kCACAwC,UAAU,CAACvD,MAAAA;AACTE,iDAAa;sCACXC,QAAQ;wCAAEC,MAAM;wCAAmBC,OAAOL,EAAEG,OAAOgD;sCAAQ;oCAC7D,CAAA;kCACF;;oCAEF5C,wBAACwC,MAAMS,MAAI,CAAA,CAAA;;;;;;;;;;;;;;;;AAYnC;AAEaG,IAAAA,wBAAwB,UACnCpD,wBAACC,KAAKoD,SAAO;EAACC,aAAaC,YAAYC;EACrC,cAAAxD,wBAAC7C,cAAAA,CAAAA,CAAAA;AAEH,CAAA;", "names": ["init", "initialState", "initialState", "initialData", "responsiveDimensions", "sizeOptimization", "autoOrientation", "videoPreview", "modifiedData", "reducer", "state", "action", "produce", "drafState", "type", "data", "set", "keys", "split", "value", "SettingsPage", "formatMessage", "useIntl", "toggleNotification", "useNotification", "get", "put", "useFetchClient", "initialData", "modifiedData", "dispatch", "useReducer", "reducer", "initialState", "init", "data", "isLoading", "refetch", "useQuery", "query<PERSON><PERSON>", "queryFn", "React", "useEffect", "type", "isSaveButtonDisabled", "isEqual", "mutateAsync", "isSubmitting", "useMutation", "body", "onSuccess", "message", "id", "onError", "err", "console", "error", "handleSubmit", "e", "preventDefault", "handleChange", "target", "name", "value", "keys", "_jsx", "Page", "Loading", "_jsxs", "Main", "tabIndex", "Title", "getTrad", "defaultMessage", "form", "onSubmit", "Layouts", "Header", "title", "primaryAction", "<PERSON><PERSON>", "disabled", "loading", "startIcon", "Check", "size", "subtitle", "Content", "Root", "Flex", "direction", "alignItems", "gap", "Box", "background", "padding", "shadow", "hasRadius", "Typography", "variant", "tag", "Grid", "<PERSON><PERSON>", "col", "s", "Field", "hint", "Label", "Toggle", "checked", "responsiveDimensions", "offLabel", "onLabel", "onChange", "Hint", "sizeOptimization", "autoOrientation", "ProtectedSettingsPage", "Protect", "permissions", "PERMISSIONS", "settings"]}