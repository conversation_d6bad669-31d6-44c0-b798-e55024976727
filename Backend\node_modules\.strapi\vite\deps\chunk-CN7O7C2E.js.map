{"version": 3, "sources": ["../../../@strapi/admin/admin/src/services/apiTokens.ts"], "sourcesContent": ["import * as ApiToken from '../../../shared/contracts/api-token';\n\nimport { adminApi } from './api';\n\nconst apiTokensService = adminApi\n  .enhanceEndpoints({\n    addTagTypes: ['ApiToken'],\n  })\n  .injectEndpoints({\n    endpoints: (builder) => ({\n      getAPITokens: builder.query<ApiToken.List.Response['data'], void>({\n        query: () => '/admin/api-tokens',\n        transformResponse: (response: ApiToken.List.Response) => response.data,\n        providesTags: (res, _err) => [\n          ...(res?.map(({ id }) => ({ type: 'ApiToken' as const, id })) ?? []),\n          { type: 'ApiToken' as const, id: 'LIST' },\n        ],\n      }),\n      getAPIToken: builder.query<ApiToken.Get.Response['data'], ApiToken.Get.Params['id']>({\n        query: (id) => `/admin/api-tokens/${id}`,\n        transformResponse: (response: ApiToken.Get.Response) => response.data,\n        providesTags: (res, _err, id) => [{ type: 'ApiToken' as const, id }],\n      }),\n      createAPIToken: builder.mutation<\n        ApiToken.Create.Response['data'],\n        ApiToken.Create.Request['body']\n      >({\n        query: (body) => ({\n          url: '/admin/api-tokens',\n          method: 'POST',\n          data: body,\n        }),\n        transformResponse: (response: ApiToken.Create.Response) => response.data,\n        invalidatesTags: [{ type: 'ApiToken' as const, id: 'LIST' }],\n      }),\n      deleteAPIToken: builder.mutation<\n        ApiToken.Revoke.Response['data'],\n        ApiToken.Revoke.Params['id']\n      >({\n        query: (id) => ({\n          url: `/admin/api-tokens/${id}`,\n          method: 'DELETE',\n        }),\n        transformResponse: (response: ApiToken.Revoke.Response) => response.data,\n        invalidatesTags: (_res, _err, id) => [{ type: 'ApiToken' as const, id }],\n      }),\n      updateAPIToken: builder.mutation<\n        ApiToken.Update.Response['data'],\n        ApiToken.Update.Params & ApiToken.Update.Request['body']\n      >({\n        query: ({ id, ...body }) => ({\n          url: `/admin/api-tokens/${id}`,\n          method: 'PUT',\n          data: body,\n        }),\n        transformResponse: (response: ApiToken.Update.Response) => response.data,\n        invalidatesTags: (_res, _err, { id }) => [{ type: 'ApiToken' as const, id }],\n      }),\n    }),\n  });\n\nconst {\n  useGetAPITokensQuery,\n  useGetAPITokenQuery,\n  useCreateAPITokenMutation,\n  useDeleteAPITokenMutation,\n  useUpdateAPITokenMutation,\n} = apiTokensService;\n\nexport {\n  useGetAPITokensQuery,\n  useGetAPITokenQuery,\n  useCreateAPITokenMutation,\n  useDeleteAPITokenMutation,\n  useUpdateAPITokenMutation,\n};\n"], "mappings": ";;;;;AAIA,IAAMA,mBAAmBC,SACtBC,iBAAiB;EAChBC,aAAa;IAAC;EAAW;AAC3B,CAAA,EACCC,gBAAgB;EACfC,WAAW,CAACC,aAAa;IACvBC,cAAcD,QAAQE,MAA4C;MAChEA,OAAO,MAAM;MACbC,mBAAmB,CAACC,aAAqCA,SAASC;MAClEC,cAAc,CAACC,KAAKC,SAAS;QACvBD,IAAAA,2BAAKE,IAAI,CAAC,EAAEC,GAAE,OAAQ;UAAEC,MAAM;UAAqBD;QAAG,QAAO,CAAA;QACjE;UAAEC,MAAM;UAAqBD,IAAI;QAAO;MACzC;IACH,CAAA;IACAE,aAAaZ,QAAQE,MAAgE;MACnFA,OAAO,CAACQ,OAAO,qBAAqBA,EAAAA;MACpCP,mBAAmB,CAACC,aAAoCA,SAASC;MACjEC,cAAc,CAACC,KAAKC,MAAME,OAAO;QAAC;UAAEC,MAAM;UAAqBD;QAAG;MAAE;IACtE,CAAA;IACAG,gBAAgBb,QAAQc,SAGtB;MACAZ,OAAO,CAACa,UAAU;QAChBC,KAAK;QACLC,QAAQ;QACRZ,MAAMU;;MAERZ,mBAAmB,CAACC,aAAuCA,SAASC;MACpEa,iBAAiB;QAAC;UAAEP,MAAM;UAAqBD,IAAI;QAAO;MAAE;IAC9D,CAAA;IACAS,gBAAgBnB,QAAQc,SAGtB;MACAZ,OAAO,CAACQ,QAAQ;QACdM,KAAK,qBAAqBN,EAAAA;QAC1BO,QAAQ;;MAEVd,mBAAmB,CAACC,aAAuCA,SAASC;MACpEa,iBAAiB,CAACE,MAAMZ,MAAME,OAAO;QAAC;UAAEC,MAAM;UAAqBD;QAAG;MAAE;IAC1E,CAAA;IACAW,gBAAgBrB,QAAQc,SAGtB;MACAZ,OAAO,CAAC,EAAEQ,IAAI,GAAGK,KAAAA,OAAY;QAC3BC,KAAK,qBAAqBN,EAAAA;QAC1BO,QAAQ;QACRZ,MAAMU;;MAERZ,mBAAmB,CAACC,aAAuCA,SAASC;MACpEa,iBAAiB,CAACE,MAAMZ,MAAM,EAAEE,GAAE,MAAO;QAAC;UAAEC,MAAM;UAAqBD;QAAG;MAAE;IAC9E,CAAA;;AAEJ,CAAA;AAEI,IAAA,EACJY,sBACAC,qBACAC,2BACAC,2BACAC,0BAAyB,IACvBhC;", "names": ["apiTokensService", "adminApi", "enhanceEndpoints", "addTagTypes", "injectEndpoints", "endpoints", "builder", "getAPITokens", "query", "transformResponse", "response", "data", "providesTags", "res", "_err", "map", "id", "type", "getAPIToken", "createAPIToken", "mutation", "body", "url", "method", "invalidatesTags", "deleteAPIToken", "_res", "updateAPIToken", "useGetAPITokensQuery", "useGetAPITokenQuery", "useCreateAPITokenMutation", "useDeleteAPITokenMutation", "useUpdateAPITokenMutation"]}