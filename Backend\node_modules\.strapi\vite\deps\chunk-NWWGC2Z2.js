import {
  Button,
  Dialog,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  ForwardRef$3
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/components/ConfirmDialog.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var ConfirmDialog = ({ children, icon = (0, import_jsx_runtime.jsx)(StyledWarning, {}), onConfirm, onCancel, variant = "danger-light", startAction, endAction, title }) => {
  const { formatMessage } = useIntl();
  const [isConfirming, setIsConfirming] = React.useState(false);
  const content = children || formatMessage({
    id: "app.confirm.body",
    defaultMessage: "Are you sure?"
  });
  const handleConfirm = async (e) => {
    if (!onConfirm) {
      return;
    }
    try {
      setIsConfirming(true);
      await onConfirm(e);
    } finally {
      setIsConfirming(false);
    }
  };
  return (0, import_jsx_runtime.jsxs)(Dialog.Content, {
    children: [
      (0, import_jsx_runtime.jsx)(Dialog.Header, {
        children: title || formatMessage({
          id: "app.components.ConfirmDialog.title",
          defaultMessage: "Confirmation"
        })
      }),
      (0, import_jsx_runtime.jsx)(Dialog.Body, {
        icon,
        children: content
      }),
      (0, import_jsx_runtime.jsxs)(Dialog.Footer, {
        children: [
          startAction || (0, import_jsx_runtime.jsx)(Dialog.Cancel, {
            children: (0, import_jsx_runtime.jsx)(Button, {
              fullWidth: true,
              variant: "tertiary",
              onClick: (e) => {
                e.stopPropagation();
                if (onCancel) {
                  onCancel(e);
                }
              },
              children: formatMessage({
                id: "app.components.Button.cancel",
                defaultMessage: "Cancel"
              })
            })
          }),
          endAction || (0, import_jsx_runtime.jsx)(Dialog.Action, {
            children: (0, import_jsx_runtime.jsx)(Button, {
              fullWidth: true,
              onClick: handleConfirm,
              variant,
              loading: isConfirming,
              children: formatMessage({
                id: "app.components.Button.confirm",
                defaultMessage: "Confirm"
              })
            })
          })
        ]
      })
    ]
  });
};
var StyledWarning = dt(ForwardRef$3)`
  width: 24px;
  height: 24px;

  path {
    fill: ${({ theme }) => theme.colors.danger600};
  }
`;

export {
  ConfirmDialog
};
//# sourceMappingURL=chunk-NWWGC2Z2.js.map
