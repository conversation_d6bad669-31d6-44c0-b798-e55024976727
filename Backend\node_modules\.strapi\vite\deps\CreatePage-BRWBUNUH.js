import {
  Permissions
} from "./chunk-PFR3HBO7.js";
import "./chunk-Y2KSLFI4.js";
import "./chunk-NE3KAGU6.js";
import {
  format
} from "./chunk-DD3MYA7D.js";
import "./chunk-O5ZNSDDU.js";
import "./chunk-MBK4V2X7.js";
import "./chunk-YJEURQPS.js";
import "./chunk-DY2RJG3P.js";
import "./chunk-GGK2TLCV.js";
import "./chunk-K65KIEAL.js";
import "./chunk-BUDFB33L.js";
import {
  Form,
  Formik
} from "./chunk-7MILHJ3J.js";
import "./chunk-SGQJOZK5.js";
import "./chunk-AFM2NWPO.js";
import {
  BackButton
} from "./chunk-DUGZ4WIW.js";
import {
  errorsTrads
} from "./chunk-IFOFBKTA.js";
import {
  create4 as create,
  create6 as create2
} from "./chunk-376QHLWZ.js";
import "./chunk-EGNP2T5O.js";
import {
  useTracking
} from "./chunk-XDCEA27D.js";
import "./chunk-EZSYDDUK.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-D4WYVNVM.js";
import "./chunk-MMOBCIZG.js";
import {
  useCreateRoleMutation,
  useGetRolePermissionLayoutQuery,
  useGetRolePermissionsQuery,
  useUpdateRolePermissionsMutation
} from "./chunk-6DRYEU2W.js";
import {
  Layouts
} from "./chunk-MTTHLNPH.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import {
  Page,
  useAPIErrorHandler
} from "./chunk-7LKLOY7A.js";
import "./chunk-ODQFI753.js";
import {
  useTypedSelector
} from "./chunk-ZOP4VV6J.js";
import {
  isBaseQueryError
} from "./chunk-WH6VCVXU.js";
import "./chunk-IL5G2D22.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-76QM3EFM.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  Box,
  Button,
  Field,
  Flex,
  Grid,
  Main,
  TextInput,
  Textarea,
  Typography,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  useNavigate,
  useParams
} from "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$4F
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/CreatePage.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var CREATE_SCHEMA = create2().shape({
  name: create().required(errorsTrads.required.id),
  description: create().required(errorsTrads.required.id)
});
var CreatePage = () => {
  const { id } = useParams();
  const { toggleNotification } = useNotification();
  const { formatMessage } = useIntl();
  const navigate = useNavigate();
  const permissionsRef = React.useRef(null);
  const { trackUsage } = useTracking();
  const { _unstableFormatAPIError: formatAPIError, _unstableFormatValidationErrors: formatValidationErrors } = useAPIErrorHandler();
  const { isLoading: isLoadingPermissionsLayout, currentData: permissionsLayout } = useGetRolePermissionLayoutQuery({
    /**
    * Role here is a query param so if there's no role we pass an empty string
    * which returns us a default layout.
    */
    role: id ?? ""
  });
  const { currentData: rolePermissions, isLoading: isLoadingRole } = useGetRolePermissionsQuery({
    id
  }, {
    skip: !id,
    refetchOnMountOrArgChange: true
  });
  const [createRole] = useCreateRoleMutation();
  const [updateRolePermissions] = useUpdateRolePermissionsMutation();
  const handleCreateRoleSubmit = async (data, formik) => {
    var _a;
    try {
      if (id) {
        trackUsage("willDuplicateRole");
      } else {
        trackUsage("willCreateNewRole");
      }
      const res = await createRole(data);
      if ("error" in res) {
        if (isBaseQueryError(res.error) && res.error.name === "ValidationError") {
          formik.setErrors(formatValidationErrors(res.error));
        } else {
          toggleNotification({
            type: "danger",
            message: formatAPIError(res.error)
          });
        }
        return;
      }
      const { permissionsToSend } = ((_a = permissionsRef.current) == null ? void 0 : _a.getPermissions()) ?? {};
      if (res.data.id && Array.isArray(permissionsToSend) && permissionsToSend.length > 0) {
        const updateRes = await updateRolePermissions({
          id: res.data.id,
          permissions: permissionsToSend
        });
        if ("error" in updateRes) {
          if (isBaseQueryError(updateRes.error) && updateRes.error.name === "ValidationError") {
            formik.setErrors(formatValidationErrors(updateRes.error));
          } else {
            toggleNotification({
              type: "danger",
              message: formatAPIError(updateRes.error)
            });
          }
          return;
        }
      }
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: "Settings.roles.created",
          defaultMessage: "created"
        })
      });
      navigate(`../roles/${res.data.id.toString()}`, {
        replace: true
      });
    } catch (err) {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "An error occurred"
        })
      });
    }
  };
  if (isLoadingPermissionsLayout && isLoadingRole || !permissionsLayout) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  return (0, import_jsx_runtime.jsxs)(Main, {
    children: [
      (0, import_jsx_runtime.jsx)(Page.Title, {
        children: formatMessage({
          id: "Settings.PageTitle",
          defaultMessage: "Settings - {name}"
        }, {
          name: "Roles"
        })
      }),
      (0, import_jsx_runtime.jsx)(Formik, {
        initialValues: {
          name: "",
          description: `${formatMessage({
            id: "Settings.roles.form.created",
            defaultMessage: "Created"
          })} ${format(/* @__PURE__ */ new Date(), "PPP")}`
        },
        onSubmit: handleCreateRoleSubmit,
        validationSchema: CREATE_SCHEMA,
        validateOnChange: false,
        children: ({ values, errors, handleReset, handleChange, isSubmitting }) => (0, import_jsx_runtime.jsx)(Form, {
          children: (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, {
            children: [
              (0, import_jsx_runtime.jsx)(Layouts.Header, {
                primaryAction: (0, import_jsx_runtime.jsxs)(Flex, {
                  gap: 2,
                  children: [
                    (0, import_jsx_runtime.jsx)(Button, {
                      variant: "secondary",
                      onClick: () => {
                        var _a;
                        handleReset();
                        (_a = permissionsRef.current) == null ? void 0 : _a.resetForm();
                      },
                      children: formatMessage({
                        id: "app.components.Button.reset",
                        defaultMessage: "Reset"
                      })
                    }),
                    (0, import_jsx_runtime.jsx)(Button, {
                      type: "submit",
                      loading: isSubmitting,
                      startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4F, {}),
                      children: formatMessage({
                        id: "global.save",
                        defaultMessage: "Save"
                      })
                    })
                  ]
                }),
                title: formatMessage({
                  id: "Settings.roles.create.title",
                  defaultMessage: "Create a role"
                }),
                subtitle: formatMessage({
                  id: "Settings.roles.create.description",
                  defaultMessage: "Define the rights given to the role"
                }),
                navigationAction: (0, import_jsx_runtime.jsx)(BackButton, {
                  fallback: "../roles"
                })
              }),
              (0, import_jsx_runtime.jsx)(Layouts.Content, {
                children: (0, import_jsx_runtime.jsxs)(Flex, {
                  direction: "column",
                  alignItems: "stretch",
                  gap: 6,
                  children: [
                    (0, import_jsx_runtime.jsx)(Box, {
                      background: "neutral0",
                      padding: 6,
                      shadow: "filterShadow",
                      hasRadius: true,
                      children: (0, import_jsx_runtime.jsxs)(Flex, {
                        direction: "column",
                        alignItems: "stretch",
                        gap: 4,
                        children: [
                          (0, import_jsx_runtime.jsxs)(Flex, {
                            justifyContent: "space-between",
                            children: [
                              (0, import_jsx_runtime.jsxs)(Box, {
                                children: [
                                  (0, import_jsx_runtime.jsx)(Box, {
                                    children: (0, import_jsx_runtime.jsx)(Typography, {
                                      fontWeight: "bold",
                                      children: formatMessage({
                                        id: "global.details",
                                        defaultMessage: "Details"
                                      })
                                    })
                                  }),
                                  (0, import_jsx_runtime.jsx)(Box, {
                                    children: (0, import_jsx_runtime.jsx)(Typography, {
                                      variant: "pi",
                                      textColor: "neutral600",
                                      children: formatMessage({
                                        id: "Settings.roles.form.description",
                                        defaultMessage: "Name and description of the role"
                                      })
                                    })
                                  })
                                ]
                              }),
                              (0, import_jsx_runtime.jsx)(UsersRoleNumber, {
                                children: formatMessage({
                                  id: "Settings.roles.form.button.users-with-role",
                                  defaultMessage: "{number, plural, =0 {# users} one {# user} other {# users}} with this role"
                                }, {
                                  number: 0
                                })
                              })
                            ]
                          }),
                          (0, import_jsx_runtime.jsxs)(Grid.Root, {
                            gap: 4,
                            children: [
                              (0, import_jsx_runtime.jsx)(Grid.Item, {
                                col: 6,
                                direction: "column",
                                alignItems: "stretch",
                                children: (0, import_jsx_runtime.jsxs)(Field.Root, {
                                  name: "name",
                                  error: errors.name && formatMessage({
                                    id: errors.name
                                  }),
                                  required: true,
                                  children: [
                                    (0, import_jsx_runtime.jsx)(Field.Label, {
                                      children: formatMessage({
                                        id: "global.name",
                                        defaultMessage: "Name"
                                      })
                                    }),
                                    (0, import_jsx_runtime.jsx)(TextInput, {
                                      onChange: handleChange,
                                      value: values.name
                                    }),
                                    (0, import_jsx_runtime.jsx)(Field.Error, {})
                                  ]
                                })
                              }),
                              (0, import_jsx_runtime.jsx)(Grid.Item, {
                                col: 6,
                                direction: "column",
                                alignItems: "stretch",
                                children: (0, import_jsx_runtime.jsxs)(Field.Root, {
                                  name: "description",
                                  error: errors.description && formatMessage({
                                    id: errors.description
                                  }),
                                  children: [
                                    (0, import_jsx_runtime.jsx)(Field.Label, {
                                      children: formatMessage({
                                        id: "global.description",
                                        defaultMessage: "Description"
                                      })
                                    }),
                                    (0, import_jsx_runtime.jsx)(Textarea, {
                                      onChange: handleChange,
                                      value: values.description
                                    })
                                  ]
                                })
                              })
                            ]
                          })
                        ]
                      })
                    }),
                    (0, import_jsx_runtime.jsx)(Box, {
                      shadow: "filterShadow",
                      hasRadius: true,
                      children: (0, import_jsx_runtime.jsx)(Permissions, {
                        isFormDisabled: false,
                        ref: permissionsRef,
                        permissions: rolePermissions,
                        layout: permissionsLayout
                      })
                    })
                  ]
                })
              })
            ]
          })
        })
      })
    ]
  });
};
var UsersRoleNumber = dt.div`
  border: 1px solid ${({ theme }) => theme.colors.primary200};
  background: ${({ theme }) => theme.colors.primary100};
  padding: ${({ theme }) => `${theme.spaces[2]} ${theme.spaces[4]}`};
  color: ${({ theme }) => theme.colors.primary600};
  border-radius: ${({ theme }) => theme.borderRadius};
  font-size: 1.2rem;
  font-weight: bold;
`;
var ProtectedCreatePage = () => {
  const permissions = useTypedSelector((state) => {
    var _a;
    return (_a = state.admin_app.permissions.settings) == null ? void 0 : _a.roles.create;
  });
  return (0, import_jsx_runtime.jsx)(Page.Protect, {
    permissions,
    children: (0, import_jsx_runtime.jsx)(CreatePage, {})
  });
};
export {
  CreatePage,
  ProtectedCreatePage
};
//# sourceMappingURL=CreatePage-BRWBUNUH.js.map
