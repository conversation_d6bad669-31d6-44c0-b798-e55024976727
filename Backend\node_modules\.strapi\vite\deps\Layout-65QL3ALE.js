import {
  require_sortBy
} from "./chunk-DGNRYZKC.js";
import "./chunk-T2YF43GM.js";
import {
  selectAdminPermissions
} from "./chunk-2TUVRMRA.js";
import "./chunk-NE3KAGU6.js";
import {
  SETTINGS_LINKS_CE,
  SubNav
} from "./chunk-VCTAT6B3.js";
import {
  useEnterprise
} from "./chunk-HZKRK7AR.js";
import "./chunk-K65KIEAL.js";
import "./chunk-BUDFB33L.js";
import "./chunk-EGNP2T5O.js";
import {
  useAppInfo,
  useTracking
} from "./chunk-XDCEA27D.js";
import "./chunk-EZSYDDUK.js";
import "./chunk-YXDCVYVT.js";
import {
  Layouts
} from "./chunk-MTTHLNPH.js";
import {
  Page
} from "./chunk-7LKLOY7A.js";
import {
  useAuth,
  useStrapiApp
} from "./chunk-ODQFI753.js";
import "./chunk-ZOP4VV6J.js";
import "./chunk-WH6VCVXU.js";
import {
  useSelector
} from "./chunk-IL5G2D22.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-76QM3EFM.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import "./chunk-UBCTZOSQ.js";
import {
  Badge,
  Divider,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  Navigate,
  Outlet,
  useLocation,
  useMatch
} from "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$2t
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/Layout.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useSettingsMenu.mjs
var React = __toESM(require_react(), 1);
var import_sortBy = __toESM(require_sortBy(), 1);
var formatLinks = (menu) => menu.map((menuSection) => {
  const formattedLinks = menuSection.links.map((link) => ({
    ...link,
    isDisplayed: false
  }));
  return {
    ...menuSection,
    links: formattedLinks
  };
});
var useSettingsMenu = () => {
  const [{ isLoading, menu }, setData] = React.useState({
    isLoading: true,
    menu: []
  });
  const checkUserHasPermission = useAuth("useSettingsMenu", (state) => state.checkUserHasPermissions);
  const shouldUpdateStrapi = useAppInfo("useSettingsMenu", (state) => state.shouldUpdateStrapi);
  const settings = useStrapiApp("useSettingsMenu", (state) => state.settings);
  const permissions = useSelector(selectAdminPermissions);
  const ceLinks = React.useMemo(() => SETTINGS_LINKS_CE(), []);
  const { admin: adminLinks, global: globalLinks } = useEnterprise(ceLinks, async () => (await import("./constants-NGVQCX7V.js")).SETTINGS_LINKS_EE(), {
    combine(ceLinks2, eeLinks) {
      return {
        admin: [
          ...eeLinks.admin,
          ...ceLinks2.admin
        ],
        global: [
          ...ceLinks2.global,
          ...eeLinks.global
        ]
      };
    },
    defaultValue: {
      admin: [],
      global: []
    }
  });
  const addPermissions = React.useCallback((link) => {
    var _a, _b;
    if (!link.id) {
      throw new Error("The settings menu item must have an id attribute.");
    }
    return {
      ...link,
      permissions: ((_b = (_a = permissions.settings) == null ? void 0 : _a[link.id]) == null ? void 0 : _b.main) ?? []
    };
  }, [
    permissions.settings
  ]);
  React.useEffect(() => {
    const getData = async () => {
      const buildMenuPermissions = (sections2) => Promise.all(sections2.reduce((acc, section, sectionIndex) => {
        const linksWithPermissions = section.links.map(async (link, linkIndex) => ({
          hasPermission: (await checkUserHasPermission(link.permissions)).length > 0,
          sectionIndex,
          linkIndex
        }));
        return [
          ...acc,
          ...linksWithPermissions
        ];
      }, []));
      const menuPermissions = await buildMenuPermissions(sections);
      setData((prev) => {
        return {
          ...prev,
          isLoading: false,
          menu: sections.map((section, sectionIndex) => ({
            ...section,
            links: section.links.map((link, linkIndex) => {
              const permission = menuPermissions.find((permission2) => permission2.sectionIndex === sectionIndex && permission2.linkIndex === linkIndex);
              return {
                ...link,
                isDisplayed: Boolean(permission == null ? void 0 : permission.hasPermission)
              };
            })
          }))
        };
      });
    };
    const { global, ...otherSections } = settings;
    const sections = formatLinks([
      {
        ...global,
        links: (0, import_sortBy.default)([
          ...global.links,
          ...globalLinks.map(addPermissions)
        ], (link) => link.id).map((link) => ({
          ...link,
          hasNotification: link.id === "000-application-infos" && shouldUpdateStrapi
        }))
      },
      {
        id: "permissions",
        intlLabel: {
          id: "Settings.permissions",
          defaultMessage: "Administration Panel"
        },
        links: adminLinks.map(addPermissions)
      },
      ...Object.values(otherSections)
    ]);
    getData();
  }, [
    adminLinks,
    globalLinks,
    settings,
    shouldUpdateStrapi,
    addPermissions,
    checkUserHasPermission
  ]);
  return {
    isLoading,
    menu: menu.map((menuItem) => ({
      ...menuItem,
      links: menuItem.links.filter((link) => link.isDisplayed)
    }))
  };
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/components/SettingsNav.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var StyledBadge = dt(Badge)`
  border-radius: 50%;
  padding: ${({ theme }) => theme.spaces[2]};
  height: 2rem;
`;
var SettingsNav = ({ menu }) => {
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const { pathname } = useLocation();
  const filteredMenu = menu.filter((section) => !section.links.every((link) => link.isDisplayed === false));
  const sections = filteredMenu.map((section) => {
    return {
      ...section,
      title: section.intlLabel,
      links: section.links.map((link) => {
        return {
          ...link,
          title: link.intlLabel,
          name: link.id
        };
      })
    };
  });
  const label = formatMessage({
    id: "global.settings",
    defaultMessage: "Settings"
  });
  const handleClickOnLink = (destination) => () => {
    trackUsage("willNavigate", {
      from: pathname,
      to: destination
    });
  };
  return (0, import_jsx_runtime.jsxs)(SubNav.Main, {
    "aria-label": label,
    children: [
      (0, import_jsx_runtime.jsx)(SubNav.Header, {
        label
      }),
      (0, import_jsx_runtime.jsx)(Divider, {
        background: "neutral150",
        marginBottom: 5
      }),
      (0, import_jsx_runtime.jsx)(SubNav.Sections, {
        children: sections.map((section) => (0, import_jsx_runtime.jsx)(SubNav.Section, {
          label: formatMessage(section.intlLabel),
          children: section.links.map((link) => {
            return (0, import_jsx_runtime.jsx)(SubNav.Link, {
              to: link.to,
              onClick: handleClickOnLink(link.to),
              label: formatMessage(link.intlLabel),
              endAction: (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, {
                children: [
                  (link == null ? void 0 : link.licenseOnly) && (0, import_jsx_runtime.jsx)(ForwardRef$2t, {
                    fill: "primary600",
                    width: "1.5rem",
                    height: "1.5rem"
                  }),
                  (link == null ? void 0 : link.hasNotification) && (0, import_jsx_runtime.jsx)(StyledBadge, {
                    "aria-label": "Notification",
                    backgroundColor: "primary600",
                    textColor: "neutral0",
                    children: "1"
                  })
                ]
              })
            }, link.id);
          })
        }, section.id))
      })
    ]
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/Layout.mjs
var Layout = () => {
  const match = useMatch("/settings/:settingId/*");
  const { formatMessage } = useIntl();
  const { isLoading, menu } = useSettingsMenu();
  if (isLoading) {
    return (0, import_jsx_runtime2.jsx)(Page.Loading, {});
  }
  if (!(match == null ? void 0 : match.params.settingId)) {
    return (0, import_jsx_runtime2.jsx)(Navigate, {
      to: "application-infos"
    });
  }
  return (0, import_jsx_runtime2.jsxs)(Layouts.Root, {
    sideNav: (0, import_jsx_runtime2.jsx)(SettingsNav, {
      menu
    }),
    children: [
      (0, import_jsx_runtime2.jsx)(Page.Title, {
        children: formatMessage({
          id: "global.settings",
          defaultMessage: "Settings"
        })
      }),
      (0, import_jsx_runtime2.jsx)(Outlet, {})
    ]
  });
};
export {
  Layout
};
//# sourceMappingURL=Layout-65QL3ALE.js.map
