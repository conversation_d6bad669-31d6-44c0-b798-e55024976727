import {
  ConfirmDialog
} from "./chunk-NWWGC2Z2.js";
import {
  intervalToDuration,
  isPast
} from "./chunk-DD3MYA7D.js";
import {
  useTracking
} from "./chunk-XDCEA27D.js";
import {
  Table
} from "./chunk-PVCRV2LE.js";
import {
  useQueryParams
} from "./chunk-ODQFI753.js";
import {
  Box,
  Dialog,
  Flex,
  IconButton,
  LinkButton,
  Typography,
  useCollator,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  NavLink,
  useNavigate
} from "./chunk-S65ZWNEO.js";
import {
  ForwardRef$1v,
  ForwardRef$j
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/components/Tokens/Table.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/components/RelativeTime.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var intervals = [
  "years",
  "months",
  "days",
  "hours",
  "minutes",
  "seconds"
];
var RelativeTime = React.forwardRef(({ timestamp, customIntervals = [], ...restProps }, forwardedRef) => {
  const { formatRelativeTime, formatDate, formatTime } = useIntl();
  const interval = intervalToDuration({
    start: timestamp,
    end: Date.now()
  });
  const unit = intervals.find((intervalUnit) => {
    return interval[intervalUnit] > 0 && Object.keys(interval).includes(intervalUnit);
  });
  const relativeTime = isPast(timestamp) ? -interval[unit] : interval[unit];
  const customInterval = customIntervals.find((custom) => interval[custom.unit] < custom.threshold);
  const displayText = customInterval ? customInterval.text : formatRelativeTime(relativeTime, unit, {
    numeric: "auto"
  });
  return (0, import_jsx_runtime.jsx)("time", {
    ref: forwardedRef,
    dateTime: timestamp.toISOString(),
    role: "time",
    title: `${formatDate(timestamp)} ${formatTime(timestamp)}`,
    ...restProps,
    children: displayText
  });
});

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/components/Tokens/Table.mjs
var Table2 = ({ permissions, headers = [], isLoading = false, tokens = [], onConfirmDelete, tokenType }) => {
  const [{ query }] = useQueryParams();
  const { formatMessage, locale } = useIntl();
  const [, sortOrder] = query && query.sort ? query.sort.split(":") : [
    void 0,
    "ASC"
  ];
  const navigate = useNavigate();
  const { trackUsage } = useTracking();
  const formatter = useCollator(locale);
  const sortedTokens = [
    ...tokens
  ].sort((a, b) => {
    return sortOrder === "DESC" ? formatter.compare(b.name, a.name) : formatter.compare(a.name, b.name);
  });
  const { canDelete, canUpdate, canRead } = permissions;
  const handleRowClick = (id) => () => {
    if (canRead) {
      trackUsage("willEditTokenFromList", {
        tokenType
      });
      navigate(id.toString());
    }
  };
  return (0, import_jsx_runtime2.jsx)(Table.Root, {
    headers,
    rows: sortedTokens,
    isLoading,
    children: (0, import_jsx_runtime2.jsxs)(Table.Content, {
      children: [
        (0, import_jsx_runtime2.jsx)(Table.Head, {
          children: headers.map((header) => (0, import_jsx_runtime2.jsx)(Table.HeaderCell, {
            ...header
          }, header.name))
        }),
        (0, import_jsx_runtime2.jsx)(Table.Empty, {}),
        (0, import_jsx_runtime2.jsx)(Table.Loading, {}),
        (0, import_jsx_runtime2.jsx)(Table.Body, {
          children: sortedTokens.map((token) => (0, import_jsx_runtime2.jsxs)(Table.Row, {
            onClick: handleRowClick(token.id),
            children: [
              (0, import_jsx_runtime2.jsx)(Table.Cell, {
                maxWidth: "25rem",
                children: (0, import_jsx_runtime2.jsx)(Typography, {
                  textColor: "neutral800",
                  fontWeight: "bold",
                  ellipsis: true,
                  children: token.name
                })
              }),
              (0, import_jsx_runtime2.jsx)(Table.Cell, {
                maxWidth: "25rem",
                children: (0, import_jsx_runtime2.jsx)(Typography, {
                  textColor: "neutral800",
                  ellipsis: true,
                  children: token.description
                })
              }),
              (0, import_jsx_runtime2.jsx)(Table.Cell, {
                children: (0, import_jsx_runtime2.jsx)(Typography, {
                  textColor: "neutral800",
                  children: (0, import_jsx_runtime2.jsx)(RelativeTime, {
                    timestamp: new Date(token.createdAt)
                  })
                })
              }),
              (0, import_jsx_runtime2.jsx)(Table.Cell, {
                children: token.lastUsedAt && (0, import_jsx_runtime2.jsx)(Typography, {
                  textColor: "neutral800",
                  children: (0, import_jsx_runtime2.jsx)(RelativeTime, {
                    timestamp: new Date(token.lastUsedAt),
                    customIntervals: [
                      {
                        unit: "hours",
                        threshold: 1,
                        text: formatMessage({
                          id: "Settings.apiTokens.lastHour",
                          defaultMessage: "last hour"
                        })
                      }
                    ]
                  })
                })
              }),
              canUpdate || canRead || canDelete ? (0, import_jsx_runtime2.jsx)(Table.Cell, {
                children: (0, import_jsx_runtime2.jsxs)(Flex, {
                  justifyContent: "end",
                  children: [
                    canUpdate && (0, import_jsx_runtime2.jsx)(UpdateButton, {
                      tokenName: token.name,
                      tokenId: token.id
                    }),
                    canDelete && (0, import_jsx_runtime2.jsx)(DeleteButton, {
                      tokenName: token.name,
                      onClickDelete: () => onConfirmDelete == null ? void 0 : onConfirmDelete(token.id),
                      tokenType
                    })
                  ]
                })
              }) : null
            ]
          }, token.id))
        })
      ]
    })
  });
};
var MESSAGES_MAP = {
  edit: {
    id: "app.component.table.edit",
    defaultMessage: "Edit {target}"
  },
  read: {
    id: "app.component.table.read",
    defaultMessage: "Read {target}"
  }
};
var DefaultButton = ({ tokenName, tokenId, buttonType = "edit", children }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime2.jsx)(LinkButtonStyled, {
    tag: NavLink,
    to: tokenId.toString(),
    onClick: (e) => e.stopPropagation(),
    title: formatMessage(MESSAGES_MAP[buttonType], {
      target: tokenName
    }),
    variant: "ghost",
    size: "S",
    children
  });
};
var LinkButtonStyled = dt(LinkButton)`
  padding: 0.7rem;

  & > span {
    display: flex;
  }
`;
var DeleteButton = ({ tokenName, onClickDelete, tokenType }) => {
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const handleClickDelete = () => {
    trackUsage("willDeleteToken", {
      tokenType
    });
    onClickDelete();
  };
  return (0, import_jsx_runtime2.jsx)(Dialog.Root, {
    children: (0, import_jsx_runtime2.jsxs)(Box, {
      paddingLeft: 1,
      onClick: (e) => e.stopPropagation(),
      children: [
        (0, import_jsx_runtime2.jsx)(Dialog.Trigger, {
          children: (0, import_jsx_runtime2.jsx)(IconButton, {
            label: formatMessage({
              id: "global.delete-target",
              defaultMessage: "Delete {target}"
            }, {
              target: `${tokenName}`
            }),
            name: "delete",
            variant: "ghost",
            children: (0, import_jsx_runtime2.jsx)(ForwardRef$j, {})
          })
        }),
        (0, import_jsx_runtime2.jsx)(ConfirmDialog, {
          onConfirm: handleClickDelete
        })
      ]
    })
  });
};
var UpdateButton = ({ tokenName, tokenId }) => {
  return (0, import_jsx_runtime2.jsx)(DefaultButton, {
    tokenName,
    tokenId,
    children: (0, import_jsx_runtime2.jsx)(ForwardRef$1v, {})
  });
};

export {
  Table2 as Table
};
//# sourceMappingURL=chunk-XMDBZNV3.js.map
