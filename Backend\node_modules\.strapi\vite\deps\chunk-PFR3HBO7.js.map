{"version": 3, "sources": ["../../../lodash/transform.js", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/hooks/usePermissionsDataManager.ts", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/utils/difference.ts", "../../../@strapi/admin/admin/src/utils/arrays.ts", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/utils/createArrayOfValues.ts", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/utils/permissions.ts", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/utils/forms.ts", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/utils/layouts.ts", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/utils/updateConditionsToFalse.ts", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/utils/updateValues.ts", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/utils/constants.ts", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/utils/removeConditionKeyFromData.ts", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/utils/getCheckboxState.ts", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/components/CollapseLabel.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/components/HiddenAction.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/components/RequiredSign.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/components/RowLabelWithCheckbox.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/components/CollapsePropertyMatrix.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/components/ConditionsButton.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/components/ConditionsModal.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/components/ContentTypeCollapses.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/components/GlobalActions.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/components/ContentTypes.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/components/PluginsAndSettings.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/components/Permissions.tsx"], "sourcesContent": ["var arrayEach = require('./_arrayEach'),\n    baseCreate = require('./_baseCreate'),\n    baseForOwn = require('./_baseForOwn'),\n    baseIteratee = require('./_baseIteratee'),\n    getPrototype = require('./_getPrototype'),\n    isArray = require('./isArray'),\n    isBuffer = require('./isBuffer'),\n    isFunction = require('./isFunction'),\n    isObject = require('./isObject'),\n    isTypedArray = require('./isTypedArray');\n\n/**\n * An alternative to `_.reduce`; this method transforms `object` to a new\n * `accumulator` object which is the result of running each of its own\n * enumerable string keyed properties thru `iteratee`, with each invocation\n * potentially mutating the `accumulator` object. If `accumulator` is not\n * provided, a new object with the same `[[Prototype]]` will be used. The\n * iteratee is invoked with four arguments: (accumulator, value, key, object).\n * Iteratee functions may exit iteration early by explicitly returning `false`.\n *\n * @static\n * @memberOf _\n * @since 1.3.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @param {*} [accumulator] The custom accumulator value.\n * @returns {*} Returns the accumulated value.\n * @example\n *\n * _.transform([2, 3, 4], function(result, n) {\n *   result.push(n *= n);\n *   return n % 2 == 0;\n * }, []);\n * // => [4, 9]\n *\n * _.transform({ 'a': 1, 'b': 2, 'c': 1 }, function(result, value, key) {\n *   (result[value] || (result[value] = [])).push(key);\n * }, {});\n * // => { '1': ['a', 'c'], '2': ['b'] }\n */\nfunction transform(object, iteratee, accumulator) {\n  var isArr = isArray(object),\n      isArrLike = isArr || isBuffer(object) || isTypedArray(object);\n\n  iteratee = baseIteratee(iteratee, 4);\n  if (accumulator == null) {\n    var Ctor = object && object.constructor;\n    if (isArrLike) {\n      accumulator = isArr ? new Ctor : [];\n    }\n    else if (isObject(object)) {\n      accumulator = isFunction(Ctor) ? baseCreate(getPrototype(object)) : {};\n    }\n    else {\n      accumulator = {};\n    }\n  }\n  (isArrLike ? arrayEach : baseForOwn)(object, function(value, index, object) {\n    return iteratee(accumulator, value, index, object);\n  });\n  return accumulator;\n}\n\nmodule.exports = transform;\n", "import { createContext } from '@radix-ui/react-context';\n\nimport { Condition } from '../../../../../../../shared/contracts/permissions';\n\nimport type {\n  OnChangeCollectionTypeGlobalActionCheckboxAction,\n  OnChangeCollectionTypeRowLeftCheckboxAction,\n  OnChangeConditionsAction,\n  State,\n} from '../components/Permissions';\n\n// Note: I had to guess most of these types based on the name and usage, but I actually don't\n// know if they are correct, because the usage is very generic. Feel free to correct them if\n// they create problems.\nexport interface PermissionsDataManagerContextValue extends Pick<State, 'modifiedData'> {\n  availableConditions: Condition[];\n  onChangeCollectionTypeLeftActionRowCheckbox: (\n    pathToCollectionType: OnChangeCollectionTypeRowLeftCheckboxAction['pathToCollectionType'],\n    propertyName: OnChangeCollectionTypeRowLeftCheckboxAction['propertyName'],\n    rowName: OnChangeCollectionTypeRowLeftCheckboxAction['rowName'],\n    value: OnChangeCollectionTypeRowLeftCheckboxAction['value']\n  ) => void;\n  onChangeConditions: (conditions: OnChangeConditionsAction['conditions']) => void;\n  onChangeSimpleCheckbox: (event: { target: { name: string; value: boolean } }) => void;\n  onChangeParentCheckbox: (event: { target: { name: string; value: boolean } }) => void;\n  onChangeCollectionTypeGlobalActionCheckbox: (\n    collectionTypeKind: OnChangeCollectionTypeGlobalActionCheckboxAction['collectionTypeKind'],\n    actionId: OnChangeCollectionTypeGlobalActionCheckboxAction['actionId'],\n    value: OnChangeCollectionTypeGlobalActionCheckboxAction['value']\n  ) => void;\n}\n\nconst [PermissionsDataManagerProvider, usePermissionsDataManagerContext] =\n  createContext<PermissionsDataManagerContextValue>('PermissionsDataManager');\n\nexport const usePermissionsDataManager = () =>\n  usePermissionsDataManagerContext('usePermissionsDataManager');\n\nexport { PermissionsDataManagerProvider };\n", "import isEqual from 'lodash/isEqual';\nimport isObject from 'lodash/isObject';\nimport transform from 'lodash/transform';\n\ntype ObjectDiff<T> = {\n  [P in keyof T]?: T[P] extends Record<string, unknown> ? ObjectDiff<T[P]> : T[P];\n};\n\nfunction difference<T extends Record<string, unknown>>(object: T, base: T): ObjectDiff<T> {\n  function changes(object: T, base: T): ObjectDiff<T> {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return transform(object, (result, value: any, key: keyof ObjectDiff<T>) => {\n      if (!isEqual(value, base[key])) {\n        result[key] =\n          isObject(value) && isObject(base[key]) ? changes(value as T, base[key] as T) : value;\n      }\n      return result;\n    });\n  }\n\n  return changes(object, base);\n}\n\nexport { difference };\n", "/**\n * @internal\n * @description Mutates a value to be a union of flat values, no arrays allowed.\n */\ntype Flat<T> = T extends string ? T : T extends ArrayLike<any> ? never : T;\n\n/**\n * @internal\n */\ninterface RecursiveArray<T> extends Array<T | RecursiveArray<T>> {}\n/**\n * @internal\n */\ninterface ArrayOfRecursiveArraysOrValues<T> extends ArrayLike<T | RecursiveArray<T>> {}\n\n/**\n * @internal\n *\n * @description Flattens an array recursively.\n */\nconst flattenDeep = <T>(\n  array?: ArrayOfRecursiveArraysOrValues<T> | null | undefined\n): Array<Flat<T>> => {\n  if (Array.isArray(array)) {\n    return array.reduce(\n      (acc, value) => {\n        if (Array.isArray(value)) {\n          acc.push(...flattenDeep(value));\n        } else {\n          acc.push(value);\n        }\n\n        return acc;\n      },\n      [] as Array<Flat<T>>\n    );\n  } else {\n    return [];\n  }\n};\n\nexport { flattenDeep };\nexport type { Flat, RecursiveArray, ArrayOfRecursiveArraysOrValues };\n", "import { Flat, flattenDeep } from '../../../../../utils/arrays';\nimport { isObject } from '../../../../../utils/objects';\n\nconst createArrayOfValues = <TData>(obj: unknown): Array<Flat<TData>> => {\n  if (!isObject(obj)) {\n    return [];\n  }\n\n  return flattenDeep(\n    Object.values(obj).map((value) => {\n      if (isObject(value)) {\n        return createArrayOfValues(value);\n      }\n\n      return value;\n    })\n  );\n};\n\nexport { createArrayOfValues };\n", "import { isObject } from '../../../../../utils/objects';\n\nimport { createArrayOfValues } from './createArrayOfValues';\n\nimport type { ConditionForm, Form, PropertyChildForm } from './forms';\nimport type { UpdatePermissions } from '../../../../../../../shared/contracts/roles';\nimport type { Permission } from '../../../../../../../shared/contracts/shared';\nimport type { PermissionsDataManagerContextValue } from '../hooks/usePermissionsDataManager';\n\ntype PermissionApiBody = UpdatePermissions.Request['body']['permissions'];\n\n/**\n * @description Given a users permissions array we find the first one that matches a provided subject & action\n */\nconst findMatchingPermission = (\n  permissions: Permission[],\n  action: string,\n  subject: string | null\n) => permissions.find((perm) => perm.action === action && perm.subject === subject);\n\nconst formatPermissionsForAPI = (\n  modifiedData: PermissionsDataManagerContextValue['modifiedData']\n): PermissionApiBody => {\n  const pluginsPermissions = formatSettingsPermissions(modifiedData.plugins);\n  const settingsPermissions = formatSettingsPermissions(modifiedData.settings);\n  const collectionTypesPermissions = formatContentTypesPermissions(modifiedData.collectionTypes);\n  const singleTypesPermissions = formatContentTypesPermissions(modifiedData.singleTypes);\n\n  return [\n    ...pluginsPermissions,\n    ...settingsPermissions,\n    ...collectionTypesPermissions,\n    ...singleTypesPermissions,\n  ];\n};\n\nconst formatSettingsPermissions = (\n  settingsPermissionsObject:\n    | PermissionsDataManagerContextValue['modifiedData']['plugins']\n    | PermissionsDataManagerContextValue['modifiedData']['settings']\n): PermissionApiBody => {\n  return Object.values(settingsPermissionsObject).reduce<PermissionApiBody>((formAcc, form) => {\n    const currentCategoryPermissions = Object.values(form).reduce<PermissionApiBody>(\n      (childFormAcc, childForm) => {\n        const permissions = Object.entries(childForm).reduce<PermissionApiBody>(\n          (\n            responsesAcc,\n            [\n              actionName,\n              {\n                conditions,\n                properties: { enabled },\n              },\n            ]\n          ) => {\n            if (!enabled) {\n              return responsesAcc;\n            }\n\n            responsesAcc.push({\n              action: actionName,\n              subject: null,\n              conditions: createConditionsArray(conditions),\n              properties: {},\n            });\n\n            return responsesAcc;\n          },\n          []\n        );\n\n        return [...childFormAcc, ...permissions];\n      },\n      []\n    );\n\n    return [...formAcc, ...currentCategoryPermissions];\n  }, []);\n};\n\nconst formatContentTypesPermissions = (contentTypesPermissions: Form): PermissionApiBody => {\n  const permissions = Object.entries(contentTypesPermissions).reduce<PermissionApiBody>(\n    (allPermissions, current) => {\n      const [subject, currentSubjectActions] = current;\n\n      const permissions = Object.entries(currentSubjectActions).reduce<PermissionApiBody>(\n        (acc, current) => {\n          const [actionName, permissions] = current;\n          const shouldCreatePermission = createArrayOfValues(permissions).some((val) => val);\n\n          if (!shouldCreatePermission) {\n            return acc;\n          }\n\n          if (!permissions?.properties?.enabled) {\n            const createdPermissionsArray = Object.entries(permissions.properties).reduce<\n              PermissionApiBody[number]\n            >(\n              (acc, current) => {\n                const [propertyName, propertyValue] = current;\n\n                // @ts-expect-error – `propertyValue` can be boolean or an object, but we don't account for it...\n                acc.properties[propertyName] = createPropertyArray(propertyValue);\n\n                return acc;\n              },\n              {\n                action: actionName,\n                subject,\n                conditions: createConditionsArray(permissions.conditions),\n                properties: {},\n              }\n            );\n\n            return [...acc, createdPermissionsArray];\n          }\n\n          if (!permissions.properties.enabled) {\n            return acc;\n          }\n\n          acc.push({\n            action: actionName,\n            subject,\n            properties: {},\n            conditions: createConditionsArray(permissions.conditions),\n          });\n\n          return acc;\n        },\n        []\n      );\n\n      return [...allPermissions, ...permissions];\n    },\n    []\n  );\n\n  return permissions;\n};\n\nconst createPropertyArray = (propertyValue: PropertyChildForm, prefix = ''): string[] => {\n  return Object.entries(propertyValue).reduce<string[]>((acc, current) => {\n    const [name, value] = current;\n\n    if (isObject(value)) {\n      return [...acc, ...createPropertyArray(value, `${prefix}${name}.`)];\n    }\n\n    if (value && !isObject(value)) {\n      acc.push(`${prefix}${name}`);\n    }\n\n    return acc;\n  }, []);\n};\n\nconst createConditionsArray = (conditions: ConditionForm) =>\n  Object.entries(conditions)\n    .filter(([, conditionValue]) => {\n      return conditionValue;\n    })\n    .map(([conditionName]) => conditionName);\n\nexport { findMatchingPermission, formatPermissionsForAPI };\n", "import isEmpty from 'lodash/isEmpty';\nimport merge from 'lodash/merge';\n\nimport { findMatchingPermission } from './permissions';\n\nimport type { GenericLayout } from './layouts';\nimport type {\n  Condition,\n  SettingPermission,\n  ContentPermission,\n  Subject,\n  SubjectProperty,\n} from '../../../../../../../shared/contracts/permissions';\nimport type { Permission } from '../../../../../../../shared/contracts/shared';\n\ntype ConditionForm = Record<string, boolean>;\n\n/**\n * Creates the default condition form: { [conditionId]: false }\n */\nconst createDefaultConditionsForm = (\n  conditions: Condition[],\n  initialConditions: Permission['conditions'] = []\n): ConditionForm =>\n  conditions.reduce<ConditionForm>((acc, current) => {\n    acc[current.id] = initialConditions.indexOf(current.id) !== -1;\n\n    return acc;\n  }, {});\n\ninterface SubCategoryForm {\n  properties: {\n    enabled: boolean;\n  };\n  conditions: ConditionForm;\n}\n\ntype ChildrenForm = Record<\n  string,\n  SubCategoryForm | (Omit<SubCategoryForm, 'properties'> & PropertyForm)\n>;\n\ntype Form = Record<string, ChildrenForm>;\n\nconst createDefaultForm = <TLayout extends Omit<SettingPermission, 'category'>>(\n  layout: GenericLayout<TLayout>[],\n  conditions: Condition[],\n  initialPermissions: Permission[] = []\n) => {\n  return layout.reduce<Record<string, Form>>((acc, { categoryId, childrenForm }) => {\n    const childrenDefaultForm = childrenForm.reduce<Form>((acc, current) => {\n      acc[current.subCategoryId] = current.actions.reduce<ChildrenForm>((acc, current) => {\n        const foundMatchingPermission = findMatchingPermission(\n          initialPermissions,\n          current.action,\n          null\n        );\n\n        acc[current.action] = {\n          properties: {\n            enabled: foundMatchingPermission !== undefined,\n          },\n          conditions: createDefaultConditionsForm(\n            conditions,\n            foundMatchingPermission?.conditions ?? []\n          ),\n        };\n\n        return acc;\n      }, {});\n\n      return acc;\n    }, {});\n\n    acc[categoryId] = childrenDefaultForm;\n\n    return acc;\n  }, {});\n};\n\ninterface PropertyChildForm extends Record<string, boolean | PropertyChildForm> {}\n\ninterface PropertyForm {\n  properties: PropertyChildForm;\n}\n\n/**\n * Creates the default form for all the properties found in a content type's layout\n */\nconst createDefaultPropertiesForm = (\n  properties: string[],\n  subject: Subject,\n  matchingPermission?: Permission\n): PropertyForm => {\n  const recursivelyCreatePropertyForm = (\n    { children = [] }: SubjectProperty,\n    propertyValues: string[],\n    prefix = ''\n  ): PropertyChildForm => {\n    return children.reduce<PropertyChildForm>((acc, current) => {\n      if (current.children) {\n        return {\n          ...acc,\n          [current.value]: recursivelyCreatePropertyForm(\n            current,\n            propertyValues,\n            `${prefix}${current.value}.`\n          ),\n        };\n      }\n\n      const hasProperty = propertyValues.indexOf(`${prefix}${current.value}`) !== -1;\n\n      acc[current.value] = hasProperty;\n\n      return acc;\n    }, {});\n  };\n\n  return properties.reduce<PropertyForm>(\n    (acc, currentPropertyName) => {\n      const foundProperty = subject.properties.find(({ value }) => value === currentPropertyName);\n\n      if (foundProperty) {\n        const matchingPermissionPropertyValues =\n          matchingPermission?.properties[foundProperty.value] ?? [];\n\n        const propertyForm = recursivelyCreatePropertyForm(\n          foundProperty,\n          matchingPermissionPropertyValues\n        );\n\n        acc.properties[currentPropertyName] = propertyForm;\n      }\n\n      return acc;\n    },\n    { properties: {} }\n  );\n};\n\n/**\n * Creates the default for for a content type\n */\nconst createDefaultCTForm = (\n  { subjects, actions = [] }: ContentPermission,\n  conditions: Condition[],\n  initialPermissions: Permission[] = []\n) => {\n  return actions.reduce<Form>((defaultForm, action) => {\n    type SubjectLayouts = Record<string, Subject>;\n\n    const subjectLayouts = action.subjects.reduce<SubjectLayouts>((acc, current) => {\n      const foundLayout = subjects.find(({ uid }) => uid === current) || null;\n\n      if (foundLayout) {\n        acc[current] = foundLayout;\n      }\n\n      return acc;\n    }, {});\n\n    // This can happen when an action is not related to a content type\n    // for instance the D&P permission is applied only with the cts that\n    // have the D&P features enabled\n    if (isEmpty(subjectLayouts)) {\n      return defaultForm;\n    }\n\n    // The object has the following shape: { [ctUID]: { [actionId]: { [property]: { enabled: false } } } }\n    const contentTypesActions = Object.keys(subjectLayouts).reduce<Form>((acc, currentCTUID) => {\n      const { actionId, applyToProperties } = action;\n      const currentSubjectLayout = subjectLayouts[currentCTUID];\n      const properties = currentSubjectLayout.properties.map(({ value }) => value);\n      const doesNothaveProperty = properties.every(\n        (property) => (applyToProperties || []).indexOf(property) === -1\n      );\n\n      const matchingPermission = findMatchingPermission(initialPermissions, actionId, currentCTUID);\n      const conditionsForm = createDefaultConditionsForm(\n        conditions,\n        matchingPermission?.conditions ?? []\n      );\n\n      if (!acc[currentCTUID]) {\n        acc[currentCTUID] = {};\n      }\n\n      if (isEmpty(applyToProperties) || doesNothaveProperty) {\n        acc[currentCTUID][actionId] = {\n          properties: {\n            enabled: matchingPermission !== undefined,\n          },\n          conditions: conditionsForm,\n        };\n\n        return acc;\n      }\n\n      const propertiesForm = createDefaultPropertiesForm(\n        applyToProperties,\n        subjectLayouts[currentCTUID],\n        matchingPermission\n      );\n\n      acc[currentCTUID][actionId] = { ...propertiesForm, conditions: conditionsForm };\n\n      return acc;\n    }, {});\n\n    return merge(defaultForm, contentTypesActions);\n  }, {});\n};\n\nexport { createDefaultConditionsForm, createDefaultForm, createDefaultCTForm };\nexport type { ConditionForm, Form, PropertyForm, SubCategoryForm, ChildrenForm, PropertyChildForm };\n", "import groupBy from 'lodash/groupBy';\n\nimport { SettingPermission } from '../../../../../../../shared/contracts/permissions';\n\ninterface GenericLayout<TLayout> {\n  category: string;\n  categoryId: string;\n  childrenForm: Array<{\n    subCategoryName: string;\n    subCategoryId: string;\n    actions: TLayout[];\n  }>;\n}\n\nconst formatLayout = <TLayout extends Omit<SettingPermission, 'category'>>(\n  layout: TLayout[],\n  groupByKey: keyof TLayout\n): GenericLayout<TLayout>[] => {\n  return Object.entries(groupBy(layout, groupByKey)).map(([itemName, item]) => ({\n    category: itemName,\n    categoryId: itemName.split(' ').join('-'),\n    childrenForm: Object.entries(groupBy(item, 'subCategory')).map(\n      ([subCategoryName, actions]) => ({\n        subCategoryName,\n        subCategoryId: subCategoryName.split(' ').join('-'),\n        actions,\n      })\n    ),\n  }));\n};\n\nexport { formatLayout };\nexport type { GenericLayout };\n", "import has from 'lodash/has';\nimport omit from 'lodash/omit';\n\nimport { isObject } from '../../../../../utils/objects';\n\nimport { createArrayOfValues } from './createArrayOfValues';\n/**\n * Changes all the conditions leaf when the properties are all falsy\n */\nconst updateConditionsToFalse = (obj: object): object => {\n  return Object.keys(obj).reduce((acc, current) => {\n    // @ts-expect-error – TODO: type better\n    const currentValue = obj[current];\n\n    if (isObject(currentValue) && !has(currentValue, 'conditions')) {\n      return { ...acc, [current]: updateConditionsToFalse(currentValue) };\n    }\n\n    if (isObject(currentValue) && has(currentValue, 'conditions')) {\n      const isActionEnabled = createArrayOfValues(omit(currentValue, 'conditions')).some(\n        (val) => val\n      );\n\n      if (!isActionEnabled) {\n        // @ts-expect-error – TODO: type better\n        const updatedConditions = Object.keys(currentValue.conditions).reduce((acc1, current) => {\n          // @ts-expect-error – TODO: type better\n          acc1[current] = false;\n\n          return acc1;\n        }, {});\n\n        return { ...acc, [current]: { ...currentValue, conditions: updatedConditions } };\n      }\n    }\n\n    // @ts-expect-error – TODO: type better\n    acc[current] = currentValue;\n\n    return acc;\n  }, {});\n};\n\nexport { updateConditionsToFalse };\n", "import { isObject } from '../../../../../utils/objects';\n\n/**\n * Sets all the none object values of an object to the given one\n * It preserves the shape of the object, it only modifies the leafs\n * of an object.\n * This utility is very helpful when dealing with parent<>children checkboxes\n */\nconst updateValues = (obj: object, valueToSet: boolean, isFieldUpdate = false): object => {\n  return Object.keys(obj).reduce((acc, current) => {\n    const currentValue = obj[current as keyof object];\n\n    if (current === 'conditions' && !isFieldUpdate) {\n      // @ts-expect-error – TODO: type better\n      acc[current] = currentValue;\n\n      return acc;\n    }\n\n    if (isObject(currentValue)) {\n      return { ...acc, [current]: updateValues(currentValue, valueToSet, current === 'fields') };\n    }\n\n    // @ts-expect-error – TODO: type better\n    acc[current] = valueToSet;\n\n    return acc;\n  }, {});\n};\n\nexport { updateValues };\n", "export const cellWidth = `12rem`;\nexport const firstRowWidth = `20rem`;\nexport const rowHeight = `5.3rem`;\n", "type DataWithoutCondition<TData extends { conditions?: unknown }> = Omit<TData, 'conditions'>;\n\nconst removeConditionKeyFromData = <TData extends { conditions?: unknown }>(\n  obj?: TData\n): DataWithoutCondition<TData> | null => {\n  if (!obj) {\n    return null;\n  }\n\n  return Object.entries(obj).reduce((acc, [key, value]) => {\n    if (key !== 'conditions') {\n      // @ts-expect-error – TODO: fix this type error correctly.\n      acc[key] = value;\n    }\n\n    return acc;\n  }, {} as DataWithoutCondition<TData>);\n};\n\nexport { removeConditionKeyFromData };\nexport type { DataWithoutCondition };\n", "import { createArrayOfValues } from './createArrayOfValues';\nimport { removeConditionKeyFromData } from './removeConditionKeyFromData';\n\ninterface RecursiveRecordOfBooleans extends Record<string, boolean | RecursiveRecordOfBooleans> {}\n\nconst getCheckboxState = (dataObj: RecursiveRecordOfBooleans) => {\n  const dataWithoutCondition = removeConditionKeyFromData(dataObj);\n\n  const arrayOfValues = createArrayOfValues(dataWithoutCondition);\n\n  if (!arrayOfValues.length) {\n    return { hasAllActionsSelected: false, hasSomeActionsSelected: false };\n  }\n\n  const hasAllActionsSelected = arrayOfValues.every((val) => val);\n  const hasSomeActionsSelected = arrayOfValues.some((val) => val) && !hasAllActionsSelected;\n\n  return { hasAllActionsSelected, hasSomeActionsSelected };\n};\n\nexport { getCheckboxState };\nexport type { RecursiveRecordOfBooleans };\n", "import { Flex, FlexComponent } from '@strapi/design-system';\nimport { styled } from 'styled-components';\n\nconst CollapseLabel = styled<FlexComponent>(Flex)<{ $isCollapsable: boolean }>`\n  padding-right: ${({ theme }) => theme.spaces[2]};\n  overflow: hidden;\n  flex: 1;\n  ${({ $isCollapsable }) => $isCollapsable && 'cursor: pointer;'}\n`;\n\nexport { CollapseLabel };\n", "import { styled } from 'styled-components';\n\nimport { cellWidth } from '../utils/constants';\n\nconst HiddenAction = styled.div`\n  width: ${cellWidth};\n`;\n\nexport { HiddenAction };\n", "import { Box } from '@strapi/design-system';\n\nconst RequiredSign = () => (\n  <Box color=\"danger700\" paddingLeft={1}>\n    *\n  </Box>\n);\n\nexport { RequiredSign };\n", "import * as React from 'react';\n\nimport { Checkbox, Box, Flex, Typography } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { PermissionsDataManagerContextValue } from '../hooks/usePermissionsDataManager';\nimport { firstRowWidth } from '../utils/constants';\n\nimport { CollapseLabel } from './CollapseLabel';\n\ninterface RowLabelWithCheckboxProps {\n  children: React.ReactNode;\n  checkboxName?: string;\n  isActive?: boolean;\n  isCollapsable?: boolean;\n  isFormDisabled?: boolean;\n  label: string;\n  onChange: PermissionsDataManagerContextValue['onChangeParentCheckbox'];\n  onClick: () => void;\n  someChecked?: boolean;\n  value: boolean;\n}\n\nconst RowLabelWithCheckbox = ({\n  checkboxName = '',\n  children,\n  isActive = false,\n  isCollapsable = false,\n  isFormDisabled = false,\n  label,\n  onChange,\n  onClick,\n  someChecked = false,\n  value,\n}: RowLabelWithCheckboxProps) => {\n  const { formatMessage } = useIntl();\n\n  const collapseLabelProps = {\n    title: label,\n    alignItems: 'center',\n    $isCollapsable: isCollapsable,\n  };\n\n  if (isCollapsable) {\n    Object.assign(collapseLabelProps, {\n      onClick,\n      'aria-expanded': isActive,\n      onKeyDown({ key }: React.KeyboardEvent<HTMLDivElement>) {\n        if (key === 'Enter' || key === ' ') {\n          onClick();\n        }\n      },\n      tabIndex: 0,\n      role: 'button',\n    });\n  }\n\n  return (\n    <Flex alignItems=\"center\" paddingLeft={6} width={firstRowWidth} shrink={0}>\n      <Box paddingRight={2}>\n        <Checkbox\n          name={checkboxName}\n          aria-label={formatMessage(\n            {\n              id: `Settings.permissions.select-all-by-permission`,\n              defaultMessage: 'Select all {label} permissions',\n            },\n            { label }\n          )}\n          disabled={isFormDisabled}\n          // Keep same signature as packages/core/admin/admin/src/components/Roles/Permissions/index.js l.91\n          onCheckedChange={(value) =>\n            onChange({\n              target: {\n                name: checkboxName,\n                value: !!value,\n              },\n            })\n          }\n          checked={someChecked ? 'indeterminate' : value}\n        />\n      </Box>\n      <CollapseLabel {...collapseLabelProps}>\n        <Typography ellipsis>{label}</Typography>\n        {children}\n      </CollapseLabel>\n    </Flex>\n  );\n};\n\nexport { RowLabelWithCheckbox };\nexport type { RowLabelWithCheckboxProps };\n", "import * as React from 'react';\n\nimport {\n  Checkbox,\n  Box,\n  BoxComponent,\n  Flex,\n  FlexComponent,\n  Typography,\n  TypographyComponent,\n} from '@strapi/design-system';\nimport { CaretDown } from '@strapi/icons';\nimport get from 'lodash/get';\nimport { useIntl } from 'react-intl';\nimport { styled, DefaultTheme, css } from 'styled-components';\n\nimport { Action, SubjectProperty } from '../../../../../../../shared/contracts/permissions';\nimport {\n  PermissionsDataManagerContextValue,\n  usePermissionsDataManager,\n} from '../hooks/usePermissionsDataManager';\nimport { cellWidth, firstRowWidth, rowHeight } from '../utils/constants';\nimport { getCheckboxState } from '../utils/getCheckboxState';\n\nimport { CollapseLabel } from './CollapseLabel';\nimport { HiddenAction } from './HiddenAction';\nimport { RequiredSign } from './RequiredSign';\nimport { RowLabelWithCheckbox, RowLabelWithCheckboxProps } from './RowLabelWithCheckbox';\n\n/* -------------------------------------------------------------------------------------------------\n * CollapsePropertyMatrix\n * -----------------------------------------------------------------------------------------------*/\n\ninterface CollapsePropertyMatrixProps\n  extends Pick<\n    ActionRowProps,\n    'childrenForm' | 'isFormDisabled' | 'label' | 'pathToData' | 'propertyName'\n  > {\n  availableActions?: Array<Action & { isDisplayed: boolean }>;\n}\n\ninterface PropertyAction {\n  label: string;\n  actionId: string;\n  isActionRelatedToCurrentProperty: boolean;\n}\n\nconst CollapsePropertyMatrix = ({\n  availableActions = [],\n  childrenForm = [],\n  isFormDisabled,\n  label,\n  pathToData,\n  propertyName,\n}: CollapsePropertyMatrixProps) => {\n  const propertyActions = React.useMemo(\n    () =>\n      availableActions.map((action) => {\n        const isActionRelatedToCurrentProperty =\n          Array.isArray(action.applyToProperties) &&\n          action.applyToProperties.indexOf(propertyName) !== -1 &&\n          action.isDisplayed;\n\n        return { label: action.label, actionId: action.actionId, isActionRelatedToCurrentProperty };\n      }) satisfies PropertyAction[],\n    [availableActions, propertyName]\n  );\n\n  return (\n    <Flex display=\"inline-flex\" direction=\"column\" alignItems=\"stretch\" minWidth={0}>\n      <Header label={label} headers={propertyActions} />\n      <Box>\n        {childrenForm.map(({ children: childrenForm, label, value, required }, i) => (\n          <ActionRow\n            childrenForm={childrenForm}\n            key={value}\n            label={label}\n            isFormDisabled={isFormDisabled}\n            name={value}\n            required={required}\n            propertyActions={propertyActions}\n            pathToData={pathToData}\n            propertyName={propertyName}\n            isOdd={i % 2 === 0}\n          />\n        ))}\n      </Box>\n    </Flex>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ActionRow\n * -----------------------------------------------------------------------------------------------*/\n\ninterface ActionRowProps\n  extends Pick<\n    SubActionRowProps,\n    'childrenForm' | 'isFormDisabled' | 'propertyActions' | 'propertyName'\n  > {\n  label: string;\n  name: string;\n  required?: boolean;\n  pathToData: string;\n  isOdd?: boolean;\n}\n\nconst ActionRow = ({\n  childrenForm = [],\n  label,\n  isFormDisabled = false,\n  name,\n  required = false,\n  pathToData,\n  propertyActions,\n  propertyName,\n  isOdd = false,\n}: ActionRowProps) => {\n  const { formatMessage } = useIntl();\n  const [rowToOpen, setRowToOpen] = React.useState<string | null>(null);\n  const {\n    modifiedData,\n    onChangeCollectionTypeLeftActionRowCheckbox,\n    onChangeParentCheckbox,\n    onChangeSimpleCheckbox,\n  } = usePermissionsDataManager();\n\n  const isActive = rowToOpen === name;\n\n  const recursiveChildren = React.useMemo(() => {\n    if (!Array.isArray(childrenForm)) {\n      return [];\n    }\n\n    return childrenForm;\n  }, [childrenForm]);\n\n  const isCollapsable = recursiveChildren.length > 0;\n\n  const handleClick = React.useCallback(() => {\n    if (isCollapsable) {\n      setRowToOpen((prev) => {\n        if (prev === name) {\n          return null;\n        }\n\n        return name;\n      });\n    }\n  }, [isCollapsable, name]);\n\n  const handleChangeLeftRowCheckbox: RowLabelWithCheckboxProps['onChange'] = ({\n    target: { value },\n  }) => {\n    onChangeCollectionTypeLeftActionRowCheckbox(pathToData, propertyName, name, value);\n  };\n\n  const { hasAllActionsSelected, hasSomeActionsSelected } = React.useMemo(() => {\n    return getRowLabelCheckboxState(propertyActions, modifiedData, pathToData, propertyName, name);\n  }, [propertyActions, modifiedData, pathToData, propertyName, name]);\n\n  return (\n    <>\n      <Wrapper\n        alignItems=\"center\"\n        $isCollapsable={isCollapsable}\n        $isActive={isActive}\n        background={isOdd ? 'neutral100' : 'neutral0'}\n      >\n        <Flex>\n          <RowLabelWithCheckbox\n            onChange={handleChangeLeftRowCheckbox}\n            onClick={handleClick}\n            isCollapsable={isCollapsable}\n            isFormDisabled={isFormDisabled}\n            label={label}\n            someChecked={hasSomeActionsSelected}\n            value={hasAllActionsSelected}\n            isActive={isActive}\n          >\n            {required && <RequiredSign />}\n            <CarretIcon $isActive={isActive} />\n          </RowLabelWithCheckbox>\n          <Flex>\n            {propertyActions.map(({ label, isActionRelatedToCurrentProperty, actionId }) => {\n              if (!isActionRelatedToCurrentProperty) {\n                return <HiddenAction key={label} />;\n              }\n\n              const checkboxName = [\n                ...pathToData.split('..'),\n                actionId,\n                'properties',\n                propertyName,\n                name,\n              ];\n\n              if (!isCollapsable) {\n                const checkboxValue = get(modifiedData, checkboxName, false);\n\n                return (\n                  <Flex\n                    key={actionId}\n                    width={cellWidth}\n                    position=\"relative\"\n                    justifyContent=\"center\"\n                    alignItems=\"center\"\n                  >\n                    <Checkbox\n                      disabled={isFormDisabled}\n                      name={checkboxName.join('..')}\n                      aria-label={formatMessage(\n                        {\n                          id: `Settings.permissions.select-by-permission`,\n                          defaultMessage: 'Select {label} permission',\n                        },\n                        { label: `${name} ${label}` }\n                      )}\n                      onCheckedChange={(value) => {\n                        onChangeSimpleCheckbox({\n                          target: {\n                            name: checkboxName.join('..'),\n                            value: !!value,\n                          },\n                        });\n                      }}\n                      checked={checkboxValue}\n                    />\n                  </Flex>\n                );\n              }\n\n              const data = get(modifiedData, checkboxName, {});\n\n              const { hasAllActionsSelected, hasSomeActionsSelected } = getCheckboxState(data);\n\n              return (\n                <Flex\n                  key={label}\n                  width={cellWidth}\n                  position=\"relative\"\n                  justifyContent=\"center\"\n                  alignItems=\"center\"\n                >\n                  <Checkbox\n                    disabled={isFormDisabled}\n                    name={checkboxName.join('..')}\n                    onCheckedChange={(value) => {\n                      onChangeParentCheckbox({\n                        target: {\n                          name: checkboxName.join('..'),\n                          value: !!value,\n                        },\n                      });\n                    }}\n                    aria-label={formatMessage(\n                      {\n                        id: `Settings.permissions.select-by-permission`,\n                        defaultMessage: 'Select {label} permission',\n                      },\n                      { label: `${name} ${label}` }\n                    )}\n                    checked={hasSomeActionsSelected ? 'indeterminate' : hasAllActionsSelected}\n                  />\n                </Flex>\n              );\n            })}\n          </Flex>\n        </Flex>\n      </Wrapper>\n      {isActive && (\n        <SubActionRow\n          childrenForm={recursiveChildren}\n          isFormDisabled={isFormDisabled}\n          parentName={name}\n          pathToDataFromActionRow={pathToData}\n          propertyName={propertyName}\n          propertyActions={propertyActions}\n          recursiveLevel={0}\n        />\n      )}\n    </>\n  );\n};\n\n/**\n *\n * Returns the state of the left checkbox of a ActionRow main checkbox\n */\nconst getRowLabelCheckboxState = (\n  propertyActions: PropertyAction[],\n  modifiedData: PermissionsDataManagerContextValue['modifiedData'],\n  pathToContentType: string,\n  propertyToCheck: string,\n  targetKey: string\n) => {\n  const actionIds = propertyActions.reduce<string[]>((acc, current) => {\n    if (current.isActionRelatedToCurrentProperty) {\n      acc.push(current.actionId);\n    }\n\n    return acc;\n  }, []);\n\n  const data = actionIds.reduce<Record<string, boolean>>((acc, current) => {\n    const mainData = get(\n      modifiedData,\n      [...pathToContentType.split('..'), current, 'properties', propertyToCheck, targetKey],\n      false\n    );\n\n    acc[current] = mainData;\n\n    return acc;\n  }, {});\n\n  return getCheckboxState(data);\n};\n\nconst Wrapper = styled<FlexComponent>(Flex)<{ $isCollapsable?: boolean; $isActive?: boolean }>`\n  height: ${rowHeight};\n  flex: 1;\n\n  &:hover {\n    ${({ $isCollapsable, theme }) => $isCollapsable && activeStyle(theme)}\n  }\n\n  ${({ $isCollapsable }) =>\n    $isCollapsable &&\n    `\n      ${CarretIcon} {\n        display: flex;\n      }\n  `}\n  ${({ $isActive, theme }) => $isActive && activeStyle(theme)};\n`;\n\nconst CarretIcon = styled(CaretDown)<{ $isActive: boolean }>`\n  display: none;\n\n  svg {\n    width: 1.4rem;\n  }\n\n  path {\n    fill: ${({ theme }) => theme.colors.neutral200};\n  }\n\n  transform: rotate(${({ $isActive }) => ($isActive ? '180' : '0')}deg);\n  margin-left: ${({ theme }) => theme.spaces[2]};\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * SubActionRow\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SubActionRowProps {\n  childrenForm: SubjectProperty['children'];\n  isFormDisabled?: boolean;\n  parentName: string;\n  pathToDataFromActionRow: string;\n  propertyActions: PropertyAction[];\n  propertyName: string;\n  recursiveLevel: number;\n}\n\nconst SubActionRow = ({\n  childrenForm = [],\n  isFormDisabled,\n  recursiveLevel,\n  pathToDataFromActionRow,\n  propertyActions,\n  parentName,\n  propertyName,\n}: SubActionRowProps) => {\n  const { formatMessage } = useIntl();\n  const { modifiedData, onChangeParentCheckbox, onChangeSimpleCheckbox } =\n    usePermissionsDataManager();\n  const [rowToOpen, setRowToOpen] = React.useState<string | null>(null);\n\n  const handleClickToggleSubLevel = (name: string) => {\n    setRowToOpen((prev) => {\n      if (prev === name) {\n        return null;\n      }\n\n      return name;\n    });\n  };\n\n  const displayedRecursiveChildren = React.useMemo(() => {\n    if (!rowToOpen) {\n      return null;\n    }\n\n    return childrenForm.find(({ value }) => value === rowToOpen);\n  }, [rowToOpen, childrenForm]);\n\n  return (\n    <Box paddingLeft={`3.2rem`}>\n      <TopTimeline />\n      {childrenForm.map(({ label, value, required, children: subChildrenForm }, index) => {\n        const isVisible = index + 1 < childrenForm.length;\n        const isArrayType = Array.isArray(subChildrenForm);\n        const isActive = rowToOpen === value;\n\n        return (\n          <LeftBorderTimeline key={value} $isVisible={isVisible}>\n            <Flex height={rowHeight}>\n              <StyledBox>\n                <Svg\n                  width=\"20\"\n                  height=\"23\"\n                  viewBox=\"0 0 20 23\"\n                  fill=\"none\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  $color=\"primary200\"\n                >\n                  <path\n                    fillRule=\"evenodd\"\n                    clipRule=\"evenodd\"\n                    d=\"M7.02477 14.7513C8.65865 17.0594 11.6046 18.6059 17.5596 18.8856C18.6836 18.9384 19.5976 19.8435 19.5976 20.9688V20.9688C19.5976 22.0941 18.6841 23.0125 17.5599 22.9643C10.9409 22.6805 6.454 20.9387 3.75496 17.1258C0.937988 13.1464 0.486328 7.39309 0.486328 0.593262H4.50974C4.50974 7.54693 5.06394 11.9813 7.02477 14.7513Z\"\n                    fill=\"#D9D8FF\"\n                  />\n                </Svg>\n              </StyledBox>\n              <Flex style={{ flex: 1 }}>\n                <RowStyle $level={recursiveLevel} $isActive={isActive} $isCollapsable={isArrayType}>\n                  <CollapseLabel\n                    alignItems=\"center\"\n                    $isCollapsable={isArrayType}\n                    {...(isArrayType && {\n                      onClick: () => handleClickToggleSubLevel(value),\n                      'aria-expanded': isActive,\n                      onKeyDown: ({ key }: React.KeyboardEvent<HTMLDivElement>) =>\n                        (key === 'Enter' || key === ' ') && handleClickToggleSubLevel(value),\n                      tabIndex: 0,\n                      role: 'button',\n                    })}\n                    title={label}\n                  >\n                    <RowLabel ellipsis>{label}</RowLabel>\n                    {required && <RequiredSign />}\n                    <CarretIcon $isActive={isActive} />\n                  </CollapseLabel>\n                </RowStyle>\n                <Flex style={{ flex: 1 }}>\n                  {propertyActions.map(\n                    ({ actionId, label: propertyLabel, isActionRelatedToCurrentProperty }) => {\n                      if (!isActionRelatedToCurrentProperty) {\n                        return <HiddenAction key={actionId} />;\n                      }\n                      /*\n                       * Usually we use a 'dot' in order to know the key path of an object for which we want to change the value.\n                       * Since an action and a subject are both separated by '.' or '::' we chose to use the '..' separators\n                       */\n                      const checkboxName = [\n                        ...pathToDataFromActionRow.split('..'),\n                        actionId,\n                        'properties',\n                        propertyName,\n                        ...parentName.split('..'),\n                        value,\n                      ];\n\n                      const checkboxValue = get(modifiedData, checkboxName, false);\n\n                      if (!subChildrenForm) {\n                        return (\n                          <Flex\n                            key={propertyLabel}\n                            position=\"relative\"\n                            width={cellWidth}\n                            justifyContent=\"center\"\n                            alignItems=\"center\"\n                          >\n                            <Checkbox\n                              disabled={isFormDisabled}\n                              name={checkboxName.join('..')}\n                              aria-label={formatMessage(\n                                {\n                                  id: `Settings.permissions.select-by-permission`,\n                                  defaultMessage: 'Select {label} permission',\n                                },\n                                { label: `${parentName} ${label} ${propertyLabel}` }\n                              )}\n                              onCheckedChange={(value) => {\n                                onChangeSimpleCheckbox({\n                                  target: {\n                                    name: checkboxName.join('..'),\n                                    value: !!value,\n                                  },\n                                });\n                              }}\n                              checked={checkboxValue}\n                            />\n                          </Flex>\n                        );\n                      }\n\n                      const { hasAllActionsSelected, hasSomeActionsSelected } =\n                        getCheckboxState(checkboxValue);\n\n                      return (\n                        <Flex\n                          key={propertyLabel}\n                          position=\"relative\"\n                          width={cellWidth}\n                          justifyContent=\"center\"\n                          alignItems=\"center\"\n                        >\n                          <Checkbox\n                            key={propertyLabel}\n                            disabled={isFormDisabled}\n                            name={checkboxName.join('..')}\n                            aria-label={formatMessage(\n                              {\n                                id: `Settings.permissions.select-by-permission`,\n                                defaultMessage: 'Select {label} permission',\n                              },\n                              { label: `${parentName} ${label} ${propertyLabel}` }\n                            )}\n                            // Keep same signature as packages/core/admin/admin/src/components/Roles/Permissions/index.js l.91\n                            onCheckedChange={(value) => {\n                              onChangeParentCheckbox({\n                                target: {\n                                  name: checkboxName.join('..'),\n                                  value: !!value,\n                                },\n                              });\n                            }}\n                            checked={\n                              hasSomeActionsSelected ? 'indeterminate' : hasAllActionsSelected\n                            }\n                          />\n                        </Flex>\n                      );\n                    }\n                  )}\n                </Flex>\n              </Flex>\n            </Flex>\n            {displayedRecursiveChildren && isActive && (\n              <Box paddingBottom={2}>\n                <SubActionRow\n                  isFormDisabled={isFormDisabled}\n                  parentName={`${parentName}..${value}`}\n                  pathToDataFromActionRow={pathToDataFromActionRow}\n                  propertyActions={propertyActions}\n                  propertyName={propertyName}\n                  recursiveLevel={recursiveLevel + 1}\n                  childrenForm={displayedRecursiveChildren.children}\n                />\n              </Box>\n            )}\n          </LeftBorderTimeline>\n        );\n      })}\n    </Box>\n  );\n};\n\nconst LeftBorderTimeline = styled<BoxComponent>(Box)<{ $isVisible?: boolean }>`\n  border-left: ${({ $isVisible, theme }) =>\n    $isVisible ? `4px solid ${theme.colors.primary200}` : '4px solid transparent'};\n`;\n\nconst RowStyle = styled<FlexComponent>(Flex)<{\n  $level: number;\n  $isCollapsable?: boolean;\n  $isActive?: boolean;\n}>`\n  padding-left: ${({ theme }) => theme.spaces[4]};\n  width: ${({ $level }) => 145 - $level * 36}px;\n\n  &:hover {\n    ${({ $isCollapsable, theme }) => $isCollapsable && activeStyle(theme)}\n  }\n\n  ${({ $isCollapsable }) =>\n    $isCollapsable &&\n    `\n      ${CarretIcon} {\n        display: flex;\n      }\n  `}\n  ${({ $isActive, theme }) => $isActive && activeStyle(theme)};\n`;\n\nconst RowLabel = styled<TypographyComponent>(Typography)``;\n\nconst TopTimeline = styled.div`\n  padding-top: ${({ theme }) => theme.spaces[2]};\n  margin-top: ${({ theme }) => theme.spaces[2]};\n  width: 0.4rem;\n  background-color: ${({ theme }) => theme.colors.primary200};\n  border-top-left-radius: 2px;\n  border-top-right-radius: 2px;\n`;\n\nconst StyledBox = styled<BoxComponent>(Box)`\n  transform: translate(-4px, -12px);\n\n  &:before {\n    content: '';\n    width: 0.4rem;\n    height: 1.2rem;\n    background: ${({ theme }) => theme.colors.primary200};\n    display: block;\n  }\n`;\n\nconst Svg = styled.svg<{ $color: keyof DefaultTheme['colors'] }>`\n  position: relative;\n  flex-shrink: 0;\n  transform: translate(-0.5px, -1px);\n\n  * {\n    fill: ${({ theme, $color }) => theme.colors[$color]};\n  }\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * Header\n * -----------------------------------------------------------------------------------------------*/\n\ninterface HeaderProps {\n  headers?: PropertyAction[];\n  label: string;\n}\n\nconst Header = ({ headers = [], label }: HeaderProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Flex>\n      <Flex width={firstRowWidth} height={rowHeight} shrink={0} alignItems=\"center\" paddingLeft={6}>\n        <Typography variant=\"sigma\" textColor=\"neutral500\">\n          {formatMessage(\n            {\n              id: 'Settings.roles.form.permission.property-label',\n              defaultMessage: '{label} permissions',\n            },\n            { label }\n          )}\n        </Typography>\n      </Flex>\n      {headers.map((header) => {\n        if (!header.isActionRelatedToCurrentProperty) {\n          return <Flex width={cellWidth} shrink={0} key={header.label} />;\n        }\n\n        return (\n          <Flex width={cellWidth} shrink={0} justifyContent=\"center\" key={header.label}>\n            <Typography variant=\"sigma\" textColor=\"neutral500\">\n              {formatMessage({\n                id: `Settings.roles.form.permissions.${header.label.toLowerCase()}`,\n                defaultMessage: header.label,\n              })}\n            </Typography>\n          </Flex>\n        );\n      })}\n    </Flex>\n  );\n};\n\nconst activeStyle = (theme: DefaultTheme) => css`\n  color: ${theme.colors.primary600};\n  font-weight: ${theme.fontWeights.bold};\n\n  ${CarretIcon} {\n    path {\n      fill: ${theme.colors.primary600};\n    }\n  }\n`;\n\nexport { CollapsePropertyMatrix };\n", "import * as React from 'react';\n\nimport { Box, BoxComponent, Button, ButtonProps } from '@strapi/design-system';\nimport { Cog } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\ninterface ConditionsButtonProps extends Pick<ButtonProps, 'className' | 'onClick' | 'variant'> {\n  hasConditions?: boolean;\n}\n\nconst ConditionsButtonImpl = React.forwardRef<HTMLButtonElement, ConditionsButtonProps>(\n  ({ onClick, className, hasConditions = false, variant = 'tertiary' }, ref) => {\n    const { formatMessage } = useIntl();\n\n    return (\n      <ButtonContainer $hasConditions={hasConditions} className={className}>\n        <Button variant={variant} startIcon={<Cog />} onClick={onClick} ref={ref} type=\"button\">\n          {formatMessage({\n            id: 'global.settings',\n            defaultMessage: 'Settings',\n          })}\n        </Button>\n      </ButtonContainer>\n    );\n  }\n);\n\nconst ButtonContainer = styled<BoxComponent>(Box)<{ $hasConditions?: boolean }>`\n  ${({ $hasConditions, theme }) =>\n    $hasConditions &&\n    `\n    &:before {\n      content: '';\n      position: absolute;\n      top: -3px;\n      left: -10px;\n      width: 6px;\n      height: 6px;\n      border-radius: 2rem;\n      background: ${theme.colors.primary600};\n    }\n  `}\n`;\n\n/**\n * We reference the component directly in other styled-components\n * and as such we need it to have a className already assigned.\n * Therefore we wrapped the implementation in a styled function.\n */\nconst ConditionsButton = styled(ConditionsButtonImpl)``;\n\nexport { ConditionsButton };\nexport type { ConditionsButtonProps };\n", "import * as React from 'react';\n\nimport {\n  Box,\n  Button,\n  Flex,\n  Modal,\n  MultiSelectNested,\n  MultiSelectNestedProps,\n  Typography,\n  Breadcrumbs,\n  Crumb,\n} from '@strapi/design-system';\nimport { produce } from 'immer';\nimport get from 'lodash/get';\nimport groupBy from 'lodash/groupBy';\nimport upperFirst from 'lodash/upperFirst';\nimport { useIntl } from 'react-intl';\n\nimport { capitalise } from '../../../../../utils/strings';\nimport {\n  PermissionsDataManagerContextValue,\n  usePermissionsDataManager,\n} from '../hooks/usePermissionsDataManager';\n\nimport type { HiddenCheckboxAction, VisibleCheckboxAction } from './ContentTypeCollapses';\nimport type { ConditionForm } from '../utils/forms';\n\n/* -------------------------------------------------------------------------------------------------\n * ConditionsModal\n * -----------------------------------------------------------------------------------------------*/\n\ninterface ConditionAction extends Pick<ActionRowProps, 'label'> {\n  actionId: string;\n  isDisplayed: boolean;\n  hasSomeActionsSelected?: boolean;\n  hasAllActionsSelected?: boolean;\n  pathToConditionsObject: string[];\n}\n\ninterface ConditionsModalProps extends Pick<ActionRowProps, 'isFormDisabled'> {\n  actions?: Array<ConditionAction | HiddenCheckboxAction | VisibleCheckboxAction>;\n  headerBreadCrumbs?: string[];\n  onClose?: () => void;\n}\n\nconst ConditionsModal = ({\n  actions = [],\n  headerBreadCrumbs = [],\n  isFormDisabled,\n  onClose,\n}: ConditionsModalProps) => {\n  const { formatMessage } = useIntl();\n  const { availableConditions, modifiedData, onChangeConditions } = usePermissionsDataManager();\n\n  const arrayOfOptionsGroupedByCategory = React.useMemo(() => {\n    return Object.entries(groupBy(availableConditions, 'category'));\n  }, [availableConditions]);\n\n  const actionsToDisplay = actions.filter<VisibleCheckboxAction | ConditionAction>(\n    // @ts-expect-error – TODO: fix this type issue\n    ({ isDisplayed, hasSomeActionsSelected, hasAllActionsSelected }) =>\n      isDisplayed && Boolean(hasSomeActionsSelected || hasAllActionsSelected)\n  );\n\n  const [state, setState] = React.useState(\n    createDefaultConditionsForm(actionsToDisplay, modifiedData, arrayOfOptionsGroupedByCategory)\n  );\n\n  const handleChange = (name: string, values: ConditionForm) => {\n    setState(\n      produce((draft) => {\n        if (!draft[name]) {\n          draft[name] = {};\n        }\n\n        if (!draft[name].default) {\n          draft[name].default = {};\n        }\n\n        draft[name].default = values;\n      })\n    );\n  };\n\n  const handleSubmit = () => {\n    const conditionsWithoutCategory = Object.entries(state).reduce<Record<string, ConditionForm>>(\n      (acc, current) => {\n        const [key, value] = current;\n\n        const merged = Object.values(value).reduce((acc1, current1) => {\n          return { ...acc1, ...current1 };\n        }, {});\n\n        acc[key] = merged;\n\n        return acc;\n      },\n      {}\n    );\n\n    onChangeConditions(conditionsWithoutCategory);\n    onClose && onClose();\n  };\n\n  const onCloseModal = () => {\n    setState(\n      createDefaultConditionsForm(actionsToDisplay, modifiedData, arrayOfOptionsGroupedByCategory)\n    );\n\n    onClose && onClose();\n  };\n\n  return (\n    <Modal.Content>\n      <Modal.Header>\n        <Breadcrumbs id=\"condition-modal-breadcrumbs\" label={headerBreadCrumbs.join(', ')}>\n          {headerBreadCrumbs.map((label, index, arr) => (\n            <Crumb isCurrent={index === arr.length - 1} key={label}>\n              {upperFirst(\n                formatMessage({\n                  id: label,\n                  defaultMessage: label,\n                })\n              )}\n            </Crumb>\n          ))}\n        </Breadcrumbs>\n      </Modal.Header>\n      <Modal.Body>\n        {actionsToDisplay.length === 0 && (\n          <Typography>\n            {formatMessage({\n              id: 'Settings.permissions.conditions.no-actions',\n              defaultMessage:\n                'You first need to select actions (create, read, update, ...) before defining conditions on them.',\n            })}\n          </Typography>\n        )}\n        <ul>\n          {actionsToDisplay.map(({ actionId, label, pathToConditionsObject }, index) => {\n            const name = pathToConditionsObject.join('..');\n\n            return (\n              <ActionRow\n                key={actionId}\n                arrayOfOptionsGroupedByCategory={arrayOfOptionsGroupedByCategory}\n                label={label}\n                isFormDisabled={isFormDisabled}\n                isGrey={index % 2 === 0}\n                name={name}\n                onChange={handleChange}\n                value={get(state, name, {})}\n              />\n            );\n          })}\n        </ul>\n      </Modal.Body>\n      <Modal.Footer>\n        <Button variant=\"tertiary\" onClick={() => onCloseModal()}>\n          {formatMessage({ id: 'app.components.Button.cancel', defaultMessage: 'Cancel' })}\n        </Button>\n        <Button onClick={handleSubmit}>\n          {formatMessage({\n            id: 'Settings.permissions.conditions.apply',\n            defaultMessage: 'Apply',\n          })}\n        </Button>\n      </Modal.Footer>\n    </Modal.Content>\n  );\n};\n\nconst createDefaultConditionsForm = (\n  actionsToDisplay: Array<ConditionAction | VisibleCheckboxAction>,\n  modifiedData: PermissionsDataManagerContextValue['modifiedData'],\n  arrayOfOptionsGroupedByCategory: ActionRowProps['arrayOfOptionsGroupedByCategory']\n) => {\n  return actionsToDisplay.reduce<Record<string, Record<string, ConditionForm>>>((acc, current) => {\n    const valueFromModifiedData: ConditionForm = get(\n      modifiedData,\n      [...current.pathToConditionsObject, 'conditions'],\n      {}\n    );\n\n    const categoryDefaultForm = arrayOfOptionsGroupedByCategory.reduce<\n      Record<string, ConditionForm>\n    >((acc, current) => {\n      const [categoryName, relatedConditions] = current;\n\n      const conditionsForm = relatedConditions.reduce<ConditionForm>((acc, current) => {\n        acc[current.id] = get(valueFromModifiedData, current.id, false);\n\n        return acc;\n      }, {});\n\n      acc[categoryName] = conditionsForm;\n\n      return acc;\n    }, {});\n\n    acc[current.pathToConditionsObject.join('..')] = categoryDefaultForm;\n\n    return acc;\n  }, {});\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ActionRow\n * -----------------------------------------------------------------------------------------------*/\n\ninterface ActionRowProps {\n  arrayOfOptionsGroupedByCategory: Array<\n    [string, PermissionsDataManagerContextValue['availableConditions']]\n  >;\n  isFormDisabled?: boolean;\n  isGrey?: boolean;\n  label: string;\n  name: string;\n  onChange?: (name: string, values: Record<string, boolean>) => void;\n  value: Record<string, ConditionForm>;\n}\n\nconst ActionRow = ({\n  arrayOfOptionsGroupedByCategory,\n  isFormDisabled = false,\n  isGrey = false,\n  label,\n  name,\n  onChange,\n  value,\n}: ActionRowProps) => {\n  const { formatMessage } = useIntl();\n\n  const handleChange: MultiSelectNestedProps['onChange'] = (val) => {\n    if (onChange) {\n      onChange(name, getNewStateFromChangedValues(arrayOfOptionsGroupedByCategory, val));\n    }\n  };\n\n  return (\n    <Flex\n      tag=\"li\"\n      background={isGrey ? 'neutral100' : 'neutral0'}\n      paddingBottom={3}\n      paddingTop={3}\n      justifyContent={'space-evenly'}\n    >\n      <Flex style={{ width: 180 }}>\n        <Typography variant=\"sigma\" textColor=\"neutral600\">\n          {formatMessage({\n            id: 'Settings.permissions.conditions.can',\n            defaultMessage: 'Can',\n          })}\n          &nbsp;\n        </Typography>\n        <Typography variant=\"sigma\" title={label} textColor=\"primary600\" ellipsis>\n          {formatMessage({\n            id: `Settings.roles.form.permissions.${label.toLowerCase()}`,\n            defaultMessage: label,\n          })}\n        </Typography>\n        <Typography variant=\"sigma\" textColor=\"neutral600\">\n          &nbsp;\n          {formatMessage({\n            id: 'Settings.permissions.conditions.when',\n            defaultMessage: 'When',\n          })}\n        </Typography>\n      </Flex>\n      <Box style={{ maxWidth: 430, width: '100%' }}>\n        <MultiSelectNested\n          id={name}\n          customizeContent={(values = []) => `${values.length} currently selected`}\n          onChange={handleChange}\n          value={getSelectedValues(value)}\n          options={getNestedOptions(arrayOfOptionsGroupedByCategory)}\n          disabled={isFormDisabled}\n        />\n      </Box>\n    </Flex>\n  );\n};\n\nconst getSelectedValues = (rawValue: Record<string, ConditionForm>): string[] =>\n  Object.values(rawValue)\n    .map((x) =>\n      Object.entries(x)\n        .filter(([, value]) => value)\n        .map(([key]) => key)\n    )\n    .flat();\n\nconst getNestedOptions = (options: ActionRowProps['arrayOfOptionsGroupedByCategory']) =>\n  options.reduce<MultiSelectNestedProps['options']>((acc, [label, children]) => {\n    acc.push({\n      label: capitalise(label),\n      children: children.map((child) => ({\n        label: child.displayName,\n        value: child.id,\n      })),\n    });\n\n    return acc;\n  }, []);\n\nconst getNewStateFromChangedValues = (\n  options: ActionRowProps['arrayOfOptionsGroupedByCategory'],\n  changedValues: string[]\n) =>\n  options\n    .map(([, values]) => values)\n    .flat()\n    .reduce<Record<string, boolean>>(\n      (acc, curr) => ({ [curr.id]: changedValues.includes(curr.id), ...acc }),\n      {}\n    );\n\nexport { ConditionsModal };\nexport type { ConditionsModalProps };\n", "import * as React from 'react';\n\nimport { Checkbox, Box, BoxComponent, Flex, FlexComponent, Modal } from '@strapi/design-system';\nimport { ChevronDown, ChevronUp } from '@strapi/icons';\nimport get from 'lodash/get';\nimport isEmpty from 'lodash/isEmpty';\nimport omit from 'lodash/omit';\nimport { useIntl } from 'react-intl';\nimport { styled, DefaultTheme } from 'styled-components';\n\nimport { Action, Subject } from '../../../../../../../shared/contracts/permissions';\nimport { capitalise } from '../../../../../utils/strings';\nimport {\n  PermissionsDataManagerContextValue,\n  usePermissionsDataManager,\n} from '../hooks/usePermissionsDataManager';\nimport { cellWidth, rowHeight } from '../utils/constants';\nimport { createArrayOfValues } from '../utils/createArrayOfValues';\nimport { ConditionForm } from '../utils/forms';\nimport { getCheckboxState } from '../utils/getCheckboxState';\n\nimport { CollapsePropertyMatrix } from './CollapsePropertyMatrix';\nimport { ConditionsButton } from './ConditionsButton';\nimport { ConditionsModal } from './ConditionsModal';\nimport { HiddenAction } from './HiddenAction';\nimport { RowLabelWithCheckbox, RowLabelWithCheckboxProps } from './RowLabelWithCheckbox';\n\n/* -------------------------------------------------------------------------------------------------\n * ContentTypeCollapses\n * -----------------------------------------------------------------------------------------------*/\n\ninterface ContentTypeCollapsesProps extends Pick<CollapseProps, 'pathToData'> {\n  actions?: Action[];\n  isFormDisabled?: boolean;\n  subjects?: Subject[];\n}\n\nconst ContentTypeCollapses = ({\n  actions = [],\n  isFormDisabled,\n  pathToData,\n  subjects = [],\n}: ContentTypeCollapsesProps) => {\n  const [collapseToOpen, setCollapseToOpen] = React.useState<string | null>(null);\n\n  const handleClickToggleCollapse = (collapseName: string) => () => {\n    const nextCollapseToOpen = collapseToOpen === collapseName ? null : collapseName;\n\n    setCollapseToOpen(nextCollapseToOpen);\n  };\n\n  return (\n    <>\n      {subjects.map(({ uid, label, properties }, index) => {\n        const isActive = collapseToOpen === uid;\n        const availableActions = actions.map((action) => ({\n          ...action,\n          isDisplayed: Array.isArray(action.subjects) && action.subjects.indexOf(uid) !== -1,\n        }));\n        return (\n          <Flex\n            key={uid}\n            direction=\"column\"\n            display=\"inline-flex\"\n            alignItems=\"stretch\"\n            minWidth=\"100%\"\n            borderColor={isActive ? 'primary600' : undefined}\n          >\n            <Collapse\n              availableActions={availableActions}\n              isActive={isActive}\n              isGrey={index % 2 === 0}\n              isFormDisabled={isFormDisabled}\n              label={label}\n              onClickToggle={handleClickToggleCollapse(uid)}\n              pathToData={[pathToData, uid].join('..')}\n            />\n            {isActive &&\n              properties.map(({ label: propertyLabel, value, children: childrenForm }) => {\n                return (\n                  <CollapsePropertyMatrix\n                    availableActions={availableActions}\n                    childrenForm={childrenForm}\n                    isFormDisabled={isFormDisabled}\n                    label={propertyLabel}\n                    pathToData={[pathToData, uid].join('..')}\n                    propertyName={value}\n                    key={value}\n                  />\n                );\n              })}\n          </Flex>\n        );\n      })}\n    </>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Collapse\n * -----------------------------------------------------------------------------------------------*/\n\ninterface CollapseProps\n  extends Pick<RowLabelWithCheckboxProps, 'isActive' | 'isFormDisabled' | 'label'> {\n  availableActions?: Array<Action & { isDisplayed: boolean }>;\n  isGrey?: boolean;\n  onClickToggle: RowLabelWithCheckboxProps['onClick'];\n  pathToData: string;\n}\n\nconst Collapse = ({\n  availableActions = [],\n  isActive = false,\n  isGrey = false,\n  isFormDisabled = false,\n  label,\n  onClickToggle,\n  pathToData,\n}: CollapseProps) => {\n  const { formatMessage } = useIntl();\n  const { modifiedData, onChangeParentCheckbox, onChangeSimpleCheckbox } =\n    usePermissionsDataManager();\n  const [isConditionModalOpen, setIsConditionModalOpen] = React.useState(false);\n\n  // This corresponds to the data related to the CT left checkbox\n  // modifiedData: { collectionTypes: { [ctuid]: {create: {properties: { fields: {f1: true} }, update: {}, ... } } } }\n  const mainData = get(modifiedData, pathToData.split('..'), {});\n  // The utils we are using: getCheckboxState, retrieves all the boolean leafs of an object in order\n  // to return the state of checkbox. Since the conditions are not related to the property we need to remove the key from the object.\n  const dataWithoutCondition = React.useMemo(() => {\n    return Object.keys(mainData).reduce<Record<string, ConditionForm>>((acc, current) => {\n      acc[current] = omit(mainData[current], 'conditions');\n\n      return acc;\n    }, {});\n  }, [mainData]);\n\n  const { hasAllActionsSelected, hasSomeActionsSelected } = getCheckboxState(dataWithoutCondition);\n\n  // Here we create an array of <checkbox>, since the state of each one of them is used in\n  // order to know if whether or not we need to display the associated action in\n  // the <ConditionsModal />\n  const checkboxesActions = React.useMemo(() => {\n    return generateCheckboxesActions(availableActions, modifiedData, pathToData);\n  }, [availableActions, modifiedData, pathToData]);\n\n  // @ts-expect-error – hasConditions does not exist on all versions of checkboxesActions.\n  const doesConditionButtonHasConditions = checkboxesActions.some((action) => action.hasConditions);\n\n  return (\n    <BoxWrapper $isActive={isActive}>\n      <Wrapper\n        height={rowHeight}\n        flex={1}\n        alignItems=\"center\"\n        background={isGrey ? 'neutral100' : 'neutral0'}\n      >\n        <RowLabelWithCheckbox\n          isCollapsable\n          isFormDisabled={isFormDisabled}\n          label={capitalise(label)}\n          checkboxName={pathToData}\n          onChange={onChangeParentCheckbox}\n          onClick={onClickToggle}\n          someChecked={hasSomeActionsSelected}\n          value={hasAllActionsSelected}\n          isActive={isActive}\n        >\n          <Chevron paddingLeft={2}>{isActive ? <ChevronUp /> : <ChevronDown />}</Chevron>\n        </RowLabelWithCheckbox>\n\n        <Flex style={{ flex: 1 }}>\n          {checkboxesActions.map(\n            ({ actionId, hasSomeActionsSelected, isDisplayed, ...restAction }) => {\n              if (!isDisplayed) {\n                return <HiddenAction key={actionId} />;\n              }\n\n              const {\n                hasConditions,\n                hasAllActionsSelected,\n                isParentCheckbox,\n                checkboxName,\n                label: permissionLabel,\n              } = restAction as VisibleCheckboxAction;\n\n              if (isParentCheckbox) {\n                return (\n                  <Cell key={actionId} justifyContent=\"center\" alignItems=\"center\">\n                    {hasConditions && (\n                      <Box\n                        tag=\"span\"\n                        position=\"absolute\"\n                        top=\"-6px\"\n                        left=\"37px\"\n                        width=\"6px\"\n                        height=\"6px\"\n                        borderRadius=\"20px\"\n                        background=\"primary600\"\n                      />\n                    )}\n                    <Checkbox\n                      disabled={isFormDisabled}\n                      name={checkboxName}\n                      aria-label={formatMessage(\n                        {\n                          id: `Settings.permissions.select-by-permission`,\n                          defaultMessage: 'Select {label} permission',\n                        },\n                        { label: `${permissionLabel} ${label}` }\n                      )}\n                      // Keep same signature as packages/core/admin/admin/src/components/Roles/Permissions/index.js l.91\n                      onCheckedChange={(value) => {\n                        onChangeParentCheckbox({\n                          target: {\n                            name: checkboxName,\n                            value: !!value,\n                          },\n                        });\n                      }}\n                      checked={hasSomeActionsSelected ? 'indeterminate' : hasAllActionsSelected}\n                    />\n                  </Cell>\n                );\n              }\n\n              return (\n                <Cell key={actionId} justifyContent=\"center\" alignItems=\"center\">\n                  {hasConditions && (\n                    <Box\n                      tag=\"span\"\n                      position=\"absolute\"\n                      top=\"-6px\"\n                      left=\"37px\"\n                      width=\"6px\"\n                      height=\"6px\"\n                      borderRadius=\"20px\"\n                      background=\"primary600\"\n                    />\n                  )}\n                  <Checkbox\n                    disabled={isFormDisabled}\n                    name={checkboxName}\n                    // Keep same signature as packages/core/admin/admin/src/components/Roles/Permissions/index.js l.91\n                    onCheckedChange={(value) => {\n                      onChangeSimpleCheckbox({\n                        target: {\n                          name: checkboxName,\n                          value: !!value,\n                        },\n                      });\n                    }}\n                    checked={hasConditions ? 'indeterminate' : hasAllActionsSelected}\n                  />\n                </Cell>\n              );\n            }\n          )}\n        </Flex>\n      </Wrapper>\n      <Box bottom=\"10px\" right=\"9px\" position=\"absolute\">\n        <Modal.Root\n          open={isConditionModalOpen}\n          onOpenChange={() => {\n            setIsConditionModalOpen((prev) => !prev);\n          }}\n        >\n          <Modal.Trigger>\n            <ConditionsButton hasConditions={doesConditionButtonHasConditions} />\n          </Modal.Trigger>\n          <ConditionsModal\n            headerBreadCrumbs={[label, 'Settings.permissions.conditions.conditions']}\n            actions={checkboxesActions}\n            isFormDisabled={isFormDisabled}\n            onClose={() => {\n              setIsConditionModalOpen(false);\n            }}\n          />\n        </Modal.Root>\n      </Box>\n    </BoxWrapper>\n  );\n};\n\ninterface VisibleCheckboxAction {\n  actionId: string;\n  hasAllActionsSelected: boolean;\n  hasSomeActionsSelected: boolean;\n  isDisplayed: true;\n  isParentCheckbox: boolean;\n  checkboxName: string;\n  label: string;\n  hasConditions: boolean;\n  pathToConditionsObject: string[];\n}\n\ninterface HiddenCheckboxAction {\n  actionId: string;\n  isDisplayed: false;\n  hasAllActionsSelected?: never;\n  hasSomeActionsSelected: boolean;\n}\n\nconst generateCheckboxesActions = (\n  availableActions: Array<Action & { isDisplayed: boolean }>,\n  modifiedData: PermissionsDataManagerContextValue['modifiedData'],\n  pathToData: string\n): Array<VisibleCheckboxAction | HiddenCheckboxAction> => {\n  return availableActions.map(({ actionId, isDisplayed, applyToProperties, label }) => {\n    if (!isDisplayed) {\n      return { actionId, hasSomeActionsSelected: false, isDisplayed };\n    }\n\n    const baseCheckboxNameArray = [...pathToData.split('..'), actionId];\n    const checkboxNameArray = isEmpty(applyToProperties)\n      ? [...baseCheckboxNameArray, 'properties', 'enabled']\n      : baseCheckboxNameArray;\n    const conditionsValue = get(modifiedData, [...baseCheckboxNameArray, 'conditions'], null);\n\n    const baseCheckboxAction = {\n      actionId,\n      checkboxName: checkboxNameArray.join('..'),\n      hasConditions: createArrayOfValues(conditionsValue).some((val) => val),\n      isDisplayed,\n      label,\n      pathToConditionsObject: baseCheckboxNameArray,\n    };\n\n    if (isEmpty(applyToProperties)) {\n      const value = get(modifiedData, checkboxNameArray, false);\n\n      // Since applyToProperties is empty it is not a parent checkbox, therefore hasAllActionsSelected is\n      // equal to hasSomeActionsSelected\n      return {\n        ...baseCheckboxAction,\n        hasAllActionsSelected: value,\n        hasSomeActionsSelected: value,\n        isParentCheckbox: false,\n      };\n    }\n\n    const mainData = get(modifiedData, checkboxNameArray, null);\n\n    const { hasAllActionsSelected, hasSomeActionsSelected } = getCheckboxState(mainData);\n\n    return {\n      ...baseCheckboxAction,\n      hasAllActionsSelected,\n      hasSomeActionsSelected,\n      isParentCheckbox: true,\n    };\n  });\n};\n\nconst activeRowStyle = (theme: DefaultTheme, isActive?: boolean): string => `\n  ${Wrapper} {\n    background-color: ${theme.colors.primary100};\n    color: ${theme.colors.primary600};\n    border-radius: ${isActive ? '2px 2px 0 0' : '2px'};\n    font-weight: ${theme.fontWeights.bold};\n  }\n\n  ${Chevron} {\n    display: flex;\n  }\n  ${ConditionsButton} {\n    display: block;\n  }\n\n  &:focus-within {\n    ${() => activeRowStyle(theme, isActive)}\n  }\n`;\n\nconst Wrapper = styled<FlexComponent>(Flex)`\n  border: 1px solid transparent;\n`;\n\nconst BoxWrapper = styled.div<{ $isActive: boolean }>`\n  display: inline-flex;\n  min-width: 100%;\n  position: relative;\n\n  ${ConditionsButton} {\n    display: none;\n  }\n\n  ${({ $isActive, theme }) => $isActive && activeRowStyle(theme, $isActive)}\n\n  &:hover {\n    ${({ theme, $isActive }) => activeRowStyle(theme, $isActive)}\n  }\n`;\n\nconst Cell = styled<FlexComponent>(Flex)`\n  width: ${cellWidth};\n  position: relative;\n`;\n\nconst Chevron = styled<BoxComponent>(Box)`\n  display: none;\n\n  svg {\n    width: 1.4rem;\n  }\n\n  path {\n    fill: ${({ theme }) => theme.colors.primary600};\n  }\n`;\n\nexport { ContentTypeCollapses };\nexport type { ContentTypeCollapsesProps, HiddenCheckboxAction, VisibleCheckboxAction };\n", "import * as React from 'react';\n\nimport { Checkbox, Box, Flex, Typography } from '@strapi/design-system';\nimport get from 'lodash/get';\nimport { useIntl } from 'react-intl';\n\nimport { Action } from '../../../../../../../shared/contracts/permissions';\nimport {\n  PermissionsDataManagerContextValue,\n  usePermissionsDataManager,\n} from '../hooks/usePermissionsDataManager';\nimport { cellWidth, firstRowWidth } from '../utils/constants';\nimport { RecursiveRecordOfBooleans, getCheckboxState } from '../utils/getCheckboxState';\nimport { removeConditionKeyFromData } from '../utils/removeConditionKeyFromData';\n\ninterface GlobalActionsProps {\n  actions: Action[];\n  isFormDisabled?: boolean;\n  kind: Extract<keyof PermissionsDataManagerContextValue['modifiedData'], `${string}Types`>;\n}\n\nconst GlobalActions = ({ actions = [], isFormDisabled, kind }: GlobalActionsProps) => {\n  const { formatMessage } = useIntl();\n  const { modifiedData, onChangeCollectionTypeGlobalActionCheckbox } = usePermissionsDataManager();\n\n  const displayedActions = actions.filter(({ subjects }) => subjects && subjects.length);\n\n  const checkboxesState = React.useMemo(() => {\n    const actionsIds = displayedActions.map(({ actionId }) => actionId);\n\n    const data = modifiedData[kind];\n\n    const relatedActionsData = actionsIds.reduce<Record<string, RecursiveRecordOfBooleans>>(\n      (acc, actionId) => {\n        Object.keys(data).forEach((ctUid) => {\n          const actionIdData = get(data, [ctUid, actionId]);\n\n          const actionIdState = { [ctUid]: removeConditionKeyFromData(actionIdData)! };\n\n          if (!acc[actionId]) {\n            acc[actionId] = actionIdState;\n          } else {\n            acc[actionId] = { ...acc[actionId], ...actionIdState };\n          }\n        });\n\n        return acc;\n      },\n      {}\n    );\n\n    const checkboxesState = Object.keys(relatedActionsData).reduce<\n      Record<\n        string,\n        {\n          hasAllActionsSelected: boolean;\n          hasSomeActionsSelected: boolean;\n        }\n      >\n    >((acc, current) => {\n      acc[current] = getCheckboxState(relatedActionsData[current]);\n\n      return acc;\n    }, {});\n\n    return checkboxesState;\n  }, [modifiedData, displayedActions, kind]);\n\n  return (\n    <Box paddingBottom={4} paddingTop={6} style={{ paddingLeft: firstRowWidth }}>\n      <Flex gap={0}>\n        {displayedActions.map(({ label, actionId }) => {\n          return (\n            <Flex\n              shrink={0}\n              width={cellWidth}\n              direction=\"column\"\n              alignItems=\"center\"\n              justifyContent=\"center\"\n              key={actionId}\n              gap={3}\n            >\n              <Typography variant=\"sigma\" textColor=\"neutral500\">\n                {formatMessage({\n                  id: `Settings.roles.form.permissions.${label.toLowerCase()}`,\n                  defaultMessage: label,\n                })}\n              </Typography>\n              <Checkbox\n                disabled={isFormDisabled}\n                onCheckedChange={(value) => {\n                  onChangeCollectionTypeGlobalActionCheckbox(kind, actionId, !!value);\n                }}\n                name={actionId}\n                aria-label={formatMessage(\n                  {\n                    id: `Settings.permissions.select-all-by-permission`,\n                    defaultMessage: 'Select all {label} permissions',\n                  },\n                  {\n                    label: formatMessage({\n                      id: `Settings.roles.form.permissions.${label.toLowerCase()}`,\n                      defaultMessage: label,\n                    }),\n                  }\n                )}\n                checked={\n                  get(checkboxesState, [actionId, 'hasSomeActionsSelected'], false)\n                    ? 'indeterminate'\n                    : get(checkboxesState, [actionId, 'hasAllActionsSelected'], false)\n                }\n              />\n            </Flex>\n          );\n        })}\n      </Flex>\n    </Box>\n  );\n};\n\nexport { GlobalActions };\nexport type { GlobalActionsProps };\n", "import { Box } from '@strapi/design-system';\n\nimport { ContentPermission } from '../../../../../../../shared/contracts/permissions';\n\nimport { ContentTypeCollapses } from './ContentTypeCollapses';\nimport { GlobalActions, GlobalActionsProps } from './GlobalActions';\n\ninterface ContentTypesProps extends Pick<GlobalActionsProps, 'kind'> {\n  isFormDisabled?: boolean;\n  layout: ContentPermission;\n}\n\nconst ContentTypes = ({\n  isFormDisabled,\n  kind,\n  layout: { actions, subjects },\n}: ContentTypesProps) => {\n  const sortedSubjects = [...subjects].sort((a, b) => a.label.localeCompare(b.label));\n\n  return (\n    <Box background=\"neutral0\">\n      <GlobalActions actions={actions} kind={kind} isFormDisabled={isFormDisabled} />\n      <ContentTypeCollapses\n        actions={actions}\n        isFormDisabled={isFormDisabled}\n        pathToData={kind}\n        subjects={sortedSubjects}\n      />\n    </Box>\n  );\n};\n\nexport { ContentTypes };\n", "import * as React from 'react';\n\nimport {\n  Accordion,\n  Box,\n  BoxComponent,\n  Checkbox,\n  Flex,\n  Grid,\n  Modal,\n  Typography,\n} from '@strapi/design-system';\nimport get from 'lodash/get';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport {\n  SettingPermission,\n  PluginPermission,\n} from '../../../../../../../shared/contracts/permissions';\nimport { capitalise } from '../../../../../utils/strings';\nimport {\n  PermissionsDataManagerContextValue,\n  usePermissionsDataManager,\n} from '../hooks/usePermissionsDataManager';\nimport { createArrayOfValues } from '../utils/createArrayOfValues';\nimport { ChildrenForm, ConditionForm } from '../utils/forms';\nimport { RecursiveRecordOfBooleans, getCheckboxState } from '../utils/getCheckboxState';\nimport { removeConditionKeyFromData } from '../utils/removeConditionKeyFromData';\n\nimport { ConditionsButton } from './ConditionsButton';\nimport { ConditionsModal } from './ConditionsModal';\n\nimport type { GenericLayout } from '../utils/layouts';\n\n/* -------------------------------------------------------------------------------------------------\n * PluginsAndSettingsPermissions\n * -----------------------------------------------------------------------------------------------*/\n\ntype Layout = GenericLayout<SettingPermission | PluginPermission>[];\n\ninterface PluginsAndSettingsPermissionsProps extends Pick<RowProps, 'kind' | 'isFormDisabled'> {\n  layout: Layout;\n}\n\nconst PluginsAndSettingsPermissions = ({\n  layout,\n  ...restProps\n}: PluginsAndSettingsPermissionsProps) => {\n  return (\n    <Box padding={6} background=\"neutral0\">\n      <Accordion.Root size=\"M\">\n        {layout.map(({ category, categoryId, childrenForm }, index) => {\n          return (\n            <Row\n              key={category}\n              childrenForm={childrenForm}\n              variant={index % 2 === 1 ? 'primary' : 'secondary'}\n              name={category}\n              pathToData={[restProps.kind, categoryId]}\n              {...restProps}\n            />\n          );\n        })}\n      </Accordion.Root>\n    </Box>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Row\n * -----------------------------------------------------------------------------------------------*/\n\ninterface RowProps\n  extends Pick<Layout[number], 'childrenForm'>,\n    Pick<Accordion.HeaderProps, 'variant'> {\n  kind: Exclude<keyof PermissionsDataManagerContextValue['modifiedData'], `${string}Types`>;\n  name: string;\n  isFormDisabled?: boolean;\n  pathToData: string[];\n}\n\nconst Row = ({\n  childrenForm,\n  kind,\n  name,\n  isFormDisabled = false,\n  variant,\n  pathToData,\n}: RowProps) => {\n  const { formatMessage } = useIntl();\n\n  const categoryName = name.split('::').pop() ?? '';\n  const categoryDisplayName =\n    categoryName === 'upload' ? 'Media Library' : capitalise(categoryName.replace(/-/g, ' '));\n\n  return (\n    <Accordion.Item value={name}>\n      <Accordion.Header variant={variant}>\n        <Accordion.Trigger\n          caretPosition=\"right\"\n          description={`${formatMessage(\n            { id: 'Settings.permissions.category', defaultMessage: categoryName },\n            { category: categoryName }\n          )} ${kind === 'plugins' ? 'plugin' : kind}`}\n        >\n          {categoryDisplayName}\n        </Accordion.Trigger>\n      </Accordion.Header>\n      <Accordion.Content>\n        <Box padding={6}>\n          {childrenForm.map(({ actions, subCategoryName, subCategoryId }) => (\n            <SubCategory\n              key={subCategoryName}\n              actions={actions}\n              categoryName={categoryName}\n              isFormDisabled={isFormDisabled}\n              subCategoryName={subCategoryName}\n              pathToData={[...pathToData, subCategoryId]}\n            />\n          ))}\n        </Box>\n      </Accordion.Content>\n    </Accordion.Item>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * SubCategory\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SubCategoryProps {\n  actions?: Array<SettingPermission | PluginPermission>;\n  categoryName: string;\n  isFormDisabled?: boolean;\n  subCategoryName: string;\n  pathToData: string[];\n}\n\nconst SubCategory = ({\n  actions = [],\n  categoryName,\n  isFormDisabled,\n  subCategoryName,\n  pathToData,\n}: SubCategoryProps) => {\n  const { modifiedData, onChangeParentCheckbox, onChangeSimpleCheckbox } =\n    usePermissionsDataManager();\n  const [isConditionModalOpen, setIsConditionModalOpen] = React.useState(false);\n  const { formatMessage } = useIntl();\n\n  const mainData = get(modifiedData, pathToData, {});\n\n  const dataWithoutCondition = React.useMemo(() => {\n    return Object.keys(mainData).reduce<RecursiveRecordOfBooleans>((acc, current) => {\n      acc[current] = removeConditionKeyFromData(mainData[current])!;\n\n      return acc;\n    }, {});\n  }, [mainData]);\n\n  const { hasAllActionsSelected, hasSomeActionsSelected } = getCheckboxState(dataWithoutCondition);\n\n  // We need to format the actions so it matches the shape of the ConditionsModal actions props\n  const formattedActions = React.useMemo(() => {\n    return actions.map((action) => {\n      const checkboxName = [...pathToData, action.action, 'properties', 'enabled'];\n      const checkboxValue = get(modifiedData, checkboxName, false);\n      const conditionValue = get(modifiedData, [...pathToData, action.action, 'conditions'], {});\n      const hasConditions = createArrayOfValues(conditionValue).some((val) => val);\n\n      return {\n        ...action,\n        isDisplayed: checkboxValue,\n        checkboxName: checkboxName.join('..'),\n        hasSomeActionsSelected: checkboxValue,\n        value: checkboxValue,\n        hasConditions,\n        label: action.displayName,\n        actionId: action.action,\n        pathToConditionsObject: [...pathToData, action.action],\n      };\n    });\n  }, [actions, modifiedData, pathToData]);\n\n  const datum: ChildrenForm = get(modifiedData, [...pathToData], {});\n\n  const doesButtonHasCondition = createArrayOfValues(\n    Object.entries(datum).reduce<Record<string, ConditionForm>>((acc, current) => {\n      const [catName, { conditions }] = current;\n\n      acc[catName] = conditions;\n\n      return acc;\n    }, {})\n  ).some((val) => val);\n\n  return (\n    <>\n      <Box>\n        <Flex justifyContent=\"space-between\" alignItems=\"center\">\n          <Box paddingRight={4}>\n            <Typography variant=\"sigma\" textColor=\"neutral600\">\n              {subCategoryName}\n            </Typography>\n          </Box>\n          <Border flex={1} />\n          <Box paddingLeft={4}>\n            <Checkbox\n              name={pathToData.join('..')}\n              disabled={isFormDisabled}\n              // Keep same signature as packages/core/admin/admin/src/components/Roles/Permissions/index.js l.91\n              onCheckedChange={(value) => {\n                onChangeParentCheckbox({\n                  target: {\n                    name: pathToData.join('..'),\n                    value: !!value,\n                  },\n                });\n              }}\n              checked={hasSomeActionsSelected ? 'indeterminate' : hasAllActionsSelected}\n            >\n              {formatMessage({ id: 'app.utils.select-all', defaultMessage: 'Select all' })}\n            </Checkbox>\n          </Box>\n        </Flex>\n        <Flex paddingTop={6} paddingBottom={6}>\n          <Grid.Root gap={2} style={{ flex: 1 }}>\n            {formattedActions.map(({ checkboxName, value, action, displayName, hasConditions }) => {\n              return (\n                <Grid.Item col={3} key={action} direction=\"column\" alignItems=\"start\">\n                  <CheckboxWrapper $disabled={isFormDisabled} $hasConditions={hasConditions}>\n                    <Checkbox\n                      name={checkboxName}\n                      disabled={isFormDisabled}\n                      // Keep same signature as packages/core/admin/admin/src/components/Roles/Permissions/index.js l.91\n                      onCheckedChange={(value) => {\n                        onChangeSimpleCheckbox({\n                          target: {\n                            name: checkboxName,\n                            value: !!value,\n                          },\n                        });\n                      }}\n                      checked={value}\n                    >\n                      {displayName}\n                    </Checkbox>\n                  </CheckboxWrapper>\n                </Grid.Item>\n              );\n            })}\n          </Grid.Root>\n          <Modal.Root\n            open={isConditionModalOpen}\n            onOpenChange={() => {\n              setIsConditionModalOpen((prev) => !prev);\n            }}\n          >\n            <Modal.Trigger>\n              <ConditionsButton hasConditions={doesButtonHasCondition} />\n            </Modal.Trigger>\n            <ConditionsModal\n              headerBreadCrumbs={[categoryName, subCategoryName]}\n              actions={formattedActions}\n              isFormDisabled={isFormDisabled}\n              onClose={() => {\n                setIsConditionModalOpen(false);\n              }}\n            />\n          </Modal.Root>\n        </Flex>\n      </Box>\n    </>\n  );\n};\n\nconst Border = styled<BoxComponent>(Box)`\n  align-self: center;\n  border-top: 1px solid ${({ theme }) => theme.colors.neutral150};\n`;\n\nconst CheckboxWrapper = styled.div<{ $hasConditions?: boolean; $disabled?: boolean }>`\n  position: relative;\n  word-break: keep-all;\n  ${({ $hasConditions, $disabled, theme }) =>\n    $hasConditions &&\n    `\n    &:before {\n      content: '';\n      position: absolute;\n      top: -0.4rem;\n      left: -0.8rem;\n      width: 0.6rem;\n      height: 0.6rem;\n      border-radius: 2rem;\n      background: ${$disabled ? theme.colors.neutral100 : theme.colors.primary600};\n    }\n  `}\n`;\n\nexport { PluginsAndSettingsPermissions };\n", "import * as React from 'react';\n\nimport { Tabs } from '@strapi/design-system';\nimport { produce } from 'immer';\nimport cloneDeep from 'lodash/cloneDeep';\nimport get from 'lodash/get';\nimport has from 'lodash/has';\nimport isEmpty from 'lodash/isEmpty';\nimport set from 'lodash/set';\nimport { useIntl } from 'react-intl';\n\nimport * as PermissonContracts from '../../../../../../../shared/contracts/permissions';\nimport { Permission } from '../../../../../../../shared/contracts/shared';\nimport { isObject } from '../../../../../utils/objects';\nimport {\n  PermissionsDataManagerContextValue,\n  PermissionsDataManagerProvider,\n} from '../hooks/usePermissionsDataManager';\nimport { difference } from '../utils/difference';\nimport { ConditionForm, Form, createDefaultCTForm, createDefaultForm } from '../utils/forms';\nimport { GenericLayout, formatLayout } from '../utils/layouts';\nimport { formatPermissionsForAPI } from '../utils/permissions';\nimport { updateConditionsToFalse } from '../utils/updateConditionsToFalse';\nimport { updateValues } from '../utils/updateValues';\n\nimport { ContentTypes } from './ContentTypes';\nimport { PluginsAndSettingsPermissions } from './PluginsAndSettings';\n\nconst TAB_LABELS = [\n  {\n    labelId: 'app.components.LeftMenuLinkContainer.collectionTypes',\n    defaultMessage: 'Collection Types',\n    id: 'collectionTypes',\n  },\n  {\n    labelId: 'app.components.LeftMenuLinkContainer.singleTypes',\n    id: 'singleTypes',\n    defaultMessage: 'Single Types',\n  },\n  {\n    labelId: 'app.components.LeftMenuLinkContainer.plugins',\n    defaultMessage: 'Plugins',\n    id: 'plugins',\n  },\n  {\n    labelId: 'app.components.LeftMenuLinkContainer.settings',\n    defaultMessage: 'Settings',\n    id: 'settings',\n  },\n] as const;\n\n/* -------------------------------------------------------------------------------------------------\n * Permissions\n * -----------------------------------------------------------------------------------------------*/\n\nexport interface PermissionsAPI {\n  getPermissions: () => {\n    didUpdateConditions: boolean;\n    permissionsToSend: Omit<Permission, 'id' | 'createdAt' | 'updatedAt' | 'actionParameters'>[];\n  };\n  resetForm: () => void;\n  setFormAfterSubmit: () => void;\n}\n\ninterface PermissionsProps {\n  isFormDisabled?: boolean;\n  permissions?: Permission[];\n  layout: PermissonContracts.GetAll.Response['data'];\n}\n\nconst Permissions = React.forwardRef<PermissionsAPI, PermissionsProps>(\n  ({ layout, isFormDisabled, permissions = [] }, api) => {\n    const [{ initialData, layouts, modifiedData }, dispatch] = React.useReducer(\n      reducer,\n      initialState,\n      () => init(layout, permissions)\n    );\n    const { formatMessage } = useIntl();\n\n    React.useImperativeHandle(api, () => {\n      return {\n        getPermissions() {\n          const collectionTypesDiff = difference(\n            initialData.collectionTypes,\n            modifiedData.collectionTypes\n          );\n          const singleTypesDiff = difference(initialData.singleTypes, modifiedData.singleTypes);\n\n          const contentTypesDiff = { ...collectionTypesDiff, ...singleTypesDiff };\n\n          let didUpdateConditions;\n\n          if (isEmpty(contentTypesDiff)) {\n            didUpdateConditions = false;\n          } else {\n            didUpdateConditions = Object.values(contentTypesDiff).some((permission = {}) => {\n              return Object.values(permission).some((permissionValue) =>\n                has(permissionValue, 'conditions')\n              );\n            });\n          }\n\n          return { permissionsToSend: formatPermissionsForAPI(modifiedData), didUpdateConditions };\n        },\n        resetForm() {\n          dispatch({ type: 'RESET_FORM' });\n        },\n        setFormAfterSubmit() {\n          dispatch({ type: 'SET_FORM_AFTER_SUBMIT' });\n        },\n      } satisfies PermissionsAPI;\n    });\n\n    const handleChangeCollectionTypeLeftActionRowCheckbox = (\n      pathToCollectionType: OnChangeCollectionTypeRowLeftCheckboxAction['pathToCollectionType'],\n      propertyName: OnChangeCollectionTypeRowLeftCheckboxAction['propertyName'],\n      rowName: OnChangeCollectionTypeRowLeftCheckboxAction['rowName'],\n      value: OnChangeCollectionTypeRowLeftCheckboxAction['value']\n    ) => {\n      dispatch({\n        type: 'ON_CHANGE_COLLECTION_TYPE_ROW_LEFT_CHECKBOX',\n        pathToCollectionType,\n        propertyName,\n        rowName,\n        value,\n      });\n    };\n\n    const handleChangeCollectionTypeGlobalActionCheckbox = (\n      collectionTypeKind: OnChangeCollectionTypeGlobalActionCheckboxAction['collectionTypeKind'],\n      actionId: OnChangeCollectionTypeGlobalActionCheckboxAction['actionId'],\n      value: OnChangeCollectionTypeGlobalActionCheckboxAction['value']\n    ) => {\n      dispatch({\n        type: 'ON_CHANGE_COLLECTION_TYPE_GLOBAL_ACTION_CHECKBOX',\n        collectionTypeKind,\n        actionId,\n        value,\n      });\n    };\n\n    const handleChangeConditions = (conditions: OnChangeConditionsAction['conditions']) => {\n      dispatch({ type: 'ON_CHANGE_CONDITIONS', conditions });\n    };\n\n    const handleChangeSimpleCheckbox: PermissionsDataManagerContextValue['onChangeSimpleCheckbox'] =\n      React.useCallback(({ target: { name, value } }) => {\n        dispatch({\n          type: 'ON_CHANGE_SIMPLE_CHECKBOX',\n          keys: name,\n          value,\n        });\n      }, []);\n\n    const handleChangeParentCheckbox: PermissionsDataManagerContextValue['onChangeParentCheckbox'] =\n      React.useCallback(({ target: { name, value } }) => {\n        dispatch({\n          type: 'ON_CHANGE_TOGGLE_PARENT_CHECKBOX',\n          keys: name,\n          value,\n        });\n      }, []);\n\n    return (\n      <PermissionsDataManagerProvider\n        availableConditions={layout.conditions}\n        modifiedData={modifiedData}\n        onChangeConditions={handleChangeConditions}\n        onChangeSimpleCheckbox={handleChangeSimpleCheckbox}\n        onChangeParentCheckbox={handleChangeParentCheckbox}\n        onChangeCollectionTypeLeftActionRowCheckbox={\n          handleChangeCollectionTypeLeftActionRowCheckbox\n        }\n        onChangeCollectionTypeGlobalActionCheckbox={handleChangeCollectionTypeGlobalActionCheckbox}\n      >\n        <Tabs.Root defaultValue={TAB_LABELS[0].id}>\n          <Tabs.List\n            aria-label={formatMessage({\n              id: 'Settings.permissions.users.tabs.label',\n              defaultMessage: 'Tabs Permissions',\n            })}\n          >\n            {TAB_LABELS.map((tabLabel) => (\n              <Tabs.Trigger key={tabLabel.id} value={tabLabel.id}>\n                {formatMessage({ id: tabLabel.labelId, defaultMessage: tabLabel.defaultMessage })}\n              </Tabs.Trigger>\n            ))}\n          </Tabs.List>\n          <Tabs.Content value={TAB_LABELS[0].id}>\n            <ContentTypes\n              layout={layouts.collectionTypes}\n              kind=\"collectionTypes\"\n              isFormDisabled={isFormDisabled}\n            />\n          </Tabs.Content>\n          <Tabs.Content value={TAB_LABELS[1].id}>\n            <ContentTypes\n              layout={layouts.singleTypes}\n              kind=\"singleTypes\"\n              isFormDisabled={isFormDisabled}\n            />\n          </Tabs.Content>\n          <Tabs.Content value={TAB_LABELS[2].id}>\n            <PluginsAndSettingsPermissions\n              layout={layouts.plugins}\n              kind=\"plugins\"\n              isFormDisabled={isFormDisabled}\n            />\n          </Tabs.Content>\n          <Tabs.Content value={TAB_LABELS[3].id}>\n            <PluginsAndSettingsPermissions\n              layout={layouts.settings}\n              kind=\"settings\"\n              isFormDisabled={isFormDisabled}\n            />\n          </Tabs.Content>\n        </Tabs.Root>\n      </PermissionsDataManagerProvider>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * reducer\n * -----------------------------------------------------------------------------------------------*/\n\ninterface PermissionForms {\n  collectionTypes: Form;\n  plugins: Record<string, Form>;\n  settings: Record<string, Form>;\n  singleTypes: Form;\n}\n\ninterface State {\n  initialData: PermissionForms;\n  modifiedData: PermissionForms;\n  layouts: {\n    collectionTypes: PermissonContracts.ContentPermission;\n    singleTypes: PermissonContracts.ContentPermission;\n    plugins: GenericLayout<PermissonContracts.PluginPermission>[];\n    settings: GenericLayout<PermissonContracts.SettingPermission>[];\n  };\n}\n\nconst initialState = {\n  initialData: {},\n  modifiedData: {},\n  layouts: {},\n};\n\ninterface OnChangeCollectionTypeGlobalActionCheckboxAction {\n  type: 'ON_CHANGE_COLLECTION_TYPE_GLOBAL_ACTION_CHECKBOX';\n  collectionTypeKind: keyof PermissionForms;\n  actionId: string;\n  value: boolean;\n}\n\ninterface OnChangeCollectionTypeRowLeftCheckboxAction {\n  type: 'ON_CHANGE_COLLECTION_TYPE_ROW_LEFT_CHECKBOX';\n  pathToCollectionType: string;\n  propertyName: string;\n  rowName: string;\n  value: boolean;\n}\n\ninterface OnChangeConditionsAction {\n  type: 'ON_CHANGE_CONDITIONS';\n  conditions: Record<string, ConditionForm>;\n}\n\ninterface OnChangeSimpleCheckboxAction {\n  type: 'ON_CHANGE_SIMPLE_CHECKBOX';\n  keys: string;\n  value: boolean;\n}\n\ninterface OnChangeToggleParentCheckbox {\n  type: 'ON_CHANGE_TOGGLE_PARENT_CHECKBOX';\n  keys: string;\n  value: boolean;\n}\n\ninterface ResetFormAction {\n  type: 'RESET_FORM';\n}\n\ninterface SetFormAfterSubmitAction {\n  type: 'SET_FORM_AFTER_SUBMIT';\n}\n\ntype Action =\n  | OnChangeCollectionTypeGlobalActionCheckboxAction\n  | OnChangeCollectionTypeRowLeftCheckboxAction\n  | OnChangeConditionsAction\n  | OnChangeSimpleCheckboxAction\n  | OnChangeToggleParentCheckbox\n  | ResetFormAction\n  | SetFormAfterSubmitAction;\n\n/* eslint-disable consistent-return */\nconst reducer = (state: State, action: Action) =>\n  produce(state, (draftState) => {\n    switch (action.type) {\n      // This action is called when a checkbox in the <GlobalActions />\n      // changes\n      case 'ON_CHANGE_COLLECTION_TYPE_GLOBAL_ACTION_CHECKBOX': {\n        const { collectionTypeKind, actionId, value } = action;\n        const pathToData = ['modifiedData', collectionTypeKind];\n\n        Object.keys(get(state, pathToData)).forEach((collectionType) => {\n          const collectionTypeActionData = get(\n            state,\n            [...pathToData, collectionType, actionId],\n            undefined\n          );\n\n          if (collectionTypeActionData) {\n            let updatedValues = updateValues(collectionTypeActionData, value);\n\n            // We need to remove the applied conditions\n            // @ts-expect-error – TODO: type better\n            if (!value && updatedValues.conditions) {\n              // @ts-expect-error – TODO: type better\n              const updatedConditions = updateValues(updatedValues.conditions, false);\n\n              updatedValues = { ...updatedValues, conditions: updatedConditions };\n            }\n\n            set(draftState, [...pathToData, collectionType, actionId], updatedValues);\n          }\n        });\n\n        break;\n      }\n      case 'ON_CHANGE_COLLECTION_TYPE_ROW_LEFT_CHECKBOX': {\n        const { pathToCollectionType, propertyName, rowName, value } = action;\n        let nextModifiedDataState = cloneDeep(state.modifiedData);\n        const pathToModifiedDataCollectionType = pathToCollectionType.split('..');\n\n        const objToUpdate = get(nextModifiedDataState, pathToModifiedDataCollectionType, {});\n\n        Object.keys(objToUpdate).forEach((actionId) => {\n          // When a ct has multiple properties (ex: locales, field)\n          // We need to make sure that we add any new property to the modifiedData\n          // object.\n          if (has(objToUpdate[actionId], `properties.${propertyName}`)) {\n            const objValue = get(objToUpdate, [actionId, 'properties', propertyName, rowName]);\n            const pathToDataToSet = [\n              ...pathToModifiedDataCollectionType,\n              actionId,\n              'properties',\n              propertyName,\n              rowName,\n            ];\n\n            if (!isObject(objValue)) {\n              set(nextModifiedDataState, pathToDataToSet, value);\n            } else {\n              const updatedValue = updateValues(objValue, value);\n\n              set(nextModifiedDataState, pathToDataToSet, updatedValue);\n            }\n          }\n        });\n\n        // When we uncheck a row, we need to check if we also need to disable the conditions\n        if (!value) {\n          // @ts-expect-error – TODO: type better\n          nextModifiedDataState = updateConditionsToFalse(nextModifiedDataState);\n        }\n\n        set(draftState, 'modifiedData', nextModifiedDataState);\n\n        break;\n      }\n      case 'ON_CHANGE_CONDITIONS': {\n        Object.entries(action.conditions).forEach((array) => {\n          const [stringPathToData, conditionsToUpdate] = array;\n\n          set(\n            draftState,\n            ['modifiedData', ...stringPathToData.split('..'), 'conditions'],\n            conditionsToUpdate\n          );\n        });\n\n        break;\n      }\n      case 'ON_CHANGE_SIMPLE_CHECKBOX': {\n        let nextModifiedDataState = cloneDeep(state.modifiedData);\n\n        set(nextModifiedDataState, [...action.keys.split('..')], action.value);\n\n        // When we uncheck a single checkbox we need to remove the conditions from the parent\n        if (!action.value) {\n          // @ts-expect-error – TODO: type better\n          nextModifiedDataState = updateConditionsToFalse(nextModifiedDataState);\n        }\n\n        set(draftState, 'modifiedData', nextModifiedDataState);\n\n        break;\n      }\n      /*\n       * Here the idea is to retrieve a specific value of the modifiedObject\n       * then update all the boolean values of the retrieved one\n       * and update the drafState.\n       *\n       * For instance in order to enable create action for all the fields and locales\n       * of the restaurant content type we need to :\n       * 1. Retrieve the modifiedData.collectionTypes.restaurant.create object\n       * 2. Toggle all the end boolean values to the desired one\n       * 3. Update the draftState\n       *\n       * Since the case works well in order to update what we called \"parent\" checkbox. We can\n       * reuse the action when we need to toggle change all the values that depends on this one.\n       * A parent checkbox is a checkbox which value is not a boolean but depends on its children ones, therefore,\n       * a parent checkbox does not have a represented value in the draftState, they are just helpers.\n       *\n       * Given the following data:\n       *\n       * const data = {\n       *  restaurant: {\n       *   create: {\n       *     fields: { name: true },\n       *     locales: { en: false }\n       *   }\n       *  }\n       * }\n       *\n       * The value of the create checkbox for the restaurant will be ƒalse since not all its children have\n       * truthy values and in order to set its value to true when need to have all the values of its children set to true.\n       *\n       * Similarly, we can reuse the logic for the components attributes\n       *\n       */\n      case 'ON_CHANGE_TOGGLE_PARENT_CHECKBOX': {\n        const { keys, value } = action;\n        const pathToValue = [...keys.split('..')];\n        let nextModifiedDataState = cloneDeep(state.modifiedData);\n        const oldValues = get(nextModifiedDataState, pathToValue, {});\n\n        const updatedValues = updateValues(oldValues, value);\n        set(nextModifiedDataState, pathToValue, updatedValues);\n\n        // When we uncheck a parent checkbox we need to remove the associated conditions\n        if (!value) {\n          // @ts-expect-error – TODO: type better\n          nextModifiedDataState = updateConditionsToFalse(nextModifiedDataState);\n        }\n\n        set(draftState, ['modifiedData'], nextModifiedDataState);\n\n        break;\n      }\n      case 'RESET_FORM': {\n        draftState.modifiedData = state.initialData;\n        break;\n      }\n      case 'SET_FORM_AFTER_SUBMIT': {\n        draftState.initialData = state.modifiedData;\n        break;\n      }\n      default:\n        return draftState;\n    }\n  });\n\n/* -------------------------------------------------------------------------------------------------\n * init (reducer)\n * -----------------------------------------------------------------------------------------------*/\n\nconst init = (\n  layout: PermissionsProps['layout'],\n  permissions: PermissionsProps['permissions']\n): State => {\n  const {\n    conditions,\n    sections: { collectionTypes, singleTypes, plugins, settings },\n  } = layout;\n\n  const layouts = {\n    collectionTypes,\n    singleTypes,\n    plugins: formatLayout(plugins, 'plugin'),\n    settings: formatLayout(settings, 'category'),\n  };\n\n  const defaultForm = {\n    collectionTypes: createDefaultCTForm(collectionTypes, conditions, permissions),\n    singleTypes: createDefaultCTForm(singleTypes, conditions, permissions),\n    plugins: createDefaultForm(layouts.plugins, conditions, permissions),\n    settings: createDefaultForm(layouts.settings, conditions, permissions),\n  };\n\n  return {\n    initialData: defaultForm,\n    modifiedData: defaultForm,\n    layouts,\n  };\n};\n\nexport { Permissions };\nexport type {\n  State,\n  OnChangeCollectionTypeRowLeftCheckboxAction,\n  OnChangeConditionsAction,\n  OnChangeCollectionTypeGlobalActionCheckboxAction,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,aAAa;AADjB,QAEI,aAAa;AAFjB,QAGI,eAAe;AAHnB,QAII,eAAe;AAJnB,QAKI,UAAU;AALd,QAMI,WAAW;AANf,QAOI,aAAa;AAPjB,QAQIA,YAAW;AARf,QASI,eAAe;AAgCnB,aAASC,WAAU,QAAQ,UAAU,aAAa;AAChD,UAAI,QAAQ,QAAQ,MAAM,GACtB,YAAY,SAAS,SAAS,MAAM,KAAK,aAAa,MAAM;AAEhE,iBAAW,aAAa,UAAU,CAAC;AACnC,UAAI,eAAe,MAAM;AACvB,YAAI,OAAO,UAAU,OAAO;AAC5B,YAAI,WAAW;AACb,wBAAc,QAAQ,IAAI,SAAO,CAAC;AAAA,QACpC,WACSD,UAAS,MAAM,GAAG;AACzB,wBAAc,WAAW,IAAI,IAAI,WAAW,aAAa,MAAM,CAAC,IAAI,CAAC;AAAA,QACvE,OACK;AACH,wBAAc,CAAC;AAAA,QACjB;AAAA,MACF;AACA,OAAC,YAAY,YAAY,YAAY,QAAQ,SAAS,OAAO,OAAOE,SAAQ;AAC1E,eAAO,SAAS,aAAa,OAAO,OAAOA,OAAM;AAAA,MACnD,CAAC;AACD,aAAO;AAAA,IACT;AAEA,WAAO,UAAUD;AAAA;AAAA;;;;;;;;;;;;AChCjB,IAAM,CAACE,gCAAgCC,gCAAiC,IACtEC,0CAAkD,wBAAA;AAEvCC,IAAAA,4BAA4B,MACvCF,iCAAiC,2BAA6B;;;;;;AC5BhE,SAASG,WAA8CC,QAAWC,MAAO;AACvE,WAASC,QAAQF,SAAWC,OAAO;AAEjC,eAAOE,iBAAAA,SAAUH,SAAQ,CAACI,QAAQC,OAAYC,QAAAA;AAC5C,UAAI,KAACC,eAAAA,SAAQF,OAAOJ,MAAKK,GAAAA,CAAI,GAAG;AAC9BF,eAAOE,GAAAA,QACLE,gBAAAA,SAASH,KAAAA,SAAUG,gBAAAA,SAASP,MAAKK,GAAAA,CAAI,IAAIJ,QAAQG,OAAYJ,MAAKK,GAAAA,CAAI,IAASD;MACnF;AACA,aAAOD;IACT,CAAA;EACF;AAEA,SAAOF,QAAQF,QAAQC,IAAAA;AACzB;;;;;;;ACDA,IAAMQ,cAAc,CAClBC,UAAAA;AAEA,MAAIC,MAAMC,QAAQF,KAAQ,GAAA;AACxB,WAAOA,MAAMG,OACX,CAACC,KAAKC,UAAAA;AACJ,UAAIJ,MAAMC,QAAQG,KAAQ,GAAA;AACxBD,YAAIE,KAAI,GAAIP,YAAYM,KAAAA,CAAAA;aACnB;AACLD,YAAIE,KAAKD,KAAAA;MACX;AAEA,aAAOD;IACT,GACA,CAAA,CAAE;SAEC;AACL,WAAO,CAAA;EACT;AACF;;;ACpCA,IAAMG,sBAAsB,CAAQC,QAAAA;AAClC,MAAI,CAACC,SAASD,GAAM,GAAA;AAClB,WAAO,CAAA;EACT;AAEA,SAAOE,YACLC,OAAOC,OAAOJ,GAAKK,EAAAA,IAAI,CAACC,UAAAA;AACtB,QAAIL,SAASK,KAAQ,GAAA;AACnB,aAAOP,oBAAoBO,KAAAA;IAC7B;AAEA,WAAOA;EACT,CAAA,CAAA;AAEJ;;;ACJC,IACKC,yBAAyB,CAC7BC,aACAC,QACAC,YACGF,YAAYG,KAAK,CAACC,SAASA,KAAKH,WAAWA,UAAUG,KAAKF,YAAYA,OAAAA;AAE3E,IAAMG,0BAA0B,CAC9BC,iBAAAA;AAEA,QAAMC,qBAAqBC,0BAA0BF,aAAaG,OAAO;AACzE,QAAMC,sBAAsBF,0BAA0BF,aAAaK,QAAQ;AAC3E,QAAMC,6BAA6BC,8BAA8BP,aAAaQ,eAAe;AAC7F,QAAMC,yBAAyBF,8BAA8BP,aAAaU,WAAW;AAErF,SAAO;IACFT,GAAAA;IACAG,GAAAA;IACAE,GAAAA;IACAG,GAAAA;EACJ;AACH;AAEA,IAAMP,4BAA4B,CAChCS,8BAAAA;AAIA,SAAOC,OAAOC,OAAOF,yBAAAA,EAA2BG,OAA0B,CAACC,SAASC,SAAAA;AAClF,UAAMC,6BAA6BL,OAAOC,OAAOG,IAAAA,EAAMF,OACrD,CAACI,cAAcC,cAAAA;AACb,YAAMzB,cAAckB,OAAOQ,QAAQD,SAAWL,EAAAA,OAC5C,CACEO,cACA,CACEC,YACA,EACEC,YACAC,YAAY,EAAEC,QAAO,EAAE,CACxB,MACF;AAED,YAAI,CAACA,SAAS;AACZ,iBAAOJ;QACT;AAEAA,qBAAaK,KAAK;UAChB/B,QAAQ2B;UACR1B,SAAS;UACT2B,YAAYI,sBAAsBJ,UAAAA;UAClCC,YAAY,CAAA;QACd,CAAA;AAEA,eAAOH;MACT,GACA,CAAA,CAAE;AAGJ,aAAO;QAAIH,GAAAA;QAAiBxB,GAAAA;MAAY;IAC1C,GACA,CAAA,CAAE;AAGJ,WAAO;MAAIqB,GAAAA;MAAYE,GAAAA;IAA2B;EACpD,GAAG,CAAA,CAAE;AACP;AAEA,IAAMV,gCAAgC,CAACqB,4BAAAA;AACrC,QAAMlC,cAAckB,OAAOQ,QAAQQ,uBAAAA,EAAyBd,OAC1D,CAACe,gBAAgBC,YAAAA;AACf,UAAM,CAAClC,SAASmC,qBAAAA,IAAyBD;AAEzC,UAAMpC,eAAckB,OAAOQ,QAAQW,qBAAAA,EAAuBjB,OACxD,CAACkB,KAAKF,aAAAA;;AACJ,YAAM,CAACR,YAAY5B,YAAAA,IAAeoC;AAClC,YAAMG,yBAAyBC,oBAAoBxC,YAAAA,EAAayC,KAAK,CAACC,QAAQA,GAAAA;AAE9E,UAAI,CAACH,wBAAwB;AAC3B,eAAOD;MACT;AAEA,UAAI,GAACtC,KAAAA,gBAAAA,gBAAAA,aAAa8B,eAAb9B,mBAAyB+B,UAAS;AACrC,cAAMY,0BAA0BzB,OAAOQ,QAAQ1B,aAAY8B,UAAU,EAAEV,OAGrE,CAACkB,MAAKF,aAAAA;AACJ,gBAAM,CAACQ,cAAcC,aAAAA,IAAiBT;AAGtCE,UAAAA,KAAIR,WAAWc,YAAa,IAAGE,oBAAoBD,aAAAA;AAEnD,iBAAOP;WAET;UACErC,QAAQ2B;UACR1B;UACA2B,YAAYI,sBAAsBjC,aAAY6B,UAAU;UACxDC,YAAY,CAAA;QACd,CAAA;AAGF,eAAO;UAAIQ,GAAAA;UAAKK;QAAwB;MAC1C;AAEA,UAAI,CAAC3C,aAAY8B,WAAWC,SAAS;AACnC,eAAOO;MACT;AAEAA,UAAIN,KAAK;QACP/B,QAAQ2B;QACR1B;QACA4B,YAAY,CAAA;QACZD,YAAYI,sBAAsBjC,aAAY6B,UAAU;MAC1D,CAAA;AAEA,aAAOS;IACT,GACA,CAAA,CAAE;AAGJ,WAAO;MAAIH,GAAAA;MAAmBnC,GAAAA;IAAY;EAC5C,GACA,CAAA,CAAE;AAGJ,SAAOA;AACT;AAEA,IAAM8C,sBAAsB,CAACD,eAAkCE,SAAS,OAAE;AACxE,SAAO7B,OAAOQ,QAAQmB,aAAAA,EAAezB,OAAiB,CAACkB,KAAKF,YAAAA;AAC1D,UAAM,CAACY,MAAMC,KAAAA,IAASb;AAEtB,QAAIc,SAASD,KAAQ,GAAA;AACnB,aAAO;QAAIX,GAAAA;QAAQQ,GAAAA,oBAAoBG,OAAO,GAAGF,MAAAA,GAASC,IAAAA,GAAO;MAAE;IACrE;AAEA,QAAIC,SAAS,CAACC,SAASD,KAAQ,GAAA;AAC7BX,UAAIN,KAAK,GAAGe,MAAO,GAAEC,IAAAA,EAAM;IAC7B;AAEA,WAAOV;EACT,GAAG,CAAA,CAAE;AACP;AAEA,IAAML,wBAAwB,CAACJ,eAC7BX,OAAOQ,QAAQG,UAAAA,EACZsB,OAAO,CAAC,CAAA,EAAGC,cAAe,MAAA;AACzB,SAAOA;AACT,CAAA,EACCC,IAAI,CAAC,CAACC,aAAAA,MAAmBA,aAAAA;;;AC9I9B,IAAMC,8BAA8B,CAClCC,YACAC,oBAA8C,CAAA,MAE9CD,WAAWE,OAAsB,CAACC,KAAKC,YAAAA;AACrCD,MAAIC,QAAQC,EAAE,IAAIJ,kBAAkBK,QAAQF,QAAQC,EAAE,MAAM;AAE5D,SAAOF;AACT,GAAG,CAAA,CAAC;AAgBN,IAAMI,oBAAoB,CACxBC,QACAR,YACAS,qBAAmC,CAAA,MAAE;AAErC,SAAOD,OAAON,OAA6B,CAACC,KAAK,EAAEO,YAAYC,aAAY,MAAE;AAC3E,UAAMC,sBAAsBD,aAAaT,OAAa,CAACC,MAAKC,YAAAA;AAC1DD,MAAAA,KAAIC,QAAQS,aAAa,IAAIT,QAAQU,QAAQZ,OAAqB,CAACC,MAAKC,aAAAA;AACtE,cAAMW,0BAA0BC,uBAC9BP,oBACAL,SAAQa,QACR,IAAA;AAGFd,QAAAA,KAAIC,SAAQa,MAAM,IAAI;UACpBC,YAAY;YACVC,SAASJ,4BAA4BK;UACvC;UACApB,YAAYD,4BACVC,aACAe,mEAAyBf,eAAc,CAAA,CAAE;QAE7C;AAEA,eAAOG;MACT,GAAG,CAAA,CAAC;AAEJ,aAAOA;IACT,GAAG,CAAA,CAAC;AAEJA,QAAIO,UAAAA,IAAcE;AAElB,WAAOT;EACT,GAAG,CAAA,CAAC;AACN;AAWA,IAAMkB,8BAA8B,CAClCH,YACAI,SACAC,uBAAAA;AAEA,QAAMC,gCAAgC,CACpC,EAAEC,WAAW,CAAA,EAAE,GACfC,gBACAC,SAAS,OAAE;AAEX,WAAOF,SAASvB,OAA0B,CAACC,KAAKC,YAAAA;AAC9C,UAAIA,QAAQqB,UAAU;AACpB,eAAO;UACL,GAAGtB;UACH,CAACC,QAAQwB,KAAK,GAAGJ,8BACfpB,SACAsB,gBACA,GAAGC,MAAAA,GAASvB,QAAQwB,KAAK,GAAG;QAEhC;MACF;AAEA,YAAMC,cAAcH,eAAepB,QAAQ,GAAGqB,MAAO,GAAEvB,QAAQwB,KAAK,EAAE,MAAM;AAE5EzB,UAAIC,QAAQwB,KAAK,IAAIC;AAErB,aAAO1B;IACT,GAAG,CAAA,CAAC;EACN;AAEA,SAAOe,WAAWhB,OAChB,CAACC,KAAK2B,wBAAAA;AACJ,UAAMC,gBAAgBT,QAAQJ,WAAWc,KAAK,CAAC,EAAEJ,MAAK,MAAOA,UAAUE,mBAAAA;AAEvE,QAAIC,eAAe;AACjB,YAAME,oCACJV,yDAAoBL,WAAWa,cAAcH,WAAU,CAAA;AAEzD,YAAMM,eAAeV,8BACnBO,eACAE,gCAAAA;AAGF9B,UAAIe,WAAWY,mBAAAA,IAAuBI;IACxC;AAEA,WAAO/B;KAET;IAAEe,YAAY,CAAA;EAAG,CAAA;AAErB;AAKMiB,IAAAA,sBAAsB,CAC1B,EAAEC,UAAUtB,UAAU,CAAA,EAAE,GACxBd,YACAS,qBAAmC,CAAA,MAAE;AAErC,SAAOK,QAAQZ,OAAa,CAACmC,aAAapB,WAAAA;AAGxC,UAAMqB,iBAAiBrB,OAAOmB,SAASlC,OAAuB,CAACC,KAAKC,YAAAA;AAClE,YAAMmC,cAAcH,SAASJ,KAAK,CAAC,EAAEQ,IAAG,MAAOA,QAAQpC,OAAY,KAAA;AAEnE,UAAImC,aAAa;AACfpC,YAAIC,OAAAA,IAAWmC;MACjB;AAEA,aAAOpC;IACT,GAAG,CAAA,CAAC;AAKJ,YAAIsC,eAAAA,SAAQH,cAAiB,GAAA;AAC3B,aAAOD;IACT;AAGA,UAAMK,sBAAsBC,OAAOC,KAAKN,cAAAA,EAAgBpC,OAAa,CAACC,KAAK0C,iBAAAA;AACzE,YAAM,EAAEC,UAAUC,kBAAiB,IAAK9B;AACxC,YAAM+B,uBAAuBV,eAAeO,YAAa;AACzD,YAAM3B,aAAa8B,qBAAqB9B,WAAW+B,IAAI,CAAC,EAAErB,MAAK,MAAOA,KAAAA;AACtE,YAAMsB,sBAAsBhC,WAAWiC,MACrC,CAACC,cAAcL,qBAAqB,CAAA,GAAIzC,QAAQ8C,QAAAA,MAAc,EAAC;AAGjE,YAAM7B,qBAAqBP,uBAAuBP,oBAAoBqC,UAAUD,YAAAA;AAChF,YAAMQ,iBAAiBtD,4BACrBC,aACAuB,yDAAoBvB,eAAc,CAAA,CAAE;AAGtC,UAAI,CAACG,IAAI0C,YAAAA,GAAe;AACtB1C,YAAI0C,YAAa,IAAG,CAAA;MACtB;AAEA,cAAIJ,eAAAA,SAAQM,iBAAAA,KAAsBG,qBAAqB;AACrD/C,YAAI0C,YAAAA,EAAcC,QAAAA,IAAY;UAC5B5B,YAAY;YACVC,SAASI,uBAAuBH;UAClC;UACApB,YAAYqD;QACd;AAEA,eAAOlD;MACT;AAEA,YAAMmD,iBAAiBjC,4BACrB0B,mBACAT,eAAeO,YAAAA,GACftB,kBAAAA;AAGFpB,UAAI0C,YAAAA,EAAcC,QAAAA,IAAY;QAAE,GAAGQ;QAAgBtD,YAAYqD;MAAe;AAE9E,aAAOlD;IACT,GAAG,CAAA,CAAC;AAEJ,eAAOoD,aAAAA,SAAMlB,aAAaK,mBAAAA;EAC5B,GAAG,CAAA,CAAC;AACN;;;;ACtMMc,IAAAA,eAAe,CACnBC,QACAC,eAAAA;AAEA,SAAOC,OAAOC,YAAQC,eAAAA,SAAQJ,QAAQC,UAAAA,CAAAA,EAAaI,IAAI,CAAC,CAACC,UAAUC,IAAAA,OAAW;IAC5EC,UAAUF;IACVG,YAAYH,SAASI,MAAM,GAAA,EAAKC,KAAK,GAAA;IACrCC,cAAcV,OAAOC,YAAQC,eAAAA,SAAQG,MAAM,aAAA,CAAA,EAAgBF,IACzD,CAAC,CAACQ,iBAAiBC,OAAAA,OAAc;MAC/BD;MACAE,eAAeF,gBAAgBH,MAAM,GAAA,EAAKC,KAAK,GAAA;MAC/CG;MACF;IAEJ;AACF;;;;;ACpBA,IAAME,0BAA0B,CAACC,QAAAA;AAC/B,SAAOC,OAAOC,KAAKF,GAAAA,EAAKG,OAAO,CAACC,KAAKC,YAAAA;AAEnC,UAAMC,eAAeN,IAAIK,OAAQ;AAEjC,QAAIE,SAASD,YAAAA,KAAiB,KAACE,WAAAA,SAAIF,cAAc,YAAe,GAAA;AAC9D,aAAO;QAAE,GAAGF;QAAK,CAACC,OAAAA,GAAUN,wBAAwBO,YAAAA;MAAc;IACpE;AAEA,QAAIC,SAASD,YAAAA,SAAiBE,WAAAA,SAAIF,cAAc,YAAe,GAAA;AAC7D,YAAMG,kBAAkBC,wBAAoBC,YAAAA,SAAKL,cAAc,YAAA,CAAA,EAAeM,KAC5E,CAACC,QAAQA,GAAAA;AAGX,UAAI,CAACJ,iBAAiB;AAEpB,cAAMK,oBAAoBb,OAAOC,KAAKI,aAAaS,UAAU,EAAEZ,OAAO,CAACa,MAAMX,aAAAA;AAE3EW,eAAKX,QAAAA,IAAW;AAEhB,iBAAOW;QACT,GAAG,CAAA,CAAC;AAEJ,eAAO;UAAE,GAAGZ;UAAK,CAACC,OAAAA,GAAU;YAAE,GAAGC;YAAcS,YAAYD;UAAkB;QAAE;MACjF;IACF;AAGAV,QAAIC,OAAAA,IAAWC;AAEf,WAAOF;EACT,GAAG,CAAA,CAAC;AACN;;;AClCC,IACKa,eAAe,CAACC,KAAaC,YAAqBC,gBAAgB,UAAK;AAC3E,SAAOC,OAAOC,KAAKJ,GAAAA,EAAKK,OAAO,CAACC,KAAKC,YAAAA;AACnC,UAAMC,eAAeR,IAAIO,OAAwB;AAEjD,QAAIA,YAAY,gBAAgB,CAACL,eAAe;AAE9CI,UAAIC,OAAAA,IAAWC;AAEf,aAAOF;IACT;AAEA,QAAIG,SAASD,YAAe,GAAA;AAC1B,aAAO;QAAE,GAAGF;QAAK,CAACC,OAAQ,GAAER,aAAaS,cAAcP,YAAYM,YAAY,QAAA;MAAU;IAC3F;AAGAD,QAAIC,OAAAA,IAAWN;AAEf,WAAOK;EACT,GAAG,CAAA,CAAC;AACN;;;;;;;;;;;;;AC5BaI,IAAAA,YAAY;AACZC,IAAAA,gBAAgB;AAChBC,IAAAA,YAAY;;;ACAzB,IAAMC,6BAA6B,CACjCC,QAAAA;AAEA,MAAI,CAACA,KAAK;AACR,WAAO;EACT;AAEA,SAAOC,OAAOC,QAAQF,GAAKG,EAAAA,OAAO,CAACC,KAAK,CAACC,KAAKC,KAAM,MAAA;AAClD,QAAID,QAAQ,cAAc;AAExBD,UAAIC,GAAAA,IAAOC;IACb;AAEA,WAAOF;EACT,GAAG,CAAA,CAAC;AACN;;;ACZA,IAAMG,mBAAmB,CAACC,YAAAA;AACxB,QAAMC,uBAAuBC,2BAA2BF,OAAAA;AAExD,QAAMG,gBAAgBC,oBAAoBH,oBAAAA;AAE1C,MAAI,CAACE,cAAcE,QAAQ;AACzB,WAAO;MAAEC,uBAAuB;MAAOC,wBAAwB;IAAM;EACvE;AAEA,QAAMD,wBAAwBH,cAAcK,MAAM,CAACC,QAAQA,GAAAA;AAC3D,QAAMF,yBAAyBJ,cAAcO,KAAK,CAACD,QAAQA,GAAAA,KAAQ,CAACH;AAEpE,SAAO;IAAEA;IAAuBC;EAAuB;AACzD;;;;;;;;ACfMI,IAAAA,gBAAgBC,GAAsBC,IAAAA;mBACzB,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAO,CAAA,CAAE;;;IAG7C,CAAC,EAAEC,eAAc,MAAOA,kBAAkB,kBAAmB;;;;ACH3DC,IAAAA,eAAeC,GAAOC;WACjBC,SAAU;;;;;ACHfC,IAAAA,eAAe,UACnBC,wBAACC,KAAAA;EAAIC,OAAM;EAAYC,aAAa;EAAG,UAAA;;;;;;ACoBnCC,IAAAA,uBAAuB,CAAC,EAC5BC,eAAe,IACfC,UACAC,WAAW,OACXC,gBAAgB,OAChBC,iBAAiB,OACjBC,OACAC,UACAC,SACAC,cAAc,OACdC,MAAK,MACqB;AAC1B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,QAAMC,qBAAqB;IACzBC,OAAOR;IACPS,YAAY;IACZC,gBAAgBZ;EAClB;AAEA,MAAIA,eAAe;AACjBa,WAAOC,OAAOL,oBAAoB;MAChCL;MACA,iBAAiBL;MACjBgB,UAAU,EAAEC,IAAG,GAAuC;AACpD,YAAIA,QAAQ,WAAWA,QAAQ,KAAK;AAClCZ,kBAAAA;QACF;MACF;MACAa,UAAU;MACVC,MAAM;IACR,CAAA;EACF;AAEA,aACEC,0BAACC,MAAAA;IAAKT,YAAW;IAASU,aAAa;IAAGC,OAAOC;IAAeC,QAAQ;;UACtEC,yBAACC,KAAAA;QAAIC,cAAc;QACjB,cAAAF,yBAACG,cAAAA;UACCC,MAAMhC;UACNiC,cAAYvB,cACV;YACEwB,IAAI;YACJC,gBAAgB;aAElB;YAAE9B;UAAM,CAAA;UAEV+B,UAAUhC;;UAEViC,iBAAiB,CAAC5B,WAChBH,SAAS;YACPgC,QAAQ;cACNN,MAAMhC;cACNS,OAAO,CAAC,CAACA;YACX;UACF,CAAA;UAEF8B,SAAS/B,cAAc,kBAAkBC;;;UAG7Ca,0BAACkB,eAAAA;QAAe,GAAG5B;;cACjBgB,yBAACa,YAAAA;YAAWC,UAAQ;YAAErC,UAAAA;;UACrBJ;;;;;AAIT;;;ACzCA,IAAM0C,yBAAyB,CAAC,EAC9BC,mBAAmB,CAAA,GACnBC,eAAe,CAAA,GACfC,gBACAC,OACAC,YACAC,aAAY,MACgB;AAC5B,QAAMC,kBAAwBC,cAC5B,MACEP,iBAAiBQ,IAAI,CAACC,WAAAA;AACpB,UAAMC,mCACJC,MAAMC,QAAQH,OAAOI,iBAAiB,KACtCJ,OAAOI,kBAAkBC,QAAQT,YAAAA,MAAkB,MACnDI,OAAOM;AAET,WAAO;MAAEZ,OAAOM,OAAON;MAAOa,UAAUP,OAAOO;MAAUN;IAAiC;GAE9F,GAAA;IAACV;IAAkBK;EAAa,CAAA;AAGlC,aACEY,0BAACC,MAAAA;IAAKC,SAAQ;IAAcC,WAAU;IAASC,YAAW;IAAUC,UAAU;;UAC5EC,yBAACC,QAAAA;QAAOrB;QAAcsB,SAASnB;;UAC/BiB,yBAACG,KAAAA;QACEzB,UAAAA,aAAaO,IAAI,CAAC,EAAEmB,UAAU1B,eAAcE,OAAAA,QAAOyB,OAAOC,SAAQ,GAAIC,UACrEP,yBAACQ,WAAAA;UACC9B,cAAcA;UAEdE,OAAOA;UACPD;UACA8B,MAAMJ;UACNC;UACAvB;UACAF;UACAC;UACA4B,OAAOH,IAAI,MAAM;QARZF,GAAAA,KAAAA,CAAAA;;;;AAcjB;AAkBA,IAAMG,YAAY,CAAC,EACjB9B,eAAe,CAAA,GACfE,OACAD,iBAAiB,OACjB8B,MACAH,WAAW,OACXzB,YACAE,iBACAD,cACA4B,QAAQ,MAAK,MACE;AACf,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,CAACC,WAAWC,YAAAA,IAAsBC,eAAwB,IAAA;AAChE,QAAM,EACJC,cACAC,6CACAC,wBACAC,uBAAsB,IACpBC,0BAAAA;AAEJ,QAAMC,WAAWR,cAAcJ;AAE/B,QAAMa,oBAA0BtC,cAAQ,MAAA;AACtC,QAAI,CAACI,MAAMC,QAAQX,YAAe,GAAA;AAChC,aAAO,CAAA;IACT;AAEA,WAAOA;KACN;IAACA;EAAa,CAAA;AAEjB,QAAM6C,gBAAgBD,kBAAkBE,SAAS;AAEjD,QAAMC,cAAoBC,kBAAY,MAAA;AACpC,QAAIH,eAAe;AACjBT,mBAAa,CAACa,SAAAA;AACZ,YAAIA,SAASlB,MAAM;AACjB,iBAAO;QACT;AAEA,eAAOA;MACT,CAAA;IACF;KACC;IAACc;IAAed;EAAK,CAAA;AAExB,QAAMmB,8BAAqE,CAAC,EAC1EC,QAAQ,EAAExB,MAAK,EAAE,MAClB;AACCY,gDAA4CpC,YAAYC,cAAc2B,MAAMJ,KAAAA;EAC9E;AAEA,QAAM,EAAEyB,uBAAuBC,uBAAsB,IAAW/C,cAAQ,MAAA;AACtE,WAAOgD,yBAAyBjD,iBAAiBiC,cAAcnC,YAAYC,cAAc2B,IAAAA;KACxF;IAAC1B;IAAiBiC;IAAcnC;IAAYC;IAAc2B;EAAK,CAAA;AAElE,aACEf,0BAAAuC,8BAAA;;UACEjC,yBAACkC,SAAAA;QACCpC,YAAW;QACXqC,gBAAgBZ;QAChBa,WAAWf;QACXgB,YAAY3B,QAAQ,eAAe;QAEnC,cAAAhB,0BAACC,MAAAA;;gBACCD,0BAAC4C,sBAAAA;cACCC,UAAUX;cACVY,SAASf;cACTF;cACA5C;cACAC;cACA6D,aAAaV;cACb1B,OAAOyB;cACPT;;gBAECf,gBAAYN,yBAAC0C,cAAAA,CAAAA,CAAAA;oBACd1C,yBAAC2C,YAAAA;kBAAWP,WAAWf;;;;gBAEzBrB,yBAACL,MAAAA;wBACEZ,gBAAgBE,IAAI,CAAC,EAAEL,OAAAA,QAAOO,kCAAkCM,SAAQ,MAAE;AACzE,oBAAI,CAACN,kCAAkC;AACrC,6BAAOa,yBAAC4C,cAAkBhE,CAAAA,GAAAA,MAAAA;gBAC5B;AAEA,sBAAMiE,eAAe;kBAChBhE,GAAAA,WAAWiE,MAAM,IAAA;kBACpBrD;kBACA;kBACAX;kBACA2B;gBACD;AAED,oBAAI,CAACc,eAAe;AAClB,wBAAMwB,oBAAgBC,WAAAA,SAAIhC,cAAc6B,cAAc,KAAA;AAEtD,6BACE7C,yBAACL,MAAAA;oBAECsD,OAAOC;oBACPC,UAAS;oBACTC,gBAAe;oBACftD,YAAW;oBAEX,cAAAE,yBAACqD,cAAAA;sBACCC,UAAU3E;sBACV8B,MAAMoC,aAAaU,KAAK,IAAA;sBACxBC,cAAY7C,cACV;wBACE8C,IAAI;wBACJC,gBAAgB;yBAElB;wBAAE9E,OAAO,GAAG6B,IAAAA,IAAQ7B,MAAAA;sBAAQ,CAAA;sBAE9B+E,iBAAiB,CAACtD,UAAAA;AAChBc,+CAAuB;0BACrBU,QAAQ;4BACNpB,MAAMoC,aAAaU,KAAK,IAAA;4BACxBlD,OAAO,CAAC,CAACA;0BACX;wBACF,CAAA;sBACF;sBACAuD,SAASb;;kBAxBNtD,GAAAA,QAAAA;gBA4BX;AAEA,sBAAMoE,WAAOb,WAAAA,SAAIhC,cAAc6B,cAAc,CAAA,CAAC;AAE9C,sBAAM,EAAEf,uBAAAA,wBAAuBC,wBAAAA,wBAAsB,IAAK+B,iBAAiBD,IAAAA;AAE3E,2BACE7D,yBAACL,MAAAA;kBAECsD,OAAOC;kBACPC,UAAS;kBACTC,gBAAe;kBACftD,YAAW;kBAEX,cAAAE,yBAACqD,cAAAA;oBACCC,UAAU3E;oBACV8B,MAAMoC,aAAaU,KAAK,IAAA;oBACxBI,iBAAiB,CAACtD,UAAAA;AAChBa,6CAAuB;wBACrBW,QAAQ;0BACNpB,MAAMoC,aAAaU,KAAK,IAAA;0BACxBlD,OAAO,CAAC,CAACA;wBACX;sBACF,CAAA;oBACF;oBACAmD,cAAY7C,cACV;sBACE8C,IAAI;sBACJC,gBAAgB;uBAElB;sBAAE9E,OAAO,GAAG6B,IAAAA,IAAQ7B,MAAAA;oBAAQ,CAAA;oBAE9BgF,SAAS7B,0BAAyB,kBAAkBD;;gBAxBjDlD,GAAAA,MAAAA;cA4BX,CAAA;;;;;MAILyC,gBACCrB,yBAAC+D,cAAAA;QACCrF,cAAc4C;QACd3C;QACAqF,YAAYvD;QACZwD,yBAAyBpF;QACzBC;QACAC;QACAmF,gBAAgB;;;;AAK1B;AAMA,IAAMlC,2BAA2B,CAC/BjD,iBACAiC,cACAmD,mBACAC,iBACAC,cAAAA;AAEA,QAAMC,YAAYvF,gBAAgBwF,OAAiB,CAACC,KAAKC,YAAAA;AACvD,QAAIA,QAAQtF,kCAAkC;AAC5CqF,UAAIE,KAAKD,QAAQhF,QAAQ;IAC3B;AAEA,WAAO+E;EACT,GAAG,CAAA,CAAE;AAEL,QAAMX,OAAOS,UAAUC,OAAgC,CAACC,KAAKC,YAAAA;AAC3D,UAAME,eAAW3B,WAAAA,SACfhC,cACA;MAAImD,GAAAA,kBAAkBrB,MAAM,IAAA;MAAO2B;MAAS;MAAcL;MAAiBC;OAC3E,KAAA;AAGFG,QAAIC,OAAAA,IAAWE;AAEf,WAAOH;EACT,GAAG,CAAA,CAAC;AAEJ,SAAOV,iBAAiBD,IAAAA;AAC1B;AAEA,IAAM3B,UAAU0C,GAAsBjF,IAAAA;YAC1BkF,SAAU;;;;MAIhB,CAAC,EAAE1C,gBAAgB2C,MAAK,MAAO3C,kBAAkB4C,YAAYD,KAAO,CAAA;;;IAGtE,CAAC,EAAE3C,eAAc,MACjBA,kBACA;QACIQ,UAAW;;;GAGhB;IACC,CAAC,EAAEP,WAAW0C,MAAK,MAAO1C,aAAa2C,YAAYD,KAAO,CAAA;;AAG9D,IAAMnC,aAAaiC,GAAOI,aAAAA;;;;;;;;YAQd,CAAC,EAAEF,MAAK,MAAOA,MAAMG,OAAOC,UAAU;;;sBAG5B,CAAC,EAAE9C,UAAS,MAAQA,YAAY,QAAQ,GAAK;iBAClD,CAAC,EAAE0C,MAAK,MAAOA,MAAMK,OAAO,CAAA,CAAE;;AAiB/C,IAAMpB,eAAe,CAAC,EACpBrF,eAAe,CAAA,GACfC,gBACAuF,gBACAD,yBACAlF,iBACAiF,YACAlF,aAAY,MACM;AAClB,QAAM,EAAE6B,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEI,cAAcE,wBAAwBC,uBAAsB,IAClEC,0BAAAA;AACF,QAAM,CAACP,WAAWC,YAAAA,IAAsBC,eAAwB,IAAA;AAEhE,QAAMqE,4BAA4B,CAAC3E,SAAAA;AACjCK,iBAAa,CAACa,SAAAA;AACZ,UAAIA,SAASlB,MAAM;AACjB,eAAO;MACT;AAEA,aAAOA;IACT,CAAA;EACF;AAEA,QAAM4E,6BAAmCrG,cAAQ,MAAA;AAC/C,QAAI,CAAC6B,WAAW;AACd,aAAO;IACT;AAEA,WAAOnC,aAAa4G,KAAK,CAAC,EAAEjF,MAAK,MAAOA,UAAUQ,SAAAA;KACjD;IAACA;IAAWnC;EAAa,CAAA;AAE5B,aACEgB,0BAACS,KAAAA;IAAIoF,aAAa;;UAChBvF,yBAACwF,aAAAA,CAAAA,CAAAA;MACA9G,aAAaO,IAAI,CAAC,EAAEL,OAAOyB,OAAOC,UAAUF,UAAUqF,gBAAe,GAAIC,UAAAA;AACxE,cAAMC,YAAYD,QAAQ,IAAIhH,aAAa8C;AAC3C,cAAMoE,cAAcxG,MAAMC,QAAQoG,eAAAA;AAClC,cAAMpE,WAAWR,cAAcR;AAE/B,mBACEX,0BAACmG,oBAAAA;UAA+BC,YAAYH;;gBAC1CjG,0BAACC,MAAAA;cAAKoG,QAAQlB;;oBACZ7E,yBAACgG,WAAAA;kBACC,cAAAhG,yBAACiG,KAAAA;oBACChD,OAAM;oBACN8C,QAAO;oBACPG,SAAQ;oBACRC,MAAK;oBACLC,OAAM;oBACNC,QAAO;oBAEP,cAAArG,yBAACsG,QAAAA;sBACCC,UAAS;sBACTC,UAAS;sBACTC,GAAE;sBACFN,MAAK;;;;oBAIXzG,0BAACC,MAAAA;kBAAK+G,OAAO;oBAAEC,MAAM;kBAAE;;wBACrB3G,yBAAC4G,UAAAA;sBAASC,QAAQ3C;sBAAgB9B,WAAWf;sBAAUc,gBAAgByD;sBACrE,cAAAlG,0BAACoH,eAAAA;wBACChH,YAAW;wBACXqC,gBAAgByD;wBACf,GAAIA,eAAe;0BAClBpD,SAAS,MAAM4C,0BAA0B/E,KAAAA;0BACzC,iBAAiBgB;0BACjB0F,WAAW,CAAC,EAAEC,IAAG,OACdA,QAAQ,WAAWA,QAAQ,QAAQ5B,0BAA0B/E,KAAAA;0BAChE4G,UAAU;0BACVC,MAAM;;wBAERC,OAAOvI;;8BAEPoB,yBAACoH,UAAAA;4BAASC,UAAQ;4BAAEzI,UAAAA;;0BACnB0B,gBAAYN,yBAAC0C,cAAAA,CAAAA,CAAAA;8BACd1C,yBAAC2C,YAAAA;4BAAWP,WAAWf;;;;;wBAG3BrB,yBAACL,MAAAA;sBAAK+G,OAAO;wBAAEC,MAAM;sBAAE;gCACpB5H,gBAAgBE,IACf,CAAC,EAAEQ,UAAUb,OAAO0I,eAAenI,iCAAgC,MAAE;AACnE,4BAAI,CAACA,kCAAkC;AACrC,qCAAOa,yBAAC4C,cAAkBnD,CAAAA,GAAAA,QAAAA;wBAC5B;AAKA,8BAAMoD,eAAe;0BAChBoB,GAAAA,wBAAwBnB,MAAM,IAAA;0BACjCrD;0BACA;0BACAX;0BACGkF,GAAAA,WAAWlB,MAAM,IAAA;0BACpBzC;wBACD;AAED,8BAAM0C,oBAAgBC,WAAAA,SAAIhC,cAAc6B,cAAc,KAAA;AAEtD,4BAAI,CAAC4C,iBAAiB;AACpB,qCACEzF,yBAACL,MAAAA;4BAECwD,UAAS;4BACTF,OAAOC;4BACPE,gBAAe;4BACftD,YAAW;4BAEX,cAAAE,yBAACqD,cAAAA;8BACCC,UAAU3E;8BACV8B,MAAMoC,aAAaU,KAAK,IAAA;8BACxBC,cAAY7C,cACV;gCACE8C,IAAI;gCACJC,gBAAgB;iCAElB;gCAAE9E,OAAO,GAAGoF,UAAW,IAAGpF,KAAM,IAAG0I,aAAAA;8BAAgB,CAAA;8BAErD3D,iBAAiB,CAACtD,WAAAA;AAChBc,uDAAuB;kCACrBU,QAAQ;oCACNpB,MAAMoC,aAAaU,KAAK,IAAA;oCACxBlD,OAAO,CAAC,CAACA;kCACX;gCACF,CAAA;8BACF;8BACAuD,SAASb;;0BAxBNuE,GAAAA,aAAAA;wBA4BX;AAEA,8BAAM,EAAExF,uBAAuBC,uBAAsB,IACnD+B,iBAAiBf,aAAAA;AAEnB,mCACE/C,yBAACL,MAAAA;0BAECwD,UAAS;0BACTF,OAAOC;0BACPE,gBAAe;0BACftD,YAAW;0BAEX,cAAAE,yBAACqD,cAAAA;4BAECC,UAAU3E;4BACV8B,MAAMoC,aAAaU,KAAK,IAAA;4BACxBC,cAAY7C,cACV;8BACE8C,IAAI;8BACJC,gBAAgB;+BAElB;8BAAE9E,OAAO,GAAGoF,UAAW,IAAGpF,KAAM,IAAG0I,aAAAA;4BAAgB,CAAA;;4BAGrD3D,iBAAiB,CAACtD,WAAAA;AAChBa,qDAAuB;gCACrBW,QAAQ;kCACNpB,MAAMoC,aAAaU,KAAK,IAAA;kCACxBlD,OAAO,CAAC,CAACA;gCACX;8BACF,CAAA;4BACF;4BACAuD,SACE7B,yBAAyB,kBAAkBD;0BApBxCwF,GAAAA,aAAAA;wBAPFA,GAAAA,aAAAA;sBAgCX,CAAA;;;;;;YAKPjC,8BAA8BhE,gBAC7BrB,yBAACG,KAAAA;cAAIoH,eAAe;cAClB,cAAAvH,yBAAC+D,cAAAA;gBACCpF;gBACAqF,YAAY,GAAGA,UAAAA,KAAe3D,KAAAA;gBAC9B4D;gBACAlF;gBACAD;gBACAoF,gBAAgBA,iBAAiB;gBACjCxF,cAAc2G,2BAA2BjF;;;;QAhJxBC,GAAAA,KAAAA;MAsJ7B,CAAA;;;AAGN;AAEA,IAAMwF,qBAAqBjB,GAAqBzE,GAAAA;iBAC/B,CAAC,EAAE2F,YAAYhB,MAAK,MACjCgB,aAAa,aAAahB,MAAMG,OAAOuC,UAAU,KAAK,uBAAwB;;AAGlF,IAAMZ,WAAWhC,GAAsBjF,IAAAA;kBAKrB,CAAC,EAAEmF,MAAK,MAAOA,MAAMK,OAAO,CAAA,CAAE;WACrC,CAAC,EAAE0B,OAAM,MAAO,MAAMA,SAAS,EAAG;;;MAGvC,CAAC,EAAE1E,gBAAgB2C,MAAK,MAAO3C,kBAAkB4C,YAAYD,KAAO,CAAA;;;IAGtE,CAAC,EAAE3C,eAAc,MACjBA,kBACA;QACIQ,UAAW;;;GAGhB;IACC,CAAC,EAAEP,WAAW0C,MAAK,MAAO1C,aAAa2C,YAAYD,KAAO,CAAA;;AAG9D,IAAMsC,WAAWxC,GAA4B6C,UAAW;AAExD,IAAMjC,cAAcZ,GAAO8C;iBACV,CAAC,EAAE5C,MAAK,MAAOA,MAAMK,OAAO,CAAA,CAAE;gBAC/B,CAAC,EAAEL,MAAK,MAAOA,MAAMK,OAAO,CAAA,CAAE;;sBAExB,CAAC,EAAEL,MAAK,MAAOA,MAAMG,OAAOuC,UAAU;;;;AAK5D,IAAMxB,YAAYpB,GAAqBzE,GAAAA;;;;;;;kBAOrB,CAAC,EAAE2E,MAAK,MAAOA,MAAMG,OAAOuC,UAAU;;;;AAKxD,IAAMvB,MAAMrB,GAAO+C;;;;;;YAMP,CAAC,EAAE7C,OAAOuB,OAAM,MAAOvB,MAAMG,OAAOoB,MAAAA,CAAO;;;AAavD,IAAMpG,SAAS,CAAC,EAAEC,UAAU,CAAA,GAAItB,MAAK,MAAe;AAClD,QAAM,EAAE+B,cAAa,IAAKC,QAAAA;AAE1B,aACElB,0BAACC,MAAAA;;UACCK,yBAACL,MAAAA;QAAKsD,OAAO2E;QAAe7B,QAAQlB;QAAWgD,QAAQ;QAAG/H,YAAW;QAASyF,aAAa;QACzF,cAAAvF,yBAACyH,YAAAA;UAAWK,SAAQ;UAAQC,WAAU;oBACnCpH,cACC;YACE8C,IAAI;YACJC,gBAAgB;aAElB;YAAE9E;UAAM,CAAA;;;MAIbsB,QAAQjB,IAAI,CAAC+I,WAAAA;AACZ,YAAI,CAACA,OAAO7I,kCAAkC;AAC5C,qBAAOa,yBAACL,MAAAA;YAAKsD,OAAOC;YAAW2E,QAAQ;UAAQG,GAAAA,OAAOpJ,KAAK;QAC7D;AAEA,mBACEoB,yBAACL,MAAAA;UAAKsD,OAAOC;UAAW2E,QAAQ;UAAGzE,gBAAe;UAChD,cAAApD,yBAACyH,YAAAA;YAAWK,SAAQ;YAAQC,WAAU;sBACnCpH,cAAc;cACb8C,IAAI,mCAAmCuE,OAAOpJ,MAAMqJ,YAAW,CAAA;cAC/DvE,gBAAgBsE,OAAOpJ;YACzB,CAAA;;QAL4DoJ,GAAAA,OAAOpJ,KAAK;MAShF,CAAA;;;AAGN;AAEA,IAAMmG,cAAc,CAACD,UAAwBoD;WAClCpD,MAAMG,OAAOkD,UAAU;iBACjBrD,MAAMsD,YAAYC,IAAI;;IAEnC1F,UAAW;;cAEDmC,MAAMG,OAAOkD,UAAU;;;;;;;;ACtpBrC,IAAMG,uBAA6BC,kBACjC,CAAC,EAAEC,SAASC,WAAWC,gBAAgB,OAAOC,UAAU,WAAU,GAAIC,QAAAA;AACpE,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEC,yBAACC,iBAAAA;IAAgBC,gBAAgBP;IAAeD;IAC9C,cAAAM,yBAACG,QAAAA;MAAOP;MAAkBQ,eAAWJ,yBAACK,eAAAA,CAAAA,CAAAA;MAAQZ;MAAkBI;MAAUS,MAAK;gBAC5ER,cAAc;QACbS,IAAI;QACJC,gBAAgB;MAClB,CAAA;;;AAIR,CAAA;AAGF,IAAMP,kBAAkBQ,GAAqBC,GAAAA;IACzC,CAAC,EAAER,gBAAgBS,MAAK,MACxBT,kBACA;;;;;;;;;oBASgBS,MAAMC,OAAOC,UAAU;;GAExC;;AAQGC,IAAAA,mBAAmBL,GAAOlB,oBAAAA;;;;;;;;ACJhC,IAAMwB,kBAAkB,CAAC,EACvBC,UAAU,CAAA,GACVC,oBAAoB,CAAA,GACpBC,gBACAC,QAAO,MACc;AACrB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,qBAAqBC,cAAcC,mBAAkB,IAAKC,0BAAAA;AAElE,QAAMC,kCAAwCC,eAAQ,MAAA;AACpD,WAAOC,OAAOC,YAAQC,gBAAAA,SAAQR,qBAAqB,UAAA,CAAA;KAClD;IAACA;EAAoB,CAAA;AAExB,QAAMS,mBAAmBf,QAAQgB;;IAE/B,CAAC,EAAEC,aAAaC,wBAAwBC,sBAAqB,MAC3DF,eAAeG,QAAQF,0BAA0BC,qBAAAA;EAAAA;AAGrD,QAAM,CAACE,OAAOC,QAAS,IAASC,gBAC9BC,6BAA4BT,kBAAkBR,cAAcG,+BAAAA,CAAAA;AAG9D,QAAMe,eAAe,CAACC,MAAcC,WAAAA;AAClCL,aACEM,GAAQ,CAACC,UAAAA;AACP,UAAI,CAACA,MAAMH,IAAAA,GAAO;AAChBG,cAAMH,IAAK,IAAG,CAAA;MAChB;AAEA,UAAI,CAACG,MAAMH,IAAK,EAACI,SAAS;AACxBD,cAAMH,IAAAA,EAAMI,UAAU,CAAA;MACxB;AAEAD,YAAMH,IAAAA,EAAMI,UAAUH;IACxB,CAAA,CAAA;EAEJ;AAEA,QAAMI,eAAe,MAAA;AACnB,UAAMC,4BAA4BpB,OAAOC,QAAQQ,KAAAA,EAAOY,OACtD,CAACC,KAAKC,YAAAA;AACJ,YAAM,CAACC,KAAKC,KAAAA,IAASF;AAErB,YAAMG,SAAS1B,OAAOe,OAAOU,KAAAA,EAAOJ,OAAO,CAACM,MAAMC,aAAAA;AAChD,eAAO;UAAE,GAAGD;UAAM,GAAGC;QAAS;MAChC,GAAG,CAAA,CAAC;AAEJN,UAAIE,GAAAA,IAAOE;AAEX,aAAOJ;IACT,GACA,CAAA,CAAC;AAGH1B,uBAAmBwB,yBAAAA;AACnB7B,eAAWA,QAAAA;EACb;AAEA,QAAMsC,eAAe,MAAA;AACnBnB,aACEE,6BAA4BT,kBAAkBR,cAAcG,+BAAAA,CAAAA;AAG9DP,eAAWA,QAAAA;EACb;AAEA,aACEuC,0BAACC,MAAMC,SAAO;;UACZC,yBAACF,MAAMG,QAAM;QACX,cAAAD,yBAACE,aAAAA;UAAYC,IAAG;UAA8BC,OAAOhD,kBAAkBiD,KAAK,IAAA;UACzEjD,UAAAA,kBAAkBkD,IAAI,CAACF,OAAOG,OAAOC,YACpCR,yBAACS,OAAAA;YAAMC,WAAWH,UAAUC,IAAIG,SAAS;YACtCC,cAAAA,kBAAAA,SACCrD,cAAc;cACZ4C,IAAIC;cACJS,gBAAgBT;YAClB,CAAA,CAAA;UAL6CA,GAAAA,KAAAA,CAAAA;;;UAWvDP,0BAACC,MAAMgB,MAAI;;UACR5C,iBAAiByC,WAAW,SAC3BX,yBAACe,YAAAA;sBACExD,cAAc;cACb4C,IAAI;cACJU,gBACE;YACJ,CAAA;;cAGJb,yBAACgB,MAAAA;sBACE9C,iBAAiBoC,IAAI,CAAC,EAAEW,UAAUb,OAAOc,uBAAsB,GAAIX,UAAAA;AAClE,oBAAM1B,OAAOqC,uBAAuBb,KAAK,IAAA;AAEzC,yBACEL,yBAACmB,YAAAA;gBAECtD;gBACAuC;gBACA/C;gBACA+D,QAAQb,QAAQ,MAAM;gBACtB1B;gBACAwC,UAAUzC;gBACVY,WAAO8B,YAAAA,SAAI9C,OAAOK,MAAM,CAAA,CAAC;cAPpBoC,GAAAA,QAAAA;YAUX,CAAA;;;;UAGJpB,0BAACC,MAAMyB,QAAM;;cACXvB,yBAACwB,QAAAA;YAAOC,SAAQ;YAAWC,SAAS,MAAM9B,aAAAA;sBACvCrC,cAAc;cAAE4C,IAAI;cAAgCU,gBAAgB;YAAS,CAAA;;cAEhFb,yBAACwB,QAAAA;YAAOE,SAASxC;sBACd3B,cAAc;cACb4C,IAAI;cACJU,gBAAgB;YAClB,CAAA;;;;;;AAKV;AAEA,IAAMlC,+BAA8B,CAClCT,kBACAR,cACAG,oCAAAA;AAEA,SAAOK,iBAAiBkB,OAAsD,CAACC,KAAKC,YAAAA;AAClF,UAAMqC,4BAAuCL,YAAAA,SAC3C5D,cACA;MAAI4B,GAAAA,QAAQ4B;MAAwB;IAAa,GACjD,CAAA,CAAC;AAGH,UAAMU,sBAAsB/D,gCAAgCuB,OAE1D,CAACC,MAAKC,aAAAA;AACN,YAAM,CAACuC,cAAcC,iBAAAA,IAAqBxC;AAE1C,YAAMyC,iBAAiBD,kBAAkB1C,OAAsB,CAACC,MAAKC,aAAAA;AACnED,QAAAA,KAAIC,SAAQa,EAAE,QAAImB,YAAAA,SAAIK,uBAAuBrC,SAAQa,IAAI,KAAA;AAEzD,eAAOd;MACT,GAAG,CAAA,CAAC;AAEJA,MAAAA,KAAIwC,YAAAA,IAAgBE;AAEpB,aAAO1C;IACT,GAAG,CAAA,CAAC;AAEJA,QAAIC,QAAQ4B,uBAAuBb,KAAK,IAAA,CAAA,IAASuB;AAEjD,WAAOvC;EACT,GAAG,CAAA,CAAC;AACN;AAkBA,IAAM8B,aAAY,CAAC,EACjBtD,iCACAR,iBAAiB,OACjB+D,SAAS,OACThB,OACAvB,MACAwC,UACA7B,MAAK,MACU;AACf,QAAM,EAAEjC,cAAa,IAAKC,QAAAA;AAE1B,QAAMoB,eAAmD,CAACoD,QAAAA;AACxD,QAAIX,UAAU;AACZA,eAASxC,MAAMoD,6BAA6BpE,iCAAiCmE,GAAAA,CAAAA;IAC/E;EACF;AAEA,aACEnC,0BAACqC,MAAAA;IACCC,KAAI;IACJC,YAAYhB,SAAS,eAAe;IACpCiB,eAAe;IACfC,YAAY;IACZC,gBAAgB;;UAEhB1C,0BAACqC,MAAAA;QAAKM,OAAO;UAAEC,OAAO;QAAI;;cACxB5C,0BAACkB,YAAAA;YAAWU,SAAQ;YAAQiB,WAAU;;cACnCnF,cAAc;gBACb4C,IAAI;gBACJU,gBAAgB;cAClB,CAAA;cAAG;;;cAGLb,yBAACe,YAAAA;YAAWU,SAAQ;YAAQkB,OAAOvC;YAAOsC,WAAU;YAAaE,UAAQ;sBACtErF,cAAc;cACb4C,IAAI,mCAAmCC,MAAMyC,YAAW,CAAA;cACxDhC,gBAAgBT;YAClB,CAAA;;cAEFP,0BAACkB,YAAAA;YAAWU,SAAQ;YAAQiB,WAAU;;cAAa;cAEhDnF,cAAc;gBACb4C,IAAI;gBACJU,gBAAgB;cAClB,CAAA;;;;;UAGJb,yBAAC8C,KAAAA;QAAIN,OAAO;UAAEO,UAAU;UAAKN,OAAO;QAAO;QACzC,cAAAzC,yBAACgD,mBAAAA;UACC7C,IAAItB;UACJoE,kBAAkB,CAACnE,SAAS,CAAA,MAAO,GAAGA,OAAO6B,MAAM;UACnDU,UAAUzC;UACVY,OAAO0D,kBAAkB1D,KAAAA;UACzB2D,SAASC,iBAAiBvF,+BAAAA;UAC1BwF,UAAUhG;;;;;AAKpB;AAEA,IAAM6F,oBAAoB,CAACI,aACzBvF,OAAOe,OAAOwE,QACXhD,EAAAA,IAAI,CAACiD,MACJxF,OAAOC,QAAQuF,CACZpF,EAAAA,OAAO,CAAC,CAAA,EAAGqB,KAAAA,MAAWA,KAAAA,EACtBc,IAAI,CAAC,CAACf,GAAI,MAAKA,GAAAA,CAAAA,EAEnBiE,KAAI;AAET,IAAMJ,mBAAmB,CAACD,YACxBA,QAAQ/D,OAA0C,CAACC,KAAK,CAACe,OAAOqD,QAAS,MAAA;AACvEpE,MAAIqE,KAAK;IACPtD,OAAOuD,WAAWvD,KAAAA;IAClBqD,UAAUA,SAASnD,IAAI,CAACsD,WAAW;MACjCxD,OAAOwD,MAAMC;MACbrE,OAAOoE,MAAMzD;MACf;EACF,CAAA;AAEA,SAAOd;AACT,GAAG,CAAA,CAAE;AAEP,IAAM4C,+BAA+B,CACnCkB,SACAW,kBAEAX,QACG7C,IAAI,CAAC,CAAA,EAAGxB,MAAAA,MAAYA,MAAAA,EACpB0E,KAAI,EACJpE,OACC,CAACC,KAAK0E,UAAU;EAAE,CAACA,KAAK5D,EAAE,GAAG2D,cAAcE,SAASD,KAAK5D,EAAE;EAAG,GAAGd;AAAI,IACrE,CAAA,CAAC;;;ACtRP,IAAM4E,uBAAuB,CAAC,EAC5BC,UAAU,CAAA,GACVC,gBACAC,YACAC,WAAW,CAAA,EAAE,MACa;AAC1B,QAAM,CAACC,gBAAgBC,iBAAAA,IAA2BC,gBAAwB,IAAA;AAE1E,QAAMC,4BAA4B,CAACC,iBAAyB,MAAA;AAC1D,UAAMC,qBAAqBL,mBAAmBI,eAAe,OAAOA;AAEpEH,sBAAkBI,kBAAAA;EACpB;AAEA,aACEC,yBAAAC,8BAAA;cACGR,SAASS,IAAI,CAAC,EAAEC,KAAKC,OAAOC,WAAU,GAAIC,UAAAA;AACzC,YAAMC,WAAWb,mBAAmBS;AACpC,YAAMK,mBAAmBlB,QAAQY,IAAI,CAACO,YAAY;QAChD,GAAGA;QACHC,aAAaC,MAAMC,QAAQH,OAAOhB,QAAQ,KAAKgB,OAAOhB,SAASoB,QAAQV,GAAAA,MAAS;QAClF;AACA,iBACEW,0BAACC,MAAAA;QAECC,WAAU;QACVC,SAAQ;QACRC,YAAW;QACXC,UAAS;QACTC,aAAab,WAAW,eAAec;;cAEvCrB,yBAACsB,UAAAA;YACCd;YACAD;YACAgB,QAAQjB,QAAQ,MAAM;YACtBf;YACAa;YACAoB,eAAe3B,0BAA0BM,GAAAA;YACzCX,YAAY;cAACA;cAAYW;YAAI,EAACsB,KAAK,IAAA;;UAEpClB,YACCF,WAAWH,IAAI,CAAC,EAAEE,OAAOsB,eAAeC,OAAOC,UAAUC,aAAY,MAAE;AACrE,uBACE7B,yBAAC8B,wBAAAA;cACCtB;cACAqB;cACAtC;cACAa,OAAOsB;cACPlC,YAAY;gBAACA;gBAAYW;cAAI,EAACsB,KAAK,IAAA;cACnCM,cAAcJ;YACTA,GAAAA,KAAAA;UAGX,CAAA;;MA7BGxB,GAAAA,GAAAA;IAgCX,CAAA;;AAGN;AAcA,IAAMmB,WAAW,CAAC,EAChBd,mBAAmB,CAAA,GACnBD,WAAW,OACXgB,SAAS,OACThC,iBAAiB,OACjBa,OACAoB,eACAhC,WAAU,MACI;AACd,QAAM,EAAEwC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,cAAcC,wBAAwBC,uBAAsB,IAClEC,0BAAAA;AACF,QAAM,CAACC,sBAAsBC,uBAAAA,IAAiC3C,gBAAS,KAAA;AAIvE,QAAM4C,eAAWC,YAAAA,SAAIP,cAAc1C,WAAWkD,MAAM,IAAA,GAAO,CAAA,CAAC;AAG5D,QAAMC,uBAA6BC,eAAQ,MAAA;AACzC,WAAOC,OAAOC,KAAKN,QAAAA,EAAUO,OAAsC,CAACC,KAAKC,YAAAA;AACvED,UAAIC,OAAQ,QAAGC,aAAAA,SAAKV,SAASS,OAAAA,GAAU,YAAA;AAEvC,aAAOD;IACT,GAAG,CAAA,CAAC;KACH;IAACR;EAAS,CAAA;AAEb,QAAM,EAAEW,uBAAuBC,uBAAsB,IAAKC,iBAAiBV,oBAAAA;AAK3E,QAAMW,oBAA0BV,eAAQ,MAAA;AACtC,WAAOW,0BAA0B/C,kBAAkB0B,cAAc1C,UAAAA;KAChE;IAACgB;IAAkB0B;IAAc1C;EAAW,CAAA;AAG/C,QAAMgE,mCAAmCF,kBAAkBG,KAAK,CAAChD,WAAWA,OAAOiD,aAAa;AAEhG,aACE5C,0BAAC6C,YAAAA;IAAWC,WAAWrD;;UACrBO,0BAAC+C,UAAAA;QACCC,QAAQC;QACRC,MAAM;QACN9C,YAAW;QACX+C,YAAY1C,SAAS,eAAe;;cAEpCvB,yBAACkE,sBAAAA;YACCC,eAAa;YACb5E;YACAa,OAAOgE,WAAWhE,KAAAA;YAClBiE,cAAc7E;YACd8E,UAAUnC;YACVoC,SAAS/C;YACTgD,aAAapB;YACbzB,OAAOwB;YACP5C;YAEA,cAAAP,yBAACyE,SAAAA;cAAQC,aAAa;wBAAInE,eAAWP,yBAAC2E,eAAAA,CAAAA,CAAAA,QAAe3E,yBAAC4E,eAAAA,CAAAA,CAAAA;;;cAGxD5E,yBAACe,MAAAA;YAAK8D,OAAO;cAAEb,MAAM;YAAE;sBACpBV,kBAAkBpD,IACjB,CAAC,EAAE4E,UAAU1B,wBAAAA,yBAAwB1C,aAAa,GAAGqE,WAAY,MAAA;AAC/D,kBAAI,CAACrE,aAAa;AAChB,2BAAOV,yBAACgF,cAAkBF,CAAAA,GAAAA,QAAAA;cAC5B;AAEA,oBAAM,EACJpB,eACAP,uBAAAA,wBACA8B,kBACAZ,cACAjE,OAAO8E,gBAAe,IACpBH;AAEJ,kBAAIE,kBAAkB;AACpB,2BACEnE,0BAACqE,MAAAA;kBAAoBC,gBAAe;kBAASlE,YAAW;;oBACrDwC,qBACC1D,yBAACqF,KAAAA;sBACCC,KAAI;sBACJC,UAAS;sBACTC,KAAI;sBACJC,MAAK;sBACLC,OAAM;sBACN5B,QAAO;sBACP6B,cAAa;sBACb1B,YAAW;;wBAGfjE,yBAAC4F,cAAAA;sBACCC,UAAUtG;sBACVuG,MAAMzB;sBACN0B,cAAY/D,cACV;wBACEgE,IAAI;wBACJC,gBAAgB;yBAElB;wBAAE7F,OAAO,GAAG8E,eAAAA,IAAmB9E,KAAAA;sBAAQ,CAAA;;sBAGzC8F,iBAAiB,CAACvE,UAAAA;AAChBQ,+CAAuB;0BACrBgE,QAAQ;4BACNL,MAAMzB;4BACN1C,OAAO,CAAC,CAACA;0BACX;wBACF,CAAA;sBACF;sBACAyE,SAAShD,0BAAyB,kBAAkBD;;;gBAhC7C2B,GAAAA,QAAAA;cAoCf;AAEA,yBACEhE,0BAACqE,MAAAA;gBAAoBC,gBAAe;gBAASlE,YAAW;;kBACrDwC,qBACC1D,yBAACqF,KAAAA;oBACCC,KAAI;oBACJC,UAAS;oBACTC,KAAI;oBACJC,MAAK;oBACLC,OAAM;oBACN5B,QAAO;oBACP6B,cAAa;oBACb1B,YAAW;;sBAGfjE,yBAAC4F,cAAAA;oBACCC,UAAUtG;oBACVuG,MAAMzB;;oBAEN6B,iBAAiB,CAACvE,UAAAA;AAChBS,6CAAuB;wBACrB+D,QAAQ;0BACNL,MAAMzB;0BACN1C,OAAO,CAAC,CAACA;wBACX;sBACF,CAAA;oBACF;oBACAyE,SAAS1C,gBAAgB,kBAAkBP;;;cAzBpC2B,GAAAA,QAAAA;YA6Bf,CAAA;;;;UAIN9E,yBAACqF,KAAAA;QAAIgB,QAAO;QAAOC,OAAM;QAAMf,UAAS;sBACtCzE,0BAACyF,MAAMC,MAAI;UACTC,MAAMnE;UACNoE,cAAc,MAAA;AACZnE,oCAAwB,CAACoE,SAAS,CAACA,IAAAA;UACrC;;gBAEA3G,yBAACuG,MAAMK,SAAO;cACZ,cAAA5G,yBAAC6G,kBAAAA;gBAAiBnD,eAAeF;;;gBAEnCxD,yBAAC8G,iBAAAA;cACCC,mBAAmB;gBAAC3G;gBAAO;cAA6C;cACxEd,SAASgE;cACT/D;cACAyH,SAAS,MAAA;AACPzE,wCAAwB,KAAA;cAC1B;;;;;;;AAMZ;AAqBA,IAAMgB,4BAA4B,CAChC/C,kBACA0B,cACA1C,eAAAA;AAEA,SAAOgB,iBAAiBN,IAAI,CAAC,EAAE4E,UAAUpE,aAAauG,mBAAmB7G,MAAK,MAAE;AAC9E,QAAI,CAACM,aAAa;AAChB,aAAO;QAAEoE;QAAU1B,wBAAwB;QAAO1C;MAAY;IAChE;AAEA,UAAMwG,wBAAwB;MAAI1H,GAAAA,WAAWkD,MAAM,IAAA;MAAOoC;IAAS;AACnE,UAAMqC,wBAAoBC,gBAAAA,SAAQH,iBAC9B,IAAA;MAAIC,GAAAA;MAAuB;MAAc;QACzCA;AACJ,UAAMG,sBAAkB5E,YAAAA,SAAIP,cAAc;MAAIgF,GAAAA;MAAuB;OAAe,IAAA;AAEpF,UAAMI,qBAAqB;MACzBxC;MACAT,cAAc8C,kBAAkB1F,KAAK,IAAA;MACrCiC,eAAe6D,oBAAoBF,eAAAA,EAAiB5D,KAAK,CAAC+D,QAAQA,GAAAA;MAClE9G;MACAN;MACAqH,wBAAwBP;IAC1B;AAEA,YAAIE,gBAAAA,SAAQH,iBAAoB,GAAA;AAC9B,YAAMtF,YAAQc,YAAAA,SAAIP,cAAciF,mBAAmB,KAAA;AAInD,aAAO;QACL,GAAGG;QACHnE,uBAAuBxB;QACvByB,wBAAwBzB;QACxBsD,kBAAkB;MACpB;IACF;AAEA,UAAMzC,eAAWC,YAAAA,SAAIP,cAAciF,mBAAmB,IAAA;AAEtD,UAAM,EAAEhE,uBAAuBC,uBAAsB,IAAKC,iBAAiBb,QAAAA;AAE3E,WAAO;MACL,GAAG8E;MACHnE;MACAC;MACA6B,kBAAkB;IACpB;EACF,CAAA;AACF;AAEA,IAAMyC,iBAAiB,CAACC,OAAqBpH,aAA+B;IACxEsD,QAAQ;wBACY8D,MAAMC,OAAOC,UAAU;aAClCF,MAAMC,OAAOE,UAAU;qBACfvH,WAAW,gBAAgB,KAAM;mBACnCoH,MAAMI,YAAYC,IAAI;;;IAGrCvD,OAAQ;;;IAGRoC,gBAAiB;;;;;MAKf,MAAMa,eAAeC,OAAOpH,QAAU,CAAA;;;AAI5C,IAAMsD,WAAUoE,GAAsBlH,IAAAA;;;AAItC,IAAM4C,aAAasE,GAAOC;;;;;IAKtBrB,gBAAiB;;;;IAIjB,CAAC,EAAEjD,WAAW+D,MAAK,MAAO/D,aAAa8D,eAAeC,OAAO/D,SAAW,CAAA;;;MAGtE,CAAC,EAAE+D,OAAO/D,UAAS,MAAO8D,eAAeC,OAAO/D,SAAW,CAAA;;;AAIjE,IAAMuB,OAAO8C,GAAsBlH,IAAAA;WACxBoH,SAAU;;;AAIrB,IAAM1D,UAAUwD,GAAqB5C,GAAAA;;;;;;;;YAQzB,CAAC,EAAEsC,MAAK,MAAOA,MAAMC,OAAOE,UAAU;;;;;;;;AClY5CM,IAAAA,gBAAgB,CAAC,EAAEC,UAAU,CAAA,GAAIC,gBAAgBC,KAAI,MAAsB;AAC/E,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,cAAcC,2CAA0C,IAAKC,0BAAAA;AAErE,QAAMC,mBAAmBR,QAAQS,OAAO,CAAC,EAAEC,SAAQ,MAAOA,YAAYA,SAASC,MAAM;AAErF,QAAMC,kBAAwBC,eAAQ,MAAA;AACpC,UAAMC,aAAaN,iBAAiBO,IAAI,CAAC,EAAEC,SAAQ,MAAOA,QAAAA;AAE1D,UAAMC,OAAOZ,aAAaH,IAAK;AAE/B,UAAMgB,qBAAqBJ,WAAWK,OACpC,CAACC,KAAKJ,aAAAA;AACJK,aAAOC,KAAKL,IAAMM,EAAAA,QAAQ,CAACC,UAAAA;AACzB,cAAMC,mBAAeC,YAAAA,SAAIT,MAAM;UAACO;UAAOR;QAAS,CAAA;AAEhD,cAAMW,gBAAgB;UAAE,CAACH,KAAAA,GAAQI,2BAA2BH,YAAAA;QAAe;AAE3E,YAAI,CAACL,IAAIJ,QAAAA,GAAW;AAClBI,cAAIJ,QAAAA,IAAYW;eACX;AACLP,cAAIJ,QAAAA,IAAY;YAAE,GAAGI,IAAIJ,QAAS;YAAE,GAAGW;UAAc;QACvD;MACF,CAAA;AAEA,aAAOP;IACT,GACA,CAAA,CAAC;AAGH,UAAMR,mBAAkBS,OAAOC,KAAKJ,kBAAAA,EAAoBC,OAQtD,CAACC,KAAKS,YAAAA;AACNT,UAAIS,OAAQ,IAAGC,iBAAiBZ,mBAAmBW,OAAQ,CAAA;AAE3D,aAAOT;IACT,GAAG,CAAA,CAAC;AAEJ,WAAOR;KACN;IAACP;IAAcG;IAAkBN;EAAK,CAAA;AAEzC,aACE6B,yBAACC,KAAAA;IAAIC,eAAe;IAAGC,YAAY;IAAGC,OAAO;MAAEC,aAAaC;IAAc;IACxE,cAAAN,yBAACO,MAAAA;MAAKC,KAAK;MACR/B,UAAAA,iBAAiBO,IAAI,CAAC,EAAEyB,OAAOxB,SAAQ,MAAE;AACxC,mBACEyB,0BAACH,MAAAA;UACCI,QAAQ;UACRC,OAAOC;UACPC,WAAU;UACVC,YAAW;UACXC,gBAAe;UAEfR,KAAK;;gBAELR,yBAACiB,YAAAA;cAAWC,SAAQ;cAAQC,WAAU;wBACnC/C,cAAc;gBACbgD,IAAI,mCAAmCX,MAAMY,YAAW,CAAA;gBACxDC,gBAAgBb;cAClB,CAAA;;gBAEFT,yBAACuB,cAAAA;cACCC,UAAUtD;cACVuD,iBAAiB,CAACC,UAAAA;AAChBnD,2DAA2CJ,MAAMc,UAAU,CAAC,CAACyC,KAAAA;cAC/D;cACAC,MAAM1C;cACN2C,cAAYxD,cACV;gBACEgD,IAAI;gBACJE,gBAAgB;iBAElB;gBACEb,OAAOrC,cAAc;kBACnBgD,IAAI,mCAAmCX,MAAMY,YAAW,CAAA;kBACxDC,gBAAgBb;gBAClB,CAAA;cACF,CAAA;cAEFoB,aACElC,YAAAA,SAAId,iBAAiB;gBAACI;gBAAU;iBAA2B,KAAA,IACvD,sBACAU,YAAAA,SAAId,iBAAiB;gBAACI;gBAAU;iBAA0B,KAAA;;;QA9B7DA,GAAAA,QAAAA;MAmCX,CAAA;;;AAIR;;;AC1GA,IAAM6C,eAAe,CAAC,EACpBC,gBACAC,MACAC,QAAQ,EAAEC,SAASC,SAAQ,EAAE,MACX;AAClB,QAAMC,iBAAiB;IAAID,GAAAA;IAAUE,KAAK,CAACC,GAAGC,MAAMD,EAAEE,MAAMC,cAAcF,EAAEC,KAAK,CAAA;AAEjF,aACEE,0BAACC,KAAAA;IAAIC,YAAW;;UACdC,yBAACC,eAAAA;QAAcZ;QAAkBF;QAAYD;;UAC7Cc,yBAACE,sBAAAA;QACCb;QACAH;QACAiB,YAAYhB;QACZG,UAAUC;;;;AAIlB;;;;;;ACeA,IAAMa,gCAAgC,CAAC,EACrCC,QACA,GAAGC,UACgC,MAAA;AACnC,aACEC,yBAACC,KAAAA;IAAIC,SAAS;IAAGC,YAAW;kBAC1BH,yBAACI,UAAUC,MAAI;MAACC,MAAK;gBAClBR,OAAOS,IAAI,CAAC,EAAEC,UAAUC,YAAYC,aAAY,GAAIC,UAAAA;AACnD,mBACEX,yBAACY,KAAAA;UAECF;UACAG,SAASF,QAAQ,MAAM,IAAI,YAAY;UACvCG,MAAMN;UACNO,YAAY;YAAChB,UAAUiB;YAAMP;UAAW;UACvC,GAAGV;QALCS,GAAAA,QAAAA;MAQX,CAAA;;;AAIR;AAeA,IAAMI,MAAM,CAAC,EACXF,cACAM,MACAF,MACAG,iBAAiB,OACjBJ,SACAE,WAAU,MACD;AACT,QAAM,EAAEG,cAAa,IAAKC,QAAAA;AAE1B,QAAMC,eAAeN,KAAKO,MAAM,IAAA,EAAMC,IAAG,KAAM;AAC/C,QAAMC,sBACJH,iBAAiB,WAAW,kBAAkBI,WAAWJ,aAAaK,QAAQ,MAAM,GAAA,CAAA;AAEtF,aACEC,0BAACtB,UAAUuB,MAAI;IAACC,OAAOd;;UACrBd,yBAACI,UAAUyB,QAAM;QAAChB;sBAChBb,yBAACI,UAAU0B,SAAO;UAChBC,eAAc;UACdC,aAAa,GAAGd,cACd;YAAEe,IAAI;YAAiCC,gBAAgBd;aACvD;YAAEZ,UAAUY;UAAa,CAAA,CAAA,IACtBJ,SAAS,YAAY,WAAWA,IAAAA;UAEpCO,UAAAA;;;UAGLvB,yBAACI,UAAU+B,SAAO;QAChB,cAAAnC,yBAACC,KAAAA;UAAIC,SAAS;oBACXQ,aAAaH,IAAI,CAAC,EAAE6B,SAASC,iBAAiBC,cAAa,UAC1DtC,yBAACuC,aAAAA;YAECH;YACAhB;YACAH;YACAoB;YACAtB,YAAY;cAAIA,GAAAA;cAAYuB;YAAc;UALrCD,GAAAA,eAAAA,CAAAA;;;;;AAYnB;AAcA,IAAME,cAAc,CAAC,EACnBH,UAAU,CAAA,GACVhB,cACAH,gBACAoB,iBACAtB,WAAU,MACO;AACjB,QAAM,EAAEyB,cAAcC,wBAAwBC,uBAAsB,IAClEC,0BAAAA;AACF,QAAM,CAACC,sBAAsBC,uBAAAA,IAAiCC,gBAAS,KAAA;AACvE,QAAM,EAAE5B,cAAa,IAAKC,QAAAA;AAE1B,QAAM4B,eAAWC,YAAAA,SAAIR,cAAczB,YAAY,CAAA,CAAC;AAEhD,QAAMkC,uBAA6BC,eAAQ,MAAA;AACzC,WAAOC,OAAOC,KAAKL,QAAAA,EAAUM,OAAkC,CAACC,KAAKC,YAAAA;AACnED,UAAIC,OAAQ,IAAGC,2BAA2BT,SAASQ,OAAQ,CAAA;AAE3D,aAAOD;IACT,GAAG,CAAA,CAAC;KACH;IAACP;EAAS,CAAA;AAEb,QAAM,EAAEU,uBAAuBC,uBAAsB,IAAKC,iBAAiBV,oBAAAA;AAG3E,QAAMW,mBAAyBV,eAAQ,MAAA;AACrC,WAAOd,QAAQ7B,IAAI,CAACsD,WAAAA;AAClB,YAAMC,eAAe;QAAI/C,GAAAA;QAAY8C,OAAOA;QAAQ;QAAc;MAAU;AAC5E,YAAME,oBAAgBf,YAAAA,SAAIR,cAAcsB,cAAc,KAAA;AACtD,YAAME,qBAAiBhB,YAAAA,SAAIR,cAAc;QAAIzB,GAAAA;QAAY8C,OAAOA;QAAQ;MAAa,GAAE,CAAA,CAAC;AACxF,YAAMI,gBAAgBC,oBAAoBF,cAAAA,EAAgBG,KAAK,CAACC,QAAQA,GAAAA;AAExE,aAAO;QACL,GAAGP;QACHQ,aAAaN;QACbD,cAAcA,aAAaQ,KAAK,IAAA;QAChCZ,wBAAwBK;QACxBnC,OAAOmC;QACPE;QACAM,OAAOV,OAAOW;QACdC,UAAUZ,OAAOA;QACjBa,wBAAwB;UAAI3D,GAAAA;UAAY8C,OAAOA;QAAO;MACxD;IACF,CAAA;KACC;IAACzB;IAASI;IAAczB;EAAW,CAAA;AAEtC,QAAM4D,YAAsB3B,YAAAA,SAAIR,cAAc;IAAIzB,GAAAA;EAAW,GAAE,CAAA,CAAC;AAEhE,QAAM6D,yBAAyBV,oBAC7Bf,OAAO0B,QAAQF,KAAOtB,EAAAA,OAAsC,CAACC,KAAKC,YAAAA;AAChE,UAAM,CAACuB,SAAS,EAAEC,WAAU,CAAE,IAAIxB;AAElCD,QAAIwB,OAAAA,IAAWC;AAEf,WAAOzB;EACT,GAAG,CAAA,CAAC,CAAA,EACJa,KAAK,CAACC,QAAQA,GAAAA;AAEhB,aACEpE,yBAAAgF,8BAAA;IACE,cAAAtD,0BAACzB,KAAAA;;YACCyB,0BAACuD,MAAAA;UAAKC,gBAAe;UAAgBC,YAAW;;gBAC9CnF,yBAACC,KAAAA;cAAImF,cAAc;cACjB,cAAApF,yBAACqF,YAAAA;gBAAWxE,SAAQ;gBAAQyE,WAAU;gBACnCjD,UAAAA;;;gBAGLrC,yBAACuF,QAAAA;cAAOC,MAAM;;gBACdxF,yBAACC,KAAAA;cAAIwF,aAAa;cAChB,cAAAzF,yBAAC0F,cAAAA;gBACC5E,MAAMC,WAAWuD,KAAK,IAAA;gBACtBqB,UAAU1E;;gBAEV2E,iBAAiB,CAAChE,UAAAA;AAChBa,yCAAuB;oBACrBoD,QAAQ;sBACN/E,MAAMC,WAAWuD,KAAK,IAAA;sBACtB1C,OAAO,CAAC,CAACA;oBACX;kBACF,CAAA;gBACF;gBACAkE,SAASpC,yBAAyB,kBAAkBD;0BAEnDvC,cAAc;kBAAEe,IAAI;kBAAwBC,gBAAgB;gBAAa,CAAA;;;;;YAIhFR,0BAACuD,MAAAA;UAAKc,YAAY;UAAGC,eAAe;;gBAClChG,yBAACiG,KAAK5F,MAAI;cAAC6F,KAAK;cAAGC,OAAO;gBAAEX,MAAM;cAAE;cACjC5B,UAAAA,iBAAiBrD,IAAI,CAAC,EAAEuD,cAAclC,OAAOiC,QAAQW,aAAaP,cAAa,MAAE;AAChF,2BACEjE,yBAACiG,KAAKtE,MAAI;kBAACyE,KAAK;kBAAgBC,WAAU;kBAASlB,YAAW;kBAC5D,cAAAnF,yBAACsG,iBAAAA;oBAAgBC,WAAWtF;oBAAgBuF,gBAAgBvC;oBAC1D,cAAAjE,yBAAC0F,cAAAA;sBACC5E,MAAMgD;sBACN6B,UAAU1E;;sBAEV2E,iBAAiB,CAAChE,WAAAA;AAChBc,+CAAuB;0BACrBmD,QAAQ;4BACN/E,MAAMgD;4BACNlC,OAAO,CAAC,CAACA;0BACX;wBACF,CAAA;sBACF;sBACAkE,SAASlE;sBAER4C,UAAAA;;;gBAhBiBX,GAAAA,MAAAA;cAqB5B,CAAA;;gBAEFnC,0BAAC+E,MAAMpG,MAAI;cACTqG,MAAM9D;cACN+D,cAAc,MAAA;AACZ9D,wCAAwB,CAAC+D,SAAS,CAACA,IAAAA;cACrC;;oBAEA5G,yBAACyG,MAAM3E,SAAO;kBACZ,cAAA9B,yBAAC6G,kBAAAA;oBAAiB5C,eAAeW;;;oBAEnC5E,yBAAC8G,iBAAAA;kBACCC,mBAAmB;oBAAC3F;oBAAciB;kBAAgB;kBAClDD,SAASwB;kBACT3C;kBACA+F,SAAS,MAAA;AACPnE,4CAAwB,KAAA;kBAC1B;;;;;;;;;AAOd;AAEA,IAAM0C,SAAS0B,GAAqBhH,GAAAA;;0BAEV,CAAC,EAAEiH,MAAK,MAAOA,MAAMC,OAAOC,UAAU;;AAGhE,IAAMd,kBAAkBW,GAAOI;;;IAG3B,CAAC,EAAEb,gBAAgBD,WAAWW,MAAK,MACnCV,kBACA;;;;;;;;;oBASgBD,YAAYW,MAAMC,OAAOG,aAAaJ,MAAMC,OAAOI,UAAU;;GAE9E;;;;AC9QH,IAAMC,aAAa;EACjB;IACEC,SAAS;IACTC,gBAAgB;IAChBC,IAAI;EACN;EACA;IACEF,SAAS;IACTE,IAAI;IACJD,gBAAgB;EAClB;EACA;IACED,SAAS;IACTC,gBAAgB;IAChBC,IAAI;EACN;EACA;IACEF,SAAS;IACTC,gBAAgB;IAChBC,IAAI;EACN;AACD;AAqBD,IAAMC,cAAoBC,kBACxB,CAAC,EAAEC,QAAQC,gBAAgBC,cAAc,CAAA,EAAE,GAAIC,QAAAA;AAC7C,QAAM,CAAC,EAAEC,aAAaC,SAASC,aAAY,GAAIC,QAAS,IAASC,kBAC/DC,SACAC,cACA,MAAMC,KAAKX,QAAQE,WAAAA,CAAAA;AAErB,QAAM,EAAEU,cAAa,IAAKC,QAAAA;AAE1BC,EAAMC,2BAAoBZ,KAAK,MAAA;AAC7B,WAAO;MACLa,iBAAAA;AACE,cAAMC,sBAAsBC,WAC1Bd,YAAYe,iBACZb,aAAaa,eAAe;AAE9B,cAAMC,kBAAkBF,WAAWd,YAAYiB,aAAaf,aAAae,WAAW;AAEpF,cAAMC,mBAAmB;UAAE,GAAGL;UAAqB,GAAGG;QAAgB;AAEtE,YAAIG;AAEJ,gBAAIC,gBAAAA,SAAQF,gBAAmB,GAAA;AAC7BC,gCAAsB;eACjB;AACLA,gCAAsBE,OAAOC,OAAOJ,gBAAAA,EAAkBK,KAAK,CAACC,aAAa,CAAA,MAAE;AACzE,mBAAOH,OAAOC,OAAOE,UAAAA,EAAYD,KAAK,CAACE,wBACrCC,YAAAA,SAAID,iBAAiB,YAAA,CAAA;UAEzB,CAAA;QACF;AAEA,eAAO;UAAEE,mBAAmBC,wBAAwB1B,YAAAA;UAAeiB;QAAoB;MACzF;MACAU,YAAAA;AACE1B,iBAAS;UAAE2B,MAAM;QAAa,CAAA;MAChC;MACAC,qBAAAA;AACE5B,iBAAS;UAAE2B,MAAM;QAAwB,CAAA;MAC3C;IACF;EACF,CAAA;AAEA,QAAME,kDAAkD,CACtDC,sBACAC,cACAC,SACAC,UAAAA;AAEAjC,aAAS;MACP2B,MAAM;MACNG;MACAC;MACAC;MACAC;IACF,CAAA;EACF;AAEA,QAAMC,iDAAiD,CACrDC,oBACAC,UACAH,UAAAA;AAEAjC,aAAS;MACP2B,MAAM;MACNQ;MACAC;MACAH;IACF,CAAA;EACF;AAEA,QAAMI,yBAAyB,CAACC,eAAAA;AAC9BtC,aAAS;MAAE2B,MAAM;MAAwBW;IAAW,CAAA;EACtD;AAEA,QAAMC,6BACEC,mBAAY,CAAC,EAAEC,QAAQ,EAAEC,MAAMT,MAAK,EAAE,MAAE;AAC5CjC,aAAS;MACP2B,MAAM;MACNgB,MAAMD;MACNT;IACF,CAAA;EACF,GAAG,CAAA,CAAE;AAEP,QAAMW,6BACEJ,mBAAY,CAAC,EAAEC,QAAQ,EAAEC,MAAMT,MAAK,EAAE,MAAE;AAC5CjC,aAAS;MACP2B,MAAM;MACNgB,MAAMD;MACNT;IACF,CAAA;EACF,GAAG,CAAA,CAAE;AAEP,aACEY,0BAACC,gCAAAA;IACCC,qBAAqBtD,OAAO6C;IAC5BvC;IACAiD,oBAAoBX;IACpBY,wBAAwBV;IACxBW,wBAAwBN;IACxBO,6CACEtB;IAEFuB,4CAA4ClB;kBAE5CmB,2BAACC,KAAKC,MAAI;MAACC,cAAcrE,WAAW,CAAE,EAACG;;YACrCuD,0BAACS,KAAKG,MAAI;UACRC,cAAYrD,cAAc;YACxBf,IAAI;YACJD,gBAAgB;UAClB,CAAA;UAECF,UAAAA,WAAWwE,IAAI,CAACC,iBACff,0BAACS,KAAKO,SAAO;YAAmB5B,OAAO2B,SAAStE;sBAC7Ce,cAAc;cAAEf,IAAIsE,SAASxE;cAASC,gBAAgBuE,SAASvE;YAAe,CAAA;UAD9DuE,GAAAA,SAAStE,EAAE,CAAA;;YAKlCuD,0BAACS,KAAKQ,SAAO;UAAC7B,OAAO9C,WAAW,CAAE,EAACG;UACjC,cAAAuD,0BAACkB,cAAAA;YACCtE,QAAQK,QAAQc;YAChBoD,MAAK;YACLtE;;;YAGJmD,0BAACS,KAAKQ,SAAO;UAAC7B,OAAO9C,WAAW,CAAE,EAACG;UACjC,cAAAuD,0BAACkB,cAAAA;YACCtE,QAAQK,QAAQgB;YAChBkD,MAAK;YACLtE;;;YAGJmD,0BAACS,KAAKQ,SAAO;UAAC7B,OAAO9C,WAAW,CAAE,EAACG;UACjC,cAAAuD,0BAACoB,+BAAAA;YACCxE,QAAQK,QAAQoE;YAChBF,MAAK;YACLtE;;;YAGJmD,0BAACS,KAAKQ,SAAO;UAAC7B,OAAO9C,WAAW,CAAE,EAACG;UACjC,cAAAuD,0BAACoB,+BAAAA;YACCxE,QAAQK,QAAQqE;YAChBH,MAAK;YACLtE;;;;;;AAMZ,CAAA;AAyBF,IAAMS,eAAe;EACnBN,aAAa,CAAA;EACbE,cAAc,CAAA;EACdD,SAAS,CAAA;AACX;AAoDA,IAAMI,UAAU,CAACkE,OAAcC,WAC7BC,GAAQF,OAAO,CAACG,eAAAA;AACd,UAAQF,OAAO1C,MAAI;IAGjB,KAAK,oDAAoD;AACvD,YAAM,EAAEQ,oBAAoBC,UAAUH,MAAK,IAAKoC;AAChD,YAAMG,aAAa;QAAC;QAAgBrC;MAAmB;AAEvDjB,aAAOyB,SAAK8B,YAAAA,SAAIL,OAAOI,UAAaE,CAAAA,EAAAA,QAAQ,CAACC,mBAAAA;AAC3C,cAAMC,+BAA2BH,YAAAA,SAC/BL,OACA;UAAII,GAAAA;UAAYG;UAAgBvC;WAChCyC,MAAAA;AAGF,YAAID,0BAA0B;AAC5B,cAAIE,gBAAgBC,aAAaH,0BAA0B3C,KAAAA;AAI3D,cAAI,CAACA,SAAS6C,cAAcxC,YAAY;AAEtC,kBAAM0C,oBAAoBD,aAAaD,cAAcxC,YAAY,KAAA;AAEjEwC,4BAAgB;cAAE,GAAGA;cAAexC,YAAY0C;YAAkB;UACpE;AAEAC,yBAAAA,SAAIV,YAAY;YAAIC,GAAAA;YAAYG;YAAgBvC;aAAW0C,aAAAA;QAC7D;MACF,CAAA;AAEA;IACF;IACA,KAAK,+CAA+C;AAClD,YAAM,EAAEhD,sBAAsBC,cAAcC,SAASC,MAAK,IAAKoC;AAC/D,UAAIa,4BAAwBC,iBAAAA,SAAUf,MAAMrE,YAAY;AACxD,YAAMqF,mCAAmCtD,qBAAqBuD,MAAM,IAAA;AAEpE,YAAMC,kBAAcb,YAAAA,SAAIS,uBAAuBE,kCAAkC,CAAA,CAAC;AAElFlE,aAAOyB,KAAK2C,WAAaZ,EAAAA,QAAQ,CAACtC,aAAAA;AAIhC,gBAAIb,YAAAA,SAAI+D,YAAYlD,QAAS,GAAE,cAAcL,YAAa,EAAC,GAAG;AAC5D,gBAAMwD,eAAWd,YAAAA,SAAIa,aAAa;YAAClD;YAAU;YAAcL;YAAcC;UAAQ,CAAA;AACjF,gBAAMwD,kBAAkB;YACnBJ,GAAAA;YACHhD;YACA;YACAL;YACAC;UACD;AAED,cAAI,CAACyD,SAASF,QAAW,GAAA;AACvBN,2BAAAA,SAAIC,uBAAuBM,iBAAiBvD,KAAAA;iBACvC;AACL,kBAAMyD,eAAeX,aAAaQ,UAAUtD,KAAAA;AAE5CgD,2BAAAA,SAAIC,uBAAuBM,iBAAiBE,YAAAA;UAC9C;QACF;MACF,CAAA;AAGA,UAAI,CAACzD,OAAO;AAEViD,gCAAwBS,wBAAwBT,qBAAAA;MAClD;AAEAD,qBAAAA,SAAIV,YAAY,gBAAgBW,qBAAAA;AAEhC;IACF;IACA,KAAK,wBAAwB;AAC3BhE,aAAO0E,QAAQvB,OAAO/B,UAAU,EAAEoC,QAAQ,CAACmB,UAAAA;AACzC,cAAM,CAACC,kBAAkBC,kBAAAA,IAAsBF;AAE/CZ,uBAAAA,SACEV,YACA;UAAC;UAAmBuB,GAAAA,iBAAiBT,MAAM,IAAA;UAAO;WAClDU,kBAAAA;MAEJ,CAAA;AAEA;IACF;IACA,KAAK,6BAA6B;AAChC,UAAIb,4BAAwBC,iBAAAA,SAAUf,MAAMrE,YAAY;AAExDkF,qBAAAA,SAAIC,uBAAuB;WAAIb,OAAO1B,KAAK0C,MAAM,IAAA;MAAM,GAAEhB,OAAOpC,KAAK;AAGrE,UAAI,CAACoC,OAAOpC,OAAO;AAEjBiD,gCAAwBS,wBAAwBT,qBAAAA;MAClD;AAEAD,qBAAAA,SAAIV,YAAY,gBAAgBW,qBAAAA;AAEhC;IACF;IAkCA,KAAK,oCAAoC;AACvC,YAAM,EAAEvC,MAAMV,MAAK,IAAKoC;AACxB,YAAM2B,cAAc;QAAIrD,GAAAA,KAAK0C,MAAM,IAAA;MAAM;AACzC,UAAIH,4BAAwBC,iBAAAA,SAAUf,MAAMrE,YAAY;AACxD,YAAMkG,gBAAYxB,YAAAA,SAAIS,uBAAuBc,aAAa,CAAA,CAAC;AAE3D,YAAMlB,gBAAgBC,aAAakB,WAAWhE,KAAAA;AAC9CgD,qBAAAA,SAAIC,uBAAuBc,aAAalB,aAAAA;AAGxC,UAAI,CAAC7C,OAAO;AAEViD,gCAAwBS,wBAAwBT,qBAAAA;MAClD;AAEAD,qBAAAA,SAAIV,YAAY;QAAC;SAAiBW,qBAAAA;AAElC;IACF;IACA,KAAK,cAAc;AACjBX,iBAAWxE,eAAeqE,MAAMvE;AAChC;IACF;IACA,KAAK,yBAAyB;AAC5B0E,iBAAW1E,cAAcuE,MAAMrE;AAC/B;IACF;IACA;AACE,aAAOwE;EACX;AACF,CAAA;AAMF,IAAMnE,OAAO,CACXX,QACAE,gBAAAA;AAEA,QAAM,EACJ2C,YACA4D,UAAU,EAAEtF,iBAAiBE,aAAaoD,SAASC,SAAQ,EAAE,IAC3D1E;AAEJ,QAAMK,UAAU;IACdc;IACAE;IACAoD,SAASiC,aAAajC,SAAS,QAAA;IAC/BC,UAAUgC,aAAahC,UAAU,UAAA;EACnC;AAEA,QAAMiC,cAAc;IAClBxF,iBAAiByF,oBAAoBzF,iBAAiB0B,YAAY3C,WAAAA;IAClEmB,aAAauF,oBAAoBvF,aAAawB,YAAY3C,WAAAA;IAC1DuE,SAASoC,kBAAkBxG,QAAQoE,SAAS5B,YAAY3C,WAAAA;IACxDwE,UAAUmC,kBAAkBxG,QAAQqE,UAAU7B,YAAY3C,WAAAA;EAC5D;AAEA,SAAO;IACLE,aAAauG;IACbrG,cAAcqG;IACdtG;EACF;AACF;", "names": ["isObject", "transform", "object", "PermissionsDataManagerProvider", "usePermissionsDataManagerContext", "createContext", "usePermissionsDataManager", "difference", "object", "base", "changes", "transform", "result", "value", "key", "isEqual", "isObject", "flattenDeep", "array", "Array", "isArray", "reduce", "acc", "value", "push", "createArrayOfValues", "obj", "isObject", "flattenDeep", "Object", "values", "map", "value", "findMatchingPermission", "permissions", "action", "subject", "find", "perm", "formatPermissionsForAPI", "modifiedData", "pluginsPermissions", "formatSettingsPermissions", "plugins", "settingsPermissions", "settings", "collectionTypesPermissions", "formatContentTypesPermissions", "collectionTypes", "singleTypesPermissions", "singleTypes", "settingsPermissionsObject", "Object", "values", "reduce", "formAcc", "form", "currentCategoryPermissions", "childFormAcc", "childForm", "entries", "responsesAcc", "actionName", "conditions", "properties", "enabled", "push", "createConditionsArray", "contentTypesPermissions", "allPermissions", "current", "currentSubjectActions", "acc", "shouldCreatePermission", "createArrayOfValues", "some", "val", "createdPermissionsArray", "propertyName", "propertyValue", "createPropertyArray", "prefix", "name", "value", "isObject", "filter", "conditionValue", "map", "conditionName", "createDefaultConditionsForm", "conditions", "initialConditions", "reduce", "acc", "current", "id", "indexOf", "createDefaultForm", "layout", "initialPermissions", "categoryId", "childrenForm", "childrenDefaultForm", "subCategoryId", "actions", "foundMatchingPermission", "findMatchingPermission", "action", "properties", "enabled", "undefined", "createDefaultPropertiesForm", "subject", "matchingPermission", "recursivelyCreatePropertyForm", "children", "propertyValues", "prefix", "value", "hasProperty", "currentPropertyName", "foundProperty", "find", "matchingPermissionPropertyValues", "propertyForm", "createDefaultCTForm", "subjects", "defaultForm", "subjectLayouts", "foundLayout", "uid", "isEmpty", "contentTypesActions", "Object", "keys", "currentCTUID", "actionId", "applyToProperties", "currentSubjectLayout", "map", "doesNothaveProperty", "every", "property", "conditionsForm", "propertiesForm", "merge", "formatLayout", "layout", "groupByKey", "Object", "entries", "groupBy", "map", "itemName", "item", "category", "categoryId", "split", "join", "childrenForm", "subCategoryName", "actions", "subCategoryId", "updateConditionsToFalse", "obj", "Object", "keys", "reduce", "acc", "current", "currentValue", "isObject", "has", "isActionEnabled", "createArrayOfValues", "omit", "some", "val", "updatedConditions", "conditions", "acc1", "updateValues", "obj", "valueToSet", "isFieldUpdate", "Object", "keys", "reduce", "acc", "current", "currentValue", "isObject", "cellWidth", "firstRowWidth", "rowHeight", "removeConditionKeyFromData", "obj", "Object", "entries", "reduce", "acc", "key", "value", "getCheckboxState", "dataObj", "dataWithoutCondition", "removeConditionKeyFromData", "arrayOfValues", "createArrayOfValues", "length", "hasAllActionsSelected", "hasSomeActionsSelected", "every", "val", "some", "CollapseLabel", "styled", "Flex", "theme", "spaces", "$isCollapsable", "HiddenAction", "styled", "div", "cellWidth", "RequiredSign", "_jsx", "Box", "color", "paddingLeft", "RowLabelWithCheckbox", "checkboxName", "children", "isActive", "isCollapsable", "isFormDisabled", "label", "onChange", "onClick", "someChecked", "value", "formatMessage", "useIntl", "collapseLabelProps", "title", "alignItems", "$isCollapsable", "Object", "assign", "onKeyDown", "key", "tabIndex", "role", "_jsxs", "Flex", "paddingLeft", "width", "firstRowWidth", "shrink", "_jsx", "Box", "paddingRight", "Checkbox", "name", "aria-label", "id", "defaultMessage", "disabled", "onCheckedChange", "target", "checked", "CollapseLabel", "Typography", "ellipsis", "CollapsePropertyMatrix", "availableActions", "childrenForm", "isFormDisabled", "label", "pathToData", "propertyName", "propertyActions", "useMemo", "map", "action", "isActionRelatedToCurrentProperty", "Array", "isArray", "applyToProperties", "indexOf", "isDisplayed", "actionId", "_jsxs", "Flex", "display", "direction", "alignItems", "min<PERSON><PERSON><PERSON>", "_jsx", "Header", "headers", "Box", "children", "value", "required", "i", "ActionRow", "name", "isOdd", "formatMessage", "useIntl", "rowToOpen", "setRowToOpen", "useState", "modifiedData", "onChangeCollectionTypeLeftActionRowCheckbox", "onChangeParentCheckbox", "onChangeSimpleCheckbox", "usePermissionsDataManager", "isActive", "recursive<PERSON><PERSON><PERSON>n", "isCollapsable", "length", "handleClick", "useCallback", "prev", "handleChangeLeftRowCheckbox", "target", "hasAllActionsSelected", "hasSomeActionsSelected", "getRowLabelCheckboxState", "_Fragment", "Wrapper", "$isCollapsable", "$isActive", "background", "RowLabelWithCheckbox", "onChange", "onClick", "someChecked", "RequiredSign", "CarretIcon", "HiddenAction", "checkboxName", "split", "checkboxValue", "get", "width", "cellWidth", "position", "justifyContent", "Checkbox", "disabled", "join", "aria-label", "id", "defaultMessage", "onCheckedChange", "checked", "data", "getCheckboxState", "SubActionRow", "parentName", "pathToDataFromActionRow", "recursiveLevel", "pathToContentType", "propertyToCheck", "<PERSON><PERSON><PERSON>", "actionIds", "reduce", "acc", "current", "push", "mainData", "styled", "rowHeight", "theme", "activeStyle", "CaretDown", "colors", "neutral200", "spaces", "handleClickToggleSubLevel", "displayedRecursiveChildren", "find", "paddingLeft", "TopTimeline", "subChildrenForm", "index", "isVisible", "isArrayType", "LeftBorderTimeline", "$isVisible", "height", "StyledBox", "Svg", "viewBox", "fill", "xmlns", "$color", "path", "fillRule", "clipRule", "d", "style", "flex", "RowStyle", "$level", "CollapseLabel", "onKeyDown", "key", "tabIndex", "role", "title", "RowLabel", "ellipsis", "propertyLabel", "paddingBottom", "primary200", "Typography", "div", "svg", "firstRowWidth", "shrink", "variant", "textColor", "header", "toLowerCase", "css", "primary600", "fontWeights", "bold", "ConditionsButtonImpl", "forwardRef", "onClick", "className", "hasConditions", "variant", "ref", "formatMessage", "useIntl", "_jsx", "ButtonContainer", "$hasConditions", "<PERSON><PERSON>", "startIcon", "Cog", "type", "id", "defaultMessage", "styled", "Box", "theme", "colors", "primary600", "ConditionsButton", "ConditionsModal", "actions", "headerBreadCrumbs", "isFormDisabled", "onClose", "formatMessage", "useIntl", "availableConditions", "modifiedData", "onChangeConditions", "usePermissionsDataManager", "arrayOfOptionsGroupedByCategory", "useMemo", "Object", "entries", "groupBy", "actionsToDisplay", "filter", "isDisplayed", "hasSomeActionsSelected", "hasAllActionsSelected", "Boolean", "state", "setState", "useState", "createDefaultConditionsForm", "handleChange", "name", "values", "produce", "draft", "default", "handleSubmit", "conditionsWithoutCategory", "reduce", "acc", "current", "key", "value", "merged", "acc1", "current1", "onCloseModal", "_jsxs", "Modal", "Content", "_jsx", "Header", "Breadcrumbs", "id", "label", "join", "map", "index", "arr", "Crumb", "isCurrent", "length", "upperFirst", "defaultMessage", "Body", "Typography", "ul", "actionId", "pathToConditionsObject", "ActionRow", "<PERSON><PERSON><PERSON>", "onChange", "get", "Footer", "<PERSON><PERSON>", "variant", "onClick", "valueFromModifiedData", "categoryDefaultForm", "categoryName", "relatedConditions", "conditionsForm", "val", "getNewStateFromChangedValues", "Flex", "tag", "background", "paddingBottom", "paddingTop", "justifyContent", "style", "width", "textColor", "title", "ellipsis", "toLowerCase", "Box", "max<PERSON><PERSON><PERSON>", "MultiSelectNested", "customizeContent", "getSelectedValues", "options", "getNestedOptions", "disabled", "rawValue", "x", "flat", "children", "push", "capitalise", "child", "displayName", "changedValues", "curr", "includes", "ContentTypeCollapses", "actions", "isFormDisabled", "pathToData", "subjects", "collapseToOpen", "setCollapseToOpen", "useState", "handleClickToggleCollapse", "collapseName", "nextCollapseToOpen", "_jsx", "_Fragment", "map", "uid", "label", "properties", "index", "isActive", "availableActions", "action", "isDisplayed", "Array", "isArray", "indexOf", "_jsxs", "Flex", "direction", "display", "alignItems", "min<PERSON><PERSON><PERSON>", "borderColor", "undefined", "Collapse", "<PERSON><PERSON><PERSON>", "onClickToggle", "join", "propertyLabel", "value", "children", "childrenForm", "CollapsePropertyMatrix", "propertyName", "formatMessage", "useIntl", "modifiedData", "onChangeParentCheckbox", "onChangeSimpleCheckbox", "usePermissionsDataManager", "isConditionModalOpen", "setIsConditionModalOpen", "mainData", "get", "split", "dataWithoutCondition", "useMemo", "Object", "keys", "reduce", "acc", "current", "omit", "hasAllActionsSelected", "hasSomeActionsSelected", "getCheckboxState", "checkboxesActions", "generateCheckboxesActions", "doesConditionButtonHasConditions", "some", "hasConditions", "BoxWrapper", "$isActive", "Wrapper", "height", "rowHeight", "flex", "background", "RowLabelWithCheckbox", "isCollapsable", "capitalise", "checkboxName", "onChange", "onClick", "someChecked", "Chevron", "paddingLeft", "ChevronUp", "ChevronDown", "style", "actionId", "restAction", "HiddenAction", "isParentCheckbox", "permissionLabel", "Cell", "justifyContent", "Box", "tag", "position", "top", "left", "width", "borderRadius", "Checkbox", "disabled", "name", "aria-label", "id", "defaultMessage", "onCheckedChange", "target", "checked", "bottom", "right", "Modal", "Root", "open", "onOpenChange", "prev", "<PERSON><PERSON>", "ConditionsButton", "ConditionsModal", "headerBreadCrumbs", "onClose", "applyToProperties", "baseCheckboxNameArray", "checkboxNameArray", "isEmpty", "conditionsValue", "baseCheckboxAction", "createArrayOfValues", "val", "pathToConditionsObject", "activeRowStyle", "theme", "colors", "primary100", "primary600", "fontWeights", "bold", "styled", "div", "cellWidth", "GlobalActions", "actions", "isFormDisabled", "kind", "formatMessage", "useIntl", "modifiedData", "onChangeCollectionTypeGlobalActionCheckbox", "usePermissionsDataManager", "displayedActions", "filter", "subjects", "length", "checkboxesState", "useMemo", "actionsIds", "map", "actionId", "data", "relatedActionsData", "reduce", "acc", "Object", "keys", "for<PERSON>ach", "ctUid", "actionIdData", "get", "actionIdState", "removeConditionKeyFromData", "current", "getCheckboxState", "_jsx", "Box", "paddingBottom", "paddingTop", "style", "paddingLeft", "firstRowWidth", "Flex", "gap", "label", "_jsxs", "shrink", "width", "cellWidth", "direction", "alignItems", "justifyContent", "Typography", "variant", "textColor", "id", "toLowerCase", "defaultMessage", "Checkbox", "disabled", "onCheckedChange", "value", "name", "aria-label", "checked", "ContentTypes", "isFormDisabled", "kind", "layout", "actions", "subjects", "sortedSubjects", "sort", "a", "b", "label", "localeCompare", "_jsxs", "Box", "background", "_jsx", "GlobalActions", "ContentTypeCollapses", "pathToData", "PluginsAndSettingsPermissions", "layout", "restProps", "_jsx", "Box", "padding", "background", "Accordion", "Root", "size", "map", "category", "categoryId", "childrenForm", "index", "Row", "variant", "name", "pathToData", "kind", "isFormDisabled", "formatMessage", "useIntl", "categoryName", "split", "pop", "categoryDisplayName", "capitalise", "replace", "_jsxs", "<PERSON><PERSON>", "value", "Header", "<PERSON><PERSON>", "caretPosition", "description", "id", "defaultMessage", "Content", "actions", "subCategoryName", "subCategoryId", "SubCategory", "modifiedData", "onChangeParentCheckbox", "onChangeSimpleCheckbox", "usePermissionsDataManager", "isConditionModalOpen", "setIsConditionModalOpen", "useState", "mainData", "get", "dataWithoutCondition", "useMemo", "Object", "keys", "reduce", "acc", "current", "removeConditionKeyFromData", "hasAllActionsSelected", "hasSomeActionsSelected", "getCheckboxState", "formattedActions", "action", "checkboxName", "checkboxValue", "conditionValue", "hasConditions", "createArrayOfValues", "some", "val", "isDisplayed", "join", "label", "displayName", "actionId", "pathToConditionsObject", "datum", "doesButtonHasCondition", "entries", "catName", "conditions", "_Fragment", "Flex", "justifyContent", "alignItems", "paddingRight", "Typography", "textColor", "Border", "flex", "paddingLeft", "Checkbox", "disabled", "onCheckedChange", "target", "checked", "paddingTop", "paddingBottom", "Grid", "gap", "style", "col", "direction", "CheckboxWrapper", "$disabled", "$hasConditions", "Modal", "open", "onOpenChange", "prev", "ConditionsButton", "ConditionsModal", "headerBreadCrumbs", "onClose", "styled", "theme", "colors", "neutral150", "div", "neutral100", "primary600", "TAB_LABELS", "labelId", "defaultMessage", "id", "Permissions", "forwardRef", "layout", "isFormDisabled", "permissions", "api", "initialData", "layouts", "modifiedData", "dispatch", "useReducer", "reducer", "initialState", "init", "formatMessage", "useIntl", "React", "useImperativeHandle", "getPermissions", "collectionTypesDiff", "difference", "collectionTypes", "singleTypesDiff", "singleTypes", "contentTypesDiff", "didUpdateConditions", "isEmpty", "Object", "values", "some", "permission", "permissionValue", "has", "permissionsToSend", "formatPermissionsForAPI", "resetForm", "type", "setFormAfterSubmit", "handleChangeCollectionTypeLeftActionRowCheckbox", "pathToCollectionType", "propertyName", "rowName", "value", "handleChangeCollectionTypeGlobalActionCheckbox", "collectionTypeKind", "actionId", "handleChangeConditions", "conditions", "handleChangeSimpleCheckbox", "useCallback", "target", "name", "keys", "handleChangeParentCheckbox", "_jsx", "PermissionsDataManagerProvider", "availableConditions", "onChangeConditions", "onChangeSimpleCheckbox", "onChangeParentCheckbox", "onChangeCollectionTypeLeftActionRowCheckbox", "onChangeCollectionTypeGlobalActionCheckbox", "_jsxs", "Tabs", "Root", "defaultValue", "List", "aria-label", "map", "tabLabel", "<PERSON><PERSON>", "Content", "ContentTypes", "kind", "PluginsAndSettingsPermissions", "plugins", "settings", "state", "action", "produce", "draftState", "pathToData", "get", "for<PERSON>ach", "collectionType", "collectionTypeActionData", "undefined", "updatedValues", "updateValues", "updatedConditions", "set", "nextModifiedDataState", "cloneDeep", "pathToModifiedDataCollectionType", "split", "objToUpdate", "objValue", "pathToDataToSet", "isObject", "updatedValue", "updateConditionsToFalse", "entries", "array", "stringPathToData", "conditionsToUpdate", "pathToValue", "oldValues", "sections", "formatLayout", "defaultForm", "createDefaultCTForm", "createDefaultForm"]}