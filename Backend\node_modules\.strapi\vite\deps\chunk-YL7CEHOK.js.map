{"version": 3, "sources": ["../../../fast-deep-equal/index.js", "../../../@react-dnd/invariant/src/index.ts", "../../../dnd-core/src/utils/js_utils.ts", "../../../dnd-core/src/actions/dragDrop/types.ts", "../../../dnd-core/src/actions/dragDrop/local/setClientOffset.ts", "../../../dnd-core/src/actions/dragDrop/beginDrag.ts", "../../../dnd-core/src/actions/dragDrop/drop.ts", "../../../dnd-core/src/actions/dragDrop/endDrag.ts", "../../../dnd-core/src/utils/matchesType.ts", "../../../dnd-core/src/actions/dragDrop/hover.ts", "../../../dnd-core/src/actions/dragDrop/publishDragSource.ts", "../../../dnd-core/src/actions/dragDrop/index.ts", "../../../dnd-core/src/classes/DragDropManagerImpl.ts", "../../../dnd-core/src/utils/coords.ts", "../../../dnd-core/src/utils/dirtiness.ts", "../../../dnd-core/src/classes/DragDropMonitorImpl.ts", "../../../@react-dnd/asap/src/makeRequestCall.ts", "../../../@react-dnd/asap/src/AsapQueue.ts", "../../../@react-dnd/asap/src/RawTask.ts", "../../../@react-dnd/asap/src/TaskFactory.ts", "../../../@react-dnd/asap/src/asap.ts", "../../../dnd-core/src/actions/registry.ts", "../../../dnd-core/src/contracts.ts", "../../../dnd-core/src/interfaces.ts", "../../../dnd-core/src/utils/getNextUniqueId.ts", "../../../dnd-core/src/classes/HandlerRegistryImpl.ts", "../../../dnd-core/src/utils/equality.ts", "../../../dnd-core/src/reducers/dirtyHandlerIds.ts", "../../../dnd-core/src/reducers/dragOffset.ts", "../../../dnd-core/src/reducers/dragOperation.ts", "../../../dnd-core/src/reducers/refCount.ts", "../../../dnd-core/src/reducers/stateId.ts", "../../../dnd-core/src/reducers/index.ts", "../../../dnd-core/src/createDragDropManager.ts", "../../../react-dnd/src/core/DndProvider.tsx", "../../../react-dnd/src/core/DndContext.ts", "../../../react-dnd/src/core/DragPreviewImage.ts", "../../../react-dnd/src/hooks/useCollector.ts", "../../../react-dnd/src/hooks/useIsomorphicLayoutEffect.ts", "../../../react-dnd/src/hooks/useMonitorOutput.ts", "../../../react-dnd/src/hooks/useCollectedProps.ts", "../../../react-dnd/src/hooks/useOptionalFactory.ts", "../../../react-dnd/src/hooks/useDrag/connectors.ts", "../../../react-dnd/src/hooks/useDrag/useDragSourceConnector.ts", "../../../react-dnd/src/internals/DragSourceMonitorImpl.ts", "../../../react-dnd/src/internals/DropTargetMonitorImpl.ts", "../../../react-dnd/src/internals/registration.ts", "../../../@react-dnd/shallowequal/src/index.ts", "../../../react-dnd/src/internals/isRef.ts", "../../../react-dnd/src/internals/wrapConnectorHooks.ts", "../../../react-dnd/src/internals/SourceConnector.ts", "../../../react-dnd/src/internals/TargetConnector.ts", "../../../react-dnd/src/hooks/useDragDropManager.ts", "../../../react-dnd/src/hooks/useDrag/useDragSourceMonitor.ts", "../../../react-dnd/src/hooks/useDrag/useDragSource.ts", "../../../react-dnd/src/hooks/useDrag/DragSourceImpl.ts", "../../../react-dnd/src/hooks/useDrag/useDragType.ts", "../../../react-dnd/src/hooks/useDrag/useRegisteredDragSource.ts", "../../../react-dnd/src/hooks/useDrag/useDrag.ts", "../../../react-dnd/src/hooks/useDragLayer.ts", "../../../react-dnd/src/hooks/useDrop/connectors.ts", "../../../react-dnd/src/hooks/useDrop/useDropTargetConnector.ts", "../../../react-dnd/src/hooks/useDrop/useDropTargetMonitor.ts", "../../../react-dnd/src/hooks/useDrop/useAccept.ts", "../../../react-dnd/src/hooks/useDrop/useDropTarget.ts", "../../../react-dnd/src/hooks/useDrop/DropTargetImpl.ts", "../../../react-dnd/src/hooks/useDrop/useRegisteredDropTarget.ts", "../../../react-dnd/src/hooks/useDrop/useDrop.ts"], "sourcesContent": ["'use strict';\n\n// do not edit .js files directly - edit src/index.jst\n\n\n\nmodule.exports = function equal(a, b) {\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n\n      if (!equal(a[key], b[key])) return false;\n    }\n\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a!==a && b!==b;\n};\n", "/**\n * Use invariant() to assert state which your program assumes to be true.\n *\n * Provide sprintf-style format (only %s is supported) and arguments\n * to provide information about what broke and what you were\n * expecting.\n *\n * The invariant message will be stripped in production, but the invariant\n * will remain to ensure logic does not differ in production.\n */\n\nexport function invariant(condition: any, format: string, ...args: any[]) {\n\tif (isProduction()) {\n\t\tif (format === undefined) {\n\t\t\tthrow new Error('invariant requires an error message argument')\n\t\t}\n\t}\n\n\tif (!condition) {\n\t\tlet error\n\t\tif (format === undefined) {\n\t\t\terror = new Error(\n\t\t\t\t'Minified exception occurred; use the non-minified dev environment ' +\n\t\t\t\t\t'for the full error message and additional helpful warnings.',\n\t\t\t)\n\t\t} else {\n\t\t\tlet argIndex = 0\n\t\t\terror = new Error(\n\t\t\t\tformat.replace(/%s/g, function () {\n\t\t\t\t\treturn args[argIndex++]\n\t\t\t\t}),\n\t\t\t)\n\t\t\terror.name = 'Invariant Violation'\n\t\t}\n\n\t\t;(error as any).framesToPop = 1 // we don't care about invariant's own frame\n\t\tthrow error\n\t}\n}\n\nfunction isProduction() {\n\treturn (\n\t\ttypeof process !== 'undefined' && process.env['NODE_ENV'] === 'production'\n\t)\n}\n", "// cheap lodash replacements\n\n/**\n * drop-in replacement for _.get\n * @param obj\n * @param path\n * @param defaultValue\n */\nexport function get<T>(obj: any, path: string, defaultValue: T): T {\n\treturn path\n\t\t.split('.')\n\t\t.reduce((a, c) => (a && a[c] ? a[c] : defaultValue || null), obj) as T\n}\n\n/**\n * drop-in replacement for _.without\n */\nexport function without<T>(items: T[], item: T): T[] {\n\treturn items.filter((i) => i !== item)\n}\n\n/**\n * drop-in replacement for _.isString\n * @param input\n */\nexport function isString(input: any): boolean {\n\treturn typeof input === 'string'\n}\n\n/**\n * drop-in replacement for _.isString\n * @param input\n */\nexport function isObject(input: any): boolean {\n\treturn typeof input === 'object'\n}\n\n/**\n * replacement for _.xor\n * @param itemsA\n * @param itemsB\n */\nexport function xor<T extends string | number>(itemsA: T[], itemsB: T[]): T[] {\n\tconst map = new Map<T, number>()\n\tconst insertItem = (item: T) => {\n\t\tmap.set(item, map.has(item) ? (map.get(item) as number) + 1 : 1)\n\t}\n\titemsA.forEach(insertItem)\n\titemsB.forEach(insertItem)\n\n\tconst result: T[] = []\n\tmap.forEach((count, key) => {\n\t\tif (count === 1) {\n\t\t\tresult.push(key)\n\t\t}\n\t})\n\treturn result\n}\n\n/**\n * replacement for _.intersection\n * @param itemsA\n * @param itemsB\n */\nexport function intersection<T>(itemsA: T[], itemsB: T[]): T[] {\n\treturn itemsA.filter((t) => itemsB.indexOf(t) > -1)\n}\n", "export const INIT_COORDS = 'dnd-core/INIT_COORDS'\nexport const BEGIN_DRAG = 'dnd-core/BEGIN_DRAG'\nexport const PUBLISH_DRAG_SOURCE = 'dnd-core/PUBLISH_DRAG_SOURCE'\nexport const HOVER = 'dnd-core/HOVER'\nexport const DROP = 'dnd-core/DROP'\nexport const END_DRAG = 'dnd-core/END_DRAG'\n", "import type { AnyAction } from 'redux'\n\nimport type { XYCoord } from '../../../interfaces.js'\nimport { INIT_COORDS } from '../types.js'\n\nexport function setClientOffset(\n\tclientOffset: XYCoord | null | undefined,\n\tsourceClientOffset?: XYCoord | null | undefined,\n): AnyAction {\n\treturn {\n\t\ttype: INIT_COORDS,\n\t\tpayload: {\n\t\t\tsourceClientOffset: sourceClientOffset || null,\n\t\t\tclientOffset: clientOffset || null,\n\t\t},\n\t}\n}\n", "import { invariant } from '@react-dnd/invariant'\n\nimport type {\n\tAction,\n\tBeginDragOptions,\n\tBeginDragPayload,\n\tDragDropManager,\n\tDragDropMonitor,\n\tHandlerRegistry,\n\tIdentifier,\n\tXYCoord,\n} from '../../interfaces.js'\nimport { isObject } from '../../utils/js_utils.js'\nimport { setClientOffset } from './local/setClientOffset.js'\nimport { BEGIN_DRAG, INIT_COORDS } from './types.js'\n\nconst ResetCoordinatesAction = {\n\ttype: INIT_COORDS,\n\tpayload: {\n\t\tclientOffset: null,\n\t\tsourceClientOffset: null,\n\t},\n}\n\nexport function createBeginDrag(manager: DragDropManager) {\n\treturn function beginDrag(\n\t\tsourceIds: Identifier[] = [],\n\t\toptions: BeginDragOptions = {\n\t\t\tpublishSource: true,\n\t\t},\n\t): Action<BeginDragPayload> | undefined {\n\t\tconst {\n\t\t\tpublishSource = true,\n\t\t\tclientOffset,\n\t\t\tgetSourceClientOffset,\n\t\t}: BeginDragOptions = options\n\t\tconst monitor = manager.getMonitor()\n\t\tconst registry = manager.getRegistry()\n\n\t\t// Initialize the coordinates using the client offset\n\t\tmanager.dispatch(setClientOffset(clientOffset))\n\n\t\tverifyInvariants(sourceIds, monitor, registry)\n\n\t\t// Get the draggable source\n\t\tconst sourceId = getDraggableSource(sourceIds, monitor)\n\t\tif (sourceId == null) {\n\t\t\tmanager.dispatch(ResetCoordinatesAction)\n\t\t\treturn\n\t\t}\n\n\t\t// Get the source client offset\n\t\tlet sourceClientOffset: XYCoord | null = null\n\t\tif (clientOffset) {\n\t\t\tif (!getSourceClientOffset) {\n\t\t\t\tthrow new Error('getSourceClientOffset must be defined')\n\t\t\t}\n\t\t\tverifyGetSourceClientOffsetIsFunction(getSourceClientOffset)\n\t\t\tsourceClientOffset = getSourceClientOffset(sourceId)\n\t\t}\n\n\t\t// Initialize the full coordinates\n\t\tmanager.dispatch(setClientOffset(clientOffset, sourceClientOffset))\n\n\t\tconst source = registry.getSource(sourceId)\n\t\tconst item = source.beginDrag(monitor, sourceId)\n\t\t// If source.beginDrag returns null, this is an indicator to cancel the drag\n\t\tif (item == null) {\n\t\t\treturn undefined\n\t\t}\n\t\tverifyItemIsObject(item)\n\t\tregistry.pinSource(sourceId)\n\n\t\tconst itemType = registry.getSourceType(sourceId)\n\t\treturn {\n\t\t\ttype: BEGIN_DRAG,\n\t\t\tpayload: {\n\t\t\t\titemType,\n\t\t\t\titem,\n\t\t\t\tsourceId,\n\t\t\t\tclientOffset: clientOffset || null,\n\t\t\t\tsourceClientOffset: sourceClientOffset || null,\n\t\t\t\tisSourcePublic: !!publishSource,\n\t\t\t},\n\t\t}\n\t}\n}\n\nfunction verifyInvariants(\n\tsourceIds: Identifier[],\n\tmonitor: DragDropMonitor,\n\tregistry: HandlerRegistry,\n) {\n\tinvariant(!monitor.isDragging(), 'Cannot call beginDrag while dragging.')\n\tsourceIds.forEach(function (sourceId) {\n\t\tinvariant(\n\t\t\tregistry.getSource(sourceId),\n\t\t\t'Expected sourceIds to be registered.',\n\t\t)\n\t})\n}\n\nfunction verifyGetSourceClientOffsetIsFunction(getSourceClientOffset: any) {\n\tinvariant(\n\t\ttypeof getSourceClientOffset === 'function',\n\t\t'When clientOffset is provided, getSourceClientOffset must be a function.',\n\t)\n}\n\nfunction verifyItemIsObject(item: any) {\n\tinvariant(isObject(item), 'Item must be an object.')\n}\n\nfunction getDraggableSource(sourceIds: Identifier[], monitor: DragDropMonitor) {\n\tlet sourceId = null\n\tfor (let i = sourceIds.length - 1; i >= 0; i--) {\n\t\tif (monitor.canDragSource(sourceIds[i])) {\n\t\t\tsourceId = sourceIds[i]\n\t\t\tbreak\n\t\t}\n\t}\n\treturn sourceId\n}\n", "import { invariant } from '@react-dnd/invariant'\n\nimport type {\n\tAction,\n\tDragDropManager,\n\tDragDropMonitor,\n\tDropPayload,\n\tHandlerRegistry,\n\tIdentifier,\n} from '../../interfaces.js'\nimport { isObject } from '../../utils/js_utils.js'\nimport { DROP } from './types.js'\n\nexport function createDrop(manager: DragDropManager) {\n\treturn function drop(options = {}): void {\n\t\tconst monitor = manager.getMonitor()\n\t\tconst registry = manager.getRegistry()\n\t\tverifyInvariants(monitor)\n\t\tconst targetIds = getDroppableTargets(monitor)\n\n\t\t// Multiple actions are dispatched here, which is why this doesn't return an action\n\t\ttargetIds.forEach((targetId, index) => {\n\t\t\tconst dropResult = determineDropResult(targetId, index, registry, monitor)\n\t\t\tconst action: Action<DropPayload> = {\n\t\t\t\ttype: DROP,\n\t\t\t\tpayload: {\n\t\t\t\t\tdropResult: {\n\t\t\t\t\t\t...options,\n\t\t\t\t\t\t...dropResult,\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t}\n\t\t\tmanager.dispatch(action)\n\t\t})\n\t}\n}\n\nfunction verifyInvariants(monitor: DragDropMonitor) {\n\tinvariant(monitor.isDragging(), 'Cannot call drop while not dragging.')\n\tinvariant(\n\t\t!monitor.didDrop(),\n\t\t'Cannot call drop twice during one drag operation.',\n\t)\n}\n\nfunction determineDropResult(\n\ttargetId: Identifier,\n\tindex: number,\n\tregistry: HandlerRegistry,\n\tmonitor: DragDropMonitor,\n) {\n\tconst target = registry.getTarget(targetId)\n\tlet dropResult = target ? target.drop(monitor, targetId) : undefined\n\tverifyDropResultType(dropResult)\n\tif (typeof dropResult === 'undefined') {\n\t\tdropResult = index === 0 ? {} : monitor.getDropResult()\n\t}\n\treturn dropResult\n}\n\nfunction verifyDropResultType(dropResult: any) {\n\tinvariant(\n\t\ttypeof dropResult === 'undefined' || isObject(dropResult),\n\t\t'Drop result must either be an object or undefined.',\n\t)\n}\n\nfunction getDroppableTargets(monitor: DragDropMonitor) {\n\tconst targetIds = monitor\n\t\t.getTargetIds()\n\t\t.filter(monitor.canDropOnTarget, monitor)\n\ttargetIds.reverse()\n\treturn targetIds\n}\n", "import { invariant } from '@react-dnd/invariant'\n\nimport type {\n\tDragDropManager,\n\tDragDropMonitor,\n\tSentinelAction,\n} from '../../interfaces.js'\nimport { END_DRAG } from './types.js'\n\nexport function createEndDrag(manager: DragDropManager) {\n\treturn function endDrag(): SentinelAction {\n\t\tconst monitor = manager.getMonitor()\n\t\tconst registry = manager.getRegistry()\n\t\tverifyIsDragging(monitor)\n\n\t\tconst sourceId = monitor.getSourceId()\n\t\tif (sourceId != null) {\n\t\t\tconst source = registry.getSource(sourceId, true)\n\t\t\tsource.endDrag(monitor, sourceId)\n\t\t\tregistry.unpinSource()\n\t\t}\n\t\treturn { type: END_DRAG }\n\t}\n}\n\nfunction verifyIsDragging(monitor: DragDropMonitor) {\n\tinvariant(monitor.isDragging(), 'Cannot call endDrag while not dragging.')\n}\n", "import type { Identifier } from '../interfaces.js'\n\nexport function matchesType(\n\ttargetType: Identifier | Identifier[] | null,\n\tdraggedItemType: Identifier | null,\n): boolean {\n\tif (draggedItemType === null) {\n\t\treturn targetType === null\n\t}\n\treturn Array.isArray(targetType)\n\t\t? (targetType as Identifier[]).some((t) => t === draggedItemType)\n\t\t: targetType === draggedItemType\n}\n", "import { invariant } from '@react-dnd/invariant'\n\nimport type {\n\tAction,\n\tDragDropManager,\n\tDragDropMonitor,\n\tHandlerRegistry,\n\tHoverOptions,\n\tHoverPayload,\n\tIdentifier,\n} from '../../interfaces.js'\nimport { matchesType } from '../../utils/matchesType.js'\nimport { HOVER } from './types.js'\n\nexport function createHover(manager: DragDropManager) {\n\treturn function hover(\n\t\ttargetIdsArg: string[],\n\t\t{ clientOffset }: HoverOptions = {},\n\t): Action<HoverPayload> {\n\t\tverifyTargetIdsIsArray(targetIdsArg)\n\t\tconst targetIds = targetIdsArg.slice(0)\n\t\tconst monitor = manager.getMonitor()\n\t\tconst registry = manager.getRegistry()\n\t\tconst draggedItemType = monitor.getItemType()\n\t\tremoveNonMatchingTargetIds(targetIds, registry, draggedItemType)\n\t\tcheckInvariants(targetIds, monitor, registry)\n\t\thoverAllTargets(targetIds, monitor, registry)\n\n\t\treturn {\n\t\t\ttype: HOVER,\n\t\t\tpayload: {\n\t\t\t\ttargetIds,\n\t\t\t\tclientOffset: clientOffset || null,\n\t\t\t},\n\t\t}\n\t}\n}\n\nfunction verifyTargetIdsIsArray(targetIdsArg: string[]) {\n\tinvariant(Array.isArray(targetIdsArg), 'Expected targetIds to be an array.')\n}\n\nfunction checkInvariants(\n\ttargetIds: string[],\n\tmonitor: DragDropMonitor,\n\tregistry: HandlerRegistry,\n) {\n\tinvariant(monitor.isDragging(), 'Cannot call hover while not dragging.')\n\tinvariant(!monitor.didDrop(), 'Cannot call hover after drop.')\n\tfor (let i = 0; i < targetIds.length; i++) {\n\t\tconst targetId = targetIds[i] as string\n\t\tinvariant(\n\t\t\ttargetIds.lastIndexOf(targetId) === i,\n\t\t\t'Expected targetIds to be unique in the passed array.',\n\t\t)\n\n\t\tconst target = registry.getTarget(targetId)\n\t\tinvariant(target, 'Expected targetIds to be registered.')\n\t}\n}\n\nfunction removeNonMatchingTargetIds(\n\ttargetIds: string[],\n\tregistry: HandlerRegistry,\n\tdraggedItemType: Identifier | null,\n) {\n\t// Remove those targetIds that don't match the targetType.  This\n\t// fixes shallow isOver which would only be non-shallow because of\n\t// non-matching targets.\n\tfor (let i = targetIds.length - 1; i >= 0; i--) {\n\t\tconst targetId = targetIds[i] as string\n\t\tconst targetType = registry.getTargetType(targetId)\n\t\tif (!matchesType(targetType, draggedItemType)) {\n\t\t\ttargetIds.splice(i, 1)\n\t\t}\n\t}\n}\n\nfunction hoverAllTargets(\n\ttargetIds: string[],\n\tmonitor: DragDropMonitor,\n\tregistry: HandlerRegistry,\n) {\n\t// Finally call hover on all matching targets.\n\ttargetIds.forEach(function (targetId) {\n\t\tconst target = registry.getTarget(targetId)\n\t\ttarget.hover(monitor, targetId)\n\t})\n}\n", "import type { Drag<PERSON>rop<PERSON>ana<PERSON>, SentinelAction } from '../../interfaces.js'\nimport { PUBLISH_DRAG_SOURCE } from './types.js'\n\nexport function createPublishDragSource(manager: DragDropManager) {\n\treturn function publishDragSource(): SentinelAction | undefined {\n\t\tconst monitor = manager.getMonitor()\n\t\tif (monitor.isDragging()) {\n\t\t\treturn { type: PUBLISH_DRAG_SOURCE }\n\t\t}\n\t\treturn\n\t}\n}\n", "import type { DragDropActions, DragDropManager } from '../../interfaces.js'\nimport { createBeginDrag } from './beginDrag.js'\nimport { createDrop } from './drop.js'\nimport { createEndDrag } from './endDrag.js'\nimport { createHover } from './hover.js'\nimport { createPublishDragSource } from './publishDragSource.js'\n\nexport * from './types.js'\n\nexport function createDragDropActions(\n\tmanager: DragDropManager,\n): DragDropActions {\n\treturn {\n\t\tbeginDrag: createBeginDrag(manager),\n\t\tpublishDragSource: createPublishDragSource(manager),\n\t\thover: createHover(manager),\n\t\tdrop: createDrop(manager),\n\t\tendDrag: createEndDrag(manager),\n\t}\n}\n", "import type { Action, Store } from 'redux'\n\nimport { createDragDropActions } from '../actions/dragDrop/index.js'\nimport type {\n\tActionCreator,\n\tBackend,\n\tDragDropActions,\n\tDragDropManager,\n\tDragDropMonitor,\n\tHandlerRegistry,\n} from '../interfaces.js'\nimport type { State } from '../reducers/index.js'\nimport type { DragDropMonitorImpl } from './DragDropMonitorImpl.js'\n\nexport class DragDropManagerImpl implements DragDropManager {\n\tprivate store: Store<State>\n\tprivate monitor: DragDropMonitor\n\tprivate backend: Backend | undefined\n\tprivate isSetUp = false\n\n\tpublic constructor(store: Store<State>, monitor: DragDropMonitor) {\n\t\tthis.store = store\n\t\tthis.monitor = monitor\n\t\tstore.subscribe(this.handleRefCountChange)\n\t}\n\n\tpublic receiveBackend(backend: Backend): void {\n\t\tthis.backend = backend\n\t}\n\n\tpublic getMonitor(): DragDropMonitor {\n\t\treturn this.monitor\n\t}\n\n\tpublic getBackend(): Backend {\n\t\treturn this.backend as Backend\n\t}\n\n\tpublic getRegistry(): HandlerRegistry {\n\t\treturn (this.monitor as DragDropMonitorImpl).registry\n\t}\n\n\tpublic getActions(): DragDropActions {\n\t\t/* eslint-disable-next-line @typescript-eslint/no-this-alias */\n\t\tconst manager = this\n\t\tconst { dispatch } = this.store\n\n\t\tfunction bindActionCreator(actionCreator: ActionCreator<any>) {\n\t\t\treturn (...args: any[]) => {\n\t\t\t\tconst action = actionCreator.apply(manager, args as any)\n\t\t\t\tif (typeof action !== 'undefined') {\n\t\t\t\t\tdispatch(action)\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tconst actions = createDragDropActions(this)\n\n\t\treturn Object.keys(actions).reduce(\n\t\t\t(boundActions: DragDropActions, key: string) => {\n\t\t\t\tconst action: ActionCreator<any> = (actions as any)[\n\t\t\t\t\tkey\n\t\t\t\t] as ActionCreator<any>\n\t\t\t\t;(boundActions as any)[key] = bindActionCreator(action)\n\t\t\t\treturn boundActions\n\t\t\t},\n\t\t\t{} as DragDropActions,\n\t\t)\n\t}\n\n\tpublic dispatch(action: Action<any>): void {\n\t\tthis.store.dispatch(action)\n\t}\n\n\tprivate handleRefCountChange = (): void => {\n\t\tconst shouldSetUp = this.store.getState().refCount > 0\n\t\tif (this.backend) {\n\t\t\tif (shouldSetUp && !this.isSetUp) {\n\t\t\t\tthis.backend.setup()\n\t\t\t\tthis.isSetUp = true\n\t\t\t} else if (!shouldSetUp && this.isSetUp) {\n\t\t\t\tthis.backend.teardown()\n\t\t\t\tthis.isSetUp = false\n\t\t\t}\n\t\t}\n\t}\n}\n", "import type { XYCoord } from '../interfaces.js'\nimport type { State } from '../reducers/dragOffset.js'\n\n/**\n * Coordinate addition\n * @param a The first coordinate\n * @param b The second coordinate\n */\nexport function add(a: XYCoord, b: XYCoord): XYCoord {\n\treturn {\n\t\tx: a.x + b.x,\n\t\ty: a.y + b.y,\n\t}\n}\n\n/**\n * Coordinate subtraction\n * @param a The first coordinate\n * @param b The second coordinate\n */\nexport function subtract(a: XYCoord, b: XYCoord): XYCoord {\n\treturn {\n\t\tx: a.x - b.x,\n\t\ty: a.y - b.y,\n\t}\n}\n\n/**\n * Returns the cartesian distance of the drag source component's position, based on its position\n * at the time when the current drag operation has started, and the movement difference.\n *\n * Returns null if no item is being dragged.\n *\n * @param state The offset state to compute from\n */\nexport function getSourceClientOffset(state: State): XYCoord | null {\n\tconst { clientOffset, initialClientOffset, initialSourceClientOffset } = state\n\tif (!clientOffset || !initialClientOffset || !initialSourceClientOffset) {\n\t\treturn null\n\t}\n\treturn subtract(\n\t\tadd(clientOffset, initialSourceClientOffset),\n\t\tinitialClientOffset,\n\t)\n}\n\n/**\n * Determines the x,y offset between the client offset and the initial client offset\n *\n * @param state The offset state to compute from\n */\nexport function getDifferenceFromInitialOffset(state: State): XYCoord | null {\n\tconst { clientOffset, initialClientOffset } = state\n\tif (!clientOffset || !initialClientOffset) {\n\t\treturn null\n\t}\n\treturn subtract(clientOffset, initialClientOffset)\n}\n", "import { intersection } from './js_utils.js'\n\nexport const NONE: string[] = []\nexport const ALL: string[] = []\n// Add these flags for debug\n;(NONE as any).__IS_NONE__ = true\n;(ALL as any).__IS_ALL__ = true\n\n/**\n * Determines if the given handler IDs are dirty or not.\n *\n * @param dirtyIds The set of dirty handler ids\n * @param handlerIds The set of handler ids to check\n */\nexport function areDirty(\n\tdirtyIds: string[],\n\thandlerIds: string[] | undefined,\n): boolean {\n\tif (dirtyIds === NONE) {\n\t\treturn false\n\t}\n\n\tif (dirtyIds === ALL || typeof handlerIds === 'undefined') {\n\t\treturn true\n\t}\n\n\tconst commonIds = intersection(handlerIds, dirtyIds)\n\treturn commonIds.length > 0\n}\n", "import { invariant } from '@react-dnd/invariant'\nimport type { Store } from 'redux'\n\nimport type {\n\tDragDropMonitor,\n\tHandlerRegistry,\n\tIdentifier,\n\tListener,\n\tUnsubscribe,\n\tXYCoord,\n} from '../interfaces.js'\nimport type { State } from '../reducers/index.js'\nimport {\n\tgetDifferenceFromInitialOffset,\n\tgetSourceClientOffset,\n} from '../utils/coords.js'\nimport { areDirty } from '../utils/dirtiness.js'\nimport { matchesType } from '../utils/matchesType.js'\n\nexport class DragDropMonitorImpl implements DragDropMonitor {\n\tprivate store: Store<State>\n\tpublic readonly registry: HandlerRegistry\n\n\tpublic constructor(store: Store<State>, registry: HandlerRegistry) {\n\t\tthis.store = store\n\t\tthis.registry = registry\n\t}\n\n\tpublic subscribeToStateChange(\n\t\tlistener: Listener,\n\t\toptions: { handlerIds?: string[] } = {},\n\t): Unsubscribe {\n\t\tconst { handlerIds } = options\n\t\tinvariant(typeof listener === 'function', 'listener must be a function.')\n\t\tinvariant(\n\t\t\ttypeof handlerIds === 'undefined' || Array.isArray(handlerIds),\n\t\t\t'handlerIds, when specified, must be an array of strings.',\n\t\t)\n\n\t\tlet prevStateId = this.store.getState().stateId\n\t\tconst handleChange = () => {\n\t\t\tconst state = this.store.getState()\n\t\t\tconst currentStateId = state.stateId\n\t\t\ttry {\n\t\t\t\tconst canSkipListener =\n\t\t\t\t\tcurrentStateId === prevStateId ||\n\t\t\t\t\t(currentStateId === prevStateId + 1 &&\n\t\t\t\t\t\t!areDirty(state.dirtyHandlerIds, handlerIds))\n\n\t\t\t\tif (!canSkipListener) {\n\t\t\t\t\tlistener()\n\t\t\t\t}\n\t\t\t} finally {\n\t\t\t\tprevStateId = currentStateId\n\t\t\t}\n\t\t}\n\n\t\treturn this.store.subscribe(handleChange)\n\t}\n\n\tpublic subscribeToOffsetChange(listener: Listener): Unsubscribe {\n\t\tinvariant(typeof listener === 'function', 'listener must be a function.')\n\n\t\tlet previousState = this.store.getState().dragOffset\n\t\tconst handleChange = () => {\n\t\t\tconst nextState = this.store.getState().dragOffset\n\t\t\tif (nextState === previousState) {\n\t\t\t\treturn\n\t\t\t}\n\n\t\t\tpreviousState = nextState\n\t\t\tlistener()\n\t\t}\n\n\t\treturn this.store.subscribe(handleChange)\n\t}\n\n\tpublic canDragSource(sourceId: string | undefined): boolean {\n\t\tif (!sourceId) {\n\t\t\treturn false\n\t\t}\n\t\tconst source = this.registry.getSource(sourceId)\n\t\tinvariant(source, `Expected to find a valid source. sourceId=${sourceId}`)\n\n\t\tif (this.isDragging()) {\n\t\t\treturn false\n\t\t}\n\n\t\treturn source.canDrag(this, sourceId)\n\t}\n\n\tpublic canDropOnTarget(targetId: string | undefined): boolean {\n\t\t// undefined on initial render\n\t\tif (!targetId) {\n\t\t\treturn false\n\t\t}\n\t\tconst target = this.registry.getTarget(targetId)\n\t\tinvariant(target, `Expected to find a valid target. targetId=${targetId}`)\n\n\t\tif (!this.isDragging() || this.didDrop()) {\n\t\t\treturn false\n\t\t}\n\n\t\tconst targetType = this.registry.getTargetType(targetId)\n\t\tconst draggedItemType = this.getItemType()\n\t\treturn (\n\t\t\tmatchesType(targetType, draggedItemType) && target.canDrop(this, targetId)\n\t\t)\n\t}\n\n\tpublic isDragging(): boolean {\n\t\treturn Boolean(this.getItemType())\n\t}\n\n\tpublic isDraggingSource(sourceId: string | undefined): boolean {\n\t\t// undefined on initial render\n\t\tif (!sourceId) {\n\t\t\treturn false\n\t\t}\n\t\tconst source = this.registry.getSource(sourceId, true)\n\t\tinvariant(source, `Expected to find a valid source. sourceId=${sourceId}`)\n\n\t\tif (!this.isDragging() || !this.isSourcePublic()) {\n\t\t\treturn false\n\t\t}\n\n\t\tconst sourceType = this.registry.getSourceType(sourceId)\n\t\tconst draggedItemType = this.getItemType()\n\t\tif (sourceType !== draggedItemType) {\n\t\t\treturn false\n\t\t}\n\n\t\treturn source.isDragging(this, sourceId)\n\t}\n\n\tpublic isOverTarget(\n\t\ttargetId: string | undefined,\n\t\toptions = { shallow: false },\n\t): boolean {\n\t\t// undefined on initial render\n\t\tif (!targetId) {\n\t\t\treturn false\n\t\t}\n\n\t\tconst { shallow } = options\n\t\tif (!this.isDragging()) {\n\t\t\treturn false\n\t\t}\n\n\t\tconst targetType = this.registry.getTargetType(targetId)\n\t\tconst draggedItemType = this.getItemType()\n\t\tif (draggedItemType && !matchesType(targetType, draggedItemType)) {\n\t\t\treturn false\n\t\t}\n\n\t\tconst targetIds = this.getTargetIds()\n\t\tif (!targetIds.length) {\n\t\t\treturn false\n\t\t}\n\n\t\tconst index = targetIds.indexOf(targetId)\n\t\tif (shallow) {\n\t\t\treturn index === targetIds.length - 1\n\t\t} else {\n\t\t\treturn index > -1\n\t\t}\n\t}\n\n\tpublic getItemType(): Identifier {\n\t\treturn this.store.getState().dragOperation.itemType as Identifier\n\t}\n\n\tpublic getItem(): any {\n\t\treturn this.store.getState().dragOperation.item\n\t}\n\n\tpublic getSourceId(): string | null {\n\t\treturn this.store.getState().dragOperation.sourceId\n\t}\n\n\tpublic getTargetIds(): string[] {\n\t\treturn this.store.getState().dragOperation.targetIds\n\t}\n\n\tpublic getDropResult(): any {\n\t\treturn this.store.getState().dragOperation.dropResult\n\t}\n\n\tpublic didDrop(): boolean {\n\t\treturn this.store.getState().dragOperation.didDrop\n\t}\n\n\tpublic isSourcePublic(): boolean {\n\t\treturn Boolean(this.store.getState().dragOperation.isSourcePublic)\n\t}\n\n\tpublic getInitialClientOffset(): XYCoord | null {\n\t\treturn this.store.getState().dragOffset.initialClientOffset\n\t}\n\n\tpublic getInitialSourceClientOffset(): XYCoord | null {\n\t\treturn this.store.getState().dragOffset.initialSourceClientOffset\n\t}\n\n\tpublic getClientOffset(): XYCoord | null {\n\t\treturn this.store.getState().dragOffset.clientOffset\n\t}\n\n\tpublic getSourceClientOffset(): XYCoord | null {\n\t\treturn getSourceClientOffset(this.store.getState().dragOffset)\n\t}\n\n\tpublic getDifferenceFromInitialOffset(): XYCoord | null {\n\t\treturn getDifferenceFromInitialOffset(this.store.getState().dragOffset)\n\t}\n}\n", "// Safari 6 and 6.1 for desktop, iPad, and iPhone are the only browsers that\n// have WebKitMutationObserver but not un-prefixed MutationObserver.\n// Must use `global` or `self` instead of `window` to work in both frames and web\n// workers. `global` is a provision of <PERSON><PERSON><PERSON><PERSON>, Mr, Mrs, or Mo<PERSON>.\n\n/* globals self */\nconst scope = typeof global !== 'undefined' ? global : self\nconst BrowserMutationObserver =\n\t(scope as any).MutationObserver || (scope as any).WebKitMutationObserver\n\nexport function makeRequestCallFromTimer(callback: () => void) {\n\treturn function requestCall() {\n\t\t// We dispatch a timeout with a specified delay of 0 for engines that\n\t\t// can reliably accommodate that request. This will usually be snapped\n\t\t// to a 4 milisecond delay, but once we're flushing, there's no delay\n\t\t// between events.\n\t\tconst timeoutHandle = setTimeout(handleTimer, 0)\n\t\t// However, since this timer gets frequently dropped in Firefox\n\t\t// workers, we enlist an interval handle that will try to fire\n\t\t// an event 20 times per second until it succeeds.\n\t\tconst intervalHandle = setInterval(handleTimer, 50)\n\n\t\tfunction handleTimer() {\n\t\t\t// Whichever timer succeeds will cancel both timers and\n\t\t\t// execute the callback.\n\t\t\tclearTimeout(timeoutHandle)\n\t\t\tclearInterval(intervalHandle)\n\t\t\tcallback()\n\t\t}\n\t}\n}\n\n// To request a high priority event, we induce a mutation observer by toggling\n// the text of a text node between \"1\" and \"-1\".\nexport function makeRequestCallFromMutationObserver(callback: () => void) {\n\tlet toggle = 1\n\tconst observer = new BrowserMutationObserver(callback)\n\tconst node = document.createTextNode('')\n\tobserver.observe(node, { characterData: true })\n\treturn function requestCall() {\n\t\ttoggle = -toggle\n\t\t;(node as any).data = toggle\n\t}\n}\n\nexport const makeRequestCall =\n\ttypeof BrowserMutationObserver === 'function'\n\t\t? // MutationObservers are desirable because they have high priority and work\n\t\t  // reliably everywhere they are implemented.\n\t\t  // They are implemented in all modern browsers.\n\t\t  //\n\t\t  // - Android 4-4.3\n\t\t  // - Chrome 26-34\n\t\t  // - Firefox 14-29\n\t\t  // - Internet Explorer 11\n\t\t  // - iPad Safari 6-7.1\n\t\t  // - iPhone Safari 7-7.1\n\t\t  // - Safari 6-7\n\t\t  makeRequestCallFromMutationObserver\n\t\t: // MessageChannels are desirable because they give direct access to the HTML\n\t\t  // task queue, are implemented in Internet Explorer 10, Safari 5.0-1, and Opera\n\t\t  // 11-12, and in web workers in many engines.\n\t\t  // Although message channels yield to any queued rendering and IO tasks, they\n\t\t  // would be better than imposing the 4ms delay of timers.\n\t\t  // However, they do not work reliably in Internet Explorer or Safari.\n\n\t\t  // Internet Explorer 10 is the only browser that has setImmediate but does\n\t\t  // not have MutationObservers.\n\t\t  // Although setImmediate yields to the browser's renderer, it would be\n\t\t  // preferrable to falling back to setTimeout since it does not have\n\t\t  // the minimum 4ms penalty.\n\t\t  // Unfortunately there appears to be a bug in Internet Explorer 10 Mobile (and\n\t\t  // Desktop to a lesser extent) that renders both setImmediate and\n\t\t  // MessageChannel useless for the purposes of ASAP.\n\t\t  // https://github.com/kriskowal/q/issues/396\n\n\t\t  // Timers are implemented universally.\n\t\t  // We fall back to timers in workers in most engines, and in foreground\n\t\t  // contexts in the following browsers.\n\t\t  // However, note that even this simple case requires nuances to operate in a\n\t\t  // broad spectrum of browsers.\n\t\t  //\n\t\t  // - Firefox 3-13\n\t\t  // - Internet Explorer 6-9\n\t\t  // - iPad Safari 4.3\n\t\t  // - Lynx 2.8.7\n\t\t  makeRequestCallFromTimer\n", "/* eslint-disable no-restricted-globals, @typescript-eslint/ban-ts-comment, @typescript-eslint/no-unused-vars, @typescript-eslint/no-non-null-assertion */\nimport { makeRequestCall, makeRequestCallFromTimer } from './makeRequestCall.js'\nimport type { Task } from './types.js'\n\nexport class AsapQueue {\n\tprivate queue: Task[] = []\n\t// We queue errors to ensure they are thrown in right order (FIFO).\n\t// Array-as-queue is good enough here, since we are just dealing with exceptions.\n\tprivate pendingErrors: any[] = []\n\t// Once a flush has been requested, no further calls to `requestFlush` are\n\t// necessary until the next `flush` completes.\n\t// @ts-ignore\n\tprivate flushing = false\n\t// `requestFlush` is an implementation-specific method that attempts to kick\n\t// off a `flush` event as quickly as possible. `flush` will attempt to exhaust\n\t// the event queue before yielding to the browser's own event loop.\n\tprivate requestFlush: () => void\n\n\tprivate requestErrorThrow: () => void\n\t// The position of the next task to execute in the task queue. This is\n\t// preserved between calls to `flush` so that it can be resumed if\n\t// a task throws an exception.\n\tprivate index = 0\n\t// If a task schedules additional tasks recursively, the task queue can grow\n\t// unbounded. To prevent memory exhaustion, the task queue will periodically\n\t// truncate already-completed tasks.\n\tprivate capacity = 1024\n\n\tpublic constructor() {\n\t\t// `requestFlush` requests that the high priority event queue be flushed as\n\t\t// soon as possible.\n\t\t// This is useful to prevent an error thrown in a task from stalling the event\n\t\t// queue if the exception handled by Node.js’s\n\t\t// `process.on(\"uncaughtException\")` or by a domain.\n\n\t\t// `requestFlush` is implemented using a strategy based on data collected from\n\t\t// every available SauceLabs Selenium web driver worker at time of writing.\n\t\t// https://docs.google.com/spreadsheets/d/1mG-5UYGup5qxGdEMWkhP6BWCz053NUb2E1QoUTU16uA/edit#gid=783724593\n\t\tthis.requestFlush = makeRequestCall(this.flush)\n\t\tthis.requestErrorThrow = makeRequestCallFromTimer(() => {\n\t\t\t// Throw first error\n\t\t\tif (this.pendingErrors.length) {\n\t\t\t\tthrow this.pendingErrors.shift()\n\t\t\t}\n\t\t})\n\t}\n\n\t// Use the fastest means possible to execute a task in its own turn, with\n\t// priority over other events including IO, animation, reflow, and redraw\n\t// events in browsers.\n\t//\n\t// An exception thrown by a task will permanently interrupt the processing of\n\t// subsequent tasks. The higher level `asap` function ensures that if an\n\t// exception is thrown by a task, that the task queue will continue flushing as\n\t// soon as possible, but if you use `rawAsap` directly, you are responsible to\n\t// either ensure that no exceptions are thrown from your task, or to manually\n\t// call `rawAsap.requestFlush` if an exception is thrown.\n\tpublic enqueueTask(task: Task): void {\n\t\tconst { queue: q, requestFlush } = this\n\t\tif (!q.length) {\n\t\t\trequestFlush()\n\t\t\tthis.flushing = true\n\t\t}\n\t\t// Equivalent to push, but avoids a function call.\n\t\tq[q.length] = task\n\t}\n\n\t// The flush function processes all tasks that have been scheduled with\n\t// `rawAsap` unless and until one of those tasks throws an exception.\n\t// If a task throws an exception, `flush` ensures that its state will remain\n\t// consistent and will resume where it left off when called again.\n\t// However, `flush` does not make any arrangements to be called again if an\n\t// exception is thrown.\n\tprivate flush = () => {\n\t\tconst { queue: q } = this\n\t\twhile (this.index < q.length) {\n\t\t\tconst currentIndex = this.index\n\t\t\t// Advance the index before calling the task. This ensures that we will\n\t\t\t// begin flushing on the next task the task throws an error.\n\t\t\tthis.index++\n\t\t\tq[currentIndex]!.call()\n\t\t\t// Prevent leaking memory for long chains of recursive calls to `asap`.\n\t\t\t// If we call `asap` within tasks scheduled by `asap`, the queue will\n\t\t\t// grow, but to avoid an O(n) walk for every task we execute, we don't\n\t\t\t// shift tasks off the queue after they have been executed.\n\t\t\t// Instead, we periodically shift 1024 tasks off the queue.\n\t\t\tif (this.index > this.capacity) {\n\t\t\t\t// Manually shift all values starting at the index back to the\n\t\t\t\t// beginning of the queue.\n\t\t\t\tfor (\n\t\t\t\t\tlet scan = 0, newLength = q.length - this.index;\n\t\t\t\t\tscan < newLength;\n\t\t\t\t\tscan++\n\t\t\t\t) {\n\t\t\t\t\tq[scan] = q[scan + this.index]!\n\t\t\t\t}\n\t\t\t\tq.length -= this.index\n\t\t\t\tthis.index = 0\n\t\t\t}\n\t\t}\n\t\tq.length = 0\n\t\tthis.index = 0\n\t\tthis.flushing = false\n\t}\n\n\t// In a web browser, exceptions are not fatal. However, to avoid\n\t// slowing down the queue of pending tasks, we rethrow the error in a\n\t// lower priority turn.\n\tpublic registerPendingError = (err: any) => {\n\t\tthis.pendingErrors.push(err)\n\t\tthis.requestErrorThrow()\n\t}\n}\n\n// The message channel technique was discovered by Malte Ubl and was the\n// original foundation for this library.\n// http://www.nonblocking.io/2011/06/windownexttick.html\n\n// Safari 6.0.5 (at least) intermittently fails to create message ports on a\n// page's first load. Thankfully, this version of Safari supports\n// MutationObservers, so we don't need to fall back in that case.\n\n// function makeRequestCallFromMessageChannel(callback) {\n//     var channel = new MessageChannel();\n//     channel.port1.onmessage = callback;\n//     return function requestCall() {\n//         channel.port2.postMessage(0);\n//     };\n// }\n\n// For reasons explained above, we are also unable to use `setImmediate`\n// under any circumstances.\n// Even if we were, there is another bug in Internet Explorer 10.\n// It is not sufficient to assign `setImmediate` to `requestFlush` because\n// `setImmediate` must be called *by name* and therefore must be wrapped in a\n// closure.\n// Never forget.\n\n// function makeRequestCallFromSetImmediate(callback) {\n//     return function requestCall() {\n//         setImmediate(callback);\n//     };\n// }\n\n// Safari 6.0 has a problem where timers will get lost while the user is\n// scrolling. This problem does not impact ASAP because Safari 6.0 supports\n// mutation observers, so that implementation is used instead.\n// However, if we ever elect to use timers in Safari, the prevalent work-around\n// is to add a scroll event listener that calls for a flush.\n\n// `setTimeout` does not call the passed callback if the delay is less than\n// approximately 7 in web workers in Firefox 8 through 18, and sometimes not\n// even then.\n\n// This is for `asap.js` only.\n// Its name will be periodically randomized to break any code that depends on\n// // its existence.\n// rawAsap.makeRequestCallFromTimer = makeRequestCallFromTimer\n\n// ASAP was originally a nextTick shim included in Q. This was factored out\n// into this ASAP package. It was later adapted to RSVP which made further\n// amendments. These decisions, particularly to marginalize MessageChannel and\n// to capture the MutationObserver implementation in a closure, were integrated\n// back into ASAP proper.\n// https://github.com/tildeio/rsvp.js/blob/cddf7232546a9cf858524b75cde6f9edf72620a7/lib/rsvp/asap.js\n", "// We wrap tasks with recyclable task objects.  A task object implements\n\nimport type { Task, TaskFn } from 'types'\n\n// `call`, just like a function.\nexport class RawTask implements Task {\n\tpublic task: TaskFn | null = null\n\n\tpublic constructor(\n\t\tprivate onError: (err: any) => void,\n\t\tprivate release: (t: RawTask) => void,\n\t) {}\n\n\tpublic call() {\n\t\ttry {\n\t\t\tthis.task && this.task()\n\t\t} catch (error) {\n\t\t\tthis.onError(error)\n\t\t} finally {\n\t\t\tthis.task = null\n\t\t\tthis.release(this)\n\t\t}\n\t}\n}\n", "import { RawTask } from './RawTask.js'\nimport type { Task } from './types.js'\n\nexport class TaskFactory {\n\tprivate freeTasks: RawTask[] = []\n\n\tpublic constructor(private onError: (err: any) => void) {}\n\n\tpublic create(task: () => void): Task {\n\t\tconst tasks = this.freeTasks\n\t\tconst t = tasks.length\n\t\t\t? (tasks.pop() as RawTask)\n\t\t\t: new RawTask(this.onError, (t) => (tasks[tasks.length] = t))\n\t\tt.task = task\n\t\treturn t\n\t}\n}\n", "import { AsapQueue } from './AsapQueue.js'\nimport { TaskFactory } from './TaskFactory.js'\nimport type { TaskFn } from './types.js'\n\nconst asapQueue = new AsapQueue()\nconst taskFactory = new TaskFactory(asapQueue.registerPendingError)\n\n/**\n * Calls a task as soon as possible after returning, in its own event, with priority\n * over other events like animation, reflow, and repaint. An error thrown from an\n * event will not interrupt, nor even substantially slow down the processing of\n * other events, but will be rather postponed to a lower priority event.\n * @param {{call}} task A callable object, typically a function that takes no\n * arguments.\n */\nexport function asap(task: TaskFn) {\n\tasapQueue.enqueueTask(taskFactory.create(task))\n}\n", "import type { Action, SourceIdPayload, TargetIdPayload } from '../interfaces.js'\n\nexport const ADD_SOURCE = 'dnd-core/ADD_SOURCE'\nexport const ADD_TARGET = 'dnd-core/ADD_TARGET'\nexport const REMOVE_SOURCE = 'dnd-core/REMOVE_SOURCE'\nexport const REMOVE_TARGET = 'dnd-core/REMOVE_TARGET'\n\nexport function addSource(sourceId: string): Action<SourceIdPayload> {\n\treturn {\n\t\ttype: ADD_SOURCE,\n\t\tpayload: {\n\t\t\tsourceId,\n\t\t},\n\t}\n}\n\nexport function addTarget(targetId: string): Action<TargetIdPayload> {\n\treturn {\n\t\ttype: ADD_TARGET,\n\t\tpayload: {\n\t\t\ttargetId,\n\t\t},\n\t}\n}\n\nexport function removeSource(sourceId: string): Action<SourceIdPayload> {\n\treturn {\n\t\ttype: REMOVE_SOURCE,\n\t\tpayload: {\n\t\t\tsourceId,\n\t\t},\n\t}\n}\n\nexport function removeTarget(targetId: string): Action<TargetIdPayload> {\n\treturn {\n\t\ttype: REMOVE_TARGET,\n\t\tpayload: {\n\t\t\ttargetId,\n\t\t},\n\t}\n}\n", "import { invariant } from '@react-dnd/invariant'\n\nimport type { DragSource, DropTarget, Identifier } from './interfaces.js'\n\nexport function validateSourceContract(source: DragSource): void {\n\tinvariant(\n\t\ttypeof source.canDrag === 'function',\n\t\t'Expected canDrag to be a function.',\n\t)\n\tinvariant(\n\t\ttypeof source.beginDrag === 'function',\n\t\t'Expected beginDrag to be a function.',\n\t)\n\tinvariant(\n\t\ttypeof source.endDrag === 'function',\n\t\t'Expected endDrag to be a function.',\n\t)\n}\n\nexport function validateTargetContract(target: DropTarget): void {\n\tinvariant(\n\t\ttypeof target.canDrop === 'function',\n\t\t'Expected canDrop to be a function.',\n\t)\n\tinvariant(\n\t\ttypeof target.hover === 'function',\n\t\t'Expected hover to be a function.',\n\t)\n\tinvariant(\n\t\ttypeof target.drop === 'function',\n\t\t'Expected beginDrag to be a function.',\n\t)\n}\n\nexport function validateType(\n\ttype: Identifier | Identifier[],\n\tallowArray?: boolean,\n): void {\n\tif (allowArray && Array.isArray(type)) {\n\t\ttype.forEach((t) => validateType(t, false))\n\t\treturn\n\t}\n\n\tinvariant(\n\t\ttypeof type === 'string' || typeof type === 'symbol',\n\t\tallowArray\n\t\t\t? 'Type can only be a string, a symbol, or an array of either.'\n\t\t\t: 'Type can only be a string or a symbol.',\n\t)\n}\n", "export type Identifier = string | symbol\nexport type SourceType = Identifier\nexport type TargetType = Identifier | Identifier[]\nexport type Unsubscribe = () => void\nexport type Listener = () => void\n\nexport interface XYCoord {\n\tx: number\n\ty: number\n}\n\nexport enum HandlerRole {\n\tSOURCE = 'SOURCE',\n\tTARGET = 'TARGET',\n}\n\nexport interface Backend {\n\tsetup(): void\n\tteardown(): void\n\tconnectDragSource(sourceId: any, node?: any, options?: any): Unsubscribe\n\tconnectDragPreview(sourceId: any, node?: any, options?: any): Unsubscribe\n\tconnectDropTarget(targetId: any, node?: any, options?: any): Unsubscribe\n\tprofile(): Record<string, number>\n}\n\nexport interface DragDropMonitor {\n\tsubscribeToStateChange(\n\t\tlistener: Listener,\n\t\toptions?: {\n\t\t\thandlerIds?: Identifier[]\n\t\t},\n\t): Unsubscribe\n\tsubscribeToOffsetChange(listener: Listener): Unsubscribe\n\tcanDragSource(sourceId: Identifier | undefined): boolean\n\tcanDropOnTarget(targetId: Identifier | undefined): boolean\n\n\t/**\n\t * Returns true if a drag operation is in progress, and either the owner initiated the drag, or its isDragging()\n\t * is defined and returns true.\n\t */\n\tisDragging(): boolean\n\tisDraggingSource(sourceId: Identifier | undefined): boolean\n\tisOverTarget(\n\t\ttargetId: Identifier | undefined,\n\t\toptions?: {\n\t\t\tshallow?: boolean\n\t\t},\n\t): boolean\n\n\t/**\n\t * Returns a string or a symbol identifying the type of the current dragged item. Returns null if no item is being dragged.\n\t */\n\tgetItemType(): Identifier | null\n\n\t/**\n\t * Returns a plain object representing the currently dragged item. Every drag source must specify it by returning an object\n\t * from its beginDrag() method. Returns null if no item is being dragged.\n\t */\n\tgetItem(): any\n\tgetSourceId(): Identifier | null\n\tgetTargetIds(): Identifier[]\n\t/**\n\t * Returns a plain object representing the last recorded drop result. The drop targets may optionally specify it by returning an\n\t * object from their drop() methods. When a chain of drop() is dispatched for the nested targets, bottom up, any parent that\n\t * explicitly returns its own result from drop() overrides the child drop result previously set by the child. Returns null if\n\t * called outside endDrag().\n\t */\n\tgetDropResult(): any\n\t/**\n\t * Returns true if some drop target has handled the drop event, false otherwise. Even if a target did not return a drop result,\n\t * didDrop() returns true. Use it inside endDrag() to test whether any drop target has handled the drop. Returns false if called\n\t * outside endDrag().\n\t */\n\tdidDrop(): boolean\n\tisSourcePublic(): boolean | null\n\t/**\n\t * Returns the { x, y } client offset of the pointer at the time when the current drag operation has started.\n\t * Returns null if no item is being dragged.\n\t */\n\tgetInitialClientOffset(): XYCoord | null\n\t/**\n\t * Returns the { x, y } client offset of the drag source component's root DOM node at the time when the current drag\n\t * operation has started. Returns null if no item is being dragged.\n\t */\n\tgetInitialSourceClientOffset(): XYCoord | null\n\n\t/**\n\t * Returns the last recorded { x, y } client offset of the pointer while a drag operation is in progress.\n\t * Returns null if no item is being dragged.\n\t */\n\tgetClientOffset(): XYCoord | null\n\n\t/**\n\t * Returns the projected { x, y } client offset of the drag source component's root DOM node, based on its position at the time\n\t * when the current drag operation has started, and the movement difference. Returns null if no item is being dragged.\n\t */\n\tgetSourceClientOffset(): XYCoord | null\n\n\t/**\n\t * Returns the { x, y } difference between the last recorded client offset of the pointer and the client offset when the current\n\t * drag operation has started. Returns null if no item is being dragged.\n\t */\n\tgetDifferenceFromInitialOffset(): XYCoord | null\n}\n\nexport interface HandlerRegistry {\n\taddSource(type: SourceType, source: DragSource): Identifier\n\taddTarget(type: TargetType, target: DropTarget): Identifier\n\tcontainsHandler(handler: DragSource | DropTarget): boolean\n\tgetSource(sourceId: Identifier, includePinned?: boolean): DragSource\n\tgetSourceType(sourceId: Identifier): SourceType\n\tgetTargetType(targetId: Identifier): TargetType\n\tgetTarget(targetId: Identifier): DropTarget\n\tisSourceId(handlerId: Identifier): boolean\n\tisTargetId(handlerId: Identifier): boolean\n\tremoveSource(sourceId: Identifier): void\n\tremoveTarget(targetId: Identifier): void\n\tpinSource(sourceId: Identifier): void\n\tunpinSource(): void\n}\n\nexport interface Action<Payload> {\n\ttype: Identifier\n\tpayload: Payload\n}\nexport interface SentinelAction {\n\ttype: Identifier\n}\n\nexport type ActionCreator<Payload> = (args: any[]) => Action<Payload>\n\nexport interface BeginDragOptions {\n\tpublishSource?: boolean\n\tclientOffset?: XYCoord\n\tgetSourceClientOffset?: (sourceId: Identifier | undefined) => XYCoord\n}\n\nexport interface InitCoordsPayload {\n\tclientOffset: XYCoord | null\n\tsourceClientOffset: XYCoord | null\n}\n\nexport interface BeginDragPayload {\n\titemType: Identifier\n\titem: any\n\tsourceId: Identifier\n\tclientOffset: XYCoord | null\n\tsourceClientOffset: XYCoord | null\n\tisSourcePublic: boolean\n}\n\nexport interface HoverPayload {\n\ttargetIds: Identifier[]\n\tclientOffset: XYCoord | null\n}\n\nexport interface HoverOptions {\n\tclientOffset?: XYCoord\n}\n\nexport interface DropPayload {\n\tdropResult: any\n}\n\nexport interface TargetIdPayload {\n\ttargetId: Identifier\n}\n\nexport interface SourceIdPayload {\n\tsourceId: Identifier\n}\n\nexport interface DragDropActions {\n\tbeginDrag(\n\t\tsourceIds?: Identifier[],\n\t\toptions?: any,\n\t): Action<BeginDragPayload> | undefined\n\tpublishDragSource(): SentinelAction | undefined\n\thover(targetIds: Identifier[], options?: any): Action<HoverPayload>\n\tdrop(options?: any): void\n\tendDrag(): SentinelAction\n}\n\nexport interface DragDropManager {\n\tgetMonitor(): DragDropMonitor\n\tgetBackend(): Backend\n\tgetRegistry(): HandlerRegistry\n\tgetActions(): DragDropActions\n\tdispatch(action: any): void\n}\n\nexport type BackendFactory = (\n\tmanager: DragDropManager,\n\tglobalContext?: any,\n\tconfiguration?: any,\n) => Backend\n\nexport interface DragSource {\n\tbeginDrag(monitor: DragDropMonitor, targetId: Identifier): void\n\tendDrag(monitor: DragDropMonitor, targetId: Identifier): void\n\tcanDrag(monitor: DragDropMonitor, targetId: Identifier): boolean\n\tisDragging(monitor: DragDropMonitor, targetId: Identifier): boolean\n}\n\nexport interface DropTarget {\n\tcanDrop(monitor: DragDropMonitor, targetId: Identifier): boolean\n\thover(monitor: DragDropMonitor, targetId: Identifier): void\n\tdrop(monitor: DragDropMonitor, targetId: Identifier): any\n}\n", "let nextUniqueId = 0\n\nexport function getNextUniqueId(): number {\n\treturn nextUniqueId++\n}\n", "import { asap } from '@react-dnd/asap'\nimport { invariant } from '@react-dnd/invariant'\nimport type { Store } from 'redux'\n\nimport {\n\taddSource,\n\taddTarget,\n\tremoveSource,\n\tremoveTarget,\n} from '../actions/registry.js'\nimport {\n\tvalidateSourceContract,\n\tvalidateTargetContract,\n\tvalidateType,\n} from '../contracts.js'\nimport type {\n\tDragSource,\n\tDropTarget,\n\tHandlerRegistry,\n\tIdentifier,\n\tSourceType,\n\tTargetType,\n} from '../interfaces.js'\nimport { HandlerRole } from '../interfaces.js'\nimport type { State } from '../reducers/index.js'\nimport { getNextUniqueId } from '../utils/getNextUniqueId.js'\n\nfunction getNextHandlerId(role: HandlerRole): string {\n\tconst id = getNextUniqueId().toString()\n\tswitch (role) {\n\t\tcase HandlerRole.SOURCE:\n\t\t\treturn `S${id}`\n\t\tcase HandlerRole.TARGET:\n\t\t\treturn `T${id}`\n\t\tdefault:\n\t\t\tthrow new Error(`Unknown Handler Role: ${role}`)\n\t}\n}\n\nfunction parseRoleFromHandlerId(handlerId: string) {\n\tswitch (handlerId[0]) {\n\t\tcase 'S':\n\t\t\treturn HandlerRole.SOURCE\n\t\tcase 'T':\n\t\t\treturn HandlerRole.TARGET\n\t\tdefault:\n\t\t\tthrow new Error(`Cannot parse handler ID: ${handlerId}`)\n\t}\n}\n\nfunction mapContainsValue<T>(map: Map<string, T>, searchValue: T) {\n\tconst entries = map.entries()\n\tlet isDone = false\n\tdo {\n\t\tconst {\n\t\t\tdone,\n\t\t\tvalue: [, value],\n\t\t} = entries.next()\n\t\tif (value === searchValue) {\n\t\t\treturn true\n\t\t}\n\t\tisDone = !!done\n\t} while (!isDone)\n\treturn false\n}\n\nexport class HandlerRegistryImpl implements HandlerRegistry {\n\tprivate types: Map<string, SourceType | TargetType> = new Map()\n\tprivate dragSources: Map<string, DragSource> = new Map()\n\tprivate dropTargets: Map<string, DropTarget> = new Map()\n\tprivate pinnedSourceId: string | null = null\n\tprivate pinnedSource: any = null\n\tprivate store: Store<State>\n\n\tpublic constructor(store: Store<State>) {\n\t\tthis.store = store\n\t}\n\n\tpublic addSource(type: SourceType, source: DragSource): string {\n\t\tvalidateType(type)\n\t\tvalidateSourceContract(source)\n\n\t\tconst sourceId = this.addHandler(HandlerRole.SOURCE, type, source)\n\t\tthis.store.dispatch(addSource(sourceId))\n\t\treturn sourceId\n\t}\n\n\tpublic addTarget(type: TargetType, target: DropTarget): string {\n\t\tvalidateType(type, true)\n\t\tvalidateTargetContract(target)\n\n\t\tconst targetId = this.addHandler(HandlerRole.TARGET, type, target)\n\t\tthis.store.dispatch(addTarget(targetId))\n\t\treturn targetId\n\t}\n\n\tpublic containsHandler(handler: DragSource | DropTarget): boolean {\n\t\treturn (\n\t\t\tmapContainsValue(this.dragSources, handler) ||\n\t\t\tmapContainsValue(this.dropTargets, handler)\n\t\t)\n\t}\n\n\tpublic getSource(sourceId: string, includePinned = false): DragSource {\n\t\tinvariant(this.isSourceId(sourceId), 'Expected a valid source ID.')\n\t\tconst isPinned = includePinned && sourceId === this.pinnedSourceId\n\t\tconst source = isPinned ? this.pinnedSource : this.dragSources.get(sourceId)\n\t\treturn source\n\t}\n\n\tpublic getTarget(targetId: string): DropTarget {\n\t\tinvariant(this.isTargetId(targetId), 'Expected a valid target ID.')\n\t\treturn this.dropTargets.get(targetId) as DropTarget\n\t}\n\n\tpublic getSourceType(sourceId: string): Identifier {\n\t\tinvariant(this.isSourceId(sourceId), 'Expected a valid source ID.')\n\t\treturn this.types.get(sourceId) as Identifier\n\t}\n\n\tpublic getTargetType(targetId: string): Identifier | Identifier[] {\n\t\tinvariant(this.isTargetId(targetId), 'Expected a valid target ID.')\n\t\treturn this.types.get(targetId) as Identifier | Identifier[]\n\t}\n\n\tpublic isSourceId(handlerId: string): boolean {\n\t\tconst role = parseRoleFromHandlerId(handlerId)\n\t\treturn role === HandlerRole.SOURCE\n\t}\n\n\tpublic isTargetId(handlerId: string): boolean {\n\t\tconst role = parseRoleFromHandlerId(handlerId)\n\t\treturn role === HandlerRole.TARGET\n\t}\n\n\tpublic removeSource(sourceId: string): void {\n\t\tinvariant(this.getSource(sourceId), 'Expected an existing source.')\n\t\tthis.store.dispatch(removeSource(sourceId))\n\t\tasap(() => {\n\t\t\tthis.dragSources.delete(sourceId)\n\t\t\tthis.types.delete(sourceId)\n\t\t})\n\t}\n\n\tpublic removeTarget(targetId: string): void {\n\t\tinvariant(this.getTarget(targetId), 'Expected an existing target.')\n\t\tthis.store.dispatch(removeTarget(targetId))\n\t\tthis.dropTargets.delete(targetId)\n\t\tthis.types.delete(targetId)\n\t}\n\n\tpublic pinSource(sourceId: string): void {\n\t\tconst source = this.getSource(sourceId)\n\t\tinvariant(source, 'Expected an existing source.')\n\n\t\tthis.pinnedSourceId = sourceId\n\t\tthis.pinnedSource = source\n\t}\n\n\tpublic unpinSource(): void {\n\t\tinvariant(this.pinnedSource, 'No source is pinned at the time.')\n\n\t\tthis.pinnedSourceId = null\n\t\tthis.pinnedSource = null\n\t}\n\n\tprivate addHandler(\n\t\trole: HandlerRole,\n\t\ttype: SourceType | TargetType,\n\t\thandler: DragSource | DropTarget,\n\t): string {\n\t\tconst id = getNextHandlerId(role)\n\t\tthis.types.set(id, type)\n\t\tif (role === HandlerRole.SOURCE) {\n\t\t\tthis.dragSources.set(id, handler as DragSource)\n\t\t} else if (role === HandlerRole.TARGET) {\n\t\t\tthis.dropTargets.set(id, handler as DropTarget)\n\t\t}\n\t\treturn id\n\t}\n}\n", "import type { XYCoord } from '../interfaces.js'\n\nexport type EqualityCheck<T> = (a: T, b: T) => boolean\nexport const strictEquality = <T>(a: T, b: T): boolean => a === b\n\n/**\n * Determine if two cartesian coordinate offsets are equal\n * @param offsetA\n * @param offsetB\n */\nexport function areCoordsEqual(\n\toffsetA: XYCoord | null | undefined,\n\toffsetB: XYCoord | null | undefined,\n): boolean {\n\tif (!offsetA && !offsetB) {\n\t\treturn true\n\t} else if (!offsetA || !offsetB) {\n\t\treturn false\n\t} else {\n\t\treturn offsetA.x === offsetB.x && offsetA.y === offsetB.y\n\t}\n}\n\n/**\n * Determines if two arrays of items are equal\n * @param a The first array of items\n * @param b The second array of items\n */\nexport function areArraysEqual<T>(\n\ta: T[],\n\tb: T[],\n\tisEqual: EqualityCheck<T> = strictEquality,\n): boolean {\n\tif (a.length !== b.length) {\n\t\treturn false\n\t}\n\tfor (let i = 0; i < a.length; ++i) {\n\t\tif (!isEqual(a[i] as T, b[i] as T)) {\n\t\t\treturn false\n\t\t}\n\t}\n\treturn true\n}\n", "import {\n\tB<PERSON>IN_DRAG,\n\tDROP,\n\t<PERSON><PERSON>_DRAG,\n\tHOVER,\n\tPUBLISH_DRAG_SOURCE,\n} from '../actions/dragDrop/index.js'\nimport {\n\tADD_SOURCE,\n\tADD_TARGET,\n\tREMOVE_SOURCE,\n\tREMOVE_TARGET,\n} from '../actions/registry.js'\nimport type { Action } from '../interfaces.js'\nimport { ALL, NONE } from '../utils/dirtiness.js'\nimport { areArraysEqual } from '../utils/equality.js'\nimport { xor } from '../utils/js_utils.js'\n\nexport type State = string[]\n\nexport interface DirtyHandlerIdPayload {\n\ttargetIds: string[]\n\tprevTargetIds: string[]\n}\n\nexport function reduce(\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\t_state: State = NONE,\n\taction: Action<DirtyHandlerIdPayload>,\n): State {\n\tswitch (action.type) {\n\t\tcase HOVER:\n\t\t\tbreak\n\t\tcase ADD_SOURCE:\n\t\tcase ADD_TARGET:\n\t\tcase REMOVE_TARGET:\n\t\tcase REMOVE_SOURCE:\n\t\t\treturn NONE\n\t\tcase BEGIN_DRAG:\n\t\tcase PUBLISH_DRAG_SOURCE:\n\t\tcase END_DRAG:\n\t\tcase DROP:\n\t\tdefault:\n\t\t\treturn ALL\n\t}\n\n\tconst { targetIds = [], prevTargetIds = [] } = action.payload\n\tconst result = xor(targetIds, prevTargetIds)\n\tconst didChange =\n\t\tresult.length > 0 || !areArraysEqual(targetIds, prevTargetIds)\n\n\tif (!didChange) {\n\t\treturn NONE\n\t}\n\n\t// Check the target ids at the innermost position. If they are valid, add them\n\t// to the result\n\tconst prevInnermostTargetId = prevTargetIds[prevTargetIds.length - 1]\n\tconst innermostTargetId = targetIds[targetIds.length - 1]\n\tif (prevInnermostTargetId !== innermostTargetId) {\n\t\tif (prevInnermostTargetId) {\n\t\t\tresult.push(prevInnermostTargetId)\n\t\t}\n\t\tif (innermostTargetId) {\n\t\t\tresult.push(innermostTargetId)\n\t\t}\n\t}\n\n\treturn result\n}\n", "import {\n\tBEGIN_DRAG,\n\tDROP,\n\t<PERSON>ND_DRAG,\n\tHOVER,\n\tINIT_COORDS,\n} from '../actions/dragDrop/index.js'\nimport type { Action, XYCoord } from '../interfaces.js'\nimport { areCoordsEqual } from '../utils/equality.js'\n\nexport interface State {\n\tinitialSourceClientOffset: XYCoord | null\n\tinitialClientOffset: XYCoord | null\n\tclientOffset: XYCoord | null\n}\n\nconst initialState: State = {\n\tinitialSourceClientOffset: null,\n\tinitialClientOffset: null,\n\tclientOffset: null,\n}\n\nexport function reduce(\n\tstate: State = initialState,\n\taction: Action<{\n\t\tsourceClientOffset: XYCoord\n\t\tclientOffset: XYCoord\n\t}>,\n): State {\n\tconst { payload } = action\n\tswitch (action.type) {\n\t\tcase INIT_COORDS:\n\t\tcase BEGIN_DRAG:\n\t\t\treturn {\n\t\t\t\tinitialSourceClientOffset: payload.sourceClientOffset,\n\t\t\t\tinitialClientOffset: payload.clientOffset,\n\t\t\t\tclientOffset: payload.clientOffset,\n\t\t\t}\n\t\tcase HOVER:\n\t\t\tif (areCoordsEqual(state.clientOffset, payload.clientOffset)) {\n\t\t\t\treturn state\n\t\t\t}\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tclientOffset: payload.clientOffset,\n\t\t\t}\n\t\tcase END_DRAG:\n\t\tcase DROP:\n\t\t\treturn initialState\n\t\tdefault:\n\t\t\treturn state\n\t}\n}\n", "import {\n\tBEGIN_DRAG,\n\tDROP,\n\t<PERSON>ND_DRAG,\n\tHOVER,\n\tPUBLISH_DRAG_SOURCE,\n} from '../actions/dragDrop/index.js'\nimport { REMOVE_TARGET } from '../actions/registry.js'\nimport type { Action, Identifier } from '../interfaces.js'\nimport { without } from '../utils/js_utils.js'\n\nexport interface State {\n\titemType: Identifier | Identifier[] | null\n\titem: any\n\tsourceId: string | null\n\ttargetIds: string[]\n\tdropResult: any\n\tdidDrop: boolean\n\tisSourcePublic: boolean | null\n}\n\nconst initialState: State = {\n\titemType: null,\n\titem: null,\n\tsourceId: null,\n\ttargetIds: [],\n\tdropResult: null,\n\tdidDrop: false,\n\tisSourcePublic: null,\n}\n\nexport function reduce(\n\tstate: State = initialState,\n\taction: Action<{\n\t\titemType: Identifier | Identifier[]\n\t\titem: any\n\t\tsourceId: string\n\t\ttargetId: string\n\t\ttargetIds: string[]\n\t\tisSourcePublic: boolean\n\t\tdropResult: any\n\t}>,\n): State {\n\tconst { payload } = action\n\tswitch (action.type) {\n\t\tcase BEGIN_DRAG:\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\titemType: payload.itemType,\n\t\t\t\titem: payload.item,\n\t\t\t\tsourceId: payload.sourceId,\n\t\t\t\tisSourcePublic: payload.isSourcePublic,\n\t\t\t\tdropResult: null,\n\t\t\t\tdidDrop: false,\n\t\t\t}\n\t\tcase PUBLISH_DRAG_SOURCE:\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tisSourcePublic: true,\n\t\t\t}\n\t\tcase HOVER:\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\ttargetIds: payload.targetIds,\n\t\t\t}\n\t\tcase REMOVE_TARGET:\n\t\t\tif (state.targetIds.indexOf(payload.targetId) === -1) {\n\t\t\t\treturn state\n\t\t\t}\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\ttargetIds: without(state.targetIds, payload.targetId),\n\t\t\t}\n\t\tcase DROP:\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tdropResult: payload.dropResult,\n\t\t\t\tdidDrop: true,\n\t\t\t\ttargetIds: [],\n\t\t\t}\n\t\tcase END_DRAG:\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\titemType: null,\n\t\t\t\titem: null,\n\t\t\t\tsourceId: null,\n\t\t\t\tdropResult: null,\n\t\t\t\tdidDrop: false,\n\t\t\t\tisSourcePublic: null,\n\t\t\t\ttargetIds: [],\n\t\t\t}\n\t\tdefault:\n\t\t\treturn state\n\t}\n}\n", "import {\n\tADD_SOURCE,\n\tADD_TARGET,\n\tREMOVE_SOURCE,\n\tREMOVE_TARGET,\n} from '../actions/registry.js'\nimport type { Action } from '../interfaces.js'\n\nexport type State = number\n\nexport function reduce(state: State = 0, action: Action<any>): State {\n\tswitch (action.type) {\n\t\tcase ADD_SOURCE:\n\t\tcase ADD_TARGET:\n\t\t\treturn state + 1\n\t\tcase REMOVE_SOURCE:\n\t\tcase REMOVE_TARGET:\n\t\t\treturn state - 1\n\t\tdefault:\n\t\t\treturn state\n\t}\n}\n", "export type State = number\n\nexport function reduce(state: State = 0): State {\n\treturn state + 1\n}\n", "import type { Action } from '../interfaces.js'\nimport { get } from '../utils/js_utils.js'\nimport type { State as DirtyHandlerIdsState } from './dirtyHandlerIds.js'\nimport { reduce as dirtyHandlerIds } from './dirtyHandlerIds.js'\nimport type { State as DragOffsetState } from './dragOffset.js'\nimport { reduce as dragOffset } from './dragOffset.js'\nimport type { State as DragOperationState } from './dragOperation.js'\nimport { reduce as dragOperation } from './dragOperation.js'\nimport type { State as RefCountState } from './refCount.js'\nimport { reduce as refCount } from './refCount.js'\nimport type { State as StateIdState } from './stateId.js'\nimport { reduce as stateId } from './stateId.js'\n\nexport interface State {\n\tdirtyHandlerIds: DirtyHandlerIdsState\n\tdragOffset: DragOffsetState\n\trefCount: RefCountState\n\tdragOperation: DragOperationState\n\tstateId: StateIdState\n}\n\nexport function reduce(state: State = {} as State, action: Action<any>): State {\n\treturn {\n\t\tdirtyHandlerIds: dirtyHandlerIds(state.dirtyHandlerIds, {\n\t\t\ttype: action.type,\n\t\t\tpayload: {\n\t\t\t\t...action.payload,\n\t\t\t\tprevTargetIds: get<string[]>(state, 'dragOperation.targetIds', []),\n\t\t\t},\n\t\t}),\n\t\tdragOffset: dragOffset(state.dragOffset, action),\n\t\trefCount: refCount(state.refCount, action),\n\t\tdragOperation: dragOperation(state.dragOperation, action),\n\t\tstateId: stateId(state.stateId),\n\t}\n}\n", "import type { Store } from 'redux'\nimport { createStore } from 'redux'\n\nimport { DragDropManagerImpl } from './classes/DragDropManagerImpl.js'\nimport { DragDropMonitorImpl } from './classes/DragDropMonitorImpl.js'\nimport { HandlerRegistryImpl } from './classes/HandlerRegistryImpl.js'\nimport type { BackendFactory, DragDropManager } from './interfaces.js'\nimport type { State } from './reducers/index.js'\nimport { reduce } from './reducers/index.js'\n\nexport function createDragDropManager(\n\tbackendFactory: BackendFactory,\n\tglobalContext: unknown = undefined,\n\tbackendOptions: unknown = {},\n\tdebugMode = false,\n): DragDropManager {\n\tconst store = makeStoreInstance(debugMode)\n\tconst monitor = new DragDropMonitorImpl(store, new HandlerRegistryImpl(store))\n\tconst manager = new DragDropManagerImpl(store, monitor)\n\tconst backend = backendFactory(manager, globalContext, backendOptions)\n\tmanager.receiveBackend(backend)\n\treturn manager\n}\n\nfunction makeStoreInstance(debugMode: boolean): Store<State> {\n\t// TODO: if we ever make a react-native version of this,\n\t// we'll need to consider how to pull off dev-tooling\n\tconst reduxDevTools =\n\t\ttypeof window !== 'undefined' &&\n\t\t(window as any).__REDUX_DEVTOOLS_EXTENSION__\n\treturn createStore(\n\t\treduce,\n\t\tdebugMode &&\n\t\t\treduxDevTools &&\n\t\t\treduxDevTools({\n\t\t\t\tname: 'dnd-core',\n\t\t\t\tinstanceId: 'dnd-core',\n\t\t\t}),\n\t)\n}\n", "import type { BackendFactory, DragDropManager } from 'dnd-core'\nimport { createDragDropManager } from 'dnd-core'\nimport type { FC, ReactNode } from 'react'\nimport { memo, useEffect } from 'react'\n\nimport { DndContext } from './DndContext.js'\n\nexport type DndProviderProps<BackendContext, BackendOptions> =\n\t| {\n\t\t\tchildren?: ReactNode\n\t\t\tmanager: DragDropManager\n\t  }\n\t| {\n\t\t\tbackend: BackendFactory\n\t\t\tchildren?: ReactNode\n\t\t\tcontext?: BackendContext\n\t\t\toptions?: BackendOptions\n\t\t\tdebugMode?: boolean\n\t  }\n\nlet refCount = 0\nconst INSTANCE_SYM = Symbol.for('__REACT_DND_CONTEXT_INSTANCE__')\n\n/**\n * A React component that provides the React-DnD context\n */\nexport const DndProvider: FC<DndProviderProps<unknown, unknown>> = memo(\n\tfunction DndProvider({ children, ...props }) {\n\t\tconst [manager, isGlobalInstance] = getDndContextValue(props) // memoized from props\n\t\t/**\n\t\t * If the global context was used to store the DND context\n\t\t * then where theres no more references to it we should\n\t\t * clean it up to avoid memory leaks\n\t\t */\n\t\tuseEffect(() => {\n\t\t\tif (isGlobalInstance) {\n\t\t\t\tconst context = getGlobalContext()\n\t\t\t\t++refCount\n\n\t\t\t\treturn () => {\n\t\t\t\t\tif (--refCount === 0) {\n\t\t\t\t\t\tcontext[INSTANCE_SYM] = null\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn\n\t\t}, [])\n\n\t\treturn <DndContext.Provider value={manager}>{children}</DndContext.Provider>\n\t},\n)\n\nfunction getDndContextValue(props: DndProviderProps<unknown, unknown>) {\n\tif ('manager' in props) {\n\t\tconst manager = { dragDropManager: props.manager }\n\t\treturn [manager, false]\n\t}\n\n\tconst manager = createSingletonDndContext(\n\t\tprops.backend,\n\t\tprops.context,\n\t\tprops.options,\n\t\tprops.debugMode,\n\t)\n\tconst isGlobalInstance = !props.context\n\n\treturn [manager, isGlobalInstance]\n}\n\nfunction createSingletonDndContext<BackendContext, BackendOptions>(\n\tbackend: BackendFactory,\n\tcontext: BackendContext = getGlobalContext(),\n\toptions: BackendOptions,\n\tdebugMode?: boolean,\n) {\n\tconst ctx = context as any\n\tif (!ctx[INSTANCE_SYM]) {\n\t\tctx[INSTANCE_SYM] = {\n\t\t\tdragDropManager: createDragDropManager(\n\t\t\t\tbackend,\n\t\t\t\tcontext,\n\t\t\t\toptions,\n\t\t\t\tdebugMode,\n\t\t\t),\n\t\t}\n\t}\n\treturn ctx[INSTANCE_SYM]\n}\n\ndeclare const global: any\nfunction getGlobalContext() {\n\treturn typeof global !== 'undefined' ? global : (window as any)\n}\n", "import type { DragDropManager } from 'dnd-core'\nimport { createContext } from 'react'\n\n/**\n * The React context type\n */\nexport interface DndContextType {\n\tdragDropManager: DragDropManager | undefined\n}\n\n/**\n * Create the React Context\n */\nexport const DndContext = createContext<DndContextType>({\n\tdragDropManager: undefined,\n})\n", "import type { FC } from 'react'\nimport { memo, useEffect } from 'react'\n\nimport type { ConnectDragPreview } from '../types/index.js'\n\nexport interface DragPreviewImageProps {\n\tconnect: ConnectDragPreview\n\tsrc: string\n}\n/**\n * A utility for rendering a drag preview image\n */\nexport const DragPreviewImage: FC<DragPreviewImageProps> = memo(\n\tfunction DragPreviewImage({ connect, src }) {\n\t\tuseEffect(() => {\n\t\t\tif (typeof Image === 'undefined') return\n\n\t\t\tlet connected = false\n\t\t\tconst img = new Image()\n\t\t\timg.src = src\n\t\t\timg.onload = () => {\n\t\t\t\tconnect(img)\n\t\t\t\tconnected = true\n\t\t\t}\n\t\t\treturn () => {\n\t\t\t\tif (connected) {\n\t\t\t\t\tconnect(null)\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\n\t\treturn null\n\t},\n)\n", "import equal from 'fast-deep-equal'\nimport { useCallback, useState } from 'react'\n\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect.js'\n\n/**\n *\n * @param monitor The monitor to collect state from\n * @param collect The collecting function\n * @param onUpdate A method to invoke when updates occur\n */\nexport function useCollector<T, S>(\n\tmonitor: T,\n\tcollect: (monitor: T) => S,\n\tonUpdate?: () => void,\n): [S, () => void] {\n\tconst [collected, setCollected] = useState(() => collect(monitor))\n\n\tconst updateCollected = useCallback(() => {\n\t\tconst nextValue = collect(monitor)\n\t\t// This needs to be a deep-equality check because some monitor-collected values\n\t\t// include XYCoord objects that may be equivalent, but do not have instance equality.\n\t\tif (!equal(collected, nextValue)) {\n\t\t\tsetCollected(nextValue)\n\t\t\tif (onUpdate) {\n\t\t\t\tonUpdate()\n\t\t\t}\n\t\t}\n\t}, [collected, monitor, onUpdate])\n\n\t// update the collected properties after react renders.\n\t// Note that the \"Dustbin Stress Test\" fails if this is not\n\t// done when the component updates\n\tuseIsomorphicLayoutEffect(updateCollected)\n\n\treturn [collected, updateCollected]\n}\n", "import { useEffect, useLayoutEffect } from 'react'\n\n// suppress the useLayoutEffect warning on server side.\nexport const useIsomorphicLayoutEffect =\n\ttypeof window !== 'undefined' ? useLayoutEffect : useEffect\n", "import type { Hand<PERSON><PERSON>anager, MonitorEventEmitter } from '../types/index.js'\nimport { useCollector } from './useCollector.js'\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect.js'\n\nexport function useMonitorOutput<Monitor extends HandlerManager, Collected>(\n\tmonitor: Monitor & MonitorEventEmitter,\n\tcollect: (monitor: Monitor) => Collected,\n\tonCollect?: () => void,\n): Collected {\n\tconst [collected, updateCollected] = useCollector(monitor, collect, onCollect)\n\n\tuseIsomorphicLayoutEffect(\n\t\tfunction subscribeToMonitorStateChange() {\n\t\t\tconst handlerId = monitor.getHandlerId()\n\t\t\tif (handlerId == null) {\n\t\t\t\treturn\n\t\t\t}\n\t\t\treturn monitor.subscribeToStateChange(updateCollected, {\n\t\t\t\thandlerIds: [handlerId],\n\t\t\t})\n\t\t},\n\t\t[monitor, updateCollected],\n\t)\n\n\treturn collected\n}\n", "import type { Connector } from '../internals/index.js'\nimport type { <PERSON>lerManager, MonitorEventEmitter } from '../types/index.js'\nimport { useMonitorOutput } from './useMonitorOutput.js'\n\nexport function useCollectedProps<Collected, Monitor extends HandlerManager>(\n\tcollector: ((monitor: Monitor) => Collected) | undefined,\n\tmonitor: Monitor & MonitorEventEmitter,\n\tconnector: Connector,\n) {\n\treturn useMonitorOutput(monitor, collector || (() => ({} as Collected)), () =>\n\t\tconnector.reconnect(),\n\t)\n}\n", "import { useMemo } from 'react'\n\nimport type { FactoryOrInstance } from './types.js'\n\nexport function useOptionalFactory<T>(\n\targ: FactoryOrInstance<T>,\n\tdeps?: unknown[],\n): T {\n\tconst memoDeps = [...(deps || [])]\n\tif (deps == null && typeof arg !== 'function') {\n\t\tmemoDeps.push(arg)\n\t}\n\treturn useMemo<T>(() => {\n\t\treturn typeof arg === 'function' ? (arg as () => T)() : (arg as T)\n\t}, memoDeps)\n}\n", "import { useMemo } from 'react'\n\nimport type { SourceConnector } from '../../internals/index.js'\n\nexport function useConnectDragSource(connector: SourceConnector) {\n\treturn useMemo(() => connector.hooks.dragSource(), [connector])\n}\n\nexport function useConnectDragPreview(connector: SourceConnector) {\n\treturn useMemo(() => connector.hooks.dragPreview(), [connector])\n}\n", "import { useMemo } from 'react'\n\nimport { SourceConnector } from '../../internals/index.js'\nimport type {\n\tDragPreviewOptions,\n\tDragSourceOptions,\n} from '../../types/index.js'\nimport { useDragDropManager } from '../useDragDropManager.js'\nimport { useIsomorphicLayoutEffect } from '../useIsomorphicLayoutEffect.js'\n\nexport function useDragSourceConnector(\n\tdragSourceOptions: DragSourceOptions | undefined,\n\tdragPreviewOptions: DragPreviewOptions | undefined,\n): SourceConnector {\n\tconst manager = useDragDropManager()\n\tconst connector = useMemo(\n\t\t() => new SourceConnector(manager.getBackend()),\n\t\t[manager],\n\t)\n\tuseIsomorphicLayoutEffect(() => {\n\t\tconnector.dragSourceOptions = dragSourceOptions || null\n\t\tconnector.reconnect()\n\t\treturn () => connector.disconnectDragSource()\n\t}, [connector, dragSourceOptions])\n\tuseIsomorphicLayoutEffect(() => {\n\t\tconnector.dragPreviewOptions = dragPreviewOptions || null\n\t\tconnector.reconnect()\n\t\treturn () => connector.disconnectDragPreview()\n\t}, [connector, dragPreviewOptions])\n\treturn connector\n}\n", "import { invariant } from '@react-dnd/invariant'\nimport type {\n\t<PERSON>agDropManager,\n\tDragDropMonitor,\n\tIdentifier,\n\tListener,\n\tUnsubscribe,\n\tXYCoord,\n} from 'dnd-core'\n\nimport type { DragSourceMonitor } from '../types/index.js'\n\nlet isCallingCanDrag = false\nlet isCallingIsDragging = false\n\nexport class DragSourceMonitorImpl implements DragSourceMonitor {\n\tprivate internalMonitor: DragDropMonitor\n\tprivate sourceId: Identifier | null = null\n\n\tpublic constructor(manager: DragDropManager) {\n\t\tthis.internalMonitor = manager.getMonitor()\n\t}\n\n\tpublic receiveHandlerId(sourceId: Identifier | null): void {\n\t\tthis.sourceId = sourceId\n\t}\n\n\tpublic getHandlerId(): Identifier | null {\n\t\treturn this.sourceId\n\t}\n\n\tpublic canDrag(): boolean {\n\t\tinvariant(\n\t\t\t!isCallingCanDrag,\n\t\t\t'You may not call monitor.canDrag() inside your canDrag() implementation. ' +\n\t\t\t\t'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor',\n\t\t)\n\n\t\ttry {\n\t\t\tisCallingCanDrag = true\n\t\t\treturn this.internalMonitor.canDragSource(this.sourceId as Identifier)\n\t\t} finally {\n\t\t\tisCallingCanDrag = false\n\t\t}\n\t}\n\n\tpublic isDragging(): boolean {\n\t\tif (!this.sourceId) {\n\t\t\treturn false\n\t\t}\n\t\tinvariant(\n\t\t\t!isCallingIsDragging,\n\t\t\t'You may not call monitor.isDragging() inside your isDragging() implementation. ' +\n\t\t\t\t'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor',\n\t\t)\n\n\t\ttry {\n\t\t\tisCallingIsDragging = true\n\t\t\treturn this.internalMonitor.isDraggingSource(this.sourceId)\n\t\t} finally {\n\t\t\tisCallingIsDragging = false\n\t\t}\n\t}\n\n\tpublic subscribeToStateChange(\n\t\tlistener: Listener,\n\t\toptions?: { handlerIds?: Identifier[] },\n\t): Unsubscribe {\n\t\treturn this.internalMonitor.subscribeToStateChange(listener, options)\n\t}\n\n\tpublic isDraggingSource(sourceId: Identifier): boolean {\n\t\treturn this.internalMonitor.isDraggingSource(sourceId)\n\t}\n\n\tpublic isOverTarget(\n\t\ttargetId: Identifier,\n\t\toptions?: { shallow: boolean },\n\t): boolean {\n\t\treturn this.internalMonitor.isOverTarget(targetId, options)\n\t}\n\n\tpublic getTargetIds(): Identifier[] {\n\t\treturn this.internalMonitor.getTargetIds()\n\t}\n\n\tpublic isSourcePublic(): boolean | null {\n\t\treturn this.internalMonitor.isSourcePublic()\n\t}\n\n\tpublic getSourceId(): Identifier | null {\n\t\treturn this.internalMonitor.getSourceId()\n\t}\n\n\tpublic subscribeToOffsetChange(listener: Listener): Unsubscribe {\n\t\treturn this.internalMonitor.subscribeToOffsetChange(listener)\n\t}\n\n\tpublic canDragSource(sourceId: Identifier): boolean {\n\t\treturn this.internalMonitor.canDragSource(sourceId)\n\t}\n\n\tpublic canDropOnTarget(targetId: Identifier): boolean {\n\t\treturn this.internalMonitor.canDropOnTarget(targetId)\n\t}\n\n\tpublic getItemType(): Identifier | null {\n\t\treturn this.internalMonitor.getItemType()\n\t}\n\n\tpublic getItem(): any {\n\t\treturn this.internalMonitor.getItem()\n\t}\n\n\tpublic getDropResult(): any {\n\t\treturn this.internalMonitor.getDropResult()\n\t}\n\n\tpublic didDrop(): boolean {\n\t\treturn this.internalMonitor.didDrop()\n\t}\n\n\tpublic getInitialClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getInitialClientOffset()\n\t}\n\n\tpublic getInitialSourceClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getInitialSourceClientOffset()\n\t}\n\n\tpublic getSourceClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getSourceClientOffset()\n\t}\n\n\tpublic getClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getClientOffset()\n\t}\n\n\tpublic getDifferenceFromInitialOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getDifferenceFromInitialOffset()\n\t}\n}\n", "import { invariant } from '@react-dnd/invariant'\nimport type {\n\t<PERSON><PERSON><PERSON><PERSON><PERSON>ana<PERSON>,\n\tDragDropMonitor,\n\tIdentifier,\n\tListener,\n\tUnsubscribe,\n\tXYCoord,\n} from 'dnd-core'\n\nimport type { DropTargetMonitor } from '../types/index.js'\n\nlet isCallingCanDrop = false\n\nexport class DropTargetMonitorImpl implements DropTargetMonitor {\n\tprivate internalMonitor: DragDropMonitor\n\tprivate targetId: Identifier | null = null\n\n\tpublic constructor(manager: DragDropManager) {\n\t\tthis.internalMonitor = manager.getMonitor()\n\t}\n\n\tpublic receiveHandlerId(targetId: Identifier | null): void {\n\t\tthis.targetId = targetId\n\t}\n\n\tpublic getHandlerId(): Identifier | null {\n\t\treturn this.targetId\n\t}\n\n\tpublic subscribeToStateChange(\n\t\tlistener: Listener,\n\t\toptions?: { handlerIds?: Identifier[] },\n\t): Unsubscribe {\n\t\treturn this.internalMonitor.subscribeToStateChange(listener, options)\n\t}\n\n\tpublic canDrop(): boolean {\n\t\t// Cut out early if the target id has not been set. This should prevent errors\n\t\t// where the user has an older version of dnd-core like in\n\t\t// https://github.com/react-dnd/react-dnd/issues/1310\n\t\tif (!this.targetId) {\n\t\t\treturn false\n\t\t}\n\t\tinvariant(\n\t\t\t!isCallingCanDrop,\n\t\t\t'You may not call monitor.canDrop() inside your canDrop() implementation. ' +\n\t\t\t\t'Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor',\n\t\t)\n\n\t\ttry {\n\t\t\tisCallingCanDrop = true\n\t\t\treturn this.internalMonitor.canDropOnTarget(this.targetId)\n\t\t} finally {\n\t\t\tisCallingCanDrop = false\n\t\t}\n\t}\n\n\tpublic isOver(options?: { shallow?: boolean }): boolean {\n\t\tif (!this.targetId) {\n\t\t\treturn false\n\t\t}\n\t\treturn this.internalMonitor.isOverTarget(this.targetId, options)\n\t}\n\n\tpublic getItemType(): Identifier | null {\n\t\treturn this.internalMonitor.getItemType()\n\t}\n\n\tpublic getItem(): any {\n\t\treturn this.internalMonitor.getItem()\n\t}\n\n\tpublic getDropResult(): any {\n\t\treturn this.internalMonitor.getDropResult()\n\t}\n\n\tpublic didDrop(): boolean {\n\t\treturn this.internalMonitor.didDrop()\n\t}\n\n\tpublic getInitialClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getInitialClientOffset()\n\t}\n\n\tpublic getInitialSourceClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getInitialSourceClientOffset()\n\t}\n\n\tpublic getSourceClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getSourceClientOffset()\n\t}\n\n\tpublic getClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getClientOffset()\n\t}\n\n\tpublic getDifferenceFromInitialOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getDifferenceFromInitialOffset()\n\t}\n}\n", "import type {\n\tDragDropManager,\n\tDragSource,\n\tDropTarget,\n\tIdentifier,\n\tSourceType,\n\tTargetType,\n\tUnsubscribe,\n} from 'dnd-core'\n\nexport function registerTarget(\n\ttype: TargetType,\n\ttarget: DropTarget,\n\tmanager: DragDropManager,\n): [Identifier, Unsubscribe] {\n\tconst registry = manager.getRegistry()\n\tconst targetId = registry.addTarget(type, target)\n\n\treturn [targetId, () => registry.removeTarget(targetId)]\n}\n\nexport function registerSource(\n\ttype: SourceType,\n\tsource: DragSource,\n\tmanager: DragDropManager,\n): [Identifier, Unsubscribe] {\n\tconst registry = manager.getRegistry()\n\tconst sourceId = registry.addSource(type, source)\n\n\treturn [sourceId, () => registry.removeSource(sourceId)]\n}\n", "export function shallowEqual<T>(\n\tobjA: T,\n\tobjB: T,\n\tcompare?: (a: T, b: T, key?: string) => boolean | void,\n\tcompareContext?: any,\n) {\n\tlet compareResult = compare\n\t\t? compare.call(compareContext, objA, objB)\n\t\t: void 0\n\tif (compareResult !== void 0) {\n\t\treturn !!compareResult\n\t}\n\n\tif (objA === objB) {\n\t\treturn true\n\t}\n\n\tif (typeof objA !== 'object' || !objA || typeof objB !== 'object' || !objB) {\n\t\treturn false\n\t}\n\n\tconst keysA = Object.keys(objA)\n\tconst keysB = Object.keys(objB)\n\n\tif (keysA.length !== keysB.length) {\n\t\treturn false\n\t}\n\n\tconst bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB)\n\n\t// Test for A's keys different from B.\n\tfor (let idx = 0; idx < keysA.length; idx++) {\n\t\tconst key = keysA[idx] as string\n\n\t\tif (!bHasOwnProperty(key)) {\n\t\t\treturn false\n\t\t}\n\n\t\tconst valueA = (objA as any)[key]\n\t\tconst valueB = (objB as any)[key]\n\n\t\tcompareResult = compare\n\t\t\t? compare.call(compareContext, valueA, valueB, key)\n\t\t\t: void 0\n\n\t\tif (\n\t\t\tcompareResult === false ||\n\t\t\t(compareResult === void 0 && valueA !== valueB)\n\t\t) {\n\t\t\treturn false\n\t\t}\n\t}\n\n\treturn true\n}\n", "export interface Ref<T> {\n\tcurrent: T\n}\n\nexport function isRef(obj: unknown): boolean {\n\treturn (\n\t\t// eslint-disable-next-line no-prototype-builtins\n\t\tobj !== null &&\n\t\ttypeof obj === 'object' &&\n\t\tObject.prototype.hasOwnProperty.call(obj, 'current')\n\t)\n}\n", "import { invariant } from '@react-dnd/invariant'\nimport type { ReactElement } from 'react'\nimport { cloneElement, isValidElement } from 'react'\n\nfunction throwIfCompositeComponentElement(element: ReactElement<any>) {\n\t// Custom components can no longer be wrapped directly in React DnD 2.0\n\t// so that we don't need to depend on findDOMNode() from react-dom.\n\tif (typeof element.type === 'string') {\n\t\treturn\n\t}\n\n\tconst displayName =\n\t\t(element.type as any).displayName || element.type.name || 'the component'\n\n\tthrow new Error(\n\t\t'Only native element nodes can now be passed to React DnD connectors.' +\n\t\t\t`You can either wrap ${displayName} into a <div>, or turn it into a ` +\n\t\t\t'drag source or a drop target itself.',\n\t)\n}\n\nfunction wrapHookToRecognizeElement(hook: (node: any, options: any) => void) {\n\treturn (elementOrNode = null, options = null) => {\n\t\t// When passed a node, call the hook straight away.\n\t\tif (!isValidElement(elementOrNode)) {\n\t\t\tconst node = elementOrNode\n\t\t\thook(node, options)\n\t\t\t// return the node so it can be chained (e.g. when within callback refs\n\t\t\t// <div ref={node => connectDragSource(connectDropTarget(node))}/>\n\t\t\treturn node\n\t\t}\n\n\t\t// If passed a ReactElement, clone it and attach this function as a ref.\n\t\t// This helps us achieve a neat API where user doesn't even know that refs\n\t\t// are being used under the hood.\n\t\tconst element: ReactElement | null = elementOrNode\n\t\tthrowIfCompositeComponentElement(element as any)\n\n\t\t// When no options are passed, use the hook directly\n\t\tconst ref = options ? (node: Element) => hook(node, options) : hook\n\t\treturn cloneWithRef(element, ref)\n\t}\n}\n\nexport function wrapConnectorHooks(hooks: any) {\n\tconst wrappedHooks: any = {}\n\n\tObject.keys(hooks).forEach((key) => {\n\t\tconst hook = hooks[key]\n\n\t\t// ref objects should be passed straight through without wrapping\n\t\tif (key.endsWith('Ref')) {\n\t\t\twrappedHooks[key] = hooks[key]\n\t\t} else {\n\t\t\tconst wrappedHook = wrapHookToRecognizeElement(hook)\n\t\t\twrappedHooks[key] = () => wrappedHook\n\t\t}\n\t})\n\n\treturn wrappedHooks\n}\n\nfunction setRef(ref: any, node: any) {\n\tif (typeof ref === 'function') {\n\t\tref(node)\n\t} else {\n\t\tref.current = node\n\t}\n}\n\nfunction cloneWithRef(element: any, newRef: any): ReactElement<any> {\n\tconst previousRef = element.ref\n\tinvariant(\n\t\ttypeof previousRef !== 'string',\n\t\t'Cannot connect React DnD to an element with an existing string ref. ' +\n\t\t\t'Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. ' +\n\t\t\t'Read more: https://reactjs.org/docs/refs-and-the-dom.html#callback-refs',\n\t)\n\n\tif (!previousRef) {\n\t\t// When there is no ref on the element, use the new ref directly\n\t\treturn cloneElement(element, {\n\t\t\tref: newRef,\n\t\t})\n\t} else {\n\t\treturn cloneElement(element, {\n\t\t\tref: (node: any) => {\n\t\t\t\tsetRef(previousRef, node)\n\t\t\t\tsetRef(newRef, node)\n\t\t\t},\n\t\t})\n\t}\n}\n", "import { shallowEqual } from '@react-dnd/shallowequal'\nimport type { Backend, Identifier, Unsubscribe } from 'dnd-core'\nimport type { ReactElement, Ref, RefObject } from 'react'\n\nimport type { DragPreviewOptions, DragSourceOptions } from '../types/index.js'\nimport { isRef } from './isRef.js'\nimport { wrapConnectorHooks } from './wrapConnectorHooks.js'\n\nexport interface Connector {\n\thooks: any\n\tconnectTarget: any\n\treceiveHandlerId(handlerId: Identifier | null): void\n\treconnect(): void\n}\n\nexport class SourceConnector implements Connector {\n\tpublic hooks = wrapConnectorHooks({\n\t\tdragSource: (\n\t\t\tnode: Element | ReactElement | Ref<any>,\n\t\t\toptions?: DragSourceOptions,\n\t\t) => {\n\t\t\tthis.clearDragSource()\n\t\t\tthis.dragSourceOptions = options || null\n\t\t\tif (isRef(node)) {\n\t\t\t\tthis.dragSourceRef = node as RefObject<any>\n\t\t\t} else {\n\t\t\t\tthis.dragSourceNode = node\n\t\t\t}\n\t\t\tthis.reconnectDragSource()\n\t\t},\n\t\tdragPreview: (node: any, options?: DragPreviewOptions) => {\n\t\t\tthis.clearDragPreview()\n\t\t\tthis.dragPreviewOptions = options || null\n\t\t\tif (isRef(node)) {\n\t\t\t\tthis.dragPreviewRef = node\n\t\t\t} else {\n\t\t\t\tthis.dragPreviewNode = node\n\t\t\t}\n\t\t\tthis.reconnectDragPreview()\n\t\t},\n\t})\n\tprivate handlerId: Identifier | null = null\n\n\t// The drop target may either be attached via ref or connect function\n\tprivate dragSourceRef: RefObject<any> | null = null\n\tprivate dragSourceNode: any\n\tprivate dragSourceOptionsInternal: DragSourceOptions | null = null\n\tprivate dragSourceUnsubscribe: Unsubscribe | undefined\n\n\t// The drag preview may either be attached via ref or connect function\n\tprivate dragPreviewRef: RefObject<any> | null = null\n\tprivate dragPreviewNode: any\n\tprivate dragPreviewOptionsInternal: DragPreviewOptions | null = null\n\tprivate dragPreviewUnsubscribe: Unsubscribe | undefined\n\n\tprivate lastConnectedHandlerId: Identifier | null = null\n\tprivate lastConnectedDragSource: any = null\n\tprivate lastConnectedDragSourceOptions: any = null\n\tprivate lastConnectedDragPreview: any = null\n\tprivate lastConnectedDragPreviewOptions: any = null\n\n\tprivate readonly backend: Backend\n\n\tpublic constructor(backend: Backend) {\n\t\tthis.backend = backend\n\t}\n\n\tpublic receiveHandlerId(newHandlerId: Identifier | null): void {\n\t\tif (this.handlerId === newHandlerId) {\n\t\t\treturn\n\t\t}\n\n\t\tthis.handlerId = newHandlerId\n\t\tthis.reconnect()\n\t}\n\n\tpublic get connectTarget(): any {\n\t\treturn this.dragSource\n\t}\n\n\tpublic get dragSourceOptions(): DragSourceOptions | null {\n\t\treturn this.dragSourceOptionsInternal\n\t}\n\tpublic set dragSourceOptions(options: DragSourceOptions | null) {\n\t\tthis.dragSourceOptionsInternal = options\n\t}\n\n\tpublic get dragPreviewOptions(): DragPreviewOptions | null {\n\t\treturn this.dragPreviewOptionsInternal\n\t}\n\n\tpublic set dragPreviewOptions(options: DragPreviewOptions | null) {\n\t\tthis.dragPreviewOptionsInternal = options\n\t}\n\n\tpublic reconnect(): void {\n\t\tconst didChange = this.reconnectDragSource()\n\t\tthis.reconnectDragPreview(didChange)\n\t}\n\n\tprivate reconnectDragSource(): boolean {\n\t\tconst dragSource = this.dragSource\n\t\t// if nothing has changed then don't resubscribe\n\t\tconst didChange =\n\t\t\tthis.didHandlerIdChange() ||\n\t\t\tthis.didConnectedDragSourceChange() ||\n\t\t\tthis.didDragSourceOptionsChange()\n\n\t\tif (didChange) {\n\t\t\tthis.disconnectDragSource()\n\t\t}\n\n\t\tif (!this.handlerId) {\n\t\t\treturn didChange\n\t\t}\n\t\tif (!dragSource) {\n\t\t\tthis.lastConnectedDragSource = dragSource\n\t\t\treturn didChange\n\t\t}\n\n\t\tif (didChange) {\n\t\t\tthis.lastConnectedHandlerId = this.handlerId\n\t\t\tthis.lastConnectedDragSource = dragSource\n\t\t\tthis.lastConnectedDragSourceOptions = this.dragSourceOptions\n\t\t\tthis.dragSourceUnsubscribe = this.backend.connectDragSource(\n\t\t\t\tthis.handlerId,\n\t\t\t\tdragSource,\n\t\t\t\tthis.dragSourceOptions,\n\t\t\t)\n\t\t}\n\t\treturn didChange\n\t}\n\n\tprivate reconnectDragPreview(forceDidChange = false): void {\n\t\tconst dragPreview = this.dragPreview\n\t\t// if nothing has changed then don't resubscribe\n\t\tconst didChange =\n\t\t\tforceDidChange ||\n\t\t\tthis.didHandlerIdChange() ||\n\t\t\tthis.didConnectedDragPreviewChange() ||\n\t\t\tthis.didDragPreviewOptionsChange()\n\n\t\tif (didChange) {\n\t\t\tthis.disconnectDragPreview()\n\t\t}\n\n\t\tif (!this.handlerId) {\n\t\t\treturn\n\t\t}\n\t\tif (!dragPreview) {\n\t\t\tthis.lastConnectedDragPreview = dragPreview\n\t\t\treturn\n\t\t}\n\n\t\tif (didChange) {\n\t\t\tthis.lastConnectedHandlerId = this.handlerId\n\t\t\tthis.lastConnectedDragPreview = dragPreview\n\t\t\tthis.lastConnectedDragPreviewOptions = this.dragPreviewOptions\n\t\t\tthis.dragPreviewUnsubscribe = this.backend.connectDragPreview(\n\t\t\t\tthis.handlerId,\n\t\t\t\tdragPreview,\n\t\t\t\tthis.dragPreviewOptions,\n\t\t\t)\n\t\t}\n\t}\n\n\tprivate didHandlerIdChange(): boolean {\n\t\treturn this.lastConnectedHandlerId !== this.handlerId\n\t}\n\n\tprivate didConnectedDragSourceChange(): boolean {\n\t\treturn this.lastConnectedDragSource !== this.dragSource\n\t}\n\n\tprivate didConnectedDragPreviewChange(): boolean {\n\t\treturn this.lastConnectedDragPreview !== this.dragPreview\n\t}\n\n\tprivate didDragSourceOptionsChange(): boolean {\n\t\treturn !shallowEqual(\n\t\t\tthis.lastConnectedDragSourceOptions,\n\t\t\tthis.dragSourceOptions,\n\t\t)\n\t}\n\n\tprivate didDragPreviewOptionsChange(): boolean {\n\t\treturn !shallowEqual(\n\t\t\tthis.lastConnectedDragPreviewOptions,\n\t\t\tthis.dragPreviewOptions,\n\t\t)\n\t}\n\n\tpublic disconnectDragSource() {\n\t\tif (this.dragSourceUnsubscribe) {\n\t\t\tthis.dragSourceUnsubscribe()\n\t\t\tthis.dragSourceUnsubscribe = undefined\n\t\t}\n\t}\n\n\tpublic disconnectDragPreview() {\n\t\tif (this.dragPreviewUnsubscribe) {\n\t\t\tthis.dragPreviewUnsubscribe()\n\t\t\tthis.dragPreviewUnsubscribe = undefined\n\t\t\tthis.dragPreviewNode = null\n\t\t\tthis.dragPreviewRef = null\n\t\t}\n\t}\n\n\tprivate get dragSource() {\n\t\treturn (\n\t\t\tthis.dragSourceNode || (this.dragSourceRef && this.dragSourceRef.current)\n\t\t)\n\t}\n\n\tprivate get dragPreview() {\n\t\treturn (\n\t\t\tthis.dragPreviewNode ||\n\t\t\t(this.dragPreviewRef && this.dragPreviewRef.current)\n\t\t)\n\t}\n\n\tprivate clearDragSource() {\n\t\tthis.dragSourceNode = null\n\t\tthis.dragSourceRef = null\n\t}\n\n\tprivate clearDragPreview() {\n\t\tthis.dragPreviewNode = null\n\t\tthis.dragPreviewRef = null\n\t}\n}\n", "import { shallowEqual } from '@react-dnd/shallowequal'\nimport type { Backend, Identifier, Unsubscribe } from 'dnd-core'\nimport type { RefObject } from 'react'\n\nimport type { DropTargetOptions } from '../types/index.js'\nimport { isRef } from './isRef.js'\nimport type { Connector } from './SourceConnector.js'\nimport { wrapConnectorHooks } from './wrapConnectorHooks.js'\n\nexport class TargetConnector implements Connector {\n\tpublic hooks = wrapConnectorHooks({\n\t\tdropTarget: (node: any, options: DropTargetOptions) => {\n\t\t\tthis.clearDropTarget()\n\t\t\tthis.dropTargetOptions = options\n\t\t\tif (isRef(node)) {\n\t\t\t\tthis.dropTargetRef = node\n\t\t\t} else {\n\t\t\t\tthis.dropTargetNode = node\n\t\t\t}\n\t\t\tthis.reconnect()\n\t\t},\n\t})\n\n\tprivate handlerId: Identifier | null = null\n\t// The drop target may either be attached via ref or connect function\n\tprivate dropTargetRef: RefObject<any> | null = null\n\tprivate dropTargetNode: any\n\tprivate dropTargetOptionsInternal: DropTargetOptions | null = null\n\tprivate unsubscribeDropTarget: Unsubscribe | undefined\n\n\tprivate lastConnectedHandlerId: Identifier | null = null\n\tprivate lastConnectedDropTarget: any = null\n\tprivate lastConnectedDropTargetOptions: DropTargetOptions | null = null\n\tprivate readonly backend: Backend\n\n\tpublic constructor(backend: Backend) {\n\t\tthis.backend = backend\n\t}\n\n\tpublic get connectTarget(): any {\n\t\treturn this.dropTarget\n\t}\n\n\tpublic reconnect(): void {\n\t\t// if nothing has changed then don't resubscribe\n\t\tconst didChange =\n\t\t\tthis.didHandlerIdChange() ||\n\t\t\tthis.didDropTargetChange() ||\n\t\t\tthis.didOptionsChange()\n\n\t\tif (didChange) {\n\t\t\tthis.disconnectDropTarget()\n\t\t}\n\n\t\tconst dropTarget = this.dropTarget\n\t\tif (!this.handlerId) {\n\t\t\treturn\n\t\t}\n\t\tif (!dropTarget) {\n\t\t\tthis.lastConnectedDropTarget = dropTarget\n\t\t\treturn\n\t\t}\n\n\t\tif (didChange) {\n\t\t\tthis.lastConnectedHandlerId = this.handlerId\n\t\t\tthis.lastConnectedDropTarget = dropTarget\n\t\t\tthis.lastConnectedDropTargetOptions = this.dropTargetOptions\n\n\t\t\tthis.unsubscribeDropTarget = this.backend.connectDropTarget(\n\t\t\t\tthis.handlerId,\n\t\t\t\tdropTarget,\n\t\t\t\tthis.dropTargetOptions,\n\t\t\t)\n\t\t}\n\t}\n\n\tpublic receiveHandlerId(newHandlerId: Identifier | null): void {\n\t\tif (newHandlerId === this.handlerId) {\n\t\t\treturn\n\t\t}\n\n\t\tthis.handlerId = newHandlerId\n\t\tthis.reconnect()\n\t}\n\n\tpublic get dropTargetOptions(): DropTargetOptions {\n\t\treturn this.dropTargetOptionsInternal\n\t}\n\tpublic set dropTargetOptions(options: DropTargetOptions) {\n\t\tthis.dropTargetOptionsInternal = options\n\t}\n\n\tprivate didHandlerIdChange(): boolean {\n\t\treturn this.lastConnectedHandlerId !== this.handlerId\n\t}\n\n\tprivate didDropTargetChange(): boolean {\n\t\treturn this.lastConnectedDropTarget !== this.dropTarget\n\t}\n\n\tprivate didOptionsChange(): boolean {\n\t\treturn !shallowEqual(\n\t\t\tthis.lastConnectedDropTargetOptions,\n\t\t\tthis.dropTargetOptions,\n\t\t)\n\t}\n\n\tpublic disconnectDropTarget() {\n\t\tif (this.unsubscribeDropTarget) {\n\t\t\tthis.unsubscribeDropTarget()\n\t\t\tthis.unsubscribeDropTarget = undefined\n\t\t}\n\t}\n\n\tprivate get dropTarget() {\n\t\treturn (\n\t\t\tthis.dropTargetNode || (this.dropTargetRef && this.dropTargetRef.current)\n\t\t)\n\t}\n\n\tprivate clearDropTarget() {\n\t\tthis.dropTargetRef = null\n\t\tthis.dropTargetNode = null\n\t}\n}\n", "import { invariant } from '@react-dnd/invariant'\nimport type { DragDropManager } from 'dnd-core'\nimport { useContext } from 'react'\n\nimport { DndContext } from '../core/index.js'\n\n/**\n * A hook to retrieve the DragDropManager from Context\n */\nexport function useDragDropManager(): DragDropManager {\n\tconst { dragDropManager } = useContext(DndContext)\n\tinvariant(dragDropManager != null, 'Expected drag drop context')\n\treturn dragDropManager as DragDropManager\n}\n", "import { useMemo } from 'react'\n\nimport { DragSourceMonitorImpl } from '../../internals/index.js'\nimport type { DragSourceMonitor } from '../../types/index.js'\nimport { useDragDropManager } from '../useDragDropManager.js'\n\nexport function useDragSourceMonitor<O, R>(): DragSourceMonitor<O, R> {\n\tconst manager = useDragDropManager()\n\treturn useMemo<DragSourceMonitor<O, R>>(\n\t\t() => new DragSourceMonitorImpl(manager),\n\t\t[manager],\n\t)\n}\n", "import { useEffect, useMemo } from 'react'\n\nimport type { Connector } from '../../internals/index.js'\nimport type { DragSourceMonitor } from '../../types/index.js'\nimport type { DragSourceHookSpec } from '../types.js'\nimport { DragSourceImpl } from './DragSourceImpl.js'\n\nexport function useDragSource<O, R, P>(\n\tspec: DragSourceHookSpec<O, R, P>,\n\tmonitor: DragSourceMonitor<O, R>,\n\tconnector: Connector,\n) {\n\tconst handler = useMemo(\n\t\t() => new DragSourceImpl(spec, monitor, connector),\n\t\t[monitor, connector],\n\t)\n\tuseEffect(() => {\n\t\thandler.spec = spec\n\t}, [spec])\n\treturn handler\n}\n", "import type { DragDropMonitor, DragSource, Identifier } from 'dnd-core'\n\nimport type { Connector } from '../../internals/index.js'\nimport type { DragSourceMonitor } from '../../types/index.js'\nimport type { DragObjectFactory, DragSourceHookSpec } from '../types.js'\n\nexport class DragSourceImpl<O, R, P> implements DragSource {\n\tpublic constructor(\n\t\tpublic spec: DragSourceHookSpec<O, R, P>,\n\t\tprivate monitor: DragSourceMonitor<O, R>,\n\t\tprivate connector: Connector,\n\t) {}\n\n\tpublic beginDrag() {\n\t\tconst spec = this.spec\n\t\tconst monitor = this.monitor\n\n\t\tlet result: O | null = null\n\t\tif (typeof spec.item === 'object') {\n\t\t\tresult = spec.item as O\n\t\t} else if (typeof spec.item === 'function') {\n\t\t\tresult = (spec.item as DragObjectFactory<O>)(monitor)\n\t\t} else {\n\t\t\tresult = {} as O\n\t\t}\n\t\treturn result ?? null\n\t}\n\n\tpublic canDrag() {\n\t\tconst spec = this.spec\n\t\tconst monitor = this.monitor\n\t\tif (typeof spec.canDrag === 'boolean') {\n\t\t\treturn spec.canDrag\n\t\t} else if (typeof spec.canDrag === 'function') {\n\t\t\treturn spec.canDrag(monitor)\n\t\t} else {\n\t\t\treturn true\n\t\t}\n\t}\n\n\tpublic isDragging(globalMonitor: DragDropMonitor, target: Identifier) {\n\t\tconst spec = this.spec\n\t\tconst monitor = this.monitor\n\t\tconst { isDragging } = spec\n\t\treturn isDragging\n\t\t\t? isDragging(monitor)\n\t\t\t: target === globalMonitor.getSourceId()\n\t}\n\n\tpublic endDrag() {\n\t\tconst spec = this.spec\n\t\tconst monitor = this.monitor\n\t\tconst connector = this.connector\n\t\tconst { end } = spec\n\t\tif (end) {\n\t\t\tend(monitor.getItem(), monitor)\n\t\t}\n\t\tconnector.reconnect()\n\t}\n}\n", "import { invariant } from '@react-dnd/invariant'\nimport type { Identifier } from 'dnd-core'\nimport { useMemo } from 'react'\n\nimport type { DragSourceHookSpec } from '../types.js'\n\nexport function useDragType(\n\tspec: DragSourceHookSpec<any, any, any>,\n): Identifier {\n\treturn useMemo(() => {\n\t\tconst result: Identifier = spec.type\n\t\tinvariant(result != null, 'spec.type must be defined')\n\t\treturn result\n\t}, [spec])\n}\n", "import type { SourceConnector } from '../../internals/index.js'\nimport { registerSource } from '../../internals/index.js'\nimport type { DragSourceMonitor } from '../../types/index.js'\nimport type { DragSourceHookSpec } from '../types.js'\nimport { useDragDropManager } from '../useDragDropManager.js'\nimport { useIsomorphicLayoutEffect } from '../useIsomorphicLayoutEffect.js'\nimport { useDragSource } from './useDragSource.js'\nimport { useDragType } from './useDragType.js'\n\nexport function useRegisteredDragSource<O, R, P>(\n\tspec: DragSourceHookSpec<O, R, P>,\n\tmonitor: DragSourceMonitor<O, R>,\n\tconnector: SourceConnector,\n): void {\n\tconst manager = useDragDropManager()\n\tconst handler = useDragSource(spec, monitor, connector)\n\tconst itemType = useDragType(spec)\n\n\tuseIsomorphicLayoutEffect(\n\t\tfunction registerDragSource() {\n\t\t\tif (itemType != null) {\n\t\t\t\tconst [handlerId, unregister] = registerSource(\n\t\t\t\t\titemType,\n\t\t\t\t\thandler,\n\t\t\t\t\tmanager,\n\t\t\t\t)\n\t\t\t\tmonitor.receiveHandlerId(handlerId)\n\t\t\t\tconnector.receiveHandlerId(handlerId)\n\t\t\t\treturn unregister\n\t\t\t}\n\t\t\treturn\n\t\t},\n\t\t[manager, monitor, connector, handler, itemType],\n\t)\n}\n", "import { invariant } from '@react-dnd/invariant'\n\nimport type {\n\tConnectDragPreview,\n\tConnectDragSource,\n} from '../../types/index.js'\nimport type { DragSourceHookSpec, FactoryOrInstance } from '../types.js'\nimport { useCollectedProps } from '../useCollectedProps.js'\nimport { useOptionalFactory } from '../useOptionalFactory.js'\nimport { useConnectDragPreview, useConnectDragSource } from './connectors.js'\nimport { useDragSourceConnector } from './useDragSourceConnector.js'\nimport { useDragSourceMonitor } from './useDragSourceMonitor.js'\nimport { useRegisteredDragSource } from './useRegisteredDragSource.js'\n\n/**\n * useDragSource hook\n * @param sourceSpec The drag source specification (object or function, function preferred)\n * @param deps The memoization deps array to use when evaluating spec changes\n */\nexport function useDrag<\n\tDragObject = unknown,\n\tDropResult = unknown,\n\tCollectedProps = unknown,\n>(\n\tspecArg: FactoryOrInstance<\n\t\tDragSourceHookSpec<DragObject, DropResult, CollectedProps>\n\t>,\n\tdeps?: unknown[],\n): [CollectedProps, ConnectDragSource, ConnectDragPreview] {\n\tconst spec = useOptionalFactory(specArg, deps)\n\tinvariant(\n\t\t!(spec as any).begin,\n\t\t`useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)`,\n\t)\n\n\tconst monitor = useDragSourceMonitor<DragObject, DropResult>()\n\tconst connector = useDragSourceConnector(spec.options, spec.previewOptions)\n\tuseRegisteredDragSource(spec, monitor, connector)\n\n\treturn [\n\t\tuseCollectedProps(spec.collect, monitor, connector),\n\t\tuseConnectDragSource(connector),\n\t\tuseConnectDragPreview(connector),\n\t]\n}\n", "import { useEffect } from 'react'\n\nimport type { DragLayerMonitor } from '../types/index.js'\nimport { useCollector } from './useCollector.js'\nimport { useDragDropManager } from './useDragDropManager.js'\n\n/**\n * useDragLayer Hook\n * @param collector The property collector\n */\nexport function useDragLayer<CollectedProps, DragObject = any>(\n\tcollect: (monitor: DragLayerMonitor<DragObject>) => CollectedProps,\n): CollectedProps {\n\tconst dragDropManager = useDragDropManager()\n\tconst monitor = dragDropManager.getMonitor()\n\tconst [collected, updateCollected] = useCollector(monitor, collect)\n\n\tuseEffect(() => monitor.subscribeToOffsetChange(updateCollected))\n\tuseEffect(() => monitor.subscribeToStateChange(updateCollected))\n\treturn collected\n}\n", "import { useMemo } from 'react'\n\nimport type { TargetConnector } from '../../internals/index.js'\n\nexport function useConnectDropTarget(connector: TargetConnector) {\n\treturn useMemo(() => connector.hooks.dropTarget(), [connector])\n}\n", "import { useMemo } from 'react'\n\nimport { TargetConnector } from '../../internals/index.js'\nimport type { DropTargetOptions } from '../../types/index.js'\nimport { useDragDropManager } from '../useDragDropManager.js'\nimport { useIsomorphicLayoutEffect } from '../useIsomorphicLayoutEffect.js'\n\nexport function useDropTargetConnector(\n\toptions: DropTargetOptions,\n): TargetConnector {\n\tconst manager = useDragDropManager()\n\tconst connector = useMemo(\n\t\t() => new TargetConnector(manager.getBackend()),\n\t\t[manager],\n\t)\n\tuseIsomorphicLayoutEffect(() => {\n\t\tconnector.dropTargetOptions = options || null\n\t\tconnector.reconnect()\n\t\treturn () => connector.disconnectDropTarget()\n\t}, [options])\n\treturn connector\n}\n", "import { useMemo } from 'react'\n\nimport { DropTargetMonitorImpl } from '../../internals/index.js'\nimport type { DropTargetMonitor } from '../../types/index.js'\nimport { useDragDropManager } from '../useDragDropManager.js'\n\nexport function useDropTargetMonitor<O, R>(): DropTargetMonitor<O, R> {\n\tconst manager = useDragDropManager()\n\treturn useMemo(() => new DropTargetMonitorImpl(manager), [manager])\n}\n", "import { invariant } from '@react-dnd/invariant'\nimport type { Identifier } from 'dnd-core'\nimport { useMemo } from 'react'\n\nimport type { DropTargetHookSpec } from '../types.js'\n\n/**\n * Internal utility hook to get an array-version of spec.accept.\n * The main utility here is that we aren't creating a new array on every render if a non-array spec.accept is passed in.\n * @param spec\n */\nexport function useAccept<O, R, P>(\n\tspec: DropTargetHookSpec<O, R, P>,\n): Identifier[] {\n\tconst { accept } = spec\n\treturn useMemo(() => {\n\t\tinvariant(spec.accept != null, 'accept must be defined')\n\t\treturn Array.isArray(accept) ? accept : [accept]\n\t}, [accept])\n}\n", "import { useEffect, useMemo } from 'react'\n\nimport type { DropTargetMonitor } from '../../types/index.js'\nimport type { DropTargetHookSpec } from '../types.js'\nimport { DropTargetImpl } from './DropTargetImpl.js'\n\nexport function useDropTarget<O, R, P>(\n\tspec: DropTargetHookSpec<O, R, P>,\n\tmonitor: DropTargetMonitor<O, R>,\n) {\n\tconst dropTarget = useMemo(() => new DropTargetImpl(spec, monitor), [monitor])\n\tuseEffect(() => {\n\t\tdropTarget.spec = spec\n\t}, [spec])\n\treturn dropTarget\n}\n", "import type { DropTarget } from 'dnd-core'\n\nimport type { DropTargetMonitor } from '../../types/index.js'\nimport type { DropTargetHookSpec } from '../types.js'\n\nexport class DropTargetImpl<O, R, P> implements DropTarget {\n\tpublic constructor(\n\t\tpublic spec: DropTargetHookSpec<O, R, P>,\n\t\tprivate monitor: DropTargetMonitor<O, R>,\n\t) {}\n\n\tpublic canDrop() {\n\t\tconst spec = this.spec\n\t\tconst monitor = this.monitor\n\t\treturn spec.canDrop ? spec.canDrop(monitor.getItem(), monitor) : true\n\t}\n\n\tpublic hover() {\n\t\tconst spec = this.spec\n\t\tconst monitor = this.monitor\n\t\tif (spec.hover) {\n\t\t\tspec.hover(monitor.getItem(), monitor)\n\t\t}\n\t}\n\n\tpublic drop() {\n\t\tconst spec = this.spec\n\t\tconst monitor = this.monitor\n\t\tif (spec.drop) {\n\t\t\treturn spec.drop(monitor.getItem(), monitor)\n\t\t}\n\t\treturn\n\t}\n}\n", "import type { TargetConnector } from '../../internals/index.js'\nimport { registerTarget } from '../../internals/index.js'\nimport type { DropTargetMonitor } from '../../types/index.js'\nimport type { DropTargetHookSpec } from '../types.js'\nimport { useDragDropManager } from '../useDragDropManager.js'\nimport { useIsomorphicLayoutEffect } from '../useIsomorphicLayoutEffect.js'\nimport { useAccept } from './useAccept.js'\nimport { useDropTarget } from './useDropTarget.js'\n\nexport function useRegisteredDropTarget<O, R, P>(\n\tspec: DropTargetHookSpec<O, R, P>,\n\tmonitor: DropTargetMonitor<O, R>,\n\tconnector: TargetConnector,\n): void {\n\tconst manager = useDragDropManager()\n\tconst dropTarget = useDropTarget(spec, monitor)\n\tconst accept = useAccept(spec)\n\n\tuseIsomorphicLayoutEffect(\n\t\tfunction registerDropTarget() {\n\t\t\tconst [handlerId, unregister] = registerTarget(\n\t\t\t\taccept,\n\t\t\t\tdropTarget,\n\t\t\t\tmanager,\n\t\t\t)\n\t\t\tmonitor.receiveHandlerId(handlerId)\n\t\t\tconnector.receiveHandlerId(handlerId)\n\t\t\treturn unregister\n\t\t},\n\t\t[\n\t\t\tmanager,\n\t\t\tmonitor,\n\t\t\tdropTarget,\n\t\t\tconnector,\n\t\t\taccept.map((a) => a.toString()).join('|'),\n\t\t],\n\t)\n}\n", "import type { ConnectDropTarget } from '../../types/index.js'\nimport type { DropTargetHookSpec, FactoryOrInstance } from '../types.js'\nimport { useCollectedProps } from '../useCollectedProps.js'\nimport { useOptionalFactory } from '../useOptionalFactory.js'\nimport { useConnectDropTarget } from './connectors.js'\nimport { useDropTargetConnector } from './useDropTargetConnector.js'\nimport { useDropTargetMonitor } from './useDropTargetMonitor.js'\nimport { useRegisteredDropTarget } from './useRegisteredDropTarget.js'\n\n/**\n * useDropTarget Hook\n * @param spec The drop target specification (object or function, function preferred)\n * @param deps The memoization deps array to use when evaluating spec changes\n */\nexport function useDrop<\n\tDragObject = unknown,\n\tDropResult = unknown,\n\tCollectedProps = unknown,\n>(\n\tspecArg: FactoryOrInstance<\n\t\tDropTargetHookSpec<DragObject, DropResult, CollectedProps>\n\t>,\n\tdeps?: unknown[],\n): [CollectedProps, ConnectDropTarget] {\n\tconst spec = useOptionalFactory(specArg, deps)\n\tconst monitor = useDropTargetMonitor<DragObject, DropResult>()\n\tconst connector = useDropTargetConnector(spec.options)\n\tuseRegisteredDropTarget(spec, monitor, connector)\n\n\treturn [\n\t\tuseCollectedProps(spec.collect, monitor, connector),\n\t\tuseConnectDropTarget(connector),\n\t]\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAMA,WAAO,UAAU,SAASA,OAAM,GAAG,GAAG;AACpC,UAAI,MAAM,EAAG,QAAO;AAEpB,UAAI,KAAK,KAAK,OAAO,KAAK,YAAY,OAAO,KAAK,UAAU;AAC1D,YAAI,EAAE,gBAAgB,EAAE,YAAa,QAAO;AAE5C,YAAI,QAAQ,GAAG;AACf,YAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,mBAAS,EAAE;AACX,cAAI,UAAU,EAAE,OAAQ,QAAO;AAC/B,eAAK,IAAI,QAAQ,QAAQ;AACvB,gBAAI,CAACA,OAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAG,QAAO;AACjC,iBAAO;AAAA,QACT;AAIA,YAAI,EAAE,gBAAgB,OAAQ,QAAO,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE;AAC5E,YAAI,EAAE,YAAY,OAAO,UAAU,QAAS,QAAO,EAAE,QAAQ,MAAM,EAAE,QAAQ;AAC7E,YAAI,EAAE,aAAa,OAAO,UAAU,SAAU,QAAO,EAAE,SAAS,MAAM,EAAE,SAAS;AAEjF,eAAO,OAAO,KAAK,CAAC;AACpB,iBAAS,KAAK;AACd,YAAI,WAAW,OAAO,KAAK,CAAC,EAAE,OAAQ,QAAO;AAE7C,aAAK,IAAI,QAAQ,QAAQ;AACvB,cAAI,CAAC,OAAO,UAAU,eAAe,KAAK,GAAG,KAAK,CAAC,CAAC,EAAG,QAAO;AAEhE,aAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,cAAI,MAAM,KAAK,CAAC;AAEhB,cAAI,CAACA,OAAM,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,EAAG,QAAO;AAAA,QACrC;AAEA,eAAO;AAAA,MACT;AAGA,aAAO,MAAI,KAAK,MAAI;AAAA,IACtB;AAAA;AAAA;;;;;;AClCO,SAASC,UAAUC,WAAgBC,WAAmBC,MAAa;AACzE,MAAIC,aAAY,GAAI;AACnB,QAAIF,WAAWG,QAAW;AACzB,YAAM,IAAIC,MAAM,8CAA8C;;;AAIhE,MAAI,CAACL,WAAW;AACf,QAAIM;AACJ,QAAIL,WAAWG,QAAW;AACzBE,cAAQ,IAAID,MACX,+HAC8D;WAEzD;AACN,UAAIE,WAAW;AACfD,cAAQ,IAAID,MACXJ,OAAOO,QAAO,OAAQ,WAAY;AACjC,eAAON,KAAKK,UAAU;OACtB,CAAC;AAEHD,YAAMG,OAAO;;AAGZH,UAAcI,cAAc;AAC9B,UAAMJ;;;AAIR,SAASH,eAAe;AACvB,SACC,OAAOQ,YAAY,eAAeA;;;;AClC7B,SAASC,IAAOC,KAAUC,MAAcC,cAAoB;AAClE,SAAOD,KACLE,MAAM,GAAG,EACTC;IAAO,CAACC,GAAGC,MAAOD,KAAKA,EAAEC,CAAC,IAAID,EAAEC,CAAC,IAAIJ,gBAAgB;IAAOF;EAAG;;AAM3D,SAASO,QAAWC,OAAYC,MAAc;AACpD,SAAOD,MAAME;IAAO,CAACC,MAAMA,MAAMF;EAAI;;AAe/B,SAASG,SAASC,OAAqB;AAC7C,SAAO,OAAOA,UAAU;;AAQlB,SAASC,IAA+BC,QAAaC,QAAkB;AAC7E,QAAMC,MAAM,oBAAIC,IAAG;AACnB,QAAMC,aAAa,CAACC,SAAY;AAC/BH,QAAII,IAAID,MAAMH,IAAIK,IAAIF,IAAI,IAAKH,IAAIM,IAAIH,IAAI,IAAe,IAAI,CAAC;;AAEhEL,SAAOS,QAAQL,UAAU;AACzBH,SAAOQ,QAAQL,UAAU;AAEzB,QAAMM,SAAc,CAAA;AACpBR,MAAIO,QAAQ,CAACE,OAAOC,QAAQ;AAC3B,QAAID,UAAU,GAAG;AAChBD,aAAOG,KAAKD,GAAG;;GAEhB;AACD,SAAOF;;AAQD,SAASI,aAAgBd,QAAaC,QAAkB;AAC9D,SAAOD,OAAOe;IAAO,CAACC,MAAMf,OAAOgB,QAAQD,CAAC,IAAI;EAAE;;;;ACjE5C,IAAME,cAAc;AACpB,IAAMC,aAAa;AACnB,IAAMC,sBAAsB;AAC5B,IAAMC,QAAQ;AACd,IAAMC,OAAO;AACb,IAAMC,WAAW;;;ACAjB,SAASC,gBACfC,cACAC,oBACY;AACZ,SAAO;IACNC,MAAMC;IACNC,SAAS;MACRH,oBAAoBA,sBAAsB;MAC1CD,cAAcA,gBAAgB;;;;;;ACGjC,IAAMK,yBAAyB;EAC9BC,MAAMC;EACNC,SAAS;IACRC,cAAc;IACdC,oBAAoB;;;AAIf,SAASC,gBAAgBC,SAA0B;AACzD,SAAO,SAASC,UACfC,YAA0B,CAAA,GAC1BC,UAA4B;IAC3BC,eAAe;KAEuB;AACvC,UAAM,EACLA,gBAAgB,MAChBP,cACAQ,uBAAAA,uBAAqB,IACAF;AACtB,UAAMG,UAAUN,QAAQO,WAAU;AAClC,UAAMC,WAAWR,QAAQS,YAAW;AAGpCT,YAAQU,SAASC,gBAAgBd,YAAY,CAAC;AAE9Ce,qBAAiBV,WAAWI,SAASE,QAAQ;AAG7C,UAAMK,WAAWC,mBAAmBZ,WAAWI,OAAO;AACtD,QAAIO,YAAY,MAAM;AACrBb,cAAQU,SAASjB,sBAAsB;AACvC;;AAID,QAAIK,qBAAqC;AACzC,QAAID,cAAc;AACjB,UAAI,CAACQ,wBAAuB;AAC3B,cAAM,IAAIU,MAAM,uCAAuC;;AAExDC,4CAAsCX,sBAAqB;AAC3DP,2BAAqBO,uBAAsBQ,QAAQ;;AAIpDb,YAAQU,SAASC,gBAAgBd,cAAcC,kBAAkB,CAAC;AAElE,UAAMmB,SAAST,SAASU,UAAUL,QAAQ;AAC1C,UAAMM,OAAOF,OAAOhB,UAAUK,SAASO,QAAQ;AAE/C,QAAIM,QAAQ,MAAM;AACjB,aAAOC;;AAERC,uBAAmBF,IAAI;AACvBX,aAASc,UAAUT,QAAQ;AAE3B,UAAMU,WAAWf,SAASgB,cAAcX,QAAQ;AAChD,WAAO;MACNnB,MAAM+B;MACN7B,SAAS;QACR2B;QACAJ;QACAN;QACAhB,cAAcA,gBAAgB;QAC9BC,oBAAoBA,sBAAsB;QAC1C4B,gBAAgB,CAAC,CAACtB;;;;;AAMtB,SAASQ,iBACRV,WACAI,SACAE,UACC;AACDmB,YAAU,CAACrB,QAAQsB,WAAU,GAAI,uCAAuC;AACxE1B,YAAU2B,QAAQ,SAAUhB,UAAU;AACrCc,cACCnB,SAASU,UAAUL,QAAQ,GAC3B,sCAAsC;GAEvC;;AAGF,SAASG,sCAAsCX,wBAA4B;AAC1EsB,YACC,OAAOtB,2BAA0B,YACjC,0EAA0E;;AAI5E,SAASgB,mBAAmBF,MAAW;AACtCQ,YAAUG,SAASX,IAAI,GAAG,yBAAyB;;AAGpD,SAASL,mBAAmBZ,WAAyBI,SAA0B;AAC9E,MAAIO,WAAW;AACf,WAASkB,IAAI7B,UAAU8B,SAAS,GAAGD,KAAK,GAAGA,KAAK;AAC/C,QAAIzB,QAAQ2B,cAAc/B,UAAU6B,CAAC,CAAC,GAAG;AACxClB,iBAAWX,UAAU6B,CAAC;AACtB;;;AAGF,SAAOlB;;;;ACzHR,SAAA,gBAAA,KAAA,KAAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaO,SAASqB,WAAWC,SAA0B;AACpD,SAAO,SAASC,KAAKC,UAAU,CAAA,GAAU;AACxC,UAAMC,UAAUH,QAAQI,WAAU;AAClC,UAAMC,WAAWL,QAAQM,YAAW;AACpCC,IAAAA,kBAAiBJ,OAAO;AACxB,UAAMK,YAAYC,oBAAoBN,OAAO;AAG7CK,cAAUE,QAAQ,CAACC,UAAUC,UAAU;AACtC,YAAMC,aAAaC,oBAAoBH,UAAUC,OAAOP,UAAUF,OAAO;AACzE,YAAMY,SAA8B;QACnCC,MAAMC;QACNC,SAAS;UACRL,YAAY,cAAA,CAAA,GACRX,SACAW,UAAU;;;AAIhBb,cAAQmB,SAASJ,MAAM;KACvB;;;AAIH,SAASR,kBAAiBJ,SAA0B;AACnDiB,YAAUjB,QAAQkB,WAAU,GAAI,sCAAsC;AACtED,YACC,CAACjB,QAAQmB,QAAO,GAChB,mDAAmD;;AAIrD,SAASR,oBACRH,UACAC,OACAP,UACAF,SACC;AACD,QAAMoB,SAASlB,SAASmB,UAAUb,QAAQ;AAC1C,MAAIE,aAAaU,SAASA,OAAOtB,KAAKE,SAASQ,QAAQ,IAAIc;AAC3DC,uBAAqBb,UAAU;AAC/B,MAAI,OAAOA,eAAe,aAAa;AACtCA,iBAAaD,UAAU,IAAI,CAAA,IAAKT,QAAQwB,cAAa;;AAEtD,SAAOd;;AAGR,SAASa,qBAAqBb,YAAiB;AAC9CO,YACC,OAAOP,eAAe,eAAee,SAASf,UAAU,GACxD,oDAAoD;;AAItD,SAASJ,oBAAoBN,SAA0B;AACtD,QAAMK,YAAYL,QAChB0B,aAAY,EACZC,OAAO3B,QAAQ4B,iBAAiB5B,OAAO;AACzCK,YAAUwB,QAAO;AACjB,SAAOxB;;;;AC/DD,SAASyB,cAAcC,SAA0B;AACvD,SAAO,SAASC,UAA0B;AACzC,UAAMC,UAAUF,QAAQG,WAAU;AAClC,UAAMC,WAAWJ,QAAQK,YAAW;AACpCC,qBAAiBJ,OAAO;AAExB,UAAMK,WAAWL,QAAQM,YAAW;AACpC,QAAID,YAAY,MAAM;AACrB,YAAME,SAASL,SAASM,UAAUH,UAAU,IAAI;AAChDE,aAAOR,QAAQC,SAASK,QAAQ;AAChCH,eAASO,YAAW;;AAErB,WAAO;MAAEC,MAAMC;;;;AAIjB,SAASP,iBAAiBJ,SAA0B;AACnDY,YAAUZ,QAAQa,WAAU,GAAI,yCAAyC;;;;ACxBnE,SAASC,YACfC,YACAC,iBACU;AACV,MAAIA,oBAAoB,MAAM;AAC7B,WAAOD,eAAe;;AAEvB,SAAOE,MAAMC,QAAQH,UAAU,IAC3BA,WAA4BI;IAAK,CAACC,MAAMA,MAAMJ;EAAe,IAC9DD,eAAeC;;;;ACGZ,SAASK,YAAYC,SAA0B;AACrD,SAAO,SAASC,MACfC,cACA,EAAEC,aAAY,IAAmB,CAAA,GACV;AACvBC,2BAAuBF,YAAY;AACnC,UAAMG,YAAYH,aAAaI,MAAM,CAAC;AACtC,UAAMC,UAAUP,QAAQQ,WAAU;AAClC,UAAMC,WAAWT,QAAQU,YAAW;AACpC,UAAMC,kBAAkBJ,QAAQK,YAAW;AAC3CC,+BAA2BR,WAAWI,UAAUE,eAAe;AAC/DG,oBAAgBT,WAAWE,SAASE,QAAQ;AAC5CM,oBAAgBV,WAAWE,SAASE,QAAQ;AAE5C,WAAO;MACNO,MAAMC;MACNC,SAAS;QACRb;QACAF,cAAcA,gBAAgB;;;;;AAMlC,SAASC,uBAAuBF,cAAwB;AACvDiB,YAAUC,MAAMC,QAAQnB,YAAY,GAAG,oCAAoC;;AAG5E,SAASY,gBACRT,WACAE,SACAE,UACC;AACDU,YAAUZ,QAAQe,WAAU,GAAI,uCAAuC;AACvEH,YAAU,CAACZ,QAAQgB,QAAO,GAAI,+BAA+B;AAC7D,WAASC,IAAI,GAAGA,IAAInB,UAAUoB,QAAQD,KAAK;AAC1C,UAAME,WAAWrB,UAAUmB,CAAC;AAC5BL,cACCd,UAAUsB,YAAYD,QAAQ,MAAMF,GACpC,sDAAsD;AAGvD,UAAMI,SAASnB,SAASoB,UAAUH,QAAQ;AAC1CP,cAAUS,QAAQ,sCAAsC;;;AAI1D,SAASf,2BACRR,WACAI,UACAE,iBACC;AAID,WAASa,IAAInB,UAAUoB,SAAS,GAAGD,KAAK,GAAGA,KAAK;AAC/C,UAAME,WAAWrB,UAAUmB,CAAC;AAC5B,UAAMM,aAAarB,SAASsB,cAAcL,QAAQ;AAClD,QAAI,CAACM,YAAYF,YAAYnB,eAAe,GAAG;AAC9CN,gBAAU4B,OAAOT,GAAG,CAAC;;;;AAKxB,SAAST,gBACRV,WACAE,SACAE,UACC;AAEDJ,YAAU6B,QAAQ,SAAUR,UAAU;AACrC,UAAME,SAASnB,SAASoB,UAAUH,QAAQ;AAC1CE,WAAO3B,MAAMM,SAASmB,QAAQ;GAC9B;;;;ACpFK,SAASS,wBAAwBC,SAA0B;AACjE,SAAO,SAASC,oBAAgD;AAC/D,UAAMC,UAAUF,QAAQG,WAAU;AAClC,QAAID,QAAQE,WAAU,GAAI;AACzB,aAAO;QAAEC,MAAMC;;;AAEhB;;;;;ACAK,SAASC,sBACfC,SACkB;AAClB,SAAO;IACNC,WAAWC,gBAAgBF,OAAO;IAClCG,mBAAmBC,wBAAwBJ,OAAO;IAClDK,OAAOC,YAAYN,OAAO;IAC1BO,MAAMC,WAAWR,OAAO;IACxBS,SAASC,cAAcV,OAAO;;;;;ACHzB,IAAMW,sBAAN,MAAyB;EAYxBC,eAAeC,SAAwB;AAC7C,SAAKA,UAAUA;;EAGTC,aAA8B;AACpC,WAAO,KAAKC;;EAGNC,aAAsB;AAC5B,WAAO,KAAKH;;EAGNI,cAA+B;AACrC,WAAQ,KAAKF,QAAgCG;;EAGvCC,aAA8B;AAEpC,UAAMC,UAAU;AAChB,UAAM,EAAEC,SAAQ,IAAK,KAAKC;AAE1B,aAASC,kBAAkBC,eAAmC;AAC7D,aAAO,IAAIC,SAAgB;AAC1B,cAAMC,SAASF,cAAcG,MAAMP,SAASK,IAAI;AAChD,YAAI,OAAOC,WAAW,aAAa;AAClCL,mBAASK,MAAM;;;;AAKlB,UAAME,UAAUC,sBAAsB,IAAI;AAE1C,WAAOC,OAAOC,KAAKH,OAAO,EAAEI,OAC3B,CAACC,cAA+BC,QAAgB;AAC/C,YAAMR,SAA8BE,QACnCM,GAAG;AAEFD,mBAAqBC,GAAG,IAAIX,kBAAkBG,MAAM;AACtD,aAAOO;OAER,CAAA,CAAE;;EAIGZ,SAASK,QAA2B;AAC1C,SAAKJ,MAAMD,SAASK,MAAM;;EAnD3B,YAAmBJ,OAAqBP,SAA0B;AAFlE,SAAQoB,UAAU;AAwDlB,SAAQC,uBAAuB,MAAY;AAC1C,YAAMC,cAAc,KAAKf,MAAMgB,SAAQ,EAAGC,WAAW;AACrD,UAAI,KAAK1B,SAAS;AACjB,YAAIwB,eAAe,CAAC,KAAKF,SAAS;AACjC,eAAKtB,QAAQ2B,MAAK;AAClB,eAAKL,UAAU;mBACL,CAACE,eAAe,KAAKF,SAAS;AACxC,eAAKtB,QAAQ4B,SAAQ;AACrB,eAAKN,UAAU;;;;AA7DjB,SAAKb,QAAQA;AACb,SAAKP,UAAUA;AACfO,UAAMoB,UAAU,KAAKN,oBAAoB;;;;;ACfpC,SAASO,IAAIC,GAAYC,GAAqB;AACpD,SAAO;IACNC,GAAGF,EAAEE,IAAID,EAAEC;IACXC,GAAGH,EAAEG,IAAIF,EAAEE;;;AASN,SAASC,SAASJ,GAAYC,GAAqB;AACzD,SAAO;IACNC,GAAGF,EAAEE,IAAID,EAAEC;IACXC,GAAGH,EAAEG,IAAIF,EAAEE;;;AAYN,SAASE,sBAAsBC,OAA8B;AACnE,QAAM,EAAEC,cAAcC,qBAAqBC,0BAAyB,IAAKH;AACzE,MAAI,CAACC,gBAAgB,CAACC,uBAAuB,CAACC,2BAA2B;AACxE,WAAO;;AAER,SAAOL,SACNL,IAAIQ,cAAcE,yBAAyB,GAC3CD,mBAAmB;;AASd,SAASE,+BAA+BJ,OAA8B;AAC5E,QAAM,EAAEC,cAAcC,oBAAmB,IAAKF;AAC9C,MAAI,CAACC,gBAAgB,CAACC,qBAAqB;AAC1C,WAAO;;AAER,SAAOJ,SAASG,cAAcC,mBAAmB;;;;ACtD3C,IAAMG,OAAiB,CAAA;AACvB,IAAMC,MAAgB,CAAA;AAE3BD,KAAaE,cAAc;AAC3BD,IAAYE,aAAa;AAQpB,SAASC,SACfC,UACAC,YACU;AACV,MAAID,aAAaL,MAAM;AACtB,WAAO;;AAGR,MAAIK,aAAaJ,OAAO,OAAOK,eAAe,aAAa;AAC1D,WAAO;;AAGR,QAAMC,YAAYC,aAAaF,YAAYD,QAAQ;AACnD,SAAOE,UAAUE,SAAS;;;;ACRpB,IAAMC,sBAAN,MAAyB;EASxBC,uBACNC,UACAC,UAAqC,CAAA,GACvB;AACd,UAAM,EAAEC,WAAU,IAAKD;AACvBE,cAAU,OAAOH,aAAa,YAAY,8BAA8B;AACxEG,cACC,OAAOD,eAAe,eAAeE,MAAMC,QAAQH,UAAU,GAC7D,0DAA0D;AAG3D,QAAII,cAAc,KAAKC,MAAMC,SAAQ,EAAGC;AACxC,UAAMC,eAAe,MAAM;AAC1B,YAAMC,QAAQ,KAAKJ,MAAMC,SAAQ;AACjC,YAAMI,iBAAiBD,MAAMF;AAC7B,UAAI;AACH,cAAMI,kBACLD,mBAAmBN,eAClBM,mBAAmBN,cAAc,KACjC,CAACQ,SAASH,MAAMI,iBAAiBb,UAAU;AAE7C,YAAI,CAACW,iBAAiB;AACrBb,mBAAQ;;gBAET;AACAM,sBAAcM;;;AAIhB,WAAO,KAAKL,MAAMS,UAAUN,YAAY;;EAGlCO,wBAAwBjB,UAAiC;AAC/DG,cAAU,OAAOH,aAAa,YAAY,8BAA8B;AAExE,QAAIkB,gBAAgB,KAAKX,MAAMC,SAAQ,EAAGW;AAC1C,UAAMT,eAAe,MAAM;AAC1B,YAAMU,YAAY,KAAKb,MAAMC,SAAQ,EAAGW;AACxC,UAAIC,cAAcF,eAAe;AAChC;;AAGDA,sBAAgBE;AAChBpB,eAAQ;;AAGT,WAAO,KAAKO,MAAMS,UAAUN,YAAY;;EAGlCW,cAAcC,UAAuC;AAC3D,QAAI,CAACA,UAAU;AACd,aAAO;;AAER,UAAMC,SAAS,KAAKC,SAASC,UAAUH,QAAQ;AAC/CnB,cAAUoB,QAAQ,6CAA6CD,QAAQ,EAAE;AAEzE,QAAI,KAAKI,WAAU,GAAI;AACtB,aAAO;;AAGR,WAAOH,OAAOI,QAAQ,MAAML,QAAQ;;EAG9BM,gBAAgBC,UAAuC;AAE7D,QAAI,CAACA,UAAU;AACd,aAAO;;AAER,UAAMC,SAAS,KAAKN,SAASO,UAAUF,QAAQ;AAC/C1B,cAAU2B,QAAQ,6CAA6CD,QAAQ,EAAE;AAEzE,QAAI,CAAC,KAAKH,WAAU,KAAM,KAAKM,QAAO,GAAI;AACzC,aAAO;;AAGR,UAAMC,aAAa,KAAKT,SAASU,cAAcL,QAAQ;AACvD,UAAMM,kBAAkB,KAAKC,YAAW;AACxC,WACCC,YAAYJ,YAAYE,eAAe,KAAKL,OAAOQ,QAAQ,MAAMT,QAAQ;;EAIpEH,aAAsB;AAC5B,WAAOa,QAAQ,KAAKH,YAAW,CAAE;;EAG3BI,iBAAiBlB,UAAuC;AAE9D,QAAI,CAACA,UAAU;AACd,aAAO;;AAER,UAAMC,SAAS,KAAKC,SAASC,UAAUH,UAAU,IAAI;AACrDnB,cAAUoB,QAAQ,6CAA6CD,QAAQ,EAAE;AAEzE,QAAI,CAAC,KAAKI,WAAU,KAAM,CAAC,KAAKe,eAAc,GAAI;AACjD,aAAO;;AAGR,UAAMC,aAAa,KAAKlB,SAASmB,cAAcrB,QAAQ;AACvD,UAAMa,kBAAkB,KAAKC,YAAW;AACxC,QAAIM,eAAeP,iBAAiB;AACnC,aAAO;;AAGR,WAAOZ,OAAOG,WAAW,MAAMJ,QAAQ;;EAGjCsB,aACNf,UACA5B,UAAU;IAAE4C,SAAS;KACX;AAEV,QAAI,CAAChB,UAAU;AACd,aAAO;;AAGR,UAAM,EAAEgB,QAAO,IAAK5C;AACpB,QAAI,CAAC,KAAKyB,WAAU,GAAI;AACvB,aAAO;;AAGR,UAAMO,aAAa,KAAKT,SAASU,cAAcL,QAAQ;AACvD,UAAMM,kBAAkB,KAAKC,YAAW;AACxC,QAAID,mBAAmB,CAACE,YAAYJ,YAAYE,eAAe,GAAG;AACjE,aAAO;;AAGR,UAAMW,YAAY,KAAKC,aAAY;AACnC,QAAI,CAACD,UAAUE,QAAQ;AACtB,aAAO;;AAGR,UAAMC,QAAQH,UAAUI,QAAQrB,QAAQ;AACxC,QAAIgB,SAAS;AACZ,aAAOI,UAAUH,UAAUE,SAAS;WAC9B;AACN,aAAOC,QAAQ;;;EAIVb,cAA0B;AAChC,WAAO,KAAK7B,MAAMC,SAAQ,EAAG2C,cAAcC;;EAGrCC,UAAe;AACrB,WAAO,KAAK9C,MAAMC,SAAQ,EAAG2C,cAAcG;;EAGrCC,cAA6B;AACnC,WAAO,KAAKhD,MAAMC,SAAQ,EAAG2C,cAAc7B;;EAGrCyB,eAAyB;AAC/B,WAAO,KAAKxC,MAAMC,SAAQ,EAAG2C,cAAcL;;EAGrCU,gBAAqB;AAC3B,WAAO,KAAKjD,MAAMC,SAAQ,EAAG2C,cAAcM;;EAGrCzB,UAAmB;AACzB,WAAO,KAAKzB,MAAMC,SAAQ,EAAG2C,cAAcnB;;EAGrCS,iBAA0B;AAChC,WAAOF,QAAQ,KAAKhC,MAAMC,SAAQ,EAAG2C,cAAcV,cAAc;;EAG3DiB,yBAAyC;AAC/C,WAAO,KAAKnD,MAAMC,SAAQ,EAAGW,WAAWwC;;EAGlCC,+BAA+C;AACrD,WAAO,KAAKrD,MAAMC,SAAQ,EAAGW,WAAW0C;;EAGlCC,kBAAkC;AACxC,WAAO,KAAKvD,MAAMC,SAAQ,EAAGW,WAAW4C;;EAGlCC,wBAAwC;AAC9C,WAAOA,sBAAsB,KAAKzD,MAAMC,SAAQ,EAAGW,UAAU;;EAGvD8C,iCAAiD;AACvD,WAAOA,+BAA+B,KAAK1D,MAAMC,SAAQ,EAAGW,UAAU;;EA9LvE,YAAmBZ,OAAqBiB,UAA2B;AAClE,SAAKjB,QAAQA;AACb,SAAKiB,WAAWA;;;;;ACnBlB,IAAM0C,QAAQ,OAAOC,WAAW,cAAcA,SAASC;AACvD,IAAMC,0BACJH,MAAcI,oBAAqBJ,MAAcK;AAE5C,SAASC,yBAAyBC,UAAsB;AAC9D,SAAO,SAASC,cAAc;AAK7B,UAAMC,gBAAgBC,WAAWC,aAAa,CAAC;AAI/C,UAAMC,iBAAiBC,YAAYF,aAAa,EAAE;AAElD,aAASA,cAAc;AAGtBG,mBAAaL,aAAa;AAC1BM,oBAAcH,cAAc;AAC5BL,eAAQ;;;;AAOJ,SAASS,oCAAoCT,UAAsB;AACzE,MAAIU,SAAS;AACb,QAAMC,WAAW,IAAIf,wBAAwBI,QAAQ;AACrD,QAAMY,OAAOC,SAASC,eAAe,EAAE;AACvCH,WAASI,QAAQH,MAAM;IAAEI,eAAe;GAAM;AAC9C,SAAO,SAASf,cAAc;AAC7BS,aAAS,CAACA;AACRE,SAAaK,OAAOP;;;AAIjB,IAAMQ,kBACZ,OAAOtB,4BAA4B;;;;;;;;;;;EAYhCa;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BAV;;;;AClFG,IAAMoB,YAAN,MAAe;;;;;;;;;;;EAqDdC,YAAYC,MAAkB;AACpC,UAAM,EAAEC,OAAOC,GAAGC,aAAY,IAAK;AACnC,QAAI,CAACD,EAAEE,QAAQ;AACdD,mBAAY;AACZ,WAAKE,WAAW;;AAGjBH,MAAEA,EAAEE,MAAM,IAAIJ;;EApCf,cAAqB;AAvBrB,SAAQC,QAAgB,CAAA;AAGxB,SAAQK,gBAAuB,CAAA;AAI/B,SAAQD,WAAW;AAUnB,SAAQE,QAAQ;AAIhB,SAAQC,WAAW;AA+CnB,SAAQC,QAAQ,MAAM;AACrB,YAAM,EAAER,OAAOC,EAAC,IAAK;AACrB,aAAO,KAAKK,QAAQL,EAAEE,QAAQ;AAC7B,cAAMM,eAAe,KAAKH;AAG1B,aAAKA;AACLL,UAAEQ,YAAY,EAAGC,KAAI;AAMrB,YAAI,KAAKJ,QAAQ,KAAKC,UAAU;AAG/B,mBACKI,OAAO,GAAGC,YAAYX,EAAEE,SAAS,KAAKG,OAC1CK,OAAOC,WACPD,QACC;AACDV,cAAEU,IAAI,IAAIV,EAAEU,OAAO,KAAKL,KAAK;;AAE9BL,YAAEE,UAAU,KAAKG;AACjB,eAAKA,QAAQ;;;AAGfL,QAAEE,SAAS;AACX,WAAKG,QAAQ;AACb,WAAKF,WAAW;;AAMjB,SAAOS,uBAAuB,CAACC,QAAa;AAC3C,WAAKT,cAAcU,KAAKD,GAAG;AAC3B,WAAKE,kBAAiB;;AAxEtB,SAAKd,eAAee,gBAAgB,KAAKT,KAAK;AAC9C,SAAKQ,oBAAoBE,yBAAyB,MAAM;AAEvD,UAAI,KAAKb,cAAcF,QAAQ;AAC9B,cAAM,KAAKE,cAAcc,MAAK;;KAE/B;;;;;ACvCI,IAAMC,UAAN,MAAa;EAQZC,OAAO;AACb,QAAI;AACH,WAAKC,QAAQ,KAAKA,KAAI;aACdC,OAAO;AACf,WAAKC,QAAQD,KAAK;cAClB;AACA,WAAKD,OAAO;AACZ,WAAKG,QAAQ,IAAI;;;EAZnB,YACSD,SACAC,SACP;SAFOD,UAAAA;SACAC,UAAAA;SAJFH,OAAsB;;;;;ACHvB,IAAMI,cAAN,MAAiB;EAKhBC,OAAOC,MAAwB;AACrC,UAAMC,QAAQ,KAAKC;AACnB,UAAMC,KAAIF,MAAMG,SACZH,MAAMI,IAAG,IACV,IAAIC;MAAQ,KAAKC;MAAS,CAACJ,MAAOF,MAAMA,MAAMG,MAAM,IAAID;IAAE;AAC7DA,OAAEH,OAAOA;AACT,WAAOG;;EARR,YAA2BI,SAA6B;SAA7BA,UAAAA;SAFnBL,YAAuB,CAAA;;;;;ACAhC,IAAMM,YAAY,IAAIC,UAAS;AAC/B,IAAMC,cAAc,IAAIC,YAAYH,UAAUI,oBAAoB;AAU3D,SAASC,KAAKC,MAAc;AAClCN,YAAUO,YAAYL,YAAYM,OAAOF,IAAI,CAAC;;;;ACdxC,IAAMG,aAAa;AACnB,IAAMC,aAAa;AACnB,IAAMC,gBAAgB;AACtB,IAAMC,gBAAgB;AAEtB,SAASC,UAAUC,UAA2C;AACpE,SAAO;IACNC,MAAMN;IACNO,SAAS;MACRF;;;;AAKI,SAASG,UAAUC,UAA2C;AACpE,SAAO;IACNH,MAAML;IACNM,SAAS;MACRE;;;;AAKI,SAASC,aAAaL,UAA2C;AACvE,SAAO;IACNC,MAAMJ;IACNK,SAAS;MACRF;;;;AAKI,SAASM,aAAaF,UAA2C;AACvE,SAAO;IACNH,MAAMH;IACNI,SAAS;MACRE;;;;;;AClCI,SAASG,uBAAuBC,QAA0B;AAChEC,YACC,OAAOD,OAAOE,YAAY,YAC1B,oCAAoC;AAErCD,YACC,OAAOD,OAAOG,cAAc,YAC5B,sCAAsC;AAEvCF,YACC,OAAOD,OAAOI,YAAY,YAC1B,oCAAoC;;AAI/B,SAASC,uBAAuBC,QAA0B;AAChEL,YACC,OAAOK,OAAOC,YAAY,YAC1B,oCAAoC;AAErCN,YACC,OAAOK,OAAOE,UAAU,YACxB,kCAAkC;AAEnCP,YACC,OAAOK,OAAOG,SAAS,YACvB,sCAAsC;;AAIjC,SAASC,aACfC,MACAC,YACO;AACP,MAAIA,cAAcC,MAAMC,QAAQH,IAAI,GAAG;AACtCA,SAAKI;MAAQ,CAACC,MAAMN,aAAaM,GAAG,KAAK;IAAC;AAC1C;;AAGDf,YACC,OAAOU,SAAS,YAAY,OAAOA,SAAS,UAC5CC,aACG,gEACA,wCAAwC;;;;AC/C7C,IAWO;UAAKK,cAAW;AAAXA,EAAAA,aACXC,QAAM,IAANA;AADWD,EAAAA,aAEXE,QAAM,IAANA;GAFWF,gBAAAA,cAAW,CAAA,EAAA;;;ACXvB,IAAIG,eAAe;AAEZ,SAASC,kBAA0B;AACzC,SAAOD;;;;ACwBR,SAASE,iBAAiBC,MAA2B;AACpD,QAAMC,KAAKC,gBAAe,EAAGC,SAAQ;AACrC,UAAQH,MAAI;IACX,KAAKI,YAAYC;AAChB,aAAO,IAAIJ,EAAE;IACd,KAAKG,YAAYE;AAChB,aAAO,IAAIL,EAAE;IACd;AACC,YAAM,IAAIM,MAAM,yBAAyBP,IAAI,EAAE;;;AAIlD,SAASQ,uBAAuBC,WAAmB;AAClD,UAAQA,UAAU,CAAC,GAAC;IACnB,KAAK;AACJ,aAAOL,YAAYC;IACpB,KAAK;AACJ,aAAOD,YAAYE;IACpB;AACC,YAAM,IAAIC,MAAM,4BAA4BE,SAAS,EAAE;;;AAI1D,SAASC,iBAAoBC,KAAqBC,aAAgB;AACjE,QAAMC,UAAUF,IAAIE,QAAO;AAC3B,MAAIC,SAAS;AACb,KAAG;AACF,UAAM,EACLC,MACAC,OAAO,CAAA,EAAGA,KAAK,EAAC,IACbH,QAAQI,KAAI;AAChB,QAAID,UAAUJ,aAAa;AAC1B,aAAO;;AAERE,aAAS,CAAC,CAACC;WACH,CAACD;AACV,SAAO;;AAGD,IAAMI,sBAAN,MAAyB;EAYxBC,UAAUC,MAAkBC,QAA4B;AAC9DC,iBAAaF,IAAI;AACjBG,2BAAuBF,MAAM;AAE7B,UAAMG,WAAW,KAAKC,WAAWrB,YAAYC,QAAQe,MAAMC,MAAM;AACjE,SAAKK,MAAMC,SAASR,UAAUK,QAAQ,CAAC;AACvC,WAAOA;;EAGDI,UAAUR,MAAkBS,QAA4B;AAC9DP,iBAAaF,MAAM,IAAI;AACvBU,2BAAuBD,MAAM;AAE7B,UAAME,WAAW,KAAKN,WAAWrB,YAAYE,QAAQc,MAAMS,MAAM;AACjE,SAAKH,MAAMC,SAASC,UAAUG,QAAQ,CAAC;AACvC,WAAOA;;EAGDC,gBAAgBC,SAA2C;AACjE,WACCvB,iBAAiB,KAAKwB,aAAaD,OAAO,KAC1CvB,iBAAiB,KAAKyB,aAAaF,OAAO;;EAIrCG,UAAUZ,UAAkBa,gBAAgB,OAAmB;AACrEC,cAAU,KAAKC,WAAWf,QAAQ,GAAG,6BAA6B;AAClE,UAAMgB,WAAWH,iBAAiBb,aAAa,KAAKiB;AACpD,UAAMpB,SAASmB,WAAW,KAAKE,eAAe,KAAKR,YAAYS,IAAInB,QAAQ;AAC3E,WAAOH;;EAGDuB,UAAUb,UAA8B;AAC9CO,cAAU,KAAKO,WAAWd,QAAQ,GAAG,6BAA6B;AAClE,WAAO,KAAKI,YAAYQ,IAAIZ,QAAQ;;EAG9Be,cAActB,UAA8B;AAClDc,cAAU,KAAKC,WAAWf,QAAQ,GAAG,6BAA6B;AAClE,WAAO,KAAKuB,MAAMJ,IAAInB,QAAQ;;EAGxBwB,cAAcjB,UAA6C;AACjEO,cAAU,KAAKO,WAAWd,QAAQ,GAAG,6BAA6B;AAClE,WAAO,KAAKgB,MAAMJ,IAAIZ,QAAQ;;EAGxBQ,WAAW9B,WAA4B;AAC7C,UAAMT,OAAOQ,uBAAuBC,SAAS;AAC7C,WAAOT,SAASI,YAAYC;;EAGtBwC,WAAWpC,WAA4B;AAC7C,UAAMT,OAAOQ,uBAAuBC,SAAS;AAC7C,WAAOT,SAASI,YAAYE;;EAGtB2C,aAAazB,UAAwB;AAC3Cc,cAAU,KAAKF,UAAUZ,QAAQ,GAAG,8BAA8B;AAClE,SAAKE,MAAMC,SAASsB,aAAazB,QAAQ,CAAC;AAC1C0B,SAAK,MAAM;AACV,WAAKhB,YAAYiB,OAAO3B,QAAQ;AAChC,WAAKuB,MAAMI,OAAO3B,QAAQ;KAC1B;;EAGK4B,aAAarB,UAAwB;AAC3CO,cAAU,KAAKM,UAAUb,QAAQ,GAAG,8BAA8B;AAClE,SAAKL,MAAMC,SAASyB,aAAarB,QAAQ,CAAC;AAC1C,SAAKI,YAAYgB,OAAOpB,QAAQ;AAChC,SAAKgB,MAAMI,OAAOpB,QAAQ;;EAGpBsB,UAAU7B,UAAwB;AACxC,UAAMH,SAAS,KAAKe,UAAUZ,QAAQ;AACtCc,cAAUjB,QAAQ,8BAA8B;AAEhD,SAAKoB,iBAAiBjB;AACtB,SAAKkB,eAAerB;;EAGdiC,cAAoB;AAC1BhB,cAAU,KAAKI,cAAc,kCAAkC;AAE/D,SAAKD,iBAAiB;AACtB,SAAKC,eAAe;;EAGbjB,WACPzB,MACAoB,MACAa,SACS;AACT,UAAMhC,KAAKF,iBAAiBC,IAAI;AAChC,SAAK+C,MAAMQ,IAAItD,IAAImB,IAAI;AACvB,QAAIpB,SAASI,YAAYC,QAAQ;AAChC,WAAK6B,YAAYqB,IAAItD,IAAIgC,OAAO;eACtBjC,SAASI,YAAYE,QAAQ;AACvC,WAAK6B,YAAYoB,IAAItD,IAAIgC,OAAO;;AAEjC,WAAOhC;;EAxGR,YAAmByB,OAAqB;AAPxC,SAAQqB,QAA8C,oBAAIS,IAAG;AAC7D,SAAQtB,cAAuC,oBAAIsB,IAAG;AACtD,SAAQrB,cAAuC,oBAAIqB,IAAG;AACtD,SAAQf,iBAAgC;AACxC,SAAQC,eAAoB;AAI3B,SAAKhB,QAAQA;;;;;ACxER,IAAM+B,iBAAiB,CAAIC,GAAMC,MAAkBD,MAAMC;AAOzD,SAASC,eACfC,SACAC,SACU;AACV,MAAI,CAACD,WAAW,CAACC,SAAS;AACzB,WAAO;aACG,CAACD,WAAW,CAACC,SAAS;AAChC,WAAO;SACD;AACN,WAAOD,QAAQE,MAAMD,QAAQC,KAAKF,QAAQG,MAAMF,QAAQE;;;AASnD,SAASC,eACfP,GACAC,GACAO,UAA4BT,gBAClB;AACV,MAAIC,EAAES,WAAWR,EAAEQ,QAAQ;AAC1B,WAAO;;AAER,WAASC,IAAI,GAAGA,IAAIV,EAAES,QAAQ,EAAEC,GAAG;AAClC,QAAI,CAACF,QAAQR,EAAEU,CAAC,GAAQT,EAAES,CAAC,CAAC,GAAQ;AACnC,aAAO;;;AAGT,SAAO;;;;AChBD,SAASC,OAEfC,SAAgBC,MAChBC,QACQ;AACR,UAAQA,OAAOC,MAAI;IAClB,KAAKC;AACJ;IACD,KAAKC;IACL,KAAKC;IACL,KAAKC;IACL,KAAKC;AACJ,aAAOP;IACR,KAAKQ;IACL,KAAKC;IACL,KAAKC;IACL,KAAKC;IACL;AACC,aAAOC;;AAGT,QAAM,EAAEC,YAAY,CAAA,GAAIC,gBAAgB,CAAA,EAAE,IAAKb,OAAOc;AACtD,QAAMC,SAASC,IAAIJ,WAAWC,aAAa;AAC3C,QAAMI,YACLF,OAAOG,SAAS,KAAK,CAACC,eAAeP,WAAWC,aAAa;AAE9D,MAAI,CAACI,WAAW;AACf,WAAOlB;;AAKR,QAAMqB,wBAAwBP,cAAcA,cAAcK,SAAS,CAAC;AACpE,QAAMG,oBAAoBT,UAAUA,UAAUM,SAAS,CAAC;AACxD,MAAIE,0BAA0BC,mBAAmB;AAChD,QAAID,uBAAuB;AAC1BL,aAAOO,KAAKF,qBAAqB;;AAElC,QAAIC,mBAAmB;AACtBN,aAAOO,KAAKD,iBAAiB;;;AAI/B,SAAON;;;;ACpER,SAAAQ,iBAAA,KAAA,KAAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,IAAMC,eAAsB;EAC3BC,2BAA2B;EAC3BC,qBAAqB;EACrBC,cAAc;;AAGR,SAASC,QACfC,QAAeL,cACfM,QAIQ;AACR,QAAM,EAAEC,QAAO,IAAKD;AACpB,UAAQA,OAAOE,MAAI;IAClB,KAAKC;IACL,KAAKC;AACJ,aAAO;QACNT,2BAA2BM,QAAQI;QACnCT,qBAAqBK,QAAQJ;QAC7BA,cAAcI,QAAQJ;;IAExB,KAAKS;AACJ,UAAIC,eAAeR,MAAMF,cAAcI,QAAQJ,YAAY,GAAG;AAC7D,eAAOE;;AAER,aAAOS,eAAA,CAAA,GACHT,OAAK;QACRF,cAAcI,QAAQJ;;IAExB,KAAKY;IACL,KAAKC;AACJ,aAAOhB;IACR;AACC,aAAOK;;;;;AClDV,SAAAY,iBAAA,KAAA,KAAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,IAAMC,gBAAsB;EAC3BC,UAAU;EACVC,MAAM;EACNC,UAAU;EACVC,WAAW,CAAA;EACXC,YAAY;EACZC,SAAS;EACTC,gBAAgB;;AAGV,SAASC,QACfC,QAAeT,eACfU,QASQ;AACR,QAAM,EAAEC,QAAO,IAAKD;AACpB,UAAQA,OAAOE,MAAI;IAClB,KAAKC;AACJ,aAAOC,eAAA,CAAA,GACHL,OAAK;QACRR,UAAUU,QAAQV;QAClBC,MAAMS,QAAQT;QACdC,UAAUQ,QAAQR;QAClBI,gBAAgBI,QAAQJ;QACxBF,YAAY;QACZC,SAAS;;IAEX,KAAKS;AACJ,aAAOD,eAAA,CAAA,GACHL,OAAK;QACRF,gBAAgB;;IAElB,KAAKS;AACJ,aAAOF,eAAA,CAAA,GACHL,OAAK;QACRL,WAAWO,QAAQP;;IAErB,KAAKa;AACJ,UAAIR,MAAML,UAAUc,QAAQP,QAAQQ,QAAQ,MAAM,IAAI;AACrD,eAAOV;;AAER,aAAOK,eAAA,CAAA,GACHL,OAAK;QACRL,WAAWgB,QAAQX,MAAML,WAAWO,QAAQQ,QAAQ;;IAEtD,KAAKE;AACJ,aAAOP,eAAA,CAAA,GACHL,OAAK;QACRJ,YAAYM,QAAQN;QACpBC,SAAS;QACTF,WAAW,CAAA;;IAEb,KAAKkB;AACJ,aAAOR,eAAA,CAAA,GACHL,OAAK;QACRR,UAAU;QACVC,MAAM;QACNC,UAAU;QACVE,YAAY;QACZC,SAAS;QACTC,gBAAgB;QAChBH,WAAW,CAAA;;IAEb;AACC,aAAOK;;;;;AClFH,SAASc,QAAOC,QAAe,GAAGC,QAA4B;AACpE,UAAQA,OAAOC,MAAI;IAClB,KAAKC;IACL,KAAKC;AACJ,aAAOJ,QAAQ;IAChB,KAAKK;IACL,KAAKC;AACJ,aAAON,QAAQ;IAChB;AACC,aAAOA;;;;;ACjBH,SAASO,QAAOC,QAAe,GAAU;AAC/C,SAAOA,QAAQ;;;;ACHhB,SAAAC,iBAAA,KAAA,KAAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBO,SAASC,QAAOC,QAAe,CAAA,GAAaC,QAA4B;AAC9E,SAAO;IACNC,iBAAiBA,OAAgBF,MAAME,iBAAiB;MACvDC,MAAMF,OAAOE;MACbC,SAASC,eAAA,CAAA,GACLJ,OAAOG,SAAO;QACjBE,eAAeC,IAAcP,OAAO,2BAA2B,CAAA,CAAE;;KAElE;IACDQ,YAAYA,QAAWR,MAAMQ,YAAYP,MAAM;IAC/CQ,UAAUA,QAAST,MAAMS,UAAUR,MAAM;IACzCS,eAAeA,QAAcV,MAAMU,eAAeT,MAAM;IACxDU,SAASA,QAAQX,MAAMW,OAAO;;;;;ACvBzB,SAASC,sBACfC,gBACAC,gBAAyBC,QACzBC,iBAA0B,CAAA,GAC1BC,YAAY,OACM;AAClB,QAAMC,QAAQC,kBAAkBF,SAAS;AACzC,QAAMG,UAAU,IAAIC,oBAAoBH,OAAO,IAAII,oBAAoBJ,KAAK,CAAC;AAC7E,QAAMK,UAAU,IAAIC,oBAAoBN,OAAOE,OAAO;AACtD,QAAMK,UAAUZ,eAAeU,SAAST,eAAeE,cAAc;AACrEO,UAAQG,eAAeD,OAAO;AAC9B,SAAOF;;AAGR,SAASJ,kBAAkBF,WAAkC;AAG5D,QAAMU,gBACL,OAAOC,WAAW,eACjBA,OAAeC;AACjB,SAAOC,YACNC,SACAd,aACCU,iBACAA,cAAc;IACbK,MAAM;IACNC,YAAY;GACZ,CAAC;;;;AClCL,IAAAC,gBAAgC;;;ACFhC,mBAA8B;AAYvB,IAAMC,iBAAaC,4BAA8B;EACvDC,iBAAiBC;CACjB;;;ADfD,SAAA,yBAAA,QAAA,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,IAAIC,WAAW;AACf,IAAMC,eAAeC,OAAOC,IAAI,gCAAgC;IAKnDC,kBAAsDC,oBAClE,SAASD,aAAY,QAAwB;MAAxB,EAAEE,SAAQ,IAAV,QAAeC,QAAK,yBAApB,QAAsB;IAApBD;;AACtB,QAAM,CAACE,SAASC,gBAAgB,IAAIC,mBAAmBH,KAAK;AAM5DI,+BAAU,MAAM;AACf,QAAIF,kBAAkB;AACrB,YAAMG,UAAUC,iBAAgB;AAChC,QAAEb;AAEF,aAAO,MAAM;AACZ,YAAI,EAAEA,aAAa,GAAG;AACrBY,kBAAQX,YAAY,IAAI;;;;AAI3B;KACE,CAAA,CAAE;AAEL,aAAO,mBAAAa,KAACC,WAAWC,UAAQ;IAACC,OAAOT;;;CACnC;AAGF,SAASU,mBAAmBC,OAA2C;AACtE,MAAI,aAAaA,OAAO;AACvB,UAAMC,WAAU;MAAEC,iBAAiBF,MAAMC;;AACzC,WAAO;MAACA;MAAS;;;AAGlB,QAAMA,UAAUE,0BACfH,MAAMI,SACNJ,MAAMK,SACNL,MAAMM,SACNN,MAAMO,SAAS;AAEhB,QAAMC,mBAAmB,CAACR,MAAMK;AAEhC,SAAO;IAACJ;IAASO;;;AAGlB,SAASL,0BACRC,SACAC,UAA0BI,iBAAgB,GAC1CH,SACAC,WACC;AACD,QAAMG,MAAML;AACZ,MAAI,CAACK,IAAIC,YAAY,GAAG;AACvBD,QAAIC,YAAY,IAAI;MACnBT,iBAAiBU,sBAChBR,SACAC,SACAC,SACAC,SAAS;;;AAIZ,SAAOG,IAAIC,YAAY;;AAIxB,SAASF,mBAAmB;AAC3B,SAAO,OAAOI,WAAW,cAAcA,SAAUC;;;;AE1FlD,IAAAC,gBAAgC;AAWzB,IAAMC,uBAA8CC,oBAC1D,SAASD,kBAAiB,EAAEE,SAASC,IAAG,GAAI;AAC3CC,+BAAU,MAAM;AACf,QAAI,OAAOC,UAAU,YAAa;AAElC,QAAIC,YAAY;AAChB,UAAMC,MAAM,IAAIF,MAAK;AACrBE,QAAIJ,MAAMA;AACVI,QAAIC,SAAS,MAAM;AAClBN,cAAQK,GAAG;AACXD,kBAAY;;AAEb,WAAO,MAAM;AACZ,UAAIA,WAAW;AACdJ,gBAAQ,IAAI;;;GAGd;AAED,SAAO;CACP;;;AChCF,6BAAkB;AAClB,IAAAO,gBAAsC;;;ACDtC,IAAAC,gBAA2C;AAGpC,IAAMC,4BACZ,OAAOC,WAAW,cAAcC,gCAAkBC;;;ADO5C,SAASC,aACfC,SACAC,SACAC,UACkB;AAClB,QAAM,CAACC,WAAWC,YAAY,QAAIC;IAAS,MAAMJ,QAAQD,OAAO;EAAC;AAEjE,QAAMM,sBAAkBC,2BAAY,MAAM;AACzC,UAAMC,YAAYP,QAAQD,OAAO;AAGjC,QAAI,KAACS,uBAAAA,SAAMN,WAAWK,SAAS,GAAG;AACjCJ,mBAAaI,SAAS;AACtB,UAAIN,UAAU;AACbA,iBAAQ;;;KAGR;IAACC;IAAWH;IAASE;GAAS;AAKjCQ,4BAA0BJ,eAAe;AAEzC,SAAO;IAACH;IAAWG;;;;;AE/Bb,SAASK,iBACfC,SACAC,SACAC,WACY;AACZ,QAAM,CAACC,WAAWC,eAAe,IAAIC,aAAaL,SAASC,SAASC,SAAS;AAE7EI,4BACC,SAASC,gCAAgC;AACxC,UAAMC,YAAYR,QAAQS,aAAY;AACtC,QAAID,aAAa,MAAM;AACtB;;AAED,WAAOR,QAAQU,uBAAuBN,iBAAiB;MACtDO,YAAY;QAACH;;KACb;KAEF;IAACR;IAASI;GAAgB;AAG3B,SAAOD;;;;ACpBD,SAASS,kBACfC,WACAC,SACAC,WACC;AACD,SAAOC;IAAiBF;IAASD,cAAc,OAAO,CAAA;IAAmB,MACxEE,UAAUE,UAAS;EAAE;;;;ACVvB,IAAAC,gBAAwB;AAIjB,SAASC,mBACfC,KACAC,MACI;AACJ,QAAMC,WAAW;OAAKD,QAAQ,CAAA;;AAC9B,MAAIA,QAAQ,QAAQ,OAAOD,QAAQ,YAAY;AAC9CE,aAASC,KAAKH,GAAG;;AAElB,aAAOI,uBAAW,MAAM;AACvB,WAAO,OAAOJ,QAAQ,aAAcA,IAAG,IAAkBA;KACvDE,QAAQ;;;;ACdZ,IAAAG,gBAAwB;AAIjB,SAASC,qBAAqBC,WAA4B;AAChE,aAAOC;IAAQ,MAAMD,UAAUE,MAAMC,WAAU;IAAI;MAACH;;EAAU;;AAGxD,SAASI,sBAAsBJ,WAA4B;AACjE,aAAOC;IAAQ,MAAMD,UAAUE,MAAMG,YAAW;IAAI;MAACL;;EAAU;;;;ACThE,IAAAM,iBAAwB;;;ACYxB,IAAIC,mBAAmB;AACvB,IAAIC,sBAAsB;AAEnB,IAAMC,wBAAN,MAA2B;EAQ1BC,iBAAiBC,UAAmC;AAC1D,SAAKA,WAAWA;;EAGVC,eAAkC;AACxC,WAAO,KAAKD;;EAGNE,UAAmB;AACzBC,cACC,CAACP,kBACD,uJAC+E;AAGhF,QAAI;AACHA,yBAAmB;AACnB,aAAO,KAAKQ,gBAAgBC,cAAc,KAAKL,QAAQ;cACvD;AACAJ,yBAAmB;;;EAIdU,aAAsB;AAC5B,QAAI,CAAC,KAAKN,UAAU;AACnB,aAAO;;AAERG,cACC,CAACN,qBACD,6JAC+E;AAGhF,QAAI;AACHA,4BAAsB;AACtB,aAAO,KAAKO,gBAAgBG,iBAAiB,KAAKP,QAAQ;cAC1D;AACAH,4BAAsB;;;EAIjBW,uBACNC,UACAC,SACc;AACd,WAAO,KAAKN,gBAAgBI,uBAAuBC,UAAUC,OAAO;;EAG9DH,iBAAiBP,UAA+B;AACtD,WAAO,KAAKI,gBAAgBG,iBAAiBP,QAAQ;;EAG/CW,aACNC,UACAF,SACU;AACV,WAAO,KAAKN,gBAAgBO,aAAaC,UAAUF,OAAO;;EAGpDG,eAA6B;AACnC,WAAO,KAAKT,gBAAgBS,aAAY;;EAGlCC,iBAAiC;AACvC,WAAO,KAAKV,gBAAgBU,eAAc;;EAGpCC,cAAiC;AACvC,WAAO,KAAKX,gBAAgBW,YAAW;;EAGjCC,wBAAwBP,UAAiC;AAC/D,WAAO,KAAKL,gBAAgBY,wBAAwBP,QAAQ;;EAGtDJ,cAAcL,UAA+B;AACnD,WAAO,KAAKI,gBAAgBC,cAAcL,QAAQ;;EAG5CiB,gBAAgBL,UAA+B;AACrD,WAAO,KAAKR,gBAAgBa,gBAAgBL,QAAQ;;EAG9CM,cAAiC;AACvC,WAAO,KAAKd,gBAAgBc,YAAW;;EAGjCC,UAAe;AACrB,WAAO,KAAKf,gBAAgBe,QAAO;;EAG7BC,gBAAqB;AAC3B,WAAO,KAAKhB,gBAAgBgB,cAAa;;EAGnCC,UAAmB;AACzB,WAAO,KAAKjB,gBAAgBiB,QAAO;;EAG7BC,yBAAyC;AAC/C,WAAO,KAAKlB,gBAAgBkB,uBAAsB;;EAG5CC,+BAA+C;AACrD,WAAO,KAAKnB,gBAAgBmB,6BAA4B;;EAGlDC,wBAAwC;AAC9C,WAAO,KAAKpB,gBAAgBoB,sBAAqB;;EAG3CC,kBAAkC;AACxC,WAAO,KAAKrB,gBAAgBqB,gBAAe;;EAGrCC,iCAAiD;AACvD,WAAO,KAAKtB,gBAAgBsB,+BAA8B;;EAxH3D,YAAmBC,SAA0B;AAF7C,SAAQ3B,WAA8B;AAGrC,SAAKI,kBAAkBuB,QAAQC,WAAU;;;;;ACR3C,IAAIC,mBAAmB;AAEhB,IAAMC,wBAAN,MAA2B;EAQ1BC,iBAAiBC,UAAmC;AAC1D,SAAKA,WAAWA;;EAGVC,eAAkC;AACxC,WAAO,KAAKD;;EAGNE,uBACNC,UACAC,SACc;AACd,WAAO,KAAKC,gBAAgBH,uBAAuBC,UAAUC,OAAO;;EAG9DE,UAAmB;AAIzB,QAAI,CAAC,KAAKN,UAAU;AACnB,aAAO;;AAERO,cACC,CAACV,kBACD,uJAC+E;AAGhF,QAAI;AACHA,yBAAmB;AACnB,aAAO,KAAKQ,gBAAgBG,gBAAgB,KAAKR,QAAQ;cACzD;AACAH,yBAAmB;;;EAIdY,OAAOL,SAA0C;AACvD,QAAI,CAAC,KAAKJ,UAAU;AACnB,aAAO;;AAER,WAAO,KAAKK,gBAAgBK,aAAa,KAAKV,UAAUI,OAAO;;EAGzDO,cAAiC;AACvC,WAAO,KAAKN,gBAAgBM,YAAW;;EAGjCC,UAAe;AACrB,WAAO,KAAKP,gBAAgBO,QAAO;;EAG7BC,gBAAqB;AAC3B,WAAO,KAAKR,gBAAgBQ,cAAa;;EAGnCC,UAAmB;AACzB,WAAO,KAAKT,gBAAgBS,QAAO;;EAG7BC,yBAAyC;AAC/C,WAAO,KAAKV,gBAAgBU,uBAAsB;;EAG5CC,+BAA+C;AACrD,WAAO,KAAKX,gBAAgBW,6BAA4B;;EAGlDC,wBAAwC;AAC9C,WAAO,KAAKZ,gBAAgBY,sBAAqB;;EAG3CC,kBAAkC;AACxC,WAAO,KAAKb,gBAAgBa,gBAAe;;EAGrCC,iCAAiD;AACvD,WAAO,KAAKd,gBAAgBc,+BAA8B;;EAhF3D,YAAmBC,SAA0B;AAF7C,SAAQpB,WAA8B;AAGrC,SAAKK,kBAAkBe,QAAQC,WAAU;;;;;ACTpC,SAASC,eACfC,MACAC,QACAC,SAC4B;AAC5B,QAAMC,WAAWD,QAAQE,YAAW;AACpC,QAAMC,WAAWF,SAASG,UAAUN,MAAMC,MAAM;AAEhD,SAAO;IAACI;IAAU,MAAMF,SAASI,aAAaF,QAAQ;;;AAGhD,SAASG,eACfR,MACAS,QACAP,SAC4B;AAC5B,QAAMC,WAAWD,QAAQE,YAAW;AACpC,QAAMM,WAAWP,SAASQ,UAAUX,MAAMS,MAAM;AAEhD,SAAO;IAACC;IAAU,MAAMP,SAASS,aAAaF,QAAQ;;;;;AC7BhD,SAASG,aACfC,MACAC,MACAC,SACAC,gBACC;AACD,MAAIC,gBAAgBF,UACjBA,QAAQG,KAAKF,gBAAgBH,MAAMC,IAAI,IACvC;AACH,MAAIG,kBAAkB,QAAQ;AAC7B,WAAO,CAAC,CAACA;;AAGV,MAAIJ,SAASC,MAAM;AAClB,WAAO;;AAGR,MAAI,OAAOD,SAAS,YAAY,CAACA,QAAQ,OAAOC,SAAS,YAAY,CAACA,MAAM;AAC3E,WAAO;;AAGR,QAAMK,QAAQC,OAAOC,KAAKR,IAAI;AAC9B,QAAMS,QAAQF,OAAOC,KAAKP,IAAI;AAE9B,MAAIK,MAAMI,WAAWD,MAAMC,QAAQ;AAClC,WAAO;;AAGR,QAAMC,kBAAkBJ,OAAOK,UAAUC,eAAeC,KAAKb,IAAI;AAGjE,WAASc,MAAM,GAAGA,MAAMT,MAAMI,QAAQK,OAAO;AAC5C,UAAMC,MAAMV,MAAMS,GAAG;AAErB,QAAI,CAACJ,gBAAgBK,GAAG,GAAG;AAC1B,aAAO;;AAGR,UAAMC,SAAUjB,KAAagB,GAAG;AAChC,UAAME,SAAUjB,KAAae,GAAG;AAEhCZ,oBAAgBF,UACbA,QAAQG,KAAKF,gBAAgBc,QAAQC,QAAQF,GAAG,IAChD;AAEH,QACCZ,kBAAkB,SACjBA,kBAAkB,UAAUa,WAAWC,QACvC;AACD,aAAO;;;AAIT,SAAO;;;;ACjDD,SAASC,MAAMC,KAAuB;AAC5C;;IAECA,QAAQ,QACR,OAAOA,QAAQ,YACfC,OAAOC,UAAUC,eAAeC,KAAKJ,KAAK,SAAS;;;;;ACPrD,IAAAK,gBAA6C;AAE7C,SAASC,iCAAiCC,SAA4B;AAGrE,MAAI,OAAOA,QAAQC,SAAS,UAAU;AACrC;;AAGD,QAAMC,cACJF,QAAQC,KAAaC,eAAeF,QAAQC,KAAKE,QAAQ;AAE3D,QAAM,IAAIC,MACT,2FACwBF,WAAW,uEACI;;AAIzC,SAASG,2BAA2BC,MAAyC;AAC5E,SAAO,CAACC,gBAAgB,MAAMC,UAAU,SAAS;AAEhD,QAAI,KAACC,8BAAeF,aAAa,GAAG;AACnC,YAAMG,OAAOH;AACbD,WAAKI,MAAMF,OAAO;AAGlB,aAAOE;;AAMR,UAAMV,UAA+BO;AACrCR,qCAAiCC,OAAO;AAGxC,UAAMW,MAAMH,UAAU,CAACE,SAAkBJ,KAAKI,MAAMF,OAAO,IAAIF;AAC/D,WAAOM,aAAaZ,SAASW,GAAG;;;AAI3B,SAASE,mBAAmBC,OAAY;AAC9C,QAAMC,eAAoB,CAAA;AAE1BC,SAAOC,KAAKH,KAAK,EAAEI,QAAQ,CAACC,QAAQ;AACnC,UAAMb,OAAOQ,MAAMK,GAAG;AAGtB,QAAIA,IAAIC,SAAS,KAAK,GAAG;AACxBL,mBAAaI,GAAG,IAAIL,MAAMK,GAAG;WACvB;AACN,YAAME,cAAchB,2BAA2BC,IAAI;AACnDS,mBAAaI,GAAG,IAAI,MAAME;;GAE3B;AAED,SAAON;;AAGR,SAASO,OAAOX,KAAUD,MAAW;AACpC,MAAI,OAAOC,QAAQ,YAAY;AAC9BA,QAAID,IAAI;SACF;AACNC,QAAIY,UAAUb;;;AAIhB,SAASE,aAAaZ,SAAcwB,QAAgC;AACnE,QAAMC,cAAczB,QAAQW;AAC5Be,YACC,OAAOD,gBAAgB,UACvB,iOAE0E;AAG3E,MAAI,CAACA,aAAa;AAEjB,eAAOE,4BAAa3B,SAAS;MAC5BW,KAAKa;KACL;SACK;AACN,eAAOG,4BAAa3B,SAAS;MAC5BW,KAAK,CAACD,SAAc;AACnBY,eAAOG,aAAaf,IAAI;AACxBY,eAAOE,QAAQd,IAAI;;KAEpB;;;;;AC3EI,IAAMkB,kBAAN,MAAqB;EAoDpBC,iBAAiBC,cAAuC;AAC9D,QAAI,KAAKC,cAAcD,cAAc;AACpC;;AAGD,SAAKC,YAAYD;AACjB,SAAKE,UAAS;;EAGf,IAAWC,gBAAqB;AAC/B,WAAO,KAAKC;;EAGb,IAAWC,oBAA8C;AACxD,WAAO,KAAKC;;EAEb,IAAWD,kBAAkBE,SAAmC;AAC/D,SAAKD,4BAA4BC;;EAGlC,IAAWC,qBAAgD;AAC1D,WAAO,KAAKC;;EAGb,IAAWD,mBAAmBD,SAAoC;AACjE,SAAKE,6BAA6BF;;EAG5BL,YAAkB;AACxB,UAAMQ,YAAY,KAAKC,oBAAmB;AAC1C,SAAKC,qBAAqBF,SAAS;;EAG5BC,sBAA+B;AACtC,UAAMP,aAAa,KAAKA;AAExB,UAAMM,YACL,KAAKG,mBAAkB,KACvB,KAAKC,6BAA4B,KACjC,KAAKC,2BAA0B;AAEhC,QAAIL,WAAW;AACd,WAAKM,qBAAoB;;AAG1B,QAAI,CAAC,KAAKf,WAAW;AACpB,aAAOS;;AAER,QAAI,CAACN,YAAY;AAChB,WAAKa,0BAA0Bb;AAC/B,aAAOM;;AAGR,QAAIA,WAAW;AACd,WAAKQ,yBAAyB,KAAKjB;AACnC,WAAKgB,0BAA0Bb;AAC/B,WAAKe,iCAAiC,KAAKd;AAC3C,WAAKe,wBAAwB,KAAKC,QAAQC,kBACzC,KAAKrB,WACLG,YACA,KAAKC,iBAAiB;;AAGxB,WAAOK;;EAGAE,qBAAqBW,iBAAiB,OAAa;AAC1D,UAAMC,cAAc,KAAKA;AAEzB,UAAMd,YACLa,kBACA,KAAKV,mBAAkB,KACvB,KAAKY,8BAA6B,KAClC,KAAKC,4BAA2B;AAEjC,QAAIhB,WAAW;AACd,WAAKiB,sBAAqB;;AAG3B,QAAI,CAAC,KAAK1B,WAAW;AACpB;;AAED,QAAI,CAACuB,aAAa;AACjB,WAAKI,2BAA2BJ;AAChC;;AAGD,QAAId,WAAW;AACd,WAAKQ,yBAAyB,KAAKjB;AACnC,WAAK2B,2BAA2BJ;AAChC,WAAKK,kCAAkC,KAAKrB;AAC5C,WAAKsB,yBAAyB,KAAKT,QAAQU,mBAC1C,KAAK9B,WACLuB,aACA,KAAKhB,kBAAkB;;;EAKlBK,qBAA8B;AACrC,WAAO,KAAKK,2BAA2B,KAAKjB;;EAGrCa,+BAAwC;AAC/C,WAAO,KAAKG,4BAA4B,KAAKb;;EAGtCqB,gCAAyC;AAChD,WAAO,KAAKG,6BAA6B,KAAKJ;;EAGvCT,6BAAsC;AAC7C,WAAO,CAACiB,aACP,KAAKb,gCACL,KAAKd,iBAAiB;;EAIhBqB,8BAAuC;AAC9C,WAAO,CAACM,aACP,KAAKH,iCACL,KAAKrB,kBAAkB;;EAIlBQ,uBAAuB;AAC7B,QAAI,KAAKI,uBAAuB;AAC/B,WAAKA,sBAAqB;AAC1B,WAAKA,wBAAwBa;;;EAIxBN,wBAAwB;AAC9B,QAAI,KAAKG,wBAAwB;AAChC,WAAKA,uBAAsB;AAC3B,WAAKA,yBAAyBG;AAC9B,WAAKC,kBAAkB;AACvB,WAAKC,iBAAiB;;;EAIxB,IAAY/B,aAAa;AACxB,WACC,KAAKgC,kBAAmB,KAAKC,iBAAiB,KAAKA,cAAcC;;EAInE,IAAYd,cAAc;AACzB,WACC,KAAKU,mBACJ,KAAKC,kBAAkB,KAAKA,eAAeG;;EAItCC,kBAAkB;AACzB,SAAKH,iBAAiB;AACtB,SAAKC,gBAAgB;;EAGdG,mBAAmB;AAC1B,SAAKN,kBAAkB;AACvB,SAAKC,iBAAiB;;EArKvB,YAAmBd,SAAkB;AA/CrC,SAAOoB,QAAQC,mBAAmB;MACjCtC,YAAY,CACXuC,MACApC,YACI;AACJ,aAAKgC,gBAAe;AACpB,aAAKlC,oBAAoBE,WAAW;AACpC,YAAIqC,MAAMD,IAAI,GAAG;AAChB,eAAKN,gBAAgBM;eACf;AACN,eAAKP,iBAAiBO;;AAEvB,aAAKhC,oBAAmB;;MAEzBa,aAAa,CAACmB,MAAWpC,YAAiC;AACzD,aAAKiC,iBAAgB;AACrB,aAAKhC,qBAAqBD,WAAW;AACrC,YAAIqC,MAAMD,IAAI,GAAG;AAChB,eAAKR,iBAAiBQ;eAChB;AACN,eAAKT,kBAAkBS;;AAExB,aAAK/B,qBAAoB;;KAE1B;AACD,SAAQX,YAA+B;AAGvC,SAAQoC,gBAAuC;AAE/C,SAAQ/B,4BAAsD;AAI9D,SAAQ6B,iBAAwC;AAEhD,SAAQ1B,6BAAwD;AAGhE,SAAQS,yBAA4C;AACpD,SAAQD,0BAA+B;AACvC,SAAQE,iCAAsC;AAC9C,SAAQS,2BAAgC;AACxC,SAAQC,kCAAuC;AAK9C,SAAKR,UAAUA;;;;;ACvDV,IAAMwB,kBAAN,MAAqB;EA8B3B,IAAWC,gBAAqB;AAC/B,WAAO,KAAKC;;EAGNC,YAAkB;AAExB,UAAMC,YACL,KAAKC,mBAAkB,KACvB,KAAKC,oBAAmB,KACxB,KAAKC,iBAAgB;AAEtB,QAAIH,WAAW;AACd,WAAKI,qBAAoB;;AAG1B,UAAMN,aAAa,KAAKA;AACxB,QAAI,CAAC,KAAKO,WAAW;AACpB;;AAED,QAAI,CAACP,YAAY;AAChB,WAAKQ,0BAA0BR;AAC/B;;AAGD,QAAIE,WAAW;AACd,WAAKO,yBAAyB,KAAKF;AACnC,WAAKC,0BAA0BR;AAC/B,WAAKU,iCAAiC,KAAKC;AAE3C,WAAKC,wBAAwB,KAAKC,QAAQC,kBACzC,KAAKP,WACLP,YACA,KAAKW,iBAAiB;;;EAKlBI,iBAAiBC,cAAuC;AAC9D,QAAIA,iBAAiB,KAAKT,WAAW;AACpC;;AAGD,SAAKA,YAAYS;AACjB,SAAKf,UAAS;;EAGf,IAAWU,oBAAuC;AACjD,WAAO,KAAKM;;EAEb,IAAWN,kBAAkBO,SAA4B;AACxD,SAAKD,4BAA4BC;;EAG1Bf,qBAA8B;AACrC,WAAO,KAAKM,2BAA2B,KAAKF;;EAGrCH,sBAA+B;AACtC,WAAO,KAAKI,4BAA4B,KAAKR;;EAGtCK,mBAA4B;AACnC,WAAO,CAACc,aACP,KAAKT,gCACL,KAAKC,iBAAiB;;EAIjBL,uBAAuB;AAC7B,QAAI,KAAKM,uBAAuB;AAC/B,WAAKA,sBAAqB;AAC1B,WAAKA,wBAAwBQ;;;EAI/B,IAAYpB,aAAa;AACxB,WACC,KAAKqB,kBAAmB,KAAKC,iBAAiB,KAAKA,cAAcC;;EAI3DC,kBAAkB;AACzB,SAAKF,gBAAgB;AACrB,SAAKD,iBAAiB;;EAvFvB,YAAmBR,SAAkB;AAzBrC,SAAOY,QAAQC,mBAAmB;MACjC1B,YAAY,CAAC2B,MAAWT,YAA+B;AACtD,aAAKM,gBAAe;AACpB,aAAKb,oBAAoBO;AACzB,YAAIU,MAAMD,IAAI,GAAG;AAChB,eAAKL,gBAAgBK;eACf;AACN,eAAKN,iBAAiBM;;AAEvB,aAAK1B,UAAS;;KAEf;AAED,SAAQM,YAA+B;AAEvC,SAAQe,gBAAuC;AAE/C,SAAQL,4BAAsD;AAG9D,SAAQR,yBAA4C;AACpD,SAAQD,0BAA+B;AACvC,SAAQE,iCAA2D;AAIlE,SAAKG,UAAUA;;;;;AClCjB,IAAAgB,gBAA2B;AAOpB,SAASC,qBAAsC;AACrD,QAAM,EAAEC,gBAAe,QAAKC,0BAAWC,UAAU;AACjDC,YAAUH,mBAAmB,MAAM,4BAA4B;AAC/D,SAAOA;;;;ATFD,SAASI,uBACfC,mBACAC,oBACkB;AAClB,QAAMC,UAAUC,mBAAkB;AAClC,QAAMC,gBAAYC;IACjB,MAAM,IAAIC,gBAAgBJ,QAAQK,WAAU,CAAE;IAC9C;MAACL;;EAAQ;AAEVM,4BAA0B,MAAM;AAC/BJ,cAAUJ,oBAAoBA,qBAAqB;AACnDI,cAAUK,UAAS;AACnB,WAAO,MAAML,UAAUM,qBAAoB;KACzC;IAACN;IAAWJ;GAAkB;AACjCQ,4BAA0B,MAAM;AAC/BJ,cAAUH,qBAAqBA,sBAAsB;AACrDG,cAAUK,UAAS;AACnB,WAAO,MAAML,UAAUO,sBAAqB;KAC1C;IAACP;IAAWH;GAAmB;AAClC,SAAOG;;;;AU7BR,IAAAQ,iBAAwB;AAMjB,SAASC,uBAAsD;AACrE,QAAMC,UAAUC,mBAAkB;AAClC,aAAOC;IACN,MAAM,IAAIC,sBAAsBH,OAAO;IACvC;MAACA;;EAAQ;;;;ACVX,IAAAI,iBAAmC;;;ACM5B,IAAMC,iBAAN,MAAoB;EAOnBC,YAAY;AAClB,UAAMC,OAAO,KAAKA;AAClB,UAAMC,UAAU,KAAKA;AAErB,QAAIC,SAAmB;AACvB,QAAI,OAAOF,KAAKG,SAAS,UAAU;AAClCD,eAASF,KAAKG;eACJ,OAAOH,KAAKG,SAAS,YAAY;AAC3CD,eAAUF,KAAKG,KAA8BF,OAAO;WAC9C;AACNC,eAAS,CAAA;;AAEV,WAAOA,WAAM,QAANA,WAAM,SAANA,SAAU;;EAGXE,UAAU;AAChB,UAAMJ,OAAO,KAAKA;AAClB,UAAMC,UAAU,KAAKA;AACrB,QAAI,OAAOD,KAAKI,YAAY,WAAW;AACtC,aAAOJ,KAAKI;eACF,OAAOJ,KAAKI,YAAY,YAAY;AAC9C,aAAOJ,KAAKI,QAAQH,OAAO;WACrB;AACN,aAAO;;;EAIFI,WAAWC,eAAgCC,QAAoB;AACrE,UAAMP,OAAO,KAAKA;AAClB,UAAMC,UAAU,KAAKA;AACrB,UAAM,EAAEI,WAAU,IAAKL;AACvB,WAAOK,aACJA,WAAWJ,OAAO,IAClBM,WAAWD,cAAcE,YAAW;;EAGjCC,UAAU;AAChB,UAAMT,OAAO,KAAKA;AAClB,UAAMC,UAAU,KAAKA;AACrB,UAAMS,YAAY,KAAKA;AACvB,UAAM,EAAEC,IAAG,IAAKX;AAChB,QAAIW,KAAK;AACRA,UAAIV,QAAQW,QAAO,GAAIX,OAAO;;AAE/BS,cAAUG,UAAS;;EAlDpB,YACQb,MACCC,SACAS,WACP;SAHMV,OAAAA;SACCC,UAAAA;SACAS,YAAAA;;;;;ADHH,SAASI,cACfC,MACAC,SACAC,WACC;AACD,QAAMC,cAAUC;IACf,MAAM,IAAIC,eAAeL,MAAMC,SAASC,SAAS;IACjD;MAACD;MAASC;;EAAU;AAErBI,gCAAU,MAAM;AACfH,YAAQH,OAAOA;KACb;IAACA;GAAK;AACT,SAAOG;;;;AEjBR,IAAAI,iBAAwB;AAIjB,SAASC,YACfC,MACa;AACb,aAAOC,wBAAQ,MAAM;AACpB,UAAMC,SAAqBF,KAAKG;AAChCC,cAAUF,UAAU,MAAM,2BAA2B;AACrD,WAAOA;KACL;IAACF;GAAK;;;;ACJH,SAASK,wBACfC,MACAC,SACAC,WACO;AACP,QAAMC,UAAUC,mBAAkB;AAClC,QAAMC,UAAUC,cAAcN,MAAMC,SAASC,SAAS;AACtD,QAAMK,WAAWC,YAAYR,IAAI;AAEjCS,4BACC,SAASC,qBAAqB;AAC7B,QAAIH,YAAY,MAAM;AACrB,YAAM,CAACI,WAAWC,UAAU,IAAIC,eAC/BN,UACAF,SACAF,OAAO;AAERF,cAAQa,iBAAiBH,SAAS;AAClCT,gBAAUY,iBAAiBH,SAAS;AACpC,aAAOC;;AAER;KAED;IAACT;IAASF;IAASC;IAAWG;IAASE;GAAS;;;;ACb3C,SAASQ,QAKfC,SAGAC,MAC0D;AAC1D,QAAMC,OAAOC,mBAAmBH,SAASC,IAAI;AAC7CG,YACC,CAAEF,KAAaG,OACf,6JAA6J;AAG9J,QAAMC,UAAUC,qBAAoB;AACpC,QAAMC,YAAYC,uBAAuBP,KAAKQ,SAASR,KAAKS,cAAc;AAC1EC,0BAAwBV,MAAMI,SAASE,SAAS;AAEhD,SAAO;IACNK,kBAAkBX,KAAKY,SAASR,SAASE,SAAS;IAClDO,qBAAqBP,SAAS;IAC9BQ,sBAAsBR,SAAS;;;;;AC1CjC,IAAAS,iBAA0B;AAUnB,SAASC,aACfC,SACiB;AACjB,QAAMC,kBAAkBC,mBAAkB;AAC1C,QAAMC,UAAUF,gBAAgBG,WAAU;AAC1C,QAAM,CAACC,WAAWC,eAAe,IAAIC,aAAaJ,SAASH,OAAO;AAElEQ;IAAU,MAAML,QAAQM,wBAAwBH,eAAe;EAAC;AAChEE;IAAU,MAAML,QAAQO,uBAAuBJ,eAAe;EAAC;AAC/D,SAAOD;;;;ACnBR,IAAAM,iBAAwB;AAIjB,SAASC,qBAAqBC,WAA4B;AAChE,aAAOC;IAAQ,MAAMD,UAAUE,MAAMC,WAAU;IAAI;MAACH;;EAAU;;;;ACL/D,IAAAI,iBAAwB;AAOjB,SAASC,uBACfC,SACkB;AAClB,QAAMC,UAAUC,mBAAkB;AAClC,QAAMC,gBAAYC;IACjB,MAAM,IAAIC,gBAAgBJ,QAAQK,WAAU,CAAE;IAC9C;MAACL;;EAAQ;AAEVM,4BAA0B,MAAM;AAC/BJ,cAAUK,oBAAoBR,WAAW;AACzCG,cAAUM,UAAS;AACnB,WAAO,MAAMN,UAAUO,qBAAoB;KACzC;IAACV;GAAQ;AACZ,SAAOG;;;;ACpBR,IAAAQ,iBAAwB;AAMjB,SAASC,uBAAsD;AACrE,QAAMC,UAAUC,mBAAkB;AAClC,aAAOC;IAAQ,MAAM,IAAIC,sBAAsBH,OAAO;IAAG;MAACA;;EAAQ;;;;ACNnE,IAAAI,iBAAwB;AASjB,SAASC,UACfC,MACe;AACf,QAAM,EAAEC,OAAM,IAAKD;AACnB,aAAOE,wBAAQ,MAAM;AACpBC,cAAUH,KAAKC,UAAU,MAAM,wBAAwB;AACvD,WAAOG,MAAMC,QAAQJ,MAAM,IAAIA,SAAS;MAACA;;KACvC;IAACA;GAAO;;;;AClBZ,IAAAK,iBAAmC;;;ACK5B,IAAMC,iBAAN,MAAoB;EAMnBC,UAAU;AAChB,UAAMC,OAAO,KAAKA;AAClB,UAAMC,UAAU,KAAKA;AACrB,WAAOD,KAAKD,UAAUC,KAAKD,QAAQE,QAAQC,QAAO,GAAID,OAAO,IAAI;;EAG3DE,QAAQ;AACd,UAAMH,OAAO,KAAKA;AAClB,UAAMC,UAAU,KAAKA;AACrB,QAAID,KAAKG,OAAO;AACfH,WAAKG,MAAMF,QAAQC,QAAO,GAAID,OAAO;;;EAIhCG,OAAO;AACb,UAAMJ,OAAO,KAAKA;AAClB,UAAMC,UAAU,KAAKA;AACrB,QAAID,KAAKI,MAAM;AACd,aAAOJ,KAAKI,KAAKH,QAAQC,QAAO,GAAID,OAAO;;AAE5C;;EAzBD,YACQD,MACCC,SACP;SAFMD,OAAAA;SACCC,UAAAA;;;;;ADFH,SAASI,cACfC,MACAC,SACC;AACD,QAAMC,iBAAaC;IAAQ,MAAM,IAAIC,eAAeJ,MAAMC,OAAO;IAAG;MAACA;;EAAQ;AAC7EI,gCAAU,MAAM;AACfH,eAAWF,OAAOA;KAChB;IAACA;GAAK;AACT,SAAOE;;;;AELD,SAASI,wBACfC,MACAC,SACAC,WACO;AACP,QAAMC,UAAUC,mBAAkB;AAClC,QAAMC,aAAaC,cAAcN,MAAMC,OAAO;AAC9C,QAAMM,SAASC,UAAUR,IAAI;AAE7BS,4BACC,SAASC,qBAAqB;AAC7B,UAAM,CAACC,WAAWC,UAAU,IAAIC,eAC/BN,QACAF,YACAF,OAAO;AAERF,YAAQa,iBAAiBH,SAAS;AAClCT,cAAUY,iBAAiBH,SAAS;AACpC,WAAOC;KAER;IACCT;IACAF;IACAI;IACAH;IACAK,OAAOQ;MAAI,CAACC,MAAMA,EAAEC,SAAQ;IAAE,EAAEC,KAAK,GAAG;GACxC;;;;ACrBI,SAASC,QAKfC,SAGAC,MACsC;AACtC,QAAMC,OAAOC,mBAAmBH,SAASC,IAAI;AAC7C,QAAMG,UAAUC,qBAAoB;AACpC,QAAMC,YAAYC,uBAAuBL,KAAKM,OAAO;AACrDC,0BAAwBP,MAAME,SAASE,SAAS;AAEhD,SAAO;IACNI,kBAAkBR,KAAKS,SAASP,SAASE,SAAS;IAClDM,qBAAqBN,SAAS;;;", "names": ["equal", "invariant", "condition", "format", "args", "isProduction", "undefined", "Error", "error", "argIndex", "replace", "name", "framesToPop", "process", "get", "obj", "path", "defaultValue", "split", "reduce", "a", "c", "without", "items", "item", "filter", "i", "isObject", "input", "xor", "itemsA", "itemsB", "map", "Map", "insertItem", "item", "set", "has", "get", "for<PERSON>ach", "result", "count", "key", "push", "intersection", "filter", "t", "indexOf", "INIT_COORDS", "BEGIN_DRAG", "PUBLISH_DRAG_SOURCE", "HOVER", "DROP", "END_DRAG", "setClientOffset", "clientOffset", "sourceClientOffset", "type", "INIT_COORDS", "payload", "ResetCoordinatesAction", "type", "INIT_COORDS", "payload", "clientOffset", "sourceClientOffset", "createBeginDrag", "manager", "beginDrag", "sourceIds", "options", "publishSource", "getSourceClientOffset", "monitor", "getMonitor", "registry", "getRegistry", "dispatch", "setClientOffset", "verifyInvariants", "sourceId", "getDraggableSource", "Error", "verifyGetSourceClientOffsetIsFunction", "source", "getSource", "item", "undefined", "verifyItemIsObject", "pinSource", "itemType", "getSourceType", "BEGIN_DRAG", "isSourcePublic", "invariant", "isDragging", "for<PERSON>ach", "isObject", "i", "length", "canDragSource", "createDrop", "manager", "drop", "options", "monitor", "getMonitor", "registry", "getRegistry", "verifyInvariants", "targetIds", "getDroppableTargets", "for<PERSON>ach", "targetId", "index", "dropResult", "determineDropResult", "action", "type", "DROP", "payload", "dispatch", "invariant", "isDragging", "didDrop", "target", "get<PERSON><PERSON><PERSON>", "undefined", "verifyDropResultType", "getDropResult", "isObject", "getTargetIds", "filter", "canDropOnTarget", "reverse", "createEndDrag", "manager", "endDrag", "monitor", "getMonitor", "registry", "getRegistry", "verifyIsDragging", "sourceId", "getSourceId", "source", "getSource", "unpinSource", "type", "END_DRAG", "invariant", "isDragging", "matchesType", "targetType", "draggedItemType", "Array", "isArray", "some", "t", "createHover", "manager", "hover", "targetIdsArg", "clientOffset", "verifyTargetIdsIsArray", "targetIds", "slice", "monitor", "getMonitor", "registry", "getRegistry", "draggedItemType", "getItemType", "removeNonMatchingTargetIds", "checkInvariants", "hoverAllTargets", "type", "HOVER", "payload", "invariant", "Array", "isArray", "isDragging", "didDrop", "i", "length", "targetId", "lastIndexOf", "target", "get<PERSON><PERSON><PERSON>", "targetType", "getTargetType", "matchesType", "splice", "for<PERSON>ach", "createPublishDragSource", "manager", "publishDragSource", "monitor", "getMonitor", "isDragging", "type", "PUBLISH_DRAG_SOURCE", "createDragDropActions", "manager", "beginDrag", "createBeginDrag", "publishDragSource", "createPublishDragSource", "hover", "createHover", "drop", "createDrop", "endDrag", "createEndDrag", "DragDropManagerImpl", "receiveBackend", "backend", "getMonitor", "monitor", "getBackend", "getRegistry", "registry", "getActions", "manager", "dispatch", "store", "bindActionCreator", "actionCreator", "args", "action", "apply", "actions", "createDragDropActions", "Object", "keys", "reduce", "boundActions", "key", "isSetUp", "handleRefCountChange", "shouldSetUp", "getState", "refCount", "setup", "teardown", "subscribe", "add", "a", "b", "x", "y", "subtract", "getSourceClientOffset", "state", "clientOffset", "initialClientOffset", "initialSourceClientOffset", "getDifferenceFromInitialOffset", "NONE", "ALL", "__IS_NONE__", "__IS_ALL__", "areDirty", "dirtyIds", "handlerIds", "commonIds", "intersection", "length", "DragDropMonitorImpl", "subscribeToStateChange", "listener", "options", "handlerIds", "invariant", "Array", "isArray", "prevStateId", "store", "getState", "stateId", "handleChange", "state", "currentStateId", "canSkipListener", "areDirty", "dirtyHandlerIds", "subscribe", "subscribeToOffsetChange", "previousState", "dragOffset", "nextState", "canDragSource", "sourceId", "source", "registry", "getSource", "isDragging", "canDrag", "canDropOnTarget", "targetId", "target", "get<PERSON><PERSON><PERSON>", "didDrop", "targetType", "getTargetType", "draggedItemType", "getItemType", "matchesType", "canDrop", "Boolean", "isDraggingSource", "isSourcePublic", "sourceType", "getSourceType", "isOverTarget", "shallow", "targetIds", "getTargetIds", "length", "index", "indexOf", "dragOperation", "itemType", "getItem", "item", "getSourceId", "getDropResult", "dropResult", "getInitialClientOffset", "initialClientOffset", "getInitialSourceClientOffset", "initialSourceClientOffset", "getClientOffset", "clientOffset", "getSourceClientOffset", "getDifferenceFromInitialOffset", "scope", "global", "self", "BrowserMutationObserver", "MutationObserver", "WebKitMutationObserver", "makeRequestCallFromTimer", "callback", "requestCall", "timeoutH<PERSON>le", "setTimeout", "handleTimer", "intervalHandle", "setInterval", "clearTimeout", "clearInterval", "makeRequestCallFromMutationObserver", "toggle", "observer", "node", "document", "createTextNode", "observe", "characterData", "data", "makeRequestCall", "AsapQueue", "enqueueTask", "task", "queue", "q", "requestFlush", "length", "flushing", "pendingErrors", "index", "capacity", "flush", "currentIndex", "call", "scan", "<PERSON><PERSON><PERSON><PERSON>", "registerPendingError", "err", "push", "requestErrorThrow", "makeRequestCall", "makeRequestCallFromTimer", "shift", "RawTask", "call", "task", "error", "onError", "release", "TaskFactory", "create", "task", "tasks", "freeTasks", "t", "length", "pop", "RawTask", "onError", "asapQueue", "AsapQueue", "taskFactory", "TaskFactory", "registerPendingError", "asap", "task", "enqueueTask", "create", "ADD_SOURCE", "ADD_TARGET", "REMOVE_SOURCE", "REMOVE_TARGET", "addSource", "sourceId", "type", "payload", "addTarget", "targetId", "removeSource", "remove<PERSON>arget", "validateSourceContract", "source", "invariant", "canDrag", "beginDrag", "endDrag", "validateTargetContract", "target", "canDrop", "hover", "drop", "validateType", "type", "allowArray", "Array", "isArray", "for<PERSON>ach", "t", "HandlerRole", "SOURCE", "TARGET", "nextUniqueId", "getNextUniqueId", "getNextHandlerId", "role", "id", "getNextUniqueId", "toString", "HandlerRole", "SOURCE", "TARGET", "Error", "parseRoleFromHandlerId", "handlerId", "mapContainsValue", "map", "searchValue", "entries", "isDone", "done", "value", "next", "HandlerRegistryImpl", "addSource", "type", "source", "validateType", "validateSourceContract", "sourceId", "add<PERSON><PERSON><PERSON>", "store", "dispatch", "addTarget", "target", "validateTargetContract", "targetId", "<PERSON><PERSON><PERSON><PERSON>", "handler", "dragSources", "dropTargets", "getSource", "include<PERSON><PERSON>ed", "invariant", "isSourceId", "isPinned", "pinnedSourceId", "pinnedSource", "get", "get<PERSON><PERSON><PERSON>", "isTargetId", "getSourceType", "types", "getTargetType", "removeSource", "asap", "delete", "remove<PERSON>arget", "pinSource", "unpinSource", "set", "Map", "strictEquality", "a", "b", "areCoordsEqual", "offsetA", "offsetB", "x", "y", "areArraysEqual", "isEqual", "length", "i", "reduce", "_state", "NONE", "action", "type", "HOVER", "ADD_SOURCE", "ADD_TARGET", "REMOVE_TARGET", "REMOVE_SOURCE", "BEGIN_DRAG", "PUBLISH_DRAG_SOURCE", "END_DRAG", "DROP", "ALL", "targetIds", "prevTargetIds", "payload", "result", "xor", "<PERSON><PERSON><PERSON><PERSON>", "length", "areArraysEqual", "prevInnermostTargetId", "innermostTargetId", "push", "_defineProperty", "initialState", "initialSourceClientOffset", "initialClientOffset", "clientOffset", "reduce", "state", "action", "payload", "type", "INIT_COORDS", "BEGIN_DRAG", "sourceClientOffset", "HOVER", "areCoordsEqual", "_objectSpread", "END_DRAG", "DROP", "_defineProperty", "initialState", "itemType", "item", "sourceId", "targetIds", "dropResult", "didDrop", "isSourcePublic", "reduce", "state", "action", "payload", "type", "BEGIN_DRAG", "_objectSpread", "PUBLISH_DRAG_SOURCE", "HOVER", "REMOVE_TARGET", "indexOf", "targetId", "without", "DROP", "END_DRAG", "reduce", "state", "action", "type", "ADD_SOURCE", "ADD_TARGET", "REMOVE_SOURCE", "REMOVE_TARGET", "reduce", "state", "_defineProperty", "reduce", "state", "action", "dirtyHandlerIds", "type", "payload", "_objectSpread", "prevTargetIds", "get", "dragOffset", "refCount", "dragOperation", "stateId", "createDragDropManager", "backendFactory", "globalContext", "undefined", "backendOptions", "debugMode", "store", "makeStoreInstance", "monitor", "DragDropMonitorImpl", "HandlerRegistryImpl", "manager", "DragDropManagerImpl", "backend", "receiveBackend", "reduxDevTools", "window", "__REDUX_DEVTOOLS_EXTENSION__", "createStore", "reduce", "name", "instanceId", "import_react", "DndContext", "createContext", "dragDropManager", "undefined", "refCount", "INSTANCE_SYM", "Symbol", "for", "DndProvider", "memo", "children", "props", "manager", "isGlobalInstance", "getDndContextValue", "useEffect", "context", "getGlobalContext", "_jsx", "DndContext", "Provider", "value", "getDndContextValue", "props", "manager", "dragDropManager", "createSingletonDndContext", "backend", "context", "options", "debugMode", "isGlobalInstance", "getGlobalContext", "ctx", "INSTANCE_SYM", "createDragDropManager", "global", "window", "import_react", "DragPreviewImage", "memo", "connect", "src", "useEffect", "Image", "connected", "img", "onload", "import_react", "import_react", "useIsomorphicLayoutEffect", "window", "useLayoutEffect", "useEffect", "useCollector", "monitor", "collect", "onUpdate", "collected", "setCollected", "useState", "updateCollected", "useCallback", "nextValue", "equal", "useIsomorphicLayoutEffect", "useMonitorOutput", "monitor", "collect", "onCollect", "collected", "updateCollected", "useCollector", "useIsomorphicLayoutEffect", "subscribeToMonitorStateChange", "handlerId", "getHandlerId", "subscribeToStateChange", "handlerIds", "useCollectedProps", "collector", "monitor", "connector", "useMonitorOutput", "reconnect", "import_react", "useOptionalFactory", "arg", "deps", "memoDeps", "push", "useMemo", "import_react", "useConnectDragSource", "connector", "useMemo", "hooks", "dragSource", "useConnectDragPreview", "dragPreview", "import_react", "isCallingCanDrag", "isCallingIsDragging", "DragSourceMonitorImpl", "receiveHandlerId", "sourceId", "getHandlerId", "canDrag", "invariant", "internalMonitor", "canDragSource", "isDragging", "isDraggingSource", "subscribeToStateChange", "listener", "options", "isOverTarget", "targetId", "getTargetIds", "isSourcePublic", "getSourceId", "subscribeToOffsetChange", "canDropOnTarget", "getItemType", "getItem", "getDropResult", "didDrop", "getInitialClientOffset", "getInitialSourceClientOffset", "getSourceClientOffset", "getClientOffset", "getDifferenceFromInitialOffset", "manager", "getMonitor", "isCallingCanDrop", "DropTargetMonitorImpl", "receiveHandlerId", "targetId", "getHandlerId", "subscribeToStateChange", "listener", "options", "internalMonitor", "canDrop", "invariant", "canDropOnTarget", "isOver", "isOverTarget", "getItemType", "getItem", "getDropResult", "didDrop", "getInitialClientOffset", "getInitialSourceClientOffset", "getSourceClientOffset", "getClientOffset", "getDifferenceFromInitialOffset", "manager", "getMonitor", "registerTarget", "type", "target", "manager", "registry", "getRegistry", "targetId", "addTarget", "remove<PERSON>arget", "registerSource", "source", "sourceId", "addSource", "removeSource", "shallowEqual", "objA", "objB", "compare", "compareContext", "compareResult", "call", "keysA", "Object", "keys", "keysB", "length", "bHasOwnProperty", "prototype", "hasOwnProperty", "bind", "idx", "key", "valueA", "valueB", "isRef", "obj", "Object", "prototype", "hasOwnProperty", "call", "import_react", "throwIfCompositeComponentElement", "element", "type", "displayName", "name", "Error", "wrapHookToRecognizeElement", "hook", "elementOrNode", "options", "isValidElement", "node", "ref", "cloneWithRef", "wrapConnectorHooks", "hooks", "<PERSON><PERSON><PERSON>s", "Object", "keys", "for<PERSON>ach", "key", "endsWith", "wrappedHook", "setRef", "current", "newRef", "previousRef", "invariant", "cloneElement", "SourceConnector", "receiveHandlerId", "newHandlerId", "handlerId", "reconnect", "connectTarget", "dragSource", "dragSourceOptions", "dragSourceOptionsInternal", "options", "dragPreviewOptions", "dragPreviewOptionsInternal", "<PERSON><PERSON><PERSON><PERSON>", "reconnectDragSource", "reconnectDragPreview", "didHandlerIdChange", "didConnectedDragSourceChange", "didDragSourceOptionsChange", "disconnectDragSource", "lastConnectedDragSource", "lastConnectedHandlerId", "lastConnectedDragSourceOptions", "dragSourceUnsubscribe", "backend", "connectDragSource", "forceDidChange", "dragPreview", "didConnectedDragPreviewChange", "didDragPreviewOptionsChange", "disconnectDragPreview", "lastConnectedDragPreview", "lastConnectedDragPreviewOptions", "dragPreviewUnsubscribe", "connectDragPreview", "shallowEqual", "undefined", "dragPreviewNode", "dragPreviewRef", "dragSourceNode", "dragSourceRef", "current", "clearDragSource", "clearDragPreview", "hooks", "wrapConnectorHooks", "node", "isRef", "TargetConnector", "connectTarget", "drop<PERSON>ar<PERSON>", "reconnect", "<PERSON><PERSON><PERSON><PERSON>", "didHandlerIdChange", "didDropTargetChange", "didOptionsChange", "disconnectDropTarget", "handlerId", "lastConnectedDropTarget", "lastConnectedHandlerId", "lastConnectedDropTargetOptions", "dropTargetOptions", "unsubscribeDropTarget", "backend", "connectDropTarget", "receiveHandlerId", "newHandlerId", "dropTargetOptionsInternal", "options", "shallowEqual", "undefined", "dropTargetNode", "dropTargetRef", "current", "clearDropTarget", "hooks", "wrapConnectorHooks", "node", "isRef", "import_react", "useDragDropManager", "dragDropManager", "useContext", "DndContext", "invariant", "useDragSourceConnector", "dragSourceOptions", "dragPreviewOptions", "manager", "useDragDropManager", "connector", "useMemo", "SourceConnector", "getBackend", "useIsomorphicLayoutEffect", "reconnect", "disconnectDragSource", "disconnectDragPreview", "import_react", "useDragSourceMonitor", "manager", "useDragDropManager", "useMemo", "DragSourceMonitorImpl", "import_react", "DragSourceImpl", "beginDrag", "spec", "monitor", "result", "item", "canDrag", "isDragging", "globalMonitor", "target", "getSourceId", "endDrag", "connector", "end", "getItem", "reconnect", "useDragSource", "spec", "monitor", "connector", "handler", "useMemo", "DragSourceImpl", "useEffect", "import_react", "useDragType", "spec", "useMemo", "result", "type", "invariant", "useRegisteredDragSource", "spec", "monitor", "connector", "manager", "useDragDropManager", "handler", "useDragSource", "itemType", "useDragType", "useIsomorphicLayoutEffect", "registerDragSource", "handlerId", "unregister", "registerSource", "receiveHandlerId", "useDrag", "specArg", "deps", "spec", "useOptionalFactory", "invariant", "begin", "monitor", "useDragSourceMonitor", "connector", "useDragSourceConnector", "options", "previewOptions", "useRegisteredDragSource", "useCollectedProps", "collect", "useConnectDragSource", "useConnectDragPreview", "import_react", "useDragLayer", "collect", "dragDropManager", "useDragDropManager", "monitor", "getMonitor", "collected", "updateCollected", "useCollector", "useEffect", "subscribeToOffsetChange", "subscribeToStateChange", "import_react", "useConnectDropTarget", "connector", "useMemo", "hooks", "drop<PERSON>ar<PERSON>", "import_react", "useDropTargetConnector", "options", "manager", "useDragDropManager", "connector", "useMemo", "TargetConnector", "getBackend", "useIsomorphicLayoutEffect", "dropTargetOptions", "reconnect", "disconnectDropTarget", "import_react", "useDropTargetMonitor", "manager", "useDragDropManager", "useMemo", "DropTargetMonitorImpl", "import_react", "useAccept", "spec", "accept", "useMemo", "invariant", "Array", "isArray", "import_react", "DropTargetImpl", "canDrop", "spec", "monitor", "getItem", "hover", "drop", "useDropTarget", "spec", "monitor", "drop<PERSON>ar<PERSON>", "useMemo", "DropTargetImpl", "useEffect", "useRegisteredDropTarget", "spec", "monitor", "connector", "manager", "useDragDropManager", "drop<PERSON>ar<PERSON>", "useDropTarget", "accept", "useAccept", "useIsomorphicLayoutEffect", "registerDropTarget", "handlerId", "unregister", "registerTarget", "receiveHandlerId", "map", "a", "toString", "join", "useDrop", "specArg", "deps", "spec", "useOptionalFactory", "monitor", "useDropTargetMonitor", "connector", "useDropTargetConnector", "options", "useRegisteredDropTarget", "useCollectedProps", "collect", "useConnectDropTarget"]}