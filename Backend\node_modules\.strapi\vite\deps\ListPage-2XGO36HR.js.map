{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/Webhooks/ListPage.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {\n  useNotifyAT,\n  Checkbox,\n  Button,\n  EmptyStateLayout,\n  Flex,\n  IconButton,\n  Switch,\n  Table,\n  T<PERSON>,\n  T<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON>r,\n  <PERSON><PERSON><PERSON>,\n  <PERSON>ly<PERSON><PERSON><PERSON>,\n  LinkButton,\n  Dialog,\n} from '@strapi/design-system';\nimport { Pencil, Plus, Trash } from '@strapi/icons';\nimport { EmptyDocuments } from '@strapi/icons/symbols';\nimport { useIntl } from 'react-intl';\nimport { NavLink, useNavigate } from 'react-router-dom';\n\nimport { UpdateWebhook } from '../../../../../../shared/contracts/webhooks';\nimport { ConfirmDialog } from '../../../../components/ConfirmDialog';\nimport { Layouts } from '../../../../components/Layouts/Layout';\nimport { Page } from '../../../../components/PageHelpers';\nimport { useTypedSelector } from '../../../../core/store/hooks';\nimport { useNotification } from '../../../../features/Notifications';\nimport { useAPIErrorHandler } from '../../../../hooks/useAPIErrorHandler';\nimport { useRBAC } from '../../../../hooks/useRBAC';\n\nimport { useWebhooks } from './hooks/useWebhooks';\n\n/* -------------------------------------------------------------------------------------------------\n * ListPage\n * -----------------------------------------------------------------------------------------------*/\n\nconst ListPage = () => {\n  const [showModal, setShowModal] = React.useState(false);\n  const [webhooksToDelete, setWebhooksToDelete] = React.useState<string[]>([]);\n  const permissions = useTypedSelector((state) => state.admin_app.permissions.settings?.webhooks);\n  const { formatMessage } = useIntl();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n  const { toggleNotification } = useNotification();\n  const navigate = useNavigate();\n\n  const {\n    isLoading: isRBACLoading,\n    allowedActions: { canCreate, canUpdate, canDelete },\n  } = useRBAC(permissions);\n  const { notifyStatus } = useNotifyAT();\n\n  const {\n    isLoading: isWebhooksLoading,\n    webhooks,\n    error: webhooksError,\n    updateWebhook,\n    deleteManyWebhooks,\n  } = useWebhooks();\n\n  React.useEffect(() => {\n    if (webhooksError) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(webhooksError),\n      });\n\n      return;\n    }\n    if (webhooks) {\n      notifyStatus(\n        formatMessage({\n          id: 'Settings.webhooks.list.loading.success',\n          defaultMessage: 'Webhooks have been loaded',\n        })\n      );\n    }\n  }, [webhooks, webhooksError, toggleNotification, formatMessage, notifyStatus, formatAPIError]);\n\n  const enableWebhook = async (body: UpdateWebhook.Request['body'] & UpdateWebhook.Params) => {\n    try {\n      const res = await updateWebhook(body);\n\n      if ('error' in res) {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(res.error),\n        });\n      }\n    } catch {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'An error occurred',\n        }),\n      });\n    }\n  };\n\n  const deleteWebhook = async (id: string) => {\n    try {\n      const res = await deleteManyWebhooks({\n        ids: [id],\n      });\n\n      if ('error' in res) {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(res.error),\n        });\n\n        return;\n      }\n\n      setWebhooksToDelete((prev) => prev.filter((webhookId) => webhookId !== id));\n    } catch {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'An error occurred',\n        }),\n      });\n    }\n  };\n\n  const confirmBulkDelete = async () => {\n    try {\n      const res = await deleteManyWebhooks({\n        ids: webhooksToDelete,\n      });\n\n      if ('error' in res) {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(res.error),\n        });\n\n        return;\n      }\n\n      setWebhooksToDelete([]);\n    } catch {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'An error occurred',\n        }),\n      });\n    } finally {\n      setShowModal(false);\n    }\n  };\n\n  const selectAllCheckbox = (selected: boolean) =>\n    selected\n      ? setWebhooksToDelete(webhooks?.map((webhook) => webhook.id) ?? [])\n      : setWebhooksToDelete([]);\n\n  const selectOneCheckbox = (selected: boolean, id: string) =>\n    selected\n      ? setWebhooksToDelete((prev) => [...prev, id])\n      : setWebhooksToDelete((prev) => prev.filter((webhookId) => webhookId !== id));\n\n  const isLoading = isRBACLoading || isWebhooksLoading;\n  const numberOfWebhooks = webhooks?.length ?? 0;\n  const webhooksToDeleteLength = webhooksToDelete.length;\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Layouts.Root>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: 'Webhooks',\n          }\n        )}\n      </Page.Title>\n      <Page.Main aria-busy={isLoading}>\n        <Layouts.Header\n          title={formatMessage({ id: 'Settings.webhooks.title', defaultMessage: 'Webhooks' })}\n          subtitle={formatMessage({\n            id: 'Settings.webhooks.list.description',\n            defaultMessage: 'Get POST changes notifications',\n          })}\n          primaryAction={\n            canCreate &&\n            !isLoading && (\n              <LinkButton tag={NavLink} startIcon={<Plus />} variant=\"default\" to=\"create\" size=\"S\">\n                {formatMessage({\n                  id: 'Settings.webhooks.list.button.add',\n                  defaultMessage: 'Create new webhook',\n                })}\n              </LinkButton>\n            )\n          }\n        />\n        {webhooksToDeleteLength > 0 && canDelete && (\n          <Layouts.Action\n            startActions={\n              <>\n                <Typography variant=\"epsilon\" textColor=\"neutral600\">\n                  {formatMessage(\n                    {\n                      id: 'Settings.webhooks.to.delete',\n                      defaultMessage:\n                        '{webhooksToDeleteLength, plural, one {# webhook} other {# webhooks}} selected',\n                    },\n                    { webhooksToDeleteLength }\n                  )}\n                </Typography>\n                <Button\n                  onClick={() => setShowModal(true)}\n                  startIcon={<Trash />}\n                  size=\"L\"\n                  variant=\"danger-light\"\n                >\n                  {formatMessage({\n                    id: 'global.delete',\n                    defaultMessage: 'Delete',\n                  })}\n                </Button>\n              </>\n            }\n          />\n        )}\n        <Layouts.Content>\n          {numberOfWebhooks > 0 ? (\n            <Table\n              colCount={5}\n              rowCount={numberOfWebhooks + 1}\n              footer={\n                <TFooter\n                  onClick={() => {\n                    if (canCreate) {\n                      navigate('create');\n                    }\n                  }}\n                  icon={<Plus />}\n                >\n                  {formatMessage({\n                    id: 'Settings.webhooks.list.button.add',\n                    defaultMessage: 'Create new webhook',\n                  })}\n                </TFooter>\n              }\n            >\n              <Thead>\n                <Tr>\n                  <Th>\n                    <Checkbox\n                      aria-label={formatMessage({\n                        id: 'global.select-all-entries',\n                        defaultMessage: 'Select all entries',\n                      })}\n                      checked={\n                        webhooksToDeleteLength > 0 && webhooksToDeleteLength < numberOfWebhooks\n                          ? 'indeterminate'\n                          : webhooksToDeleteLength === numberOfWebhooks\n                      }\n                      onCheckedChange={selectAllCheckbox}\n                    />\n                  </Th>\n                  <Th width=\"20%\">\n                    <Typography variant=\"sigma\" textColor=\"neutral600\">\n                      {formatMessage({\n                        id: 'global.name',\n                        defaultMessage: 'Name',\n                      })}\n                    </Typography>\n                  </Th>\n                  <Th width=\"60%\">\n                    <Typography variant=\"sigma\" textColor=\"neutral600\">\n                      {formatMessage({\n                        id: 'Settings.webhooks.form.url',\n                        defaultMessage: 'URL',\n                      })}\n                    </Typography>\n                  </Th>\n                  <Th width=\"20%\">\n                    <Typography variant=\"sigma\" textColor=\"neutral600\">\n                      {formatMessage({\n                        id: 'Settings.webhooks.list.th.status',\n                        defaultMessage: 'Status',\n                      })}\n                    </Typography>\n                  </Th>\n                  <Th>\n                    <VisuallyHidden>\n                      {formatMessage({\n                        id: 'Settings.webhooks.list.th.actions',\n                        defaultMessage: 'Actions',\n                      })}\n                    </VisuallyHidden>\n                  </Th>\n                </Tr>\n              </Thead>\n              <Tbody>\n                {webhooks?.map((webhook) => (\n                  <Tr\n                    key={webhook.id}\n                    onClick={() => {\n                      if (canUpdate) {\n                        navigate(webhook.id);\n                      }\n                    }}\n                    style={{ cursor: canUpdate ? 'pointer' : 'default' }}\n                  >\n                    <Td onClick={(e) => e.stopPropagation()}>\n                      <Checkbox\n                        aria-label={`${formatMessage({\n                          id: 'global.select',\n                          defaultMessage: 'Select',\n                        })} ${webhook.name}`}\n                        checked={webhooksToDelete?.includes(webhook.id)}\n                        onCheckedChange={(selected) => selectOneCheckbox(!!selected, webhook.id)}\n                        name=\"select\"\n                      />\n                    </Td>\n                    <Td>\n                      <Typography fontWeight=\"semiBold\" textColor=\"neutral800\">\n                        {webhook.name}\n                      </Typography>\n                    </Td>\n                    <Td>\n                      <Typography textColor=\"neutral800\">{webhook.url}</Typography>\n                    </Td>\n                    <Td onClick={(e) => e.stopPropagation()}>\n                      <Flex>\n                        <Switch\n                          onLabel={formatMessage({\n                            id: 'global.enabled',\n                            defaultMessage: 'Enabled',\n                          })}\n                          offLabel={formatMessage({\n                            id: 'global.disabled',\n                            defaultMessage: 'Disabled',\n                          })}\n                          aria-label={`${webhook.name} ${formatMessage({\n                            id: 'Settings.webhooks.list.th.status',\n                            defaultMessage: 'Status',\n                          })}`}\n                          checked={webhook.isEnabled}\n                          onCheckedChange={(enabled) => {\n                            enableWebhook({\n                              ...webhook,\n                              isEnabled: enabled,\n                            });\n                          }}\n                          visibleLabels\n                        />\n                      </Flex>\n                    </Td>\n                    <Td>\n                      <Flex gap={1}>\n                        {canUpdate && (\n                          <IconButton\n                            label={formatMessage({\n                              id: 'Settings.webhooks.events.update',\n                              defaultMessage: 'Update',\n                            })}\n                            variant=\"ghost\"\n                          >\n                            <Pencil />\n                          </IconButton>\n                        )}\n                        {canDelete && (\n                          <DeleteActionButton\n                            onDelete={() => {\n                              deleteWebhook(webhook.id);\n                            }}\n                          />\n                        )}\n                      </Flex>\n                    </Td>\n                  </Tr>\n                ))}\n              </Tbody>\n            </Table>\n          ) : (\n            <EmptyStateLayout\n              icon={<EmptyDocuments width=\"160px\" />}\n              content={formatMessage({\n                id: 'Settings.webhooks.list.empty.description',\n                defaultMessage: 'No webhooks found',\n              })}\n              action={\n                canCreate ? (\n                  <LinkButton variant=\"secondary\" startIcon={<Plus />} tag={NavLink} to=\"create\">\n                    {formatMessage({\n                      id: 'Settings.webhooks.list.button.add',\n                      defaultMessage: 'Create new webhook',\n                    })}\n                  </LinkButton>\n                ) : null\n              }\n            />\n          )}\n        </Layouts.Content>\n      </Page.Main>\n      <Dialog.Root open={showModal} onOpenChange={setShowModal}>\n        <ConfirmDialog onConfirm={confirmBulkDelete} />\n      </Dialog.Root>\n    </Layouts.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * DeleteActionButton\n * -----------------------------------------------------------------------------------------------*/\n\ntype DeleteActionButtonProps = {\n  onDelete: () => void;\n};\n\nconst DeleteActionButton = ({ onDelete }: DeleteActionButtonProps) => {\n  const [showModal, setShowModal] = React.useState(false);\n  const { formatMessage } = useIntl();\n\n  return (\n    <>\n      <IconButton\n        onClick={(e) => {\n          e.stopPropagation();\n          setShowModal(true);\n        }}\n        label={formatMessage({\n          id: 'Settings.webhooks.events.delete',\n          defaultMessage: 'Delete webhook',\n        })}\n        variant=\"ghost\"\n      >\n        <Trash />\n      </IconButton>\n\n      <Dialog.Root open={showModal} onOpenChange={setShowModal}>\n        <ConfirmDialog\n          onConfirm={(e) => {\n            e?.stopPropagation();\n            onDelete();\n          }}\n        />\n      </Dialog.Root>\n    </>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ProtectedListView\n * -----------------------------------------------------------------------------------------------*/\n\nconst ProtectedListPage = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.webhooks.main\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <ListPage />\n    </Page.Protect>\n  );\n};\n\nexport { ListPage, ProtectedListPage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCkG,IAE5FA,WAAW,MAAA;AACf,QAAM,CAACC,WAAWC,YAAAA,IAAsBC,eAAS,KAAA;AACjD,QAAM,CAACC,kBAAkBC,mBAAAA,IAA6BF,eAAmB,CAAA,CAAE;AAC3E,QAAMG,cAAcC,iBAAiB,CAACC,UAAUA;;AAAAA,uBAAMC,UAAUH,YAAYI,aAA5BF,mBAAsCG;GAAAA;AACtF,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,yBAAyBC,eAAc,IAAKC,mBAAAA;AACpD,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAMC,WAAWC,YAAAA;AAEjB,QAAM,EACJC,WAAWC,eACXC,gBAAgB,EAAEC,WAAWC,WAAWC,UAAS,EAAE,IACjDC,QAAQrB,WAAAA;AACZ,QAAM,EAAEsB,aAAY,IAAKC,YAAAA;AAEzB,QAAM,EACJR,WAAWS,mBACXnB,UACAoB,OAAOC,eACPC,eACAC,mBAAkB,IAChBC,YAAAA;AAEJC,EAAMC,gBAAU,MAAA;AACd,QAAIL,eAAe;AACjBf,yBAAmB;QACjBqB,MAAM;QACNC,SAASxB,eAAeiB,aAAAA;MAC1B,CAAA;AAEA;IACF;AACA,QAAIrB,UAAU;AACZiB,mBACEhB,cAAc;QACZ4B,IAAI;QACJC,gBAAgB;MAClB,CAAA,CAAA;IAEJ;KACC;IAAC9B;IAAUqB;IAAef;IAAoBL;IAAegB;IAAcb;EAAe,CAAA;AAE7F,QAAM2B,gBAAgB,OAAOC,SAAAA;AAC3B,QAAI;AACF,YAAMC,MAAM,MAAMX,cAAcU,IAAAA;AAEhC,UAAI,WAAWC,KAAK;AAClB3B,2BAAmB;UACjBqB,MAAM;UACNC,SAASxB,eAAe6B,IAAIb,KAAK;QACnC,CAAA;MACF;IACF,QAAQ;AACNd,yBAAmB;QACjBqB,MAAM;QACNC,SAAS3B,cAAc;UACrB4B,IAAI;UACJC,gBAAgB;QAClB,CAAA;MACF,CAAA;IACF;EACF;AAEA,QAAMI,gBAAgB,OAAOL,OAAAA;AAC3B,QAAI;AACF,YAAMI,MAAM,MAAMV,mBAAmB;QACnCY,KAAK;UAACN;QAAG;MACX,CAAA;AAEA,UAAI,WAAWI,KAAK;AAClB3B,2BAAmB;UACjBqB,MAAM;UACNC,SAASxB,eAAe6B,IAAIb,KAAK;QACnC,CAAA;AAEA;MACF;AAEA1B,0BAAoB,CAAC0C,SAASA,KAAKC,OAAO,CAACC,cAAcA,cAAcT,EAAAA,CAAAA;IACzE,QAAQ;AACNvB,yBAAmB;QACjBqB,MAAM;QACNC,SAAS3B,cAAc;UACrB4B,IAAI;UACJC,gBAAgB;QAClB,CAAA;MACF,CAAA;IACF;EACF;AAEA,QAAMS,oBAAoB,YAAA;AACxB,QAAI;AACF,YAAMN,MAAM,MAAMV,mBAAmB;QACnCY,KAAK1C;MACP,CAAA;AAEA,UAAI,WAAWwC,KAAK;AAClB3B,2BAAmB;UACjBqB,MAAM;UACNC,SAASxB,eAAe6B,IAAIb,KAAK;QACnC,CAAA;AAEA;MACF;AAEA1B,0BAAoB,CAAA,CAAE;IACxB,QAAQ;AACNY,yBAAmB;QACjBqB,MAAM;QACNC,SAAS3B,cAAc;UACrB4B,IAAI;UACJC,gBAAgB;QAClB,CAAA;MACF,CAAA;cACQ;AACRvC,mBAAa,KAAA;IACf;EACF;AAEA,QAAMiD,oBAAoB,CAACC,aACzBA,WACI/C,qBAAoBM,qCAAU0C,IAAI,CAACC,YAAYA,QAAQd,QAAO,CAAA,CAAE,IAChEnC,oBAAoB,CAAA,CAAE;AAE5B,QAAMkD,oBAAoB,CAACH,UAAmBZ,OAC5CY,WACI/C,oBAAoB,CAAC0C,SAAS;IAAIA,GAAAA;IAAMP;GAAG,IAC3CnC,oBAAoB,CAAC0C,SAASA,KAAKC,OAAO,CAACC,cAAcA,cAAcT,EAAAA,CAAAA;AAE7E,QAAMnB,YAAYC,iBAAiBQ;AACnC,QAAM0B,oBAAmB7C,qCAAU8C,WAAU;AAC7C,QAAMC,yBAAyBtD,iBAAiBqD;AAEhD,MAAIpC,WAAW;AACb,eAAOsC,wBAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,aACEC,yBAACC,QAAQC,MAAI;;UACXL,wBAACC,KAAKK,OAAK;kBACRrD,cACC;UAAE4B,IAAI;UAAsBC,gBAAgB;WAC5C;UACEyB,MAAM;QACR,CAAA;;UAGJJ,yBAACF,KAAKO,MAAI;QAACC,aAAW/C;;cACpBsC,wBAACI,QAAQM,QAAM;YACbC,OAAO1D,cAAc;cAAE4B,IAAI;cAA2BC,gBAAgB;YAAW,CAAA;YACjF8B,UAAU3D,cAAc;cACtB4B,IAAI;cACJC,gBAAgB;YAClB,CAAA;YACA+B,eACEhD,aACA,CAACH,iBACCsC,wBAACc,YAAAA;cAAWC,KAAKC;cAASC,eAAWjB,wBAACkB,eAAAA,CAAAA,CAAAA;cAASC,SAAQ;cAAUC,IAAG;cAASC,MAAK;wBAC/EpE,cAAc;gBACb4B,IAAI;gBACJC,gBAAgB;cAClB,CAAA;;;UAKPiB,yBAAyB,KAAKhC,iBAC7BiC,wBAACI,QAAQkB,QAAM;YACbC,kBACEpB,yBAAAqB,6BAAA;;oBACExB,wBAACyB,YAAAA;kBAAWN,SAAQ;kBAAUO,WAAU;4BACrCzE,cACC;oBACE4B,IAAI;oBACJC,gBACE;qBAEJ;oBAAEiB;kBAAuB,CAAA;;oBAG7BC,wBAAC2B,QAAAA;kBACCC,SAAS,MAAMrF,aAAa,IAAA;kBAC5B0E,eAAWjB,wBAAC6B,cAAAA,CAAAA,CAAAA;kBACZR,MAAK;kBACLF,SAAQ;4BAEPlE,cAAc;oBACb4B,IAAI;oBACJC,gBAAgB;kBAClB,CAAA;;;;;cAMVkB,wBAACI,QAAQ0B,SAAO;YACbjC,UAAAA,mBAAmB,QAClBM,yBAAC4B,OAAAA;cACCC,UAAU;cACVC,UAAUpC,mBAAmB;cAC7BqC,YACElC,wBAACmC,SAAAA;gBACCP,SAAS,MAAA;AACP,sBAAI/D,WAAW;AACbL,6BAAS,QAAA;kBACX;gBACF;gBACA4E,UAAMpC,wBAACkB,eAAAA,CAAAA,CAAAA;0BAENjE,cAAc;kBACb4B,IAAI;kBACJC,gBAAgB;gBAClB,CAAA;;;oBAIJkB,wBAACqC,OAAAA;kBACC,cAAAlC,yBAACmC,IAAAA;;0BACCtC,wBAACuC,IAAAA;wBACC,cAAAvC,wBAACwC,cAAAA;0BACCC,cAAYxF,cAAc;4BACxB4B,IAAI;4BACJC,gBAAgB;0BAClB,CAAA;0BACA4D,SACE3C,yBAAyB,KAAKA,yBAAyBF,mBACnD,kBACAE,2BAA2BF;0BAEjC8C,iBAAiBnD;;;0BAGrBQ,wBAACuC,IAAAA;wBAAGK,OAAM;wBACR,cAAA5C,wBAACyB,YAAAA;0BAAWN,SAAQ;0BAAQO,WAAU;oCACnCzE,cAAc;4BACb4B,IAAI;4BACJC,gBAAgB;0BAClB,CAAA;;;0BAGJkB,wBAACuC,IAAAA;wBAAGK,OAAM;wBACR,cAAA5C,wBAACyB,YAAAA;0BAAWN,SAAQ;0BAAQO,WAAU;oCACnCzE,cAAc;4BACb4B,IAAI;4BACJC,gBAAgB;0BAClB,CAAA;;;0BAGJkB,wBAACuC,IAAAA;wBAAGK,OAAM;wBACR,cAAA5C,wBAACyB,YAAAA;0BAAWN,SAAQ;0BAAQO,WAAU;oCACnCzE,cAAc;4BACb4B,IAAI;4BACJC,gBAAgB;0BAClB,CAAA;;;0BAGJkB,wBAACuC,IAAAA;wBACC,cAAAvC,wBAAC6C,gBAAAA;oCACE5F,cAAc;4BACb4B,IAAI;4BACJC,gBAAgB;0BAClB,CAAA;;;;;;oBAKRkB,wBAAC8C,OAAAA;4BACE9F,qCAAU0C,IAAI,CAACC,gBACdQ,yBAACmC,IAAAA;oBAECV,SAAS,MAAA;AACP,0BAAI9D,WAAW;AACbN,iCAASmC,QAAQd,EAAE;sBACrB;oBACF;oBACAkE,OAAO;sBAAEC,QAAQlF,YAAY,YAAY;oBAAU;;0BAEnDkC,wBAACiD,IAAAA;wBAAGrB,SAAS,CAACsB,MAAMA,EAAEC,gBAAe;wBACnC,cAAAnD,wBAACwC,cAAAA;0BACCC,cAAY,GAAGxF,cAAc;4BAC3B4B,IAAI;4BACJC,gBAAgB;0BAClB,CAAA,CAAA,IAAMa,QAAQY,IAAI;0BAClBmC,SAASjG,qDAAkB2G,SAASzD,QAAQd;0BAC5C8D,iBAAiB,CAAClD,aAAaG,kBAAkB,CAAC,CAACH,UAAUE,QAAQd,EAAE;0BACvE0B,MAAK;;;0BAGTP,wBAACiD,IAAAA;wBACC,cAAAjD,wBAACyB,YAAAA;0BAAW4B,YAAW;0BAAW3B,WAAU;0BACzC/B,UAAAA,QAAQY;;;0BAGbP,wBAACiD,IAAAA;wBACC,cAAAjD,wBAACyB,YAAAA;0BAAWC,WAAU;0BAAc/B,UAAAA,QAAQ2D;;;0BAE9CtD,wBAACiD,IAAAA;wBAAGrB,SAAS,CAACsB,MAAMA,EAAEC,gBAAe;wBACnC,cAAAnD,wBAACuD,MAAAA;0BACC,cAAAvD,wBAACwD,YAAAA;4BACCC,SAASxG,cAAc;8BACrB4B,IAAI;8BACJC,gBAAgB;4BAClB,CAAA;4BACA4E,UAAUzG,cAAc;8BACtB4B,IAAI;8BACJC,gBAAgB;4BAClB,CAAA;4BACA2D,cAAY,GAAG9C,QAAQY,IAAI,IAAItD,cAAc;8BAC3C4B,IAAI;8BACJC,gBAAgB;4BAClB,CAAA,CAAA;4BACA4D,SAAS/C,QAAQgE;4BACjBhB,iBAAiB,CAACiB,YAAAA;AAChB7E,4CAAc;gCACZ,GAAGY;gCACHgE,WAAWC;8BACb,CAAA;4BACF;4BACAC,eAAa;;;;0BAInB7D,wBAACiD,IAAAA;wBACC,cAAA9C,yBAACoD,MAAAA;0BAAKO,KAAK;;4BACRhG,iBACCkC,wBAAC+D,YAAAA;8BACCC,OAAO/G,cAAc;gCACnB4B,IAAI;gCACJC,gBAAgB;8BAClB,CAAA;8BACAqC,SAAQ;8BAER,cAAAnB,wBAACiE,eAAAA,CAAAA,CAAAA;;4BAGJlG,iBACCiC,wBAACkE,oBAAAA;8BACCC,UAAU,MAAA;AACRjF,8CAAcS,QAAQd,EAAE;8BAC1B;;;;;;kBAtEHc,GAAAA,QAAQd,EAAE;;;qBAgFvBmB,wBAACoE,kBAAAA;cACChC,UAAMpC,wBAACqE,cAAAA;gBAAezB,OAAM;;cAC5B0B,SAASrH,cAAc;gBACrB4B,IAAI;gBACJC,gBAAgB;cAClB,CAAA;cACAyF,QACE1G,gBACEmC,wBAACc,YAAAA;gBAAWK,SAAQ;gBAAYF,eAAWjB,wBAACkB,eAAAA,CAAAA,CAAAA;gBAASH,KAAKC;gBAASI,IAAG;0BACnEnE,cAAc;kBACb4B,IAAI;kBACJC,gBAAgB;gBAClB,CAAA;cAEA,CAAA,IAAA;;;;;UAMdkB,wBAACwE,OAAOnE,MAAI;QAACoE,MAAMnI;QAAWoI,cAAcnI;QAC1C,cAAAyD,wBAAC2E,eAAAA;UAAcC,WAAWrF;;;;;AAIlC;AAUA,IAAM2E,qBAAqB,CAAC,EAAEC,SAAQ,MAA2B;AAC/D,QAAM,CAAC7H,WAAWC,YAAAA,IAAsBC,eAAS,KAAA;AACjD,QAAM,EAAES,cAAa,IAAKC,QAAAA;AAE1B,aACEiD,yBAAAqB,6BAAA;;UACExB,wBAAC+D,YAAAA;QACCnC,SAAS,CAACsB,MAAAA;AACRA,YAAEC,gBAAe;AACjB5G,uBAAa,IAAA;QACf;QACAyH,OAAO/G,cAAc;UACnB4B,IAAI;UACJC,gBAAgB;QAClB,CAAA;QACAqC,SAAQ;QAER,cAAAnB,wBAAC6B,cAAAA,CAAAA,CAAAA;;UAGH7B,wBAACwE,OAAOnE,MAAI;QAACoE,MAAMnI;QAAWoI,cAAcnI;QAC1C,cAAAyD,wBAAC2E,eAAAA;UACCC,WAAW,CAAC1B,MAAAA;AACVA,mCAAGC;AACHgB,qBAAAA;UACF;;;;;AAKV;AAIkG,IAE5FU,oBAAoB,MAAA;AACxB,QAAMlI,cAAcC,iBAClB,CAACC,UAAUA;;AAAAA,uBAAMC,UAAUH,YAAYI,aAA5BF,mBAAsCG,SAAS8H;GAAAA;AAG5D,aACE9E,wBAACC,KAAK8E,SAAO;IAACpI;IACZ,cAAAqD,wBAAC3D,UAAAA,CAAAA,CAAAA;;AAGP;", "names": ["ListPage", "showModal", "setShowModal", "useState", "webhooksToDelete", "setWebhooksToDelete", "permissions", "useTypedSelector", "state", "admin_app", "settings", "webhooks", "formatMessage", "useIntl", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "toggleNotification", "useNotification", "navigate", "useNavigate", "isLoading", "isRBACLoading", "allowedActions", "canCreate", "canUpdate", "canDelete", "useRBAC", "notify<PERSON><PERSON><PERSON>", "useNotifyAT", "isWebhooksLoading", "error", "webhooksError", "updateWebhook", "deleteManyWebhooks", "useWebhooks", "React", "useEffect", "type", "message", "id", "defaultMessage", "enableWebhook", "body", "res", "deleteWebhook", "ids", "prev", "filter", "webhookId", "confirmBulkDelete", "selectAllCheckbox", "selected", "map", "webhook", "selectOneCheckbox", "numberOfWebhooks", "length", "webhooksToDeleteLength", "_jsx", "Page", "Loading", "_jsxs", "Layouts", "Root", "Title", "name", "Main", "aria-busy", "Header", "title", "subtitle", "primaryAction", "LinkButton", "tag", "NavLink", "startIcon", "Plus", "variant", "to", "size", "Action", "startActions", "_Fragment", "Typography", "textColor", "<PERSON><PERSON>", "onClick", "Trash", "Content", "Table", "col<PERSON>ount", "rowCount", "footer", "TF<PERSON>er", "icon", "<PERSON><PERSON>", "Tr", "Th", "Checkbox", "aria-label", "checked", "onCheckedChange", "width", "VisuallyHidden", "Tbody", "style", "cursor", "Td", "e", "stopPropagation", "includes", "fontWeight", "url", "Flex", "Switch", "onLabel", "offLabel", "isEnabled", "enabled", "visible<PERSON><PERSON><PERSON>", "gap", "IconButton", "label", "Pencil", "DeleteActionButton", "onDelete", "EmptyStateLayout", "EmptyDocuments", "content", "action", "Dialog", "open", "onOpenChange", "ConfirmDialog", "onConfirm", "ProtectedListPage", "main", "Protect"]}