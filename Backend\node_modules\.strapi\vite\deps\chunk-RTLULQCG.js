import {
  ForwardRef$11,
  ForwardRef$13,
  ForwardRef$3,
  ForwardRef$5,
  ForwardRef$D,
  ForwardRef$N,
  ForwardRef$P,
  ForwardRef$V,
  ForwardRef$X,
  ForwardRef$b,
  ForwardRef$h,
  ForwardRef$j,
  ForwardRef$n,
  ForwardRef$r
} from "./chunk-7LKLOY7A.js";
import {
  useStrapiApp
} from "./chunk-ODQFI753.js";
import {
  Box
} from "./chunk-7GC3Y62Q.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/dist/admin/components/FieldTypeIcon.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var iconByTypes = {
  biginteger: (0, import_jsx_runtime.jsx)(ForwardRef$j, {}),
  boolean: (0, import_jsx_runtime.jsx)(ForwardRef$11, {}),
  date: (0, import_jsx_runtime.jsx)(ForwardRef$V, {}),
  datetime: (0, import_jsx_runtime.jsx)(ForwardRef$V, {}),
  decimal: (0, import_jsx_runtime.jsx)(ForwardRef$j, {}),
  email: (0, import_jsx_runtime.jsx)(ForwardRef$N, {}),
  enumeration: (0, import_jsx_runtime.jsx)(ForwardRef$D, {}),
  float: (0, import_jsx_runtime.jsx)(ForwardRef$j, {}),
  integer: (0, import_jsx_runtime.jsx)(ForwardRef$j, {}),
  media: (0, import_jsx_runtime.jsx)(ForwardRef$n, {}),
  password: (0, import_jsx_runtime.jsx)(ForwardRef$h, {}),
  relation: (0, import_jsx_runtime.jsx)(ForwardRef$b, {}),
  string: (0, import_jsx_runtime.jsx)(ForwardRef$5, {}),
  text: (0, import_jsx_runtime.jsx)(ForwardRef$5, {}),
  richtext: (0, import_jsx_runtime.jsx)(ForwardRef$5, {}),
  time: (0, import_jsx_runtime.jsx)(ForwardRef$V, {}),
  timestamp: (0, import_jsx_runtime.jsx)(ForwardRef$V, {}),
  json: (0, import_jsx_runtime.jsx)(ForwardRef$r, {}),
  uid: (0, import_jsx_runtime.jsx)(ForwardRef$3, {}),
  component: (0, import_jsx_runtime.jsx)(ForwardRef$X, {}),
  dynamiczone: (0, import_jsx_runtime.jsx)(ForwardRef$P, {}),
  blocks: (0, import_jsx_runtime.jsx)(ForwardRef$13, {})
};
var FieldTypeIcon = ({ type, customFieldUid }) => {
  const getCustomField = useStrapiApp("FieldTypeIcon", (state) => state.customFields.get);
  if (!type) {
    return null;
  }
  let Compo = iconByTypes[type];
  if (customFieldUid) {
    const customField = getCustomField(customFieldUid);
    const CustomFieldIcon = customField == null ? void 0 : customField.icon;
    if (CustomFieldIcon) {
      Compo = (0, import_jsx_runtime.jsx)(Box, {
        marginRight: 3,
        width: 7,
        height: 6,
        children: (0, import_jsx_runtime.jsx)(CustomFieldIcon, {})
      });
    }
  }
  if (!iconByTypes[type]) {
    return null;
  }
  return Compo;
};

export {
  FieldTypeIcon
};
//# sourceMappingURL=chunk-RTLULQCG.js.map
