{"version": 3, "sources": ["../../../@strapi/icons/src/symbols/BlocksField.tsx", "../../../@strapi/icons/src/symbols/BooleanField.tsx", "../../../@strapi/icons/src/symbols/CodeSquare.tsx", "../../../@strapi/icons/src/symbols/CollectionType.tsx", "../../../@strapi/icons/src/symbols/ComponentField.tsx", "../../../@strapi/icons/src/symbols/DateField.tsx", "../../../@strapi/icons/src/symbols/Discord.tsx", "../../../@strapi/icons/src/symbols/Discourse.tsx", "../../../@strapi/icons/src/symbols/DynamicZoneField.tsx", "../../../@strapi/icons/src/symbols/EmailField.tsx", "../../../@strapi/icons/src/symbols/EmptyData.tsx", "../../../@strapi/icons/src/symbols/EmptyDocuments.tsx", "../../../@strapi/icons/src/symbols/EmptyPermissions.tsx", "../../../@strapi/icons/src/symbols/EmptyPictures.tsx", "../../../@strapi/icons/src/symbols/EnumerationField.tsx", "../../../@strapi/icons/src/symbols/Facebook.tsx", "../../../@strapi/icons/src/symbols/FeatherSquare.tsx", "../../../@strapi/icons/src/symbols/GitHub.tsx", "../../../@strapi/icons/src/symbols/GlassesSquare.tsx", "../../../@strapi/icons/src/symbols/InformationSquare.tsx", "../../../@strapi/icons/src/symbols/JsonField.tsx", "../../../@strapi/icons/src/symbols/MarkdownField.tsx", "../../../@strapi/icons/src/symbols/MediaField.tsx", "../../../@strapi/icons/src/symbols/Medium.tsx", "../../../@strapi/icons/src/symbols/NumberField.tsx", "../../../@strapi/icons/src/symbols/PasswordField.tsx", "../../../@strapi/icons/src/symbols/PlaySquare.tsx", "../../../@strapi/icons/src/symbols/Reddit.tsx", "../../../@strapi/icons/src/symbols/RelationField.tsx", "../../../@strapi/icons/src/symbols/SingleType.tsx", "../../../@strapi/icons/src/symbols/Strapi.tsx", "../../../@strapi/icons/src/symbols/TextField.tsx", "../../../@strapi/icons/src/symbols/UidField.tsx", "../../../@strapi/icons/src/symbols/X.tsx", "../../../@strapi/admin/admin/src/utils/getPrefixedId.ts", "../../../@strapi/admin/admin/src/utils/normalizeAPIError.ts", "../../../@strapi/admin/admin/src/hooks/useAPIErrorHandler.ts", "../../../@strapi/admin/admin/src/components/PageHelpers.tsx"], "sourcesContent": ["import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgBlocksField = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <rect width={31} height={23} x={0.5} y={4.5} fill=\"#EAF5FF\" stroke=\"#B8E1FF\" rx={2.5} />\n    <path\n      fill=\"#0C75AF\"\n      d=\"M14.75 11.75a1 1 0 1 1-2 0 1 1 0 0 1 2 0m3.5 1a1 1 0 1 0 0-2 1 1 0 0 0 0 2M13.75 15a1 1 0 1 0 0 2 1 1 0 0 0 0-2m4.5 0a1 1 0 1 0 0 2 1 1 0 0 0 0-2m-4.5 4.25a1 1 0 1 0 0 2 1 1 0 0 0 0-2m4.5 0a1 1 0 1 0 0 2 1 1 0 0 0 0-2\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgBlocksField);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgBooleanField = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <rect width={31} height={23} x={0.5} y={4.5} fill=\"#EAFBE7\" stroke=\"#C6F0C2\" rx={2.5} />\n    <path\n      fill=\"#328048\"\n      d=\"M19 11.5h-6a4.5 4.5 0 1 0 0 9h6a4.5 4.5 0 1 0 0-9m0 7a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgBooleanField);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgCodeSquare = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <path fill=\"#D9822F\" d=\"M0 4a4 4 0 0 1 4-4h24a4 4 0 0 1 4 4v24a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4z\" />\n    <path\n      fill=\"#fff\"\n      fillRule=\"evenodd\"\n      d=\"M17.143 18.659v2.912l6.856-3.878v-2.815L17.143 11v2.906l4.16 2.38zm-2.287 0-4.16-2.374 4.16-2.38V11L8 14.877v2.816l6.856 3.878z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgCodeSquare);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgCollectionType = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <rect width={31} height={23} x={0.5} y={4.5} fill=\"#4945FF\" stroke=\"#4945FF\" rx={2.5} />\n    <path\n      fill=\"#fff\"\n      d=\"M14.328 14.54v-.083c-.04-.937-.75-1.559-1.787-1.559-1.535 0-2.725 1.57-2.725 3.65 0 1.302.71 2.104 1.846 2.104.961 0 1.787-.545 2.063-1.37h1.752c-.37 1.78-1.922 2.935-3.967 2.935-2.121 0-3.504-1.395-3.504-3.545 0-3.123 1.951-5.344 4.646-5.344 1.94 0 3.41 1.283 3.41 2.96 0 .087 0 .163-.011.251zM20.053 20H18.27l1.489-6.943h-2.532l.311-1.512h6.844l-.31 1.512H21.54z\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgCollectionType);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgComponentField = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <rect width={31} height={23} x={0.5} y={4.5} fill=\"#F6F6F9\" stroke=\"#DCDCE4\" rx={2.5} />\n    <path\n      fill=\"#666687\"\n      d=\"M20.5 17.5c-.358 0-.71.085-1.029.25l-1.337-1.04q.11-.326.116-.67l.647-.214a2.25 2.25 0 1 0-.637-1.37l-.486.162A2.25 2.25 0 0 0 16 13.75c-.062 0-.117 0-.176.008l-.278-.625A2.25 2.25 0 1 0 14 13.75c.063 0 .117 0 .176-.008l.278.625a2.24 2.24 0 0 0-.537 2.482l-1.33 1.182a2.25 2.25 0 1 0 .996 1.12l1.33-1.182a2.25 2.25 0 0 0 2.3-.075l1.224.954A2.25 2.25 0 1 0 20.5 17.5m0-4a.75.75 0 1 1 0 1.5.75.75 0 0 1 0-1.5m-7.25-2a.75.75 0 1 1 1.5 0 .75.75 0 0 1-1.5 0m-1.75 9.25a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5M15.25 16a.75.75 0 1 1 1.5 0 .75.75 0 0 1-1.5 0m5.25 4.5a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgComponentField);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgDateField = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <rect width={31} height={23} x={0.5} y={4.5} fill=\"#FDF4DC\" stroke=\"#FAE7B9\" rx={2.5} />\n    <path\n      fill=\"#D9822F\"\n      d=\"M21 10h-1.5v-.5a.5.5 0 0 0-1 0v.5h-5v-.5a.5.5 0 0 0-1 0v.5H11a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V11a1 1 0 0 0-1-1m0 3H11v-2h1.5v.5a.5.5 0 0 0 1 0V11h5v.5a.5.5 0 0 0 1 0V11H21z\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgDateField);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgDiscord = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <path\n      fill=\"#5865F2\"\n      d=\"M27.107 5.911a26.5 26.5 0 0 0-6.602-2.031 18 18 0 0 0-.845 1.72 24.6 24.6 0 0 0-7.327 0 18 18 0 0 0-.846-1.72A26.7 26.7 0 0 0 4.88 5.916C.702 12.098-.43 18.126.136 24.068a26.6 26.6 0 0 0 8.097 4.065 19.6 19.6 0 0 0 1.734-2.796c-.947-.354-1.86-.79-2.73-1.304.228-.166.452-.337.669-.504a19.02 19.02 0 0 0 16.188 0q.33.271.67.504c-.872.515-1.788.952-2.736 1.306a19.4 19.4 0 0 0 1.734 2.794 26.5 26.5 0 0 0 8.102-4.062c.665-6.892-1.135-12.864-4.757-18.16M10.684 20.414c-1.578 0-2.882-1.433-2.882-3.194 0-1.762 1.259-3.207 2.877-3.207 1.619 0 2.912 1.445 2.885 3.207s-1.271 3.194-2.88 3.194m10.632 0c-1.581 0-2.88-1.433-2.88-3.194 0-1.762 1.259-3.207 2.88-3.207s2.904 1.445 2.877 3.207-1.269 3.194-2.877 3.194\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgDiscord);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgDiscourse = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <path\n      fill=\"#231F20\"\n      d=\"M15.659.302C7.158.302 0 7.194 0 15.698v15.943l15.656-.015c8.501 0 15.396-7.158 15.396-15.66 0-8.5-6.901-15.664-15.393-15.664\"\n    />\n    <path\n      fill=\"#FFF9AE\"\n      d=\"M15.81 6.261a9.546 9.546 0 0 0-8.39 14.09l-1.726 5.554 6.2-1.4A9.541 9.541 0 1 0 15.82 6.26z\"\n    />\n    <path\n      fill=\"#00AEEF\"\n      d=\"M23.381 9.999a9.54 9.54 0 0 1-11.487 14.49l-6.2 1.419 6.312-.746A9.54 9.54 0 0 0 23.381 10\"\n    />\n    <path\n      fill=\"#00A94F\"\n      d=\"M21.624 8.239a9.54 9.54 0 0 1-9.91 15.61l-6.02 2.059 6.2-1.404a9.54 9.54 0 0 0 9.73-16.265\"\n    />\n    <path fill=\"#F15D22\" d=\"M7.991 20.562A9.542 9.542 0 0 1 23.387 9.994 9.543 9.543 0 0 0 7.42 20.35l-1.726 5.555z\" />\n    <path fill=\"#E31B23\" d=\"M7.42 20.35A9.543 9.543 0 0 1 21.624 8.238 9.543 9.543 0 0 0 6.832 20.202l-1.135 5.706z\" />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgDiscourse);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgDynamicZoneField = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <rect width={31} height={23} x={0.5} y={4.5} fill=\"#F6F6F9\" stroke=\"#DCDCE4\" rx={2.5} />\n    <path\n      fill=\"#666687\"\n      d=\"M23.75 16a3.75 3.75 0 0 1-6.402 2.652l-.03-.033-3.742-4.225a2.25 2.25 0 1 0 0 3.212l.193-.218a.75.75 0 1 1 1.125.994l-.21.237-.03.033a3.75 3.75 0 1 1 0-5.304l.03.033 3.742 4.225a2.25 2.25 0 1 0 0-3.212l-.193.218a.751.751 0 1 1-1.125-.995l.21-.236.03-.033A3.75 3.75 0 0 1 23.75 16\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgDynamicZoneField);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgEmailField = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <rect width={31} height={23} x={0.5} y={4.5} fill=\"#FCECEA\" stroke=\"#F5C0B8\" rx={2.5} />\n    <path\n      fill=\"#D02B20\"\n      d=\"M16 9.25a6.75 6.75 0 0 0 0 13.5c1.392 0 2.856-.42 3.915-1.125a.75.75 0 1 0-.83-1.25c-.813.54-1.994.875-3.085.875A5.25 5.25 0 1 1 21.25 16c0 .58-.104 1.067-.293 1.372-.165.265-.375.378-.707.378s-.542-.113-.707-.378c-.187-.305-.293-.791-.293-1.372v-2.5a.75.75 0 0 0-1.468-.216 3.25 3.25 0 1 0 .554 4.973c.433.637 1.09.993 1.914.993 1.542 0 2.5-1.245 2.5-3.25A6.76 6.76 0 0 0 16 9.25m0 8.5a1.75 1.75 0 1 1 0-3.5 1.75 1.75 0 0 1 0 3.5\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgEmailField);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgEmptyData = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={16} fill=\"none\" viewBox=\"0 0 217 121\" ref={ref} {...props}>\n    <g clipPath=\"url(#EmptyData_svg__a)\">\n      <path\n        stroke=\"#EEEEFA\"\n        strokeDasharray=\"3.93 4.91\"\n        strokeLinecap=\"round\"\n        strokeWidth={0.982}\n        d=\"M1.158 99.652h215.018M1.158 80.015h215.018M1.158 59.397h215.018M1.158 38.779h215.018M1.158 20.124h215.018\"\n      />\n      <path\n        fill=\"#D9D8FF\"\n        fillOpacity={0.8}\n        fillRule=\"evenodd\"\n        d=\"M182.63 25.086c4.112 0 7.446 3.279 7.446 7.323s-3.334 7.324-7.446 7.324h-42.545c4.112 0 7.445 3.279 7.445 7.323s-3.333 7.324-7.445 7.324h23.4c4.112 0 7.445 3.278 7.445 7.323 0 4.044-3.333 7.323-7.445 7.323h-10.821c-5.185 0-9.388 3.28-9.388 7.324q0 2.847 3.162 5.314c1.968 1.536 4.676 1.736 6.861 2.943 2.27 1.255 3.804 3.646 3.804 6.39 0 4.044-3.333 7.323-7.445 7.323H61.376c-4.112 0-7.446-3.279-7.446-7.323s3.334-7.324 7.446-7.324H19.894c-4.112 0-7.445-3.279-7.445-7.323s3.333-7.324 7.445-7.324H62.44c4.112 0 7.445-3.278 7.445-7.323s-3.334-7.324-7.446-7.324H35.85c-4.112 0-7.446-3.278-7.446-7.323 0-4.044 3.334-7.323 7.446-7.323h42.545c-4.112 0-7.445-3.28-7.445-7.324s3.333-7.323 7.445-7.323zm0 29.294c4.112 0 7.446 3.278 7.446 7.323 0 4.044-3.334 7.323-7.446 7.323s-7.445-3.278-7.445-7.323 3.333-7.324 7.445-7.324\"\n        clipRule=\"evenodd\"\n      />\n      <rect\n        width={27}\n        height={79.036}\n        x={109.403}\n        y={33.133}\n        fill=\"#fff\"\n        stroke=\"#7B79FF\"\n        strokeWidth={2.455}\n        rx={4.173}\n      />\n      <rect width={27} height={102.6} x={74.058} y={9.57} fill=\"#fff\" stroke=\"#7B79FF\" strokeWidth={2.455} rx={4.173} />\n      <rect\n        width={27}\n        height={58.418}\n        x={39.203}\n        y={53.26}\n        fill=\"#fff\"\n        stroke=\"#7B79FF\"\n        strokeWidth={2.455}\n        rx={4.173}\n      />\n      <rect\n        width={27}\n        height={55.473}\n        x={144.748}\n        y={56.697}\n        fill=\"#fff\"\n        stroke=\"#7B79FF\"\n        strokeWidth={2.455}\n        rx={4.173}\n      />\n      <rect width={21.6} height={53.018} x={41.903} y={55.961} fill=\"#EEEEFA\" rx={1.964} />\n      <rect width={21.6} height={73.636} x={112.103} y={35.833} fill=\"#EEEEFA\" rx={1.964} />\n    </g>\n    <defs>\n      <clipPath id=\"EmptyData_svg__a\">\n        <path fill=\"#fff\" d=\"M.667.797h216v120h-216z\" />\n      </clipPath>\n    </defs>\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgEmptyData);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgEmptyDocuments = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={16} fill=\"none\" viewBox=\"0 0 217 121\" ref={ref} {...props}>\n    <g clipPath=\"url(#EmptyDocuments_svg__a)\" opacity={0.84}>\n      <path\n        fill=\"#D9D8FF\"\n        fillOpacity={0.8}\n        fillRule=\"evenodd\"\n        d=\"M189.917 20.442a7.583 7.583 0 0 1 0 15.167h-43.334a7.584 7.584 0 1 1 0 15.167h23.834a7.583 7.583 0 0 1 0 15.166h-11.022c-5.281 0-9.562 3.396-9.562 7.584q0 2.934 3.19 5.479c2.017 1.608 4.824 1.818 7.065 3.097a7.584 7.584 0 0 1-3.755 14.174H66.417a7.583 7.583 0 1 1 0-15.167h-42.25a7.583 7.583 0 0 1 0-15.167H67.5a7.583 7.583 0 0 0 0-15.166H40.417a7.583 7.583 0 0 1 0-15.167H83.75a7.583 7.583 0 0 1 0-15.167zm0 30.334a7.583 7.583 0 0 1 0 15.166 7.584 7.584 0 0 1 0-15.166\"\n        clipRule=\"evenodd\"\n      />\n      <path\n        fill=\"#fff\"\n        fillRule=\"evenodd\"\n        d=\"m133.228 20.443 10.077 73.496.905 7.373a4.33 4.33 0 0 1-3.773 4.829l-63.44 7.79a4.334 4.334 0 0 1-4.83-3.773l-9.766-79.547a2.167 2.167 0 0 1 1.886-2.414l.023-.003 5.263-.59zm-59.4 6.683 4.97-.557z\"\n        clipRule=\"evenodd\"\n      />\n      <path\n        stroke=\"#7B79FF\"\n        strokeWidth={2.5}\n        d=\"m73.829 27.126 4.97-.557m54.429-6.126 10.077 73.496.905 7.373a4.33 4.33 0 0 1-3.773 4.829l-63.44 7.79a4.334 4.334 0 0 1-4.83-3.773l-9.766-79.547a2.167 2.167 0 0 1 1.886-2.414l.023-.003 5.263-.59z\"\n      />\n      <path\n        fill=\"#F0F0FF\"\n        fillRule=\"evenodd\"\n        d=\"m130.485 25.068 9.121 66.607.821 6.683c.264 2.152-1.246 4.109-3.373 4.37l-56.812 6.976c-2.128.261-4.066-1.272-4.33-3.425l-8.83-71.908a2.166 2.166 0 0 1 1.887-2.414l7.028-.863\"\n        clipRule=\"evenodd\"\n      />\n      <path\n        fill=\"#fff\"\n        fillRule=\"evenodd\"\n        stroke=\"#7B79FF\"\n        strokeWidth={2.5}\n        d=\"M135.998 6.63H86.645a2.97 2.97 0 0 0-2.107.872 2.97 2.97 0 0 0-.873 2.107v82.333c0 .823.334 1.568.873 2.107a2.97 2.97 0 0 0 2.106.872h63.917a2.97 2.97 0 0 0 2.107-.872 2.97 2.97 0 0 0 .872-2.107V24.164a2.98 2.98 0 0 0-.873-2.108L138.104 7.502a2.98 2.98 0 0 0-2.106-.872Z\"\n        clipRule=\"evenodd\"\n      />\n      <path\n        stroke=\"#7B79FF\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        strokeWidth={2.5}\n        d=\"M136.478 7.879v12.563a3.25 3.25 0 0 0 3.25 3.25h8.595M95.311 78.942h28.167m-28.167-55.25h28.167zm0 13h46.583zm0 14.084h46.583zm0 14.083h46.583z\"\n      />\n    </g>\n    <defs>\n      <clipPath id=\"EmptyDocuments_svg__a\">\n        <path fill=\"#fff\" d=\"M.667.797h216v120h-216z\" />\n      </clipPath>\n    </defs>\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgEmptyDocuments);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgEmptyPermissions = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={16} fill=\"none\" viewBox=\"0 0 193 121\" ref={ref} {...props}>\n    <g opacity={0.88}>\n      <path\n        fill=\"#DBDBFA\"\n        fillRule=\"evenodd\"\n        d=\"M160.947 53.823a4 4 0 0 0-.15-.281c-3.5-5.96-7.289-11.263-11.52-15.858h18.096c4.445 0 8.048 3.613 8.048 8.07 0 4.456-3.603 8.069-8.048 8.069zm-8.493 16.139c-11.562 11.57-31.953 19.597-55.21 19.597-31.435 0-54.384-16.705-55.701-35.736H29.409c-4.445 0-8.048 3.613-8.048 8.07 0 4.456 3.603 8.069 8.048 8.069h11.697c5.604 0 10.148 3.612 10.148 8.069q0 3.786-4.98 6.963c-.942.601-2.034.876-3.123 1.15-.33.083-.658.166-.983.257a8.07 8.07 0 0 0-5.86 7.768c0 4.457 3.602 8.07 8.047 8.07h95.425c4.445 0 8.048-3.613 8.048-8.07 0-4.456-3.603-8.069-8.048-8.069h44.839c4.444 0 8.048-3.613 8.048-8.07 0-4.456-3.604-8.068-8.048-8.068zM68.723 21.546H8.715c-4.445 0-8.048 3.613-8.048 8.07 0 4.456 3.603 8.068 8.048 8.068h37.314c4.606-6.843 12.5-12.477 22.694-16.138M.667 61.892c0-4.456 3.603-8.069 8.048-8.069 4.444 0 8.047 3.613 8.047 8.07 0 4.456-3.603 8.069-8.047 8.069S.667 66.349.667 61.892\"\n        clipRule=\"evenodd\"\n      />\n      <path\n        stroke=\"#7B79FF\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        strokeWidth={2.5}\n        d=\"m158.091 49.212 1.446 2.131\"\n      />\n      <path\n        fill=\"#fff\"\n        d=\"M154.862 59.69c-45.683-57.683-95.639-25.812-117.374.01-1.404 1.668-1.426 4.117-.15 5.882 47.31 65.454 96.71 29.044 117.704-.133 1.24-1.722 1.138-4.094-.18-5.758\"\n      />\n      <path\n        fill=\"#7B79FF\"\n        fillRule=\"evenodd\"\n        d=\"M113.536 28.091c-9.141-2.486-18.085-2.558-26.571-.94-21.301 4.058-39.458 18.707-50.43 31.742-1.794 2.132-1.798 5.217-.206 7.42C48.246 82.798 60.36 92.962 72.218 98.41q1.186.544 2.367 1.027l1.206-2.209a57 57 0 0 1-2.53-1.09c-11.366-5.222-23.168-15.052-34.905-31.291-.961-1.33-.92-3.143.092-4.345 10.764-12.787 28.453-26.985 48.985-30.897 7.905-1.506 16.271-1.495 24.869.745zm-28.277 71.966c7.097 1.397 13.98 1.155 20.536-.279 20.46-4.474 37.826-20.593 48.237-35.062.899-1.25.84-3.007-.146-4.252-10.728-13.547-21.653-22.062-32.351-26.854l1.201-2.2c11.026 4.98 22.21 13.738 33.11 27.502 1.649 2.083 1.795 5.069.215 7.265-10.584 14.708-28.416 31.382-49.732 36.044-7.115 1.556-14.607 1.77-22.323.131z\"\n        clipRule=\"evenodd\"\n      />\n      <path\n        stroke=\"#7B79FF\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        strokeWidth={2.5}\n        d=\"M127.159 22.08c13.23 6.32 21.864 14.378 27.624 21.78M34.068 48.642C53.002 23.337 89.197 8.3 117.953 18.635\"\n      />\n      <ellipse cx={94.98} cy={66.505} fill=\"#F0F0FF\" rx={18.395} ry={18.444} />\n      <path\n        fill=\"#7B79FF\"\n        fillRule=\"evenodd\"\n        d=\"M104.76 44.362a24 24 0 0 0-9.776-2.067c-13.334 0-24.144 10.838-24.144 24.208 0 8.428 4.296 15.85 10.813 20.186l1.203-2.204c-5.741-3.903-9.516-10.498-9.516-17.982 0-11.995 9.696-21.708 21.644-21.708 3.045 0 5.944.631 8.574 1.77zM91.95 87.999q1.488.211 3.034.212c11.947 0 21.643-9.713 21.643-21.708a21.66 21.66 0 0 0-5.222-14.142l1.272-2.33a24.16 24.16 0 0 1 6.45 16.472c0 13.37-10.809 24.207-24.143 24.207-1.47 0-2.908-.131-4.305-.383z\"\n        clipRule=\"evenodd\"\n      />\n      <path\n        fill=\"#fff\"\n        stroke=\"#7B79FF\"\n        strokeWidth={2.5}\n        d=\"M116.729 54.977c0 4.406-3.562 7.972-7.948 7.972s-7.948-3.566-7.948-7.972 3.562-7.972 7.948-7.972 7.948 3.566 7.948 7.972Z\"\n      />\n      <path\n        fill=\"#fff\"\n        d=\"M110.472 33.635c1.275-2.324 4.078-3.019 6.296-1.56 2.264 1.49 3.053 4.654 1.751 7.027l-31.287 57.05c-1.275 2.324-4.079 3.018-6.296 1.559-2.265-1.49-3.053-4.653-1.751-7.027z\"\n      />\n      <rect\n        width={3.668}\n        height={134.376}\n        fill=\"#7B79FF\"\n        rx={1.834}\n        transform=\"matrix(.82817 .56048 -.47918 .87772 127.181 .797)\"\n      />\n    </g>\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgEmptyPermissions);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgEmptyPictures = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={16} fill=\"none\" viewBox=\"0 0 217 121\" ref={ref} {...props}>\n    <g clipPath=\"url(#EmptyPictures_svg__a)\">\n      <g clipPath=\"url(#EmptyPictures_svg__b)\" opacity={0.88}>\n        <path\n          fill=\"#D9D8FF\"\n          fillOpacity={0.8}\n          fillRule=\"evenodd\"\n          d=\"M119.667 28.797a7 7 0 1 1 0 14h64a7 7 0 1 1 0 14h22a7 7 0 1 1 0 14h-19a7 7 0 1 0 0 14h6a7 7 0 1 1 0 14h-52a7 7 0 0 1-1.5-.161 7 7 0 0 1-1.5.16h-91a7 7 0 0 1 0-14h-39a7 7 0 1 1 0-14h40a7 7 0 0 0 0-14h-25a7 7 0 1 1 0-14h40a7 7 0 1 1 0-14zm90 56a7 7 0 1 1 0 14 7 7 0 0 1 0-14\"\n          clipRule=\"evenodd\"\n        />\n        <path\n          fill=\"#fff\"\n          fillRule=\"evenodd\"\n          d=\"m74.497 103.07-8.622 1.422a4 4 0 0 1-4.518-3.404L50.224 21.866a4 4 0 0 1 3.404-4.518l78.231-10.994a4 4 0 0 1 4.518 3.404c.474 3.377 2.408 16.468 2.571 17.63\"\n          clipRule=\"evenodd\"\n        />\n        <path\n          fill=\"#F0F0FF\"\n          fillRule=\"evenodd\"\n          d=\"m72.472 99.51-3.696.525a3.62 3.62 0 0 1-4.096-3.085l-9.996-71.925a3.646 3.646 0 0 1 3.097-4.107L128.82 10.82a3.62 3.62 0 0 1 4.096 3.085l.859 6.18 9.206 66.599c.306 2.212-1.22 4.257-3.408 4.566l-.07.01z\"\n          clipRule=\"evenodd\"\n        />\n        <path\n          stroke=\"#7B79FF\"\n          strokeLinecap=\"round\"\n          strokeWidth={2.5}\n          d=\"m69.945 103.92-4.07.572a4 4 0 0 1-4.518-3.405L50.223 21.866a4 4 0 0 1 3.405-4.518l78.231-10.994a4 4 0 0 1 4.518 3.404l.956 6.808M138.167 21.177l.5 3.12\"\n        />\n        <path\n          fill=\"#fff\"\n          fillRule=\"evenodd\"\n          stroke=\"#7B79FF\"\n          strokeWidth={2.5}\n          d=\"m165.078 31.096-78.567-8.258a2.74 2.74 0 0 0-2.018.598 2.74 2.74 0 0 0-1.005 1.85l-8.362 79.561a2.748 2.748 0 0 0 2.447 3.023l78.568 8.258a2.74 2.74 0 0 0 2.018-.598 2.74 2.74 0 0 0 1.004-1.85l8.362-79.562a2.74 2.74 0 0 0-.597-2.018 2.74 2.74 0 0 0-1.85-1.004Z\"\n          clipRule=\"evenodd\"\n        />\n        <path\n          fill=\"#fff\"\n          fillRule=\"evenodd\"\n          d=\"m93.657 31.382 62.655 6.585a3 3 0 0 1 2.67 3.297l-5.54 52.71a3 3 0 0 1-3.298 2.67L87.49 90.059a3 3 0 0 1-2.67-3.297l5.54-52.71a3 3 0 0 1 3.297-2.67\"\n          clipRule=\"evenodd\"\n        />\n        <path\n          fill=\"#F0F0FF\"\n          fillRule=\"evenodd\"\n          d=\"m93.407 74.676 9.798-6.609a4 4 0 0 1 5.167.595l7.174 7.722a1 1 0 0 0 1.362.097l15.34-12.43a4 4 0 0 1 5.877.936l9.981 15.438 1.433 2.392-.686 8.124a1 1 0 0 1-1.107.91l-56.963-6.329a1 1 0 0 1-.885-1.085l.755-8.199z\"\n          clipRule=\"evenodd\"\n        />\n        <path\n          stroke=\"#7B79FF\"\n          strokeWidth={2.5}\n          d=\"m156.181 39.21-62.655-6.585c-.48-.05-.936.099-1.284.38a1.75 1.75 0 0 0-.64 1.178l-5.54 52.71c-.05.48.1.936.381 1.284s.697.588 1.177.639l62.655 6.585c.481.05.936-.099 1.284-.38s.589-.697.639-1.177l5.54-52.71a1.74 1.74 0 0 0-.38-1.284 1.74 1.74 0 0 0-1.177-.64Z\"\n          clipRule=\"evenodd\"\n        />\n        <path\n          fill=\"#F0F0FF\"\n          stroke=\"#7B79FF\"\n          strokeWidth={2.5}\n          d=\"M105.071 56.714a6 6 0 1 0 1.254-11.936 6 6 0 0 0-1.254 11.936Z\"\n        />\n        <path\n          stroke=\"#7B79FF\"\n          strokeLinecap=\"round\"\n          strokeWidth={2.5}\n          d=\"m91.396 76.222 11.809-8.155a4 4 0 0 1 5.167.594l7.174 7.723a1 1 0 0 0 1.362.096l15.34-12.43a4 4 0 0 1 5.877.936l11.064 17.556\"\n        />\n      </g>\n    </g>\n    <defs>\n      <clipPath id=\"EmptyPictures_svg__a\">\n        <path fill=\"#fff\" d=\"M.667.797h216v120h-216z\" />\n      </clipPath>\n      <clipPath id=\"EmptyPictures_svg__b\">\n        <path fill=\"#fff\" d=\"M.667.797h216v120h-216z\" />\n      </clipPath>\n    </defs>\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgEmptyPictures);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgEnumerationField = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <rect width={31} height={23} x={0.5} y={4.5} fill=\"#F6ECFC\" stroke=\"#E0C1F4\" rx={2.5} />\n    <path\n      fill=\"#9736E8\"\n      d=\"M12.75 12a.75.75 0 0 1 .75-.75h8a.75.75 0 1 1 0 1.5h-8a.75.75 0 0 1-.75-.75m8.75 3.25h-8a.75.75 0 1 0 0 1.5h8a.75.75 0 1 0 0-1.5m0 4h-8a.75.75 0 1 0 0 1.5h8a.75.75 0 1 0 0-1.5M10.75 15a1 1 0 1 0 0 2 1 1 0 0 0 0-2m0-4a1 1 0 1 0 0 2 1 1 0 0 0 0-2m0 8a1 1 0 1 0 0 2 1 1 0 0 0 0-2\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgEnumerationField);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgFacebook = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <path\n      fill=\"#1977F3\"\n      d=\"M32 16c0-8.836-7.164-16-16-16S0 7.164 0 16c0 7.985 5.85 14.605 13.5 15.807v-11.18H9.437V16H13.5v-3.526c0-4.01 2.39-6.226 6.044-6.226 1.75 0 3.582.313 3.582.313V10.5h-2.018c-1.987 0-2.608 1.233-2.608 2.5V16h4.437l-.709 4.626H18.5v11.18C26.15 30.607 32 23.989 32 16\"\n    />\n    <path\n      fill=\"#FEFEFE\"\n      d=\"M22.228 20.626 22.937 16H18.5v-3.002c0-1.264.619-2.5 2.608-2.5h2.018V6.562s-1.832-.313-3.582-.313c-3.654 0-6.044 2.214-6.044 6.226V16H9.437v4.626H13.5v11.18Q14.724 32 16 32c.85 0 1.685-.068 2.5-.194v-11.18z\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgFacebook);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgFeatherSquare = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <path fill=\"#9736E8\" d=\"M0 4a4 4 0 0 1 4-4h24a4 4 0 0 1 4 4v24a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4z\" />\n    <path\n      fill=\"#fff\"\n      d=\"M18.037 11.774a28.6 28.6 0 0 0-2.948 2.706c-1.995 2.109-3.55 4.093-4.761 6.06-.289.469-.574.945-.855 1.418a9 9 0 0 0-.463 1.536c-.074.37.275.68.577.395.312-.299.587-.64.851-.985.467-.608.906-1.237 1.342-1.867 3.37.242 7.27-2.048 8.933-4.857a.2.2 0 0 0 .017-.167.18.18 0 0 0-.114-.118c-.809-.27-1.798-.44-2.207-.462-.017 0-.034-.014-.037-.035a.04.04 0 0 1 .024-.043c1.113-.58 1.924-.647 2.877-.505.07.01.134-.046.16-.114.095-.217.356-.87.537-1.404a.2.2 0 0 0-.087-.239c-.71-.384-1.656-.643-2.035-.682-.017 0-.03-.018-.034-.036a.04.04 0 0 1 .024-.043c1.1-.483 1.485-.497 2.364-.302.087.018.17-.05.19-.142.433-1.714.574-3.197.608-3.68a.2.2 0 0 0-.057-.157.18.18 0 0 0-.148-.05c-2.444.356-4.403.865-6.093 1.55-.057.022-.11.072-.11.136.144.551-.242 1.209-.845 1.703a.04.04 0 0 1-.044.018.05.05 0 0 1-.027-.043c.004-.046.158-.665.067-1.116-.013-.064-.033-.125-.084-.16a.17.17 0 0 0-.17-.014c-7.924 3.811-5.922 10.098-5.922 10.098q.015.004.03.007c.895-1.86 1.904-3.232 3.49-5.035 1.178-1.337 2.331-2.425 3.525-3.325.75-.565 2.448-1.738 3.51-2.144a.3.3 0 0 1 .105-.021c.097 0 .177.064.2.16a.26.26 0 0 1-.046.228z\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgFeatherSquare);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgGitHub = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <path\n      fill=\"#24292F\"\n      fillRule=\"evenodd\"\n      d=\"M15.952 0C7.132 0 0 7.184 0 16.07c0 7.105 4.57 13.118 10.908 15.247.792.16 1.083-.346 1.083-.772 0-.372-.027-1.65-.027-2.98-4.437.958-5.361-1.916-5.361-1.916-.713-1.862-1.77-2.34-1.77-2.34-1.452-.985.106-.985.106-.985 1.61.106 2.456 1.65 2.456 1.65 1.426 2.447 3.724 1.755 4.648 1.33.132-1.038.555-1.757 1.004-2.156-3.54-.372-7.263-1.756-7.263-7.929 0-1.756.634-3.193 1.637-4.31-.158-.399-.713-2.049.16-4.257 0 0 1.346-.426 4.383 1.65 1.3-.352 2.641-.531 3.988-.533 1.347 0 2.72.187 3.988.532 3.038-2.075 4.385-1.65 4.385-1.65.871 2.21.316 3.859.158 4.258 1.03 1.117 1.637 2.554 1.637 4.31 0 6.173-3.723 7.53-7.289 7.93.581.505 1.083 1.463 1.083 2.98 0 2.154-.026 3.884-.026 4.416 0 .426.29.932 1.082.772 6.34-2.13 10.908-8.142 10.908-15.246C31.904 7.184 24.748 0 15.952 0\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgGitHub);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgGlassesSquare = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <path fill=\"#AC73E6\" d=\"M0 4a4 4 0 0 1 4-4h24a4 4 0 0 1 4 4v24a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4z\" />\n    <path\n      fill=\"#fff\"\n      fillRule=\"evenodd\"\n      d=\"M15.027 13.839c-3.19-.836-6.305-1.064-10.18-.608-1.215.152-1.063 1.975.076 2.203.304.836.456 2.355.912 3.267.987 2.279 5.622 1.975 7.369.835 1.14-.683 1.443-2.279 1.9-3.494.227-.684 1.595-.684 1.822 0 .38 1.215.76 2.81 1.9 3.494 1.747 1.14 6.381 1.444 7.369-.835.456-.912.607-2.431.911-3.267 1.14-.228 1.216-2.051.076-2.203-3.874-.456-6.989-.228-10.18.608-.455.075-1.519.075-1.975 0\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgGlassesSquare);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgInformationSquare = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <path fill=\"#4945FF\" d=\"M0 4a4 4 0 0 1 4-4h24a4 4 0 0 1 4 4v24a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4z\" />\n    <path\n      fill=\"#fff\"\n      d=\"M15.733 8c.343 0 .678.108.963.31s.507.49.639.826c.13.337.165.707.098 1.064a1.9 1.9 0 0 1-.474.942 1.7 1.7 0 0 1-.887.504 1.64 1.64 0 0 1-1.002-.105 1.76 1.76 0 0 1-.778-.678A1.9 1.9 0 0 1 14 9.841a1.9 1.9 0 0 1 .508-1.302c.325-.345.766-.539 1.225-.539M20 24h-8v-2.265h2.933v-6.23H12.8v-2.266h4.267v8.496H20z\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgInformationSquare);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgJsonField = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <rect width={31} height={23} x={0.5} y={4.5} fill=\"#EAF5FF\" stroke=\"#B8E1FF\" rx={2.5} />\n    <path\n      fill=\"#0C75AF\"\n      d=\"M11.425 15.468a2.2 2.2 0 0 1-.36.532q.22.24.36.532c.325.67.325 1.457.325 2.218 0 1.621.115 2 1.25 2a.75.75 0 1 1 0 1.5c-1.196 0-2.012-.431-2.425-1.282-.325-.67-.325-1.457-.325-2.218 0-1.621-.115-2-1.25-2a.75.75 0 1 1 0-1.5c1.135 0 1.25-.379 1.25-2 0-.76 0-1.547.325-2.218.413-.85 1.229-1.282 2.425-1.282a.75.75 0 1 1 0 1.5c-1.135 0-1.25.379-1.25 2 0 .76 0 1.547-.325 2.218M23 15.25c-1.135 0-1.25-.379-1.25-2 0-.76 0-1.547-.325-2.218-.413-.85-1.229-1.282-2.425-1.282a.75.75 0 1 0 0 1.5c1.135 0 1.25.379 1.25 2 0 .76 0 1.547.325 2.218q.142.292.363.532a2.2 2.2 0 0 0-.36.532c-.328.67-.328 1.457-.328 2.218 0 1.621-.115 2-1.25 2a.75.75 0 1 0 0 1.5c1.196 0 2.012-.431 2.425-1.282.325-.67.325-1.457.325-2.218 0-1.621.115-2 1.25-2a.75.75 0 1 0 0-1.5\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgJsonField);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgMarkdownField = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <rect width={31} height={23} x={0.5} y={4.5} fill=\"#EAF5FF\" stroke=\"#B8E1FF\" rx={2.5} />\n    <path\n      fill=\"#0C75AF\"\n      d=\"M9.75 12a.75.75 0 0 1 .75-.75h11a.75.75 0 1 1 0 1.5h-11a.75.75 0 0 1-.75-.75m.75 3.25h8a.75.75 0 1 0 0-1.5h-8a.75.75 0 1 0 0 1.5m11 1h-11a.75.75 0 1 0 0 1.5h11a.75.75 0 1 0 0-1.5m-3 2.5h-8a.75.75 0 1 0 0 1.5h8a.75.75 0 1 0 0-1.5\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgMarkdownField);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgMediaField = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <rect width={31} height={23} x={0.5} y={4.5} fill=\"#F6ECFC\" stroke=\"#E0C1F4\" rx={2.5} />\n    <path\n      fill=\"#9736E8\"\n      d=\"M21.5 10.5h-11a1 1 0 0 0-1 1v9a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-9a1 1 0 0 0-1-1m-3.75 3a.75.75 0 1 1 0 1.5.75.75 0 0 1 0-1.5m-7.25 7v-1.75l3.25-3.25 5 5zm11 0h-1.336l-2.25-2.25 1.25-1.25 2.336 2.336z\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgMediaField);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgMedium = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <path\n      fill=\"#32324D\"\n      d=\"M18.05 16.007c0 5.019-4.04 9.087-9.025 9.087-4.984 0-9.025-4.07-9.025-9.087C0 10.99 4.04 6.92 9.025 6.92s9.025 4.069 9.025 9.087M27.95 16.007c0 4.724-2.02 8.555-4.512 8.555s-4.513-3.831-4.513-8.555 2.02-8.555 4.513-8.555 4.512 3.83 4.512 8.555M32 16.007c0 4.231-.71 7.664-1.587 7.664s-1.587-3.432-1.587-7.664.71-7.664 1.587-7.664c.876 0 1.587 3.432 1.587 7.664\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgMedium);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgNumberField = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <rect width={31} height={23} x={0.5} y={4.5} fill=\"#FCECEA\" stroke=\"#F5C0B8\" rx={2.5} />\n    <path\n      fill=\"#D02B20\"\n      d=\"M8.68 20v-6.22h-.096l-1.902 1.322V13.64l2.004-1.392h1.616V20zm3.733 0v-1.09l2.498-2.466c1.09-1.058 1.385-1.45 1.385-1.992v-.017c0-.66-.45-1.122-1.192-1.122-.757 0-1.278.505-1.278 1.24v.028h-1.499l-.005-.022c0-1.488 1.16-2.508 2.857-2.508 1.595 0 2.713.913 2.713 2.25v.016c0 .881-.457 1.612-1.87 2.917l-1.434 1.337v.124h3.416V20zm9.974.172c-1.75 0-2.906-.94-3.013-2.326l-.005-.07h1.552l.005.06c.07.601.623 1.03 1.461 1.03.827 0 1.37-.461 1.37-1.116v-.011c0-.741-.553-1.15-1.493-1.15h-.887v-1.154h.865c.817 0 1.343-.43 1.343-1.059v-.01c0-.645-.446-1.042-1.209-1.042-.762 0-1.273.413-1.337 1.058l-.005.048H19.54l.005-.064c.113-1.386 1.203-2.288 2.83-2.288 1.665 0 2.74.838 2.74 2.073v.01c0 .967-.71 1.596-1.617 1.784v.032c1.155.107 1.907.773 1.907 1.826v.011c0 1.407-1.209 2.358-3.019 2.358\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgNumberField);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgPasswordField = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <rect width={31} height={23} x={0.5} y={4.5} fill=\"#FDF4DC\" stroke=\"#FAE7B9\" rx={2.5} />\n    <path\n      fill=\"#D9822F\"\n      d=\"M21 13h-2v-1.5a3 3 0 0 0-6 0V13h-2a1 1 0 0 0-1 1v7a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1v-7a1 1 0 0 0-1-1m-5 5.25a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5M18 13h-4v-1.5a2 2 0 0 1 4 0z\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgPasswordField);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgPlaySquare = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <path fill=\"#66B7F1\" d=\"M0 4a4 4 0 0 1 4-4h24a4 4 0 0 1 4 4v24a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4z\" />\n    <path\n      fill=\"#fff\"\n      fillRule=\"evenodd\"\n      d=\"M12 10.921a.5.5 0 0 1 .773-.419l8.582 5.579a.5.5 0 0 1 0 .838l-8.582 5.579a.5.5 0 0 1-.773-.42z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgPlaySquare);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgReddit = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <path\n      fill=\"#FF4500\"\n      d=\"M16 0C7.164 0 0 7.164 0 16a15.95 15.95 0 0 0 4.686 11.314L1.64 30.36c-.605.605-.177 1.639.678 1.639H16c8.836 0 16-7.164 16-16S24.836 0 16 0\"\n    />\n    <path\n      fill=\"#fff\"\n      d=\"M19.255 7.545a2.668 2.668 0 0 0 5.261-.614 2.666 2.666 0 0 0-5.277-.54 4.307 4.307 0 0 0-3.84 4.277v.013c-2.345.099-4.487.767-6.187 1.82a3.736 3.736 0 1 0-3.869 6.34c.124 4.338 4.85 7.826 10.664 7.826s10.547-3.492 10.664-7.833a3.737 3.737 0 0 0-1.602-7.111c-.857 0-1.645.288-2.275.773-1.715-1.061-3.88-1.729-6.25-1.817v-.01a3.16 3.16 0 0 1 2.71-3.121zM9.062 17.829c.063-1.355.963-2.395 2.01-2.395 1.045 0 1.845 1.098 1.783 2.454-.063 1.354-.844 1.847-1.891 1.847S9 19.184 9.062 17.829m11.883-2.395c1.047 0 1.947 1.04 2.009 2.395s-.855 1.906-1.902 1.906-1.828-.491-1.89-1.848c-.063-1.355.735-2.453 1.783-2.453m-1.245 5.53c.196.02.321.224.245.406a4.268 4.268 0 0 1-7.875 0 .296.296 0 0 1 .245-.406c1.15-.116 2.394-.18 3.692-.18 1.3 0 2.542.064 3.693.18\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgReddit);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgRelationField = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <rect width={31} height={23} x={0.5} y={4.5} fill=\"#F0F0FF\" stroke=\"#D9D8FF\" rx={2.5} />\n    <path\n      fill=\"#4945FF\"\n      d=\"M16.523 19.72a.75.75 0 0 1 0 1.063l-.371.371a3.751 3.751 0 1 1-5.305-5.305l1.507-1.507a3.75 3.75 0 0 1 5.146-.155.753.753 0 0 1-1 1.126 2.25 2.25 0 0 0-3.086.091l-1.506 1.505a2.25 2.25 0 0 0 3.183 3.183l.37-.371a.747.747 0 0 1 1.062 0m4.63-8.874a3.755 3.755 0 0 0-5.305 0l-.371.37a.751.751 0 1 0 1.062 1.063l.372-.37a2.25 2.25 0 1 1 3.182 3.182l-1.507 1.507a2.25 2.25 0 0 1-3.086.09.755.755 0 0 0-1.211.315.75.75 0 0 0 .211.81 3.75 3.75 0 0 0 5.144-.152l1.507-1.507a3.756 3.756 0 0 0 .002-5.307z\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgRelationField);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgSingleType = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <rect width={31} height={23} x={0.5} y={4.5} fill=\"#0C75AF\" stroke=\"#0C75AF\" rx={2.5} />\n    <path\n      fill=\"#fff\"\n      d=\"M8.523 17.586h1.711c.123.727.844 1.195 1.758 1.195.95 0 1.606-.445 1.606-1.107 0-.492-.352-.797-1.266-1.084l-.879-.276c-1.248-.386-1.963-1.218-1.963-2.308 0-1.547 1.418-2.678 3.328-2.678 1.858 0 3.164 1.078 3.217 2.62h-1.67c-.105-.71-.744-1.184-1.617-1.184-.826 0-1.459.433-1.459 1.03 0 .47.34.815 1.137 1.067l.867.27c1.436.451 2.086 1.154 2.086 2.297 0 1.675-1.418 2.789-3.516 2.789-1.922 0-3.234-.99-3.34-2.631M20.107 20h-1.78l1.487-6.943h-2.53l.31-1.512h6.843l-.31 1.512h-2.531z\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgSingleType);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgStrapi = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <path\n      fill=\"#4945FF\"\n      d=\"M0 11.093c0-5.23 0-7.844 1.625-9.468C3.249 0 5.864 0 11.093 0h9.814c5.23 0 7.844 0 9.468 1.625C32 3.249 32 5.864 32 11.093v9.814c0 5.23 0 7.844-1.625 9.468C28.751 32 26.136 32 20.907 32h-9.814c-5.23 0-7.844 0-9.468-1.625C0 28.751 0 26.136 0 20.907z\"\n    />\n    <path\n      fill=\"#fff\"\n      fillRule=\"evenodd\"\n      d=\"M22.08 9.707H11.307V15.2H16.8v5.493h5.493V9.92a.213.213 0 0 0-.213-.213\"\n      clipRule=\"evenodd\"\n    />\n    <path fill=\"#fff\" d=\"M16.8 15.2h-.213v.213h.213z\" />\n    <path\n      fill=\"#9593FF\"\n      d=\"M11.307 15.2h5.28c.117 0 .213.096.213.213v5.28h-5.28a.213.213 0 0 1-.213-.213zM16.8 20.693h5.493l-5.31 5.312a.107.107 0 0 1-.183-.076zM11.307 15.2H6.07a.107.107 0 0 1-.076-.182l5.312-5.311z\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgStrapi);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgTextField = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <rect width={31} height={23} x={0.5} y={4.5} fill=\"#EAFBE7\" stroke=\"#C6F0C2\" rx={2.5} />\n    <path\n      fill=\"#328048\"\n      d=\"M13.679 11.18a.75.75 0 0 0-1.358 0l-4 8.5a.75.75 0 0 0 1.357.64l.974-2.07h4.695l.974 2.07a.75.75 0 1 0 1.358-.64zm-2.32 5.57 1.64-3.489 1.643 3.489zm9.14-3c-.865 0-1.547.241-2.027.717a.749.749 0 1 0 1.056 1.063c.188-.187.516-.283.972-.283.584 0 1.074.323 1.21.757a3 3 0 0 0-1.21-.254c-1.516 0-2.75 1.121-2.75 2.5s1.234 2.5 2.75 2.5c.479.001.95-.114 1.375-.336A.75.75 0 0 0 23.25 20v-3.75c0-1.379-1.234-2.5-2.75-2.5m0 5.5c-.687 0-1.25-.449-1.25-1s.563-1 1.25-1 1.25.449 1.25 1-.562 1-1.25 1\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgTextField);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgUidField = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <rect width={31} height={23} x={0.5} y={4.5} fill=\"#F0F0FF\" stroke=\"#D9D8FF\" rx={2.5} />\n    <path\n      fill=\"#4945FF\"\n      d=\"M18 9a5.005 5.005 0 0 0-4.756 6.549l-3.598 3.597a.5.5 0 0 0-.146.354V22a.5.5 0 0 0 .5.5h2.5a.5.5 0 0 0 .5-.5v-1h1a.5.5 0 0 0 .5-.5v-1h1a.5.5 0 0 0 .354-.146l.597-.598A5 5 0 1 0 18 9m1.25 4.75a1 1 0 1 1 0-2 1 1 0 0 1 0 2\"\n    />\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgUidField);\nexport default ForwardRef;\n", "import type { SVGProps } from 'react';\nimport { Ref, forwardRef } from 'react';\nconst SvgX = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" width={16} height={16} ref={ref} {...props}>\n    <g fillRule=\"evenodd\" clipRule=\"evenodd\">\n      <path\n        fill=\"#FAFAFA\"\n        d=\"M6.566 6.533c.064.092 1.557 2.264 3.317 4.828l3.617 5.268c.23.334.418.614.418.622s-.086.114-.19.234-.4.462-.654.76l-3.258 3.787c-1.153 1.34-1.32 1.534-2.197 2.556-.47.546-.919 1.068-1 1.16s-.146.177-.146.189c0 .014.295.021.83.021h.83l.911-1.062 1.1-1.279a888 888 0 0 0 2.243-2.61c.043-.048.377-.437.744-.864s.676-.787.689-.8l.431-.502a9 9 0 0 1 .424-.478c.009 0 1.164 1.672 2.567 3.717l2.608 3.797.055.08h2.846c2.34.001 2.843-.004 2.834-.027-.01-.025-1.373-2.013-4.87-7.103-2.517-3.665-2.852-4.157-2.843-4.182.01-.024.353-.425 2.607-3.049l1.779-2.07c.062-.07.388-.45.724-.84l1.96-2.283c.027-.035-.02-.038-.814-.038h-.842l-.375.437a1174 1174 0 0 1-2.23 2.594c-.084.096-.506.586-.938 1.09a129 129 0 0 1-1.004 1.167c-.186.22-.374.44-1.239 1.442-.38.44-.399.459-.43.418-.02-.023-1.132-1.64-2.473-3.594L12.16 6.366H6.45zm2.228 1.165 1.186 1.7c1.196 1.71 5.895 8.436 8.917 12.763a421 421 0 0 0 1.783 2.54c.02.022.301.026 1.314.022l1.287-.005-3.37-4.823-5.963-8.534-2.593-3.712-1.3-.005-1.3-.006z\"\n      />\n      <path\n        fill=\"#040404\"\n        d=\"M0 16v16l16.005-.005 16.006-.006.005-15.994L32.022 0H0zm.01.01c0 8.8.003 12.4.006 8s.003-11.6 0-16-.005-.8-.005 8m6.556-9.477c.064.092 1.557 2.264 3.317 4.828l3.617 5.268c.23.334.418.614.418.622s-.086.114-.19.234-.399.462-.654.76l-2.014 2.34-1.244 1.447c-1.153 1.34-1.32 1.534-2.197 2.556-.469.546-.918 1.068-1 1.16-.08.092-.146.177-.146.189 0 .014.295.021.83.021h.83l.911-1.062c.502-.585.996-1.16 1.1-1.279a888 888 0 0 0 2.243-2.61c.043-.048.377-.437.744-.864l.689-.8.431-.502a9 9 0 0 1 .424-.478c.009 0 1.164 1.672 2.567 3.717l2.608 3.797.056.08h2.845c2.34.001 2.843-.004 2.834-.027-.01-.025-1.373-2.013-4.87-7.103-2.517-3.665-2.852-4.157-2.842-4.182.009-.024.352-.425 2.606-3.049l1.78-2.07.723-.84 1.96-2.283c.027-.035-.02-.038-.814-.038h-.842l-.375.437a1129 1129 0 0 1-2.23 2.594c-.084.096-.506.586-.938 1.09a129 129 0 0 1-1.004 1.167c-.186.22-.374.44-1.239 1.442-.38.44-.399.459-.43.418-.02-.023-1.132-1.64-2.473-3.594L12.16 6.366H6.45zm2.228 1.165 1.186 1.7 8.918 12.763a416 416 0 0 0 1.782 2.54c.02.022.301.026 1.314.022l1.287-.005-3.37-4.823-5.963-8.534-2.593-3.712-1.3-.005-1.3-.006z\"\n      />\n    </g>\n  </svg>\n);\nconst ForwardRef = forwardRef(SvgX);\nexport default ForwardRef;\n", "/**\n * Prefix message with 'apiError.'\n */\nexport function getPrefixedId(message: string, callback?: (prefixedMessage: string) => string) {\n  const prefixedMessage = `apiError.${message}`;\n\n  // if a prefix function has been passed in it is used to\n  // prefix the id, e.g. to allow an error message to be\n  // set only for a localization namespace\n  if (typeof callback === 'function') {\n    return callback(prefixedMessage);\n  }\n\n  return prefixedMessage;\n}\n", "import { getPrefixedId } from './getPrefixedId';\n\nimport type { ApiError } from '../hooks/useAPIErrorHandler';\nimport type { FetchError } from '../utils/getFetchClient';\nimport type { errors } from '@strapi/utils';\n\nexport interface NormalizeErrorOptions {\n  name?: string;\n  intlMessagePrefixCallback?: (id: string) => string;\n}\n\ninterface NormalizeErrorReturn {\n  id: string;\n  defaultMessage: string;\n  name?: string;\n  values: Record<'path', string> | Record<string, never>;\n}\n\ninterface YupFormattedError {\n  path: string[];\n  message: string;\n  name: string;\n  value: string;\n}\n\nfunction normalizeError(\n  error: ApiError | YupFormattedError,\n  { name, intlMessagePrefixCallback }: NormalizeErrorOptions\n): NormalizeErrorReturn {\n  const { message } = error;\n\n  const normalizedError = {\n    id: getPrefixedId(message, intlMessagePrefixCallback),\n    defaultMessage: message,\n    name: error.name ?? name,\n    values: {},\n  };\n\n  if ('path' in error) {\n    normalizedError.values = { path: error.path.join('.') };\n  }\n\n  return normalizedError;\n}\n\nconst validateErrorIsYupValidationError = (\n  err: ApiError\n): err is errors.YupValidationError & { details: { errors: YupFormattedError[] } } =>\n  typeof err.details === 'object' && err.details !== null && 'errors' in err.details;\n\n/**\n * Normalize the format of `ResponseError`\n * in places where the hook `useAPIErrorHandler` can not called\n * (e.g. outside of a React component).\n */\nexport function normalizeAPIError(\n  apiError: FetchError,\n  intlMessagePrefixCallback?: NormalizeErrorOptions['intlMessagePrefixCallback']\n):\n  | NormalizeErrorReturn\n  | { name: string; message: string | null; errors: NormalizeErrorReturn[] }\n  | null {\n  const error = apiError.response?.data?.error;\n\n  if (error) {\n    // some errors carry multiple errors (such as ValidationError)\n    if (validateErrorIsYupValidationError(error)) {\n      return {\n        name: error.name,\n        message: error?.message || null,\n        errors: error.details.errors.map((err) =>\n          normalizeError(err, { name: error.name, intlMessagePrefixCallback })\n        ),\n      };\n    }\n    return normalizeError(error, { intlMessagePrefixCallback });\n  }\n\n  return null;\n}\n", "import * as React from 'react';\n\nimport { IntlFormatters, useIntl } from 'react-intl';\n\nimport { FetchError, ApiError } from '../utils/getFetchClient';\nimport { getPrefixedId } from '../utils/getPrefixedId';\nimport { NormalizeErrorOptions, normalizeAPIError } from '../utils/normalizeAPIError';\nimport { setIn } from '../utils/objects';\n\ninterface UnknownApiError {\n  /**\n   * The name of the ApiError, is always a static value.\n   */\n  name: 'UnknownError';\n  /**\n   * The error message.\n   */\n  message: string;\n  /**\n   * The error details.\n   */\n  details?: unknown;\n  /**\n   * The HTTP status code of the error.\n   */\n  status?: number;\n}\n\n/**\n * The last item is the fallback error SerializedError which\n * typically comes from redux-toolkit itself.\n */\ninterface SerializedError {\n  /**\n   * The name of the error.\n   */\n  name?: string;\n  /**\n   * The error message that explains what went wrong.\n   */\n  message?: string;\n  /**\n   * The stack trace of the error.\n   */\n  stack?: string;\n  /**\n   * A specific error code associated with the error.\n   */\n  code?: string;\n}\n\n/**\n * These are the types or errors we return\n * from the redux-toolkit data-fetching setup.\n */\ntype BaseQueryError = ApiError | UnknownApiError | SerializedError;\n\ninterface YupFormattedError {\n  /**\n   * An array representing the path to the field where the validation error occurred.\n   */\n  path: string[];\n  /**\n   * The error message describing the validation failure.\n   */\n  message: string;\n  /**\n   * The name of the error, typically identifies the type of validation error that occurred.\n   */\n  name: string;\n\n  value: string;\n}\n\n/**\n * @public\n * @description The purpose of this hook is to offer a unified way to handle errors thrown by API endpoints, regardless of the type of error (`ValidationError`, `ApplicationErrror` ...)\nthat has been thrown.\n * @example\n * ```tsx\n * import * as React from 'react';\n * import { useFetchClient, useAPIErrorHandler, useNotification } from '@strapi/admin/admin';\n *\n * const MyComponent = () => {\n *   const { get } = useFetchClient();\n *   const { formatAPIError } = useAPIErrorHandler(getTrad);\n *   const { toggleNotification } = useNotification();\n *\n *   const handleDeleteItem = async () => {\n *     try {\n *       return await get('/admin');\n *     } catch (error) {\n *       toggleNotification({\n *         type: 'danger',\n *         message: formatAPIError(error),\n *       });\n *     }\n *   };\n *   return <button onClick={handleDeleteItem}>Delete item</button>;\n * };\n * ```\n */\nexport function useAPIErrorHandler(\n  intlMessagePrefixCallback?: FormatAPIErrorOptions['intlMessagePrefixCallback']\n) {\n  const { formatMessage } = useIntl();\n\n  /**\n   * @description This method try to normalize the passed error\n   * and then call formatAPIError to stringify the ResponseObject\n   * into a string. If it fails it will call formatFetchError and\n   * return the error message.\n   */\n  const formatError = React.useCallback(\n    (error: FetchError) => {\n      // Try to normalize the passed error first. This will fail for e.g. network\n      // errors which are thrown by fetchClient directly.\n      try {\n        const formattedErr = formatAPIError(error, { intlMessagePrefixCallback, formatMessage });\n\n        if (!formattedErr) {\n          return formatFetchError(error, { intlMessagePrefixCallback, formatMessage });\n        }\n\n        return formattedErr;\n      } catch (_) {\n        throw new Error('formatAPIError: Unknown error:', error);\n      }\n    },\n    [formatMessage, intlMessagePrefixCallback]\n  );\n\n  return {\n    /**\n     * @alpha\n     * Convert ValidationErrors from the API into an object that can be used by forms.\n     */\n    _unstableFormatValidationErrors: React.useCallback(\n      (error: Extract<BaseQueryError, { name: 'ValidationError' }>): Record<string, string> => {\n        if (typeof error.details === 'object' && error.details !== null) {\n          if ('errors' in error.details && Array.isArray(error.details.errors)) {\n            const validationErrors = error.details.errors as YupFormattedError[];\n\n            return validationErrors.reduce((acc, err) => {\n              const { path, message } = err;\n\n              return setIn(acc, path.join('.'), message);\n            }, {});\n          } else {\n            const details = error.details as Record<string, string[]>;\n\n            return Object.keys(details).reduce((acc, key) => {\n              const messages = details[key];\n\n              return {\n                ...acc,\n                [key]: messages.join(', '),\n              };\n            }, {});\n          }\n        } else {\n          return {};\n        }\n      },\n      []\n    ),\n    /**\n     * @alpha\n     * This handles the errors given from `redux-toolkit`'s axios based baseQuery function.\n     */\n    _unstableFormatAPIError: React.useCallback(\n      (error: BaseQueryError) => {\n        const err = {\n          response: {\n            data: {\n              error,\n            },\n          },\n        } as FetchError;\n\n        /**\n         * There's a chance with SerializedErrors that the message is not set.\n         * In that case we return a generic error message.\n         */\n        if (!error.message) {\n          return 'Unknown error occured.';\n        }\n\n        return formatError(err);\n      },\n      [formatError]\n    ),\n    formatAPIError: formatError,\n  };\n}\n\nfunction formatFetchError(\n  error: FetchError,\n  { intlMessagePrefixCallback, formatMessage }: FormatAPIErrorOptions\n) {\n  const { code, message } = error;\n\n  return formatMessage(\n    {\n      id: getPrefixedId(message, intlMessagePrefixCallback),\n      defaultMessage: message,\n    },\n    {\n      code,\n    }\n  );\n}\n\ntype FormatAPIErrorOptions = Partial<Pick<NormalizeErrorOptions, 'intlMessagePrefixCallback'>> &\n  Pick<IntlFormatters, 'formatMessage'>;\n\n/**\n * @description This method stringifies the `ResponseObject` into\n * a string. If multiple errors are thrown by the API, which\n * happens e.g.in the case of a `ValidationError`, all errors\n * will bo concatenated into a single string.\n */\nfunction formatAPIError(\n  error: FetchError,\n  { formatMessage, intlMessagePrefixCallback }: FormatAPIErrorOptions\n) {\n  if (!formatMessage) {\n    throw new Error('The formatMessage callback is a mandatory argument.');\n  }\n\n  const normalizedError = normalizeAPIError(error, intlMessagePrefixCallback);\n\n  if (!normalizedError) {\n    return null;\n  }\n\n  if ('message' in normalizedError && normalizedError.message !== null) {\n    return normalizedError.message;\n  }\n\n  // stringify multiple errors\n  if ('errors' in normalizedError) {\n    return normalizedError.errors\n      .map(({ id, defaultMessage, values }) => formatMessage({ id, defaultMessage }, values))\n      .join('\\n');\n  }\n\n  return formatMessage(normalizedError);\n}\n\nexport type { ApiError };\n", "import * as React from 'react';\n\nimport {\n  Box,\n  EmptyStateLayout,\n  type EmptyStateLayoutProps,\n  Flex,\n  Loader,\n  Main,\n  MainProps,\n} from '@strapi/design-system';\nimport { WarningCircle } from '@strapi/icons';\nimport { EmptyPermissions, EmptyDocuments } from '@strapi/icons/symbols';\nimport { useIntl } from 'react-intl';\n\nimport { useAuth, Permission } from '../features/Auth';\nimport { useNotification } from '../features/Notifications';\nimport { useAPIErrorHandler } from '../hooks/useAPIErrorHandler';\nimport { useCheckPermissionsQuery } from '../services/auth';\n\n/* -------------------------------------------------------------------------------------------------\n * Main\n * -----------------------------------------------------------------------------------------------*/\ninterface PageMainProps extends MainProps {\n  children: React.ReactNode;\n}\n\nconst PageMain = ({ children, ...restProps }: PageMainProps) => {\n  return <Main {...restProps}>{children}</Main>;\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Loading\n * -----------------------------------------------------------------------------------------------*/\ninterface LoadingProps {\n  /**\n   * @default 'Loading content.'\n   */\n  children?: React.ReactNode;\n}\n\n/**\n * @public\n * @description A loading component that should be rendered as the page\n * whilst you load the content for the aforementioned page.\n */\nconst Loading = ({ children = 'Loading content.' }: LoadingProps) => {\n  return (\n    <PageMain height=\"100vh\" aria-busy={true}>\n      <Flex alignItems=\"center\" height=\"100%\" justifyContent=\"center\">\n        <Loader>{children}</Loader>\n      </Flex>\n    </PageMain>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Error\n * -----------------------------------------------------------------------------------------------*/\ninterface ErrorProps extends Partial<EmptyStateLayoutProps> {}\n\n/**\n * TODO: should we start passing our errors here so they're persisted on the screen?\n * This could follow something similar to how the global app error works...?\n */\n\n/**\n * @public\n * @description An error component that should be rendered as the page\n * when an error occurs.\n */\nconst Error = (props: ErrorProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <PageMain height=\"100%\">\n      <Flex alignItems=\"center\" height=\"100%\" justifyContent=\"center\">\n        <EmptyStateLayout\n          icon={<WarningCircle width=\"16rem\" />}\n          content={formatMessage({\n            id: 'anErrorOccurred',\n            defaultMessage: 'Whoops! Something went wrong. Please, try again.',\n          })}\n          {...props}\n        />\n      </Flex>\n    </PageMain>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * NoPermissions\n * -----------------------------------------------------------------------------------------------*/\ninterface NoPermissionsProps extends Partial<EmptyStateLayoutProps> {}\n\n/**\n * @public\n * @description A component that should be rendered as the page\n * when the user does not have the permissions to access the content.\n * This component does not check any permissions, it's up to you to decide\n * when it should be rendered.\n */\nconst NoPermissions = (props: NoPermissionsProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <PageMain height=\"100%\">\n      <Flex alignItems=\"center\" height=\"100%\" justifyContent=\"center\">\n        <Box minWidth=\"50%\">\n          <EmptyStateLayout\n            icon={<EmptyPermissions width=\"16rem\" />}\n            content={formatMessage({\n              id: 'app.components.EmptyStateLayout.content-permissions',\n              defaultMessage: \"You don't have the permissions to access that content\",\n            })}\n            {...props}\n          />\n        </Box>\n      </Flex>\n    </PageMain>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * NoData\n * -----------------------------------------------------------------------------------------------*/\ninterface NoDataProps extends Partial<EmptyStateLayoutProps> {}\n\n/**\n * @public\n * @description A component that should be rendered as the page\n * when there is no data available to display.\n * This component does not check any permissions, it's up to you to decide\n * when it should be rendered.\n */\nconst NoData = (props: NoDataProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <PageMain height=\"100%\" background=\"neutral100\">\n      <Flex alignItems=\"center\" height=\"100%\" width=\"100%\" justifyContent=\"center\">\n        <Box minWidth=\"50%\">\n          <EmptyStateLayout\n            icon={<EmptyDocuments width=\"16rem\" />}\n            action={props.action}\n            content={formatMessage({\n              id: 'app.components.EmptyStateLayout.content-document',\n              defaultMessage: 'No content found',\n            })}\n            {...props}\n          />\n        </Box>\n      </Flex>\n    </PageMain>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Protect\n * -----------------------------------------------------------------------------------------------*/\nexport interface ProtectProps {\n  /**\n   * The children to render if the user has the required permissions.\n   * If providing a function, it will be called with an object containing\n   * the permissions the user has based on the array you passed to the component.\n   */\n  children: React.ReactNode | ((args: { permissions: Permission[] }) => React.ReactNode);\n  /**\n   * The permissions the user needs to have to access the content.\n   */\n  permissions?: Array<Omit<Partial<Permission>, 'action'> & Pick<Permission, 'action'>>;\n}\n\n/**\n * @public\n * @description A wrapper component that should be used to protect a page. It will check the permissions\n * you pass to it and render the children if the user has the required permissions. If a user does not have ALL\n * the required permissions, it will redirect the user to the home page. Whilst these checks happen it will render\n * the loading component and should the check fail it will render the error component with a notification.\n */\nconst Protect = ({ permissions = [], children }: ProtectProps) => {\n  const userPermissions = useAuth('Protect', (state) => state.permissions);\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n\n  const matchingPermissions = userPermissions.filter(\n    (permission) =>\n      permissions.findIndex(\n        (perm) => perm.action === permission.action && perm.subject === permission.subject\n      ) >= 0\n  );\n\n  const shouldCheckConditions = matchingPermissions.some(\n    (perm) => Array.isArray(perm.conditions) && perm.conditions.length > 0\n  );\n\n  const { isLoading, error, data } = useCheckPermissionsQuery(\n    {\n      permissions: matchingPermissions.map((perm) => ({\n        action: perm.action,\n        subject: perm.subject,\n      })),\n    },\n    {\n      skip: !shouldCheckConditions,\n    }\n  );\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    }\n  }, [error, formatAPIError, toggleNotification]);\n\n  if (isLoading) {\n    return <Loading />;\n  }\n\n  if (error) {\n    return <Error />;\n  }\n\n  const { data: permissionsData } = data || {};\n\n  const canAccess =\n    shouldCheckConditions && permissionsData\n      ? !permissionsData.includes(false)\n      : matchingPermissions.length > 0;\n\n  if (!canAccess) {\n    return <NoPermissions />;\n  }\n\n  return (\n    <>\n      {typeof children === 'function' ? children({ permissions: matchingPermissions }) : children}\n    </>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Title\n * -----------------------------------------------------------------------------------------------*/\nexport interface TitleProps {\n  children: string;\n}\n\n/**\n * @public\n * @description This component takes the children (must be a string) and sets\n * it as the title of the html.\n */\nconst Title = ({ children: title }: TitleProps) => {\n  React.useEffect(() => {\n    document.title = `${title} | Strapi`;\n  }, [title]);\n\n  return null;\n};\n\nconst Page = {\n  Error,\n  Loading,\n  NoPermissions,\n  Protect,\n  NoData,\n  Main: PageMain,\n  Title,\n};\n\nexport { Page };\nexport type { ErrorProps, LoadingProps, NoPermissionsProps, PageMainProps as MainProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,iBAAiB,CAAC,OAAgC,YACtD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA,wBAAC,QAAK,EAAA,OAAO,IAAI,QAAQ,IAAI,GAAG,KAAK,GAAG,KAAK,MAAK,WAAU,QAAO,WAAU,IAAI,IAAA,CAAK;MACtF;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA;AAEF,IAAMA,oBAAa,yBAAW,cAAc;AAC5C,IAAA,gBAAeA;ACVf,IAAM,kBAAkB,CAAC,OAAgC,YACvD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA,wBAAC,QAAK,EAAA,OAAO,IAAI,QAAQ,IAAI,GAAG,KAAK,GAAG,KAAK,MAAK,WAAU,QAAO,WAAU,IAAI,IAAA,CAAK;MACtF;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA;AAEF,IAAMA,oBAAa,yBAAW,eAAe;AAC7C,IAAA,gBAAeA;ACVf,IAAM,gBAAgB,CAAC,OAAgC,YACrD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA,wBAAC,QAAK,EAAA,MAAK,WAAU,GAAE,wEAAA,CAAwE;MAC/F;IAAC;IAAA;MACC,MAAK;MACL,UAAS;MACT,GAAE;MACF,UAAS;IAAA;EACX;AAAA,EACF,CAAA;AAEF,IAAMA,mBAAa,yBAAW,aAAa;ACX3C,IAAM,oBAAoB,CAAC,OAAgC,YACzD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA,wBAAC,QAAK,EAAA,OAAO,IAAI,QAAQ,IAAI,GAAG,KAAK,GAAG,KAAK,MAAK,WAAU,QAAO,WAAU,IAAI,IAAA,CAAK;MACtF;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA;AAEF,IAAMC,mBAAa,yBAAW,iBAAiB;AAC/C,IAAA,eAAeA;ACVf,IAAM,oBAAoB,CAAC,OAAgC,YACzD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA,wBAAC,QAAK,EAAA,OAAO,IAAI,QAAQ,IAAI,GAAG,KAAK,GAAG,KAAK,MAAK,WAAU,QAAO,WAAU,IAAI,IAAA,CAAK;MACtF;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA;AAEF,IAAMA,mBAAa,yBAAW,iBAAiB;AAC/C,IAAA,eAAeA;ACVf,IAAM,eAAe,CAAC,OAAgC,YACpD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA,wBAAC,QAAK,EAAA,OAAO,IAAI,QAAQ,IAAI,GAAG,KAAK,GAAG,KAAK,MAAK,WAAU,QAAO,WAAU,IAAI,IAAA,CAAK;MACtF;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA;AAEF,IAAMA,mBAAa,yBAAW,YAAY;AAC1C,IAAA,eAAeA;ACVf,IAAM,aAAa,CAAC,OAAgC,YAClD,wBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,cAAA;EAAC;EAAA;IACC,MAAK;IACL,GAAE;EAAA;AACJ,EACF,CAAA;AAEF,IAAMA,mBAAa,yBAAW,UAAU;ACRxC,IAAM,eAAe,CAAC,OAAgC,YACpD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;MACA;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;MACA;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;MACA;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;MACC,wBAAA,QAAA,EAAK,MAAK,WAAU,GAAE,0FAAA,CAA0F;MAChH,wBAAA,QAAA,EAAK,MAAK,WAAU,GAAE,0FAAA,CAA0F;AAAA,EACnH,CAAA;AAEF,IAAMC,mBAAa,yBAAW,YAAY;ACtB1C,IAAM,sBAAsB,CAAC,OAAgC,YAC3D,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA,wBAAC,QAAK,EAAA,OAAO,IAAI,QAAQ,IAAI,GAAG,KAAK,GAAG,KAAK,MAAK,WAAU,QAAO,WAAU,IAAI,IAAA,CAAK;MACtF;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA;AAEF,IAAMC,mBAAa,yBAAW,mBAAmB;AACjD,IAAA,eAAeA;ACVf,IAAM,gBAAgB,CAAC,OAAgC,YACrD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA,wBAAC,QAAK,EAAA,OAAO,IAAI,QAAQ,IAAI,GAAG,KAAK,GAAG,KAAK,MAAK,WAAU,QAAO,WAAU,IAAI,IAAA,CAAK;MACtF;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA;AAEF,IAAMA,mBAAa,yBAAW,aAAa;AAC3C,IAAA,eAAeA;ACVf,IAAM,eAAe,CAAC,OAAgC,YACnD,yBAAA,OAAA,EAAI,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,QAAO,SAAQ,eAAc,KAAW,GAAG,OAC7G,UAAA;MAAC,yBAAA,KAAA,EAAE,UAAS,0BACV,UAAA;QAAA;MAAC;MAAA;QACC,QAAO;QACP,iBAAgB;QAChB,eAAc;QACd,aAAa;QACb,GAAE;MAAA;IACJ;QACA;MAAC;MAAA;QACC,MAAK;QACL,aAAa;QACb,UAAS;QACT,GAAE;QACF,UAAS;MAAA;IACX;QACA;MAAC;MAAA;QACC,OAAO;QACP,QAAQ;QACR,GAAG;QACH,GAAG;QACH,MAAK;QACL,QAAO;QACP,aAAa;QACb,IAAI;MAAA;IACN;QAAA,wBACC,QAAK,EAAA,OAAO,IAAI,QAAQ,OAAO,GAAG,QAAQ,GAAG,MAAM,MAAK,QAAO,QAAO,WAAU,aAAa,OAAO,IAAI,MAAA,CAAO;QAChH;MAAC;MAAA;QACC,OAAO;QACP,QAAQ;QACR,GAAG;QACH,GAAG;QACH,MAAK;QACL,QAAO;QACP,aAAa;QACb,IAAI;MAAA;IACN;QACA;MAAC;MAAA;QACC,OAAO;QACP,QAAQ;QACR,GAAG;QACH,GAAG;QACH,MAAK;QACL,QAAO;QACP,aAAa;QACb,IAAI;MAAA;IACN;QACC,wBAAA,QAAA,EAAK,OAAO,MAAM,QAAQ,QAAQ,GAAG,QAAQ,GAAG,QAAQ,MAAK,WAAU,IAAI,MAAA,CAAO;QAClF,wBAAA,QAAA,EAAK,OAAO,MAAM,QAAQ,QAAQ,GAAG,SAAS,GAAG,QAAQ,MAAK,WAAU,IAAI,MAAA,CAAO;EAAA,EAAA,CACtF;MACC,wBAAA,QAAA,EACC,cAAC,wBAAA,YAAA,EAAS,IAAG,oBACX,cAAC,wBAAA,QAAA,EAAK,MAAK,QAAO,GAAE,0BAA0B,CAAA,EAChD,CAAA,EAAA,CACF;AAAA,EACF,CAAA;AAEF,IAAMA,mBAAa,yBAAW,YAAY;AC1D1C,IAAM,oBAAoB,CAAC,OAAgC,YACxD,yBAAA,OAAA,EAAI,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,QAAO,SAAQ,eAAc,KAAW,GAAG,OAC7G,UAAA;MAAA,yBAAC,KAAE,EAAA,UAAS,+BAA8B,SAAS,MACjD,UAAA;QAAA;MAAC;MAAA;QACC,MAAK;QACL,aAAa;QACb,UAAS;QACT,GAAE;QACF,UAAS;MAAA;IACX;QACA;MAAC;MAAA;QACC,MAAK;QACL,UAAS;QACT,GAAE;QACF,UAAS;MAAA;IACX;QACA;MAAC;MAAA;QACC,QAAO;QACP,aAAa;QACb,GAAE;MAAA;IACJ;QACA;MAAC;MAAA;QACC,MAAK;QACL,UAAS;QACT,GAAE;QACF,UAAS;MAAA;IACX;QACA;MAAC;MAAA;QACC,MAAK;QACL,UAAS;QACT,QAAO;QACP,aAAa;QACb,GAAE;QACF,UAAS;MAAA;IACX;QACA;MAAC;MAAA;QACC,QAAO;QACP,eAAc;QACd,gBAAe;QACf,aAAa;QACb,GAAE;MAAA;IACJ;EAAA,EAAA,CACF;MACC,wBAAA,QAAA,EACC,cAAC,wBAAA,YAAA,EAAS,IAAG,yBACX,cAAC,wBAAA,QAAA,EAAK,MAAK,QAAO,GAAE,0BAA0B,CAAA,EAChD,CAAA,EAAA,CACF;AAAA,EACF,CAAA;AAEF,IAAMC,mBAAa,yBAAW,iBAAiB;AAC/C,IAAA,eAAeA;ACnDf,IAAM,sBAAsB,CAAC,OAAgC,YAAA,wBAC1D,OAAI,EAAA,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,QAAO,SAAQ,eAAc,KAAW,GAAG,OAC7G,cAAA,yBAAC,KAAE,EAAA,SAAS,MACV,UAAA;MAAA;IAAC;IAAA;MACC,MAAK;MACL,UAAS;MACT,GAAE;MACF,UAAS;IAAA;EACX;MACA;IAAC;IAAA;MACC,QAAO;MACP,eAAc;MACd,gBAAe;MACf,aAAa;MACb,GAAE;IAAA;EACJ;MACA;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;MACA;IAAC;IAAA;MACC,MAAK;MACL,UAAS;MACT,GAAE;MACF,UAAS;IAAA;EACX;MACA;IAAC;IAAA;MACC,QAAO;MACP,eAAc;MACd,gBAAe;MACf,aAAa;MACb,GAAE;IAAA;EACJ;MACA,wBAAC,WAAQ,EAAA,IAAI,OAAO,IAAI,QAAQ,MAAK,WAAU,IAAI,QAAQ,IAAI,OAAQ,CAAA;MACvE;IAAC;IAAA;MACC,MAAK;MACL,UAAS;MACT,GAAE;MACF,UAAS;IAAA;EACX;MACA;IAAC;IAAA;MACC,MAAK;MACL,QAAO;MACP,aAAa;MACb,GAAE;IAAA;EACJ;MACA;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;MACA;IAAC;IAAA;MACC,OAAO;MACP,QAAQ;MACR,MAAK;MACL,IAAI;MACJ,WAAU;IAAA;EACZ;AAAA,EACF,CAAA,EACF,CAAA;AAEF,IAAMA,mBAAa,yBAAW,mBAAmB;AACjD,IAAA,eAAeA;AC7Df,IAAM,mBAAmB,CAAC,OAAgC,YACvD,yBAAA,OAAA,EAAI,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,QAAO,SAAQ,eAAc,KAAW,GAAG,OAC7G,UAAA;MAAC,wBAAA,KAAA,EAAE,UAAS,8BACV,cAAA,yBAAC,KAAA,EAAE,UAAS,8BAA6B,SAAS,MAChD,UAAA;QAAA;MAAC;MAAA;QACC,MAAK;QACL,aAAa;QACb,UAAS;QACT,GAAE;QACF,UAAS;MAAA;IACX;QACA;MAAC;MAAA;QACC,MAAK;QACL,UAAS;QACT,GAAE;QACF,UAAS;MAAA;IACX;QACA;MAAC;MAAA;QACC,MAAK;QACL,UAAS;QACT,GAAE;QACF,UAAS;MAAA;IACX;QACA;MAAC;MAAA;QACC,QAAO;QACP,eAAc;QACd,aAAa;QACb,GAAE;MAAA;IACJ;QACA;MAAC;MAAA;QACC,MAAK;QACL,UAAS;QACT,QAAO;QACP,aAAa;QACb,GAAE;QACF,UAAS;MAAA;IACX;QACA;MAAC;MAAA;QACC,MAAK;QACL,UAAS;QACT,GAAE;QACF,UAAS;MAAA;IACX;QACA;MAAC;MAAA;QACC,MAAK;QACL,UAAS;QACT,GAAE;QACF,UAAS;MAAA;IACX;QACA;MAAC;MAAA;QACC,QAAO;QACP,aAAa;QACb,GAAE;QACF,UAAS;MAAA;IACX;QACA;MAAC;MAAA;QACC,MAAK;QACL,QAAO;QACP,aAAa;QACb,GAAE;MAAA;IACJ;QACA;MAAC;MAAA;QACC,QAAO;QACP,eAAc;QACd,aAAa;QACb,GAAE;MAAA;IACJ;EAAA,EAAA,CACF,EACF,CAAA;MAAA,yBACC,QACC,EAAA,UAAA;QAAC,wBAAA,YAAA,EAAS,IAAG,wBACX,cAAA,wBAAC,QAAA,EAAK,MAAK,QAAO,GAAE,0BAAA,CAA0B,EAChD,CAAA;QACA,wBAAC,YAAS,EAAA,IAAG,wBACX,cAAA,wBAAC,QAAA,EAAK,MAAK,QAAO,GAAE,0BAAA,CAA0B,EAChD,CAAA;EAAA,EAAA,CACF;AAAA,EACF,CAAA;AAEF,IAAMA,mBAAa,yBAAW,gBAAgB;AAC9C,IAAA,eAAeA;AChFf,IAAM,sBAAsB,CAAC,OAAgC,YAC3D,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA,wBAAC,QAAK,EAAA,OAAO,IAAI,QAAQ,IAAI,GAAG,KAAK,GAAG,KAAK,MAAK,WAAU,QAAO,WAAU,IAAI,IAAA,CAAK;MACtF;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA;AAEF,IAAMA,mBAAa,yBAAW,mBAAmB;AACjD,IAAA,eAAeA;ACVf,IAAM,cAAc,CAAC,OAAgC,YACnD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;MACA;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA;AAEF,IAAMA,mBAAa,yBAAW,WAAW;ACZzC,IAAM,mBAAmB,CAAC,OAAgC,YACxD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA,wBAAC,QAAK,EAAA,MAAK,WAAU,GAAE,wEAAA,CAAwE;MAC/F;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA;AAEF,IAAMC,mBAAa,yBAAW,gBAAgB;ACT9C,IAAM,YAAY,CAAC,OAAgC,YACjD,wBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,cAAA;EAAC;EAAA;IACC,MAAK;IACL,UAAS;IACT,GAAE;IACF,UAAS;EAAA;AACX,EACF,CAAA;AAEF,IAAMC,mBAAa,yBAAW,SAAS;AACvC,IAAA,eAAeA;ACXf,IAAM,mBAAmB,CAAC,OAAgC,YACxD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA,wBAAC,QAAK,EAAA,MAAK,WAAU,GAAE,wEAAA,CAAwE;MAC/F;IAAC;IAAA;MACC,MAAK;MACL,UAAS;MACT,GAAE;MACF,UAAS;IAAA;EACX;AAAA,EACF,CAAA;AAEF,IAAMA,mBAAa,yBAAW,gBAAgB;AAC9C,IAAA,eAAeA;ACZf,IAAM,uBAAuB,CAAC,OAAgC,YAC5D,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA,wBAAC,QAAK,EAAA,MAAK,WAAU,GAAE,wEAAA,CAAwE;MAC/F;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA;AAEF,IAAMA,mBAAa,yBAAW,oBAAoB;ACTlD,IAAM,eAAe,CAAC,OAAgC,YACpD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA,wBAAC,QAAK,EAAA,OAAO,IAAI,QAAQ,IAAI,GAAG,KAAK,GAAG,KAAK,MAAK,WAAU,QAAO,WAAU,IAAI,IAAA,CAAK;MACtF;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA;AAEF,IAAMC,mBAAa,yBAAW,YAAY;AAC1C,IAAA,eAAeA;ACVf,IAAM,mBAAmB,CAAC,OAAgC,YACxD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA,wBAAC,QAAK,EAAA,OAAO,IAAI,QAAQ,IAAI,GAAG,KAAK,GAAG,KAAK,MAAK,WAAU,QAAO,WAAU,IAAI,IAAA,CAAK;MACtF;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA;AAEF,IAAMA,mBAAa,yBAAW,gBAAgB;AAC9C,IAAA,eAAeA;ACVf,IAAM,gBAAgB,CAAC,OAAgC,YACrD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA,wBAAC,QAAK,EAAA,OAAO,IAAI,QAAQ,IAAI,GAAG,KAAK,GAAG,KAAK,MAAK,WAAU,QAAO,WAAU,IAAI,IAAA,CAAK;MACtF;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA;AAEF,IAAMA,mBAAa,yBAAW,aAAa;AAC3C,IAAA,eAAeA;ACVf,IAAM,YAAY,CAAC,OAAgC,YACjD,wBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,cAAA;EAAC;EAAA;IACC,MAAK;IACL,GAAE;EAAA;AACJ,EACF,CAAA;AAEF,IAAMA,mBAAa,yBAAW,SAAS;AACvC,IAAA,eAAeA;ACTf,IAAM,iBAAiB,CAAC,OAAgC,YACtD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA,wBAAC,QAAK,EAAA,OAAO,IAAI,QAAQ,IAAI,GAAG,KAAK,GAAG,KAAK,MAAK,WAAU,QAAO,WAAU,IAAI,IAAA,CAAK;MACtF;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA;AAEF,IAAMA,mBAAa,yBAAW,cAAc;AAC5C,IAAA,eAAeA;ACVf,IAAM,mBAAmB,CAAC,OAAgC,YACxD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA,wBAAC,QAAK,EAAA,OAAO,IAAI,QAAQ,IAAI,GAAG,KAAK,GAAG,KAAK,MAAK,WAAU,QAAO,WAAU,IAAI,IAAA,CAAK;MACtF;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA;AAEF,IAAMA,mBAAa,yBAAW,gBAAgB;AAC9C,IAAA,eAAeA;ACVf,IAAM,gBAAgB,CAAC,OAAgC,YACrD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA,wBAAC,QAAK,EAAA,MAAK,WAAU,GAAE,wEAAA,CAAwE;MAC/F;IAAC;IAAA;MACC,MAAK;MACL,UAAS;MACT,GAAE;MACF,UAAS;IAAA;EACX;AAAA,EACF,CAAA;AAEF,IAAMA,mBAAa,yBAAW,aAAa;ACX3C,IAAM,YAAY,CAAC,OAAgC,YACjD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;MACA;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA;AAEF,IAAMC,mBAAa,yBAAW,SAAS;ACZvC,IAAM,mBAAmB,CAAC,OAAgC,YACxD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA,wBAAC,QAAK,EAAA,OAAO,IAAI,QAAQ,IAAI,GAAG,KAAK,GAAG,KAAK,MAAK,WAAU,QAAO,WAAU,IAAI,IAAA,CAAK;MACtF;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA;AAEF,IAAMC,mBAAa,yBAAW,gBAAgB;AAC9C,IAAA,eAAeA;ACVf,IAAM,gBAAgB,CAAC,OAAgC,YACrD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA,wBAAC,QAAK,EAAA,OAAO,IAAI,QAAQ,IAAI,GAAG,KAAK,GAAG,KAAK,MAAK,WAAU,QAAO,WAAU,IAAI,IAAA,CAAK;MACtF;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA;AAEF,IAAMA,mBAAa,yBAAW,aAAa;AAC3C,IAAA,eAAeA;ACVf,IAAM,YAAY,CAAC,OAAgC,YACjD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;MACA;IAAC;IAAA;MACC,MAAK;MACL,UAAS;MACT,GAAE;MACF,UAAS;IAAA;EACX;MACC,wBAAA,QAAA,EAAK,MAAK,QAAO,GAAE,8BAAA,CAA8B;MAClD;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA;AAEF,IAAMA,mBAAa,yBAAW,SAAS;ACnBvC,IAAM,eAAe,CAAC,OAAgC,YACpD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA,wBAAC,QAAK,EAAA,OAAO,IAAI,QAAQ,IAAI,GAAG,KAAK,GAAG,KAAK,MAAK,WAAU,QAAO,WAAU,IAAI,IAAA,CAAK;MACtF;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA;AAEF,IAAMC,mBAAa,yBAAW,YAAY;AAC1C,IAAA,eAAeA;ACVf,IAAM,cAAc,CAAC,OAAgC,YACnD,yBAAC,OAAA,EAAI,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,UAAA;MAAA,wBAAC,QAAK,EAAA,OAAO,IAAI,QAAQ,IAAI,GAAG,KAAK,GAAG,KAAK,MAAK,WAAU,QAAO,WAAU,IAAI,IAAA,CAAK;MACtF;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA;AAEF,IAAMA,mBAAa,yBAAW,WAAW;AACzC,IAAAC,gBAAeD;ACVf,IAAM,OAAO,CAAC,OAAgC,YAAA,wBAC3C,OAAI,EAAA,OAAM,8BAA6B,SAAQ,aAAY,OAAO,IAAI,QAAQ,IAAI,KAAW,GAAG,OAC/F,cAAA,yBAAC,KAAE,EAAA,UAAS,WAAU,UAAS,WAC7B,UAAA;MAAA;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;MACA;IAAC;IAAA;MACC,MAAK;MACL,GAAE;IAAA;EACJ;AAAA,EACF,CAAA,EACF,CAAA;AAEF,IAAM,iBAAa,yBAAW,IAAI;AAClC,IAAA,eAAe;;;;;;ACdR,SAASE,cAAcC,SAAiBC,UAA8C;AAC3F,QAAMC,kBAAkB,YAAYF,OAAAA;AAKpC,MAAI,OAAOC,aAAa,YAAY;AAClC,WAAOA,SAASC,eAAAA;EAClB;AAEA,SAAOA;AACT;;;ACWA,SAASC,eACPC,OACA,EAAEC,MAAMC,0BAAyB,GAAyB;AAE1D,QAAM,EAAEC,QAAO,IAAKH;AAEpB,QAAMI,kBAAkB;IACtBC,IAAIC,cAAcH,SAASD,yBAAAA;IAC3BK,gBAAgBJ;IAChBF,MAAMD,MAAMC,QAAQA;IACpBO,QAAQ,CAAA;EACV;AAEA,MAAI,UAAUR,OAAO;AACnBI,oBAAgBI,SAAS;MAAEC,MAAMT,MAAMS,KAAKC,KAAK,GAAA;IAAK;EACxD;AAEA,SAAON;AACT;AAEA,IAAMO,oCAAoC,CACxCC,QAEA,OAAOA,IAAIC,YAAY,YAAYD,IAAIC,YAAY,QAAQ,YAAYD,IAAIC;AAOtE,SAASC,kBACdC,UACAb,2BAA8E;;AAK9E,QAAMF,SAAQe,oBAASC,aAATD,mBAAmBE,SAAnBF,mBAAyBf;AAEvC,MAAIA,OAAO;AAET,QAAIW,kCAAkCX,KAAQ,GAAA;AAC5C,aAAO;QACLC,MAAMD,MAAMC;QACZE,UAASH,+BAAOG,YAAW;QAC3Be,QAAQlB,MAAMa,QAAQK,OAAOC,IAAI,CAACP,QAChCb,eAAea,KAAK;UAAEX,MAAMD,MAAMC;UAAMC;QAA0B,CAAA,CAAA;MAEtE;IACF;AACA,WAAOH,eAAeC,OAAO;MAAEE;IAA0B,CAAA;EAC3D;AAEA,SAAO;AACT;;;ACuBO,SAASkB,mBACdC,2BAA8E;AAE9E,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAQ1B,QAAMC,cAAoBC,kBACxB,CAACC,UAAAA;AAGC,QAAI;AACF,YAAMC,eAAeC,eAAeF,OAAO;QAAEL;QAA2BC;MAAc,CAAA;AAEtF,UAAI,CAACK,cAAc;AACjB,eAAOE,iBAAiBH,OAAO;UAAEL;UAA2BC;QAAc,CAAA;MAC5E;AAEA,aAAOK;IACT,SAASG,GAAG;AACV,YAAM,IAAIC,MAAM,kCAAkCL,KAAAA;IACpD;KAEF;IAACJ;IAAeD;EAA0B,CAAA;AAG5C,SAAO;;;;;IAKLW,iCAAuCP,kBACrC,CAACC,UAAAA;AACC,UAAI,OAAOA,MAAMO,YAAY,YAAYP,MAAMO,YAAY,MAAM;AAC/D,YAAI,YAAYP,MAAMO,WAAWC,MAAMC,QAAQT,MAAMO,QAAQG,MAAM,GAAG;AACpE,gBAAMC,mBAAmBX,MAAMO,QAAQG;AAEvC,iBAAOC,iBAAiBC,OAAO,CAACC,KAAKC,QAAAA;AACnC,kBAAM,EAAEC,MAAMC,QAAO,IAAKF;AAE1B,mBAAOG,MAAMJ,KAAKE,KAAKG,KAAK,GAAMF,GAAAA,OAAAA;UACpC,GAAG,CAAA,CAAC;eACC;AACL,gBAAMT,UAAUP,MAAMO;AAEtB,iBAAOY,OAAOC,KAAKb,OAAAA,EAASK,OAAO,CAACC,KAAKQ,QAAAA;AACvC,kBAAMC,WAAWf,QAAQc,GAAI;AAE7B,mBAAO;cACL,GAAGR;cACH,CAACQ,GAAI,GAAEC,SAASJ,KAAK,IAAA;YACvB;UACF,GAAG,CAAA,CAAC;QACN;aACK;AACL,eAAO,CAAA;MACT;IACF,GACA,CAAA,CAAE;;;;;IAMJK,yBAA+BxB,kBAC7B,CAACC,UAAAA;AACC,YAAMc,MAAM;QACVU,UAAU;UACRC,MAAM;YACJzB;UACF;QACF;MACF;AAMA,UAAI,CAACA,MAAMgB,SAAS;AAClB,eAAO;MACT;AAEA,aAAOlB,YAAYgB,GAAAA;OAErB;MAAChB;IAAY,CAAA;IAEfI,gBAAgBJ;EAClB;AACF;AAEA,SAASK,iBACPH,OACA,EAAEL,2BAA2BC,cAAa,GAAyB;AAEnE,QAAM,EAAE8B,MAAMV,QAAO,IAAKhB;AAE1B,SAAOJ,cACL;IACE+B,IAAIC,cAAcZ,SAASrB,yBAAAA;IAC3BkC,gBAAgBb;KAElB;IACEU;EACF,CAAA;AAEJ;AAWA,SAASxB,eACPF,OACA,EAAEJ,eAAeD,0BAAyB,GAAyB;AAEnE,MAAI,CAACC,eAAe;AAClB,UAAM,IAAIS,MAAM,qDAAA;EAClB;AAEA,QAAMyB,kBAAkBC,kBAAkB/B,OAAOL,yBAAAA;AAEjD,MAAI,CAACmC,iBAAiB;AACpB,WAAO;EACT;AAEA,MAAI,aAAaA,mBAAmBA,gBAAgBd,YAAY,MAAM;AACpE,WAAOc,gBAAgBd;EACzB;AAGA,MAAI,YAAYc,iBAAiB;AAC/B,WAAOA,gBAAgBpB,OACpBsB,IAAI,CAAC,EAAEL,IAAIE,gBAAgBI,OAAM,MAAOrC,cAAc;MAAE+B;MAAIE;OAAkBI,MAAAA,CAAAA,EAC9Ef,KAAK,IAAA;EACV;AAEA,SAAOtB,cAAckC,eAAAA;AACvB;;;;;AC7NA,IAAMI,WAAW,CAAC,EAAEC,UAAU,GAAGC,UAA0B,MAAA;AACzD,aAAOC,yBAACC,MAAAA;IAAM,GAAGF;IAAYD;;AAC/B;AAiBA,IAAMI,UAAU,CAAC,EAAEJ,WAAW,mBAAkB,MAAgB;AAC9D,aACEE,yBAACH,UAAAA;IAASM,QAAO;IAAQC,aAAW;IAClC,cAAAJ,yBAACK,MAAAA;MAAKC,YAAW;MAASH,QAAO;MAAOI,gBAAe;MACrD,cAAAP,yBAACQ,QAAAA;QAAQV;;;;AAIjB;AAiBA,IAAMW,SAAQ,CAACC,UAAAA;AACb,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEZ,yBAACH,UAAAA;IAASM,QAAO;IACf,cAAAH,yBAACK,MAAAA;MAAKC,YAAW;MAASH,QAAO;MAAOI,gBAAe;MACrD,cAAAP,yBAACa,kBAAAA;QACCC,UAAMd,yBAACe,cAAAA;UAAcC,OAAM;;QAC3BC,SAASN,cAAc;UACrBO,IAAI;UACJC,gBAAgB;QAClB,CAAA;QACC,GAAGT;;;;AAKd;AAcA,IAAMU,gBAAgB,CAACV,UAAAA;AACrB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEZ,yBAACH,UAAAA;IAASM,QAAO;IACf,cAAAH,yBAACK,MAAAA;MAAKC,YAAW;MAASH,QAAO;MAAOI,gBAAe;MACrD,cAAAP,yBAACqB,KAAAA;QAAIC,UAAS;QACZ,cAAAtB,yBAACa,kBAAAA;UACCC,UAAMd,yBAACuB,cAAAA;YAAiBP,OAAM;;UAC9BC,SAASN,cAAc;YACrBO,IAAI;YACJC,gBAAgB;UAClB,CAAA;UACC,GAAGT;;;;;AAMhB;AAcA,IAAMc,SAAS,CAACd,UAAAA;AACd,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEZ,yBAACH,UAAAA;IAASM,QAAO;IAAOsB,YAAW;IACjC,cAAAzB,yBAACK,MAAAA;MAAKC,YAAW;MAASH,QAAO;MAAOa,OAAM;MAAOT,gBAAe;MAClE,cAAAP,yBAACqB,KAAAA;QAAIC,UAAS;QACZ,cAAAtB,yBAACa,kBAAAA;UACCC,UAAMd,yBAAC0B,cAAAA;YAAeV,OAAM;;UAC5BW,QAAQjB,MAAMiB;UACdV,SAASN,cAAc;YACrBO,IAAI;YACJC,gBAAgB;UAClB,CAAA;UACC,GAAGT;;;;;AAMhB;AAyBA,IAAMkB,UAAU,CAAC,EAAEC,cAAc,CAAA,GAAI/B,SAAQ,MAAgB;AAC3D,QAAMgC,kBAAkBC,QAAQ,WAAW,CAACC,UAAUA,MAAMH,WAAW;AACvE,QAAM,EAAEI,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,yBAAyBC,gBAAc,IAAKC,mBAAAA;AAEpD,QAAMC,sBAAsBR,gBAAgBS,OAC1C,CAACC,eACCX,YAAYY,UACV,CAACC,SAASA,KAAKf,WAAWa,WAAWb,UAAUe,KAAKC,YAAYH,WAAWG,OAAO,KAC/E,CAAA;AAGT,QAAMC,wBAAwBN,oBAAoBO,KAChD,CAACH,SAASI,MAAMC,QAAQL,KAAKM,UAAU,KAAKN,KAAKM,WAAWC,SAAS,CAAA;AAGvE,QAAM,EAAEC,WAAWC,OAAOC,KAAI,IAAKC,yBACjC;IACExB,aAAaS,oBAAoBgB,IAAI,CAACZ,UAAU;MAC9Cf,QAAQe,KAAKf;MACbgB,SAASD,KAAKC;MAChB;KAEF;IACEY,MAAM,CAACX;EACT,CAAA;AAGFY,EAAMC,iBAAU,MAAA;AACd,QAAIN,OAAO;AACTlB,yBAAmB;QACjByB,MAAM;QACNC,SAASvB,gBAAee,KAAAA;MAC1B,CAAA;IACF;KACC;IAACA;IAAOf;IAAgBH;EAAmB,CAAA;AAE9C,MAAIiB,WAAW;AACb,eAAOlD,yBAACE,SAAAA,CAAAA,CAAAA;EACV;AAEA,MAAIiD,OAAO;AACT,eAAOnD,yBAACS,QAAAA,CAAAA,CAAAA;EACV;AAEA,QAAM,EAAE2C,MAAMQ,gBAAe,IAAKR,QAAQ,CAAA;AAE1C,QAAMS,YACJjB,yBAAyBgB,kBACrB,CAACA,gBAAgBE,SAAS,KAAA,IAC1BxB,oBAAoBW,SAAS;AAEnC,MAAI,CAACY,WAAW;AACd,eAAO7D,yBAACoB,eAAAA,CAAAA,CAAAA;EACV;AAEA,aACEpB,yBAAA+D,8BAAA;cACG,OAAOjE,aAAa,aAAaA,SAAS;MAAE+B,aAAaS;KAAyBxC,IAAAA;;AAGzF;AAcA,IAAMkE,QAAQ,CAAC,EAAElE,UAAUmE,MAAK,MAAc;AAC5CT,EAAMC,iBAAU,MAAA;AACdS,aAASD,QAAQ,GAAGA,KAAAA;KACnB;IAACA;EAAM,CAAA;AAEV,SAAO;AACT;AAEA,IAAME,OAAO;EACX1D,OAAAA;EACAP;EACAkB;EACAQ;EACAJ;EACAvB,MAAMJ;EACNmE;AACF;", "names": ["ForwardRef", "ForwardRef", "ForwardRef", "ForwardRef", "ForwardRef", "ForwardRef", "ForwardRef", "ForwardRef", "ForwardRef", "ForwardRef", "ForwardRef", "ForwardRef$3", "getPrefixedId", "message", "callback", "prefixedMessage", "normalizeError", "error", "name", "intlMessagePrefixCallback", "message", "normalizedError", "id", "getPrefixedId", "defaultMessage", "values", "path", "join", "validateErrorIsYupValidationError", "err", "details", "normalizeAPIError", "apiError", "response", "data", "errors", "map", "useAPIErrorHandler", "intlMessagePrefixCallback", "formatMessage", "useIntl", "formatError", "useCallback", "error", "formattedErr", "formatAPIError", "formatFetchError", "_", "Error", "_unstableFormatValidationErrors", "details", "Array", "isArray", "errors", "validationErrors", "reduce", "acc", "err", "path", "message", "setIn", "join", "Object", "keys", "key", "messages", "_unstableFormatAPIError", "response", "data", "code", "id", "getPrefixedId", "defaultMessage", "normalizedError", "normalizeAPIError", "map", "values", "<PERSON><PERSON><PERSON>", "children", "restProps", "_jsx", "Main", "Loading", "height", "aria-busy", "Flex", "alignItems", "justifyContent", "Loader", "Error", "props", "formatMessage", "useIntl", "EmptyStateLayout", "icon", "WarningCircle", "width", "content", "id", "defaultMessage", "NoPermissions", "Box", "min<PERSON><PERSON><PERSON>", "EmptyPermissions", "NoData", "background", "EmptyDocuments", "action", "Protect", "permissions", "userPermissions", "useAuth", "state", "toggleNotification", "useNotification", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "matchingPermissions", "filter", "permission", "findIndex", "perm", "subject", "shouldCheckConditions", "some", "Array", "isArray", "conditions", "length", "isLoading", "error", "data", "useCheckPermissionsQuery", "map", "skip", "React", "useEffect", "type", "message", "permissionsData", "canAccess", "includes", "_Fragment", "Title", "title", "document", "Page"]}