{"version": 3, "sources": ["../../../@strapi/admin/admin/src/services/transferTokens.ts"], "sourcesContent": ["import * as TransferTokens from '../../../shared/contracts/transfer';\n\nimport { adminApi } from './api';\n\nconst transferTokenService = adminApi\n  .enhanceEndpoints({\n    addTagTypes: ['TransferToken'],\n  })\n  .injectEndpoints({\n    endpoints: (builder) => ({\n      regenerateToken: builder.mutation<TransferTokens.TokenRegenerate.Response['data'], string>({\n        query: (url) => ({\n          method: 'POST',\n          url: `${url}/regenerate`,\n        }),\n        transformResponse: (response: TransferTokens.TokenRegenerate.Response) => response.data,\n      }),\n      getTransferTokens: builder.query<TransferTokens.TokenList.Response['data'], void>({\n        query: () => ({\n          url: '/admin/transfer/tokens',\n          method: 'GET',\n        }),\n        transformResponse: (response: TransferTokens.TokenList.Response) => response.data,\n        providesTags: (res, _err) => [\n          ...(res?.map(({ id }) => ({ type: 'TransferToken' as const, id })) ?? []),\n          { type: 'TransferToken' as const, id: 'LIST' },\n        ],\n      }),\n      getTransferToken: builder.query<\n        TransferTokens.TokenGetById.Response['data'],\n        TransferTokens.TokenGetById.Params['id']\n      >({\n        query: (id) => `/admin/transfer/tokens/${id}`,\n        transformResponse: (response: TransferTokens.TokenGetById.Response) => response.data,\n        providesTags: (res, _err, id) => [{ type: 'TransferToken' as const, id }],\n      }),\n      createTransferToken: builder.mutation<\n        TransferTokens.TokenCreate.Response['data'],\n        TransferTokens.TokenCreate.Request['body']\n      >({\n        query: (body) => ({\n          url: '/admin/transfer/tokens',\n          method: 'POST',\n          data: body,\n        }),\n        transformResponse: (response: TransferTokens.TokenCreate.Response) => response.data,\n        invalidatesTags: [{ type: 'TransferToken' as const, id: 'LIST' }],\n      }),\n      deleteTransferToken: builder.mutation<\n        TransferTokens.TokenRevoke.Response['data'],\n        TransferTokens.TokenRevoke.Params['id']\n      >({\n        query: (id) => ({\n          url: `/admin/transfer/tokens/${id}`,\n          method: 'DELETE',\n        }),\n        transformResponse: (response: TransferTokens.TokenRevoke.Response) => response.data,\n        invalidatesTags: (_res, _err, id) => [{ type: 'TransferToken' as const, id }],\n      }),\n      updateTransferToken: builder.mutation<\n        TransferTokens.TokenUpdate.Response['data'],\n        TransferTokens.TokenUpdate.Params & TransferTokens.TokenUpdate.Request['body']\n      >({\n        query: ({ id, ...body }) => ({\n          url: `/admin/transfer/tokens/${id}`,\n          method: 'PUT',\n          data: body,\n        }),\n        transformResponse: (response: TransferTokens.TokenUpdate.Response) => response.data,\n        invalidatesTags: (_res, _err, { id }) => [{ type: 'TransferToken' as const, id }],\n      }),\n    }),\n    overrideExisting: false,\n  });\n\nconst {\n  useGetTransferTokensQuery,\n  useGetTransferTokenQuery,\n  useCreateTransferTokenMutation,\n  useDeleteTransferTokenMutation,\n  useUpdateTransferTokenMutation,\n  useRegenerateTokenMutation,\n} = transferTokenService;\n\nexport {\n  useGetTransferTokensQuery,\n  useGetTransferTokenQuery,\n  useCreateTransferTokenMutation,\n  useDeleteTransferTokenMutation,\n  useUpdateTransferTokenMutation,\n  useRegenerateTokenMutation,\n};\n"], "mappings": ";;;;;AAIA,IAAMA,uBAAuBC,SAC1BC,iBAAiB;EAChBC,aAAa;IAAC;EAAgB;AAChC,CAAA,EACCC,gBAAgB;EACfC,WAAW,CAACC,aAAa;IACvBC,iBAAiBD,QAAQE,SAAkE;MACzFC,OAAO,CAACC,SAAS;QACfC,QAAQ;QACRD,KAAK,GAAGA,GAAI;;MAEdE,mBAAmB,CAACC,aAAsDA,SAASC;IACrF,CAAA;IACAC,mBAAmBT,QAAQG,MAAuD;MAChFA,OAAO,OAAO;QACZC,KAAK;QACLC,QAAQ;;MAEVC,mBAAmB,CAACC,aAAgDA,SAASC;MAC7EE,cAAc,CAACC,KAAKC,SAAS;QACvBD,IAAAA,2BAAKE,IAAI,CAAC,EAAEC,GAAE,OAAQ;UAAEC,MAAM;UAA0BD;QAAG,QAAO,CAAA;QACtE;UAAEC,MAAM;UAA0BD,IAAI;QAAO;MAC9C;IACH,CAAA;IACAE,kBAAkBhB,QAAQG,MAGxB;MACAA,OAAO,CAACW,OAAO,0BAA0BA,EAAAA;MACzCR,mBAAmB,CAACC,aAAmDA,SAASC;MAChFE,cAAc,CAACC,KAAKC,MAAME,OAAO;QAAC;UAAEC,MAAM;UAA0BD;QAAG;MAAE;IAC3E,CAAA;IACAG,qBAAqBjB,QAAQE,SAG3B;MACAC,OAAO,CAACe,UAAU;QAChBd,KAAK;QACLC,QAAQ;QACRG,MAAMU;;MAERZ,mBAAmB,CAACC,aAAkDA,SAASC;MAC/EW,iBAAiB;QAAC;UAAEJ,MAAM;UAA0BD,IAAI;QAAO;MAAE;IACnE,CAAA;IACAM,qBAAqBpB,QAAQE,SAG3B;MACAC,OAAO,CAACW,QAAQ;QACdV,KAAK,0BAA0BU,EAAAA;QAC/BT,QAAQ;;MAEVC,mBAAmB,CAACC,aAAkDA,SAASC;MAC/EW,iBAAiB,CAACE,MAAMT,MAAME,OAAO;QAAC;UAAEC,MAAM;UAA0BD;QAAG;MAAE;IAC/E,CAAA;IACAQ,qBAAqBtB,QAAQE,SAG3B;MACAC,OAAO,CAAC,EAAEW,IAAI,GAAGI,KAAAA,OAAY;QAC3Bd,KAAK,0BAA0BU,EAAAA;QAC/BT,QAAQ;QACRG,MAAMU;;MAERZ,mBAAmB,CAACC,aAAkDA,SAASC;MAC/EW,iBAAiB,CAACE,MAAMT,MAAM,EAAEE,GAAE,MAAO;QAAC;UAAEC,MAAM;UAA0BD;QAAG;MAAE;IACnF,CAAA;;EAEFS,kBAAkB;AACpB,CAAA;AAEF,IAAM,EACJC,2BACAC,0BACAC,gCACAC,gCACAC,gCACAC,2BAA0B,IACxBnC;", "names": ["transferTokenService", "adminApi", "enhanceEndpoints", "addTagTypes", "injectEndpoints", "endpoints", "builder", "regenerateToken", "mutation", "query", "url", "method", "transformResponse", "response", "data", "getTransferTokens", "providesTags", "res", "_err", "map", "id", "type", "getTransferToken", "createTransferToken", "body", "invalidatesTags", "deleteTransferToken", "_res", "updateTransferToken", "overrideExisting", "useGetTransferTokensQuery", "useGetTransferTokenQuery", "useCreateTransferTokenMutation", "useDeleteTransferTokenMutation", "useUpdateTransferTokenMutation", "useRegenerateTokenMutation"]}