import {
  createSelector,
  useDispatch,
  useSelector,
  useStore
} from "./chunk-IL5G2D22.js";

// node_modules/@strapi/admin/dist/admin/admin/src/core/store/hooks.mjs
var useTypedDispatch = useDispatch;
var useTypedStore = useStore;
var useTypedSelector = useSelector;
var createTypedSelector = (selector) => createSelector((state) => state, selector);

export {
  useTypedDispatch,
  useTypedStore,
  useTypedSelector,
  createTypedSelector
};
//# sourceMappingURL=chunk-ZOP4VV6J.js.map
