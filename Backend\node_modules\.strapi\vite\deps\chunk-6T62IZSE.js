import {
  pluginId
} from "./chunk-MEKEEUV3.js";
import {
  useMutation,
  useQuery
} from "./chunk-75D2ZJP5.js";
import {
  useFetchClient
} from "./chunk-Y4UEUAII.js";
import {
  useTracking
} from "./chunk-XDCEA27D.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  useIntl
} from "./chunk-7GC3Y62Q.js";

// node_modules/@strapi/upload/dist/admin/hooks/useConfig.mjs
var endpoint = `/${pluginId}/configuration`;
var queryKey = [
  pluginId,
  "configuration"
];
var useConfig = () => {
  const { trackUsage } = useTracking();
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { get, put } = useFetchClient();
  const config = useQuery(queryKey, async () => {
    const res = await get(endpoint);
    return res.data.data;
  }, {
    onError() {
      return toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error"
        })
      });
    },
    /**
    * We're cementing that we always expect an object to be returned.
    */
    select: (data) => data || {}
  });
  const putMutation = useMutation(async (body) => {
    await put(endpoint, body);
  }, {
    onSuccess() {
      trackUsage("didEditMediaLibraryConfig");
      config.refetch();
    },
    onError() {
      return toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error"
        })
      });
    }
  });
  return {
    config,
    mutateConfig: putMutation
  };
};

export {
  useConfig
};
//# sourceMappingURL=chunk-6T62IZSE.js.map
