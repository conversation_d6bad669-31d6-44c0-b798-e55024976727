import {
  FieldTypeIcon
} from "./chunk-RTLULQCG.js";
import {
  CSS,
  DndContext,
  DragOverlay,
  SortableContext,
  arrayMove,
  useDroppable,
  useSortable
} from "./chunk-25ZR4HQ4.js";
import {
  ComponentIcon
} from "./chunk-QBVVXK5Q.js";
import {
  ATTRIBUTE_TYPES_THAT_CANNOT_BE_MAIN_FIELD,
  useGetInitialDataQuery
} from "./chunk-GZO3GFLN.js";
import {
  capitalise
} from "./chunk-A46TUCLT.js";
import {
  getTranslation
} from "./chunk-HIZVCZYI.js";
import {
  BackButton
} from "./chunk-DUGZ4WIW.js";
import {
  create3 as create,
  create4 as create2,
  create5 as create3,
  create6 as create4
} from "./chunk-376QHLWZ.js";
import {
  MemoizedInputRenderer
} from "./chunk-HWAQQGJJ.js";
import {
  Form,
  generateNKeysBetween,
  useField,
  useForm
} from "./chunk-L5JCPKMP.js";
import {
  Layouts
} from "./chunk-MTTHLNPH.js";
import {
  require_pipe
} from "./chunk-WH6VCVXU.js";
import {
  fn
} from "./chunk-QOUV5O5E.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  Box,
  Button,
  Divider,
  Flex,
  Grid,
  IconButton,
  Link,
  Main,
  Menu,
  Modal,
  Typography,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  NavLink
} from "./chunk-S65ZWNEO.js";
import {
  ForwardRef$1h,
  ForwardRef$1v,
  ForwardRef$3T,
  ForwardRef$45,
  ForwardRef$4d
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/dist/admin/components/ConfigurationForm/Fields.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);

// node_modules/@strapi/content-manager/dist/admin/components/ConfigurationForm/EditFieldForm.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var FIELD_SCHEMA = create4().shape({
  label: create2().required().nullable(),
  description: create2(),
  editable: create(),
  size: create3().required()
});
var EditFieldForm = ({ attribute, name, onClose }) => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { value, onChange } = useField(name);
  const { data: mainFieldOptions } = useGetInitialDataQuery(void 0, {
    selectFromResult: (res) => {
      if ((attribute == null ? void 0 : attribute.type) !== "relation" || !res.data) {
        return {
          data: []
        };
      }
      if ("targetModel" in attribute && typeof attribute.targetModel === "string") {
        const targetSchema = res.data.contentTypes.find((schema) => schema.uid === attribute.targetModel);
        if (targetSchema) {
          return {
            data: Object.entries(targetSchema.attributes).reduce((acc, [key, attribute2]) => {
              if (!ATTRIBUTE_TYPES_THAT_CANNOT_BE_MAIN_FIELD.includes(attribute2.type)) {
                acc.push({
                  label: key,
                  value: key
                });
              }
              return acc;
            }, [])
          };
        }
      }
      return {
        data: []
      };
    },
    skip: (attribute == null ? void 0 : attribute.type) !== "relation"
  });
  if (!value || value.name === TEMP_FIELD_NAME || !attribute) {
    console.error("You've opened a field to edit without it being part of the form, this is likely a bug with Strapi. Please open an issue.");
    toggleNotification({
      message: formatMessage({
        id: "content-manager.containers.edit-settings.modal-form.error",
        defaultMessage: "An error occurred while trying to open the form."
      }),
      type: "danger"
    });
    return null;
  }
  return (0, import_jsx_runtime.jsx)(Modal.Content, {
    children: (0, import_jsx_runtime.jsxs)(Form, {
      method: "PUT",
      initialValues: value,
      validationSchema: FIELD_SCHEMA,
      onSubmit: (data) => {
        onChange(name, data);
        onClose();
      },
      children: [
        (0, import_jsx_runtime.jsx)(Modal.Header, {
          children: (0, import_jsx_runtime.jsxs)(Flex, {
            gap: 3,
            children: [
              (0, import_jsx_runtime.jsx)(FieldTypeIcon, {
                type: attribute.type
              }),
              (0, import_jsx_runtime.jsx)(Modal.Title, {
                children: formatMessage({
                  id: "content-manager.containers.edit-settings.modal-form.label",
                  defaultMessage: "Edit {fieldName}"
                }, {
                  fieldName: capitalise(value.name)
                })
              })
            ]
          })
        }),
        (0, import_jsx_runtime.jsx)(Modal.Body, {
          children: (0, import_jsx_runtime.jsx)(Grid.Root, {
            gap: 4,
            children: [
              {
                name: "label",
                label: formatMessage({
                  id: getTranslation("containers.edit-settings.modal-form.label"),
                  defaultMessage: "Label"
                }),
                size: 6,
                type: "string"
              },
              {
                name: "description",
                label: formatMessage({
                  id: getTranslation("containers.edit-settings.modal-form.description"),
                  defaultMessage: "Description"
                }),
                size: 6,
                type: "string"
              },
              {
                name: "placeholder",
                label: formatMessage({
                  id: getTranslation("containers.edit-settings.modal-form.placeholder"),
                  defaultMessage: "Placeholder"
                }),
                size: 6,
                type: "string"
              },
              {
                name: "editable",
                label: formatMessage({
                  id: getTranslation("containers.edit-settings.modal-form.editable"),
                  defaultMessage: "Editable"
                }),
                size: 6,
                type: "boolean"
              },
              {
                name: "mainField",
                label: formatMessage({
                  id: getTranslation("containers.edit-settings.modal-form.mainField"),
                  defaultMessage: "Entry title"
                }),
                hint: formatMessage({
                  id: getTranslation("containers.SettingPage.edit-settings.modal-form.mainField.hint"),
                  defaultMessage: "Set the displayed field"
                }),
                size: 6,
                options: mainFieldOptions,
                type: "enumeration"
              },
              {
                name: "size",
                label: formatMessage({
                  id: getTranslation("containers.ListSettingsView.modal-form.size"),
                  defaultMessage: "Size"
                }),
                size: 6,
                options: [
                  {
                    value: "4",
                    label: "33%"
                  },
                  {
                    value: "6",
                    label: "50%"
                  },
                  {
                    value: "8",
                    label: "66%"
                  },
                  {
                    value: "12",
                    label: "100%"
                  }
                ],
                type: "enumeration"
              }
            ].filter(filterFieldsBasedOnAttributeType(attribute.type)).map(({ size, ...field }) => (0, import_jsx_runtime.jsx)(Grid.Item, {
              col: size,
              direction: "column",
              alignItems: "stretch",
              children: (0, import_jsx_runtime.jsx)(MemoizedInputRenderer, {
                ...field
              })
            }, field.name))
          })
        }),
        (0, import_jsx_runtime.jsxs)(Modal.Footer, {
          children: [
            (0, import_jsx_runtime.jsx)(Modal.Close, {
              children: (0, import_jsx_runtime.jsx)(Button, {
                variant: "tertiary",
                children: formatMessage({
                  id: "app.components.Button.cancel",
                  defaultMessage: "Cancel"
                })
              })
            }),
            (0, import_jsx_runtime.jsx)(Button, {
              type: "submit",
              children: formatMessage({
                id: "global.finish",
                defaultMessage: "Finish"
              })
            })
          ]
        })
      ]
    })
  });
};
var filterFieldsBasedOnAttributeType = (type) => (field) => {
  switch (type) {
    case "blocks":
    case "richtext":
      return field.name !== "size" && field.name !== "mainField";
    case "boolean":
    case "media":
      return field.name !== "placeholder" && field.name !== "mainField";
    case "component":
    case "dynamiczone":
      return field.name === "label" || field.name === "editable";
    case "json":
      return field.name !== "placeholder" && field.name !== "mainField" && field.name !== "size";
    case "relation":
      return true;
    default:
      return field.name !== "mainField";
  }
};

// node_modules/@strapi/content-manager/dist/admin/components/ConfigurationForm/Fields.mjs
var GRID_COLUMNS = 12;
var DroppableContainer = ({ id, children }) => {
  const droppable = useDroppable({
    id
  });
  return children(droppable);
};
var SortableItem = ({ id, children }) => {
  const { attributes, setNodeRef, transform, transition } = useSortable({
    id
  });
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    height: "100%"
  };
  return (0, import_jsx_runtime2.jsx)("div", {
    ref: setNodeRef,
    style,
    ...attributes,
    children
  });
};
var createDragAndDropContainersFromLayout = (layout) => {
  return layout.map((row, containerIndex) => ({
    ...row,
    // Use unique ids for drag and drop items
    dndId: `container-${containerIndex}`,
    children: row.children.map((child, childIndex) => ({
      ...child,
      dndId: `container-${containerIndex}-child-${childIndex}`,
      // The formName must be recomputed each time an item is moved
      formName: `layout.${containerIndex}.children.${childIndex}`
    }))
  }));
};
var Fields = ({ attributes, fieldSizes, components, metadatas = {} }) => {
  const { formatMessage } = useIntl();
  const layout = useForm("Fields", (state) => state.values.layout ?? []);
  const onChange = useForm("Fields", (state) => state.onChange);
  const addFieldRow = useForm("Fields", (state) => state.addFieldRow);
  const removeFieldRow = useForm("Fields", (state) => state.removeFieldRow);
  const existingFields = layout.map((row) => row.children.map((field) => field.name)).flat();
  const remainingFields = Object.entries(metadatas).reduce((acc, current) => {
    var _a;
    const [name, { visible, ...field }] = current;
    if (!existingFields.includes(name) && visible === true) {
      const type = (_a = attributes[name]) == null ? void 0 : _a.type;
      const size = type ? fieldSizes[type] : GRID_COLUMNS;
      acc.push({
        ...field,
        label: field.label ?? name,
        name,
        size
      });
    }
    return acc;
  }, []);
  const handleRemoveField = (rowIndex, fieldIndex) => () => {
    if (layout[rowIndex].children.length === 1) {
      removeFieldRow(`layout`, rowIndex);
    } else {
      onChange(`layout.${rowIndex}.children`, [
        ...layout[rowIndex].children.slice(0, fieldIndex),
        ...layout[rowIndex].children.slice(fieldIndex + 1)
      ]);
    }
  };
  const handleAddField = (field) => () => {
    addFieldRow("layout", {
      children: [
        field
      ]
    });
  };
  const [containers, setContainers] = React.useState(() => createDragAndDropContainersFromLayout(layout));
  const [activeDragItem, setActiveDragItem] = React.useState(null);
  function findContainer(id, containersAsDictionary) {
    if (id in containersAsDictionary) {
      return id;
    }
    return Object.keys(containersAsDictionary).find((key) => containersAsDictionary[key].children.find((child) => child.dndId === id));
  }
  const getItemFromContainer = (id, container) => {
    return container.children.find((item) => id === item.dndId);
  };
  const getContainersAsDictionary = () => {
    return Object.fromEntries(containers.map((container) => [
      container.dndId,
      container
    ]));
  };
  const createContainersWithSpacers = (layout2) => {
    return layout2.map((row) => ({
      ...row,
      children: row.children.filter((field) => field.name !== TEMP_FIELD_NAME)
    })).filter((row) => row.children.length > 0).map((row) => {
      var _a;
      const totalSpaceTaken = row.children.reduce((acc, curr) => acc + curr.size, 0);
      if (totalSpaceTaken < GRID_COLUMNS) {
        const [spacerKey] = generateNKeysBetween((_a = row.children.at(-1)) == null ? void 0 : _a.__temp_key__, void 0, 1);
        return {
          ...row,
          children: [
            ...row.children,
            {
              name: TEMP_FIELD_NAME,
              size: GRID_COLUMNS - totalSpaceTaken,
              __temp_key__: spacerKey
            }
          ]
        };
      }
      return row;
    });
  };
  React.useEffect(() => {
    const containers2 = createDragAndDropContainersFromLayout(layout);
    setContainers(containers2);
  }, [
    layout,
    setContainers
  ]);
  return (0, import_jsx_runtime2.jsx)(DndContext, {
    onDragStart: (event) => {
      const containersAsDictionary = getContainersAsDictionary();
      const activeContainer = findContainer(event.active.id, containersAsDictionary);
      if (!activeContainer) return;
      const activeItem = getItemFromContainer(event.active.id, containersAsDictionary[activeContainer]);
      if (activeItem) {
        setActiveDragItem(activeItem);
      }
    },
    onDragOver: ({ active, over }) => {
      const containersAsDictionary = getContainersAsDictionary();
      const activeContainer = findContainer(active.id, containersAsDictionary);
      const overContainer = findContainer((over == null ? void 0 : over.id) ?? "", containersAsDictionary);
      const activeContainerIndex = containers.findIndex((container) => container.dndId === activeContainer);
      const overContainerIndex = containers.findIndex((container) => container.dndId === overContainer);
      if (!activeContainer || !overContainer) {
        return;
      }
      const draggedItem = getItemFromContainer(active.id, containersAsDictionary[activeContainer]);
      const overItem = getItemFromContainer((over == null ? void 0 : over.id) ?? "", containersAsDictionary[overContainer]);
      const overIndex = containersAsDictionary[overContainer].children.findIndex((item) => item.dndId === (over == null ? void 0 : over.id));
      if (!draggedItem) return;
      if ((draggedItem == null ? void 0 : draggedItem.size) === GRID_COLUMNS) {
        const update2 = fn(containers, (draft) => {
          draft[activeContainerIndex].children = containers[overContainerIndex].children;
          draft[overContainerIndex].children = containers[activeContainerIndex].children;
        });
        setContainers(update2);
        return;
      }
      const update = fn(containers, (draft) => {
        draft[activeContainerIndex].children = draft[activeContainerIndex].children.filter((item) => item.dndId !== active.id);
        const spaceTaken = draft[overContainerIndex].children.reduce((acc, curr) => {
          if (curr.name === TEMP_FIELD_NAME) {
            return acc;
          }
          return acc + curr.size;
        }, 0);
        if (spaceTaken + draggedItem.size > GRID_COLUMNS) {
          draft[activeContainerIndex].children = containers[activeContainerIndex].children;
          return;
        }
        if ((overItem == null ? void 0 : overItem.name) === TEMP_FIELD_NAME) {
          draft[overContainerIndex].children.splice(overIndex, 1, draggedItem);
          return;
        }
        draft[overContainerIndex].children.splice(overIndex, 0, draggedItem);
      });
      setContainers(update);
    },
    onDragEnd: (event) => {
      const { active, over } = event;
      const { id } = active;
      const overId = over == null ? void 0 : over.id;
      const containersAsDictionary = getContainersAsDictionary();
      const activeContainer = findContainer(id, containersAsDictionary);
      const overContainer = findContainer(overId, containersAsDictionary);
      if (!activeContainer || !overContainer) {
        return;
      }
      const activeIndex = containersAsDictionary[activeContainer].children.findIndex((children) => children.dndId === id);
      const overIndex = containersAsDictionary[overContainer].children.findIndex((children) => children.dndId === overId);
      const movedContainerItems = fn(containersAsDictionary, (draft) => {
        if (activeIndex !== overIndex && activeContainer === overContainer) {
          draft[activeContainer].children = arrayMove(draft[activeContainer].children, activeIndex, overIndex);
        }
      });
      const updatedContainers = Object.values(movedContainerItems);
      const updatedContainersWithSpacers = createContainersWithSpacers(updatedContainers);
      const updatedLayout = updatedContainersWithSpacers.map(({ dndId: _dndId, children, ...container }) => ({
        ...container,
        children: children.map(({ dndId: _dndId2, formName: _formName, ...child }) => child)
      }));
      onChange("layout", updatedLayout);
      setActiveDragItem(null);
    },
    children: (0, import_jsx_runtime2.jsxs)(Flex, {
      paddingTop: 6,
      direction: "column",
      alignItems: "stretch",
      gap: 4,
      children: [
        (0, import_jsx_runtime2.jsxs)(Flex, {
          alignItems: "flex-start",
          direction: "column",
          justifyContent: "space-between",
          children: [
            (0, import_jsx_runtime2.jsx)(Typography, {
              fontWeight: "bold",
              children: formatMessage({
                id: getTranslation("containers.list.displayedFields"),
                defaultMessage: "Displayed fields"
              })
            }),
            (0, import_jsx_runtime2.jsx)(Typography, {
              variant: "pi",
              textColor: "neutral600",
              children: formatMessage({
                id: "containers.SettingPage.editSettings.description",
                defaultMessage: "Drag & drop the fields to build the layout"
              })
            })
          ]
        }),
        (0, import_jsx_runtime2.jsx)(Box, {
          padding: 4,
          hasRadius: true,
          borderStyle: "dashed",
          borderWidth: "1px",
          borderColor: "neutral300",
          children: (0, import_jsx_runtime2.jsxs)(Flex, {
            direction: "column",
            alignItems: "stretch",
            gap: 2,
            children: [
              containers.map((container, containerIndex) => (0, import_jsx_runtime2.jsx)(SortableContext, {
                id: container.dndId,
                items: container.children.map((child) => ({
                  id: child.dndId
                })),
                children: (0, import_jsx_runtime2.jsx)(DroppableContainer, {
                  id: container.dndId,
                  children: ({ setNodeRef }) => (0, import_jsx_runtime2.jsx)(Grid.Root, {
                    ref: setNodeRef,
                    gap: 2,
                    children: container.children.map((child, childIndex) => (0, import_jsx_runtime2.jsx)(Grid.Item, {
                      col: child.size,
                      direction: "column",
                      alignItems: "stretch",
                      children: (0, import_jsx_runtime2.jsx)(SortableItem, {
                        id: child.dndId,
                        children: (0, import_jsx_runtime2.jsx)(Field, {
                          attribute: attributes[child.name],
                          components,
                          name: child.formName,
                          onRemoveField: handleRemoveField(containerIndex, childIndex),
                          dndId: child.dndId
                        })
                      })
                    }, child.dndId))
                  }, container.dndId)
                })
              }, container.dndId)),
              (0, import_jsx_runtime2.jsx)(DragOverlay, {
                children: activeDragItem ? (0, import_jsx_runtime2.jsx)(Field, {
                  attribute: attributes[activeDragItem.name],
                  components,
                  name: activeDragItem.formName,
                  dndId: activeDragItem.dndId
                }) : null
              }),
              (0, import_jsx_runtime2.jsxs)(Menu.Root, {
                children: [
                  (0, import_jsx_runtime2.jsx)(Menu.Trigger, {
                    startIcon: (0, import_jsx_runtime2.jsx)(ForwardRef$1h, {}),
                    endIcon: null,
                    disabled: remainingFields.length === 0,
                    fullWidth: true,
                    variant: "secondary",
                    children: formatMessage({
                      id: getTranslation("containers.SettingPage.add.field"),
                      defaultMessage: "Insert another field"
                    })
                  }),
                  (0, import_jsx_runtime2.jsx)(Menu.Content, {
                    children: remainingFields.map((field) => (0, import_jsx_runtime2.jsx)(Menu.Item, {
                      onSelect: handleAddField(field),
                      children: field.label
                    }, field.name))
                  })
                ]
              })
            ]
          })
        })
      ]
    })
  });
};
var TEMP_FIELD_NAME = "_TEMP_";
var Field = ({ attribute, components, name, onRemoveField, dndId }) => {
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const { formatMessage } = useIntl();
  const { value } = useField(name);
  const { listeners, setActivatorNodeRef } = useSortable({
    id: dndId
  });
  const handleRemoveField = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (onRemoveField) {
      onRemoveField == null ? void 0 : onRemoveField(e);
    }
  };
  const onEditFieldMeta = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsModalOpen(true);
  };
  if (!value) {
    return null;
  }
  if (value.name === TEMP_FIELD_NAME) {
    return (0, import_jsx_runtime2.jsx)(Flex, {
      tag: "span",
      height: "100%",
      style: {
        opacity: 0
      }
    });
  }
  if (!attribute) {
    return null;
  }
  return (0, import_jsx_runtime2.jsxs)(Modal.Root, {
    open: isModalOpen,
    onOpenChange: setIsModalOpen,
    children: [
      (0, import_jsx_runtime2.jsxs)(Flex, {
        borderColor: "neutral150",
        background: "neutral100",
        hasRadius: true,
        gap: 3,
        cursor: "pointer",
        onClick: () => {
          setIsModalOpen(true);
        },
        children: [
          (0, import_jsx_runtime2.jsx)(DragButton, {
            ref: setActivatorNodeRef,
            tag: "span",
            withTooltip: false,
            label: formatMessage({
              id: getTranslation("components.DraggableCard.move.field"),
              defaultMessage: "Move {item}"
            }, {
              item: value.label
            }),
            ...listeners,
            children: (0, import_jsx_runtime2.jsx)(ForwardRef$3T, {})
          }),
          (0, import_jsx_runtime2.jsxs)(Flex, {
            direction: "column",
            alignItems: "flex-start",
            grow: 1,
            overflow: "hidden",
            children: [
              (0, import_jsx_runtime2.jsxs)(Flex, {
                gap: 3,
                justifyContent: "space-between",
                width: "100%",
                children: [
                  (0, import_jsx_runtime2.jsx)(Typography, {
                    ellipsis: true,
                    fontWeight: "bold",
                    children: value.label
                  }),
                  (0, import_jsx_runtime2.jsxs)(Flex, {
                    children: [
                      (0, import_jsx_runtime2.jsx)(IconButton, {
                        type: "button",
                        variant: "ghost",
                        background: "transparent",
                        onClick: onEditFieldMeta,
                        withTooltip: false,
                        label: formatMessage({
                          id: getTranslation("components.DraggableCard.edit.field"),
                          defaultMessage: "Edit {item}"
                        }, {
                          item: value.label
                        }),
                        children: (0, import_jsx_runtime2.jsx)(ForwardRef$1v, {})
                      }),
                      (0, import_jsx_runtime2.jsx)(IconButton, {
                        type: "button",
                        variant: "ghost",
                        onClick: handleRemoveField,
                        background: "transparent",
                        withTooltip: false,
                        label: formatMessage({
                          id: getTranslation("components.DraggableCard.delete.field"),
                          defaultMessage: "Delete {item}"
                        }, {
                          item: value.label
                        }),
                        children: (0, import_jsx_runtime2.jsx)(ForwardRef$45, {})
                      })
                    ]
                  })
                ]
              }),
              (attribute == null ? void 0 : attribute.type) === "component" ? (0, import_jsx_runtime2.jsxs)(Flex, {
                paddingTop: 3,
                paddingRight: 3,
                paddingBottom: 3,
                paddingLeft: 0,
                alignItems: "flex-start",
                direction: "column",
                gap: 2,
                width: "100%",
                children: [
                  (0, import_jsx_runtime2.jsx)(Grid.Root, {
                    gap: 4,
                    width: "100%",
                    children: components[attribute.component].layout.map((row) => row.map(({ size, ...field }) => (0, import_jsx_runtime2.jsx)(Grid.Item, {
                      col: size,
                      direction: "column",
                      alignItems: "stretch",
                      children: (0, import_jsx_runtime2.jsx)(Flex, {
                        alignItems: "center",
                        background: "neutral0",
                        paddingTop: 2,
                        paddingBottom: 2,
                        paddingLeft: 3,
                        paddingRight: 3,
                        hasRadius: true,
                        borderColor: "neutral200",
                        children: (0, import_jsx_runtime2.jsx)(Typography, {
                          textColor: "neutral800",
                          children: field.name
                        })
                      })
                    }, field.name)))
                  }),
                  (0, import_jsx_runtime2.jsx)(Link, {
                    // used to stop the edit form from appearing when we click here.
                    onClick: (e) => e.stopPropagation(),
                    startIcon: (0, import_jsx_runtime2.jsx)(ForwardRef$4d, {}),
                    tag: NavLink,
                    to: `../components/${attribute.component}/configurations/edit`,
                    children: formatMessage({
                      id: getTranslation("components.FieldItem.linkToComponentLayout"),
                      defaultMessage: "Set the component's layout"
                    })
                  })
                ]
              }) : null,
              (attribute == null ? void 0 : attribute.type) === "dynamiczone" ? (0, import_jsx_runtime2.jsx)(Flex, {
                paddingTop: 3,
                paddingRight: 3,
                paddingBottom: 3,
                paddingLeft: 0,
                alignItems: "flex-start",
                gap: 2,
                width: "100%",
                wrap: "wrap",
                children: attribute == null ? void 0 : attribute.components.map((uid) => (0, import_jsx_runtime2.jsxs)(ComponentLink, {
                  // used to stop the edit form from appearing when we click here.
                  onClick: (e) => e.stopPropagation(),
                  to: `../components/${uid}/configurations/edit`,
                  children: [
                    (0, import_jsx_runtime2.jsx)(ComponentIcon, {
                      icon: components[uid].settings.icon
                    }),
                    (0, import_jsx_runtime2.jsx)(Typography, {
                      fontSize: 1,
                      textColor: "neutral600",
                      fontWeight: "bold",
                      children: components[uid].settings.displayName
                    })
                  ]
                }, uid))
              }) : null
            ]
          })
        ]
      }),
      value.name !== TEMP_FIELD_NAME && (0, import_jsx_runtime2.jsx)(EditFieldForm, {
        attribute,
        name,
        onClose: () => setIsModalOpen(false)
      })
    ]
  });
};
var DragButton = dt(IconButton)`
  height: unset;
  align-self: stretch;
  display: flex;
  align-items: center;
  padding: 0;
  border: none;
  background-color: transparent;
  border-radius: 0px;
  border-right: 1px solid ${({ theme }) => theme.colors.neutral150};
  cursor: all-scroll;

  svg {
    width: 1.2rem;
    height: 1.2rem;
  }
`;
var ComponentLink = dt(NavLink)`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${({ theme }) => theme.spaces[1]};
  padding: ${(props) => props.theme.spaces[2]};
  border: 1px solid ${({ theme }) => theme.colors.neutral200};
  background: ${({ theme }) => theme.colors.neutral0};
  width: 14rem;
  border-radius: ${({ theme }) => theme.borderRadius};
  text-decoration: none;

  &:focus,
  &:hover {
    ${({ theme }) => `
      background-color: ${theme.colors.primary100};
      border-color: ${theme.colors.primary200};

      ${Typography} {
          color: ${theme.colors.primary600};
      }
    `}

    /* > ComponentIcon */
    > div:first-child {
      background: ${({ theme }) => theme.colors.primary200};
      color: ${({ theme }) => theme.colors.primary600};

      svg {
        path {
          fill: ${({ theme }) => theme.colors.primary600};
        }
      }
    }
  }
`;

// node_modules/@strapi/content-manager/dist/admin/components/ConfigurationForm/Form.mjs
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var React2 = __toESM(require_react(), 1);
var import_pipe = __toESM(require_pipe(), 1);
var ConfigurationForm = ({ attributes, fieldSizes, layout: editLayout, onSubmit }) => {
  const { components, settings, layout, metadatas } = editLayout;
  const { formatMessage } = useIntl();
  const initialValues = React2.useMemo(() => {
    const transformations = (0, import_pipe.default)(flattenPanels, replaceMainFieldWithNameOnly, extractMetadata, addTmpSpaceToLayout, addTmpKeysToLayout);
    return {
      layout: transformations(layout),
      settings
    };
  }, [
    layout,
    settings
  ]);
  return (0, import_jsx_runtime3.jsx)(Layouts.Root, {
    children: (0, import_jsx_runtime3.jsx)(Main, {
      children: (0, import_jsx_runtime3.jsxs)(Form, {
        initialValues,
        onSubmit,
        method: "PUT",
        children: [
          (0, import_jsx_runtime3.jsx)(Header, {
            name: settings.displayName ?? ""
          }),
          (0, import_jsx_runtime3.jsx)(Layouts.Content, {
            children: (0, import_jsx_runtime3.jsxs)(Flex, {
              alignItems: "stretch",
              background: "neutral0",
              direction: "column",
              gap: 6,
              hasRadius: true,
              shadow: "tableShadow",
              paddingTop: 6,
              paddingBottom: 6,
              paddingLeft: 7,
              paddingRight: 7,
              children: [
                (0, import_jsx_runtime3.jsx)(Typography, {
                  variant: "delta",
                  tag: "h2",
                  children: formatMessage({
                    id: getTranslation("containers.SettingPage.settings"),
                    defaultMessage: "Settings"
                  })
                }),
                (0, import_jsx_runtime3.jsxs)(Grid.Root, {
                  children: [
                    (0, import_jsx_runtime3.jsx)(Grid.Item, {
                      col: 6,
                      s: 12,
                      direction: "column",
                      alignItems: "stretch",
                      children: (0, import_jsx_runtime3.jsx)(MemoizedInputRenderer, {
                        type: "enumeration",
                        label: formatMessage({
                          id: getTranslation("containers.SettingPage.editSettings.entry.title"),
                          defaultMessage: "Entry title"
                        }),
                        hint: formatMessage({
                          id: getTranslation("containers.SettingPage.editSettings.entry.title.description"),
                          defaultMessage: "Set the display field of your entry"
                        }),
                        name: "settings.mainField",
                        options: Object.entries(attributes).reduce((acc, [key, attribute]) => {
                          if (!attribute) {
                            return acc;
                          }
                          if (!ATTRIBUTE_TYPES_THAT_CANNOT_BE_MAIN_FIELD.includes(attribute.type)) {
                            acc.push({
                              label: key,
                              value: key
                            });
                          }
                          return acc;
                        }, [])
                      })
                    }),
                    (0, import_jsx_runtime3.jsx)(Grid.Item, {
                      paddingTop: 6,
                      paddingBottom: 6,
                      col: 12,
                      s: 12,
                      direction: "column",
                      alignItems: "stretch",
                      children: (0, import_jsx_runtime3.jsx)(Divider, {})
                    }),
                    (0, import_jsx_runtime3.jsx)(Grid.Item, {
                      col: 12,
                      s: 12,
                      direction: "column",
                      alignItems: "stretch",
                      children: (0, import_jsx_runtime3.jsx)(Typography, {
                        variant: "delta",
                        tag: "h3",
                        children: formatMessage({
                          id: getTranslation("containers.SettingPage.view"),
                          defaultMessage: "View"
                        })
                      })
                    }),
                    (0, import_jsx_runtime3.jsx)(Grid.Item, {
                      col: 12,
                      s: 12,
                      direction: "column",
                      alignItems: "stretch",
                      children: (0, import_jsx_runtime3.jsx)(Fields, {
                        attributes,
                        components,
                        fieldSizes,
                        metadatas
                      })
                    })
                  ]
                })
              ]
            })
          })
        ]
      })
    })
  });
};
var flattenPanels = (layout) => layout.flat(1);
var replaceMainFieldWithNameOnly = (layout) => layout.map((row) => row.map((field) => {
  var _a;
  return {
    ...field,
    mainField: (_a = field.mainField) == null ? void 0 : _a.name
  };
}));
var extractMetadata = (layout) => {
  return layout.map((row) => row.map(({ label, disabled, hint, placeholder, size, name, mainField }) => ({
    label,
    editable: !disabled,
    description: hint,
    mainField,
    placeholder,
    size,
    name,
    __temp_key__: ""
  })));
};
var addTmpSpaceToLayout = (layout) => [
  ...layout.map((row) => {
    const totalSpaceTaken = row.reduce((acc, field) => acc + field.size, 0);
    if (totalSpaceTaken < 12) {
      return [
        ...row,
        {
          name: TEMP_FIELD_NAME,
          size: 12 - totalSpaceTaken,
          __temp_key__: ""
        }
      ];
    }
    return row;
  })
];
var addTmpKeysToLayout = (layout) => {
  const keys = generateNKeysBetween(void 0, void 0, layout.length);
  return layout.map((row, rowIndex) => {
    const fieldKeys = generateNKeysBetween(void 0, void 0, row.length);
    return {
      __temp_key__: keys[rowIndex],
      children: row.map((field, fieldIndex) => {
        return {
          ...field,
          __temp_key__: fieldKeys[fieldIndex]
        };
      })
    };
  });
};
var Header = ({ name }) => {
  const { formatMessage } = useIntl();
  const modified = useForm("Header", (state) => state.modified);
  const isSubmitting = useForm("Header", (state) => state.isSubmitting);
  return (0, import_jsx_runtime3.jsx)(Layouts.Header, {
    title: formatMessage({
      id: getTranslation("components.SettingsViewWrapper.pluginHeader.title"),
      defaultMessage: `Configure the view - {name}`
    }, {
      name: capitalise(name)
    }),
    subtitle: formatMessage({
      id: getTranslation("components.SettingsViewWrapper.pluginHeader.description.edit-settings"),
      defaultMessage: "Customize how the edit view will look like."
    }),
    navigationAction: (0, import_jsx_runtime3.jsx)(BackButton, {}),
    primaryAction: (0, import_jsx_runtime3.jsx)(Button, {
      disabled: !modified,
      loading: isSubmitting,
      type: "submit",
      children: formatMessage({
        id: "global.save",
        defaultMessage: "Save"
      })
    })
  });
};

export {
  TEMP_FIELD_NAME,
  ConfigurationForm
};
//# sourceMappingURL=chunk-X73AXI2G.js.map
