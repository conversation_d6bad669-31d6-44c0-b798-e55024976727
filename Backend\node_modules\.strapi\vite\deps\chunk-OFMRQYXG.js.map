{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/components/DragPreviews/CardDragPreview.tsx"], "sourcesContent": ["import { Flex, FlexComponent, Typography } from '@strapi/design-system';\nimport { <PERSON>, Drag, Pencil } from '@strapi/icons';\nimport { styled } from 'styled-components';\n\ninterface CardDragPreviewProps {\n  label: string;\n  isSibling?: boolean;\n}\n\nconst CardDragPreview = ({ label, isSibling = false }: CardDragPreviewProps) => {\n  return (\n    <FieldContainer\n      background={isSibling ? 'neutral100' : 'primary100'}\n      display=\"inline-flex\"\n      gap={3}\n      hasRadius\n      justifyContent=\"space-between\"\n      $isSibling={isSibling}\n      max-height={`3.2rem`}\n      maxWidth=\"min-content\"\n    >\n      <Flex gap={3}>\n        <DragButton alignItems=\"center\" cursor=\"all-scroll\" padding={3}>\n          <Drag />\n        </DragButton>\n\n        <Typography\n          textColor={isSibling ? undefined : 'primary600'}\n          fontWeight=\"bold\"\n          ellipsis\n          maxWidth=\"7.2rem\"\n        >\n          {label}\n        </Typography>\n      </Flex>\n\n      <Flex>\n        <ActionBox alignItems=\"center\">\n          <Pencil />\n        </ActionBox>\n\n        <ActionBox alignItems=\"center\">\n          <Cross />\n        </ActionBox>\n      </Flex>\n    </FieldContainer>\n  );\n};\n\nconst ActionBox = styled<FlexComponent>(Flex)`\n  height: ${({ theme }) => theme.spaces[7]};\n\n  &:last-child {\n    padding: 0 ${({ theme }) => theme.spaces[3]};\n  }\n`;\n\nconst DragButton = styled(ActionBox)`\n  border-right: 1px solid ${({ theme }) => theme.colors.primary200};\n\n  svg {\n    width: 1.2rem;\n    height: 1.2rem;\n  }\n`;\n\nconst FieldContainer = styled<FlexComponent>(Flex)<{ $isSibling: boolean }>`\n  border: 1px solid\n    ${({ theme, $isSibling }) => ($isSibling ? theme.colors.neutral150 : theme.colors.primary200)};\n\n  svg {\n    width: 1rem;\n    height: 1rem;\n\n    path {\n      fill: ${({ theme, $isSibling }) => ($isSibling ? undefined : theme.colors.primary600)};\n    }\n  }\n`;\n\nexport { CardDragPreview };\nexport type { CardDragPreviewProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AASA,IAAMA,kBAAkB,CAAC,EAAEC,OAAOC,YAAY,MAAK,MAAwB;AACzE,aACEC,yBAACC,gBAAAA;IACCC,YAAYH,YAAY,eAAe;IACvCI,SAAQ;IACRC,KAAK;IACLC,WAAS;IACTC,gBAAe;IACfC,YAAYR;IACZS,cAAY;IACZC,UAAS;;UAETT,yBAACU,MAAAA;QAAKN,KAAK;;cACTO,wBAACC,YAAAA;YAAWC,YAAW;YAASC,QAAO;YAAaC,SAAS;YAC3D,cAAAJ,wBAACK,eAAAA,CAAAA,CAAAA;;cAGHL,wBAACM,YAAAA;YACCC,WAAWnB,YAAYoB,SAAY;YACnCC,YAAW;YACXC,UAAQ;YACRZ,UAAS;YAERX,UAAAA;;;;UAILE,yBAACU,MAAAA;;cACCC,wBAACW,WAAAA;YAAUT,YAAW;YACpB,cAAAF,wBAACY,eAAAA,CAAAA,CAAAA;;cAGHZ,wBAACW,WAAAA;YAAUT,YAAW;YACpB,cAAAF,wBAACa,eAAAA,CAAAA,CAAAA;;;;;;AAKX;AAEA,IAAMF,YAAYG,GAAsBf,IAAAA;YAC5B,CAAC,EAAEgB,MAAK,MAAOA,MAAMC,OAAO,CAAA,CAAE;;;iBAGzB,CAAC,EAAED,MAAK,MAAOA,MAAMC,OAAO,CAAA,CAAE;;;AAI/C,IAAMf,aAAaa,GAAOH,SAAAA;4BACE,CAAC,EAAEI,MAAK,MAAOA,MAAME,OAAOC,UAAU;;;;;;;AAQlE,IAAM5B,iBAAiBwB,GAAsBf,IAAAA;;MAEvC,CAAC,EAAEgB,OAAOnB,WAAU,MAAQA,aAAamB,MAAME,OAAOE,aAAaJ,MAAME,OAAOC,UAAU;;;;;;;cAOlF,CAAC,EAAEH,OAAOnB,WAAU,MAAQA,aAAaY,SAAYO,MAAME,OAAOG,UAAU;;;;", "names": ["CardDragPreview", "label", "is<PERSON><PERSON>ling", "_jsxs", "FieldC<PERSON>r", "background", "display", "gap", "hasRadius", "justifyContent", "$isSibling", "max-height", "max<PERSON><PERSON><PERSON>", "Flex", "_jsx", "Drag<PERSON><PERSON><PERSON>", "alignItems", "cursor", "padding", "Drag", "Typography", "textColor", "undefined", "fontWeight", "ellipsis", "ActionBox", "Pencil", "Cross", "styled", "theme", "spaces", "colors", "primary200", "neutral150", "primary600"]}