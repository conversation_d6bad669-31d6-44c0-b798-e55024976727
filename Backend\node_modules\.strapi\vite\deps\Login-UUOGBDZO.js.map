{"version": 3, "sources": ["../../../@strapi/admin/ee/admin/src/pages/AuthPage/components/Login.tsx"], "sourcesContent": ["import { Box, Divider, Flex, Typography } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { Login, LoginProps } from '../../../../../../admin/src/pages/Auth/components/Login';\nimport { useGetProvidersQuery } from '../../../../../../admin/src/services/auth';\n\nimport { SSOProviders } from './SSOProviders';\n\nconst DividerFull = styled(Divider)`\n  flex: 1;\n`;\n\nconst LoginEE = (loginProps: LoginProps) => {\n  const { formatMessage } = useIntl();\n  const { isLoading, data: providers = [] } = useGetProvidersQuery(undefined, {\n    skip: !window.strapi.features.isEnabled(window.strapi.features.SSO),\n  });\n\n  if (\n    !window.strapi.features.isEnabled(window.strapi.features.SSO) ||\n    (!isLoading && providers.length === 0)\n  ) {\n    return <Login {...loginProps} />;\n  }\n\n  return (\n    <Login {...loginProps}>\n      <Box paddingTop={7}>\n        <Flex direction=\"column\" alignItems=\"stretch\" gap={7}>\n          <Flex>\n            <DividerFull />\n            <Box paddingLeft={3} paddingRight={3}>\n              <Typography variant=\"sigma\" textColor=\"neutral600\">\n                {formatMessage({ id: 'Auth.login.sso.divider' })}\n              </Typography>\n            </Box>\n            <DividerFull />\n          </Flex>\n          <SSOProviders providers={providers} displayAllProviders={false} />\n        </Flex>\n      </Box>\n    </Login>\n  );\n};\n\nexport { LoginEE };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAMA,cAAcC,GAAOC,OAAAA;;;AAI3B,IAAMC,UAAU,CAACC,eAAAA;AACf,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,WAAWC,MAAMC,YAAY,CAAA,EAAE,IAAKC,qBAAqBC,QAAW;IAC1EC,MAAM,CAACC,OAAOC,OAAOC,SAASC,UAAUH,OAAOC,OAAOC,SAASE,GAAG;EACpE,CAAA;AAEA,MACE,CAACJ,OAAOC,OAAOC,SAASC,UAAUH,OAAOC,OAAOC,SAASE,GAAG,KAC3D,CAACV,aAAaE,UAAUS,WAAW,GACpC;AACA,eAAOC,wBAACC,OAAAA;MAAO,GAAGhB;;EACpB;AAEA,aACEe,wBAACC,OAAAA;IAAO,GAAGhB;IACT,cAAAe,wBAACE,KAAAA;MAAIC,YAAY;MACf,cAAAC,yBAACC,MAAAA;QAAKC,WAAU;QAASC,YAAW;QAAUC,KAAK;;cACjDJ,yBAACC,MAAAA;;kBACCL,wBAACnB,aAAAA,CAAAA,CAAAA;kBACDmB,wBAACE,KAAAA;gBAAIO,aAAa;gBAAGC,cAAc;gBACjC,cAAAV,wBAACW,YAAAA;kBAAWC,SAAQ;kBAAQC,WAAU;4BACnC3B,cAAc;oBAAE4B,IAAI;kBAAyB,CAAA;;;kBAGlDd,wBAACnB,aAAAA,CAAAA,CAAAA;;;cAEHmB,wBAACe,cAAAA;YAAazB;YAAsB0B,qBAAqB;;;;;;AAKnE;", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "styled", "Divider", "LoginEE", "loginProps", "formatMessage", "useIntl", "isLoading", "data", "providers", "useGetProvidersQuery", "undefined", "skip", "window", "strapi", "features", "isEnabled", "SSO", "length", "_jsx", "<PERSON><PERSON>", "Box", "paddingTop", "_jsxs", "Flex", "direction", "alignItems", "gap", "paddingLeft", "paddingRight", "Typography", "variant", "textColor", "id", "SSOProviders", "displayAllProviders"]}