{"version": 3, "sources": ["../../../@strapi/admin/admin/src/services/users.ts"], "sourcesContent": ["import * as Permissions from '../../../shared/contracts/permissions';\nimport * as Roles from '../../../shared/contracts/roles';\nimport * as Users from '../../../shared/contracts/user';\n\nimport { adminApi } from './api';\n\nimport type { Data } from '@strapi/types';\n\nconst usersService = adminApi\n  .enhanceEndpoints({\n    addTagTypes: ['LicenseLimits', 'User', 'Role', 'RolePermissions'],\n  })\n  .injectEndpoints({\n    endpoints: (builder) => ({\n      /**\n       * users\n       */\n      createUser: builder.mutation<Users.Create.Response['data'], Users.Create.Request['body']>({\n        query: (body) => ({\n          url: '/admin/users',\n          method: 'POST',\n          data: body,\n        }),\n        transformResponse: (response: Users.Create.Response) => response.data,\n        invalidatesTags: ['LicenseLimits', { type: 'User', id: 'LIST' }],\n      }),\n      updateUser: builder.mutation<\n        Users.Update.Response['data'],\n        Omit<Users.Update.Request['body'] & Users.Update.Params, 'blocked'>\n      >({\n        query: ({ id, ...body }) => ({\n          url: `/admin/users/${id}`,\n          method: 'PUT',\n          data: body,\n        }),\n        invalidatesTags: (_res, _err, { id }) => [\n          { type: 'User', id },\n          { type: 'User', id: 'LIST' },\n        ],\n      }),\n      getUsers: builder.query<\n        {\n          users: Users.FindAll.Response['data']['results'];\n          pagination: Users.FindAll.Response['data']['pagination'] | null;\n        },\n        GetUsersParams\n      >({\n        query: ({ id, ...params } = {}) => ({\n          url: `/admin/users/${id ?? ''}`,\n          method: 'GET',\n          config: {\n            params,\n          },\n        }),\n        transformResponse: (res: Users.FindAll.Response | Users.FindOne.Response) => {\n          let users: Users.FindAll.Response['data']['results'] = [];\n\n          if (res.data) {\n            if ('results' in res.data) {\n              if (Array.isArray(res.data.results)) {\n                users = res.data.results;\n              }\n            } else {\n              users = [res.data];\n            }\n          }\n\n          return {\n            users,\n            pagination: 'pagination' in res.data ? res.data.pagination : null,\n          };\n        },\n        providesTags: (res, _err, arg) => {\n          if (typeof arg === 'object' && 'id' in arg) {\n            return [{ type: 'User' as const, id: arg.id }];\n          } else {\n            return [\n              ...(res?.users.map(({ id }) => ({ type: 'User' as const, id })) ?? []),\n              { type: 'User' as const, id: 'LIST' },\n            ];\n          }\n        },\n      }),\n      deleteManyUsers: builder.mutation<\n        Users.DeleteMany.Response['data'],\n        Users.DeleteMany.Request['body']\n      >({\n        query: (body) => ({\n          url: '/admin/users/batch-delete',\n          method: 'POST',\n          data: body,\n        }),\n        transformResponse: (res: Users.DeleteMany.Response) => res.data,\n        invalidatesTags: ['LicenseLimits', { type: 'User', id: 'LIST' }],\n      }),\n      /**\n       * roles\n       */\n      createRole: builder.mutation<Roles.Create.Response['data'], Roles.Create.Request['body']>({\n        query: (body) => ({\n          url: '/admin/roles',\n          method: 'POST',\n          data: body,\n        }),\n        transformResponse: (res: Roles.Create.Response) => res.data,\n        invalidatesTags: [{ type: 'Role', id: 'LIST' }],\n      }),\n      getRoles: builder.query<Roles.FindRoles.Response['data'], GetRolesParams | void>({\n        query: ({ id, ...params } = {}) => ({\n          url: `/admin/roles/${id ?? ''}`,\n          method: 'GET',\n          config: {\n            params,\n          },\n        }),\n        transformResponse: (res: Roles.FindRole.Response | Roles.FindRoles.Response) => {\n          let roles: Roles.FindRoles.Response['data'] = [];\n\n          if (res.data) {\n            if (Array.isArray(res.data)) {\n              roles = res.data;\n            } else {\n              roles = [res.data];\n            }\n          }\n\n          return roles;\n        },\n        providesTags: (res, _err, arg) => {\n          if (typeof arg === 'object' && 'id' in arg) {\n            return [{ type: 'Role' as const, id: arg.id }];\n          } else {\n            return [\n              ...(res?.map(({ id }) => ({ type: 'Role' as const, id })) ?? []),\n              { type: 'Role' as const, id: 'LIST' },\n            ];\n          }\n        },\n      }),\n      updateRole: builder.mutation<\n        Roles.Update.Response['data'],\n        Roles.Update.Request['body'] & Roles.Update.Request['params']\n      >({\n        query: ({ id, ...body }) => ({\n          url: `/admin/roles/${id}`,\n          method: 'PUT',\n          data: body,\n        }),\n        transformResponse: (res: Roles.Create.Response) => res.data,\n        invalidatesTags: (_res, _err, { id }) => [{ type: 'Role' as const, id }],\n      }),\n      getRolePermissions: builder.query<\n        Roles.GetPermissions.Response['data'],\n        GetRolePermissionsParams\n      >({\n        query: ({ id, ...params }) => ({\n          url: `/admin/roles/${id}/permissions`,\n          method: 'GET',\n          config: {\n            params,\n          },\n        }),\n        transformResponse: (res: Roles.GetPermissions.Response) => res.data,\n        providesTags: (_res, _err, { id }) => [{ type: 'RolePermissions' as const, id }],\n      }),\n      updateRolePermissions: builder.mutation<\n        Roles.UpdatePermissions.Response['data'],\n        Roles.UpdatePermissions.Request['body'] & Roles.UpdatePermissions.Request['params']\n      >({\n        query: ({ id, ...body }) => ({\n          url: `/admin/roles/${id}/permissions`,\n          method: 'PUT',\n          data: body,\n        }),\n        transformResponse: (res: Roles.UpdatePermissions.Response) => res.data,\n        invalidatesTags: (_res, _err, { id }) => [{ type: 'RolePermissions' as const, id }],\n      }),\n      /**\n       * Permissions\n       */\n      getRolePermissionLayout: builder.query<\n        Permissions.GetAll.Response['data'],\n        Permissions.GetAll.Request['params']\n      >({\n        query: (params) => ({\n          url: '/admin/permissions',\n          method: 'GET',\n          config: {\n            params,\n          },\n        }),\n        transformResponse: (res: Permissions.GetAll.Response) => res.data,\n      }),\n    }),\n    overrideExisting: false,\n  });\n\ntype GetUsersParams =\n  | Users.FindOne.Params\n  | (Users.FindAll.Request['query'] & { id?: never })\n  | void;\ntype GetRolesParams =\n  | Roles.FindRole.Request['params']\n  | (Roles.FindRoles.Request['query'] & { id?: never });\ninterface GetRolePermissionsParams {\n  id: Data.ID;\n}\n\nconst {\n  useCreateUserMutation,\n  useGetUsersQuery,\n  useUpdateUserMutation,\n  useDeleteManyUsersMutation,\n  useGetRolesQuery,\n  useCreateRoleMutation,\n  useUpdateRoleMutation,\n  useGetRolePermissionsQuery,\n  useGetRolePermissionLayoutQuery,\n  useUpdateRolePermissionsMutation,\n} = usersService;\n\nconst useAdminUsers = useGetUsersQuery;\n\nexport {\n  useUpdateUserMutation,\n  useGetRolesQuery,\n  useAdminUsers,\n  useDeleteManyUsersMutation,\n  useCreateUserMutation,\n  useGetRolePermissionsQuery,\n  useGetRolePermissionLayoutQuery,\n  useCreateRoleMutation,\n  useUpdateRolePermissionsMutation,\n  useUpdateRoleMutation,\n};\nexport type { GetRolesParams, GetUsersParams, GetRolePermissionsParams };\n"], "mappings": ";;;;;AAQA,IAAMA,eAAeC,SAClBC,iBAAiB;EAChBC,aAAa;IAAC;IAAiB;IAAQ;IAAQ;EAAkB;AACnE,CAAA,EACCC,gBAAgB;EACfC,WAAW,CAACC,aAAa;;;;IAIvBC,YAAYD,QAAQE,SAAsE;MACxFC,OAAO,CAACC,UAAU;QAChBC,KAAK;QACLC,QAAQ;QACRC,MAAMH;;MAERI,mBAAmB,CAACC,aAAoCA,SAASF;MACjEG,iBAAiB;QAAC;QAAiB;UAAEC,MAAM;UAAQC,IAAI;QAAO;MAAE;IAClE,CAAA;IACAC,YAAYb,QAAQE,SAGlB;MACAC,OAAO,CAAC,EAAES,IAAI,GAAGR,KAAAA,OAAY;QAC3BC,KAAK,gBAAgBO,EAAAA;QACrBN,QAAQ;QACRC,MAAMH;;MAERM,iBAAiB,CAACI,MAAMC,MAAM,EAAEH,GAAE,MAAO;QACvC;UAAED,MAAM;UAAQC;QAAG;QACnB;UAAED,MAAM;UAAQC,IAAI;QAAO;MAC5B;IACH,CAAA;IACAI,UAAUhB,QAAQG,MAMhB;MACAA,OAAO,CAAC,EAAES,IAAI,GAAGK,OAAAA,IAAW,CAAA,OAAQ;QAClCZ,KAAK,gBAAgBO,MAAM,EAAA;QAC3BN,QAAQ;QACRY,QAAQ;UACND;QACF;;MAEFT,mBAAmB,CAACW,QAAAA;AAClB,YAAIC,QAAmD,CAAA;AAEvD,YAAID,IAAIZ,MAAM;AACZ,cAAI,aAAaY,IAAIZ,MAAM;AACzB,gBAAIc,MAAMC,QAAQH,IAAIZ,KAAKgB,OAAO,GAAG;AACnCH,sBAAQD,IAAIZ,KAAKgB;YACnB;iBACK;AACLH,oBAAQ;cAACD,IAAIZ;YAAK;UACpB;QACF;AAEA,eAAO;UACLa;UACAI,YAAY,gBAAgBL,IAAIZ,OAAOY,IAAIZ,KAAKiB,aAAa;QAC/D;MACF;MACAC,cAAc,CAACN,KAAKJ,MAAMW,QAAAA;AACxB,YAAI,OAAOA,QAAQ,YAAY,QAAQA,KAAK;AAC1C,iBAAO;YAAC;cAAEf,MAAM;cAAiBC,IAAIc,IAAId;YAAG;UAAE;eACzC;AACL,iBAAO;YACDO,IAAAA,2BAAKC,MAAMO,IAAI,CAAC,EAAEf,GAAE,OAAQ;cAAED,MAAM;cAAiBC;YAAG,QAAO,CAAA;YACnE;cAAED,MAAM;cAAiBC,IAAI;YAAO;UACrC;QACH;MACF;IACF,CAAA;IACAgB,iBAAiB5B,QAAQE,SAGvB;MACAC,OAAO,CAACC,UAAU;QAChBC,KAAK;QACLC,QAAQ;QACRC,MAAMH;;MAERI,mBAAmB,CAACW,QAAmCA,IAAIZ;MAC3DG,iBAAiB;QAAC;QAAiB;UAAEC,MAAM;UAAQC,IAAI;QAAO;MAAE;IAClE,CAAA;;;;IAIAiB,YAAY7B,QAAQE,SAAsE;MACxFC,OAAO,CAACC,UAAU;QAChBC,KAAK;QACLC,QAAQ;QACRC,MAAMH;;MAERI,mBAAmB,CAACW,QAA+BA,IAAIZ;MACvDG,iBAAiB;QAAC;UAAEC,MAAM;UAAQC,IAAI;QAAO;MAAE;IACjD,CAAA;IACAkB,UAAU9B,QAAQG,MAA+D;MAC/EA,OAAO,CAAC,EAAES,IAAI,GAAGK,OAAAA,IAAW,CAAA,OAAQ;QAClCZ,KAAK,gBAAgBO,MAAM,EAAA;QAC3BN,QAAQ;QACRY,QAAQ;UACND;QACF;;MAEFT,mBAAmB,CAACW,QAAAA;AAClB,YAAIY,QAA0C,CAAA;AAE9C,YAAIZ,IAAIZ,MAAM;AACZ,cAAIc,MAAMC,QAAQH,IAAIZ,IAAI,GAAG;AAC3BwB,oBAAQZ,IAAIZ;iBACP;AACLwB,oBAAQ;cAACZ,IAAIZ;YAAK;UACpB;QACF;AAEA,eAAOwB;MACT;MACAN,cAAc,CAACN,KAAKJ,MAAMW,QAAAA;AACxB,YAAI,OAAOA,QAAQ,YAAY,QAAQA,KAAK;AAC1C,iBAAO;YAAC;cAAEf,MAAM;cAAiBC,IAAIc,IAAId;YAAG;UAAE;eACzC;AACL,iBAAO;YACDO,IAAAA,2BAAKQ,IAAI,CAAC,EAAEf,GAAE,OAAQ;cAAED,MAAM;cAAiBC;YAAG,QAAO,CAAA;YAC7D;cAAED,MAAM;cAAiBC,IAAI;YAAO;UACrC;QACH;MACF;IACF,CAAA;IACAoB,YAAYhC,QAAQE,SAGlB;MACAC,OAAO,CAAC,EAAES,IAAI,GAAGR,KAAAA,OAAY;QAC3BC,KAAK,gBAAgBO,EAAAA;QACrBN,QAAQ;QACRC,MAAMH;;MAERI,mBAAmB,CAACW,QAA+BA,IAAIZ;MACvDG,iBAAiB,CAACI,MAAMC,MAAM,EAAEH,GAAE,MAAO;QAAC;UAAED,MAAM;UAAiBC;QAAG;MAAE;IAC1E,CAAA;IACAqB,oBAAoBjC,QAAQG,MAG1B;MACAA,OAAO,CAAC,EAAES,IAAI,GAAGK,OAAAA,OAAc;QAC7BZ,KAAK,gBAAgBO,EAAAA;QACrBN,QAAQ;QACRY,QAAQ;UACND;QACF;;MAEFT,mBAAmB,CAACW,QAAuCA,IAAIZ;MAC/DkB,cAAc,CAACX,MAAMC,MAAM,EAAEH,GAAE,MAAO;QAAC;UAAED,MAAM;UAA4BC;QAAG;MAAE;IAClF,CAAA;IACAsB,uBAAuBlC,QAAQE,SAG7B;MACAC,OAAO,CAAC,EAAES,IAAI,GAAGR,KAAAA,OAAY;QAC3BC,KAAK,gBAAgBO,EAAAA;QACrBN,QAAQ;QACRC,MAAMH;;MAERI,mBAAmB,CAACW,QAA0CA,IAAIZ;MAClEG,iBAAiB,CAACI,MAAMC,MAAM,EAAEH,GAAE,MAAO;QAAC;UAAED,MAAM;UAA4BC;QAAG;MAAE;IACrF,CAAA;;;;IAIAuB,yBAAyBnC,QAAQG,MAG/B;MACAA,OAAO,CAACc,YAAY;QAClBZ,KAAK;QACLC,QAAQ;QACRY,QAAQ;UACND;QACF;;MAEFT,mBAAmB,CAACW,QAAqCA,IAAIZ;IAC/D,CAAA;;EAEF6B,kBAAkB;AACpB,CAAA;AAaI,IAAA,EACJC,uBACAC,kBACAC,uBACAC,4BACAC,kBACAC,uBACAC,uBACAC,4BACAC,iCACAC,iCAAgC,IAC9BpD;AAEJ,IAAMqD,gBAAgBT;", "names": ["usersService", "adminApi", "enhanceEndpoints", "addTagTypes", "injectEndpoints", "endpoints", "builder", "createUser", "mutation", "query", "body", "url", "method", "data", "transformResponse", "response", "invalidatesTags", "type", "id", "updateUser", "_res", "_err", "getUsers", "params", "config", "res", "users", "Array", "isArray", "results", "pagination", "providesTags", "arg", "map", "deleteManyUsers", "createRole", "getRoles", "roles", "updateRole", "getRolePermissions", "updateRolePermissions", "getRolePermissionLayout", "overrideExisting", "useCreateUserMutation", "useGetUsersQuery", "useUpdateUserMutation", "useDeleteManyUsersMutation", "useGetRolesQuery", "useCreateRoleMutation", "useUpdateRoleMutation", "useGetRolePermissionsQuery", "useGetRolePermissionLayoutQuery", "useUpdateRolePermissionsMutation", "useAdminUsers"]}