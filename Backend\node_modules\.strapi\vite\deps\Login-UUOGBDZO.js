import {
  SSOProviders
} from "./chunk-OT6263S7.js";
import {
  Login
} from "./chunk-D2TGW5YS.js";
import "./chunk-M27D4U76.js";
import "./chunk-HX66WGOY.js";
import "./chunk-IFOFBKTA.js";
import "./chunk-376QHLWZ.js";
import "./chunk-EGNP2T5O.js";
import "./chunk-XDCEA27D.js";
import "./chunk-EZSYDDUK.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-HWAQQGJJ.js";
import "./chunk-L5JCPKMP.js";
import "./chunk-ZJEMJY2Q.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import "./chunk-7LKLOY7A.js";
import {
  useGetProvidersQuery
} from "./chunk-ODQFI753.js";
import "./chunk-ZOP4VV6J.js";
import "./chunk-WH6VCVXU.js";
import "./chunk-IL5G2D22.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-76QM3EFM.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import "./chunk-UBCTZOSQ.js";
import {
  Box,
  Divider,
  Flex,
  Typography,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-ACIMPXWY.js";
import "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/ee/admin/src/pages/AuthPage/components/Login.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var DividerFull = dt(Divider)`
  flex: 1;
`;
var LoginEE = (loginProps) => {
  const { formatMessage } = useIntl();
  const { isLoading, data: providers = [] } = useGetProvidersQuery(void 0, {
    skip: !window.strapi.features.isEnabled(window.strapi.features.SSO)
  });
  if (!window.strapi.features.isEnabled(window.strapi.features.SSO) || !isLoading && providers.length === 0) {
    return (0, import_jsx_runtime.jsx)(Login, {
      ...loginProps
    });
  }
  return (0, import_jsx_runtime.jsx)(Login, {
    ...loginProps,
    children: (0, import_jsx_runtime.jsx)(Box, {
      paddingTop: 7,
      children: (0, import_jsx_runtime.jsxs)(Flex, {
        direction: "column",
        alignItems: "stretch",
        gap: 7,
        children: [
          (0, import_jsx_runtime.jsxs)(Flex, {
            children: [
              (0, import_jsx_runtime.jsx)(DividerFull, {}),
              (0, import_jsx_runtime.jsx)(Box, {
                paddingLeft: 3,
                paddingRight: 3,
                children: (0, import_jsx_runtime.jsx)(Typography, {
                  variant: "sigma",
                  textColor: "neutral600",
                  children: formatMessage({
                    id: "Auth.login.sso.divider"
                  })
                })
              }),
              (0, import_jsx_runtime.jsx)(DividerFull, {})
            ]
          }),
          (0, import_jsx_runtime.jsx)(SSOProviders, {
            providers,
            displayAllProviders: false
          })
        ]
      })
    })
  });
};
export {
  LoginEE
};
//# sourceMappingURL=Login-UUOGBDZO.js.map
