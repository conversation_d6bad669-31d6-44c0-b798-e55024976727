import {
  useQueryParams
} from "./chunk-ODQFI753.js";
import {
  require_lib
} from "./chunk-WH6VCVXU.js";
import {
  createContext
} from "./chunk-76QM3EFM.js";
import {
  Dots,
  Flex,
  NextLink,
  PageLink,
  Pagination,
  PreviousLink,
  SingleSelect,
  SingleSelectOption,
  Typography,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  Link
} from "./chunk-S65ZWNEO.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/components/Pagination.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_qs = __toESM(require_lib(), 1);
var [PaginationProvider, usePagination] = createContext("Pagination");
var Root = React.forwardRef(({ children, defaultPageSize = 10, pageCount = 0, defaultPage = 1, onPageSizeChange, total = 0 }, forwardedRef) => {
  const [{ query }, setQuery] = useQueryParams({
    pageSize: defaultPageSize.toString(),
    page: defaultPage.toString()
  });
  const setPageSize = (pageSize) => {
    setQuery({
      pageSize,
      page: "1"
    });
    if (onPageSizeChange) {
      onPageSizeChange(pageSize);
    }
  };
  return (0, import_jsx_runtime.jsx)(Flex, {
    ref: forwardedRef,
    paddingTop: 4,
    paddingBottom: 4,
    alignItems: "flex-end",
    justifyContent: "space-between",
    children: (0, import_jsx_runtime.jsx)(PaginationProvider, {
      currentQuery: query,
      page: query.page,
      pageSize: query.pageSize,
      pageCount: pageCount.toString(),
      setPageSize,
      total,
      children
    })
  });
});
var PageSize = ({ options = [
  "10",
  "20",
  "50",
  "100"
] }) => {
  const { formatMessage } = useIntl();
  const pageSize = usePagination("PageSize", (state) => state.pageSize);
  const totalCount = usePagination("PageSize", (state) => state.total);
  const setPageSize = usePagination("PageSize", (state) => state.setPageSize);
  const handleChange = (value) => {
    setPageSize(value);
  };
  const minimumOption = parseInt(options[0], 10);
  if (minimumOption >= totalCount) {
    return null;
  }
  return (0, import_jsx_runtime.jsxs)(Flex, {
    gap: 2,
    children: [
      (0, import_jsx_runtime.jsx)(SingleSelect, {
        size: "S",
        "aria-label": formatMessage({
          id: "components.PageFooter.select",
          defaultMessage: "Entries per page"
        }),
        // @ts-expect-error from the DS V2 this won't be needed because we're only returning strings.
        onChange: handleChange,
        value: pageSize,
        children: options.map((option) => (0, import_jsx_runtime.jsx)(SingleSelectOption, {
          value: option,
          children: option
        }, option))
      }),
      (0, import_jsx_runtime.jsx)(Typography, {
        textColor: "neutral600",
        tag: "span",
        children: formatMessage({
          id: "components.PageFooter.select",
          defaultMessage: "Entries per page"
        })
      })
    ]
  });
};
var Links = ({ boundaryCount = 1, siblingCount = 1 }) => {
  const { formatMessage } = useIntl();
  const query = usePagination("Links", (state) => state.currentQuery);
  const currentPage = usePagination("Links", (state) => state.page);
  const totalPages = usePagination("Links", (state) => state.pageCount);
  const pageCount = parseInt(totalPages, 10);
  const activePage = parseInt(currentPage, 10);
  const range = (start, end) => {
    const length = end - start + 1;
    return Array.from({
      length
    }, (_, i) => start + i);
  };
  const startPages = range(1, Math.min(boundaryCount, pageCount));
  const endPages = range(Math.max(pageCount - boundaryCount + 1, boundaryCount + 1), pageCount);
  const siblingsStart = Math.max(
    Math.min(
      // Natural start
      activePage - siblingCount,
      // Lower boundary when page is high
      pageCount - boundaryCount - siblingCount * 2 - 1
    ),
    // Greater than startPages
    boundaryCount + 2
  );
  const siblingsEnd = Math.min(
    Math.max(
      // Natural end
      activePage + siblingCount,
      // Upper boundary when page is low
      boundaryCount + siblingCount * 2 + 2
    ),
    // Less than endPages
    endPages.length > 0 ? endPages[0] - 2 : pageCount - 1
  );
  const items = [
    ...startPages,
    // Start ellipsis
    // eslint-disable-next-line no-nested-ternary
    ...siblingsStart > boundaryCount + 2 ? [
      "start-ellipsis"
    ] : boundaryCount + 1 < pageCount - boundaryCount ? [
      boundaryCount + 1
    ] : [],
    // Sibling pages
    ...range(siblingsStart, siblingsEnd),
    // End ellipsis
    // eslint-disable-next-line no-nested-ternary
    ...siblingsEnd < pageCount - boundaryCount - 1 ? [
      "end-ellipsis"
    ] : pageCount - boundaryCount > boundaryCount ? [
      pageCount - boundaryCount
    ] : [],
    ...endPages
  ];
  if (pageCount <= 1) {
    return null;
  }
  return (0, import_jsx_runtime.jsxs)(Pagination, {
    activePage,
    pageCount,
    children: [
      (0, import_jsx_runtime.jsx)(PreviousLink, {
        tag: Link,
        to: {
          search: (0, import_qs.stringify)({
            ...query,
            page: activePage - 1
          })
        },
        children: formatMessage({
          id: "components.pagination.go-to-previous",
          defaultMessage: "Go to previous page"
        })
      }),
      items.map((item) => {
        if (typeof item === "number") {
          return (0, import_jsx_runtime.jsx)(PageLink, {
            tag: Link,
            number: item,
            to: {
              search: (0, import_qs.stringify)({
                ...query,
                page: item
              })
            },
            children: formatMessage({
              id: "components.pagination.go-to",
              defaultMessage: "Go to page {page}"
            }, {
              page: item
            })
          }, item);
        }
        return (0, import_jsx_runtime.jsx)(Dots, {}, item);
      }),
      (0, import_jsx_runtime.jsx)(NextLink, {
        tag: Link,
        to: {
          search: (0, import_qs.stringify)({
            ...query,
            page: activePage + 1
          })
        },
        children: formatMessage({
          id: "components.pagination.go-to-next",
          defaultMessage: "Go to next page"
        })
      })
    ]
  });
};
var Pagination2 = {
  Root,
  Links,
  PageSize
};

export {
  Pagination2 as Pagination
};
//# sourceMappingURL=chunk-W6ZGCRX6.js.map
