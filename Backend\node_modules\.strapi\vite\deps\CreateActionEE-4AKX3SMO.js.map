{"version": 3, "sources": ["../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/Users/<USER>/CreateActionEE.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { Button, Flex, Tooltip } from '@strapi/design-system';\nimport { Mail, WarningCircle } from '@strapi/icons';\nimport isNil from 'lodash/isNil';\nimport { useIntl } from 'react-intl';\n\nimport { useLicenseLimits } from '../../../../../hooks/useLicenseLimits';\n\nimport type { CreateActionCEProps } from '../../../../../../../../admin/src/pages/Settings/pages/Users/<USER>/CreateActionCE';\n\nexport const CreateActionEE = React.forwardRef<HTMLButtonElement, CreateActionCEProps>(\n  (props, ref) => {\n    const { formatMessage } = useIntl();\n    const { license, isError, isLoading } = useLicenseLimits();\n\n    const { permittedSeats, shouldStopCreate } = license ?? {};\n\n    if (isError || isLoading) {\n      return null;\n    }\n\n    return (\n      <Flex gap={2}>\n        {!isNil(permittedSeats) && shouldStopCreate && (\n          <Tooltip\n            label={formatMessage({\n              id: 'Settings.application.admin-seats.at-limit-tooltip',\n              defaultMessage: 'At limit: add seats to invite more users',\n            })}\n            side=\"left\"\n          >\n            <WarningCircle width=\"1.4rem\" height=\"1.4rem\" fill=\"danger500\" />\n          </Tooltip>\n        )}\n        <Button\n          ref={ref}\n          data-testid=\"create-user-button\"\n          startIcon={<Mail />}\n          size=\"S\"\n          disabled={shouldStopCreate}\n          {...props}\n        >\n          {formatMessage({\n            id: 'Settings.permissions.users.create',\n            defaultMessage: 'Invite new user',\n          })}\n        </Button>\n      </Flex>\n    );\n  }\n);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAWaA,iBAAuBC,iBAClC,CAACC,OAAOC,QAAAA;AACN,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,SAASC,SAASC,UAAS,IAAKC,iBAAAA;AAExC,QAAM,EAAEC,gBAAgBC,iBAAgB,IAAKL,WAAW,CAAA;AAExD,MAAIC,WAAWC,WAAW;AACxB,WAAO;EACT;AAEA,aACEI,yBAACC,MAAAA;IAAKC,KAAK;;MACR,KAACC,aAAAA,SAAML,cAAmBC,KAAAA,wBACzBK,wBAACC,aAAAA;QACCC,OAAOd,cAAc;UACnBe,IAAI;UACJC,gBAAgB;QAClB,CAAA;QACAC,MAAK;QAEL,cAAAL,wBAACM,cAAAA;UAAcC,OAAM;UAASC,QAAO;UAASC,MAAK;;;UAGvDT,wBAACU,QAAAA;QACCvB;QACAwB,eAAY;QACZC,eAAWZ,wBAACa,eAAAA,CAAAA,CAAAA;QACZC,MAAK;QACLC,UAAUpB;QACT,GAAGT;kBAEHE,cAAc;UACbe,IAAI;UACJC,gBAAgB;QAClB,CAAA;;;;AAIR,CACA;", "names": ["CreateActionEE", "forwardRef", "props", "ref", "formatMessage", "useIntl", "license", "isError", "isLoading", "useLicenseLimits", "permittedSeats", "shouldStopCreate", "_jsxs", "Flex", "gap", "isNil", "_jsx", "<PERSON><PERSON><PERSON>", "label", "id", "defaultMessage", "side", "WarningCircle", "width", "height", "fill", "<PERSON><PERSON>", "data-testid", "startIcon", "Mail", "size", "disabled"]}