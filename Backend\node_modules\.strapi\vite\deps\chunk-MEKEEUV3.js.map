{"version": 3, "sources": ["../../../@strapi/upload/dist/admin/package.json.mjs", "../../../@strapi/upload/admin/src/pluginId.ts", "../../../byte-size/index.js", "../../../@strapi/upload/admin/src/utils/getTrad.ts", "../../../@strapi/upload/admin/src/utils/urlYupSchema.ts", "../../../@strapi/upload/admin/src/constants.ts"], "sourcesContent": ["var name = \"@strapi/upload\";\nvar version = \"5.15.1\";\nvar description = \"Makes it easy to upload images and files to your Strapi Application.\";\nvar license = \"SEE LICENSE IN LICENSE\";\nvar author = {\n    name: \"Strapi Solutions SAS\",\n    email: \"<EMAIL>\",\n    url: \"https://strapi.io\"\n};\nvar maintainers = [\n    {\n        name: \"Strapi Solutions SAS\",\n        email: \"<EMAIL>\",\n        url: \"https://strapi.io\"\n    }\n];\nvar exports = {\n    \"./strapi-admin\": {\n        types: \"./dist/admin/src/index.d.ts\",\n        source: \"./admin/src/index.ts\",\n        \"import\": \"./dist/admin/index.mjs\",\n        require: \"./dist/admin/index.js\",\n        \"default\": \"./dist/admin/index.js\"\n    },\n    \"./_internal/shared\": {\n        types: \"./dist/shared/index.d.ts\",\n        source: \"./shared/index.ts\",\n        \"import\": \"./dist/shared/index.mjs\",\n        require: \"./dist/shared/index.js\",\n        \"default\": \"./dist/shared/index.js\"\n    },\n    \"./strapi-server\": {\n        types: \"./dist/server/src/index.d.ts\",\n        source: \"./server/src/index.ts\",\n        \"import\": \"./dist/server/index.mjs\",\n        require: \"./dist/server/index.js\",\n        \"default\": \"./dist/server/index.js\"\n    },\n    \"./package.json\": \"./package.json\"\n};\nvar files = [\n    \"dist/\",\n    \"strapi-server.js\"\n];\nvar scripts = {\n    build: \"run -T npm-run-all clean --parallel build:code build:types\",\n    \"build:code\": \"run -T rollup -c\",\n    \"build:types\": \"run -T run-p build:types:server build:types:admin\",\n    \"build:types:server\": \"run -T tsc -p server/tsconfig.build.json --emitDeclarationOnly\",\n    \"build:types:admin\": \"run -T tsc -p admin/tsconfig.build.json --emitDeclarationOnly\",\n    clean: \"run -T rimraf dist\",\n    lint: \"run -T eslint .\",\n    \"test:front\": \"run -T cross-env IS_EE=true jest --config ./jest.config.front.js\",\n    \"test:unit\": \"run -T jest\",\n    \"test:ts:back\": \"run -T tsc --noEmit -p server/tsconfig.json\",\n    \"test:ts:front\": \"run -T tsc -p admin/tsconfig.json\",\n    \"test:front:watch\": \"run -T cross-env IS_EE=true jest --config ./jest.config.front.js --watch\",\n    \"test:unit:watch\": \"run -T jest --watch\",\n    watch: \"run -T rollup -c -w\"\n};\nvar dependencies = {\n    \"@mux/mux-player-react\": \"3.1.0\",\n    \"@strapi/design-system\": \"2.0.0-rc.26\",\n    \"@strapi/icons\": \"2.0.0-rc.26\",\n    \"@strapi/provider-upload-local\": \"5.15.1\",\n    \"@strapi/utils\": \"5.15.1\",\n    \"byte-size\": \"8.1.1\",\n    cropperjs: \"1.6.1\",\n    \"date-fns\": \"2.30.0\",\n    formik: \"2.4.5\",\n    \"fs-extra\": \"11.2.0\",\n    immer: \"9.0.21\",\n    \"koa-range\": \"0.3.0\",\n    \"koa-static\": \"5.0.0\",\n    lodash: \"4.17.21\",\n    \"mime-types\": \"2.1.35\",\n    \"prop-types\": \"^15.8.1\",\n    qs: \"6.11.1\",\n    \"react-dnd\": \"16.0.1\",\n    \"react-intl\": \"6.6.2\",\n    \"react-query\": \"3.39.3\",\n    \"react-redux\": \"8.1.3\",\n    \"react-select\": \"5.8.0\",\n    sharp: \"0.33.5\",\n    yup: \"0.32.9\"\n};\nvar devDependencies = {\n    \"@strapi/admin\": \"5.15.1\",\n    \"@strapi/types\": \"5.15.1\",\n    \"@testing-library/dom\": \"10.1.0\",\n    \"@testing-library/react\": \"15.0.7\",\n    \"@testing-library/user-event\": \"14.5.2\",\n    \"@types/byte-size\": \"8.1.2\",\n    \"@types/fs-extra\": \"11.0.4\",\n    \"@types/koa\": \"2.13.4\",\n    \"@types/koa-range\": \"0.3.5\",\n    \"@types/koa-static\": \"4.0.2\",\n    formidable: \"3.5.4\",\n    koa: \"2.16.1\",\n    \"koa-body\": \"6.0.1\",\n    msw: \"1.3.0\",\n    react: \"18.3.1\",\n    \"react-dom\": \"18.3.1\",\n    \"react-router-dom\": \"6.22.3\",\n    \"styled-components\": \"6.1.8\"\n};\nvar peerDependencies = {\n    \"@strapi/admin\": \"^5.0.0\",\n    react: \"^17.0.0 || ^18.0.0\",\n    \"react-dom\": \"^17.0.0 || ^18.0.0\",\n    \"react-router-dom\": \"^6.0.0\",\n    \"styled-components\": \"^6.0.0\"\n};\nvar engines = {\n    node: \">=18.0.0 <=22.x.x\",\n    npm: \">=6.0.0\"\n};\nvar strapi = {\n    displayName: \"Media Library\",\n    name: \"upload\",\n    description: \"Media file management.\",\n    required: true,\n    kind: \"plugin\"\n};\nvar pluginPkg = {\n    name: name,\n    version: version,\n    description: description,\n    license: license,\n    author: author,\n    maintainers: maintainers,\n    exports: exports,\n    files: files,\n    scripts: scripts,\n    dependencies: dependencies,\n    devDependencies: devDependencies,\n    peerDependencies: peerDependencies,\n    engines: engines,\n    strapi: strapi\n};\n\nexport { author, pluginPkg as default, dependencies, description, devDependencies, engines, exports, files, license, maintainers, name, peerDependencies, scripts, strapi, version };\n//# sourceMappingURL=package.json.mjs.map\n", "import pluginPkg from '../../package.json';\n\nexport const pluginId = pluginPkg.name.replace(/^@strapi\\//i, '');\n", "/**\n * @module byte-size\n */\n\nlet defaultOptions = {}\nconst _options = new WeakMap()\n\nconst referenceTables = {\n  metric: [\n    { from: 0, to: 1e3, unit: 'B', long: 'bytes' },\n    { from: 1e3, to: 1e6, unit: 'kB', long: 'kilobytes' },\n    { from: 1e6, to: 1e9, unit: 'MB', long: 'megabytes' },\n    { from: 1e9, to: 1e12, unit: 'GB', long: 'gigabytes' },\n    { from: 1e12, to: 1e15, unit: 'TB', long: 'terabytes' },\n    { from: 1e15, to: 1e18, unit: 'PB', long: 'petabytes' },\n    { from: 1e18, to: 1e21, unit: 'EB', long: 'exabytes' },\n    { from: 1e21, to: 1e24, unit: 'ZB', long: 'zettabytes' },\n    { from: 1e24, to: 1e27, unit: 'YB', long: 'yottabytes' }\n  ],\n  metric_octet: [\n    { from: 0, to: 1e3, unit: 'o', long: 'octets' },\n    { from: 1e3, to: 1e6, unit: 'ko', long: 'kilooctets' },\n    { from: 1e6, to: 1e9, unit: 'Mo', long: 'megaoctets' },\n    { from: 1e9, to: 1e12, unit: 'Go', long: 'gigaoctets' },\n    { from: 1e12, to: 1e15, unit: 'To', long: 'teraoctets' },\n    { from: 1e15, to: 1e18, unit: 'Po', long: 'petaoctets' },\n    { from: 1e18, to: 1e21, unit: 'Eo', long: 'exaoctets' },\n    { from: 1e21, to: 1e24, unit: 'Zo', long: 'zettaoctets' },\n    { from: 1e24, to: 1e27, unit: 'Yo', long: 'yottaoctets' }\n  ],\n  iec: [\n    { from: 0, to: Math.pow(1024, 1), unit: 'B', long: 'bytes' },\n    { from: Math.pow(1024, 1), to: Math.pow(1024, 2), unit: 'KiB', long: 'kibibytes' },\n    { from: Math.pow(1024, 2), to: Math.pow(1024, 3), unit: 'MiB', long: 'mebibytes' },\n    { from: Math.pow(1024, 3), to: Math.pow(1024, 4), unit: 'GiB', long: 'gibibytes' },\n    { from: Math.pow(1024, 4), to: Math.pow(1024, 5), unit: 'TiB', long: 'tebibytes' },\n    { from: Math.pow(1024, 5), to: Math.pow(1024, 6), unit: 'PiB', long: 'pebibytes' },\n    { from: Math.pow(1024, 6), to: Math.pow(1024, 7), unit: 'EiB', long: 'exbibytes' },\n    { from: Math.pow(1024, 7), to: Math.pow(1024, 8), unit: 'ZiB', long: 'zebibytes' },\n    { from: Math.pow(1024, 8), to: Math.pow(1024, 9), unit: 'YiB', long: 'yobibytes' }\n  ],\n  iec_octet: [\n    { from: 0, to: Math.pow(1024, 1), unit: 'o', long: 'octets' },\n    { from: Math.pow(1024, 1), to: Math.pow(1024, 2), unit: 'Kio', long: 'kibioctets' },\n    { from: Math.pow(1024, 2), to: Math.pow(1024, 3), unit: 'Mio', long: 'mebioctets' },\n    { from: Math.pow(1024, 3), to: Math.pow(1024, 4), unit: 'Gio', long: 'gibioctets' },\n    { from: Math.pow(1024, 4), to: Math.pow(1024, 5), unit: 'Tio', long: 'tebioctets' },\n    { from: Math.pow(1024, 5), to: Math.pow(1024, 6), unit: 'Pio', long: 'pebioctets' },\n    { from: Math.pow(1024, 6), to: Math.pow(1024, 7), unit: 'Eio', long: 'exbioctets' },\n    { from: Math.pow(1024, 7), to: Math.pow(1024, 8), unit: 'Zio', long: 'zebioctets' },\n    { from: Math.pow(1024, 8), to: Math.pow(1024, 9), unit: 'Yio', long: 'yobioctets' }\n  ]\n}\n\nclass ByteSize {\n  constructor (bytes, options) {\n    options = Object.assign({\n      units: 'metric',\n      precision: 1,\n      locale: undefined // Default to the user's system locale\n    }, defaultOptions, options)\n    _options.set(this, options)\n\n    Object.assign(referenceTables, options.customUnits)\n\n    const prefix = bytes < 0 ? '-' : ''\n    bytes = Math.abs(bytes)\n    const table = referenceTables[options.units]\n    if (table) {\n      const units = table.find(u => bytes >= u.from && bytes < u.to)\n      if (units) {\n        const defaultFormat = new Intl.NumberFormat(options.locale, {\n          style: 'decimal',\n          minimumFractionDigits: options.precision,\n          maximumFractionDigits: options.precision\n        })\n        const value = units.from === 0\n          ? prefix + bytes\n          : prefix + defaultFormat.format(bytes / units.from)\n        this.value = value\n        this.unit = units.unit\n        this.long = units.long\n      } else {\n        this.value = prefix + bytes\n        this.unit = ''\n        this.long = ''\n      }\n    } else {\n      throw new Error(`Invalid units specified: ${options.units}`)\n    }\n  }\n\n  toString () {\n    const options = _options.get(this)\n    return options.toStringFn ? options.toStringFn.bind(this)() : `${this.value} ${this.unit}`\n  }\n}\n\n/**\n * Returns an object with the spec `{ value: string, unit: string, long: string }`. The returned object defines a `toString` method meaning it can be used in any string context.\n * @param {number} - The bytes value to convert.\n * @param [options] {object} - Optional config.\n * @param [options.precision] {number} - Number of decimal places. Defaults to `1`.\n * @param [options.units] {string} - Specify `'metric'`, `'iec'`, `'metric_octet'`, `'iec_octet'` or the name of a property from the custom units table in `options.customUnits`. Defaults to `metric`.\n * @param [options.customUnits] {object} - An object containing one or more custom unit lookup tables.\n * @param [options.toStringFn] {function} - A `toString` function to override the default.\n * @param [options.locale] {string|string[]} - *Node >=13 or modern browser only - on earlier platforms this option is ignored*. The locale to use for number formatting (e.g. `'de-DE'`). Defaults to your system locale. Passed directed into [Intl.NumberFormat()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/NumberFormat/NumberFormat).\n * @returns {object}\n * @alias module:byte-size\n */\nfunction byteSize (bytes, options) {\n  return new ByteSize(bytes, options)\n}\n\n/**\n * Set the default `byteSize` options for the duration of the process.\n * @param options {object} - A `byteSize` options object.\n */\nbyteSize.defaultOptions = function (options) {\n  defaultOptions = options\n}\n\nexport default byteSize\n", "import { pluginId } from '../pluginId';\n\nexport const getTrad = (id: string) => `${pluginId}.${id}`;\n", "import { translatedErrors as errorsTrads } from '@strapi/admin/strapi-admin';\nimport * as yup from 'yup';\n\nimport { getTrad } from './getTrad';\n\nexport const urlSchema = yup.object().shape({\n  urls: yup.string().test({\n    name: 'isUrlValid',\n    // eslint-disable-next-line no-template-curly-in-string\n    message: '${path}',\n    test(values = '') {\n      const urls = values.split(/\\r?\\n/);\n\n      if (urls.length === 0) {\n        return this.createError({\n          path: this.path,\n          message: errorsTrads.min.id,\n        });\n      }\n\n      if (urls.length > 20) {\n        return this.createError({\n          path: this.path,\n          message: errorsTrads.max.id,\n        });\n      }\n\n      const filtered = urls.filter((val) => {\n        try {\n          // eslint-disable-next-line no-new\n          new URL(val);\n\n          return false;\n        } catch (err) {\n          // invalid url\n          return true;\n        }\n      });\n\n      const filteredLength = filtered.length;\n\n      if (filteredLength === 0) {\n        return true;\n      }\n\n      const errorMessage =\n        filteredLength > 1\n          ? 'form.upload-url.error.url.invalids'\n          : 'form.upload-url.error.url.invalid';\n\n      return this.createError({\n        path: this.path,\n        message: getTrad(errorMessage),\n        params: { number: filtered.length },\n      });\n    },\n  }),\n});\n", "import { getTrad } from './utils';\n\nexport enum AssetType {\n  Video = 'video',\n  Image = 'image',\n  Document = 'doc',\n  Audio = 'audio',\n}\n\nexport enum AssetSource {\n  Url = 'url',\n  Computer = 'computer',\n}\n\nexport const PERMISSIONS = {\n  // This permission regards the main component (App) and is used to tell\n  // If the plugin link should be displayed in the menu\n  // And also if the plugin is accessible. This use case is found when a user types the url of the\n  // plugin directly in the browser\n  main: [\n    { action: 'plugin::upload.read', subject: null },\n    {\n      action: 'plugin::upload.assets.create',\n      subject: null,\n    },\n    {\n      action: 'plugin::upload.assets.update',\n      subject: null,\n    },\n  ],\n  copyLink: [\n    {\n      action: 'plugin::upload.assets.copy-link',\n      subject: null,\n    },\n  ],\n  create: [\n    {\n      action: 'plugin::upload.assets.create',\n      subject: null,\n    },\n  ],\n  download: [\n    {\n      action: 'plugin::upload.assets.download',\n      subject: null,\n    },\n  ],\n  read: [{ action: 'plugin::upload.read', subject: null }],\n  configureView: [{ action: 'plugin::upload.configure-view', subject: null }],\n  settings: [{ action: 'plugin::upload.settings.read', subject: null }],\n  update: [{ action: 'plugin::upload.assets.update', subject: null, fields: null }],\n};\n\nexport const tableHeaders = [\n  {\n    name: 'preview',\n    key: 'preview',\n    metadatas: {\n      label: { id: getTrad('list.table.header.preview'), defaultMessage: 'preview' },\n      isSortable: false,\n    },\n    type: 'image',\n  },\n  {\n    name: 'name',\n    key: 'name',\n    metadatas: {\n      label: { id: getTrad('list.table.header.name'), defaultMessage: 'name' },\n      isSortable: true,\n    },\n    type: 'text',\n  },\n  {\n    name: 'ext',\n    key: 'extension',\n    metadatas: {\n      label: { id: getTrad('list.table.header.ext'), defaultMessage: 'extension' },\n      isSortable: false,\n    },\n    type: 'ext',\n  },\n  {\n    name: 'size',\n    key: 'size',\n    metadatas: {\n      label: { id: getTrad('list.table.header.size'), defaultMessage: 'size' },\n      isSortable: false,\n    },\n    type: 'size',\n  },\n  {\n    name: 'createdAt',\n    key: 'createdAt',\n    metadatas: {\n      label: { id: getTrad('list.table.header.createdAt'), defaultMessage: 'created' },\n      isSortable: true,\n    },\n    type: 'date',\n  },\n  {\n    name: 'updatedAt',\n    key: 'updatedAt',\n    metadatas: {\n      label: { id: getTrad('list.table.header.updatedAt'), defaultMessage: 'last update' },\n      isSortable: true,\n    },\n    type: 'date',\n  },\n];\n\nexport const sortOptions = [\n  { key: 'sort.created_at_desc', value: 'createdAt:DESC' },\n  { key: 'sort.created_at_asc', value: 'createdAt:ASC' },\n  { key: 'sort.name_asc', value: 'name:ASC' },\n  { key: 'sort.name_desc', value: 'name:DESC' },\n  { key: 'sort.updated_at_desc', value: 'updatedAt:DESC' },\n  { key: 'sort.updated_at_asc', value: 'updatedAt:ASC' },\n];\n\nexport const pageSizes = [10, 20, 50, 100];\n\nexport const localStorageKeys = {\n  modalView: `STRAPI_UPLOAD_MODAL_VIEW`,\n  view: `STRAPI_UPLOAD_LIBRARY_VIEW`,\n};\n\nexport const viewOptions = {\n  GRID: 0,\n  LIST: 1,\n};\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAI,OAAO;AACX,IAAI,UAAU;AACd,IAAI,cAAc;AAClB,IAAI,UAAU;AACd,IAAI,SAAS;AAAA,EACT,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AACT;AACA,IAAI,cAAc;AAAA,EACd;AAAA,IACI,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,EACT;AACJ;AACA,IAAI,UAAU;AAAA,EACV,kBAAkB;AAAA,IACd,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAAS;AAAA,IACT,WAAW;AAAA,EACf;AAAA,EACA,sBAAsB;AAAA,IAClB,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAAS;AAAA,IACT,WAAW;AAAA,EACf;AAAA,EACA,mBAAmB;AAAA,IACf,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAAS;AAAA,IACT,WAAW;AAAA,EACf;AAAA,EACA,kBAAkB;AACtB;AACA,IAAI,QAAQ;AAAA,EACR;AAAA,EACA;AACJ;AACA,IAAI,UAAU;AAAA,EACV,OAAO;AAAA,EACP,cAAc;AAAA,EACd,eAAe;AAAA,EACf,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,cAAc;AAAA,EACd,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,OAAO;AACX;AACA,IAAI,eAAe;AAAA,EACf,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,iBAAiB;AAAA,EACjB,iCAAiC;AAAA,EACjC,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,aAAa;AAAA,EACb,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,cAAc;AAAA,EACd,IAAI;AAAA,EACJ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,eAAe;AAAA,EACf,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,OAAO;AAAA,EACP,KAAK;AACT;AACA,IAAI,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,cAAc;AAAA,EACd,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,YAAY;AAAA,EACZ,KAAK;AAAA,EACL,YAAY;AAAA,EACZ,KAAK;AAAA,EACL,OAAO;AAAA,EACP,aAAa;AAAA,EACb,oBAAoB;AAAA,EACpB,qBAAqB;AACzB;AACA,IAAI,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,OAAO;AAAA,EACP,aAAa;AAAA,EACb,oBAAoB;AAAA,EACpB,qBAAqB;AACzB;AACA,IAAI,UAAU;AAAA,EACV,MAAM;AAAA,EACN,KAAK;AACT;AACA,IAAI,SAAS;AAAA,EACT,aAAa;AAAA,EACb,MAAM;AAAA,EACN,aAAa;AAAA,EACb,UAAU;AAAA,EACV,MAAM;AACV;AACA,IAAI,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;;;ACzIO,IAAMA,WAAWC,UAAUC,KAAKC,QAAQ,eAAe,EAAI;;;ACElE,IAAI,iBAAiB,CAAC;AACtB,IAAM,WAAW,oBAAI,QAAQ;AAE7B,IAAM,kBAAkB;AAAA,EACtB,QAAQ;AAAA,IACN,EAAE,MAAM,GAAG,IAAI,KAAK,MAAM,KAAK,MAAM,QAAQ;AAAA,IAC7C,EAAE,MAAM,KAAK,IAAI,KAAK,MAAM,MAAM,MAAM,YAAY;AAAA,IACpD,EAAE,MAAM,KAAK,IAAI,KAAK,MAAM,MAAM,MAAM,YAAY;AAAA,IACpD,EAAE,MAAM,KAAK,IAAI,MAAM,MAAM,MAAM,MAAM,YAAY;AAAA,IACrD,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,YAAY;AAAA,IACtD,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,YAAY;AAAA,IACtD,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,WAAW;AAAA,IACrD,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,aAAa;AAAA,IACvD,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,aAAa;AAAA,EACzD;AAAA,EACA,cAAc;AAAA,IACZ,EAAE,MAAM,GAAG,IAAI,KAAK,MAAM,KAAK,MAAM,SAAS;AAAA,IAC9C,EAAE,MAAM,KAAK,IAAI,KAAK,MAAM,MAAM,MAAM,aAAa;AAAA,IACrD,EAAE,MAAM,KAAK,IAAI,KAAK,MAAM,MAAM,MAAM,aAAa;AAAA,IACrD,EAAE,MAAM,KAAK,IAAI,MAAM,MAAM,MAAM,MAAM,aAAa;AAAA,IACtD,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,aAAa;AAAA,IACvD,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,aAAa;AAAA,IACvD,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,YAAY;AAAA,IACtD,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,cAAc;AAAA,IACxD,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,cAAc;AAAA,EAC1D;AAAA,EACA,KAAK;AAAA,IACH,EAAE,MAAM,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,KAAK,MAAM,QAAQ;AAAA,IAC3D,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,YAAY;AAAA,IACjF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,YAAY;AAAA,IACjF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,YAAY;AAAA,IACjF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,YAAY;AAAA,IACjF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,YAAY;AAAA,IACjF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,YAAY;AAAA,IACjF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,YAAY;AAAA,IACjF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,YAAY;AAAA,EACnF;AAAA,EACA,WAAW;AAAA,IACT,EAAE,MAAM,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,KAAK,MAAM,SAAS;AAAA,IAC5D,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,aAAa;AAAA,IAClF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,aAAa;AAAA,IAClF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,aAAa;AAAA,IAClF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,aAAa;AAAA,IAClF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,aAAa;AAAA,IAClF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,aAAa;AAAA,IAClF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,aAAa;AAAA,IAClF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,aAAa;AAAA,EACpF;AACF;AAEA,IAAM,WAAN,MAAe;AAAA,EACb,YAAa,OAAO,SAAS;AAC3B,cAAU,OAAO,OAAO;AAAA,MACtB,OAAO;AAAA,MACP,WAAW;AAAA,MACX,QAAQ;AAAA;AAAA,IACV,GAAG,gBAAgB,OAAO;AAC1B,aAAS,IAAI,MAAM,OAAO;AAE1B,WAAO,OAAO,iBAAiB,QAAQ,WAAW;AAElD,UAAM,SAAS,QAAQ,IAAI,MAAM;AACjC,YAAQ,KAAK,IAAI,KAAK;AACtB,UAAM,QAAQ,gBAAgB,QAAQ,KAAK;AAC3C,QAAI,OAAO;AACT,YAAM,QAAQ,MAAM,KAAK,OAAK,SAAS,EAAE,QAAQ,QAAQ,EAAE,EAAE;AAC7D,UAAI,OAAO;AACT,cAAM,gBAAgB,IAAI,KAAK,aAAa,QAAQ,QAAQ;AAAA,UAC1D,OAAO;AAAA,UACP,uBAAuB,QAAQ;AAAA,UAC/B,uBAAuB,QAAQ;AAAA,QACjC,CAAC;AACD,cAAM,QAAQ,MAAM,SAAS,IACzB,SAAS,QACT,SAAS,cAAc,OAAO,QAAQ,MAAM,IAAI;AACpD,aAAK,QAAQ;AACb,aAAK,OAAO,MAAM;AAClB,aAAK,OAAO,MAAM;AAAA,MACpB,OAAO;AACL,aAAK,QAAQ,SAAS;AACtB,aAAK,OAAO;AACZ,aAAK,OAAO;AAAA,MACd;AAAA,IACF,OAAO;AACL,YAAM,IAAI,MAAM,4BAA4B,QAAQ,KAAK,EAAE;AAAA,IAC7D;AAAA,EACF;AAAA,EAEA,WAAY;AACV,UAAM,UAAU,SAAS,IAAI,IAAI;AACjC,WAAO,QAAQ,aAAa,QAAQ,WAAW,KAAK,IAAI,EAAE,IAAI,GAAG,KAAK,KAAK,IAAI,KAAK,IAAI;AAAA,EAC1F;AACF;AAcA,SAAS,SAAU,OAAO,SAAS;AACjC,SAAO,IAAI,SAAS,OAAO,OAAO;AACpC;AAMA,SAAS,iBAAiB,SAAU,SAAS;AAC3C,mBAAiB;AACnB;AAEA,IAAO,oBAAQ;;;ACxHR,IAAMC,UAAU,CAACC,OAAe,GAAGC,QAAAA,IAAYD,EAAG;;;ICG5CE,YAAgBC,QAAM,EAAGC,MAAM;EAC1CC,MAAUC,OAAM,EAAGC,KAAK;IACtBC,MAAM;;IAENC,SAAS;IACTF,KAAKG,SAAS,IAAE;AACd,YAAML,OAAOK,OAAOC,MAAM,OAAA;AAE1B,UAAIN,KAAKO,WAAW,GAAG;AACrB,eAAO,KAAKC,YAAY;UACtBC,MAAM,KAAKA;UACXL,SAASM,YAAYC,IAAIC;QAC3B,CAAA;MACF;AAEA,UAAIZ,KAAKO,SAAS,IAAI;AACpB,eAAO,KAAKC,YAAY;UACtBC,MAAM,KAAKA;UACXL,SAASM,YAAYG,IAAID;QAC3B,CAAA;MACF;AAEA,YAAME,WAAWd,KAAKe,OAAO,CAACC,QAAAA;AAC5B,YAAI;AAEF,cAAIC,IAAID,GAAAA;AAER,iBAAO;QACT,SAASE,KAAK;AAEZ,iBAAO;QACT;MACF,CAAA;AAEA,YAAMC,iBAAiBL,SAASP;AAEhC,UAAIY,mBAAmB,GAAG;AACxB,eAAO;MACT;AAEA,YAAMC,eACJD,iBAAiB,IACb,uCACA;AAEN,aAAO,KAAKX,YAAY;QACtBC,MAAM,KAAKA;QACXL,SAASiB,QAAQD,YAAAA;QACjBE,QAAQ;UAAEC,QAAQT,SAASP;QAAO;MACpC,CAAA;IACF;EACF,CAAA;AACF,CAAG;;;;;CCvDSiB,SAAAA,YAAAA;;;;;GAAAA,cAAAA,YAAAA,CAAAA,EAAAA;;CAOAC,SAAAA,cAAAA;;;GAAAA,gBAAAA,cAAAA,CAAAA,EAAAA;IAKCC,cAAc;;;;;EAKzBC,MAAM;IACJ;MAAEC,QAAQ;MAAuBC,SAAS;IAAK;IAC/C;MACED,QAAQ;MACRC,SAAS;IACX;IACA;MACED,QAAQ;MACRC,SAAS;IACX;EACD;EACDC,UAAU;IACR;MACEF,QAAQ;MACRC,SAAS;IACX;EACD;EACDE,QAAQ;IACN;MACEH,QAAQ;MACRC,SAAS;IACX;EACD;EACDG,UAAU;IACR;MACEJ,QAAQ;MACRC,SAAS;IACX;EACD;EACDI,MAAM;IAAC;MAAEL,QAAQ;MAAuBC,SAAS;IAAK;EAAE;EACxDK,eAAe;IAAC;MAAEN,QAAQ;MAAiCC,SAAS;IAAK;EAAE;EAC3EM,UAAU;IAAC;MAAEP,QAAQ;MAAgCC,SAAS;IAAK;EAAE;EACrEO,QAAQ;IAAC;MAAER,QAAQ;MAAgCC,SAAS;MAAMQ,QAAQ;IAAK;EAAE;AACnF;IAEaC,eAAe;EAC1B;IACEC,MAAM;IACNC,KAAK;IACLC,WAAW;MACTC,OAAO;QAAEC,IAAIC,QAAQ,2BAAA;QAA8BC,gBAAgB;MAAU;MAC7EC,YAAY;IACd;IACAC,MAAM;EACR;EACA;IACER,MAAM;IACNC,KAAK;IACLC,WAAW;MACTC,OAAO;QAAEC,IAAIC,QAAQ,wBAAA;QAA2BC,gBAAgB;MAAO;MACvEC,YAAY;IACd;IACAC,MAAM;EACR;EACA;IACER,MAAM;IACNC,KAAK;IACLC,WAAW;MACTC,OAAO;QAAEC,IAAIC,QAAQ,uBAAA;QAA0BC,gBAAgB;MAAY;MAC3EC,YAAY;IACd;IACAC,MAAM;EACR;EACA;IACER,MAAM;IACNC,KAAK;IACLC,WAAW;MACTC,OAAO;QAAEC,IAAIC,QAAQ,wBAAA;QAA2BC,gBAAgB;MAAO;MACvEC,YAAY;IACd;IACAC,MAAM;EACR;EACA;IACER,MAAM;IACNC,KAAK;IACLC,WAAW;MACTC,OAAO;QAAEC,IAAIC,QAAQ,6BAAA;QAAgCC,gBAAgB;MAAU;MAC/EC,YAAY;IACd;IACAC,MAAM;EACR;EACA;IACER,MAAM;IACNC,KAAK;IACLC,WAAW;MACTC,OAAO;QAAEC,IAAIC,QAAQ,6BAAA;QAAgCC,gBAAgB;MAAc;MACnFC,YAAY;IACd;IACAC,MAAM;EACR;;IAGWC,cAAc;EACzB;IAAER,KAAK;IAAwBS,OAAO;EAAiB;EACvD;IAAET,KAAK;IAAuBS,OAAO;EAAgB;EACrD;IAAET,KAAK;IAAiBS,OAAO;EAAW;EAC1C;IAAET,KAAK;IAAkBS,OAAO;EAAY;EAC5C;IAAET,KAAK;IAAwBS,OAAO;EAAiB;EACvD;IAAET,KAAK;IAAuBS,OAAO;EAAgB;;IAG1CC,YAAY;EAAC;EAAI;EAAI;EAAI;;IAEzBC,mBAAmB;EAC9BC,WAAW;EACXC,MAAM;AACR;IAEaC,cAAc;EACzBC,MAAM;EACNC,MAAM;AACR;", "names": ["pluginId", "pluginPkg", "name", "replace", "getTrad", "id", "pluginId", "urlSchema", "object", "shape", "urls", "string", "test", "name", "message", "values", "split", "length", "createError", "path", "<PERSON><PERSON><PERSON><PERSON>", "min", "id", "max", "filtered", "filter", "val", "URL", "err", "<PERSON><PERSON><PERSON><PERSON>", "errorMessage", "getTrad", "params", "number", "AssetType", "AssetSource", "PERMISSIONS", "main", "action", "subject", "copyLink", "create", "download", "read", "configure<PERSON><PERSON><PERSON>", "settings", "update", "fields", "tableHeaders", "name", "key", "metadatas", "label", "id", "getTrad", "defaultMessage", "isSortable", "type", "sortOptions", "value", "pageSizes", "localStorageKeys", "modalView", "view", "viewOptions", "GRID", "LIST"]}