{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/CreatePage.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {\n  Box,\n  Button,\n  Field,\n  Flex,\n  Grid,\n  Main,\n  Textarea,\n  TextInput,\n  Typography,\n} from '@strapi/design-system';\nimport { Check } from '@strapi/icons';\nimport { format } from 'date-fns';\nimport { Formik, Form, FormikHelpers } from 'formik';\nimport { useIntl } from 'react-intl';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { styled } from 'styled-components';\nimport * as yup from 'yup';\n\nimport { Layouts } from '../../../../components/Layouts/Layout';\nimport { Page } from '../../../../components/PageHelpers';\nimport { useTypedSelector } from '../../../../core/store/hooks';\nimport { BackButton } from '../../../../features/BackButton';\nimport { useNotification } from '../../../../features/Notifications';\nimport { useTracking } from '../../../../features/Tracking';\nimport { useAPIErrorHandler } from '../../../../hooks/useAPIErrorHandler';\nimport {\n  useCreateRoleMutation,\n  useGetRolePermissionLayoutQuery,\n  useGetRolePermissionsQuery,\n  useUpdateRolePermissionsMutation,\n} from '../../../../services/users';\nimport { isBaseQueryError } from '../../../../utils/baseQuery';\nimport { translatedErrors } from '../../../../utils/translatedErrors';\n\nimport { Permissions, PermissionsAPI } from './components/Permissions';\n\n/* -------------------------------------------------------------------------------------------------\n * CreatePage\n * -----------------------------------------------------------------------------------------------*/\n\nconst CREATE_SCHEMA = yup.object().shape({\n  name: yup.string().required(translatedErrors.required.id),\n  description: yup.string().required(translatedErrors.required.id),\n});\n\n/**\n * TODO: be nice if we could just infer this from the schema\n */\ninterface CreateRoleFormValues {\n  name: string;\n  description: string;\n}\n\n/**\n * TODO: this whole section of the app needs refactoring. Using a ref to\n * manage the state of the child is nonsensical.\n */\nconst CreatePage = () => {\n  const { id } = useParams();\n  const { toggleNotification } = useNotification();\n  const { formatMessage } = useIntl();\n  const navigate = useNavigate();\n  const permissionsRef = React.useRef<PermissionsAPI>(null);\n  const { trackUsage } = useTracking();\n  const {\n    _unstableFormatAPIError: formatAPIError,\n    _unstableFormatValidationErrors: formatValidationErrors,\n  } = useAPIErrorHandler();\n\n  const { isLoading: isLoadingPermissionsLayout, currentData: permissionsLayout } =\n    useGetRolePermissionLayoutQuery({\n      /**\n       * Role here is a query param so if there's no role we pass an empty string\n       * which returns us a default layout.\n       */\n      role: id ?? '',\n    });\n\n  /**\n   * We need this so if we're cloning a role, we can fetch\n   * the current permissions that role has.\n   */\n  const { currentData: rolePermissions, isLoading: isLoadingRole } = useGetRolePermissionsQuery(\n    {\n      id: id!,\n    },\n    {\n      skip: !id,\n      refetchOnMountOrArgChange: true,\n    }\n  );\n\n  const [createRole] = useCreateRoleMutation();\n  const [updateRolePermissions] = useUpdateRolePermissionsMutation();\n\n  const handleCreateRoleSubmit = async (\n    data: CreateRoleFormValues,\n    formik: FormikHelpers<CreateRoleFormValues>\n  ) => {\n    try {\n      if (id) {\n        trackUsage('willDuplicateRole');\n      } else {\n        trackUsage('willCreateNewRole');\n      }\n\n      const res = await createRole(data);\n\n      if ('error' in res) {\n        if (isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n          formik.setErrors(formatValidationErrors(res.error));\n        } else {\n          toggleNotification({\n            type: 'danger',\n            message: formatAPIError(res.error),\n          });\n        }\n\n        return;\n      }\n\n      const { permissionsToSend } = permissionsRef.current?.getPermissions() ?? {};\n\n      if (res.data.id && Array.isArray(permissionsToSend) && permissionsToSend.length > 0) {\n        const updateRes = await updateRolePermissions({\n          id: res.data.id,\n          permissions: permissionsToSend,\n        });\n\n        if ('error' in updateRes) {\n          if (isBaseQueryError(updateRes.error) && updateRes.error.name === 'ValidationError') {\n            formik.setErrors(formatValidationErrors(updateRes.error));\n          } else {\n            toggleNotification({\n              type: 'danger',\n              message: formatAPIError(updateRes.error),\n            });\n          }\n\n          return;\n        }\n      }\n\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({ id: 'Settings.roles.created', defaultMessage: 'created' }),\n      });\n\n      navigate(`../roles/${res.data.id.toString()}`, { replace: true });\n    } catch (err) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occurred' }),\n      });\n    }\n  };\n\n  if ((isLoadingPermissionsLayout && isLoadingRole) || !permissionsLayout) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Main>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: 'Roles',\n          }\n        )}\n      </Page.Title>\n      <Formik\n        initialValues={\n          {\n            name: '',\n            description: `${formatMessage({\n              id: 'Settings.roles.form.created',\n              defaultMessage: 'Created',\n            })} ${format(new Date(), 'PPP')}`,\n          } satisfies CreateRoleFormValues\n        }\n        onSubmit={handleCreateRoleSubmit}\n        validationSchema={CREATE_SCHEMA}\n        validateOnChange={false}\n      >\n        {({ values, errors, handleReset, handleChange, isSubmitting }) => (\n          <Form>\n            <>\n              <Layouts.Header\n                primaryAction={\n                  <Flex gap={2}>\n                    <Button\n                      variant=\"secondary\"\n                      onClick={() => {\n                        handleReset();\n                        permissionsRef.current?.resetForm();\n                      }}\n                    >\n                      {formatMessage({\n                        id: 'app.components.Button.reset',\n                        defaultMessage: 'Reset',\n                      })}\n                    </Button>\n                    <Button type=\"submit\" loading={isSubmitting} startIcon={<Check />}>\n                      {formatMessage({\n                        id: 'global.save',\n                        defaultMessage: 'Save',\n                      })}\n                    </Button>\n                  </Flex>\n                }\n                title={formatMessage({\n                  id: 'Settings.roles.create.title',\n                  defaultMessage: 'Create a role',\n                })}\n                subtitle={formatMessage({\n                  id: 'Settings.roles.create.description',\n                  defaultMessage: 'Define the rights given to the role',\n                })}\n                navigationAction={<BackButton fallback=\"../roles\" />}\n              />\n              <Layouts.Content>\n                <Flex direction=\"column\" alignItems=\"stretch\" gap={6}>\n                  <Box background=\"neutral0\" padding={6} shadow=\"filterShadow\" hasRadius>\n                    <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n                      <Flex justifyContent=\"space-between\">\n                        <Box>\n                          <Box>\n                            <Typography fontWeight=\"bold\">\n                              {formatMessage({\n                                id: 'global.details',\n                                defaultMessage: 'Details',\n                              })}\n                            </Typography>\n                          </Box>\n                          <Box>\n                            <Typography variant=\"pi\" textColor=\"neutral600\">\n                              {formatMessage({\n                                id: 'Settings.roles.form.description',\n                                defaultMessage: 'Name and description of the role',\n                              })}\n                            </Typography>\n                          </Box>\n                        </Box>\n                        <UsersRoleNumber>\n                          {formatMessage(\n                            {\n                              id: 'Settings.roles.form.button.users-with-role',\n                              defaultMessage:\n                                '{number, plural, =0 {# users} one {# user} other {# users}} with this role',\n                            },\n                            { number: 0 }\n                          )}\n                        </UsersRoleNumber>\n                      </Flex>\n                      <Grid.Root gap={4}>\n                        <Grid.Item col={6} direction=\"column\" alignItems=\"stretch\">\n                          <Field.Root\n                            name=\"name\"\n                            error={errors.name && formatMessage({ id: errors.name })}\n                            required\n                          >\n                            <Field.Label>\n                              {formatMessage({\n                                id: 'global.name',\n                                defaultMessage: 'Name',\n                              })}\n                            </Field.Label>\n                            <TextInput onChange={handleChange} value={values.name} />\n                            <Field.Error />\n                          </Field.Root>\n                        </Grid.Item>\n                        <Grid.Item col={6} direction=\"column\" alignItems=\"stretch\">\n                          <Field.Root\n                            name=\"description\"\n                            error={errors.description && formatMessage({ id: errors.description })}\n                          >\n                            <Field.Label>\n                              {formatMessage({\n                                id: 'global.description',\n                                defaultMessage: 'Description',\n                              })}\n                            </Field.Label>\n                            <Textarea onChange={handleChange} value={values.description} />\n                          </Field.Root>\n                        </Grid.Item>\n                      </Grid.Root>\n                    </Flex>\n                  </Box>\n                  <Box shadow=\"filterShadow\" hasRadius>\n                    <Permissions\n                      isFormDisabled={false}\n                      ref={permissionsRef}\n                      permissions={rolePermissions}\n                      layout={permissionsLayout}\n                    />\n                  </Box>\n                </Flex>\n              </Layouts.Content>\n            </>\n          </Form>\n        )}\n      </Formik>\n    </Main>\n  );\n};\n\nconst UsersRoleNumber = styled.div`\n  border: 1px solid ${({ theme }) => theme.colors.primary200};\n  background: ${({ theme }) => theme.colors.primary100};\n  padding: ${({ theme }) => `${theme.spaces[2]} ${theme.spaces[4]}`};\n  color: ${({ theme }) => theme.colors.primary600};\n  border-radius: ${({ theme }) => theme.borderRadius};\n  font-size: 1.2rem;\n  font-weight: bold;\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * ProtectedCreatePage\n * -----------------------------------------------------------------------------------------------*/\n\nconst ProtectedCreatePage = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.roles.create\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <CreatePage />\n    </Page.Protect>\n  );\n};\n\nexport { CreatePage, ProtectedCreatePage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CA,IAAMA,gBAAoBC,QAAM,EAAGC,MAAM;EACvCC,MAAUC,OAAM,EAAGC,SAASC,YAAiBD,SAASE,EAAE;EACxDC,aAAiBJ,OAAM,EAAGC,SAASC,YAAiBD,SAASE,EAAE;AACjE,CAAA;AAaC,IACKE,aAAa,MAAA;AACjB,QAAM,EAAEF,GAAE,IAAKG,UAAAA;AACf,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAMC,WAAWC,YAAAA;AACjB,QAAMC,iBAAuBC,aAAuB,IAAA;AACpD,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAM,EACJC,yBAAyBC,gBACzBC,iCAAiCC,uBAAsB,IACrDC,mBAAAA;AAEJ,QAAM,EAAEC,WAAWC,4BAA4BC,aAAaC,kBAAiB,IAC3EC,gCAAgC;;;;;IAK9BC,MAAMxB,MAAM;EACd,CAAA;AAMF,QAAM,EAAEqB,aAAaI,iBAAiBN,WAAWO,cAAa,IAAKC,2BACjE;IACE3B;KAEF;IACE4B,MAAM,CAAC5B;IACP6B,2BAA2B;EAC7B,CAAA;AAGF,QAAM,CAACC,UAAAA,IAAcC,sBAAAA;AACrB,QAAM,CAACC,qBAAAA,IAAyBC,iCAAAA;AAEhC,QAAMC,yBAAyB,OAC7BC,MACAC,WAAAA;;AAEA,QAAI;AACF,UAAIpC,IAAI;AACNY,mBAAW,mBAAA;aACN;AACLA,mBAAW,mBAAA;MACb;AAEA,YAAMyB,MAAM,MAAMP,WAAWK,IAAAA;AAE7B,UAAI,WAAWE,KAAK;AAClB,YAAIC,iBAAiBD,IAAIE,KAAK,KAAKF,IAAIE,MAAM3C,SAAS,mBAAmB;AACvEwC,iBAAOI,UAAUvB,uBAAuBoB,IAAIE,KAAK,CAAA;eAC5C;AACLnC,6BAAmB;YACjBqC,MAAM;YACNC,SAAS3B,eAAesB,IAAIE,KAAK;UACnC,CAAA;QACF;AAEA;MACF;AAEA,YAAM,EAAEI,kBAAiB,MAAKjC,oBAAekC,YAAflC,mBAAwBmC,qBAAoB,CAAA;AAE1E,UAAIR,IAAIF,KAAKnC,MAAM8C,MAAMC,QAAQJ,iBAAsBA,KAAAA,kBAAkBK,SAAS,GAAG;AACnF,cAAMC,YAAY,MAAMjB,sBAAsB;UAC5ChC,IAAIqC,IAAIF,KAAKnC;UACbkD,aAAaP;QACf,CAAA;AAEA,YAAI,WAAWM,WAAW;AACxB,cAAIX,iBAAiBW,UAAUV,KAAK,KAAKU,UAAUV,MAAM3C,SAAS,mBAAmB;AACnFwC,mBAAOI,UAAUvB,uBAAuBgC,UAAUV,KAAK,CAAA;iBAClD;AACLnC,+BAAmB;cACjBqC,MAAM;cACNC,SAAS3B,eAAekC,UAAUV,KAAK;YACzC,CAAA;UACF;AAEA;QACF;MACF;AAEAnC,yBAAmB;QACjBqC,MAAM;QACNC,SAASpC,cAAc;UAAEN,IAAI;UAA0BmD,gBAAgB;QAAU,CAAA;MACnF,CAAA;AAEA3C,eAAS,YAAY6B,IAAIF,KAAKnC,GAAGoD,SAAQ,CAAG,IAAG;QAAEC,SAAS;MAAK,CAAA;IACjE,SAASC,KAAK;AACZlD,yBAAmB;QACjBqC,MAAM;QACNC,SAASpC,cAAc;UAAEN,IAAI;UAAsBmD,gBAAgB;QAAoB,CAAA;MACzF,CAAA;IACF;EACF;AAEA,MAAI,8BAA+BzB,iBAAkB,CAACJ,mBAAmB;AACvE,eAAOiC,wBAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,aACEC,yBAACC,MAAAA;;UACCJ,wBAACC,KAAKI,OAAK;kBACRtD,cACC;UAAEN,IAAI;UAAsBmD,gBAAgB;WAC5C;UACEvD,MAAM;QACR,CAAA;;UAGJ2D,wBAACM,QAAAA;QACCC,eACE;UACElE,MAAM;UACNK,aAAa,GAAGK,cAAc;YAC5BN,IAAI;YACJmD,gBAAgB;UAClB,CAAA,CAAA,IAAMY,OAAO,oBAAIC,KAAAA,GAAQ,KAAA,CAAA;QAC3B;QAEFC,UAAU/B;QACVgC,kBAAkBzE;QAClB0E,kBAAkB;QAEjB,UAAA,CAAC,EAAEC,QAAQC,QAAQC,aAAaC,cAAcC,aAAY,UACzDjB,wBAACkB,MAAAA;wBACCf,yBAAAgB,6BAAA;;kBACEnB,wBAACoB,QAAQC,QAAM;gBACbC,mBACEnB,yBAACoB,MAAAA;kBAAKC,KAAK;;wBACTxB,wBAACyB,QAAAA;sBACCC,SAAQ;sBACRC,SAAS,MAAA;;AACPZ,oCAAAA;AACA5D,6CAAekC,YAAflC,mBAAwByE;sBAC1B;gCAEC7E,cAAc;wBACbN,IAAI;wBACJmD,gBAAgB;sBAClB,CAAA;;wBAEFI,wBAACyB,QAAAA;sBAAOvC,MAAK;sBAAS2C,SAASZ;sBAAca,eAAW9B,wBAAC+B,eAAAA,CAAAA,CAAAA;gCACtDhF,cAAc;wBACbN,IAAI;wBACJmD,gBAAgB;sBAClB,CAAA;;;;gBAINoC,OAAOjF,cAAc;kBACnBN,IAAI;kBACJmD,gBAAgB;gBAClB,CAAA;gBACAqC,UAAUlF,cAAc;kBACtBN,IAAI;kBACJmD,gBAAgB;gBAClB,CAAA;gBACAsC,sBAAkBlC,wBAACmC,YAAAA;kBAAWC,UAAS;;;kBAEzCpC,wBAACoB,QAAQiB,SAAO;gBACd,cAAAlC,yBAACoB,MAAAA;kBAAKe,WAAU;kBAASC,YAAW;kBAAUf,KAAK;;wBACjDxB,wBAACwC,KAAAA;sBAAIC,YAAW;sBAAWC,SAAS;sBAAGC,QAAO;sBAAeC,WAAS;sBACpE,cAAAzC,yBAACoB,MAAAA;wBAAKe,WAAU;wBAASC,YAAW;wBAAUf,KAAK;;8BACjDrB,yBAACoB,MAAAA;4BAAKsB,gBAAe;;kCACnB1C,yBAACqC,KAAAA;;sCACCxC,wBAACwC,KAAAA;oCACC,cAAAxC,wBAAC8C,YAAAA;sCAAWC,YAAW;gDACpBhG,cAAc;wCACbN,IAAI;wCACJmD,gBAAgB;sCAClB,CAAA;;;sCAGJI,wBAACwC,KAAAA;oCACC,cAAAxC,wBAAC8C,YAAAA;sCAAWpB,SAAQ;sCAAKsB,WAAU;gDAChCjG,cAAc;wCACbN,IAAI;wCACJmD,gBAAgB;sCAClB,CAAA;;;;;kCAINI,wBAACiD,iBAAAA;0CACElG,cACC;kCACEN,IAAI;kCACJmD,gBACE;mCAEJ;kCAAEsD,QAAQ;gCAAE,CAAA;;;;8BAIlB/C,yBAACgD,KAAKC,MAAI;4BAAC5B,KAAK;;kCACdxB,wBAACmD,KAAKE,MAAI;gCAACC,KAAK;gCAAGhB,WAAU;gCAASC,YAAW;8CAC/CpC,yBAACoD,MAAMH,MAAI;kCACT/G,MAAK;kCACL2C,OAAO8B,OAAOzE,QAAQU,cAAc;oCAAEN,IAAIqE,OAAOzE;kCAAK,CAAA;kCACtDE,UAAQ;;wCAERyD,wBAACuD,MAAMC,OAAK;gDACTzG,cAAc;wCACbN,IAAI;wCACJmD,gBAAgB;sCAClB,CAAA;;wCAEFI,wBAACyD,WAAAA;sCAAUC,UAAU1C;sCAAc2C,OAAO9C,OAAOxE;;wCACjD2D,wBAACuD,MAAMK,OAAK,CAAA,CAAA;;;;kCAGhB5D,wBAACmD,KAAKE,MAAI;gCAACC,KAAK;gCAAGhB,WAAU;gCAASC,YAAW;8CAC/CpC,yBAACoD,MAAMH,MAAI;kCACT/G,MAAK;kCACL2C,OAAO8B,OAAOpE,eAAeK,cAAc;oCAAEN,IAAIqE,OAAOpE;kCAAY,CAAA;;wCAEpEsD,wBAACuD,MAAMC,OAAK;gDACTzG,cAAc;wCACbN,IAAI;wCACJmD,gBAAgB;sCAClB,CAAA;;wCAEFI,wBAAC6D,UAAAA;sCAASH,UAAU1C;sCAAc2C,OAAO9C,OAAOnE;;;;;;;;;;wBAM1DsD,wBAACwC,KAAAA;sBAAIG,QAAO;sBAAeC,WAAS;sBAClC,cAAA5C,wBAAC8D,aAAAA;wBACCC,gBAAgB;wBAChBC,KAAK7G;wBACLwC,aAAazB;wBACb+F,QAAQlG;;;;;;;;;;;;AAW9B;AAEA,IAAMkF,kBAAkBiB,GAAOC;sBACT,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAOC,UAAU;gBAC5C,CAAC,EAAEF,MAAK,MAAOA,MAAMC,OAAOE,UAAU;aACzC,CAAC,EAAEH,MAAK,MAAO,GAAGA,MAAMI,OAAO,CAAA,CAAE,IAAIJ,MAAMI,OAAO,CAAE,CAAA,EAAE;WACxD,CAAC,EAAEJ,MAAK,MAAOA,MAAMC,OAAOI,UAAU;mBAC9B,CAAC,EAAEL,MAAK,MAAOA,MAAMM,YAAY;;;;AAO8C,IAE5FC,sBAAsB,MAAA;AAC1B,QAAMhF,cAAciF,iBAClB,CAACC,UAAUA;;AAAAA,uBAAMC,UAAUnF,YAAYoF,aAA5BF,mBAAsCG,MAAMC;GAAAA;AAGzD,aACEjF,wBAACC,KAAKiF,SAAO;IAACvF;IACZ,cAAAK,wBAACrD,YAAAA,CAAAA,CAAAA;;AAGP;", "names": ["CREATE_SCHEMA", "object", "shape", "name", "string", "required", "translatedErrors", "id", "description", "CreatePage", "useParams", "toggleNotification", "useNotification", "formatMessage", "useIntl", "navigate", "useNavigate", "permissionsRef", "useRef", "trackUsage", "useTracking", "_unstableFormatAPIError", "formatAPIError", "_unstableFormatValidationErrors", "formatValidationErrors", "useAPIErrorHandler", "isLoading", "isLoadingPermissionsLayout", "currentData", "permissionsLayout", "useGetRolePermissionLayoutQuery", "role", "rolePermissions", "isLoadingRole", "useGetRolePermissionsQuery", "skip", "refetchOnMountOrArgChange", "createRole", "useCreateRoleMutation", "updateRolePermissions", "useUpdateRolePermissionsMutation", "handleCreateRoleSubmit", "data", "formik", "res", "isBaseQueryError", "error", "setErrors", "type", "message", "permissionsToSend", "current", "getPermissions", "Array", "isArray", "length", "updateRes", "permissions", "defaultMessage", "toString", "replace", "err", "_jsx", "Page", "Loading", "_jsxs", "Main", "Title", "<PERSON><PERSON>", "initialValues", "format", "Date", "onSubmit", "validationSchema", "validateOnChange", "values", "errors", "handleReset", "handleChange", "isSubmitting", "Form", "_Fragment", "Layouts", "Header", "primaryAction", "Flex", "gap", "<PERSON><PERSON>", "variant", "onClick", "resetForm", "loading", "startIcon", "Check", "title", "subtitle", "navigationAction", "BackButton", "fallback", "Content", "direction", "alignItems", "Box", "background", "padding", "shadow", "hasRadius", "justifyContent", "Typography", "fontWeight", "textColor", "UsersRoleNumber", "number", "Grid", "Root", "<PERSON><PERSON>", "col", "Field", "Label", "TextInput", "onChange", "value", "Error", "Textarea", "Permissions", "isFormDisabled", "ref", "layout", "styled", "div", "theme", "colors", "primary200", "primary100", "spaces", "primary600", "borderRadius", "ProtectedCreatePage", "useTypedSelector", "state", "admin_app", "settings", "roles", "create", "Protect"]}