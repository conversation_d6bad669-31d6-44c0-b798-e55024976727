{"version": 3, "sources": ["../../../@strapi/admin/admin/src/components/RelativeTime.tsx", "../../../@strapi/admin/admin/src/pages/Settings/components/Tokens/Table.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { Duration, intervalToDuration, isPast } from 'date-fns';\nimport { useIntl } from 'react-intl';\n\nconst intervals: Array<keyof Duration> = ['years', 'months', 'days', 'hours', 'minutes', 'seconds'];\n\ninterface CustomInterval {\n  unit: keyof Duration;\n  text: string;\n  threshold: number;\n}\n\ninterface RelativeTimeProps extends React.ComponentPropsWithoutRef<'time'> {\n  timestamp: Date;\n  customIntervals?: CustomInterval[];\n}\n\n/**\n * Displays the relative time between a given timestamp and the current time.\n * You can display a custom message for given time intervals by passing an array of custom intervals.\n *\n * @example\n * ```jsx\n * <caption>Display \"last hour\" if the timestamp is less than an hour ago</caption>\n * <RelativeTime\n *  timestamp={new Date('2021-01-01')}\n *  customIntervals={[\n *   { unit: 'hours', threshold: 1, text: 'last hour' },\n *  ]}\n * ```\n */\nconst RelativeTime = React.forwardRef<HTMLTimeElement, RelativeTimeProps>(\n  ({ timestamp, customIntervals = [], ...restProps }, forwardedRef) => {\n    const { formatRelativeTime, formatDate, formatTime } = useIntl();\n\n    /**\n     * TODO: make this auto-update, like a clock.\n     */\n    const interval = intervalToDuration({\n      start: timestamp,\n      end: Date.now(),\n      // see https://github.com/date-fns/date-fns/issues/2891 – No idea why it's all partial it returns it every time.\n    }) as Required<Duration>;\n\n    const unit = intervals.find((intervalUnit) => {\n      return interval[intervalUnit] > 0 && Object.keys(interval).includes(intervalUnit);\n    })!;\n\n    const relativeTime = isPast(timestamp) ? -interval[unit] : interval[unit];\n\n    // Display custom text if interval is less than the threshold\n    const customInterval = customIntervals.find(\n      (custom) => interval[custom.unit] < custom.threshold\n    );\n\n    const displayText = customInterval\n      ? customInterval.text\n      : formatRelativeTime(relativeTime, unit, { numeric: 'auto' });\n\n    return (\n      <time\n        ref={forwardedRef}\n        dateTime={timestamp.toISOString()}\n        role=\"time\"\n        title={`${formatDate(timestamp)} ${formatTime(timestamp)}`}\n        {...restProps}\n      >\n        {displayText}\n      </time>\n    );\n  }\n);\n\nexport { RelativeTime };\nexport type { CustomInterval, RelativeTimeProps };\n", "import * as React from 'react';\n\nimport {\n  <PERSON>,\n  <PERSON>lex,\n  Icon<PERSON>utton,\n  <PERSON>po<PERSON>,\n  use<PERSON>ollator,\n  Dialog,\n  LinkButton,\n} from '@strapi/design-system';\nimport { Pencil, Trash } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { NavLink, useNavigate } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { ApiToken } from '../../../../../../shared/contracts/api-token';\nimport { SanitizedTransferToken } from '../../../../../../shared/contracts/transfer';\nimport { ConfirmDialog } from '../../../../components/ConfirmDialog';\nimport { RelativeTime } from '../../../../components/RelativeTime';\nimport { Table as TableImpl } from '../../../../components/Table';\nimport { useTracking } from '../../../../features/Tracking';\nimport { useQueryParams } from '../../../../hooks/useQueryParams';\n\nimport type { Data } from '@strapi/types';\n\n/* -------------------------------------------------------------------------------------------------\n * Table\n * -----------------------------------------------------------------------------------------------*/\n\ninterface TableProps\n  extends Pick<TableImpl.Props<SanitizedTransferToken | ApiToken>, 'headers' | 'isLoading'> {\n  onConfirmDelete: (id: Data.ID) => void;\n  permissions: {\n    canRead: boolean;\n    canDelete: boolean;\n    canUpdate: boolean;\n  };\n  tokens: SanitizedTransferToken[] | ApiToken[];\n  tokenType: 'api-token' | 'transfer-token';\n}\n\nconst Table = ({\n  permissions,\n  headers = [],\n  isLoading = false,\n  tokens = [],\n  onConfirmDelete,\n  tokenType,\n}: TableProps) => {\n  const [{ query }] = useQueryParams<{ sort?: string }>();\n  const { formatMessage, locale } = useIntl();\n  const [, sortOrder] = query && query.sort ? query.sort.split(':') : [undefined, 'ASC'];\n  const navigate = useNavigate();\n  const { trackUsage } = useTracking();\n  const formatter = useCollator(locale);\n\n  const sortedTokens = [...tokens].sort((a, b) => {\n    return sortOrder === 'DESC'\n      ? formatter.compare(b.name, a.name)\n      : formatter.compare(a.name, b.name);\n  });\n\n  const { canDelete, canUpdate, canRead } = permissions;\n\n  const handleRowClick = (id: Data.ID) => () => {\n    if (canRead) {\n      trackUsage('willEditTokenFromList', {\n        tokenType,\n      });\n      navigate(id.toString());\n    }\n  };\n\n  return (\n    <TableImpl.Root headers={headers} rows={sortedTokens} isLoading={isLoading}>\n      <TableImpl.Content>\n        <TableImpl.Head>\n          {headers.map((header) => (\n            <TableImpl.HeaderCell key={header.name} {...header} />\n          ))}\n        </TableImpl.Head>\n        <TableImpl.Empty />\n        <TableImpl.Loading />\n        <TableImpl.Body>\n          {sortedTokens.map((token) => (\n            <TableImpl.Row key={token.id} onClick={handleRowClick(token.id)}>\n              <TableImpl.Cell maxWidth=\"25rem\">\n                <Typography textColor=\"neutral800\" fontWeight=\"bold\" ellipsis>\n                  {token.name}\n                </Typography>\n              </TableImpl.Cell>\n              <TableImpl.Cell maxWidth=\"25rem\">\n                <Typography textColor=\"neutral800\" ellipsis>\n                  {token.description}\n                </Typography>\n              </TableImpl.Cell>\n              <TableImpl.Cell>\n                <Typography textColor=\"neutral800\">\n                  {/* @ts-expect-error One of the tokens doesn't have createdAt */}\n                  <RelativeTime timestamp={new Date(token.createdAt)} />\n                </Typography>\n              </TableImpl.Cell>\n              <TableImpl.Cell>\n                {token.lastUsedAt && (\n                  <Typography textColor=\"neutral800\">\n                    <RelativeTime\n                      timestamp={new Date(token.lastUsedAt)}\n                      customIntervals={[\n                        {\n                          unit: 'hours',\n                          threshold: 1,\n                          text: formatMessage({\n                            id: 'Settings.apiTokens.lastHour',\n                            defaultMessage: 'last hour',\n                          }),\n                        },\n                      ]}\n                    />\n                  </Typography>\n                )}\n              </TableImpl.Cell>\n              {canUpdate || canRead || canDelete ? (\n                <TableImpl.Cell>\n                  <Flex justifyContent=\"end\">\n                    {canUpdate && <UpdateButton tokenName={token.name} tokenId={token.id} />}\n                    {canDelete && (\n                      <DeleteButton\n                        tokenName={token.name}\n                        onClickDelete={() => onConfirmDelete?.(token.id)}\n                        tokenType={tokenType}\n                      />\n                    )}\n                  </Flex>\n                </TableImpl.Cell>\n              ) : null}\n            </TableImpl.Row>\n          ))}\n        </TableImpl.Body>\n      </TableImpl.Content>\n    </TableImpl.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * CRUD Buttons\n * -----------------------------------------------------------------------------------------------*/\n\nconst MESSAGES_MAP = {\n  edit: {\n    id: 'app.component.table.edit',\n    defaultMessage: 'Edit {target}',\n  },\n  read: {\n    id: 'app.component.table.read',\n    defaultMessage: 'Read {target}',\n  },\n};\n\ninterface DefaultButtonProps {\n  tokenName: string;\n  tokenId: Data.ID;\n  buttonType?: 'edit' | 'read';\n  children: React.ReactNode;\n}\n\nconst DefaultButton = ({\n  tokenName,\n  tokenId,\n  buttonType = 'edit',\n  children,\n}: DefaultButtonProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <LinkButtonStyled\n      tag={NavLink}\n      to={tokenId.toString()}\n      onClick={(e: React.MouseEvent) => e.stopPropagation()}\n      title={formatMessage(MESSAGES_MAP[buttonType], { target: tokenName })}\n      variant=\"ghost\"\n      size=\"S\"\n    >\n      {children}\n    </LinkButtonStyled>\n  );\n};\n\nconst LinkButtonStyled = styled(LinkButton)`\n  padding: 0.7rem;\n\n  & > span {\n    display: flex;\n  }\n`;\n\ninterface DeleteButtonProps extends Pick<ButtonProps, 'tokenName'>, Pick<TableProps, 'tokenType'> {\n  onClickDelete: () => void;\n}\n\nconst DeleteButton = ({ tokenName, onClickDelete, tokenType }: DeleteButtonProps) => {\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n  const handleClickDelete = () => {\n    trackUsage('willDeleteToken', {\n      tokenType,\n    });\n    onClickDelete();\n  };\n\n  return (\n    <Dialog.Root>\n      <Box<'div'> paddingLeft={1} onClick={(e) => e.stopPropagation()}>\n        <Dialog.Trigger>\n          <IconButton\n            label={formatMessage(\n              {\n                id: 'global.delete-target',\n                defaultMessage: 'Delete {target}',\n              },\n              { target: `${tokenName}` }\n            )}\n            name=\"delete\"\n            variant=\"ghost\"\n          >\n            <Trash />\n          </IconButton>\n        </Dialog.Trigger>\n        <ConfirmDialog onConfirm={handleClickDelete} />\n      </Box>\n    </Dialog.Root>\n  );\n};\n\ninterface ButtonProps {\n  tokenName: string;\n  tokenId: Data.ID;\n}\n\nconst UpdateButton = ({ tokenName, tokenId }: ButtonProps) => {\n  return (\n    <DefaultButton tokenName={tokenName} tokenId={tokenId}>\n      <Pencil />\n    </DefaultButton>\n  );\n};\n\nexport { Table };\nexport type { TableProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,IAAMA,YAAmC;EAAC;EAAS;EAAU;EAAQ;EAAS;EAAW;AAAU;AA2B7FC,IAAAA,eAAqBC,iBACzB,CAAC,EAAEC,WAAWC,kBAAkB,CAAA,GAAI,GAAGC,UAAAA,GAAaC,iBAAAA;AAClD,QAAM,EAAEC,oBAAoBC,YAAYC,WAAU,IAAKC,QAAAA;AAKvD,QAAMC,WAAWC,mBAAmB;IAClCC,OAAOV;IACPW,KAAKC,KAAKC,IAAG;EAEf,CAAA;AAEA,QAAMC,OAAOjB,UAAUkB,KAAK,CAACC,iBAAAA;AAC3B,WAAOR,SAASQ,YAAAA,IAAgB,KAAKC,OAAOC,KAAKV,QAAUW,EAAAA,SAASH,YAAAA;EACtE,CAAA;AAEA,QAAMI,eAAeC,OAAOrB,SAAAA,IAAa,CAACQ,SAASM,IAAK,IAAGN,SAASM,IAAK;AAGzE,QAAMQ,iBAAiBrB,gBAAgBc,KACrC,CAACQ,WAAWf,SAASe,OAAOT,IAAI,IAAIS,OAAOC,SAAS;AAGtD,QAAMC,cAAcH,iBAChBA,eAAeI,OACftB,mBAAmBgB,cAAcN,MAAM;IAAEa,SAAS;EAAO,CAAA;AAE7D,aACEC,wBAACC,QAAAA;IACCC,KAAK3B;IACL4B,UAAU/B,UAAUgC,YAAW;IAC/BC,MAAK;IACLC,OAAO,GAAG7B,WAAWL,SAAAA,CAAAA,IAAcM,WAAWN,SAAAA,CAAAA;IAC7C,GAAGE;IAEHuB,UAAAA;;AAGP,CAAA;;;AC7BF,IAAMU,SAAQ,CAAC,EACbC,aACAC,UAAU,CAAA,GACVC,YAAY,OACZC,SAAS,CAAA,GACTC,iBACAC,UAAS,MACE;AACX,QAAM,CAAC,EAAEC,MAAK,CAAE,IAAIC,eAAAA;AACpB,QAAM,EAAEC,eAAeC,OAAM,IAAKC,QAAAA;AAClC,QAAM,CAAA,EAAGC,SAAAA,IAAaL,SAASA,MAAMM,OAAON,MAAMM,KAAKC,MAAM,GAAO,IAAA;IAACC;IAAW;EAAM;AACtF,QAAMC,WAAWC,YAAAA;AACjB,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAMC,YAAYC,YAAYX,MAAAA;AAE9B,QAAMY,eAAe;IAAIlB,GAAAA;IAAQS,KAAK,CAACU,GAAGC,MAAAA;AACxC,WAAOZ,cAAc,SACjBQ,UAAUK,QAAQD,EAAEE,MAAMH,EAAEG,IAAI,IAChCN,UAAUK,QAAQF,EAAEG,MAAMF,EAAEE,IAAI;EACtC,CAAA;AAEA,QAAM,EAAEC,WAAWC,WAAWC,QAAO,IAAK5B;AAE1C,QAAM6B,iBAAiB,CAACC,OAAgB,MAAA;AACtC,QAAIF,SAAS;AACXX,iBAAW,yBAAyB;QAClCZ;MACF,CAAA;AACAU,eAASe,GAAGC,SAAQ,CAAA;IACtB;EACF;AAEA,aACEC,yBAACC,MAAUC,MAAI;IAACjC;IAAkBkC,MAAMd;IAAcnB;kBACpDkC,0BAACH,MAAUI,SAAO;;YAChBL,yBAACC,MAAUK,MAAI;UACZrC,UAAAA,QAAQsC,IAAI,CAACC,eACZR,yBAACC,MAAUQ,YAAU;YAAoB,GAAGD;UAAjBA,GAAAA,OAAOf,IAAI,CAAA;;YAG1CO,yBAACC,MAAUS,OAAK,CAAA,CAAA;YAChBV,yBAACC,MAAUU,SAAO,CAAA,CAAA;YAClBX,yBAACC,MAAUW,MAAI;UACZvB,UAAAA,aAAakB,IAAI,CAACM,cACjBT,0BAACH,MAAUa,KAAG;YAAgBC,SAASlB,eAAegB,MAAMf,EAAE;;kBAC5DE,yBAACC,MAAUe,MAAI;gBAACC,UAAS;gBACvB,cAAAjB,yBAACkB,YAAAA;kBAAWC,WAAU;kBAAaC,YAAW;kBAAOC,UAAQ;kBAC1DR,UAAAA,MAAMpB;;;kBAGXO,yBAACC,MAAUe,MAAI;gBAACC,UAAS;gBACvB,cAAAjB,yBAACkB,YAAAA;kBAAWC,WAAU;kBAAaE,UAAQ;kBACxCR,UAAAA,MAAMS;;;kBAGXtB,yBAACC,MAAUe,MAAI;gBACb,cAAAhB,yBAACkB,YAAAA;kBAAWC,WAAU;kBAEpB,cAAAnB,yBAACuB,cAAAA;oBAAaC,WAAW,IAAIC,KAAKZ,MAAMa,SAAS;;;;kBAGrD1B,yBAACC,MAAUe,MAAI;0BACZH,MAAMc,kBACL3B,yBAACkB,YAAAA;kBAAWC,WAAU;kBACpB,cAAAnB,yBAACuB,cAAAA;oBACCC,WAAW,IAAIC,KAAKZ,MAAMc,UAAU;oBACpCC,iBAAiB;sBACf;wBACEC,MAAM;wBACNC,WAAW;wBACXC,MAAMvD,cAAc;0BAClBsB,IAAI;0BACJkC,gBAAgB;wBAClB,CAAA;sBACF;oBACD;;;;cAKRrC,aAAaC,WAAWF,gBACvBM,yBAACC,MAAUe,MAAI;gBACb,cAAAZ,0BAAC6B,MAAAA;kBAAKC,gBAAe;;oBAClBvC,iBAAaK,yBAACmC,cAAAA;sBAAaC,WAAWvB,MAAMpB;sBAAM4C,SAASxB,MAAMf;;oBACjEJ,iBACCM,yBAACsC,cAAAA;sBACCF,WAAWvB,MAAMpB;sBACjB8C,eAAe,MAAMnE,mDAAkByC,MAAMf;sBAC7CzB;;;;cAKN,CAAA,IAAA;;UAjDcwC,GAAAA,MAAMf,EAAE,CAAA;;;;;AAwDxC;AAMA,IAAM0C,eAAe;EACnBC,MAAM;IACJ3C,IAAI;IACJkC,gBAAgB;EAClB;EACAU,MAAM;IACJ5C,IAAI;IACJkC,gBAAgB;EAClB;AACF;AASA,IAAMW,gBAAgB,CAAC,EACrBP,WACAC,SACAO,aAAa,QACbC,SAAQ,MACW;AACnB,QAAM,EAAErE,cAAa,IAAKE,QAAAA;AAE1B,aACEsB,yBAAC8C,kBAAAA;IACCC,KAAKC;IACLC,IAAIZ,QAAQtC,SAAQ;IACpBgB,SAAS,CAACmC,MAAwBA,EAAEC,gBAAe;IACnDC,OAAO5E,cAAcgE,aAAaI,UAAAA,GAAa;MAAES,QAAQjB;IAAU,CAAA;IACnEkB,SAAQ;IACRC,MAAK;IAEJV;;AAGP;AAEA,IAAMC,mBAAmBU,GAAOC,UAAAA;;;;;;;AAYhC,IAAMnB,eAAe,CAAC,EAAEF,WAAWG,eAAelE,UAAS,MAAqB;AAC9E,QAAM,EAAEG,cAAa,IAAKE,QAAAA;AAC1B,QAAM,EAAEO,WAAU,IAAKC,YAAAA;AACvB,QAAMwE,oBAAoB,MAAA;AACxBzE,eAAW,mBAAmB;MAC5BZ;IACF,CAAA;AACAkE,kBAAAA;EACF;AAEA,aACEvC,yBAAC2D,OAAOzD,MAAI;IACV,cAAAE,0BAACwD,KAAAA;MAAWC,aAAa;MAAG9C,SAAS,CAACmC,MAAMA,EAAEC,gBAAe;;YAC3DnD,yBAAC2D,OAAOG,SAAO;UACb,cAAA9D,yBAAC+D,YAAAA;YACCC,OAAOxF,cACL;cACEsB,IAAI;cACJkC,gBAAgB;eAElB;cAAEqB,QAAQ,GAAGjB,SAAAA;YAAY,CAAA;YAE3B3C,MAAK;YACL6D,SAAQ;YAER,cAAAtD,yBAACiE,cAAAA,CAAAA,CAAAA;;;YAGLjE,yBAACkE,eAAAA;UAAcC,WAAWT;;;;;AAIlC;AAOA,IAAMvB,eAAe,CAAC,EAAEC,WAAWC,QAAO,MAAe;AACvD,aACErC,yBAAC2C,eAAAA;IAAcP;IAAsBC;IACnC,cAAArC,yBAACoE,eAAAA,CAAAA,CAAAA;;AAGP;", "names": ["intervals", "RelativeTime", "forwardRef", "timestamp", "customIntervals", "restProps", "forwardedRef", "formatRelativeTime", "formatDate", "formatTime", "useIntl", "interval", "intervalToDuration", "start", "end", "Date", "now", "unit", "find", "intervalUnit", "Object", "keys", "includes", "relativeTime", "isPast", "customInterval", "custom", "threshold", "displayText", "text", "numeric", "_jsx", "time", "ref", "dateTime", "toISOString", "role", "title", "Table", "permissions", "headers", "isLoading", "tokens", "onConfirmDelete", "tokenType", "query", "useQueryParams", "formatMessage", "locale", "useIntl", "sortOrder", "sort", "split", "undefined", "navigate", "useNavigate", "trackUsage", "useTracking", "formatter", "useCollator", "sortedTokens", "a", "b", "compare", "name", "canDelete", "canUpdate", "canRead", "handleRowClick", "id", "toString", "_jsx", "TableImpl", "Root", "rows", "_jsxs", "Content", "Head", "map", "header", "<PERSON><PERSON><PERSON><PERSON>", "Empty", "Loading", "Body", "token", "Row", "onClick", "Cell", "max<PERSON><PERSON><PERSON>", "Typography", "textColor", "fontWeight", "ellipsis", "description", "RelativeTime", "timestamp", "Date", "createdAt", "lastUsedAt", "customIntervals", "unit", "threshold", "text", "defaultMessage", "Flex", "justifyContent", "UpdateButton", "tokenName", "tokenId", "DeleteButton", "onClickDelete", "MESSAGES_MAP", "edit", "read", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buttonType", "children", "LinkButtonStyled", "tag", "NavLink", "to", "e", "stopPropagation", "title", "target", "variant", "size", "styled", "LinkButton", "handleClickDelete", "Dialog", "Box", "paddingLeft", "<PERSON><PERSON>", "IconButton", "label", "Trash", "ConfirmDialog", "onConfirm", "Pencil"]}