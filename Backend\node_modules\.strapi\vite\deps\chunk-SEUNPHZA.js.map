{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/modules/hooks.ts"], "sourcesContent": ["import { Dispatch } from '@reduxjs/toolkit';\nimport { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';\n\nimport { State } from './reducers';\n\nimport type { Store } from '@strapi/admin/strapi-admin';\n\ntype RootState = ReturnType<Store['getState']> & {\n  ['content-manager']: State;\n};\n\nconst useTypedDispatch: () => Dispatch = useDispatch;\nconst useTypedSelector: TypedUseSelectorHook<RootState> = useSelector;\n\nexport { useTypedSelector, useTypedDispatch };\n"], "mappings": ";;;;;;AAWA,IAAMA,mBAAmCC;AACzC,IAAMC,mBAAoDC;", "names": ["useTypedDispatch", "useDispatch", "useTypedSelector", "useSelector"]}