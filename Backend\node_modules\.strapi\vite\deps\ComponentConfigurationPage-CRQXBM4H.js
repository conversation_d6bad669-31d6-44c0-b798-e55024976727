import {
  ConfigurationForm,
  TEMP_FIELD_NAME
} from "./chunk-X73AXI2G.js";
import "./chunk-RTLULQCG.js";
import {
  useTypedSelector
} from "./chunk-SEUNPHZA.js";
import "./chunk-25ZR4HQ4.js";
import "./chunk-QBVVXK5Q.js";
import {
  DEFAULT_SETTINGS,
  convertEditLayoutToFieldLayouts,
  extractContentTypeComponents,
  setIn,
  useGetInitialDataQuery
} from "./chunk-GZO3GFLN.js";
import {
  contentManagerApi
} from "./chunk-A46TUCLT.js";
import "./chunk-HIZVCZYI.js";
import "./chunk-EKXSMIUH.js";
import "./chunk-YV3ONWF5.js";
import "./chunk-UMW22TSS.js";
import "./chunk-4QC3H4VA.js";
import "./chunk-75D2ZJP5.js";
import "./chunk-VCTAT6B3.js";
import "./chunk-ROZIXYJG.js";
import "./chunk-C72RZIDJ.js";
import "./chunk-HZKRK7AR.js";
import "./chunk-LRN6A2UC.js";
import "./chunk-D2TGW5YS.js";
import "./chunk-M27D4U76.js";
import "./chunk-HX66WGOY.js";
import "./chunk-Y4UEUAII.js";
import "./chunk-BN2UQHMJ.js";
import "./chunk-NWWGC2Z2.js";
import "./chunk-MBK4V2X7.js";
import "./chunk-DY2RJG3P.js";
import "./chunk-K65KIEAL.js";
import "./chunk-BUDFB33L.js";
import "./chunk-7MILHJ3J.js";
import "./chunk-SGQJOZK5.js";
import "./chunk-AFM2NWPO.js";
import "./chunk-DUGZ4WIW.js";
import "./chunk-IFOFBKTA.js";
import "./chunk-376QHLWZ.js";
import "./chunk-EGNP2T5O.js";
import "./chunk-XDCEA27D.js";
import "./chunk-EZSYDDUK.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-QIJGNK42.js";
import "./chunk-CJHUGFLE.js";
import "./chunk-IQGHPIIW.js";
import "./chunk-DWSGKQEK.js";
import "./chunk-W6ZGCRX6.js";
import "./chunk-PVCRV2LE.js";
import "./chunk-HWAQQGJJ.js";
import "./chunk-L5JCPKMP.js";
import "./chunk-ZJEMJY2Q.js";
import "./chunk-6DRYEU2W.js";
import "./chunk-MTTHLNPH.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import {
  Page,
  useAPIErrorHandler
} from "./chunk-7LKLOY7A.js";
import "./chunk-ODQFI753.js";
import "./chunk-ZOP4VV6J.js";
import "./chunk-WH6VCVXU.js";
import "./chunk-IL5G2D22.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-76QM3EFM.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  useIntl
} from "./chunk-7GC3Y62Q.js";
import "./chunk-5ZC4PE57.js";
import {
  useParams
} from "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/dist/admin/pages/ComponentConfigurationPage.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);

// node_modules/@strapi/content-manager/dist/admin/services/components.mjs
var componentsApi = contentManagerApi.injectEndpoints({
  endpoints: (builder) => ({
    getComponentConfiguration: builder.query({
      query: (uid) => `/content-manager/components/${uid}/configuration`,
      transformResponse: (response) => response.data,
      providesTags: (_result, _error, uid) => [
        {
          type: "ComponentConfiguration",
          id: uid
        }
      ]
    }),
    updateComponentConfiguration: builder.mutation({
      query: ({ uid, ...body }) => ({
        url: `/content-manager/components/${uid}/configuration`,
        method: "PUT",
        data: body
      }),
      transformResponse: (response) => response.data,
      invalidatesTags: (_result, _error, { uid }) => [
        {
          type: "ComponentConfiguration",
          id: uid
        },
        // otherwise layouts already fetched will have stale component configuration data.
        {
          type: "ContentTypeSettings",
          id: "LIST"
        }
      ]
    })
  })
});
var { useGetComponentConfigurationQuery, useUpdateComponentConfigurationMutation } = componentsApi;

// node_modules/@strapi/content-manager/dist/admin/pages/ComponentConfigurationPage.mjs
var ComponentConfigurationPage = () => {
  const { slug: model } = useParams();
  const { toggleNotification } = useNotification();
  const { formatMessage } = useIntl();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const { components, fieldSizes, schema, error: errorSchema, isLoading: isLoadingSchema, isFetching: isFetchingSchema } = useGetInitialDataQuery(void 0, {
    selectFromResult: (res) => {
      var _a, _b, _c;
      const schema2 = (_a = res.data) == null ? void 0 : _a.components.find((ct) => ct.uid === model);
      const componentsByKey = (_b = res.data) == null ? void 0 : _b.components.reduce((acc, component) => {
        acc[component.uid] = component;
        return acc;
      }, {});
      const components2 = extractContentTypeComponents(schema2 == null ? void 0 : schema2.attributes, componentsByKey);
      const fieldSizes2 = Object.entries(((_c = res.data) == null ? void 0 : _c.fieldSizes) ?? {}).reduce((acc, [attributeName, { default: size }]) => {
        acc[attributeName] = size;
        return acc;
      }, {});
      return {
        isFetching: res.isFetching,
        isLoading: res.isLoading,
        error: res.error,
        components: components2,
        schema: schema2,
        fieldSizes: fieldSizes2
      };
    }
  });
  React.useEffect(() => {
    if (errorSchema) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(errorSchema)
      });
    }
  }, [
    errorSchema,
    formatAPIError,
    toggleNotification
  ]);
  const { data, isLoading: isLoadingConfig, isFetching: isFetchingConfig, error } = useGetComponentConfigurationQuery(model ?? "");
  React.useEffect(() => {
    if (error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    }
  }, [
    error,
    formatAPIError,
    toggleNotification
  ]);
  const isLoading = isLoadingConfig || isLoadingSchema || isFetchingConfig || isFetchingSchema;
  const editLayout = React.useMemo(() => data && !isLoading ? formatEditLayout(data, {
    schema,
    components
  }) : {
    layout: [],
    components: {},
    metadatas: {},
    options: {},
    settings: DEFAULT_SETTINGS
  }, [
    data,
    isLoading,
    schema,
    components
  ]);
  const [updateConfiguration] = useUpdateComponentConfigurationMutation();
  const handleSubmit = async (formData) => {
    try {
      const meta = Object.entries((data == null ? void 0 : data.component.metadatas) ?? {}).reduce((acc, [name, { edit, list }]) => {
        const { __temp_key__, size: _size, name: _name, ...editedMetadata } = formData.layout.flatMap((row) => row.children).find((field) => field.name === name) ?? {};
        acc[name] = {
          edit: {
            ...edit,
            ...editedMetadata
          },
          list
        };
        return acc;
      }, {});
      const res = await updateConfiguration({
        layouts: {
          edit: formData.layout.map((row) => row.children.reduce((acc, { name, size }) => {
            if (name !== TEMP_FIELD_NAME) {
              return [
                ...acc,
                {
                  name,
                  size
                }
              ];
            }
            return acc;
          }, [])),
          list: data == null ? void 0 : data.component.layouts.list
        },
        settings: setIn(formData.settings, "displayName", void 0),
        metadatas: meta,
        uid: model
      });
      if ("data" in res) {
        toggleNotification({
          type: "success",
          message: formatMessage({
            id: "notification.success.saved",
            defaultMessage: "Saved"
          })
        });
      } else {
        toggleNotification({
          type: "danger",
          message: formatAPIError(res.error)
        });
      }
    } catch {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "An error occurred"
        })
      });
    }
  };
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  if (error || errorSchema || !schema) {
    return (0, import_jsx_runtime.jsx)(Page.Error, {});
  }
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, {
    children: [
      (0, import_jsx_runtime.jsx)(Page.Title, {
        children: `Configure ${editLayout.settings.displayName} Edit View`
      }),
      (0, import_jsx_runtime.jsx)(ConfigurationForm, {
        onSubmit: handleSubmit,
        attributes: schema.attributes,
        fieldSizes,
        layout: editLayout
      })
    ]
  });
};
var formatEditLayout = (data, { schema, components }) => {
  const editAttributes = convertEditLayoutToFieldLayouts(data.component.layouts.edit, schema == null ? void 0 : schema.attributes, data.component.metadatas, {
    configurations: data.components,
    schemas: components
  });
  const componentEditAttributes = Object.entries(data.components).reduce((acc, [uid, configuration]) => {
    acc[uid] = {
      layout: convertEditLayoutToFieldLayouts(configuration.layouts.edit, components[uid].attributes, configuration.metadatas),
      settings: {
        ...configuration.settings,
        icon: components[uid].info.icon,
        displayName: components[uid].info.displayName
      }
    };
    return acc;
  }, {});
  const editMetadatas = Object.entries(data.component.metadatas).reduce((acc, [attribute, metadata]) => {
    return {
      ...acc,
      [attribute]: metadata.edit
    };
  }, {});
  return {
    layout: [
      editAttributes
    ],
    components: componentEditAttributes,
    metadatas: editMetadatas,
    options: {
      ...schema == null ? void 0 : schema.options,
      ...schema == null ? void 0 : schema.pluginOptions
    },
    settings: {
      ...data.component.settings,
      displayName: schema == null ? void 0 : schema.info.displayName
    }
  };
};
var ProtectedComponentConfigurationPage = () => {
  const permissions = useTypedSelector((state) => {
    var _a;
    return (_a = state.admin_app.permissions.contentManager) == null ? void 0 : _a.componentsConfigurations;
  });
  return (0, import_jsx_runtime.jsx)(Page.Protect, {
    permissions,
    children: (0, import_jsx_runtime.jsx)(ComponentConfigurationPage, {})
  });
};
export {
  ComponentConfigurationPage,
  ProtectedComponentConfigurationPage
};
//# sourceMappingURL=ComponentConfigurationPage-CRQXBM4H.js.map
