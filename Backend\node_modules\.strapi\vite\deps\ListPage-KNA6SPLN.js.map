{"version": 3, "sources": ["../../../@strapi/admin/ee/admin/src/services/auditLogs.ts", "../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/AuditLogs/hooks/useFormatTimeStamp.ts", "../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/AuditLogs/utils/getActionTypesDefaultMessages.ts", "../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/AuditLogs/components/Modal.tsx", "../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/AuditLogs/hooks/useAuditLogsData.ts", "../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/AuditLogs/components/ComboboxFilter.tsx", "../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/AuditLogs/utils/getDisplayedFilters.ts", "../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/AuditLogs/ListPage.tsx"], "sourcesContent": ["import { adminApi } from '../../../../admin/src/services/api';\nimport * as AuditLogs from '../../../../shared/contracts/audit-logs';\n\nconst auditLogsService = adminApi.injectEndpoints({\n  endpoints: (builder) => ({\n    getAuditLogs: builder.query<AuditLogs.GetAll.Response, AuditLogs.GetAll.Request['query']>({\n      query: (params) => ({\n        url: `/admin/audit-logs`,\n        config: {\n          params,\n        },\n      }),\n    }),\n    getAuditLog: builder.query<AuditLogs.Get.Response, AuditLogs.Get.Params['id']>({\n      query: (id) => `/admin/audit-logs/${id}`,\n    }),\n  }),\n  overrideExisting: false,\n});\n\nconst { useGetAuditLogsQuery, useGetAuditLogQuery } = auditLogsService;\n\nexport { useGetAuditLogsQuery, useGetAuditLogQuery };\n", "import parseISO from 'date-fns/parseISO';\nimport { useIntl } from 'react-intl';\n\nexport const useFormatTimeStamp = () => {\n  const { formatDate } = useIntl();\n\n  const formatTimeStamp = (value: string) => {\n    const date = parseISO(value);\n\n    const formattedDate = formatDate(date, {\n      dateStyle: 'long',\n    });\n    const formattedTime = formatDate(date, {\n      timeStyle: 'medium',\n      hourCycle: 'h24',\n    });\n\n    return `${formattedDate}, ${formattedTime}`;\n  };\n\n  return formatTimeStamp;\n};\n", "export const actionTypes = {\n  'entry.create': 'Create entry{model, select, undefined {} other { ({model})}}',\n  'entry.update': 'Update entry{model, select, undefined {} other { ({model})}}',\n  'entry.delete': 'Delete entry{model, select, undefined {} other { ({model})}}',\n  'entry.publish': 'Publish entry{model, select, undefined {} other { ({model})}}',\n  'entry.unpublish': 'Unpublish entry{model, select, undefined {} other { ({model})}}',\n  'media.create': 'Create media',\n  'media.update': 'Update media',\n  'media.delete': 'Delete media',\n  'media-folder.create': 'Create media folder',\n  'media-folder.update': 'Update media folder',\n  'media-folder.delete': 'Delete media folder',\n  'user.create': 'Create user',\n  'user.update': 'Update user',\n  'user.delete': 'Delete user',\n  'admin.auth.success': 'Admin login',\n  'admin.logout': 'Admin logout',\n  'content-type.create': 'Create content type',\n  'content-type.update': 'Update content type',\n  'content-type.delete': 'Delete content type',\n  'component.create': 'Create component',\n  'component.update': 'Update component',\n  'component.delete': 'Delete component',\n  'role.create': 'Create role',\n  'role.update': 'Update role',\n  'role.delete': 'Delete role',\n  'permission.create': 'Create permission',\n  'permission.update': 'Update permission',\n  'permission.delete': 'Delete permission',\n};\n\nexport const getDefaultMessage = (value: keyof typeof actionTypes) => {\n  return actionTypes[value] || value;\n};\n", "import * as React from 'react';\n\nimport {\n  Box,\n  Flex,\n  Grid,\n  JSONInput,\n  Loader,\n  Modal as DSModal,\n  Typography,\n  Breadcrumbs,\n  Crumb,\n  Field,\n} from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { useNotification } from '../../../../../../../../admin/src/features/Notifications';\nimport { useAPIErrorHandler } from '../../../../../../../../admin/src/hooks/useAPIErrorHandler';\nimport { AuditLog } from '../../../../../../../../shared/contracts/audit-logs';\nimport { useGetAuditLogQuery } from '../../../../../services/auditLogs';\nimport { useFormatTimeStamp } from '../hooks/useFormatTimeStamp';\nimport { actionTypes, getDefaultMessage } from '../utils/getActionTypesDefaultMessages';\n\ninterface ModalProps {\n  handleClose: () => void;\n  logId: string;\n}\n\nexport const Modal = ({ handleClose, logId }: ModalProps) => {\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n\n  const { data, error, isLoading } = useGetAuditLogQuery(logId);\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n      handleClose();\n    }\n  }, [error, formatAPIError, handleClose, toggleNotification]);\n\n  const formatTimeStamp = useFormatTimeStamp();\n  const formattedDate = data && 'date' in data ? formatTimeStamp(data.date) : '';\n\n  return (\n    <DSModal.Root defaultOpen onOpenChange={handleClose}>\n      <DSModal.Content>\n        <DSModal.Header>\n          {/**\n           * TODO: this is not semantically correct and should be amended.\n           */}\n          <Breadcrumbs label={formattedDate} id=\"title\">\n            <Crumb isCurrent>{formattedDate}</Crumb>\n          </Breadcrumbs>\n        </DSModal.Header>\n        <DSModal.Body>\n          <ActionBody isLoading={isLoading} data={data as AuditLog} formattedDate={formattedDate} />\n        </DSModal.Body>\n      </DSModal.Content>\n    </DSModal.Root>\n  );\n};\n\ninterface ActionBodyProps {\n  isLoading?: boolean;\n  data: AuditLog;\n  formattedDate: string;\n}\n\nconst ActionBody = ({ isLoading, data, formattedDate }: ActionBodyProps) => {\n  const { formatMessage } = useIntl();\n\n  if (isLoading) {\n    return (\n      <Flex padding={7} justifyContent=\"center\" alignItems=\"center\">\n        {/**\n         * TODO: this will need to be translated.\n         */}\n        <Loader>Loading content...</Loader>\n      </Flex>\n    );\n  }\n\n  const { action, user, payload } = data;\n\n  return (\n    <>\n      <Box marginBottom={3}>\n        <Typography variant=\"delta\" id=\"title\">\n          {formatMessage({\n            id: 'Settings.permissions.auditLogs.details',\n            defaultMessage: 'Log Details',\n          })}\n        </Typography>\n      </Box>\n      <Grid.Root\n        gap={4}\n        gridCols={2}\n        paddingTop={4}\n        paddingBottom={4}\n        paddingLeft={6}\n        paddingRight={6}\n        marginBottom={4}\n        background=\"neutral100\"\n        hasRadius\n      >\n        <ActionItem\n          actionLabel={formatMessage({\n            id: 'Settings.permissions.auditLogs.action',\n            defaultMessage: 'Action',\n          })}\n          actionName={formatMessage(\n            {\n              id: `Settings.permissions.auditLogs.${action}`,\n              defaultMessage: getDefaultMessage(action as keyof typeof actionTypes),\n            },\n            // @ts-expect-error - any\n            { model: payload?.model }\n          )}\n        />\n        <ActionItem\n          actionLabel={formatMessage({\n            id: 'Settings.permissions.auditLogs.date',\n            defaultMessage: 'Date',\n          })}\n          actionName={formattedDate}\n        />\n        <ActionItem\n          actionLabel={formatMessage({\n            id: 'Settings.permissions.auditLogs.user',\n            defaultMessage: 'User',\n          })}\n          actionName={user?.displayName || '-'}\n        />\n        <ActionItem\n          actionLabel={formatMessage({\n            id: 'Settings.permissions.auditLogs.userId',\n            defaultMessage: 'User ID',\n          })}\n          actionName={user?.id.toString() || '-'}\n        />\n      </Grid.Root>\n      <Field.Root>\n        <Field.Label>\n          {formatMessage({\n            id: 'Settings.permissions.auditLogs.payload',\n            defaultMessage: 'Payload',\n          })}\n        </Field.Label>\n        <Payload value={JSON.stringify(payload, null, 2)} disabled />\n      </Field.Root>\n    </>\n  );\n};\n\nconst Payload = styled(JSONInput)`\n  max-width: 100%;\n  overflow: scroll;\n`;\n\ninterface ActionItemProps {\n  actionLabel: string;\n  actionName: string;\n}\n\nconst ActionItem = ({ actionLabel, actionName }: ActionItemProps) => {\n  return (\n    <Flex direction=\"column\" alignItems=\"baseline\" gap={1}>\n      <Typography textColor=\"neutral600\" variant=\"sigma\">\n        {actionLabel}\n      </Typography>\n      <Typography textColor=\"neutral600\">{actionName}</Typography>\n    </Flex>\n  );\n};\n", "import * as React from 'react';\n\nimport { useNotification } from '../../../../../../../../admin/src/features/Notifications';\nimport { useAPIErrorHandler } from '../../../../../../../../admin/src/hooks/useAPIErrorHandler';\nimport { useQueryParams } from '../../../../../../../../admin/src/hooks/useQueryParams';\nimport { useAdminUsers } from '../../../../../../../../admin/src/services/users';\nimport { useGetAuditLogsQuery } from '../../../../../services/auditLogs';\n\nexport const useAuditLogsData = ({\n  canReadAuditLogs,\n  canReadUsers,\n}: {\n  canReadAuditLogs: boolean;\n  canReadUsers: boolean;\n}) => {\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n  const [{ query }] = useQueryParams();\n\n  const {\n    data,\n    error,\n    isError: isUsersError,\n    isLoading: isLoadingUsers,\n  } = useAdminUsers(\n    {},\n    {\n      skip: !canReadUsers,\n      refetchOnMountOrArgChange: true,\n    }\n  );\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({ type: 'danger', message: formatAPIError(error) });\n    }\n  }, [error, toggleNotification, formatAPIError]);\n\n  const {\n    data: auditLogs,\n    isLoading: isLoadingAuditLogs,\n    isError: isAuditLogsError,\n    error: auditLogsError,\n  } = useGetAuditLogsQuery(query, {\n    refetchOnMountOrArgChange: true,\n    skip: !canReadAuditLogs,\n  });\n\n  React.useEffect(() => {\n    if (auditLogsError) {\n      toggleNotification({ type: 'danger', message: formatAPIError(auditLogsError) });\n    }\n  }, [auditLogsError, toggleNotification, formatAPIError]);\n\n  return {\n    auditLogs,\n    users: data?.users ?? [],\n    isLoading: isLoadingUsers || isLoadingAuditLogs,\n    hasError: isAuditLogsError || isUsersError,\n  };\n};\n", "import { Combobox, ComboboxOption } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { Filters } from '../../../../../../../../admin/src/components/Filters';\nimport { useField } from '../../../../../../../../admin/src/components/Form';\n\nexport const ComboboxFilter = (props: Filters.ValueInputProps) => {\n  const { formatMessage } = useIntl();\n  const field = useField(props.name);\n  const ariaLabel = formatMessage({\n    id: 'Settings.permissions.auditLogs.filter.aria-label',\n    defaultMessage: 'Search and select an option to filter',\n  });\n\n  const handleChange = (value?: string) => {\n    field.onChange(props.name, value);\n  };\n\n  return (\n    <Combobox aria-label={ariaLabel} value={field.value} onChange={handleChange}>\n      {props.options?.map((opt) => {\n        const value = typeof opt === 'string' ? opt : opt.value;\n        const label = typeof opt === 'string' ? opt : opt.label;\n        return (\n          <ComboboxOption key={value} value={value}>\n            {label}\n          </ComboboxOption>\n        );\n      })}\n    </Combobox>\n  );\n};\n", "import { IntlShape } from 'react-intl';\n\nimport { Filters } from '../../../../../../../../admin/src/components/Filters';\nimport { getDisplayName } from '../../../../../../../../admin/src/utils/users';\nimport { SanitizedAdminUser } from '../../../../../../../../shared/contracts/shared';\nimport { ComboboxFilter } from '../components/ComboboxFilter';\n\nimport { actionTypes, getDefaultMessage } from './getActionTypesDefaultMessages';\n\nexport const getDisplayedFilters = ({\n  formatMessage,\n  users,\n  canReadUsers,\n}: {\n  formatMessage: IntlShape['formatMessage'];\n  users: SanitizedAdminUser[];\n  canReadUsers: boolean;\n}): Filters.Filter[] => {\n  const operators = [\n    {\n      label: formatMessage({\n        id: 'components.FilterOptions.FILTER_TYPES.$eq',\n        defaultMessage: 'is',\n      }),\n      value: '$eq',\n    },\n    {\n      label: formatMessage({\n        id: 'components.FilterOptions.FILTER_TYPES.$ne',\n        defaultMessage: 'is not',\n      }),\n      value: '$ne',\n    },\n  ] as NonNullable<Filters.Filter['operators']>;\n\n  const filters = [\n    {\n      input: ComboboxFilter,\n      label: formatMessage({\n        id: 'Settings.permissions.auditLogs.action',\n        defaultMessage: 'Action',\n      }),\n      name: 'action',\n      operators,\n      options: (Object.keys(actionTypes) as (keyof typeof actionTypes)[]).map((action) => ({\n        label: formatMessage(\n          {\n            id: `Settings.permissions.auditLogs.${action}`,\n            defaultMessage: getDefaultMessage(action),\n          },\n          { model: undefined }\n        ),\n        value: action,\n      })),\n      type: 'enumeration',\n    },\n    {\n      label: formatMessage({\n        id: 'Settings.permissions.auditLogs.date',\n        defaultMessage: 'Date',\n      }),\n      name: 'date',\n      type: 'datetime',\n    },\n  ] satisfies Filters.Filter[];\n\n  if (canReadUsers && users) {\n    return [\n      ...filters,\n      {\n        input: ComboboxFilter,\n        label: formatMessage({\n          id: 'Settings.permissions.auditLogs.user',\n          defaultMessage: 'User',\n        }),\n        mainField: { name: 'id', type: 'integer' },\n        name: 'user',\n        operators,\n        options: users.map((user) => ({\n          label: getDisplayName(user),\n          value: user.id.toString(),\n        })),\n        type: 'relation',\n      } satisfies Filters.Filter,\n    ];\n  }\n\n  return filters;\n};\n", "import { <PERSON><PERSON>, <PERSON><PERSON>B<PERSON>on, Typography } from '@strapi/design-system';\nimport { Eye } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport { Filters } from '../../../../../../../admin/src/components/Filters';\nimport { Layouts } from '../../../../../../../admin/src/components/Layouts/Layout';\nimport { Page } from '../../../../../../../admin/src/components/PageHelpers';\nimport { Pagination } from '../../../../../../../admin/src/components/Pagination';\nimport { Table } from '../../../../../../../admin/src/components/Table';\nimport { useTypedSelector } from '../../../../../../../admin/src/core/store/hooks';\nimport { useQueryParams } from '../../../../../../../admin/src/hooks/useQueryParams';\nimport { useRBAC } from '../../../../../../../admin/src/hooks/useRBAC';\nimport { AuditLog } from '../../../../../../../shared/contracts/audit-logs';\n\nimport { Modal } from './components/Modal';\nimport { useAuditLogsData } from './hooks/useAuditLogsData';\nimport { useFormatTimeStamp } from './hooks/useFormatTimeStamp';\nimport { getDefaultMessage } from './utils/getActionTypesDefaultMessages';\nimport { getDisplayedFilters } from './utils/getDisplayedFilters';\n\nconst ListPage = () => {\n  const { formatMessage } = useIntl();\n  const permissions = useTypedSelector((state) => state.admin_app.permissions.settings);\n\n  const {\n    allowedActions: { canRead: canReadAuditLogs, canReadUsers },\n    isLoading: isLoadingRBAC,\n  } = useRBAC({\n    ...permissions?.auditLogs,\n    readUsers: permissions?.users.read || [],\n  });\n\n  const [{ query }, setQuery] = useQueryParams<{ id?: AuditLog['id'] }>();\n  const {\n    auditLogs,\n    users,\n    isLoading: isLoadingData,\n    hasError,\n  } = useAuditLogsData({\n    canReadAuditLogs,\n    canReadUsers,\n  });\n\n  const formatTimeStamp = useFormatTimeStamp();\n\n  const displayedFilters = getDisplayedFilters({ formatMessage, users, canReadUsers });\n\n  const headers: Table.Header<AuditLog, object>[] = [\n    {\n      name: 'action',\n      label: formatMessage({\n        id: 'Settings.permissions.auditLogs.action',\n        defaultMessage: 'Action',\n      }),\n      sortable: true,\n    },\n    {\n      name: 'date',\n      label: formatMessage({\n        id: 'Settings.permissions.auditLogs.date',\n        defaultMessage: 'Date',\n      }),\n      sortable: true,\n    },\n    {\n      name: 'user',\n      label: formatMessage({\n        id: 'Settings.permissions.auditLogs.user',\n        defaultMessage: 'User',\n      }),\n      sortable: false,\n      // In this case, the passed parameter cannot and shouldn't be something else than User\n      cellFormatter: ({ user }) => (user ? user.displayName : ''),\n    },\n  ];\n\n  if (hasError) {\n    return <Page.Error />;\n  }\n\n  const isLoading = isLoadingData || isLoadingRBAC;\n\n  const { results = [] } = auditLogs ?? {};\n\n  return (\n    <Page.Main aria-busy={isLoading}>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: formatMessage({\n              id: 'global.auditLogs',\n              defaultMessage: 'Audit Logs',\n            }),\n          }\n        )}\n      </Page.Title>\n      <Layouts.Header\n        title={formatMessage({\n          id: 'global.auditLogs',\n          defaultMessage: 'Audit Logs',\n        })}\n        subtitle={formatMessage({\n          id: 'Settings.permissions.auditLogs.listview.header.subtitle',\n          defaultMessage: 'Logs of all the activities that happened in your environment',\n        })}\n      />\n      <Layouts.Action\n        startActions={\n          <Filters.Root options={displayedFilters}>\n            <Filters.Trigger />\n            <Filters.Popover />\n            <Filters.List />\n          </Filters.Root>\n        }\n      />\n      <Layouts.Content>\n        <Table.Root rows={results} headers={headers} isLoading={isLoading}>\n          <Table.Content>\n            <Table.Head>\n              {headers.map((header) => (\n                <Table.HeaderCell key={header.name} {...header} />\n              ))}\n            </Table.Head>\n            <Table.Empty />\n            <Table.Loading />\n            <Table.Body>\n              {results.map((log) => (\n                <Table.Row key={log.id} onClick={() => setQuery({ id: log.id })}>\n                  {headers.map((header) => {\n                    const { name, cellFormatter } = header;\n\n                    switch (name) {\n                      case 'action':\n                        return (\n                          <Table.Cell key={name}>\n                            <Typography textColor=\"neutral800\">\n                              {formatMessage(\n                                {\n                                  id: `Settings.permissions.auditLogs.${log.action}`,\n                                  // @ts-expect-error – getDefaultMessage probably doesn't benefit from being so strongly typed unless we just add string at the end.\n                                  defaultMessage: getDefaultMessage(log.action),\n                                },\n                                { model: (log.payload?.model as string) ?? '' }\n                              )}\n                            </Typography>\n                          </Table.Cell>\n                        );\n                      case 'date':\n                        return (\n                          <Table.Cell key={name}>\n                            <Typography textColor=\"neutral800\">\n                              {formatTimeStamp(log.date)}\n                            </Typography>\n                          </Table.Cell>\n                        );\n                      case 'user':\n                        return (\n                          <Table.Cell key={name}>\n                            <Typography textColor=\"neutral800\">\n                              {cellFormatter ? cellFormatter(log, header) : '-'}\n                            </Typography>\n                          </Table.Cell>\n                        );\n                      default:\n                        return (\n                          <Table.Cell key={name}>\n                            <Typography textColor=\"neutral800\">\n                              {(log[name as keyof AuditLog] as string) || '-'}\n                            </Typography>\n                          </Table.Cell>\n                        );\n                    }\n                  })}\n                  <Table.Cell onClick={(e) => e.stopPropagation()}>\n                    <Flex justifyContent=\"end\">\n                      <IconButton\n                        onClick={() => setQuery({ id: log.id })}\n                        withTooltip={false}\n                        label={formatMessage(\n                          { id: 'app.component.table.view', defaultMessage: '{target} details' },\n                          { target: `${log.action} action` }\n                        )}\n                        variant=\"ghost\"\n                      >\n                        <Eye />\n                      </IconButton>\n                    </Flex>\n                  </Table.Cell>\n                </Table.Row>\n              ))}\n            </Table.Body>\n          </Table.Content>\n        </Table.Root>\n\n        <Pagination.Root {...auditLogs?.pagination}>\n          <Pagination.PageSize />\n          <Pagination.Links />\n        </Pagination.Root>\n      </Layouts.Content>\n      {query?.id && (\n        <Modal handleClose={() => setQuery({ id: '' }, 'remove')} logId={query.id.toString()} />\n      )}\n    </Page.Main>\n  );\n};\n\nconst ProtectedListPage = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.auditLogs?.main\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <ListPage />\n    </Page.Protect>\n  );\n};\n\nexport { ListPage, ProtectedListPage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAMA,mBAAmBC,SAASC,gBAAgB;EAChDC,WAAW,CAACC,aAAa;IACvBC,cAAcD,QAAQE,MAAoE;MACxFA,OAAO,CAACC,YAAY;QAClBC,KAAK;QACLC,QAAQ;UACNF;QACF;;IAEJ,CAAA;IACAG,aAAaN,QAAQE,MAA0D;MAC7EA,OAAO,CAACK,OAAO,qBAAqBA,EAAAA;IACtC,CAAA;;EAEFC,kBAAkB;AACpB,CAAA;AAEA,IAAM,EAAEC,sBAAsBC,oBAAmB,IAAKd;;;ICjBzCe,qBAAqB,MAAA;AAChC,QAAM,EAAEC,WAAU,IAAKC,QAAAA;AAEvB,QAAMC,kBAAkB,CAACC,UAAAA;AACvB,UAAMC,OAAOC,SAASF,KAAAA;AAEtB,UAAMG,gBAAgBN,WAAWI,MAAM;MACrCG,WAAW;IACb,CAAA;AACA,UAAMC,gBAAgBR,WAAWI,MAAM;MACrCK,WAAW;MACXC,WAAW;IACb,CAAA;AAEA,WAAO,GAAGJ,aAAAA,KAAkBE,aAAAA;EAC9B;AAEA,SAAON;AACT;;;ICrBaS,cAAc;EACzB,gBAAgB;EAChB,gBAAgB;EAChB,gBAAgB;EAChB,iBAAiB;EACjB,mBAAmB;EACnB,gBAAgB;EAChB,gBAAgB;EAChB,gBAAgB;EAChB,uBAAuB;EACvB,uBAAuB;EACvB,uBAAuB;EACvB,eAAe;EACf,eAAe;EACf,eAAe;EACf,sBAAsB;EACtB,gBAAgB;EAChB,uBAAuB;EACvB,uBAAuB;EACvB,uBAAuB;EACvB,oBAAoB;EACpB,oBAAoB;EACpB,oBAAoB;EACpB,eAAe;EACf,eAAe;EACf,eAAe;EACf,qBAAqB;EACrB,qBAAqB;EACrB,qBAAqB;AACvB;AAEO,IAAMC,oBAAoB,CAACC,UAAAA;AAChC,SAAOF,YAAYE,KAAAA,KAAUA;AAC/B;;;ICJaC,SAAQ,CAAC,EAAEC,aAAaC,MAAK,MAAc;AACtD,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,yBAAyBC,eAAc,IAAKC,mBAAAA;AAEpD,QAAM,EAAEC,MAAMC,OAAOC,UAAS,IAAKC,oBAAoBT,KAAAA;AAEvDU,EAAMC,gBAAU,MAAA;AACd,QAAIJ,OAAO;AACTN,yBAAmB;QACjBW,MAAM;QACNC,SAAST,eAAeG,KAAAA;MAC1B,CAAA;AACAR,kBAAAA;IACF;KACC;IAACQ;IAAOH;IAAgBL;IAAaE;EAAmB,CAAA;AAE3D,QAAMa,kBAAkBC,mBAAAA;AACxB,QAAMC,gBAAgBV,QAAQ,UAAUA,OAAOQ,gBAAgBR,KAAKW,IAAI,IAAI;AAE5E,aACEC,wBAACC,MAAQC,MAAI;IAACC,aAAW;IAACC,cAAcvB;kBACtCwB,yBAACJ,MAAQK,SAAO;;YACdN,wBAACC,MAAQM,QAAM;UAIb,cAAAP,wBAACQ,aAAAA;YAAYC,OAAOX;YAAeY,IAAG;YACpC,cAAAV,wBAACW,OAAAA;cAAMC,WAAS;cAAEd,UAAAA;;;;YAGtBE,wBAACC,MAAQY,MAAI;UACX,cAAAb,wBAACc,YAAAA;YAAWxB;YAAsBF;YAAwBU;;;;;;AAKpE;AAQA,IAAMgB,aAAa,CAAC,EAAExB,WAAWF,MAAMU,cAAa,MAAmB;AACrE,QAAM,EAAEiB,cAAa,IAAKC,QAAAA;AAE1B,MAAI1B,WAAW;AACb,eACEU,wBAACiB,MAAAA;MAAKC,SAAS;MAAGC,gBAAe;MAASC,YAAW;MAInD,cAAApB,wBAACqB,QAAAA;QAAO,UAAA;;;EAGd;AAEA,QAAM,EAAEC,QAAQC,MAAMC,QAAO,IAAKpC;AAElC,aACEiB,yBAAAoB,6BAAA;;UACEzB,wBAAC0B,KAAAA;QAAIC,cAAc;QACjB,cAAA3B,wBAAC4B,YAAAA;UAAWC,SAAQ;UAAQnB,IAAG;oBAC5BK,cAAc;YACbL,IAAI;YACJoB,gBAAgB;UAClB,CAAA;;;UAGJzB,yBAAC0B,KAAK7B,MAAI;QACR8B,KAAK;QACLC,UAAU;QACVC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,cAAc;QACdV,cAAc;QACdW,YAAW;QACXC,WAAS;;cAETvC,wBAACwC,YAAAA;YACCC,aAAa1B,cAAc;cACzBL,IAAI;cACJoB,gBAAgB;YAClB,CAAA;YACAY,YAAY3B;cACV;gBACEL,IAAI,kCAAkCY,MAAAA;gBACtCQ,gBAAgBa,kBAAkBrB,MAAAA;cACpC;;cAEA;gBAAEsB,OAAOpB,mCAASoB;cAAM;YAAA;;cAG5B5C,wBAACwC,YAAAA;YACCC,aAAa1B,cAAc;cACzBL,IAAI;cACJoB,gBAAgB;YAClB,CAAA;YACAY,YAAY5C;;cAEdE,wBAACwC,YAAAA;YACCC,aAAa1B,cAAc;cACzBL,IAAI;cACJoB,gBAAgB;YAClB,CAAA;YACAY,aAAYnB,6BAAMsB,gBAAe;;cAEnC7C,wBAACwC,YAAAA;YACCC,aAAa1B,cAAc;cACzBL,IAAI;cACJoB,gBAAgB;YAClB,CAAA;YACAY,aAAYnB,6BAAMb,GAAGoC,eAAc;;;;UAGvCzC,yBAAC0C,MAAM7C,MAAI;;cACTF,wBAAC+C,MAAMC,OAAK;sBACTjC,cAAc;cACbL,IAAI;cACJoB,gBAAgB;YAClB,CAAA;;cAEF9B,wBAACiD,SAAAA;YAAQC,OAAOC,KAAKC,UAAU5B,SAAS,MAAM,CAAA;YAAI6B,UAAQ;;;;;;AAIlE;AAEA,IAAMJ,UAAUK,GAAOC,SAAAA;;;;AAUvB,IAAMf,aAAa,CAAC,EAAEC,aAAaC,WAAU,MAAmB;AAC9D,aACErC,yBAACY,MAAAA;IAAKuC,WAAU;IAASpC,YAAW;IAAWY,KAAK;;UAClDhC,wBAAC4B,YAAAA;QAAW6B,WAAU;QAAa5B,SAAQ;QACxCY,UAAAA;;UAEHzC,wBAAC4B,YAAAA;QAAW6B,WAAU;QAAcf,UAAAA;;;;AAG1C;;;;IC1KagB,mBAAmB,CAAC,EAC/BC,kBACAC,aAAY,MAIb;AACC,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,yBAAyBC,eAAc,IAAKC,mBAAAA;AACpD,QAAM,CAAC,EAAEC,MAAK,CAAE,IAAIC,eAAAA;AAEpB,QAAM,EACJC,MACAC,OACAC,SAASC,cACTC,WAAWC,eAAc,IACvBC,cACF,CAAA,GACA;IACEC,MAAM,CAACf;IACPgB,2BAA2B;EAC7B,CAAA;AAGFC,EAAMC,iBAAU,MAAA;AACd,QAAIT,OAAO;AACTR,yBAAmB;QAAEkB,MAAM;QAAUC,SAAShB,eAAeK,KAAAA;MAAO,CAAA;IACtE;KACC;IAACA;IAAOR;IAAoBG;EAAe,CAAA;AAE9C,QAAM,EACJI,MAAMa,WACNT,WAAWU,oBACXZ,SAASa,kBACTd,OAAOe,eAAc,IACnBC,qBAAqBnB,OAAO;IAC9BU,2BAA2B;IAC3BD,MAAM,CAAChB;EACT,CAAA;AAEAkB,EAAMC,iBAAU,MAAA;AACd,QAAIM,gBAAgB;AAClBvB,yBAAmB;QAAEkB,MAAM;QAAUC,SAAShB,eAAeoB,cAAAA;MAAgB,CAAA;IAC/E;KACC;IAACA;IAAgBvB;IAAoBG;EAAe,CAAA;AAEvD,SAAO;IACLiB;IACAK,QAAOlB,6BAAMkB,UAAS,CAAA;IACtBd,WAAWC,kBAAkBS;IAC7BK,UAAUJ,oBAAoBZ;EAChC;AACF;;;;ACtDO,IAAMiB,iBAAiB,CAACC,UAAAA;;AAC7B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAMC,QAAQC,SAASJ,MAAMK,IAAI;AACjC,QAAMC,YAAYL,cAAc;IAC9BM,IAAI;IACJC,gBAAgB;EAClB,CAAA;AAEA,QAAMC,eAAe,CAACC,UAAAA;AACpBP,UAAMQ,SAASX,MAAMK,MAAMK,KAAAA;EAC7B;AAEA,aACEE,yBAACC,UAAAA;IAASC,cAAYR;IAAWI,OAAOP,MAAMO;IAAOC,UAAUF;eAC5DT,WAAMe,YAANf,mBAAegB,IAAI,CAACC,QAAAA;AACnB,YAAMP,QAAQ,OAAOO,QAAQ,WAAWA,MAAMA,IAAIP;AAClD,YAAMQ,QAAQ,OAAOD,QAAQ,WAAWA,MAAMA,IAAIC;AAClD,iBACEN,yBAACO,QAAAA;QAA2BT;QACzBQ,UAAAA;MADkBR,GAAAA,KAAAA;IAIzB;;AAGN;;;ACtBO,IAAMU,sBAAsB,CAAC,EAClCC,eACAC,OACAC,aAAY,MAKb;AACC,QAAMC,YAAY;IAChB;MACEC,OAAOJ,cAAc;QACnBK,IAAI;QACJC,gBAAgB;MAClB,CAAA;MACAC,OAAO;IACT;IACA;MACEH,OAAOJ,cAAc;QACnBK,IAAI;QACJC,gBAAgB;MAClB,CAAA;MACAC,OAAO;IACT;EACD;AAED,QAAMC,UAAU;IACd;MACEC,OAAOC;MACPN,OAAOJ,cAAc;QACnBK,IAAI;QACJC,gBAAgB;MAClB,CAAA;MACAK,MAAM;MACNR;MACAS,SAAUC,OAAOC,KAAKC,WAAAA,EAA8CC,IAAI,CAACC,YAAY;QACnFb,OAAOJ,cACL;UACEK,IAAI,kCAAkCY,MAAAA;UACtCX,gBAAgBY,kBAAkBD,MAAAA;WAEpC;UAAEE,OAAOC;QAAU,CAAA;QAErBb,OAAOU;QACT;MACAI,MAAM;IACR;IACA;MACEjB,OAAOJ,cAAc;QACnBK,IAAI;QACJC,gBAAgB;MAClB,CAAA;MACAK,MAAM;MACNU,MAAM;IACR;EACD;AAED,MAAInB,gBAAgBD,OAAO;AACzB,WAAO;MACFO,GAAAA;MACH;QACEC,OAAOC;QACPN,OAAOJ,cAAc;UACnBK,IAAI;UACJC,gBAAgB;QAClB,CAAA;QACAgB,WAAW;UAAEX,MAAM;UAAMU,MAAM;QAAU;QACzCV,MAAM;QACNR;QACAS,SAASX,MAAMe,IAAI,CAACO,UAAU;UAC5BnB,OAAOoB,eAAeD,IAAAA;UACtBhB,OAAOgB,KAAKlB,GAAGoB,SAAQ;UACzB;QACAJ,MAAM;MACR;IACD;EACH;AAEA,SAAOb;AACT;;;ACpEA,IAAMkB,WAAW,MAAA;AACf,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAMC,cAAcC,iBAAiB,CAACC,UAAUA,MAAMC,UAAUH,YAAYI,QAAQ;AAEpF,QAAM,EACJC,gBAAgB,EAAEC,SAASC,kBAAkBC,aAAY,GACzDC,WAAWC,cAAa,IACtBC,QAAQ;IACV,GAAGX,2CAAaY;IAChBC,YAAWb,2CAAac,MAAMC,SAAQ,CAAA;EACxC,CAAA;AAEA,QAAM,CAAC,EAAEC,MAAK,GAAIC,QAAAA,IAAYC,eAAAA;AAC9B,QAAM,EACJN,WACAE,OACAL,WAAWU,eACXC,SAAQ,IACNC,iBAAiB;IACnBd;IACAC;EACF,CAAA;AAEA,QAAMc,kBAAkBC,mBAAAA;AAExB,QAAMC,mBAAmBC,oBAAoB;IAAE3B;IAAegB;IAAON;EAAa,CAAA;AAElF,QAAMkB,UAA4C;IAChD;MACEC,MAAM;MACNC,OAAO9B,cAAc;QACnB+B,IAAI;QACJC,gBAAgB;MAClB,CAAA;MACAC,UAAU;IACZ;IACA;MACEJ,MAAM;MACNC,OAAO9B,cAAc;QACnB+B,IAAI;QACJC,gBAAgB;MAClB,CAAA;MACAC,UAAU;IACZ;IACA;MACEJ,MAAM;MACNC,OAAO9B,cAAc;QACnB+B,IAAI;QACJC,gBAAgB;MAClB,CAAA;MACAC,UAAU;;MAEVC,eAAe,CAAC,EAAEC,KAAI,MAAQA,OAAOA,KAAKC,cAAc;IAC1D;EACD;AAED,MAAId,UAAU;AACZ,eAAOe,yBAACC,KAAKC,OAAK,CAAA,CAAA;EACpB;AAEA,QAAM5B,YAAYU,iBAAiBT;AAEnC,QAAM,EAAE4B,UAAU,CAAA,EAAE,IAAK1B,aAAa,CAAA;AAEtC,aACE2B,0BAACH,KAAKI,MAAI;IAACC,aAAWhC;;UACpB0B,yBAACC,KAAKM,OAAK;kBACR5C,cACC;UAAE+B,IAAI;UAAsBC,gBAAgB;WAC5C;UACEH,MAAM7B,cAAc;YAClB+B,IAAI;YACJC,gBAAgB;UAClB,CAAA;QACF,CAAA;;UAGJK,yBAACQ,QAAQC,QAAM;QACbC,OAAO/C,cAAc;UACnB+B,IAAI;UACJC,gBAAgB;QAClB,CAAA;QACAgB,UAAUhD,cAAc;UACtB+B,IAAI;UACJC,gBAAgB;QAClB,CAAA;;UAEFK,yBAACQ,QAAQI,QAAM;QACbC,kBACET,0BAACU,QAAQC,MAAI;UAACC,SAAS3B;;gBACrBW,yBAACc,QAAQG,SAAO,CAAA,CAAA;gBAChBjB,yBAACc,QAAQI,SAAO,CAAA,CAAA;gBAChBlB,yBAACc,QAAQK,MAAI,CAAA,CAAA;;;;UAInBf,0BAACI,QAAQY,SAAO;;cACdpB,yBAACqB,MAAMN,MAAI;YAACO,MAAMnB;YAASZ;YAAkBjB;0BAC3C8B,0BAACiB,MAAMD,SAAO;;oBACZpB,yBAACqB,MAAME,MAAI;kBACRhC,UAAAA,QAAQiC,IAAI,CAACC,eACZzB,yBAACqB,MAAMK,YAAU;oBAAoB,GAAGD;kBAAjBA,GAAAA,OAAOjC,IAAI,CAAA;;oBAGtCQ,yBAACqB,MAAMM,OAAK,CAAA,CAAA;oBACZ3B,yBAACqB,MAAMO,SAAO,CAAA,CAAA;oBACd5B,yBAACqB,MAAMQ,MAAI;kBACR1B,UAAAA,QAAQqB,IAAI,CAACM,YACZ1B,0BAACiB,MAAMU,KAAG;oBAAcC,SAAS,MAAMlD,SAAS;sBAAEY,IAAIoC,IAAIpC;oBAAG,CAAA;;sBAC1DH,QAAQiC,IAAI,CAACC,WAAAA;;AACZ,8BAAM,EAAEjC,MAAMK,cAAa,IAAK4B;AAEhC,gCAAQjC,MAAAA;0BACN,KAAK;AACH,uCACEQ,yBAACqB,MAAMY,MAAI;8BACT,cAAAjC,yBAACkC,YAAAA;gCAAWC,WAAU;0CACnBxE,cACC;kCACE+B,IAAI,kCAAkCoC,IAAIM,MAAM;;kCAEhDzC,gBAAgB0C,kBAAkBP,IAAIM,MAAM;mCAE9C;kCAAEE,SAAO,SAAKC,YAAL,mBAAcD,UAAoB;gCAAG,CAAA;;4BARnC9C,GAAAA,IAAAA;0BAarB,KAAK;AACH,uCACEQ,yBAACqB,MAAMY,MAAI;8BACT,cAAAjC,yBAACkC,YAAAA;gCAAWC,WAAU;gCACnBhD,UAAAA,gBAAgB2C,IAAIU,IAAI;;4BAFZhD,GAAAA,IAAAA;0BAMrB,KAAK;AACH,uCACEQ,yBAACqB,MAAMY,MAAI;8BACT,cAAAjC,yBAACkC,YAAAA;gCAAWC,WAAU;0CACnBtC,gBAAgBA,cAAciC,KAAKL,MAAU,IAAA;;4BAFjCjC,GAAAA,IAAAA;0BAMrB;AACE,uCACEQ,yBAACqB,MAAMY,MAAI;8BACT,cAAAjC,yBAACkC,YAAAA;gCAAWC,WAAU;0CACnB,IAAK3C,IAAAA,KAAsC;;4BAF/BA,GAAAA,IAAAA;wBAMvB;sBACF,CAAA;0BACAQ,yBAACqB,MAAMY,MAAI;wBAACD,SAAS,CAACS,MAAMA,EAAEC,gBAAe;wBAC3C,cAAA1C,yBAAC2C,MAAAA;0BAAKC,gBAAe;0BACnB,cAAA5C,yBAAC6C,YAAAA;4BACCb,SAAS,MAAMlD,SAAS;8BAAEY,IAAIoC,IAAIpC;4BAAG,CAAA;4BACrCoD,aAAa;4BACbrD,OAAO9B,cACL;8BAAE+B,IAAI;8BAA4BC,gBAAgB;+BAClD;8BAAEoD,QAAQ,GAAGjB,IAAIM,MAAM;4BAAU,CAAA;4BAEnCY,SAAQ;4BAER,cAAAhD,yBAACiD,eAAAA,CAAAA,CAAAA;;;;;kBAzDOnB,GAAAA,IAAIpC,EAAE,CAAA;;;;;cAmE9BU,0BAAC8C,WAAWnC,MAAI;YAAE,GAAGtC,uCAAW0E;;kBAC9BnD,yBAACkD,WAAWE,UAAQ,CAAA,CAAA;kBACpBpD,yBAACkD,WAAWG,OAAK,CAAA,CAAA;;;;;OAGpBxE,+BAAOa,WACNM,yBAACsD,QAAAA;QAAMC,aAAa,MAAMzE,SAAS;UAAEY,IAAI;WAAM,QAAA;QAAW8D,OAAO3E,MAAMa,GAAG+D,SAAQ;;;;AAI1F;AAEA,IAAMC,oBAAoB,MAAA;AACxB,QAAM7F,cAAcC,iBAClB,CAACC,UAAUA;;AAAAA,6BAAMC,UAAUH,YAAYI,aAA5BF,mBAAsCU,cAAtCV,mBAAiD4F;GAAAA;AAG9D,aACE3D,yBAACC,KAAK2D,SAAO;IAAC/F;IACZ,cAAAmC,yBAACtC,UAAAA,CAAAA,CAAAA;;AAGP;", "names": ["auditLogsService", "adminApi", "injectEndpoints", "endpoints", "builder", "getAuditLogs", "query", "params", "url", "config", "getAuditLog", "id", "overrideExisting", "useGetAuditLogsQuery", "useGetAuditLogQuery", "useFormatTimeStamp", "formatDate", "useIntl", "formatTimeStamp", "value", "date", "parseISO", "formattedDate", "dateStyle", "formattedTime", "timeStyle", "hourCycle", "actionTypes", "getDefaultMessage", "value", "Modal", "handleClose", "logId", "toggleNotification", "useNotification", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "data", "error", "isLoading", "useGetAuditLogQuery", "React", "useEffect", "type", "message", "formatTimeStamp", "useFormatTimeStamp", "formattedDate", "date", "_jsx", "DSModal", "Root", "defaultOpen", "onOpenChange", "_jsxs", "Content", "Header", "Breadcrumbs", "label", "id", "Crumb", "isCurrent", "Body", "ActionBody", "formatMessage", "useIntl", "Flex", "padding", "justifyContent", "alignItems", "Loader", "action", "user", "payload", "_Fragment", "Box", "marginBottom", "Typography", "variant", "defaultMessage", "Grid", "gap", "gridCols", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "background", "hasRadius", "ActionItem", "actionLabel", "actionName", "getDefaultMessage", "model", "displayName", "toString", "Field", "Label", "Payload", "value", "JSON", "stringify", "disabled", "styled", "JSONInput", "direction", "textColor", "useAuditLogsData", "canReadAuditLogs", "canReadUsers", "toggleNotification", "useNotification", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "query", "useQueryParams", "data", "error", "isError", "isUsersError", "isLoading", "isLoadingUsers", "useAdminUsers", "skip", "refetchOnMountOrArgChange", "React", "useEffect", "type", "message", "auditLogs", "isLoadingAuditLogs", "isAuditLogsError", "auditLogsError", "useGetAuditLogsQuery", "users", "<PERSON><PERSON><PERSON><PERSON>", "ComboboxFilter", "props", "formatMessage", "useIntl", "field", "useField", "name", "aria<PERSON><PERSON><PERSON>", "id", "defaultMessage", "handleChange", "value", "onChange", "_jsx", "Combobox", "aria-label", "options", "map", "opt", "label", "ComboboxOption", "getDisplayedFilters", "formatMessage", "users", "canReadUsers", "operators", "label", "id", "defaultMessage", "value", "filters", "input", "ComboboxFilter", "name", "options", "Object", "keys", "actionTypes", "map", "action", "getDefaultMessage", "model", "undefined", "type", "mainField", "user", "getDisplayName", "toString", "ListPage", "formatMessage", "useIntl", "permissions", "useTypedSelector", "state", "admin_app", "settings", "allowedActions", "canRead", "canReadAuditLogs", "canReadUsers", "isLoading", "isLoadingRBAC", "useRBAC", "auditLogs", "readUsers", "users", "read", "query", "<PERSON><PERSON><PERSON><PERSON>", "useQueryParams", "isLoadingData", "<PERSON><PERSON><PERSON><PERSON>", "useAuditLogsData", "formatTimeStamp", "useFormatTimeStamp", "displayedFilters", "getDisplayedFilters", "headers", "name", "label", "id", "defaultMessage", "sortable", "cell<PERSON>ormatt<PERSON>", "user", "displayName", "_jsx", "Page", "Error", "results", "_jsxs", "Main", "aria-busy", "Title", "Layouts", "Header", "title", "subtitle", "Action", "startActions", "Filters", "Root", "options", "<PERSON><PERSON>", "Popover", "List", "Content", "Table", "rows", "Head", "map", "header", "<PERSON><PERSON><PERSON><PERSON>", "Empty", "Loading", "Body", "log", "Row", "onClick", "Cell", "Typography", "textColor", "action", "getDefaultMessage", "model", "payload", "date", "e", "stopPropagation", "Flex", "justifyContent", "IconButton", "withTooltip", "target", "variant", "Eye", "Pagination", "pagination", "PageSize", "Links", "Modal", "handleClose", "logId", "toString", "ProtectedListPage", "main", "Protect"]}