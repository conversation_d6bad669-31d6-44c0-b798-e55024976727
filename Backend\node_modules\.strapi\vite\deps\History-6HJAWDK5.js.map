{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/history/components/VersionInputRenderer.tsx", "../../../@strapi/content-manager/admin/src/history/components/VersionContent.tsx", "../../../@strapi/content-manager/admin/src/history/services/historyVersion.ts", "../../../@strapi/content-manager/admin/src/history/components/VersionHeader.tsx", "../../../@strapi/content-manager/admin/src/history/components/VersionsList.tsx", "../../../@strapi/content-manager/admin/src/history/pages/History.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {\n  useStrapi<PERSON><PERSON>,\n  useForm,\n  InputRenderer as FormInput<PERSON>enderer,\n  useField,\n  Form,\n} from '@strapi/admin/strapi-admin';\nimport { Al<PERSON>, Box, Field, Flex, Link, Tooltip, Typography } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { NavLink } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { HistoryVersionDataResponse } from '../../../../shared/contracts/history-versions';\nimport { COLLECTION_TYPES } from '../../constants/collections';\nimport { useDocumentRBAC } from '../../features/DocumentRBAC';\nimport { useDoc } from '../../hooks/useDocument';\nimport { useDocLayout } from '../../hooks/useDocumentLayout';\nimport { useLazyComponents } from '../../hooks/useLazyComponents';\nimport { useTypedSelector } from '../../modules/hooks';\nimport { DocumentStatus } from '../../pages/EditView/components/DocumentStatus';\nimport { BlocksInput } from '../../pages/EditView/components/FormInputs/BlocksInput/BlocksInput';\nimport { ComponentInput } from '../../pages/EditView/components/FormInputs/Component/Input';\nimport {\n  DynamicZone,\n  useDynamicZone,\n} from '../../pages/EditView/components/FormInputs/DynamicZone/Field';\nimport { NotAllowedInput } from '../../pages/EditView/components/FormInputs/NotAllowed';\nimport { UIDInput } from '../../pages/EditView/components/FormInputs/UID';\nimport { Wysiwyg } from '../../pages/EditView/components/FormInputs/Wysiwyg/Field';\nimport { useFieldHint } from '../../pages/EditView/components/InputRenderer';\nimport { getRelationLabel } from '../../utils/relations';\nimport { useHistoryContext } from '../pages/History';\n\nimport { getRemaingFieldsLayout } from './VersionContent';\n\nimport type { EditFieldLayout } from '../../hooks/useDocumentLayout';\nimport type { RelationsFieldProps } from '../../pages/EditView/components/FormInputs/Relations/Relations';\nimport type { RelationResult } from '../../services/relations';\nimport type { Schema } from '@strapi/types';\nimport type { DistributiveOmit } from 'react-redux';\n\nconst StyledAlert = styled(Alert).attrs({ closeLabel: 'Close', onClose: () => {}, shadow: 'none' })`\n  button {\n    display: none;\n  }\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * CustomRelationInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst LinkEllipsis = styled(Link)`\n  display: block;\n\n  & > span {\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    display: block;\n  }\n`;\n\nconst CustomRelationInput = (props: RelationsFieldProps) => {\n  const { formatMessage } = useIntl();\n  const field = useField<\n    { results: RelationResult[]; meta: { missingCount: number } } | RelationResult[]\n  >(props.name);\n\n  /**\n   * Ideally the server would return the correct shape, however, for admin user relations\n   * it sanitizes everything out when it finds an object for the relation value.\n   */\n  let formattedFieldValue;\n  if (field) {\n    formattedFieldValue = Array.isArray(field.value)\n      ? { results: field.value, meta: { missingCount: 0 } }\n      : field.value;\n  }\n\n  if (\n    !formattedFieldValue ||\n    (formattedFieldValue.results.length === 0 && formattedFieldValue.meta.missingCount === 0)\n  ) {\n    return (\n      <>\n        <Field.Label action={props.labelAction}>{props.label}</Field.Label>\n        <Box marginTop={1}>\n          {/* @ts-expect-error – we dont need closeLabel */}\n          <StyledAlert variant=\"default\">\n            {formatMessage({\n              id: 'content-manager.history.content.no-relations',\n              defaultMessage: 'No relations.',\n            })}\n          </StyledAlert>\n        </Box>\n      </>\n    );\n  }\n\n  const { results, meta } = formattedFieldValue;\n\n  return (\n    <Box>\n      <Field.Label>{props.label}</Field.Label>\n      {results.length > 0 && (\n        <Flex direction=\"column\" gap={2} marginTop={1} alignItems=\"stretch\">\n          {results.map((relationData) => {\n            // @ts-expect-error - targetModel does exist on the attribute. But it's not typed.\n            const { targetModel } = props.attribute;\n            const href = `../${COLLECTION_TYPES}/${targetModel}/${relationData.documentId}`;\n            const label = getRelationLabel(relationData, props.mainField);\n            const isAdminUserRelation = targetModel === 'admin::user';\n\n            return (\n              <Flex\n                key={relationData.documentId ?? relationData.id}\n                paddingTop={2}\n                paddingBottom={2}\n                paddingLeft={4}\n                paddingRight={4}\n                hasRadius\n                borderColor=\"neutral200\"\n                background=\"neutral150\"\n                justifyContent=\"space-between\"\n              >\n                <Box minWidth={0} paddingTop={1} paddingBottom={1} paddingRight={4}>\n                  <Tooltip label={label}>\n                    {isAdminUserRelation ? (\n                      <Typography>{label}</Typography>\n                    ) : (\n                      <LinkEllipsis tag={NavLink} to={href}>\n                        {label}\n                      </LinkEllipsis>\n                    )}\n                  </Tooltip>\n                </Box>\n                <DocumentStatus status={relationData.status as string} />\n              </Flex>\n            );\n          })}\n        </Flex>\n      )}\n      {meta.missingCount > 0 && (\n        /* @ts-expect-error – we dont need closeLabel */\n        <StyledAlert\n          marginTop={1}\n          variant=\"warning\"\n          title={formatMessage(\n            {\n              id: 'content-manager.history.content.missing-relations.title',\n              defaultMessage:\n                '{number, plural, =1 {Missing relation} other {{number} missing relations}}',\n            },\n            { number: meta.missingCount }\n          )}\n        >\n          {formatMessage(\n            {\n              id: 'content-manager.history.content.missing-relations.message',\n              defaultMessage:\n                \"{number, plural, =1 {It has} other {They have}} been deleted and can't be restored.\",\n            },\n            { number: meta.missingCount }\n          )}\n        </StyledAlert>\n      )}\n    </Box>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * CustomMediaInput\n * -----------------------------------------------------------------------------------------------*/\n\n//  Create an object with value at key path (i.e. 'a.b.c')\nconst createInitialValuesForPath = (keyPath: string, value: any) => {\n  const keys = keyPath.split('.');\n  // The root level object\n  const root: Record<string, any> = {};\n\n  // Make the first node the root\n  let node = root;\n  keys.forEach((key, index) => {\n    // Skip prototype pollution keys\n    if (key === '__proto__' || key === 'constructor') return;\n    // If it's the last key, set the node value\n    if (index === keys.length - 1) {\n      node[key] = value;\n    } else {\n      // Ensure the key exists and is an object\n      node[key] = node[key] || {};\n    }\n\n    // Traverse down the tree\n    node = node[key];\n  });\n\n  return root;\n};\n\nconst CustomMediaInput = (props: VersionInputRendererProps) => {\n  const { value } = useField(props.name);\n  const results = value?.results ?? [];\n  const meta = value?.meta ?? { missingCount: 0 };\n\n  const { formatMessage } = useIntl();\n\n  const fields = useStrapiApp('CustomMediaInput', (state) => state.fields);\n  const MediaLibrary = fields.media as React.ComponentType<\n    VersionInputRendererProps & { multiple: boolean }\n  >;\n\n  return (\n    <Flex direction=\"column\" gap={2} alignItems=\"stretch\">\n      <Form\n        method=\"PUT\"\n        disabled={true}\n        initialValues={createInitialValuesForPath(props.name, results)}\n      >\n        <MediaLibrary {...props} disabled={true} multiple={results.length > 1} />\n      </Form>\n      {meta.missingCount > 0 && (\n        <StyledAlert\n          variant=\"warning\"\n          closeLabel=\"Close\"\n          onClose={() => {}}\n          title={formatMessage(\n            {\n              id: 'content-manager.history.content.missing-assets.title',\n              defaultMessage:\n                '{number, plural, =1 {Missing asset} other {{number} missing assets}}',\n            },\n            { number: meta.missingCount }\n          )}\n        >\n          {formatMessage(\n            {\n              id: 'content-manager.history.content.missing-assets.message',\n              defaultMessage:\n                \"{number, plural, =1 {It has} other {They have}} been deleted in the Media Library and can't be restored.\",\n            },\n            { number: meta.missingCount }\n          )}\n        </StyledAlert>\n      )}\n    </Flex>\n  );\n};\n\ntype VersionInputRendererProps = DistributiveOmit<EditFieldLayout, 'size'> & {\n  /**\n   * In the context of content history, deleted fields need to ignore RBAC\n   * @default false\n   */\n  shouldIgnoreRBAC?: boolean;\n};\n\n/**\n * Checks if the i18n plugin added a label action to the field and modifies it\n * to adapt the wording for the history page.\n */\nconst getLabelAction = (labelAction: VersionInputRendererProps['labelAction']) => {\n  if (!React.isValidElement(labelAction)) {\n    return labelAction;\n  }\n\n  // TODO: find a better way to do this rather than access internals\n  const labelActionTitleId = labelAction.props.title.id;\n\n  if (labelActionTitleId === 'i18n.Field.localized') {\n    return React.cloneElement(labelAction, {\n      ...labelAction.props,\n      title: {\n        id: 'history.content.localized',\n        defaultMessage:\n          'This value is specific to this locale. If you restore this version, the content will not be replaced for other locales.',\n      },\n    });\n  }\n\n  if (labelActionTitleId === 'i18n.Field.not-localized') {\n    return React.cloneElement(labelAction, {\n      ...labelAction.props,\n      title: {\n        id: 'history.content.not-localized',\n        defaultMessage:\n          'This value is common to all locales. If you restore this version and save the changes, the content will be replaced for all locales.',\n      },\n    });\n  }\n\n  // Label action is unrelated to i18n, don't touch it.\n  return labelAction;\n};\n\n/**\n * @internal\n *\n * @description An abstraction around the regular form input renderer designed specifically\n * to be used on the History page in the content-manager. It understands how to render specific\n * inputs within the context of a history version (i.e. relations, media, ignored RBAC, etc...)\n */\nconst VersionInputRenderer = ({\n  visible,\n  hint: providedHint,\n  shouldIgnoreRBAC = false,\n  labelAction,\n  ...props\n}: VersionInputRendererProps) => {\n  const customLabelAction = getLabelAction(labelAction);\n\n  const { formatMessage } = useIntl();\n  const version = useHistoryContext('VersionContent', (state) => state.selectedVersion);\n  const configuration = useHistoryContext('VersionContent', (state) => state.configuration);\n  const fieldSizes = useTypedSelector((state) => state['content-manager'].app.fieldSizes);\n\n  const { id, components } = useDoc();\n  const isFormDisabled = useForm('InputRenderer', (state) => state.disabled);\n\n  const isInDynamicZone = useDynamicZone('isInDynamicZone', (state) => state.isInDynamicZone);\n\n  const canCreateFields = useDocumentRBAC('InputRenderer', (rbac) => rbac.canCreateFields);\n  const canReadFields = useDocumentRBAC('InputRenderer', (rbac) => rbac.canReadFields);\n  const canUpdateFields = useDocumentRBAC('InputRenderer', (rbac) => rbac.canUpdateFields);\n  const canUserAction = useDocumentRBAC('InputRenderer', (rbac) => rbac.canUserAction);\n\n  const editableFields = id ? canUpdateFields : canCreateFields;\n  const readableFields = id ? canReadFields : canCreateFields;\n  /**\n   * Component fields are always readable and editable,\n   * however the fields within them may not be.\n   */\n  const canUserReadField = canUserAction(props.name, readableFields, props.type);\n  const canUserEditField = canUserAction(props.name, editableFields, props.type);\n\n  const fields = useStrapiApp('InputRenderer', (app) => app.fields);\n  const { lazyComponentStore } = useLazyComponents(\n    attributeHasCustomFieldProperty(props.attribute) ? [props.attribute.customField] : undefined\n  );\n\n  const hint = useFieldHint(providedHint, props.attribute);\n  const {\n    edit: { components: componentsLayout },\n  } = useDocLayout();\n\n  if (!visible) {\n    return null;\n  }\n\n  /**\n   * Don't render the field if the user can't read it.\n   */\n  if (!shouldIgnoreRBAC && !canUserReadField && !isInDynamicZone) {\n    return <NotAllowedInput hint={hint} {...props} />;\n  }\n\n  const fieldIsDisabled =\n    (!canUserEditField && !isInDynamicZone) || props.disabled || isFormDisabled;\n\n  /**\n   * Attributes found on the current content-type schema cannot be restored. We handle\n   * this by displaying a warning alert to the user instead of the input for that field type.\n   */\n  const addedAttributes = version.meta.unknownAttributes.added;\n  if (Object.keys(addedAttributes).includes(props.name)) {\n    return (\n      <Flex direction=\"column\" alignItems=\"flex-start\" gap={1}>\n        <Field.Label>{props.label}</Field.Label>\n        <StyledAlert\n          width=\"100%\"\n          closeLabel=\"Close\"\n          onClose={() => {}}\n          variant=\"warning\"\n          title={formatMessage({\n            id: 'content-manager.history.content.new-field.title',\n            defaultMessage: 'New field',\n          })}\n        >\n          {formatMessage({\n            id: 'content-manager.history.content.new-field.message',\n            defaultMessage:\n              \"This field didn't exist when this version was saved. If you restore this version, it will be empty.\",\n          })}\n        </StyledAlert>\n      </Flex>\n    );\n  }\n\n  /**\n   * Because a custom field has a unique prop but the type could be confused with either\n   * the useField hook or the type of the field we need to handle it separately and first.\n   */\n  if (attributeHasCustomFieldProperty(props.attribute)) {\n    const CustomInput = lazyComponentStore[props.attribute.customField];\n\n    if (CustomInput) {\n      return (\n        <CustomInput\n          {...props}\n          // @ts-expect-error – TODO: fix this type error in the useLazyComponents hook.\n          hint={hint}\n          labelAction={customLabelAction}\n          disabled={fieldIsDisabled}\n        />\n      );\n    }\n\n    return (\n      <FormInputRenderer\n        {...props}\n        hint={hint}\n        labelAction={customLabelAction}\n        // @ts-expect-error – this workaround lets us display that the custom field is missing.\n        type={props.attribute.customField}\n        disabled={fieldIsDisabled}\n      />\n    );\n  }\n\n  /**\n   * Since media fields use a custom input via the upload plugin provided by the useLibrary hook,\n   * we need to handle the them before other custom inputs coming from the useLibrary hook.\n   */\n  if (props.type === 'media') {\n    return (\n      <CustomMediaInput {...props} labelAction={customLabelAction} disabled={fieldIsDisabled} />\n    );\n  }\n  /**\n   * This is where we handle ONLY the fields from the `useLibrary` hook.\n   */\n  const addedInputTypes = Object.keys(fields);\n  if (!attributeHasCustomFieldProperty(props.attribute) && addedInputTypes.includes(props.type)) {\n    const CustomInput = fields[props.type];\n    return (\n      <CustomInput\n        {...props}\n        // @ts-expect-error – TODO: fix this type error in the useLibrary hook.\n        hint={hint}\n        labelAction={customLabelAction}\n        disabled={fieldIsDisabled}\n      />\n    );\n  }\n\n  /**\n   * These include the content-manager specific fields, failing that we fall back\n   * to the more generic form input renderer.\n   */\n  switch (props.type) {\n    case 'blocks':\n      return <BlocksInput {...props} hint={hint} type={props.type} disabled={fieldIsDisabled} />;\n    case 'component':\n      const { layout } = componentsLayout[props.attribute.component];\n      // Components can only have one panel, so only save the first layout item\n      const [remainingFieldsLayout] = getRemaingFieldsLayout({\n        layout: [layout],\n        metadatas: configuration.components[props.attribute.component].metadatas,\n        fieldSizes,\n        schemaAttributes: components[props.attribute.component].attributes,\n      });\n\n      return (\n        <ComponentInput\n          {...props}\n          layout={[...layout, ...(remainingFieldsLayout || [])]}\n          hint={hint}\n          labelAction={customLabelAction}\n          disabled={fieldIsDisabled}\n        >\n          {(inputProps) => <VersionInputRenderer {...inputProps} shouldIgnoreRBAC={true} />}\n        </ComponentInput>\n      );\n    case 'dynamiczone':\n      return (\n        <DynamicZone\n          {...props}\n          hint={hint}\n          labelAction={customLabelAction}\n          disabled={fieldIsDisabled}\n        >\n          {(inputProps) => <VersionInputRenderer {...inputProps} shouldIgnoreRBAC={true} />}\n        </DynamicZone>\n      );\n    case 'relation':\n      return (\n        <CustomRelationInput\n          {...props}\n          hint={hint}\n          labelAction={customLabelAction}\n          disabled={fieldIsDisabled}\n        />\n      );\n    case 'richtext':\n      return (\n        <Wysiwyg\n          {...props}\n          hint={hint}\n          type={props.type}\n          labelAction={customLabelAction}\n          disabled={fieldIsDisabled}\n        />\n      );\n    case 'uid':\n      return (\n        <UIDInput\n          {...props}\n          hint={hint}\n          type={props.type}\n          labelAction={customLabelAction}\n          disabled={fieldIsDisabled}\n        />\n      );\n    /**\n     * Enumerations are a special case because they require options.\n     */\n    case 'enumeration':\n      return (\n        <FormInputRenderer\n          {...props}\n          hint={hint}\n          labelAction={customLabelAction}\n          options={props.attribute.enum.map((value) => ({ value }))}\n          // @ts-expect-error – Temp workaround so we don't forget custom-fields don't work!\n          type={props.customField ? 'custom-field' : props.type}\n          disabled={fieldIsDisabled}\n        />\n      );\n    default:\n      // These props are not needed for the generic form input renderer.\n      const { unique: _unique, mainField: _mainField, ...restProps } = props;\n      return (\n        <FormInputRenderer\n          {...restProps}\n          hint={hint}\n          labelAction={customLabelAction}\n          // @ts-expect-error – Temp workaround so we don't forget custom-fields don't work!\n          type={props.customField ? 'custom-field' : props.type}\n          disabled={fieldIsDisabled}\n        />\n      );\n  }\n};\n\nconst attributeHasCustomFieldProperty = (\n  attribute: Schema.Attribute.AnyAttribute\n): attribute is Schema.Attribute.AnyAttribute & Schema.Attribute.CustomField<string> =>\n  'customField' in attribute && typeof attribute.customField === 'string';\n\nexport type { VersionInputRendererProps };\nexport { VersionInputRenderer };\n", "import * as React from 'react';\n\nimport { Form, Layouts } from '@strapi/admin/strapi-admin';\nimport { Box, Divider, Flex, Grid, Typography } from '@strapi/design-system';\nimport pipe from 'lodash/fp/pipe';\nimport { useIntl } from 'react-intl';\n\nimport { useDoc } from '../../hooks/useDocument';\nimport { useTypedSelector } from '../../modules/hooks';\nimport {\n  prepareTempKeys,\n  removeFieldsThatDontExistOnSchema,\n} from '../../pages/EditView/utils/data';\nimport { HistoryContextValue, useHistoryContext } from '../pages/History';\n\nimport { VersionInputRenderer } from './VersionInputRenderer';\n\nimport type { Metadatas } from '../../../../shared/contracts/content-types';\nimport type { GetInitData } from '../../../../shared/contracts/init';\nimport type { ComponentsDictionary, Document } from '../../hooks/useDocument';\nimport type { EditFieldLayout } from '../../hooks/useDocumentLayout';\nimport type { Schema } from '@strapi/types';\n\nconst createLayoutFromFields = <T extends EditFieldLayout | UnknownField>(fields: T[]) => {\n  return (\n    fields\n      .reduce<Array<T[]>>((rows, field) => {\n        if (field.type === 'dynamiczone') {\n          // Dynamic zones take up all the columns in a row\n          rows.push([field]);\n\n          return rows;\n        }\n\n        if (!rows[rows.length - 1]) {\n          // Create a new row if there isn't one available\n          rows.push([]);\n        }\n\n        // Push fields to the current row, they wrap and handle their own column size\n        rows[rows.length - 1].push(field);\n\n        return rows;\n      }, [])\n      // Map the rows to panels\n      .map((row) => [row])\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * getRemainingFieldsLayout\n * -----------------------------------------------------------------------------------------------*/\n\ninterface GetRemainingFieldsLayoutOptions\n  extends Pick<HistoryContextValue, 'layout'>,\n    Pick<GetInitData.Response['data'], 'fieldSizes'> {\n  schemaAttributes: HistoryContextValue['schema']['attributes'];\n  metadatas: Metadatas;\n}\n\n/**\n * Build a layout for the fields that are were deleted from the edit view layout\n * via the configure the view page. This layout will be merged with the main one.\n * Those fields would be restored if the user restores the history version, which is why it's\n * important to show them, even if they're not in the normal layout.\n */\nfunction getRemaingFieldsLayout({\n  layout,\n  metadatas,\n  schemaAttributes,\n  fieldSizes,\n}: GetRemainingFieldsLayoutOptions) {\n  const fieldsInLayout = layout.flatMap((panel) =>\n    panel.flatMap((row) => row.flatMap((field) => field.name))\n  );\n  const remainingFields = Object.entries(metadatas).reduce<EditFieldLayout[]>(\n    (currentRemainingFields, [name, field]) => {\n      // Make sure we do not fields that are not visible, e.g. \"id\"\n      if (!fieldsInLayout.includes(name) && field.edit.visible === true) {\n        const attribute = schemaAttributes[name];\n        // @ts-expect-error not sure why attribute causes type error\n        currentRemainingFields.push({\n          attribute,\n          type: attribute.type,\n          visible: true,\n          disabled: true,\n          label: field.edit.label || name,\n          name: name,\n          size: fieldSizes[attribute.type].default ?? 12,\n        });\n      }\n\n      return currentRemainingFields;\n    },\n    []\n  );\n\n  return createLayoutFromFields(remainingFields);\n}\n\n/* -------------------------------------------------------------------------------------------------\n * FormPanel\n * -----------------------------------------------------------------------------------------------*/\n\nconst FormPanel = ({ panel }: { panel: EditFieldLayout[][] }) => {\n  if (panel.some((row) => row.some((field) => field.type === 'dynamiczone'))) {\n    const [row] = panel;\n    const [field] = row;\n\n    return (\n      <Grid.Root key={field.name} gap={4}>\n        <Grid.Item col={12} s={12} xs={12} direction=\"column\" alignItems=\"stretch\">\n          <VersionInputRenderer {...field} />\n        </Grid.Item>\n      </Grid.Root>\n    );\n  }\n\n  return (\n    <Box\n      hasRadius\n      background=\"neutral0\"\n      shadow=\"tableShadow\"\n      paddingLeft={6}\n      paddingRight={6}\n      paddingTop={6}\n      paddingBottom={6}\n      borderColor=\"neutral150\"\n    >\n      <Flex direction=\"column\" alignItems=\"stretch\" gap={6}>\n        {panel.map((row, gridRowIndex) => (\n          <Grid.Root key={gridRowIndex} gap={4}>\n            {row.map(({ size, ...field }) => {\n              return (\n                <Grid.Item\n                  col={size}\n                  key={field.name}\n                  s={12}\n                  xs={12}\n                  direction=\"column\"\n                  alignItems=\"stretch\"\n                >\n                  <VersionInputRenderer {...field} />\n                </Grid.Item>\n              );\n            })}\n          </Grid.Root>\n        ))}\n      </Flex>\n    </Box>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * VersionContent\n * -----------------------------------------------------------------------------------------------*/\n\ntype UnknownField = EditFieldLayout & { shouldIgnoreRBAC: boolean };\n\nconst VersionContent = () => {\n  const { formatMessage } = useIntl();\n  const { fieldSizes } = useTypedSelector((state) => state['content-manager'].app);\n  const version = useHistoryContext('VersionContent', (state) => state.selectedVersion);\n  const layout = useHistoryContext('VersionContent', (state) => state.layout);\n  const configuration = useHistoryContext('VersionContent', (state) => state.configuration);\n  const schema = useHistoryContext('VersionContent', (state) => state.schema);\n\n  // Build a layout for the unknown fields section\n  const removedAttributes = version.meta.unknownAttributes.removed;\n  const removedAttributesAsFields = Object.entries(removedAttributes).map(\n    ([attributeName, attribute]) => {\n      const field = {\n        attribute,\n        shouldIgnoreRBAC: true,\n        type: attribute.type,\n        visible: true,\n        disabled: true,\n        label: attributeName,\n        name: attributeName,\n        size: fieldSizes[attribute.type].default ?? 12,\n      } as UnknownField;\n\n      return field;\n    }\n  );\n  const unknownFieldsLayout = createLayoutFromFields(removedAttributesAsFields);\n\n  // Build a layout for the fields that are were deleted from the layout\n  const remainingFieldsLayout = getRemaingFieldsLayout({\n    metadatas: configuration.contentType.metadatas,\n    layout,\n    schemaAttributes: schema.attributes,\n    fieldSizes,\n  });\n\n  const { components } = useDoc();\n\n  /**\n   * Transform the data before passing it to the form so that each field\n   * has a uniquely generated key\n   */\n  const transformedData = React.useMemo(() => {\n    const transform =\n      (schemaAttributes: Schema.Attributes, components: ComponentsDictionary = {}) =>\n      (document: Omit<Document, 'id'>) => {\n        const schema = { attributes: schemaAttributes };\n\n        const transformations = pipe(\n          removeFieldsThatDontExistOnSchema(schema),\n          prepareTempKeys(schema, components)\n        );\n        return transformations(document);\n      };\n\n    return transform(version.schema, components)(version.data);\n  }, [components, version.data, version.schema]);\n\n  return (\n    <Layouts.Content>\n      <Box paddingBottom={8}>\n        <Form disabled={true} method=\"PUT\" initialValues={transformedData}>\n          <Flex direction=\"column\" alignItems=\"stretch\" gap={6} position=\"relative\">\n            {[...layout, ...remainingFieldsLayout].map((panel, index) => {\n              return <FormPanel key={index} panel={panel} />;\n            })}\n          </Flex>\n        </Form>\n      </Box>\n      {removedAttributesAsFields.length > 0 && (\n        <>\n          <Divider />\n          <Box paddingTop={8}>\n            <Flex direction=\"column\" alignItems=\"flex-start\" paddingBottom={6} gap={1}>\n              <Typography variant=\"delta\">\n                {formatMessage({\n                  id: 'content-manager.history.content.unknown-fields.title',\n                  defaultMessage: 'Unknown fields',\n                })}\n              </Typography>\n              <Typography variant=\"pi\">\n                {formatMessage(\n                  {\n                    id: 'content-manager.history.content.unknown-fields.message',\n                    defaultMessage:\n                      'These fields have been deleted or renamed in the Content-Type Builder. <b>These fields will not be restored.</b>',\n                  },\n                  {\n                    b: (chunks: React.ReactNode) => (\n                      <Typography variant=\"pi\" fontWeight=\"bold\">\n                        {chunks}\n                      </Typography>\n                    ),\n                  }\n                )}\n              </Typography>\n            </Flex>\n            <Form disabled={true} method=\"PUT\" initialValues={version.data}>\n              <Flex direction=\"column\" alignItems=\"stretch\" gap={6} position=\"relative\">\n                {unknownFieldsLayout.map((panel, index) => {\n                  return <FormPanel key={index} panel={panel} />;\n                })}\n              </Flex>\n            </Form>\n          </Box>\n        </>\n      )}\n    </Layouts.Content>\n  );\n};\n\nexport { VersionContent, getRemaingFieldsLayout };\n", "import {\n  GetHistoryVersions,\n  RestoreHistoryVersion,\n} from '../../../../shared/contracts/history-versions';\nimport { COLLECTION_TYPES } from '../../constants/collections';\nimport { contentManagerApi } from '../../services/api';\n\nimport type { Data } from '@strapi/types';\n\ninterface RestoreVersion extends RestoreHistoryVersion.Request {\n  documentId: Data.ID;\n  collectionType?: string;\n}\n\nconst historyVersionsApi = contentManagerApi.injectEndpoints({\n  endpoints: (builder) => ({\n    getHistoryVersions: builder.query<\n      GetHistoryVersions.Response,\n      GetHistoryVersions.Request['query']\n    >({\n      query(params) {\n        return {\n          url: `/content-manager/history-versions`,\n          method: 'GET',\n          config: {\n            params,\n          },\n        };\n      },\n      providesTags: ['HistoryVersion'],\n    }),\n    restoreVersion: builder.mutation<RestoreHistoryVersion.Response, RestoreVersion>({\n      query({ params, body }) {\n        return {\n          url: `/content-manager/history-versions/${params.versionId}/restore`,\n          method: 'PUT',\n          data: body,\n        };\n      },\n      invalidatesTags: (_res, _error, { documentId, collectionType, params }) => {\n        return [\n          'HistoryVersion',\n          {\n            type: 'Document',\n            id:\n              collectionType === COLLECTION_TYPES\n                ? `${params.contentType}_${documentId}`\n                : params.contentType,\n          },\n        ];\n      },\n    }),\n  }),\n});\n\nconst { useGetHistoryVersionsQuery, useRestoreVersionMutation } = historyVersionsApi;\n\nexport { useGetHistoryVersionsQuery, useRestoreVersionMutation };\n", "import * as React from 'react';\n\nimport {\n  ConfirmDialog,\n  useNotification,\n  useQueryParams,\n  useTracking,\n  useRBAC,\n  Layouts,\n  GradientBadge,\n} from '@strapi/admin/strapi-admin';\nimport { Button, Typography, Flex, Link, Dialog } from '@strapi/design-system';\nimport { ArrowLeft, WarningCircle } from '@strapi/icons';\nimport { stringify } from 'qs';\nimport { useIntl } from 'react-intl';\nimport { NavLink, useNavigate, useParams, type To } from 'react-router-dom';\n\nimport { PERMISSIONS } from '../../constants/plugin';\nimport { useHistoryContext } from '../pages/History';\nimport { useRestoreVersionMutation } from '../services/historyVersion';\n\nimport type { UID } from '@strapi/types';\n\ninterface VersionHeaderProps {\n  headerId: string;\n}\n\nexport const VersionHeader = ({ headerId }: VersionHeaderProps) => {\n  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = React.useState(false);\n  const navigate = useNavigate();\n  const { formatMessage, formatDate } = useIntl();\n  const { trackUsage } = useTracking();\n  const { toggleNotification } = useNotification();\n  const [{ query }] = useQueryParams<{\n    plugins?: Record<string, unknown>;\n  }>();\n  const { collectionType, slug } = useParams<{ collectionType: string; slug: UID.ContentType }>();\n  const [restoreVersion, { isLoading }] = useRestoreVersionMutation();\n  const { allowedActions } = useRBAC(PERMISSIONS.map((action) => ({ action, subject: slug })));\n\n  const version = useHistoryContext('VersionHeader', (state) => state.selectedVersion);\n  const mainField = useHistoryContext('VersionHeader', (state) => state.mainField);\n  const schema = useHistoryContext('VersionHeader', (state) => state.schema);\n  const isCurrentVersion = useHistoryContext(\n    'VersionHeader',\n    (state) => state.page === 1 && state.versions.data[0].id === state.selectedVersion.id\n  );\n\n  const mainFieldValue = version.data[mainField];\n\n  const getNextNavigation = (): To => {\n    const pluginsQueryParams = stringify({ plugins: query.plugins }, { encode: false });\n\n    return {\n      pathname: '..',\n      search: pluginsQueryParams,\n    };\n  };\n\n  const handleRestore = async () => {\n    try {\n      const response = await restoreVersion({\n        documentId: version.relatedDocumentId,\n        collectionType,\n        params: {\n          versionId: version.id,\n          contentType: version.contentType,\n        },\n        body: { contentType: version.contentType },\n      });\n\n      if ('data' in response) {\n        navigate(getNextNavigation(), { relative: 'path' });\n\n        toggleNotification({\n          type: 'success',\n          title: formatMessage({\n            id: 'content-manager.restore.success.title',\n            defaultMessage: 'Version restored.',\n          }),\n          message: formatMessage({\n            id: 'content-manager.restore.success.message',\n            defaultMessage: 'A past version of the content was restored.',\n          }),\n        });\n\n        trackUsage('didRestoreHistoryVersion');\n      }\n\n      if ('error' in response) {\n        toggleNotification({\n          type: 'danger',\n          message: formatMessage({\n            id: 'content-manager.history.restore.error.message',\n            defaultMessage: 'Could not restore version.',\n          }),\n        });\n      }\n    } catch (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occurred' }),\n      });\n    }\n  };\n\n  return (\n    <Dialog.Root open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>\n      <Layouts.BaseHeader\n        id={headerId}\n        title={formatDate(new Date(version.createdAt), {\n          year: 'numeric',\n          month: 'numeric',\n          day: 'numeric',\n          hour: 'numeric',\n          minute: 'numeric',\n        })}\n        secondaryAction={\n          <GradientBadge\n            label={formatMessage({\n              id: 'components.premiumFeature.title',\n              defaultMessage: 'Premium feature',\n            })}\n          />\n        }\n        subtitle={\n          <Typography variant=\"epsilon\" textColor=\"neutral600\">\n            {formatMessage(\n              {\n                id: 'content-manager.history.version.subtitle',\n                defaultMessage:\n                  '{hasLocale, select, true {{subtitle}, in {locale}} other {{subtitle}}}',\n              },\n              {\n                hasLocale: Boolean(version.locale),\n                subtitle: `${mainFieldValue || ''} (${schema.info.singularName})`.trim(),\n                locale: version.locale?.name,\n              }\n            )}\n          </Typography>\n        }\n        navigationAction={\n          <Link\n            startIcon={<ArrowLeft />}\n            tag={NavLink}\n            to={getNextNavigation()}\n            relative=\"path\"\n            isExternal={false}\n          >\n            {formatMessage({\n              id: 'global.back',\n              defaultMessage: 'Back',\n            })}\n          </Link>\n        }\n        sticky={false}\n        primaryAction={\n          <Dialog.Trigger>\n            <Button\n              disabled={!allowedActions.canUpdate || isCurrentVersion}\n              onClick={() => {\n                setIsConfirmDialogOpen(true);\n              }}\n            >\n              {formatMessage({\n                id: 'content-manager.history.restore.confirm.button',\n                defaultMessage: 'Restore',\n              })}\n            </Button>\n          </Dialog.Trigger>\n        }\n      />\n      <ConfirmDialog\n        onConfirm={handleRestore}\n        endAction={\n          <Button variant=\"secondary\" onClick={handleRestore} loading={isLoading}>\n            {formatMessage({\n              id: 'content-manager.history.restore.confirm.button',\n              defaultMessage: 'Restore',\n            })}\n          </Button>\n        }\n      >\n        <Flex\n          direction=\"column\"\n          alignItems=\"center\"\n          justifyContent=\"center\"\n          gap={2}\n          textAlign=\"center\"\n        >\n          <Flex justifyContent=\"center\">\n            <WarningCircle width=\"24px\" height=\"24px\" fill=\"danger600\" />\n          </Flex>\n          <Typography>\n            {formatMessage({\n              id: 'content-manager.history.restore.confirm.title',\n              defaultMessage: 'Are you sure you want to restore this version?',\n            })}\n          </Typography>\n          <Typography>\n            {formatMessage(\n              {\n                id: 'content-manager.history.restore.confirm.message',\n                defaultMessage:\n                  \"{isDraft, select, true {The restored content will override your draft.} other {The restored content won't be published, it will override the draft and be saved as pending changes. You'll be able to publish the changes at anytime.}}\",\n              },\n              {\n                isDraft: version.status === 'draft',\n              }\n            )}\n          </Typography>\n        </Flex>\n      </ConfirmDialog>\n    </Dialog.Root>\n  );\n};\n", "import * as React from 'react';\n\nimport { useQueryParams } from '@strapi/admin/strapi-admin';\nimport { Box, Flex, Typography } from '@strapi/design-system';\nimport { stringify } from 'qs';\nimport { useIntl } from 'react-intl';\nimport { Link } from 'react-router-dom';\n\nimport { RelativeTime } from '../../components/RelativeTime';\nimport { DocumentStatus } from '../../pages/EditView/components/DocumentStatus';\nimport { getDisplayName } from '../../utils/users';\nimport { useHistoryContext } from '../pages/History';\n\nimport type { HistoryVersions } from '../../../../shared/contracts';\n\n/* -------------------------------------------------------------------------------------------------\n * BlueText\n * -----------------------------------------------------------------------------------------------*/\n\nconst BlueText = (children: React.ReactNode) => (\n  <Typography textColor=\"primary600\" variant=\"pi\">\n    {children}\n  </Typography>\n);\n\n/* -------------------------------------------------------------------------------------------------\n * VersionCard\n * -----------------------------------------------------------------------------------------------*/\n\ninterface VersionCardProps {\n  version: HistoryVersions.HistoryVersionDataResponse;\n  isCurrent: boolean;\n}\n\nconst VersionCard = ({ version, isCurrent }: VersionCardProps) => {\n  const { formatDate, formatMessage } = useIntl();\n  const [{ query }] = useQueryParams<{ id?: string }>();\n  const isActive = query.id === version.id.toString();\n  const author = version.createdBy && getDisplayName(version.createdBy);\n\n  return (\n    <Flex\n      direction=\"column\"\n      alignItems=\"flex-start\"\n      gap={3}\n      hasRadius\n      borderWidth=\"1px\"\n      borderStyle=\"solid\"\n      borderColor={isActive ? 'primary600' : 'neutral200'}\n      color=\"neutral800\"\n      padding={5}\n      tag={Link}\n      to={`?${stringify({ ...query, id: version.id })}`}\n      style={{ textDecoration: 'none' }}\n    >\n      <Flex direction=\"column\" gap={1} alignItems=\"flex-start\">\n        <Typography tag=\"h3\" fontWeight=\"semiBold\">\n          {formatDate(version.createdAt, {\n            day: 'numeric',\n            month: 'numeric',\n            year: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit',\n          })}\n        </Typography>\n        <Typography tag=\"p\" variant=\"pi\" textColor=\"neutral600\">\n          {formatMessage(\n            {\n              id: 'content-manager.history.sidebar.versionDescription',\n              defaultMessage:\n                '{distanceToNow}{isAnonymous, select, true {} other { by {author}}}{isCurrent, select, true { <b>(current)</b>} other {}}',\n            },\n            {\n              distanceToNow: <RelativeTime timestamp={new Date(version.createdAt)} />,\n              author,\n              isAnonymous: !Boolean(version.createdBy),\n              isCurrent,\n              b: BlueText,\n            }\n          )}\n        </Typography>\n      </Flex>\n      {version.status && <DocumentStatus status={version.status} size=\"XS\" />}\n    </Flex>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * PaginationButton\n * -----------------------------------------------------------------------------------------------*/\n\ninterface PaginationButtonProps {\n  page: number;\n  children: React.ReactNode;\n}\n\nconst PaginationButton = ({ page, children }: PaginationButtonProps) => {\n  const [{ query }] = useQueryParams<{ id?: string }>();\n\n  // Remove the id from the pagination link, so that the history page can redirect\n  // to the id of the first history version in the new page once it's loaded\n  const { id: _id, ...queryRest } = query;\n\n  return (\n    <Link to={{ search: stringify({ ...queryRest, page }) }} style={{ textDecoration: 'none' }}>\n      <Typography variant=\"omega\" textColor=\"primary600\">\n        {children}\n      </Typography>\n    </Link>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * VersionsList\n * -----------------------------------------------------------------------------------------------*/\n\nconst VersionsList = () => {\n  const { formatMessage } = useIntl();\n  const { versions, page } = useHistoryContext('VersionsList', (state) => ({\n    versions: state.versions,\n    page: state.page,\n  }));\n\n  return (\n    <Flex\n      shrink={0}\n      direction=\"column\"\n      alignItems=\"stretch\"\n      width=\"320px\"\n      height=\"100vh\"\n      background=\"neutral0\"\n      borderColor=\"neutral200\"\n      borderWidth=\"0 0 0 1px\"\n      borderStyle=\"solid\"\n      tag=\"aside\"\n    >\n      <Flex\n        direction=\"row\"\n        justifyContent=\"space-between\"\n        padding={4}\n        borderColor=\"neutral200\"\n        borderWidth=\"0 0 1px\"\n        borderStyle=\"solid\"\n        tag=\"header\"\n      >\n        <Typography tag=\"h2\" variant=\"omega\" fontWeight=\"semiBold\">\n          {formatMessage({\n            id: 'content-manager.history.sidebar.title',\n            defaultMessage: 'Versions',\n          })}\n        </Typography>\n        <Box background=\"neutral150\" hasRadius padding={1}>\n          <Typography variant=\"sigma\" textColor=\"neutral600\">\n            {versions.meta.pagination.total}\n          </Typography>\n        </Box>\n      </Flex>\n      <Box flex={1} overflow=\"auto\">\n        {versions.meta.pagination.page > 1 && (\n          <Box paddingTop={4} textAlign=\"center\">\n            <PaginationButton page={page - 1}>\n              {formatMessage({\n                id: 'content-manager.history.sidebar.show-newer',\n                defaultMessage: 'Show newer versions',\n              })}\n            </PaginationButton>\n          </Box>\n        )}\n        <Flex direction=\"column\" gap={3} padding={4} tag=\"ul\" alignItems=\"stretch\">\n          {versions.data.map((version, index) => (\n            <li\n              key={version.id}\n              aria-label={formatMessage({\n                id: 'content-manager.history.sidebar.title.version-card.aria-label',\n                defaultMessage: 'Version card',\n              })}\n            >\n              <VersionCard version={version} isCurrent={page === 1 && index === 0} />\n            </li>\n          ))}\n        </Flex>\n        {versions.meta.pagination.page < versions.meta.pagination.pageCount && (\n          <Box paddingBottom={4} textAlign=\"center\">\n            <PaginationButton page={page + 1}>\n              {formatMessage({\n                id: 'content-manager.history.sidebar.show-older',\n                defaultMessage: 'Show older versions',\n              })}\n            </PaginationButton>\n          </Box>\n        )}\n      </Box>\n    </Flex>\n  );\n};\n\nexport { VersionsList };\n", "import * as React from 'react';\n\nimport { useQ<PERSON>yParams, Page, createContext, useRBAC } from '@strapi/admin/strapi-admin';\nimport { Box, Flex, FocusTrap, Main, Portal, Link } from '@strapi/design-system';\nimport { stringify } from 'qs';\nimport { useIntl } from 'react-intl';\nimport { Navigate, useParams, NavLink } from 'react-router-dom';\n\nimport { COLLECTION_TYPES } from '../../constants/collections';\nimport { PERMISSIONS } from '../../constants/plugin';\nimport { DocumentRBAC } from '../../features/DocumentRBAC';\nimport { useDocument } from '../../hooks/useDocument';\nimport { type EditLayout, useDocumentLayout } from '../../hooks/useDocumentLayout';\nimport { useGetContentTypeConfigurationQuery } from '../../services/contentTypes';\nimport { buildValidParams } from '../../utils/api';\nimport { VersionContent } from '../components/VersionContent';\nimport { VersionHeader } from '../components/VersionHeader';\nimport { VersionsList } from '../components/VersionsList';\nimport { useGetHistoryVersionsQuery } from '../services/historyVersion';\n\nimport type {\n  ContentType,\n  FindContentTypeConfiguration,\n} from '../../../../shared/contracts/content-types';\nimport type {\n  HistoryVersionDataResponse,\n  GetHistoryVersions,\n} from '../../../../shared/contracts/history-versions';\nimport type { UID } from '@strapi/types';\n\n/* -------------------------------------------------------------------------------------------------\n * HistoryProvider\n * -----------------------------------------------------------------------------------------------*/\n\ninterface HistoryContextValue {\n  contentType: UID.ContentType;\n  id?: string; // null for single types\n  layout: EditLayout['layout'];\n  configuration: FindContentTypeConfiguration.Response['data'];\n  selectedVersion: HistoryVersionDataResponse;\n  // Errors are handled outside of the provider, so we exclude errors from the response type\n  versions: Extract<GetHistoryVersions.Response, { data: Array<HistoryVersionDataResponse> }>;\n  page: number;\n  mainField: string;\n  schema: ContentType;\n}\n\nconst [HistoryProvider, useHistoryContext] = createContext<HistoryContextValue>('HistoryPage');\n\n/* -------------------------------------------------------------------------------------------------\n * HistoryPage\n * -----------------------------------------------------------------------------------------------*/\n\nconst HistoryPage = () => {\n  const headerId = React.useId();\n  const { formatMessage } = useIntl();\n  const {\n    slug,\n    id: documentId,\n    collectionType,\n  } = useParams<{\n    collectionType: string;\n    slug: UID.ContentType;\n    id: string;\n  }>();\n\n  const { isLoading: isLoadingDocument, schema } = useDocument({\n    collectionType: collectionType!,\n    model: slug!,\n  });\n\n  const {\n    isLoading: isLoadingLayout,\n    edit: {\n      layout,\n      settings: { displayName, mainField },\n    },\n  } = useDocumentLayout(slug!);\n  const { data: configuration, isLoading: isLoadingConfiguration } =\n    useGetContentTypeConfigurationQuery(slug!);\n\n  // Parse state from query params\n  const [{ query }] = useQueryParams<{\n    page?: number;\n    id?: string;\n    plugins?: Record<string, unknown>;\n  }>();\n  const { id: selectedVersionId, ...queryWithoutId } = query;\n  const validQueryParamsWithoutId = buildValidParams(queryWithoutId);\n  const page = validQueryParamsWithoutId.page ? Number(validQueryParamsWithoutId.page) : 1;\n\n  const versionsResponse = useGetHistoryVersionsQuery(\n    {\n      contentType: slug!,\n      ...(documentId ? { documentId } : {}),\n      // Omit id since it's not needed by the endpoint and caused extra refetches\n      ...validQueryParamsWithoutId,\n    },\n    { refetchOnMountOrArgChange: true }\n  );\n\n  /**\n   * When the page is first mounted, if there's already data in the cache, RTK has a fullfilled\n   * status for the first render, right before it triggers a new request. This means the code\n   * briefly reaches the part that redirects to the first history version (if none is set).\n   * But since that data is stale, that means auto-selecting a version that may not be the most\n   * recent. To avoid this, we identify through requestId if the query is stale despite the\n   * fullfilled status, and show the loader in that case.\n   * This means we essentially don't want cache. We always refetch when the page mounts, and\n   * we always show the loader until we have the most recent data. That's fine for this page.\n   */\n  const initialRequestId = React.useRef(versionsResponse.requestId);\n  const isStaleRequest = versionsResponse.requestId === initialRequestId.current;\n\n  /**\n   * Ensure that we have the necessary data to render the page:\n   * - slug for single types\n   * - slug _and_ documentId for collection types\n   */\n  if (!slug || (collectionType === COLLECTION_TYPES && !documentId)) {\n    return <Navigate to=\"/content-manager\" />;\n  }\n\n  if (\n    isLoadingDocument ||\n    isLoadingLayout ||\n    versionsResponse.isFetching ||\n    isStaleRequest ||\n    isLoadingConfiguration\n  ) {\n    return <Page.Loading />;\n  }\n\n  // It was a success, handle empty data\n  if (!versionsResponse.isError && !versionsResponse.data?.data?.length) {\n    return (\n      <>\n        <Page.NoData\n          action={\n            <Link\n              tag={NavLink}\n              to={`/content-manager/${collectionType}/${slug}${documentId ? `/${documentId}` : ''}`}\n            >\n              {formatMessage({\n                id: 'global.back',\n                defaultMessage: 'Back',\n              })}\n            </Link>\n          }\n        />\n      </>\n    );\n  }\n\n  // We have data, handle selected version\n  if (versionsResponse.data?.data?.length && !selectedVersionId) {\n    return (\n      <Navigate\n        to={{ search: stringify({ ...query, id: versionsResponse.data.data[0].id }) }}\n        replace\n      />\n    );\n  }\n\n  const selectedVersion = versionsResponse.data?.data?.find(\n    (version) => version.id.toString() === selectedVersionId\n  );\n  if (\n    versionsResponse.isError ||\n    !layout ||\n    !schema ||\n    !selectedVersion ||\n    !configuration ||\n    // This should not happen as it's covered by versionsResponse.isError, but we need it for TS\n    versionsResponse.data.error\n  ) {\n    return <Page.Error />;\n  }\n\n  return (\n    <>\n      <Page.Title>\n        {formatMessage(\n          {\n            id: 'content-manager.history.page-title',\n            defaultMessage: '{contentType} history',\n          },\n          {\n            contentType: displayName,\n          }\n        )}\n      </Page.Title>\n      <HistoryProvider\n        contentType={slug}\n        id={documentId}\n        schema={schema}\n        layout={layout}\n        configuration={configuration}\n        selectedVersion={selectedVersion}\n        versions={versionsResponse.data}\n        page={page}\n        mainField={mainField}\n      >\n        <Flex direction=\"row\" alignItems=\"flex-start\">\n          <Main\n            grow={1}\n            height=\"100vh\"\n            background=\"neutral100\"\n            paddingBottom={6}\n            overflow=\"auto\"\n            labelledBy={headerId}\n          >\n            <VersionHeader headerId={headerId} />\n\n            <VersionContent />\n          </Main>\n          <VersionsList />\n        </Flex>\n      </HistoryProvider>\n    </>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ProtectedHistoryPage\n * -----------------------------------------------------------------------------------------------*/\n\nconst ProtectedHistoryPageImpl = () => {\n  const { slug } = useParams<{\n    slug: string;\n  }>();\n  const {\n    permissions = [],\n    isLoading,\n    error,\n  } = useRBAC(PERMISSIONS.map((action) => ({ action, subject: slug })));\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  if (error || !slug) {\n    return (\n      <Box\n        height=\"100vh\"\n        width=\"100vw\"\n        position=\"fixed\"\n        top={0}\n        left={0}\n        zIndex={2}\n        background=\"neutral0\"\n      >\n        <Page.Error />\n      </Box>\n    );\n  }\n\n  return (\n    <Box\n      height=\"100vh\"\n      width=\"100vw\"\n      position=\"fixed\"\n      top={0}\n      left={0}\n      zIndex={2}\n      background=\"neutral0\"\n    >\n      <Page.Protect permissions={permissions}>\n        {({ permissions }) => (\n          <DocumentRBAC permissions={permissions}>\n            <HistoryPage />\n          </DocumentRBAC>\n        )}\n      </Page.Protect>\n    </Box>\n  );\n};\n\nconst ProtectedHistoryPage = () => {\n  return (\n    <Portal>\n      <FocusTrap>\n        <ProtectedHistoryPageImpl />\n      </FocusTrap>\n    </Portal>\n  );\n};\n\nexport { ProtectedHistoryPage, HistoryProvider, useHistoryContext };\nexport type { HistoryContextValue };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CA,IAAMA,cAAcC,GAAOC,KAAOC,EAAAA,MAAM;EAAEC,YAAY;EAASC,SAAS,MAAO;EAAA;EAAGC,QAAQ;AAAO,CAAA;;;;;AAUjG,IAAMC,eAAeN,GAAOO,KAAAA;;;;;;;;;;AAW5B,IAAMC,sBAAsB,CAACC,UAAAA;AAC3B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAMC,QAAQC,SAEZJ,MAAMK,IAAI;AAMZ,MAAIC;AACJ,MAAIH,OAAO;AACTG,0BAAsBC,MAAMC,QAAQL,MAAMM,KAAK,IAC3C;MAAEC,SAASP,MAAMM;MAAOE,MAAM;QAAEC,cAAc;MAAE;IAAE,IAClDT,MAAMM;EACZ;AAEA,MACE,CAACH,uBACAA,oBAAoBI,QAAQG,WAAW,KAAKP,oBAAoBK,KAAKC,iBAAiB,GACvF;AACA,eACEE,yBAAAC,6BAAA;;YACEC,wBAACC,MAAMC,OAAK;UAACC,QAAQnB,MAAMoB;UAAcpB,UAAAA,MAAMqB;;YAC/CL,wBAACM,KAAAA;UAAIC,WAAW;UAEd,cAAAP,wBAAC1B,aAAAA;YAAYkC,SAAQ;sBAClBvB,cAAc;cACbwB,IAAI;cACJC,gBAAgB;YAClB,CAAA;;;;;EAKV;AAEA,QAAM,EAAEhB,SAASC,KAAI,IAAKL;AAE1B,aACEQ,yBAACQ,KAAAA;;UACCN,wBAACC,MAAMC,OAAK;QAAElB,UAAAA,MAAMqB;;MACnBX,QAAQG,SAAS,SAChBG,wBAACW,MAAAA;QAAKC,WAAU;QAASC,KAAK;QAAGN,WAAW;QAAGO,YAAW;kBACvDpB,QAAQqB,IAAI,CAACC,iBAAAA;AAEZ,gBAAM,EAAEC,YAAW,IAAKjC,MAAMkC;AAC9B,gBAAMC,OAAO,MAAMC,gBAAiB,IAAGH,WAAAA,IAAeD,aAAaK,UAAU;AAC7E,gBAAMhB,QAAQiB,iBAAiBN,cAAchC,MAAMuC,SAAS;AAC5D,gBAAMC,sBAAsBP,gBAAgB;AAE5C,qBACEnB,yBAACa,MAAAA;YAECc,YAAY;YACZC,eAAe;YACfC,aAAa;YACbC,cAAc;YACdC,WAAS;YACTC,aAAY;YACZC,YAAW;YACXC,gBAAe;;kBAEfhC,wBAACM,KAAAA;gBAAI2B,UAAU;gBAAGR,YAAY;gBAAGC,eAAe;gBAAGE,cAAc;gBAC/D,cAAA5B,wBAACkC,aAAAA;kBAAQ7B;kBACNmB,UAAAA,0BACCxB,wBAACmC,YAAAA;oBAAY9B,UAAAA;2BAEbL,wBAACnB,cAAAA;oBAAauD,KAAKC;oBAASC,IAAInB;oBAC7Bd,UAAAA;;;;kBAKTL,wBAACuC,gBAAAA;gBAAeC,QAAQxB,aAAawB;;;aArBhCxB,aAAaK,cAAcL,aAAaP,EAAE;QAwBrD,CAAA;;MAGHd,KAAKC,eAAe;UAEnBI,wBAAC1B,aAAAA;QACCiC,WAAW;QACXC,SAAQ;QACRiC,OAAOxD,cACL;UACEwB,IAAI;UACJC,gBACE;WAEJ;UAAEgC,QAAQ/C,KAAKC;QAAa,CAAA;kBAG7BX,cACC;UACEwB,IAAI;UACJC,gBACE;WAEJ;UAAEgC,QAAQ/C,KAAKC;QAAa,CAAA;;;;AAMxC;AAOA,IAAM+C,6BAA6B,CAACC,SAAiBnD,UAAAA;AACnD,QAAMoD,OAAOD,QAAQE,MAAM,GAAA;AAE3B,QAAMC,OAA4B,CAAA;AAGlC,MAAIC,OAAOD;AACXF,OAAKI,QAAQ,CAACC,KAAKC,UAAAA;AAEjB,QAAID,QAAQ,eAAeA,QAAQ,cAAe;AAElD,QAAIC,UAAUN,KAAKhD,SAAS,GAAG;AAC7BmD,WAAKE,GAAAA,IAAOzD;WACP;AAELuD,WAAKE,GAAI,IAAGF,KAAKE,GAAAA,KAAQ,CAAA;IAC3B;AAGAF,WAAOA,KAAKE,GAAI;EAClB,CAAA;AAEA,SAAOH;AACT;AAEA,IAAMK,mBAAmB,CAACpE,UAAAA;AACxB,QAAM,EAAES,MAAK,IAAKL,SAASJ,MAAMK,IAAI;AACrC,QAAMK,WAAUD,+BAAOC,YAAW,CAAA;AAClC,QAAMC,QAAOF,+BAAOE,SAAQ;IAAEC,cAAc;EAAE;AAE9C,QAAM,EAAEX,cAAa,IAAKC,QAAAA;AAE1B,QAAMmE,SAASC,aAAa,oBAAoB,CAACC,UAAUA,MAAMF,MAAM;AACvE,QAAMG,eAAeH,OAAOI;AAI5B,aACE3D,yBAACa,MAAAA;IAAKC,WAAU;IAASC,KAAK;IAAGC,YAAW;;UAC1Cd,wBAAC0D,MAAAA;QACCC,QAAO;QACPC,UAAU;QACVC,eAAelB,2BAA2B3D,MAAMK,MAAMK,OAAAA;QAEtD,cAAAM,wBAACwD,cAAAA;UAAc,GAAGxE;UAAO4E,UAAU;UAAME,UAAUpE,QAAQG,SAAS;;;MAErEF,KAAKC,eAAe,SACnBI,wBAAC1B,aAAAA;QACCkC,SAAQ;QACR9B,YAAW;QACXC,SAAS,MAAO;QAAA;QAChB8D,OAAOxD,cACL;UACEwB,IAAI;UACJC,gBACE;WAEJ;UAAEgC,QAAQ/C,KAAKC;QAAa,CAAA;kBAG7BX,cACC;UACEwB,IAAI;UACJC,gBACE;WAEJ;UAAEgC,QAAQ/C,KAAKC;QAAa,CAAA;;;;AAMxC;AAcA,IAAMmE,iBAAiB,CAAC3D,gBAAAA;AACtB,MAAI,CAAO4D,qBAAe5D,WAAc,GAAA;AACtC,WAAOA;EACT;AAGA,QAAM6D,qBAAqB7D,YAAYpB,MAAMyD,MAAMhC;AAEnD,MAAIwD,uBAAuB,wBAAwB;AACjD,WAAaC,mBAAa9D,aAAa;MACrC,GAAGA,YAAYpB;MACfyD,OAAO;QACLhC,IAAI;QACJC,gBACE;MACJ;IACF,CAAA;EACF;AAEA,MAAIuD,uBAAuB,4BAA4B;AACrD,WAAaC,mBAAa9D,aAAa;MACrC,GAAGA,YAAYpB;MACfyD,OAAO;QACLhC,IAAI;QACJC,gBACE;MACJ;IACF,CAAA;EACF;AAGA,SAAON;AACT;AASM+D,IAAAA,uBAAuB,CAAC,EAC5BC,SACAC,MAAMC,cACNC,mBAAmB,OACnBnE,aACA,GAAGpB,MACuB,MAAA;AAC1B,QAAMwF,oBAAoBT,eAAe3D,WAAAA;AAEzC,QAAM,EAAEnB,cAAa,IAAKC,QAAAA;AAC1B,QAAMuF,UAAUC,kBAAkB,kBAAkB,CAACnB,UAAUA,MAAMoB,eAAe;AACpF,QAAMC,gBAAgBF,kBAAkB,kBAAkB,CAACnB,UAAUA,MAAMqB,aAAa;AACxF,QAAMC,aAAaC,iBAAiB,CAACvB,UAAUA,MAAM,iBAAkB,EAACwB,IAAIF,UAAU;AAEtF,QAAM,EAAEpE,IAAIuE,WAAU,IAAKC,OAAAA;AAC3B,QAAMC,iBAAiBC,QAAQ,iBAAiB,CAAC5B,UAAUA,MAAMK,QAAQ;AAEzE,QAAMwB,kBAAkBC,eAAe,mBAAmB,CAAC9B,UAAUA,MAAM6B,eAAe;AAE1F,QAAME,kBAAkBC,gBAAgB,iBAAiB,CAACC,SAASA,KAAKF,eAAe;AACvF,QAAMG,gBAAgBF,gBAAgB,iBAAiB,CAACC,SAASA,KAAKC,aAAa;AACnF,QAAMC,kBAAkBH,gBAAgB,iBAAiB,CAACC,SAASA,KAAKE,eAAe;AACvF,QAAMC,gBAAgBJ,gBAAgB,iBAAiB,CAACC,SAASA,KAAKG,aAAa;AAEnF,QAAMC,iBAAiBnF,KAAKiF,kBAAkBJ;AAC9C,QAAMO,iBAAiBpF,KAAKgF,gBAAgBH;AAK5C,QAAMQ,mBAAmBH,cAAc3G,MAAMK,MAAMwG,gBAAgB7G,MAAM+G,IAAI;AAC7E,QAAMC,mBAAmBL,cAAc3G,MAAMK,MAAMuG,gBAAgB5G,MAAM+G,IAAI;AAE7E,QAAM1C,SAASC,aAAa,iBAAiB,CAACyB,QAAQA,IAAI1B,MAAM;AAChE,QAAM,EAAE4C,mBAAkB,IAAKC,kBAC7BC,gCAAgCnH,MAAMkC,SAAS,IAAI;IAAClC,MAAMkC,UAAUkF;MAAeC,MAAAA;AAGrF,QAAMhC,OAAOiC,aAAahC,cAActF,MAAMkC,SAAS;AACvD,QAAM,EACJqF,MAAM,EAAEvB,YAAYwB,iBAAgB,EAAE,IACpCC,aAAAA;AAEJ,MAAI,CAACrC,SAAS;AACZ,WAAO;EACT;AAKA,MAAI,CAACG,oBAAoB,CAACuB,oBAAoB,CAACV,iBAAiB;AAC9D,eAAOpF,wBAAC0G,iBAAAA;MAAgBrC;MAAa,GAAGrF;;EAC1C;AAEA,QAAM2H,kBACJ,CAAEX,oBAAoB,CAACZ,mBAAoBpG,MAAM4E,YAAYsB;AAM/D,QAAM0B,kBAAkBnC,QAAQ9E,KAAKkH,kBAAkBC;AACvD,MAAIC,OAAOlE,KAAK+D,eAAAA,EAAiBI,SAAShI,MAAMK,IAAI,GAAG;AACrD,eACES,yBAACa,MAAAA;MAAKC,WAAU;MAASE,YAAW;MAAaD,KAAK;;YACpDb,wBAACC,MAAMC,OAAK;UAAElB,UAAAA,MAAMqB;;YACpBL,wBAAC1B,aAAAA;UACC2I,OAAM;UACNvI,YAAW;UACXC,SAAS,MAAO;UAAA;UAChB6B,SAAQ;UACRiC,OAAOxD,cAAc;YACnBwB,IAAI;YACJC,gBAAgB;UAClB,CAAA;oBAECzB,cAAc;YACbwB,IAAI;YACJC,gBACE;UACJ,CAAA;;;;EAIR;AAMA,MAAIyF,gCAAgCnH,MAAMkC,SAAS,GAAG;AACpD,UAAMgG,cAAcjB,mBAAmBjH,MAAMkC,UAAUkF,WAAW;AAElE,QAAIc,aAAa;AACf,iBACElH,wBAACkH,aAAAA;QACE,GAAGlI;;QAEJqF;QACAjE,aAAaoE;QACbZ,UAAU+C;;IAGhB;AAEA,eACE3G,wBAACmH,uBAAAA;MACE,GAAGnI;MACJqF;MACAjE,aAAaoE;;MAEbuB,MAAM/G,MAAMkC,UAAUkF;MACtBxC,UAAU+C;;EAGhB;AAMA,MAAI3H,MAAM+G,SAAS,SAAS;AAC1B,eACE/F,wBAACoD,kBAAAA;MAAkB,GAAGpE;MAAOoB,aAAaoE;MAAmBZ,UAAU+C;;EAE3E;AAIA,QAAMS,kBAAkBL,OAAOlE,KAAKQ,MAAAA;AACpC,MAAI,CAAC8C,gCAAgCnH,MAAMkC,SAAS,KAAKkG,gBAAgBJ,SAAShI,MAAM+G,IAAI,GAAG;AAC7F,UAAMmB,cAAc7D,OAAOrE,MAAM+G,IAAI;AACrC,eACE/F,wBAACkH,aAAAA;MACE,GAAGlI;;MAEJqF;MACAjE,aAAaoE;MACbZ,UAAU+C;;EAGhB;AAMA,UAAQ3H,MAAM+G,MAAI;IAChB,KAAK;AACH,iBAAO/F,wBAACqH,qBAAAA;QAAa,GAAGrI;QAAOqF;QAAY0B,MAAM/G,MAAM+G;QAAMnC,UAAU+C;;IACzE,KAAK;AACH,YAAM,EAAEW,OAAM,IAAKd,iBAAiBxH,MAAMkC,UAAUqG,SAAS;AAE7D,YAAM,CAACC,qBAAsB,IAAGC,uBAAuB;QACrDH,QAAQ;UAACA;QAAO;QAChBI,WAAW9C,cAAcI,WAAWhG,MAAMkC,UAAUqG,SAAS,EAAEG;QAC/D7C;QACA8C,kBAAkB3C,WAAWhG,MAAMkC,UAAUqG,SAAS,EAAEK;MAC1D,CAAA;AAEA,iBACE5H,wBAAC6H,wBAAAA;QACE,GAAG7I;QACJsI,QAAQ;UAAIA,GAAAA;UAAYE,GAAAA,yBAAyB,CAAA;QAAI;QACrDnD;QACAjE,aAAaoE;QACbZ,UAAU+C;QAET,UAAA,CAACmB,mBAAe9H,wBAACmE,sBAAAA;UAAsB,GAAG2D;UAAYvD,kBAAkB;;;IAG/E,KAAK;AACH,iBACEvE,wBAAC+H,aAAAA;QACE,GAAG/I;QACJqF;QACAjE,aAAaoE;QACbZ,UAAU+C;QAET,UAAA,CAACmB,mBAAe9H,wBAACmE,sBAAAA;UAAsB,GAAG2D;UAAYvD,kBAAkB;;;IAG/E,KAAK;AACH,iBACEvE,wBAACjB,qBAAAA;QACE,GAAGC;QACJqF;QACAjE,aAAaoE;QACbZ,UAAU+C;;IAGhB,KAAK;AACH,iBACE3G,wBAACgI,iBAAAA;QACE,GAAGhJ;QACJqF;QACA0B,MAAM/G,MAAM+G;QACZ3F,aAAaoE;QACbZ,UAAU+C;;IAGhB,KAAK;AACH,iBACE3G,wBAACiI,kBAAAA;QACE,GAAGjJ;QACJqF;QACA0B,MAAM/G,MAAM+G;QACZ3F,aAAaoE;QACbZ,UAAU+C;;IAMhB,KAAK;AACH,iBACE3G,wBAACmH,uBAAAA;QACE,GAAGnI;QACJqF;QACAjE,aAAaoE;QACb0D,SAASlJ,MAAMkC,UAAUiH,KAAKpH,IAAI,CAACtB,WAAW;UAAEA;UAAM;;QAEtDsG,MAAM/G,MAAMoH,cAAc,iBAAiBpH,MAAM+G;QACjDnC,UAAU+C;;IAGhB;AAEE,YAAM,EAAEyB,QAAQC,SAAS9G,WAAW+G,YAAY,GAAGC,UAAAA,IAAcvJ;AACjE,iBACEgB,wBAACmH,uBAAAA;QACE,GAAGoB;QACJlE;QACAjE,aAAaoE;;QAEbuB,MAAM/G,MAAMoH,cAAc,iBAAiBpH,MAAM+G;QACjDnC,UAAU+C;;EAGlB;AACF;AAEA,IAAMR,kCAAkC,CACtCjF,cAEA,iBAAiBA,aAAa,OAAOA,UAAUkF,gBAAgB;;;AC9gBjE,IAAMoC,yBAAyB,CAA2CC,WAAAA;AACxE,SACEA,OACGC,OAAmB,CAACC,MAAMC,UAAAA;AACzB,QAAIA,MAAMC,SAAS,eAAe;AAEhCF,WAAKG,KAAK;QAACF;MAAM,CAAA;AAEjB,aAAOD;IACT;AAEA,QAAI,CAACA,KAAKA,KAAKI,SAAS,CAAA,GAAI;AAE1BJ,WAAKG,KAAK,CAAA,CAAE;IACd;AAGAH,SAAKA,KAAKI,SAAS,CAAE,EAACD,KAAKF,KAAAA;AAE3B,WAAOD;KACN,CAAA,CAAE,EAEJK,IAAI,CAACC,QAAQ;IAACA;EAAI,CAAA;AAEzB;AAmBA,SAASC,uBAAuB,EAC9BC,QACAC,WACAC,kBACAC,WAAU,GACsB;AAChC,QAAMC,iBAAiBJ,OAAOK,QAAQ,CAACC,UACrCA,MAAMD,QAAQ,CAACP,QAAQA,IAAIO,QAAQ,CAACZ,UAAUA,MAAMc,IAAI,CAAA,CAAA;AAE1D,QAAMC,kBAAkBC,OAAOC,QAAQT,SAAAA,EAAWV,OAChD,CAACoB,wBAAwB,CAACJ,MAAMd,KAAM,MAAA;AAEpC,QAAI,CAACW,eAAeQ,SAASL,IAAAA,KAASd,MAAMoB,KAAKC,YAAY,MAAM;AACjE,YAAMC,YAAYb,iBAAiBK,IAAK;AAExCI,6BAAuBhB,KAAK;QAC1BoB;QACArB,MAAMqB,UAAUrB;QAChBoB,SAAS;QACTE,UAAU;QACVC,OAAOxB,MAAMoB,KAAKI,SAASV;QAC3BA;QACAW,MAAMf,WAAWY,UAAUrB,IAAI,EAAEyB,WAAW;MAC9C,CAAA;IACF;AAEA,WAAOR;EACT,GACA,CAAA,CAAE;AAGJ,SAAOtB,uBAAuBmB,eAAAA;AAChC;AAMA,IAAMY,YAAY,CAAC,EAAEd,MAAK,MAAkC;AAC1D,MAAIA,MAAMe,KAAK,CAACvB,QAAQA,IAAIuB,KAAK,CAAC5B,UAAUA,MAAMC,SAAS,aAAiB,CAAA,GAAA;AAC1E,UAAM,CAACI,GAAAA,IAAOQ;AACd,UAAM,CAACb,KAAAA,IAASK;AAEhB,eACEwB,yBAACC,KAAKC,MAAI;MAAkBC,KAAK;oBAC/BH,yBAACC,KAAKG,MAAI;QAACC,KAAK;QAAIC,GAAG;QAAIC,IAAI;QAAIC,WAAU;QAASC,YAAW;QAC/D,cAAAT,yBAACU,sBAAAA;UAAsB,GAAGvC;;;IAFdA,GAAAA,MAAMc,IAAI;EAM9B;AAEA,aACEe,yBAACW,KAAAA;IACCC,WAAS;IACTC,YAAW;IACXC,QAAO;IACPC,aAAa;IACbC,cAAc;IACdC,YAAY;IACZC,eAAe;IACfC,aAAY;IAEZ,cAAAnB,yBAACoB,MAAAA;MAAKZ,WAAU;MAASC,YAAW;MAAUN,KAAK;MAChDnB,UAAAA,MAAMT,IAAI,CAACC,KAAK6C,qBACfrB,yBAACC,KAAKC,MAAI;QAAoBC,KAAK;QAChC3B,UAAAA,IAAID,IAAI,CAAC,EAAEqB,MAAM,GAAGzB,MAAO,MAAA;AAC1B,qBACE6B,yBAACC,KAAKG,MAAI;YACRC,KAAKT;YAELU,GAAG;YACHC,IAAI;YACJC,WAAU;YACVC,YAAW;YAEX,cAAAT,yBAACU,sBAAAA;cAAsB,GAAGvC;;UANrBA,GAAAA,MAAMc,IAAI;QASrB,CAAA;MAdcoC,GAAAA,YAAAA,CAAAA;;;AAoB1B;AAQA,IAAMC,iBAAiB,MAAA;AACrB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAE3C,WAAU,IAAK4C,iBAAiB,CAACC,UAAUA,MAAM,iBAAkB,EAACC,GAAG;AAC/E,QAAMC,UAAUC,kBAAkB,kBAAkB,CAACH,UAAUA,MAAMI,eAAe;AACpF,QAAMpD,SAASmD,kBAAkB,kBAAkB,CAACH,UAAUA,MAAMhD,MAAM;AAC1E,QAAMqD,gBAAgBF,kBAAkB,kBAAkB,CAACH,UAAUA,MAAMK,aAAa;AACxF,QAAMC,SAASH,kBAAkB,kBAAkB,CAACH,UAAUA,MAAMM,MAAM;AAG1E,QAAMC,oBAAoBL,QAAQM,KAAKC,kBAAkBC;AACzD,QAAMC,4BAA4BlD,OAAOC,QAAQ6C,iBAAAA,EAAmB1D,IAClE,CAAC,CAAC+D,eAAe7C,SAAU,MAAA;AACzB,UAAMtB,QAAQ;MACZsB;MACA8C,kBAAkB;MAClBnE,MAAMqB,UAAUrB;MAChBoB,SAAS;MACTE,UAAU;MACVC,OAAO2C;MACPrD,MAAMqD;MACN1C,MAAMf,WAAWY,UAAUrB,IAAI,EAAEyB,WAAW;IAC9C;AAEA,WAAO1B;EACT,CAAA;AAEF,QAAMqE,sBAAsBzE,uBAAuBsE,yBAAAA;AAGnD,QAAMI,wBAAwBhE,uBAAuB;IACnDE,WAAWoD,cAAcW,YAAY/D;IACrCD;IACAE,kBAAkBoD,OAAOW;IACzB9D;EACF,CAAA;AAEA,QAAM,EAAE+D,WAAU,IAAKC,OAAAA;AAMvB,QAAMC,kBAAwBC,eAAQ,MAAA;AACpC,UAAMC,YACJ,CAACpE,kBAAqCgE,cAAmC,CAAA,MACzE,CAACK,aAAAA;AACC,YAAMjB,UAAS;QAAEW,YAAY/D;MAAiB;AAE9C,YAAMsE,sBAAkBC,YAAAA,SACtBC,kCAAkCpB,OAAAA,GAClCqB,gBAAgBrB,SAAQY,WAAAA,CAAAA;AAE1B,aAAOM,gBAAgBD,QAAAA;IACzB;AAEF,WAAOD,UAAUpB,QAAQI,QAAQY,UAAAA,EAAYhB,QAAQ0B,IAAI;KACxD;IAACV;IAAYhB,QAAQ0B;IAAM1B,QAAQI;EAAO,CAAA;AAE7C,aACEuB,0BAACC,QAAQC,SAAO;;UACdzD,yBAACW,KAAAA;QAAIO,eAAe;QAClB,cAAAlB,yBAAC0D,MAAAA;UAAKhE,UAAU;UAAMiE,QAAO;UAAMC,eAAed;UAChD,cAAA9C,yBAACoB,MAAAA;YAAKZ,WAAU;YAASC,YAAW;YAAUN,KAAK;YAAG0D,UAAS;YAC5D,UAAA;cAAInF,GAAAA;cAAW+D,GAAAA;cAAuBlE,IAAI,CAACS,OAAO8E,UAAAA;AACjD,yBAAO9D,yBAACF,WAAAA;gBAAsBd;cAAP8E,GAAAA,KAAAA;YACzB,CAAA;;;;MAILzB,0BAA0B/D,SAAS,SAClCiF,0BAAAQ,8BAAA;;cACE/D,yBAACgE,SAAAA,CAAAA,CAAAA;cACDT,0BAAC5C,KAAAA;YAAIM,YAAY;;kBACfsC,0BAACnC,MAAAA;gBAAKZ,WAAU;gBAASC,YAAW;gBAAaS,eAAe;gBAAGf,KAAK;;sBACtEH,yBAACiE,YAAAA;oBAAWC,SAAQ;8BACjB3C,cAAc;sBACb4C,IAAI;sBACJC,gBAAgB;oBAClB,CAAA;;sBAEFpE,yBAACiE,YAAAA;oBAAWC,SAAQ;8BACjB3C,cACC;sBACE4C,IAAI;sBACJC,gBACE;uBAEJ;sBACEC,GAAG,CAACC,eACFtE,yBAACiE,YAAAA;wBAAWC,SAAQ;wBAAKK,YAAW;wBACjCD,UAAAA;;oBAGP,CAAA;;;;kBAINtE,yBAAC0D,MAAAA;gBAAKhE,UAAU;gBAAMiE,QAAO;gBAAMC,eAAehC,QAAQ0B;gBACxD,cAAAtD,yBAACoB,MAAAA;kBAAKZ,WAAU;kBAASC,YAAW;kBAAUN,KAAK;kBAAG0D,UAAS;4BAC5DrB,oBAAoBjE,IAAI,CAACS,OAAO8E,UAAAA;AAC/B,+BAAO9D,yBAACF,WAAAA;sBAAsBd;oBAAP8E,GAAAA,KAAAA;kBACzB,CAAA;;;;;;;;;AAQhB;;;;;;;;AC9PA,IAAMU,qBAAqBC,kBAAkBC,gBAAgB;EAC3DC,WAAW,CAACC,aAAa;IACvBC,oBAAoBD,QAAQE,MAG1B;MACAA,MAAMC,QAAM;AACV,eAAO;UACLC,KAAK;UACLC,QAAQ;UACRC,QAAQ;YACNH;UACF;QACF;MACF;MACAI,cAAc;QAAC;MAAiB;IAClC,CAAA;IACAC,gBAAgBR,QAAQS,SAAyD;MAC/EP,MAAM,EAAEC,QAAQO,KAAI,GAAE;AACpB,eAAO;UACLN,KAAK,qCAAqCD,OAAOQ,SAAS;UAC1DN,QAAQ;UACRO,MAAMF;QACR;MACF;MACAG,iBAAiB,CAACC,MAAMC,QAAQ,EAAEC,YAAYC,gBAAgBd,OAAM,MAAE;AACpE,eAAO;UACL;UACA;YACEe,MAAM;YACNC,IACEF,mBAAmBG,mBACf,GAAGjB,OAAOkB,WAAW,IAAIL,UAAAA,KACzBb,OAAOkB;UACf;QACD;MACH;IACF,CAAA;;AAEJ,CAAA;AAEA,IAAM,EAAEC,4BAA4BC,0BAAyB,IAAK3B;;;AC5BrD4B,IAAAA,gBAAgB,CAAC,EAAEC,SAAQ,MAAsB;;AAC5D,QAAM,CAACC,qBAAqBC,sBAAAA,IAAgCC,gBAAS,KAAA;AACrE,QAAMC,WAAWC,YAAAA;AACjB,QAAM,EAAEC,eAAeC,WAAU,IAAKC,QAAAA;AACtC,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,CAAC,EAAEC,MAAK,CAAE,IAAIC,eAAAA;AAGpB,QAAM,EAAEC,gBAAgBC,KAAI,IAAKC,UAAAA;AACjC,QAAM,CAACC,gBAAgB,EAAEC,UAAS,CAAE,IAAIC,0BAAAA;AACxC,QAAM,EAAEC,eAAc,IAAKC,QAAQC,YAAYC,IAAI,CAACC,YAAY;IAAEA;IAAQC,SAASV;IAAK,CAAA;AAExF,QAAMW,UAAUC,kBAAkB,iBAAiB,CAACC,UAAUA,MAAMC,eAAe;AACnF,QAAMC,YAAYH,kBAAkB,iBAAiB,CAACC,UAAUA,MAAME,SAAS;AAC/E,QAAMC,SAASJ,kBAAkB,iBAAiB,CAACC,UAAUA,MAAMG,MAAM;AACzE,QAAMC,mBAAmBL,kBACvB,iBACA,CAACC,UAAUA,MAAMK,SAAS,KAAKL,MAAMM,SAASC,KAAK,CAAE,EAACC,OAAOR,MAAMC,gBAAgBO,EAAE;AAGvF,QAAMC,iBAAiBX,QAAQS,KAAKL,SAAU;AAE9C,QAAMQ,oBAAoB,MAAA;AACxB,UAAMC,yBAAqBC,qBAAU;MAAEC,SAAS7B,MAAM6B;OAAW;MAAEC,QAAQ;IAAM,CAAA;AAEjF,WAAO;MACLC,UAAU;MACVC,QAAQL;IACV;EACF;AAEA,QAAMM,gBAAgB,YAAA;AACpB,QAAI;AACF,YAAMC,WAAW,MAAM7B,eAAe;QACpC8B,YAAYrB,QAAQsB;QACpBlC;QACAmC,QAAQ;UACNC,WAAWxB,QAAQU;UACnBe,aAAazB,QAAQyB;QACvB;QACAC,MAAM;UAAED,aAAazB,QAAQyB;QAAY;MAC3C,CAAA;AAEA,UAAI,UAAUL,UAAU;AACtB3C,iBAASmC,kBAAqB,GAAA;UAAEe,UAAU;QAAO,CAAA;AAEjD3C,2BAAmB;UACjB4C,MAAM;UACNC,OAAOlD,cAAc;YACnB+B,IAAI;YACJoB,gBAAgB;UAClB,CAAA;UACAC,SAASpD,cAAc;YACrB+B,IAAI;YACJoB,gBAAgB;UAClB,CAAA;QACF,CAAA;AAEAhD,mBAAW,0BAAA;MACb;AAEA,UAAI,WAAWsC,UAAU;AACvBpC,2BAAmB;UACjB4C,MAAM;UACNG,SAASpD,cAAc;YACrB+B,IAAI;YACJoB,gBAAgB;UAClB,CAAA;QACF,CAAA;MACF;IACF,SAASE,OAAO;AACdhD,yBAAmB;QACjB4C,MAAM;QACNG,SAASpD,cAAc;UAAE+B,IAAI;UAAsBoB,gBAAgB;QAAoB,CAAA;MACzF,CAAA;IACF;EACF;AAEA,aACEG,0BAACC,OAAOC,MAAI;IAACC,MAAM9D;IAAqB+D,cAAc9D;;UACpD+D,yBAACC,QAAQC,YAAU;QACjB9B,IAAIrC;QACJwD,OAAOjD,WAAW,IAAI6D,KAAKzC,QAAQ0C,SAAS,GAAG;UAC7CC,MAAM;UACNC,OAAO;UACPC,KAAK;UACLC,MAAM;UACNC,QAAQ;QACV,CAAA;QACAC,qBACEV,yBAACW,uBAAAA;UACCC,OAAOvE,cAAc;YACnB+B,IAAI;YACJoB,gBAAgB;UAClB,CAAA;;QAGJqB,cACEb,yBAACc,YAAAA;UAAWC,SAAQ;UAAUC,WAAU;oBACrC3E,cACC;YACE+B,IAAI;YACJoB,gBACE;aAEJ;YACEyB,WAAWC,QAAQxD,QAAQyD,MAAM;YACjCN,UAAU,GAAGxC,kBAAkB,EAAA,KAAON,OAAOqD,KAAKC,YAAY,IAAIC,KAAI;YACtEH,SAAQzD,aAAQyD,WAARzD,mBAAgB6D;UAC1B,CAAA;;QAINC,sBACExB,yBAACyB,OAAAA;UACCC,eAAW1B,yBAAC2B,eAAAA,CAAAA,CAAAA;UACZC,KAAKC;UACLC,IAAIxD,kBAAAA;UACJe,UAAS;UACT0C,YAAY;oBAEX1F,cAAc;YACb+B,IAAI;YACJoB,gBAAgB;UAClB,CAAA;;QAGJwC,QAAQ;QACRC,mBACEjC,yBAACJ,OAAOsC,SAAO;UACb,cAAAlC,yBAACmC,QAAAA;YACCC,UAAU,CAAChF,eAAeiF,aAAarE;YACvCsE,SAAS,MAAA;AACPrG,qCAAuB,IAAA;YACzB;sBAECI,cAAc;cACb+B,IAAI;cACJoB,gBAAgB;YAClB,CAAA;;;;UAKRQ,yBAACuC,eAAAA;QACCC,WAAW3D;QACX4D,eACEzC,yBAACmC,QAAAA;UAAOpB,SAAQ;UAAYuB,SAASzD;UAAe6D,SAASxF;oBAC1Db,cAAc;YACb+B,IAAI;YACJoB,gBAAgB;UAClB,CAAA;;QAIJ,cAAAG,0BAACgD,MAAAA;UACCC,WAAU;UACVC,YAAW;UACXC,gBAAe;UACfC,KAAK;UACLC,WAAU;;gBAEVhD,yBAAC2C,MAAAA;cAAKG,gBAAe;cACnB,cAAA9C,yBAACiD,cAAAA;gBAAcC,OAAM;gBAAOC,QAAO;gBAAOC,MAAK;;;gBAEjDpD,yBAACc,YAAAA;wBACEzE,cAAc;gBACb+B,IAAI;gBACJoB,gBAAgB;cAClB,CAAA;;gBAEFQ,yBAACc,YAAAA;wBACEzE,cACC;gBACE+B,IAAI;gBACJoB,gBACE;iBAEJ;gBACE6D,SAAS3F,QAAQ4F,WAAW;cAC9B,CAAA;;;;;;;AAOd;;;;;;ACpMA,IAAMC,WAAW,CAACC,iBAChBC,yBAACC,YAAAA;EAAWC,WAAU;EAAaC,SAAQ;EACxCJ;;AAaL,IAAMK,cAAc,CAAC,EAAEC,SAASC,UAAS,MAAoB;AAC3D,QAAM,EAAEC,YAAYC,cAAa,IAAKC,QAAAA;AACtC,QAAM,CAAC,EAAEC,MAAK,CAAE,IAAIC,eAAAA;AACpB,QAAMC,WAAWF,MAAMG,OAAOR,QAAQQ,GAAGC,SAAQ;AACjD,QAAMC,SAASV,QAAQW,aAAaC,eAAeZ,QAAQW,SAAS;AAEpE,aACEE,0BAACC,MAAAA;IACCC,WAAU;IACVC,YAAW;IACXC,KAAK;IACLC,WAAS;IACTC,aAAY;IACZC,aAAY;IACZC,aAAad,WAAW,eAAe;IACvCe,OAAM;IACNC,SAAS;IACTC,KAAKC;IACLC,IAAI,QAAIC,sBAAU;MAAE,GAAGtB;MAAOG,IAAIR,QAAQQ;IAAG,CAAA,CAAA;IAC7CoB,OAAO;MAAEC,gBAAgB;IAAO;;UAEhChB,0BAACC,MAAAA;QAAKC,WAAU;QAASE,KAAK;QAAGD,YAAW;;cAC1CrB,yBAACC,YAAAA;YAAW4B,KAAI;YAAKM,YAAW;sBAC7B5B,WAAWF,QAAQ+B,WAAW;cAC7BC,KAAK;cACLC,OAAO;cACPC,MAAM;cACNC,MAAM;cACNC,QAAQ;YACV,CAAA;;cAEFzC,yBAACC,YAAAA;YAAW4B,KAAI;YAAI1B,SAAQ;YAAKD,WAAU;sBACxCM,cACC;cACEK,IAAI;cACJ6B,gBACE;eAEJ;cACEC,mBAAe3C,yBAAC4C,cAAAA;gBAAaC,WAAW,IAAIC,KAAKzC,QAAQ+B,SAAS;;cAClErB;cACAgC,aAAa,CAACC,QAAQ3C,QAAQW,SAAS;cACvCV;cACA2C,GAAGnD;YACL,CAAA;;;;MAILO,QAAQ6C,cAAUlD,yBAACmD,gBAAAA;QAAeD,QAAQ7C,QAAQ6C;QAAQE,MAAK;;;;AAGtE;AAWA,IAAMC,mBAAmB,CAAC,EAAEC,MAAMvD,SAAQ,MAAyB;AACjE,QAAM,CAAC,EAAEW,MAAK,CAAE,IAAIC,eAAAA;AAIpB,QAAM,EAAEE,IAAI0C,KAAK,GAAGC,UAAAA,IAAc9C;AAElC,aACEV,yBAAC8B,MAAAA;IAAKC,IAAI;MAAE0B,YAAQzB,sBAAU;QAAE,GAAGwB;QAAWF;MAAK,CAAA;IAAG;IAAGrB,OAAO;MAAEC,gBAAgB;IAAO;IACvF,cAAAlC,yBAACC,YAAAA;MAAWE,SAAQ;MAAQD,WAAU;MACnCH;;;AAIT;AAIkG,IAE5F2D,eAAe,MAAA;AACnB,QAAM,EAAElD,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEkD,UAAUL,KAAI,IAAKM,kBAAkB,gBAAgB,CAACC,WAAW;IACvEF,UAAUE,MAAMF;IAChBL,MAAMO,MAAMP;IACd;AAEA,aACEpC,0BAACC,MAAAA;IACC2C,QAAQ;IACR1C,WAAU;IACVC,YAAW;IACX0C,OAAM;IACNC,QAAO;IACPC,YAAW;IACXvC,aAAY;IACZF,aAAY;IACZC,aAAY;IACZI,KAAI;;UAEJX,0BAACC,MAAAA;QACCC,WAAU;QACV8C,gBAAe;QACftC,SAAS;QACTF,aAAY;QACZF,aAAY;QACZC,aAAY;QACZI,KAAI;;cAEJ7B,yBAACC,YAAAA;YAAW4B,KAAI;YAAK1B,SAAQ;YAAQgC,YAAW;sBAC7C3B,cAAc;cACbK,IAAI;cACJ6B,gBAAgB;YAClB,CAAA;;cAEF1C,yBAACmE,KAAAA;YAAIF,YAAW;YAAa1C,WAAS;YAACK,SAAS;YAC9C,cAAA5B,yBAACC,YAAAA;cAAWE,SAAQ;cAAQD,WAAU;cACnCyD,UAAAA,SAASS,KAAKC,WAAWC;;;;;UAIhCpD,0BAACiD,KAAAA;QAAII,MAAM;QAAGC,UAAS;;UACpBb,SAASS,KAAKC,WAAWf,OAAO,SAC/BtD,yBAACmE,KAAAA;YAAIM,YAAY;YAAGC,WAAU;YAC5B,cAAA1E,yBAACqD,kBAAAA;cAAiBC,MAAMA,OAAO;wBAC5B9C,cAAc;gBACbK,IAAI;gBACJ6B,gBAAgB;cAClB,CAAA;;;cAIN1C,yBAACmB,MAAAA;YAAKC,WAAU;YAASE,KAAK;YAAGM,SAAS;YAAGC,KAAI;YAAKR,YAAW;YAC9DsC,UAAAA,SAASgB,KAAKC,IAAI,CAACvE,SAASwE,cAC3B7E,yBAAC8E,MAAAA;cAECC,cAAYvE,cAAc;gBACxBK,IAAI;gBACJ6B,gBAAgB;cAClB,CAAA;cAEA,cAAA1C,yBAACI,aAAAA;gBAAYC;gBAAkBC,WAAWgD,SAAS,KAAKuB,UAAU;;YAN7DxE,GAAAA,QAAQQ,EAAE,CAAA;;UAUpB8C,SAASS,KAAKC,WAAWf,OAAOK,SAASS,KAAKC,WAAWW,iBACxDhF,yBAACmE,KAAAA;YAAIc,eAAe;YAAGP,WAAU;YAC/B,cAAA1E,yBAACqD,kBAAAA;cAAiBC,MAAMA,OAAO;wBAC5B9C,cAAc;gBACbK,IAAI;gBACJ6B,gBAAgB;cAClB,CAAA;;;;;;;AAOd;;;ACnJA,IAAM,CAACwC,iBAAiBC,iBAAkB,IAAGC,cAAmC,aAAA;AAMhF,IAAMC,cAAc,MAAA;;AAClB,QAAMC,WAAiBC,aAAK;AAC5B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EACJC,MACAC,IAAIC,YACJC,eAAc,IACZC,UAAAA;AAMJ,QAAM,EAAEC,WAAWC,mBAAmBC,OAAM,IAAKC,YAAY;IAC3DL;IACAM,OAAOT;EACT,CAAA;AAEA,QAAM,EACJK,WAAWK,iBACXC,MAAM,EACJC,QACAC,UAAU,EAAEC,aAAaC,UAAS,EAAE,EACrC,IACCC,kBAAkBhB,IAAAA;AACtB,QAAM,EAAEiB,MAAMC,eAAeb,WAAWc,uBAAsB,IAC5DC,oCAAoCpB,IAAAA;AAGtC,QAAM,CAAC,EAAEqB,MAAK,CAAE,IAAIC,eAAAA;AAKpB,QAAM,EAAErB,IAAIsB,mBAAmB,GAAGC,eAAAA,IAAmBH;AACrD,QAAMI,4BAA4BC,iBAAiBF,cAAAA;AACnD,QAAMG,OAAOF,0BAA0BE,OAAOC,OAAOH,0BAA0BE,IAAI,IAAI;AAEvF,QAAME,mBAAmBC,2BACvB;IACEC,aAAa/B;IACb,GAAIE,aAAa;MAAEA;IAAW,IAAI,CAAA;;IAElC,GAAGuB;KAEL;IAAEO,2BAA2B;EAAK,CAAA;AAapC,QAAMC,mBAAyBC,cAAOL,iBAAiBM,SAAS;AAChE,QAAMC,iBAAiBP,iBAAiBM,cAAcF,iBAAiBI;AAOvE,MAAI,CAACrC,QAASG,mBAAmBmC,oBAAoB,CAACpC,YAAa;AACjE,eAAOqC,yBAACC,UAAAA;MAASC,IAAG;;EACtB;AAEA,MACEnC,qBACAI,mBACAmB,iBAAiBa,cACjBN,kBACAjB,wBACA;AACA,eAAOoB,yBAACI,KAAKC,SAAO,CAAA,CAAA;EACtB;AAGA,MAAI,CAACf,iBAAiBgB,WAAW,GAAChB,4BAAiBZ,SAAjBY,mBAAuBZ,SAAvBY,mBAA6BiB,SAAQ;AACrE,eACEP,yBAAAQ,8BAAA;oBACER,yBAACI,KAAKK,QAAM;QACVC,YACEV,yBAACW,OAAAA;UACCC,KAAKC;UACLX,IAAI,oBAAoBtC,cAAe,IAAGH,IAAK,GAAEE,aAAa,IAAIA,UAAAA,KAAe,EAAA;oBAEhFJ,cAAc;YACbG,IAAI;YACJoD,gBAAgB;UAClB,CAAA;;;;EAMZ;AAGA,QAAIxB,4BAAiBZ,SAAjBY,mBAAuBZ,SAAvBY,mBAA6BiB,WAAU,CAACvB,mBAAmB;AAC7D,eACEgB,yBAACC,UAAAA;MACCC,IAAI;QAAEa,YAAQC,sBAAU;UAAE,GAAGlC;UAAOpB,IAAI4B,iBAAiBZ,KAAKA,KAAK,CAAA,EAAGhB;QAAG,CAAA;MAAG;MAC5EuD,SAAO;;EAGb;AAEA,QAAMC,mBAAkB5B,4BAAiBZ,SAAjBY,mBAAuBZ,SAAvBY,mBAA6B6B,KACnD,CAACC,YAAYA,QAAQ1D,GAAG2D,SAAQ,MAAOrC;AAEzC,MACEM,iBAAiBgB,WACjB,CAACjC,UACD,CAACL,UACD,CAACkD,mBACD,CAACvC;EAEDW,iBAAiBZ,KAAK4C,OACtB;AACA,eAAOtB,yBAACI,KAAKmB,OAAK,CAAA,CAAA;EACpB;AAEA,aACEC,0BAAAhB,8BAAA;;UACER,yBAACI,KAAKqB,OAAK;kBACRlE,cACC;UACEG,IAAI;UACJoD,gBAAgB;WAElB;UACEtB,aAAajB;QACf,CAAA;;UAGJyB,yBAAC/C,iBAAAA;QACCuC,aAAa/B;QACbC,IAAIC;QACJK;QACAK;QACAM;QACAuC;QACAQ,UAAUpC,iBAAiBZ;QAC3BU;QACAZ;QAEA,cAAAgD,0BAACG,MAAAA;UAAKC,WAAU;UAAMC,YAAW;;gBAC/BL,0BAACM,MAAAA;cACCC,MAAM;cACNC,QAAO;cACPC,YAAW;cACXC,eAAe;cACfC,UAAS;cACTC,YAAY/E;;oBAEZ2C,yBAACqC,eAAAA;kBAAchF;;oBAEf2C,yBAACsC,gBAAAA,CAAAA,CAAAA;;;gBAEHtC,yBAACuC,cAAAA,CAAAA,CAAAA;;;;;;AAKX;AAMA,IAAMC,2BAA2B,MAAA;AAC/B,QAAM,EAAE/E,KAAI,IAAKI,UAAAA;AAGjB,QAAM,EACJ4E,cAAc,CAAA,GACd3E,WACAwD,MAAK,IACHoB,QAAQC,YAAYC,IAAI,CAAClC,YAAY;IAAEA;IAAQmC,SAASpF;IAAK,CAAA;AAEjE,MAAIK,WAAW;AACb,eAAOkC,yBAACI,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,MAAIiB,SAAS,CAAC7D,MAAM;AAClB,eACEuC,yBAAC8C,KAAAA;MACCd,QAAO;MACPe,OAAM;MACNC,UAAS;MACTC,KAAK;MACLC,MAAM;MACNC,QAAQ;MACRlB,YAAW;oBAEXjC,yBAACI,KAAKmB,OAAK,CAAA,CAAA;;EAGjB;AAEA,aACEvB,yBAAC8C,KAAAA;IACCd,QAAO;IACPe,OAAM;IACNC,UAAS;IACTC,KAAK;IACLC,MAAM;IACNC,QAAQ;IACRlB,YAAW;kBAEXjC,yBAACI,KAAKgD,SAAO;MAACX;MACX,UAAA,CAAC,EAAEA,aAAAA,aAAW,UACbzC,yBAACqD,cAAAA;QAAaZ,aAAaA;QACzB,cAAAzC,yBAAC5C,aAAAA,CAAAA,CAAAA;;;;AAMb;AAEA,IAAMkG,uBAAuB,MAAA;AAC3B,aACEtD,yBAACuD,UAAAA;IACC,cAAAvD,yBAACwD,WAAAA;MACC,cAAAxD,yBAACwC,0BAAAA,CAAAA,CAAAA;;;AAIT;", "names": ["<PERSON>d<PERSON><PERSON><PERSON>", "styled", "<PERSON><PERSON>", "attrs", "<PERSON><PERSON><PERSON><PERSON>", "onClose", "shadow", "LinkEllipsis", "Link", "CustomRelationInput", "props", "formatMessage", "useIntl", "field", "useField", "name", "formattedFieldValue", "Array", "isArray", "value", "results", "meta", "missingCount", "length", "_jsxs", "_Fragment", "_jsx", "Field", "Label", "action", "labelAction", "label", "Box", "marginTop", "variant", "id", "defaultMessage", "Flex", "direction", "gap", "alignItems", "map", "relationData", "targetModel", "attribute", "href", "COLLECTION_TYPES", "documentId", "getRelationLabel", "mainField", "isAdminUserRelation", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "hasRadius", "borderColor", "background", "justifyContent", "min<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Typography", "tag", "NavLink", "to", "DocumentStatus", "status", "title", "number", "createInitialValuesForPath", "keyP<PERSON>", "keys", "split", "root", "node", "for<PERSON>ach", "key", "index", "CustomMediaInput", "fields", "useStrapiApp", "state", "MediaLibrary", "media", "Form", "method", "disabled", "initialValues", "multiple", "getLabelAction", "isValidElement", "labelActionTitleId", "cloneElement", "VersionInputRenderer", "visible", "hint", "providedHint", "shouldIgnoreRBAC", "customLabelAction", "version", "useHistoryContext", "selectedVersion", "configuration", "fieldSizes", "useTypedSelector", "app", "components", "useDoc", "isFormDisabled", "useForm", "isInDynamicZone", "useDynamicZone", "canCreateFields", "useDocumentRBAC", "rbac", "can<PERSON><PERSON>Fields", "canUp<PERSON><PERSON><PERSON>s", "canUserAction", "editable<PERSON><PERSON>s", "readableFields", "canUserReadField", "type", "canUserEditField", "lazyComponentStore", "useLazyComponents", "attributeHasCustomFieldProperty", "customField", "undefined", "useFieldHint", "edit", "componentsLayout", "useDocLayout", "NotAllowedInput", "fieldIsDisabled", "addedAttributes", "unknownAttributes", "added", "Object", "includes", "width", "CustomInput", "FormInput<PERSON><PERSON>er", "addedInputTypes", "BlocksInput", "layout", "component", "remainingFieldsLayout", "getRemaingFieldsLayout", "metadatas", "schemaAttributes", "attributes", "ComponentInput", "inputProps", "DynamicZone", "Wysiwyg", "UIDInput", "options", "enum", "unique", "_unique", "_mainField", "restProps", "createLayoutFromFields", "fields", "reduce", "rows", "field", "type", "push", "length", "map", "row", "getRemaingFieldsLayout", "layout", "metadatas", "schemaAttributes", "fieldSizes", "fieldsInLayout", "flatMap", "panel", "name", "remainingFields", "Object", "entries", "currentRemainingFields", "includes", "edit", "visible", "attribute", "disabled", "label", "size", "default", "FormPanel", "some", "_jsx", "Grid", "Root", "gap", "<PERSON><PERSON>", "col", "s", "xs", "direction", "alignItems", "VersionInputRenderer", "Box", "hasRadius", "background", "shadow", "paddingLeft", "paddingRight", "paddingTop", "paddingBottom", "borderColor", "Flex", "gridRowIndex", "VersionContent", "formatMessage", "useIntl", "useTypedSelector", "state", "app", "version", "useHistoryContext", "selectedVersion", "configuration", "schema", "removedAttributes", "meta", "unknownAttributes", "removed", "removedAttributesAs<PERSON>ields", "attributeName", "shouldIgnoreRBAC", "unknownFieldsLayout", "remainingFieldsLayout", "contentType", "attributes", "components", "useDoc", "transformedData", "useMemo", "transform", "document", "transformations", "pipe", "removeFieldsThatDontExistOnSchema", "prepareTempKeys", "data", "_jsxs", "Layouts", "Content", "Form", "method", "initialValues", "position", "index", "_Fragment", "Divider", "Typography", "variant", "id", "defaultMessage", "b", "chunks", "fontWeight", "historyVersionsApi", "contentManagerApi", "injectEndpoints", "endpoints", "builder", "getHistoryVersions", "query", "params", "url", "method", "config", "providesTags", "restoreVersion", "mutation", "body", "versionId", "data", "invalidatesTags", "_res", "_error", "documentId", "collectionType", "type", "id", "COLLECTION_TYPES", "contentType", "useGetHistoryVersionsQuery", "useRestoreVersionMutation", "VersionHeader", "headerId", "isConfirmDialogOpen", "setIsConfirmDialogOpen", "useState", "navigate", "useNavigate", "formatMessage", "formatDate", "useIntl", "trackUsage", "useTracking", "toggleNotification", "useNotification", "query", "useQueryParams", "collectionType", "slug", "useParams", "restoreVersion", "isLoading", "useRestoreVersionMutation", "allowedActions", "useRBAC", "PERMISSIONS", "map", "action", "subject", "version", "useHistoryContext", "state", "selectedVersion", "mainField", "schema", "isCurrentVersion", "page", "versions", "data", "id", "mainFieldValue", "getNextNavigation", "pluginsQueryParams", "stringify", "plugins", "encode", "pathname", "search", "handleRestore", "response", "documentId", "relatedDocumentId", "params", "versionId", "contentType", "body", "relative", "type", "title", "defaultMessage", "message", "error", "_jsxs", "Dialog", "Root", "open", "onOpenChange", "_jsx", "Layouts", "BaseHeader", "Date", "createdAt", "year", "month", "day", "hour", "minute", "secondaryAction", "GradientBadge", "label", "subtitle", "Typography", "variant", "textColor", "hasLocale", "Boolean", "locale", "info", "singularName", "trim", "name", "navigationAction", "Link", "startIcon", "ArrowLeft", "tag", "NavLink", "to", "isExternal", "sticky", "primaryAction", "<PERSON><PERSON>", "<PERSON><PERSON>", "disabled", "canUpdate", "onClick", "ConfirmDialog", "onConfirm", "endAction", "loading", "Flex", "direction", "alignItems", "justifyContent", "gap", "textAlign", "WarningCircle", "width", "height", "fill", "isDraft", "status", "BlueText", "children", "_jsx", "Typography", "textColor", "variant", "VersionCard", "version", "isCurrent", "formatDate", "formatMessage", "useIntl", "query", "useQueryParams", "isActive", "id", "toString", "author", "created<PERSON>y", "getDisplayName", "_jsxs", "Flex", "direction", "alignItems", "gap", "hasRadius", "borderWidth", "borderStyle", "borderColor", "color", "padding", "tag", "Link", "to", "stringify", "style", "textDecoration", "fontWeight", "createdAt", "day", "month", "year", "hour", "minute", "defaultMessage", "distanceToNow", "RelativeTime", "timestamp", "Date", "isAnonymous", "Boolean", "b", "status", "DocumentStatus", "size", "PaginationButton", "page", "_id", "queryRest", "search", "VersionsList", "versions", "useHistoryContext", "state", "shrink", "width", "height", "background", "justifyContent", "Box", "meta", "pagination", "total", "flex", "overflow", "paddingTop", "textAlign", "data", "map", "index", "li", "aria-label", "pageCount", "paddingBottom", "HistoryProvider", "useHistoryContext", "createContext", "HistoryPage", "headerId", "useId", "formatMessage", "useIntl", "slug", "id", "documentId", "collectionType", "useParams", "isLoading", "isLoadingDocument", "schema", "useDocument", "model", "isLoadingLayout", "edit", "layout", "settings", "displayName", "mainField", "useDocumentLayout", "data", "configuration", "isLoadingConfiguration", "useGetContentTypeConfigurationQuery", "query", "useQueryParams", "selectedVersionId", "queryWithoutId", "validQueryParamsWithoutId", "buildValidParams", "page", "Number", "versionsResponse", "useGetHistoryVersionsQuery", "contentType", "refetchOnMountOrArgChange", "initialRequestId", "useRef", "requestId", "isStaleRequest", "current", "COLLECTION_TYPES", "_jsx", "Navigate", "to", "isFetching", "Page", "Loading", "isError", "length", "_Fragment", "NoData", "action", "Link", "tag", "NavLink", "defaultMessage", "search", "stringify", "replace", "selectedVersion", "find", "version", "toString", "error", "Error", "_jsxs", "Title", "versions", "Flex", "direction", "alignItems", "Main", "grow", "height", "background", "paddingBottom", "overflow", "labelledBy", "VersionHeader", "VersionContent", "VersionsList", "ProtectedHistoryPageImpl", "permissions", "useRBAC", "PERMISSIONS", "map", "subject", "Box", "width", "position", "top", "left", "zIndex", "Protect", "DocumentRBAC", "ProtectedHistoryPage", "Portal", "FocusTrap"]}