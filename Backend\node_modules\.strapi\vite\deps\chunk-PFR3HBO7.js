import {
  require_groupBy
} from "./chunk-Y2KSLFI4.js";
import {
  require_isEmpty
} from "./chunk-YJEURQPS.js";
import {
  require_merge,
  require_omit
} from "./chunk-DY2RJG3P.js";
import {
  require_cloneDeep
} from "./chunk-GGK2TLCV.js";
import {
  require_set
} from "./chunk-SGQJOZK5.js";
import {
  require_has,
  require_upperFirst
} from "./chunk-376QHLWZ.js";
import {
  require_baseForOwn
} from "./chunk-EGNP2T5O.js";
import {
  capitalise
} from "./chunk-PQINNV4N.js";
import {
  require_isEqual
} from "./chunk-VYSYYPOB.js";
import {
  require_baseIteratee,
  require_get
} from "./chunk-WH6VCVXU.js";
import {
  isObject
} from "./chunk-BHLYCXQ7.js";
import {
  require_arrayEach,
  require_baseCreate,
  require_getPrototype,
  require_isArray,
  require_isBuffer,
  require_isFunction,
  require_isObject,
  require_isTypedArray
} from "./chunk-CE4VABH2.js";
import {
  fn
} from "./chunk-QOUV5O5E.js";
import {
  $c512c27ab02ef895$export$fd42f52fd3ae1109,
  Accordion,
  Box,
  Breadcrumbs,
  Button,
  CheckboxImpl,
  Crumb,
  Flex,
  Grid,
  Modal,
  MultiSelectNested,
  Tabs,
  Typography,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  ForwardRef$4T,
  ForwardRef$4d,
  ForwardRef$4t,
  ForwardRef$4z
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt,
  lt
} from "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __commonJS,
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/transform.js
var require_transform = __commonJS({
  "node_modules/lodash/transform.js"(exports, module) {
    var arrayEach = require_arrayEach();
    var baseCreate = require_baseCreate();
    var baseForOwn = require_baseForOwn();
    var baseIteratee = require_baseIteratee();
    var getPrototype = require_getPrototype();
    var isArray = require_isArray();
    var isBuffer = require_isBuffer();
    var isFunction = require_isFunction();
    var isObject3 = require_isObject();
    var isTypedArray = require_isTypedArray();
    function transform2(object, iteratee, accumulator) {
      var isArr = isArray(object), isArrLike = isArr || isBuffer(object) || isTypedArray(object);
      iteratee = baseIteratee(iteratee, 4);
      if (accumulator == null) {
        var Ctor = object && object.constructor;
        if (isArrLike) {
          accumulator = isArr ? new Ctor() : [];
        } else if (isObject3(object)) {
          accumulator = isFunction(Ctor) ? baseCreate(getPrototype(object)) : {};
        } else {
          accumulator = {};
        }
      }
      (isArrLike ? arrayEach : baseForOwn)(object, function(value, index, object2) {
        return iteratee(accumulator, value, index, object2);
      });
      return accumulator;
    }
    module.exports = transform2;
  }
});

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/components/Permissions.mjs
var import_jsx_runtime10 = __toESM(require_jsx_runtime(), 1);
var React7 = __toESM(require_react(), 1);
var import_cloneDeep = __toESM(require_cloneDeep(), 1);
var import_get6 = __toESM(require_get(), 1);
var import_has2 = __toESM(require_has(), 1);
var import_isEmpty3 = __toESM(require_isEmpty(), 1);
var import_set = __toESM(require_set(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/hooks/usePermissionsDataManager.mjs
var [PermissionsDataManagerProvider, usePermissionsDataManagerContext] = $c512c27ab02ef895$export$fd42f52fd3ae1109("PermissionsDataManager");
var usePermissionsDataManager = () => usePermissionsDataManagerContext("usePermissionsDataManager");

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/utils/difference.mjs
var import_isEqual = __toESM(require_isEqual(), 1);
var import_isObject = __toESM(require_isObject(), 1);
var import_transform = __toESM(require_transform(), 1);
function difference(object, base) {
  function changes(object2, base2) {
    return (0, import_transform.default)(object2, (result, value, key) => {
      if (!(0, import_isEqual.default)(value, base2[key])) {
        result[key] = (0, import_isObject.default)(value) && (0, import_isObject.default)(base2[key]) ? changes(value, base2[key]) : value;
      }
      return result;
    });
  }
  return changes(object, base);
}

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/utils/forms.mjs
var import_isEmpty = __toESM(require_isEmpty(), 1);
var import_merge = __toESM(require_merge(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/utils/arrays.mjs
var flattenDeep = (array) => {
  if (Array.isArray(array)) {
    return array.reduce((acc, value) => {
      if (Array.isArray(value)) {
        acc.push(...flattenDeep(value));
      } else {
        acc.push(value);
      }
      return acc;
    }, []);
  } else {
    return [];
  }
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/utils/createArrayOfValues.mjs
var createArrayOfValues = (obj) => {
  if (!isObject(obj)) {
    return [];
  }
  return flattenDeep(Object.values(obj).map((value) => {
    if (isObject(value)) {
      return createArrayOfValues(value);
    }
    return value;
  }));
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/utils/permissions.mjs
var findMatchingPermission = (permissions, action, subject) => permissions.find((perm) => perm.action === action && perm.subject === subject);
var formatPermissionsForAPI = (modifiedData) => {
  const pluginsPermissions = formatSettingsPermissions(modifiedData.plugins);
  const settingsPermissions = formatSettingsPermissions(modifiedData.settings);
  const collectionTypesPermissions = formatContentTypesPermissions(modifiedData.collectionTypes);
  const singleTypesPermissions = formatContentTypesPermissions(modifiedData.singleTypes);
  return [
    ...pluginsPermissions,
    ...settingsPermissions,
    ...collectionTypesPermissions,
    ...singleTypesPermissions
  ];
};
var formatSettingsPermissions = (settingsPermissionsObject) => {
  return Object.values(settingsPermissionsObject).reduce((formAcc, form) => {
    const currentCategoryPermissions = Object.values(form).reduce((childFormAcc, childForm) => {
      const permissions = Object.entries(childForm).reduce((responsesAcc, [actionName, { conditions, properties: { enabled } }]) => {
        if (!enabled) {
          return responsesAcc;
        }
        responsesAcc.push({
          action: actionName,
          subject: null,
          conditions: createConditionsArray(conditions),
          properties: {}
        });
        return responsesAcc;
      }, []);
      return [
        ...childFormAcc,
        ...permissions
      ];
    }, []);
    return [
      ...formAcc,
      ...currentCategoryPermissions
    ];
  }, []);
};
var formatContentTypesPermissions = (contentTypesPermissions) => {
  const permissions = Object.entries(contentTypesPermissions).reduce((allPermissions, current) => {
    const [subject, currentSubjectActions] = current;
    const permissions2 = Object.entries(currentSubjectActions).reduce((acc, current2) => {
      var _a;
      const [actionName, permissions3] = current2;
      const shouldCreatePermission = createArrayOfValues(permissions3).some((val) => val);
      if (!shouldCreatePermission) {
        return acc;
      }
      if (!((_a = permissions3 == null ? void 0 : permissions3.properties) == null ? void 0 : _a.enabled)) {
        const createdPermissionsArray = Object.entries(permissions3.properties).reduce((acc2, current3) => {
          const [propertyName, propertyValue] = current3;
          acc2.properties[propertyName] = createPropertyArray(propertyValue);
          return acc2;
        }, {
          action: actionName,
          subject,
          conditions: createConditionsArray(permissions3.conditions),
          properties: {}
        });
        return [
          ...acc,
          createdPermissionsArray
        ];
      }
      if (!permissions3.properties.enabled) {
        return acc;
      }
      acc.push({
        action: actionName,
        subject,
        properties: {},
        conditions: createConditionsArray(permissions3.conditions)
      });
      return acc;
    }, []);
    return [
      ...allPermissions,
      ...permissions2
    ];
  }, []);
  return permissions;
};
var createPropertyArray = (propertyValue, prefix = "") => {
  return Object.entries(propertyValue).reduce((acc, current) => {
    const [name, value] = current;
    if (isObject(value)) {
      return [
        ...acc,
        ...createPropertyArray(value, `${prefix}${name}.`)
      ];
    }
    if (value && !isObject(value)) {
      acc.push(`${prefix}${name}`);
    }
    return acc;
  }, []);
};
var createConditionsArray = (conditions) => Object.entries(conditions).filter(([, conditionValue]) => {
  return conditionValue;
}).map(([conditionName]) => conditionName);

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/utils/forms.mjs
var createDefaultConditionsForm = (conditions, initialConditions = []) => conditions.reduce((acc, current) => {
  acc[current.id] = initialConditions.indexOf(current.id) !== -1;
  return acc;
}, {});
var createDefaultForm = (layout, conditions, initialPermissions = []) => {
  return layout.reduce((acc, { categoryId, childrenForm }) => {
    const childrenDefaultForm = childrenForm.reduce((acc2, current) => {
      acc2[current.subCategoryId] = current.actions.reduce((acc3, current2) => {
        const foundMatchingPermission = findMatchingPermission(initialPermissions, current2.action, null);
        acc3[current2.action] = {
          properties: {
            enabled: foundMatchingPermission !== void 0
          },
          conditions: createDefaultConditionsForm(conditions, (foundMatchingPermission == null ? void 0 : foundMatchingPermission.conditions) ?? [])
        };
        return acc3;
      }, {});
      return acc2;
    }, {});
    acc[categoryId] = childrenDefaultForm;
    return acc;
  }, {});
};
var createDefaultPropertiesForm = (properties, subject, matchingPermission) => {
  const recursivelyCreatePropertyForm = ({ children = [] }, propertyValues, prefix = "") => {
    return children.reduce((acc, current) => {
      if (current.children) {
        return {
          ...acc,
          [current.value]: recursivelyCreatePropertyForm(current, propertyValues, `${prefix}${current.value}.`)
        };
      }
      const hasProperty = propertyValues.indexOf(`${prefix}${current.value}`) !== -1;
      acc[current.value] = hasProperty;
      return acc;
    }, {});
  };
  return properties.reduce((acc, currentPropertyName) => {
    const foundProperty = subject.properties.find(({ value }) => value === currentPropertyName);
    if (foundProperty) {
      const matchingPermissionPropertyValues = (matchingPermission == null ? void 0 : matchingPermission.properties[foundProperty.value]) ?? [];
      const propertyForm = recursivelyCreatePropertyForm(foundProperty, matchingPermissionPropertyValues);
      acc.properties[currentPropertyName] = propertyForm;
    }
    return acc;
  }, {
    properties: {}
  });
};
var createDefaultCTForm = ({ subjects, actions = [] }, conditions, initialPermissions = []) => {
  return actions.reduce((defaultForm, action) => {
    const subjectLayouts = action.subjects.reduce((acc, current) => {
      const foundLayout = subjects.find(({ uid }) => uid === current) || null;
      if (foundLayout) {
        acc[current] = foundLayout;
      }
      return acc;
    }, {});
    if ((0, import_isEmpty.default)(subjectLayouts)) {
      return defaultForm;
    }
    const contentTypesActions = Object.keys(subjectLayouts).reduce((acc, currentCTUID) => {
      const { actionId, applyToProperties } = action;
      const currentSubjectLayout = subjectLayouts[currentCTUID];
      const properties = currentSubjectLayout.properties.map(({ value }) => value);
      const doesNothaveProperty = properties.every((property) => (applyToProperties || []).indexOf(property) === -1);
      const matchingPermission = findMatchingPermission(initialPermissions, actionId, currentCTUID);
      const conditionsForm = createDefaultConditionsForm(conditions, (matchingPermission == null ? void 0 : matchingPermission.conditions) ?? []);
      if (!acc[currentCTUID]) {
        acc[currentCTUID] = {};
      }
      if ((0, import_isEmpty.default)(applyToProperties) || doesNothaveProperty) {
        acc[currentCTUID][actionId] = {
          properties: {
            enabled: matchingPermission !== void 0
          },
          conditions: conditionsForm
        };
        return acc;
      }
      const propertiesForm = createDefaultPropertiesForm(applyToProperties, subjectLayouts[currentCTUID], matchingPermission);
      acc[currentCTUID][actionId] = {
        ...propertiesForm,
        conditions: conditionsForm
      };
      return acc;
    }, {});
    return (0, import_merge.default)(defaultForm, contentTypesActions);
  }, {});
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/utils/layouts.mjs
var import_groupBy = __toESM(require_groupBy(), 1);
var formatLayout = (layout, groupByKey) => {
  return Object.entries((0, import_groupBy.default)(layout, groupByKey)).map(([itemName, item]) => ({
    category: itemName,
    categoryId: itemName.split(" ").join("-"),
    childrenForm: Object.entries((0, import_groupBy.default)(item, "subCategory")).map(([subCategoryName, actions]) => ({
      subCategoryName,
      subCategoryId: subCategoryName.split(" ").join("-"),
      actions
    }))
  }));
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/utils/updateConditionsToFalse.mjs
var import_has = __toESM(require_has(), 1);
var import_omit = __toESM(require_omit(), 1);
var updateConditionsToFalse = (obj) => {
  return Object.keys(obj).reduce((acc, current) => {
    const currentValue = obj[current];
    if (isObject(currentValue) && !(0, import_has.default)(currentValue, "conditions")) {
      return {
        ...acc,
        [current]: updateConditionsToFalse(currentValue)
      };
    }
    if (isObject(currentValue) && (0, import_has.default)(currentValue, "conditions")) {
      const isActionEnabled = createArrayOfValues((0, import_omit.default)(currentValue, "conditions")).some((val) => val);
      if (!isActionEnabled) {
        const updatedConditions = Object.keys(currentValue.conditions).reduce((acc1, current2) => {
          acc1[current2] = false;
          return acc1;
        }, {});
        return {
          ...acc,
          [current]: {
            ...currentValue,
            conditions: updatedConditions
          }
        };
      }
    }
    acc[current] = currentValue;
    return acc;
  }, {});
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/utils/updateValues.mjs
var updateValues = (obj, valueToSet, isFieldUpdate = false) => {
  return Object.keys(obj).reduce((acc, current) => {
    const currentValue = obj[current];
    if (current === "conditions" && !isFieldUpdate) {
      acc[current] = currentValue;
      return acc;
    }
    if (isObject(currentValue)) {
      return {
        ...acc,
        [current]: updateValues(currentValue, valueToSet, current === "fields")
      };
    }
    acc[current] = valueToSet;
    return acc;
  }, {});
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/components/ContentTypes.mjs
var import_jsx_runtime8 = __toESM(require_jsx_runtime(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/components/ContentTypeCollapses.mjs
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var React4 = __toESM(require_react(), 1);
var import_get3 = __toESM(require_get(), 1);
var import_isEmpty2 = __toESM(require_isEmpty(), 1);
var import_omit2 = __toESM(require_omit(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/utils/constants.mjs
var cellWidth = `12rem`;
var firstRowWidth = `20rem`;
var rowHeight = `5.3rem`;

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/utils/removeConditionKeyFromData.mjs
var removeConditionKeyFromData = (obj) => {
  if (!obj) {
    return null;
  }
  return Object.entries(obj).reduce((acc, [key, value]) => {
    if (key !== "conditions") {
      acc[key] = value;
    }
    return acc;
  }, {});
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/utils/getCheckboxState.mjs
var getCheckboxState = (dataObj) => {
  const dataWithoutCondition = removeConditionKeyFromData(dataObj);
  const arrayOfValues = createArrayOfValues(dataWithoutCondition);
  if (!arrayOfValues.length) {
    return {
      hasAllActionsSelected: false,
      hasSomeActionsSelected: false
    };
  }
  const hasAllActionsSelected = arrayOfValues.every((val) => val);
  const hasSomeActionsSelected = arrayOfValues.some((val) => val) && !hasAllActionsSelected;
  return {
    hasAllActionsSelected,
    hasSomeActionsSelected
  };
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/components/CollapsePropertyMatrix.mjs
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_get = __toESM(require_get(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/components/CollapseLabel.mjs
var CollapseLabel = dt(Flex)`
  padding-right: ${({ theme }) => theme.spaces[2]};
  overflow: hidden;
  flex: 1;
  ${({ $isCollapsable }) => $isCollapsable && "cursor: pointer;"}
`;

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/components/HiddenAction.mjs
var HiddenAction = dt.div`
  width: ${cellWidth};
`;

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/components/RequiredSign.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var RequiredSign = () => (0, import_jsx_runtime.jsx)(Box, {
  color: "danger700",
  paddingLeft: 1,
  children: "*"
});

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/components/RowLabelWithCheckbox.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var RowLabelWithCheckbox = ({ checkboxName = "", children, isActive = false, isCollapsable = false, isFormDisabled = false, label, onChange, onClick, someChecked = false, value }) => {
  const { formatMessage } = useIntl();
  const collapseLabelProps = {
    title: label,
    alignItems: "center",
    $isCollapsable: isCollapsable
  };
  if (isCollapsable) {
    Object.assign(collapseLabelProps, {
      onClick,
      "aria-expanded": isActive,
      onKeyDown({ key }) {
        if (key === "Enter" || key === " ") {
          onClick();
        }
      },
      tabIndex: 0,
      role: "button"
    });
  }
  return (0, import_jsx_runtime2.jsxs)(Flex, {
    alignItems: "center",
    paddingLeft: 6,
    width: firstRowWidth,
    shrink: 0,
    children: [
      (0, import_jsx_runtime2.jsx)(Box, {
        paddingRight: 2,
        children: (0, import_jsx_runtime2.jsx)(CheckboxImpl, {
          name: checkboxName,
          "aria-label": formatMessage({
            id: `Settings.permissions.select-all-by-permission`,
            defaultMessage: "Select all {label} permissions"
          }, {
            label
          }),
          disabled: isFormDisabled,
          // Keep same signature as packages/core/admin/admin/src/components/Roles/Permissions/index.js l.91
          onCheckedChange: (value2) => onChange({
            target: {
              name: checkboxName,
              value: !!value2
            }
          }),
          checked: someChecked ? "indeterminate" : value
        })
      }),
      (0, import_jsx_runtime2.jsxs)(CollapseLabel, {
        ...collapseLabelProps,
        children: [
          (0, import_jsx_runtime2.jsx)(Typography, {
            ellipsis: true,
            children: label
          }),
          children
        ]
      })
    ]
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/components/CollapsePropertyMatrix.mjs
var CollapsePropertyMatrix = ({ availableActions = [], childrenForm = [], isFormDisabled, label, pathToData, propertyName }) => {
  const propertyActions = React.useMemo(() => availableActions.map((action) => {
    const isActionRelatedToCurrentProperty = Array.isArray(action.applyToProperties) && action.applyToProperties.indexOf(propertyName) !== -1 && action.isDisplayed;
    return {
      label: action.label,
      actionId: action.actionId,
      isActionRelatedToCurrentProperty
    };
  }), [
    availableActions,
    propertyName
  ]);
  return (0, import_jsx_runtime3.jsxs)(Flex, {
    display: "inline-flex",
    direction: "column",
    alignItems: "stretch",
    minWidth: 0,
    children: [
      (0, import_jsx_runtime3.jsx)(Header, {
        label,
        headers: propertyActions
      }),
      (0, import_jsx_runtime3.jsx)(Box, {
        children: childrenForm.map(({ children: childrenForm2, label: label2, value, required }, i) => (0, import_jsx_runtime3.jsx)(ActionRow, {
          childrenForm: childrenForm2,
          label: label2,
          isFormDisabled,
          name: value,
          required,
          propertyActions,
          pathToData,
          propertyName,
          isOdd: i % 2 === 0
        }, value))
      })
    ]
  });
};
var ActionRow = ({ childrenForm = [], label, isFormDisabled = false, name, required = false, pathToData, propertyActions, propertyName, isOdd = false }) => {
  const { formatMessage } = useIntl();
  const [rowToOpen, setRowToOpen] = React.useState(null);
  const { modifiedData, onChangeCollectionTypeLeftActionRowCheckbox, onChangeParentCheckbox, onChangeSimpleCheckbox } = usePermissionsDataManager();
  const isActive = rowToOpen === name;
  const recursiveChildren = React.useMemo(() => {
    if (!Array.isArray(childrenForm)) {
      return [];
    }
    return childrenForm;
  }, [
    childrenForm
  ]);
  const isCollapsable = recursiveChildren.length > 0;
  const handleClick = React.useCallback(() => {
    if (isCollapsable) {
      setRowToOpen((prev) => {
        if (prev === name) {
          return null;
        }
        return name;
      });
    }
  }, [
    isCollapsable,
    name
  ]);
  const handleChangeLeftRowCheckbox = ({ target: { value } }) => {
    onChangeCollectionTypeLeftActionRowCheckbox(pathToData, propertyName, name, value);
  };
  const { hasAllActionsSelected, hasSomeActionsSelected } = React.useMemo(() => {
    return getRowLabelCheckboxState(propertyActions, modifiedData, pathToData, propertyName, name);
  }, [
    propertyActions,
    modifiedData,
    pathToData,
    propertyName,
    name
  ]);
  return (0, import_jsx_runtime3.jsxs)(import_jsx_runtime3.Fragment, {
    children: [
      (0, import_jsx_runtime3.jsx)(Wrapper, {
        alignItems: "center",
        $isCollapsable: isCollapsable,
        $isActive: isActive,
        background: isOdd ? "neutral100" : "neutral0",
        children: (0, import_jsx_runtime3.jsxs)(Flex, {
          children: [
            (0, import_jsx_runtime3.jsxs)(RowLabelWithCheckbox, {
              onChange: handleChangeLeftRowCheckbox,
              onClick: handleClick,
              isCollapsable,
              isFormDisabled,
              label,
              someChecked: hasSomeActionsSelected,
              value: hasAllActionsSelected,
              isActive,
              children: [
                required && (0, import_jsx_runtime3.jsx)(RequiredSign, {}),
                (0, import_jsx_runtime3.jsx)(CarretIcon, {
                  $isActive: isActive
                })
              ]
            }),
            (0, import_jsx_runtime3.jsx)(Flex, {
              children: propertyActions.map(({ label: label2, isActionRelatedToCurrentProperty, actionId }) => {
                if (!isActionRelatedToCurrentProperty) {
                  return (0, import_jsx_runtime3.jsx)(HiddenAction, {}, label2);
                }
                const checkboxName = [
                  ...pathToData.split(".."),
                  actionId,
                  "properties",
                  propertyName,
                  name
                ];
                if (!isCollapsable) {
                  const checkboxValue = (0, import_get.default)(modifiedData, checkboxName, false);
                  return (0, import_jsx_runtime3.jsx)(Flex, {
                    width: cellWidth,
                    position: "relative",
                    justifyContent: "center",
                    alignItems: "center",
                    children: (0, import_jsx_runtime3.jsx)(CheckboxImpl, {
                      disabled: isFormDisabled,
                      name: checkboxName.join(".."),
                      "aria-label": formatMessage({
                        id: `Settings.permissions.select-by-permission`,
                        defaultMessage: "Select {label} permission"
                      }, {
                        label: `${name} ${label2}`
                      }),
                      onCheckedChange: (value) => {
                        onChangeSimpleCheckbox({
                          target: {
                            name: checkboxName.join(".."),
                            value: !!value
                          }
                        });
                      },
                      checked: checkboxValue
                    })
                  }, actionId);
                }
                const data = (0, import_get.default)(modifiedData, checkboxName, {});
                const { hasAllActionsSelected: hasAllActionsSelected2, hasSomeActionsSelected: hasSomeActionsSelected2 } = getCheckboxState(data);
                return (0, import_jsx_runtime3.jsx)(Flex, {
                  width: cellWidth,
                  position: "relative",
                  justifyContent: "center",
                  alignItems: "center",
                  children: (0, import_jsx_runtime3.jsx)(CheckboxImpl, {
                    disabled: isFormDisabled,
                    name: checkboxName.join(".."),
                    onCheckedChange: (value) => {
                      onChangeParentCheckbox({
                        target: {
                          name: checkboxName.join(".."),
                          value: !!value
                        }
                      });
                    },
                    "aria-label": formatMessage({
                      id: `Settings.permissions.select-by-permission`,
                      defaultMessage: "Select {label} permission"
                    }, {
                      label: `${name} ${label2}`
                    }),
                    checked: hasSomeActionsSelected2 ? "indeterminate" : hasAllActionsSelected2
                  })
                }, label2);
              })
            })
          ]
        })
      }),
      isActive && (0, import_jsx_runtime3.jsx)(SubActionRow, {
        childrenForm: recursiveChildren,
        isFormDisabled,
        parentName: name,
        pathToDataFromActionRow: pathToData,
        propertyName,
        propertyActions,
        recursiveLevel: 0
      })
    ]
  });
};
var getRowLabelCheckboxState = (propertyActions, modifiedData, pathToContentType, propertyToCheck, targetKey) => {
  const actionIds = propertyActions.reduce((acc, current) => {
    if (current.isActionRelatedToCurrentProperty) {
      acc.push(current.actionId);
    }
    return acc;
  }, []);
  const data = actionIds.reduce((acc, current) => {
    const mainData = (0, import_get.default)(modifiedData, [
      ...pathToContentType.split(".."),
      current,
      "properties",
      propertyToCheck,
      targetKey
    ], false);
    acc[current] = mainData;
    return acc;
  }, {});
  return getCheckboxState(data);
};
var Wrapper = dt(Flex)`
  height: ${rowHeight};
  flex: 1;

  &:hover {
    ${({ $isCollapsable, theme }) => $isCollapsable && activeStyle(theme)}
  }

  ${({ $isCollapsable }) => $isCollapsable && `
      ${CarretIcon} {
        display: flex;
      }
  `}
  ${({ $isActive, theme }) => $isActive && activeStyle(theme)};
`;
var CarretIcon = dt(ForwardRef$4T)`
  display: none;

  svg {
    width: 1.4rem;
  }

  path {
    fill: ${({ theme }) => theme.colors.neutral200};
  }

  transform: rotate(${({ $isActive }) => $isActive ? "180" : "0"}deg);
  margin-left: ${({ theme }) => theme.spaces[2]};
`;
var SubActionRow = ({ childrenForm = [], isFormDisabled, recursiveLevel, pathToDataFromActionRow, propertyActions, parentName, propertyName }) => {
  const { formatMessage } = useIntl();
  const { modifiedData, onChangeParentCheckbox, onChangeSimpleCheckbox } = usePermissionsDataManager();
  const [rowToOpen, setRowToOpen] = React.useState(null);
  const handleClickToggleSubLevel = (name) => {
    setRowToOpen((prev) => {
      if (prev === name) {
        return null;
      }
      return name;
    });
  };
  const displayedRecursiveChildren = React.useMemo(() => {
    if (!rowToOpen) {
      return null;
    }
    return childrenForm.find(({ value }) => value === rowToOpen);
  }, [
    rowToOpen,
    childrenForm
  ]);
  return (0, import_jsx_runtime3.jsxs)(Box, {
    paddingLeft: `3.2rem`,
    children: [
      (0, import_jsx_runtime3.jsx)(TopTimeline, {}),
      childrenForm.map(({ label, value, required, children: subChildrenForm }, index) => {
        const isVisible = index + 1 < childrenForm.length;
        const isArrayType = Array.isArray(subChildrenForm);
        const isActive = rowToOpen === value;
        return (0, import_jsx_runtime3.jsxs)(LeftBorderTimeline, {
          $isVisible: isVisible,
          children: [
            (0, import_jsx_runtime3.jsxs)(Flex, {
              height: rowHeight,
              children: [
                (0, import_jsx_runtime3.jsx)(StyledBox, {
                  children: (0, import_jsx_runtime3.jsx)(Svg, {
                    width: "20",
                    height: "23",
                    viewBox: "0 0 20 23",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg",
                    $color: "primary200",
                    children: (0, import_jsx_runtime3.jsx)("path", {
                      fillRule: "evenodd",
                      clipRule: "evenodd",
                      d: "M7.02477 14.7513C8.65865 17.0594 11.6046 18.6059 17.5596 18.8856C18.6836 18.9384 19.5976 19.8435 19.5976 20.9688V20.9688C19.5976 22.0941 18.6841 23.0125 17.5599 22.9643C10.9409 22.6805 6.454 20.9387 3.75496 17.1258C0.937988 13.1464 0.486328 7.39309 0.486328 0.593262H4.50974C4.50974 7.54693 5.06394 11.9813 7.02477 14.7513Z",
                      fill: "#D9D8FF"
                    })
                  })
                }),
                (0, import_jsx_runtime3.jsxs)(Flex, {
                  style: {
                    flex: 1
                  },
                  children: [
                    (0, import_jsx_runtime3.jsx)(RowStyle, {
                      $level: recursiveLevel,
                      $isActive: isActive,
                      $isCollapsable: isArrayType,
                      children: (0, import_jsx_runtime3.jsxs)(CollapseLabel, {
                        alignItems: "center",
                        $isCollapsable: isArrayType,
                        ...isArrayType && {
                          onClick: () => handleClickToggleSubLevel(value),
                          "aria-expanded": isActive,
                          onKeyDown: ({ key }) => (key === "Enter" || key === " ") && handleClickToggleSubLevel(value),
                          tabIndex: 0,
                          role: "button"
                        },
                        title: label,
                        children: [
                          (0, import_jsx_runtime3.jsx)(RowLabel, {
                            ellipsis: true,
                            children: label
                          }),
                          required && (0, import_jsx_runtime3.jsx)(RequiredSign, {}),
                          (0, import_jsx_runtime3.jsx)(CarretIcon, {
                            $isActive: isActive
                          })
                        ]
                      })
                    }),
                    (0, import_jsx_runtime3.jsx)(Flex, {
                      style: {
                        flex: 1
                      },
                      children: propertyActions.map(({ actionId, label: propertyLabel, isActionRelatedToCurrentProperty }) => {
                        if (!isActionRelatedToCurrentProperty) {
                          return (0, import_jsx_runtime3.jsx)(HiddenAction, {}, actionId);
                        }
                        const checkboxName = [
                          ...pathToDataFromActionRow.split(".."),
                          actionId,
                          "properties",
                          propertyName,
                          ...parentName.split(".."),
                          value
                        ];
                        const checkboxValue = (0, import_get.default)(modifiedData, checkboxName, false);
                        if (!subChildrenForm) {
                          return (0, import_jsx_runtime3.jsx)(Flex, {
                            position: "relative",
                            width: cellWidth,
                            justifyContent: "center",
                            alignItems: "center",
                            children: (0, import_jsx_runtime3.jsx)(CheckboxImpl, {
                              disabled: isFormDisabled,
                              name: checkboxName.join(".."),
                              "aria-label": formatMessage({
                                id: `Settings.permissions.select-by-permission`,
                                defaultMessage: "Select {label} permission"
                              }, {
                                label: `${parentName} ${label} ${propertyLabel}`
                              }),
                              onCheckedChange: (value2) => {
                                onChangeSimpleCheckbox({
                                  target: {
                                    name: checkboxName.join(".."),
                                    value: !!value2
                                  }
                                });
                              },
                              checked: checkboxValue
                            })
                          }, propertyLabel);
                        }
                        const { hasAllActionsSelected, hasSomeActionsSelected } = getCheckboxState(checkboxValue);
                        return (0, import_jsx_runtime3.jsx)(Flex, {
                          position: "relative",
                          width: cellWidth,
                          justifyContent: "center",
                          alignItems: "center",
                          children: (0, import_jsx_runtime3.jsx)(CheckboxImpl, {
                            disabled: isFormDisabled,
                            name: checkboxName.join(".."),
                            "aria-label": formatMessage({
                              id: `Settings.permissions.select-by-permission`,
                              defaultMessage: "Select {label} permission"
                            }, {
                              label: `${parentName} ${label} ${propertyLabel}`
                            }),
                            // Keep same signature as packages/core/admin/admin/src/components/Roles/Permissions/index.js l.91
                            onCheckedChange: (value2) => {
                              onChangeParentCheckbox({
                                target: {
                                  name: checkboxName.join(".."),
                                  value: !!value2
                                }
                              });
                            },
                            checked: hasSomeActionsSelected ? "indeterminate" : hasAllActionsSelected
                          }, propertyLabel)
                        }, propertyLabel);
                      })
                    })
                  ]
                })
              ]
            }),
            displayedRecursiveChildren && isActive && (0, import_jsx_runtime3.jsx)(Box, {
              paddingBottom: 2,
              children: (0, import_jsx_runtime3.jsx)(SubActionRow, {
                isFormDisabled,
                parentName: `${parentName}..${value}`,
                pathToDataFromActionRow,
                propertyActions,
                propertyName,
                recursiveLevel: recursiveLevel + 1,
                childrenForm: displayedRecursiveChildren.children
              })
            })
          ]
        }, value);
      })
    ]
  });
};
var LeftBorderTimeline = dt(Box)`
  border-left: ${({ $isVisible, theme }) => $isVisible ? `4px solid ${theme.colors.primary200}` : "4px solid transparent"};
`;
var RowStyle = dt(Flex)`
  padding-left: ${({ theme }) => theme.spaces[4]};
  width: ${({ $level }) => 145 - $level * 36}px;

  &:hover {
    ${({ $isCollapsable, theme }) => $isCollapsable && activeStyle(theme)}
  }

  ${({ $isCollapsable }) => $isCollapsable && `
      ${CarretIcon} {
        display: flex;
      }
  `}
  ${({ $isActive, theme }) => $isActive && activeStyle(theme)};
`;
var RowLabel = dt(Typography)``;
var TopTimeline = dt.div`
  padding-top: ${({ theme }) => theme.spaces[2]};
  margin-top: ${({ theme }) => theme.spaces[2]};
  width: 0.4rem;
  background-color: ${({ theme }) => theme.colors.primary200};
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
`;
var StyledBox = dt(Box)`
  transform: translate(-4px, -12px);

  &:before {
    content: '';
    width: 0.4rem;
    height: 1.2rem;
    background: ${({ theme }) => theme.colors.primary200};
    display: block;
  }
`;
var Svg = dt.svg`
  position: relative;
  flex-shrink: 0;
  transform: translate(-0.5px, -1px);

  * {
    fill: ${({ theme, $color }) => theme.colors[$color]};
  }
`;
var Header = ({ headers = [], label }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime3.jsxs)(Flex, {
    children: [
      (0, import_jsx_runtime3.jsx)(Flex, {
        width: firstRowWidth,
        height: rowHeight,
        shrink: 0,
        alignItems: "center",
        paddingLeft: 6,
        children: (0, import_jsx_runtime3.jsx)(Typography, {
          variant: "sigma",
          textColor: "neutral500",
          children: formatMessage({
            id: "Settings.roles.form.permission.property-label",
            defaultMessage: "{label} permissions"
          }, {
            label
          })
        })
      }),
      headers.map((header) => {
        if (!header.isActionRelatedToCurrentProperty) {
          return (0, import_jsx_runtime3.jsx)(Flex, {
            width: cellWidth,
            shrink: 0
          }, header.label);
        }
        return (0, import_jsx_runtime3.jsx)(Flex, {
          width: cellWidth,
          shrink: 0,
          justifyContent: "center",
          children: (0, import_jsx_runtime3.jsx)(Typography, {
            variant: "sigma",
            textColor: "neutral500",
            children: formatMessage({
              id: `Settings.roles.form.permissions.${header.label.toLowerCase()}`,
              defaultMessage: header.label
            })
          })
        }, header.label);
      })
    ]
  });
};
var activeStyle = (theme) => lt`
  color: ${theme.colors.primary600};
  font-weight: ${theme.fontWeights.bold};

  ${CarretIcon} {
    path {
      fill: ${theme.colors.primary600};
    }
  }
`;

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/components/ConditionsButton.mjs
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var React2 = __toESM(require_react(), 1);
var ConditionsButtonImpl = React2.forwardRef(({ onClick, className, hasConditions = false, variant = "tertiary" }, ref) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime4.jsx)(ButtonContainer, {
    $hasConditions: hasConditions,
    className,
    children: (0, import_jsx_runtime4.jsx)(Button, {
      variant,
      startIcon: (0, import_jsx_runtime4.jsx)(ForwardRef$4d, {}),
      onClick,
      ref,
      type: "button",
      children: formatMessage({
        id: "global.settings",
        defaultMessage: "Settings"
      })
    })
  });
});
var ButtonContainer = dt(Box)`
  ${({ $hasConditions, theme }) => $hasConditions && `
    &:before {
      content: '';
      position: absolute;
      top: -3px;
      left: -10px;
      width: 6px;
      height: 6px;
      border-radius: 2rem;
      background: ${theme.colors.primary600};
    }
  `}
`;
var ConditionsButton = dt(ConditionsButtonImpl)``;

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/components/ConditionsModal.mjs
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var React3 = __toESM(require_react(), 1);
var import_get2 = __toESM(require_get(), 1);
var import_groupBy2 = __toESM(require_groupBy(), 1);
var import_upperFirst = __toESM(require_upperFirst(), 1);
var ConditionsModal = ({ actions = [], headerBreadCrumbs = [], isFormDisabled, onClose }) => {
  const { formatMessage } = useIntl();
  const { availableConditions, modifiedData, onChangeConditions } = usePermissionsDataManager();
  const arrayOfOptionsGroupedByCategory = React3.useMemo(() => {
    return Object.entries((0, import_groupBy2.default)(availableConditions, "category"));
  }, [
    availableConditions
  ]);
  const actionsToDisplay = actions.filter(
    // @ts-expect-error – TODO: fix this type issue
    ({ isDisplayed, hasSomeActionsSelected, hasAllActionsSelected }) => isDisplayed && Boolean(hasSomeActionsSelected || hasAllActionsSelected)
  );
  const [state, setState] = React3.useState(createDefaultConditionsForm2(actionsToDisplay, modifiedData, arrayOfOptionsGroupedByCategory));
  const handleChange = (name, values) => {
    setState(fn((draft) => {
      if (!draft[name]) {
        draft[name] = {};
      }
      if (!draft[name].default) {
        draft[name].default = {};
      }
      draft[name].default = values;
    }));
  };
  const handleSubmit = () => {
    const conditionsWithoutCategory = Object.entries(state).reduce((acc, current) => {
      const [key, value] = current;
      const merged = Object.values(value).reduce((acc1, current1) => {
        return {
          ...acc1,
          ...current1
        };
      }, {});
      acc[key] = merged;
      return acc;
    }, {});
    onChangeConditions(conditionsWithoutCategory);
    onClose && onClose();
  };
  const onCloseModal = () => {
    setState(createDefaultConditionsForm2(actionsToDisplay, modifiedData, arrayOfOptionsGroupedByCategory));
    onClose && onClose();
  };
  return (0, import_jsx_runtime5.jsxs)(Modal.Content, {
    children: [
      (0, import_jsx_runtime5.jsx)(Modal.Header, {
        children: (0, import_jsx_runtime5.jsx)(Breadcrumbs, {
          id: "condition-modal-breadcrumbs",
          label: headerBreadCrumbs.join(", "),
          children: headerBreadCrumbs.map((label, index, arr) => (0, import_jsx_runtime5.jsx)(Crumb, {
            isCurrent: index === arr.length - 1,
            children: (0, import_upperFirst.default)(formatMessage({
              id: label,
              defaultMessage: label
            }))
          }, label))
        })
      }),
      (0, import_jsx_runtime5.jsxs)(Modal.Body, {
        children: [
          actionsToDisplay.length === 0 && (0, import_jsx_runtime5.jsx)(Typography, {
            children: formatMessage({
              id: "Settings.permissions.conditions.no-actions",
              defaultMessage: "You first need to select actions (create, read, update, ...) before defining conditions on them."
            })
          }),
          (0, import_jsx_runtime5.jsx)("ul", {
            children: actionsToDisplay.map(({ actionId, label, pathToConditionsObject }, index) => {
              const name = pathToConditionsObject.join("..");
              return (0, import_jsx_runtime5.jsx)(ActionRow2, {
                arrayOfOptionsGroupedByCategory,
                label,
                isFormDisabled,
                isGrey: index % 2 === 0,
                name,
                onChange: handleChange,
                value: (0, import_get2.default)(state, name, {})
              }, actionId);
            })
          })
        ]
      }),
      (0, import_jsx_runtime5.jsxs)(Modal.Footer, {
        children: [
          (0, import_jsx_runtime5.jsx)(Button, {
            variant: "tertiary",
            onClick: () => onCloseModal(),
            children: formatMessage({
              id: "app.components.Button.cancel",
              defaultMessage: "Cancel"
            })
          }),
          (0, import_jsx_runtime5.jsx)(Button, {
            onClick: handleSubmit,
            children: formatMessage({
              id: "Settings.permissions.conditions.apply",
              defaultMessage: "Apply"
            })
          })
        ]
      })
    ]
  });
};
var createDefaultConditionsForm2 = (actionsToDisplay, modifiedData, arrayOfOptionsGroupedByCategory) => {
  return actionsToDisplay.reduce((acc, current) => {
    const valueFromModifiedData = (0, import_get2.default)(modifiedData, [
      ...current.pathToConditionsObject,
      "conditions"
    ], {});
    const categoryDefaultForm = arrayOfOptionsGroupedByCategory.reduce((acc2, current2) => {
      const [categoryName, relatedConditions] = current2;
      const conditionsForm = relatedConditions.reduce((acc3, current3) => {
        acc3[current3.id] = (0, import_get2.default)(valueFromModifiedData, current3.id, false);
        return acc3;
      }, {});
      acc2[categoryName] = conditionsForm;
      return acc2;
    }, {});
    acc[current.pathToConditionsObject.join("..")] = categoryDefaultForm;
    return acc;
  }, {});
};
var ActionRow2 = ({ arrayOfOptionsGroupedByCategory, isFormDisabled = false, isGrey = false, label, name, onChange, value }) => {
  const { formatMessage } = useIntl();
  const handleChange = (val) => {
    if (onChange) {
      onChange(name, getNewStateFromChangedValues(arrayOfOptionsGroupedByCategory, val));
    }
  };
  return (0, import_jsx_runtime5.jsxs)(Flex, {
    tag: "li",
    background: isGrey ? "neutral100" : "neutral0",
    paddingBottom: 3,
    paddingTop: 3,
    justifyContent: "space-evenly",
    children: [
      (0, import_jsx_runtime5.jsxs)(Flex, {
        style: {
          width: 180
        },
        children: [
          (0, import_jsx_runtime5.jsxs)(Typography, {
            variant: "sigma",
            textColor: "neutral600",
            children: [
              formatMessage({
                id: "Settings.permissions.conditions.can",
                defaultMessage: "Can"
              }),
              " "
            ]
          }),
          (0, import_jsx_runtime5.jsx)(Typography, {
            variant: "sigma",
            title: label,
            textColor: "primary600",
            ellipsis: true,
            children: formatMessage({
              id: `Settings.roles.form.permissions.${label.toLowerCase()}`,
              defaultMessage: label
            })
          }),
          (0, import_jsx_runtime5.jsxs)(Typography, {
            variant: "sigma",
            textColor: "neutral600",
            children: [
              " ",
              formatMessage({
                id: "Settings.permissions.conditions.when",
                defaultMessage: "When"
              })
            ]
          })
        ]
      }),
      (0, import_jsx_runtime5.jsx)(Box, {
        style: {
          maxWidth: 430,
          width: "100%"
        },
        children: (0, import_jsx_runtime5.jsx)(MultiSelectNested, {
          id: name,
          customizeContent: (values = []) => `${values.length} currently selected`,
          onChange: handleChange,
          value: getSelectedValues(value),
          options: getNestedOptions(arrayOfOptionsGroupedByCategory),
          disabled: isFormDisabled
        })
      })
    ]
  });
};
var getSelectedValues = (rawValue) => Object.values(rawValue).map((x) => Object.entries(x).filter(([, value]) => value).map(([key]) => key)).flat();
var getNestedOptions = (options) => options.reduce((acc, [label, children]) => {
  acc.push({
    label: capitalise(label),
    children: children.map((child) => ({
      label: child.displayName,
      value: child.id
    }))
  });
  return acc;
}, []);
var getNewStateFromChangedValues = (options, changedValues) => options.map(([, values]) => values).flat().reduce((acc, curr) => ({
  [curr.id]: changedValues.includes(curr.id),
  ...acc
}), {});

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/components/ContentTypeCollapses.mjs
var ContentTypeCollapses = ({ actions = [], isFormDisabled, pathToData, subjects = [] }) => {
  const [collapseToOpen, setCollapseToOpen] = React4.useState(null);
  const handleClickToggleCollapse = (collapseName) => () => {
    const nextCollapseToOpen = collapseToOpen === collapseName ? null : collapseName;
    setCollapseToOpen(nextCollapseToOpen);
  };
  return (0, import_jsx_runtime6.jsx)(import_jsx_runtime6.Fragment, {
    children: subjects.map(({ uid, label, properties }, index) => {
      const isActive = collapseToOpen === uid;
      const availableActions = actions.map((action) => ({
        ...action,
        isDisplayed: Array.isArray(action.subjects) && action.subjects.indexOf(uid) !== -1
      }));
      return (0, import_jsx_runtime6.jsxs)(Flex, {
        direction: "column",
        display: "inline-flex",
        alignItems: "stretch",
        minWidth: "100%",
        borderColor: isActive ? "primary600" : void 0,
        children: [
          (0, import_jsx_runtime6.jsx)(Collapse, {
            availableActions,
            isActive,
            isGrey: index % 2 === 0,
            isFormDisabled,
            label,
            onClickToggle: handleClickToggleCollapse(uid),
            pathToData: [
              pathToData,
              uid
            ].join("..")
          }),
          isActive && properties.map(({ label: propertyLabel, value, children: childrenForm }) => {
            return (0, import_jsx_runtime6.jsx)(CollapsePropertyMatrix, {
              availableActions,
              childrenForm,
              isFormDisabled,
              label: propertyLabel,
              pathToData: [
                pathToData,
                uid
              ].join(".."),
              propertyName: value
            }, value);
          })
        ]
      }, uid);
    })
  });
};
var Collapse = ({ availableActions = [], isActive = false, isGrey = false, isFormDisabled = false, label, onClickToggle, pathToData }) => {
  const { formatMessage } = useIntl();
  const { modifiedData, onChangeParentCheckbox, onChangeSimpleCheckbox } = usePermissionsDataManager();
  const [isConditionModalOpen, setIsConditionModalOpen] = React4.useState(false);
  const mainData = (0, import_get3.default)(modifiedData, pathToData.split(".."), {});
  const dataWithoutCondition = React4.useMemo(() => {
    return Object.keys(mainData).reduce((acc, current) => {
      acc[current] = (0, import_omit2.default)(mainData[current], "conditions");
      return acc;
    }, {});
  }, [
    mainData
  ]);
  const { hasAllActionsSelected, hasSomeActionsSelected } = getCheckboxState(dataWithoutCondition);
  const checkboxesActions = React4.useMemo(() => {
    return generateCheckboxesActions(availableActions, modifiedData, pathToData);
  }, [
    availableActions,
    modifiedData,
    pathToData
  ]);
  const doesConditionButtonHasConditions = checkboxesActions.some((action) => action.hasConditions);
  return (0, import_jsx_runtime6.jsxs)(BoxWrapper, {
    $isActive: isActive,
    children: [
      (0, import_jsx_runtime6.jsxs)(Wrapper2, {
        height: rowHeight,
        flex: 1,
        alignItems: "center",
        background: isGrey ? "neutral100" : "neutral0",
        children: [
          (0, import_jsx_runtime6.jsx)(RowLabelWithCheckbox, {
            isCollapsable: true,
            isFormDisabled,
            label: capitalise(label),
            checkboxName: pathToData,
            onChange: onChangeParentCheckbox,
            onClick: onClickToggle,
            someChecked: hasSomeActionsSelected,
            value: hasAllActionsSelected,
            isActive,
            children: (0, import_jsx_runtime6.jsx)(Chevron, {
              paddingLeft: 2,
              children: isActive ? (0, import_jsx_runtime6.jsx)(ForwardRef$4t, {}) : (0, import_jsx_runtime6.jsx)(ForwardRef$4z, {})
            })
          }),
          (0, import_jsx_runtime6.jsx)(Flex, {
            style: {
              flex: 1
            },
            children: checkboxesActions.map(({ actionId, hasSomeActionsSelected: hasSomeActionsSelected2, isDisplayed, ...restAction }) => {
              if (!isDisplayed) {
                return (0, import_jsx_runtime6.jsx)(HiddenAction, {}, actionId);
              }
              const { hasConditions, hasAllActionsSelected: hasAllActionsSelected2, isParentCheckbox, checkboxName, label: permissionLabel } = restAction;
              if (isParentCheckbox) {
                return (0, import_jsx_runtime6.jsxs)(Cell, {
                  justifyContent: "center",
                  alignItems: "center",
                  children: [
                    hasConditions && (0, import_jsx_runtime6.jsx)(Box, {
                      tag: "span",
                      position: "absolute",
                      top: "-6px",
                      left: "37px",
                      width: "6px",
                      height: "6px",
                      borderRadius: "20px",
                      background: "primary600"
                    }),
                    (0, import_jsx_runtime6.jsx)(CheckboxImpl, {
                      disabled: isFormDisabled,
                      name: checkboxName,
                      "aria-label": formatMessage({
                        id: `Settings.permissions.select-by-permission`,
                        defaultMessage: "Select {label} permission"
                      }, {
                        label: `${permissionLabel} ${label}`
                      }),
                      // Keep same signature as packages/core/admin/admin/src/components/Roles/Permissions/index.js l.91
                      onCheckedChange: (value) => {
                        onChangeParentCheckbox({
                          target: {
                            name: checkboxName,
                            value: !!value
                          }
                        });
                      },
                      checked: hasSomeActionsSelected2 ? "indeterminate" : hasAllActionsSelected2
                    })
                  ]
                }, actionId);
              }
              return (0, import_jsx_runtime6.jsxs)(Cell, {
                justifyContent: "center",
                alignItems: "center",
                children: [
                  hasConditions && (0, import_jsx_runtime6.jsx)(Box, {
                    tag: "span",
                    position: "absolute",
                    top: "-6px",
                    left: "37px",
                    width: "6px",
                    height: "6px",
                    borderRadius: "20px",
                    background: "primary600"
                  }),
                  (0, import_jsx_runtime6.jsx)(CheckboxImpl, {
                    disabled: isFormDisabled,
                    name: checkboxName,
                    // Keep same signature as packages/core/admin/admin/src/components/Roles/Permissions/index.js l.91
                    onCheckedChange: (value) => {
                      onChangeSimpleCheckbox({
                        target: {
                          name: checkboxName,
                          value: !!value
                        }
                      });
                    },
                    checked: hasConditions ? "indeterminate" : hasAllActionsSelected2
                  })
                ]
              }, actionId);
            })
          })
        ]
      }),
      (0, import_jsx_runtime6.jsx)(Box, {
        bottom: "10px",
        right: "9px",
        position: "absolute",
        children: (0, import_jsx_runtime6.jsxs)(Modal.Root, {
          open: isConditionModalOpen,
          onOpenChange: () => {
            setIsConditionModalOpen((prev) => !prev);
          },
          children: [
            (0, import_jsx_runtime6.jsx)(Modal.Trigger, {
              children: (0, import_jsx_runtime6.jsx)(ConditionsButton, {
                hasConditions: doesConditionButtonHasConditions
              })
            }),
            (0, import_jsx_runtime6.jsx)(ConditionsModal, {
              headerBreadCrumbs: [
                label,
                "Settings.permissions.conditions.conditions"
              ],
              actions: checkboxesActions,
              isFormDisabled,
              onClose: () => {
                setIsConditionModalOpen(false);
              }
            })
          ]
        })
      })
    ]
  });
};
var generateCheckboxesActions = (availableActions, modifiedData, pathToData) => {
  return availableActions.map(({ actionId, isDisplayed, applyToProperties, label }) => {
    if (!isDisplayed) {
      return {
        actionId,
        hasSomeActionsSelected: false,
        isDisplayed
      };
    }
    const baseCheckboxNameArray = [
      ...pathToData.split(".."),
      actionId
    ];
    const checkboxNameArray = (0, import_isEmpty2.default)(applyToProperties) ? [
      ...baseCheckboxNameArray,
      "properties",
      "enabled"
    ] : baseCheckboxNameArray;
    const conditionsValue = (0, import_get3.default)(modifiedData, [
      ...baseCheckboxNameArray,
      "conditions"
    ], null);
    const baseCheckboxAction = {
      actionId,
      checkboxName: checkboxNameArray.join(".."),
      hasConditions: createArrayOfValues(conditionsValue).some((val) => val),
      isDisplayed,
      label,
      pathToConditionsObject: baseCheckboxNameArray
    };
    if ((0, import_isEmpty2.default)(applyToProperties)) {
      const value = (0, import_get3.default)(modifiedData, checkboxNameArray, false);
      return {
        ...baseCheckboxAction,
        hasAllActionsSelected: value,
        hasSomeActionsSelected: value,
        isParentCheckbox: false
      };
    }
    const mainData = (0, import_get3.default)(modifiedData, checkboxNameArray, null);
    const { hasAllActionsSelected, hasSomeActionsSelected } = getCheckboxState(mainData);
    return {
      ...baseCheckboxAction,
      hasAllActionsSelected,
      hasSomeActionsSelected,
      isParentCheckbox: true
    };
  });
};
var activeRowStyle = (theme, isActive) => `
  ${Wrapper2} {
    background-color: ${theme.colors.primary100};
    color: ${theme.colors.primary600};
    border-radius: ${isActive ? "2px 2px 0 0" : "2px"};
    font-weight: ${theme.fontWeights.bold};
  }

  ${Chevron} {
    display: flex;
  }
  ${ConditionsButton} {
    display: block;
  }

  &:focus-within {
    ${() => activeRowStyle(theme, isActive)}
  }
`;
var Wrapper2 = dt(Flex)`
  border: 1px solid transparent;
`;
var BoxWrapper = dt.div`
  display: inline-flex;
  min-width: 100%;
  position: relative;

  ${ConditionsButton} {
    display: none;
  }

  ${({ $isActive, theme }) => $isActive && activeRowStyle(theme, $isActive)}

  &:hover {
    ${({ theme, $isActive }) => activeRowStyle(theme, $isActive)}
  }
`;
var Cell = dt(Flex)`
  width: ${cellWidth};
  position: relative;
`;
var Chevron = dt(Box)`
  display: none;

  svg {
    width: 1.4rem;
  }

  path {
    fill: ${({ theme }) => theme.colors.primary600};
  }
`;

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/components/GlobalActions.mjs
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var React5 = __toESM(require_react(), 1);
var import_get4 = __toESM(require_get(), 1);
var GlobalActions = ({ actions = [], isFormDisabled, kind }) => {
  const { formatMessage } = useIntl();
  const { modifiedData, onChangeCollectionTypeGlobalActionCheckbox } = usePermissionsDataManager();
  const displayedActions = actions.filter(({ subjects }) => subjects && subjects.length);
  const checkboxesState = React5.useMemo(() => {
    const actionsIds = displayedActions.map(({ actionId }) => actionId);
    const data = modifiedData[kind];
    const relatedActionsData = actionsIds.reduce((acc, actionId) => {
      Object.keys(data).forEach((ctUid) => {
        const actionIdData = (0, import_get4.default)(data, [
          ctUid,
          actionId
        ]);
        const actionIdState = {
          [ctUid]: removeConditionKeyFromData(actionIdData)
        };
        if (!acc[actionId]) {
          acc[actionId] = actionIdState;
        } else {
          acc[actionId] = {
            ...acc[actionId],
            ...actionIdState
          };
        }
      });
      return acc;
    }, {});
    const checkboxesState2 = Object.keys(relatedActionsData).reduce((acc, current) => {
      acc[current] = getCheckboxState(relatedActionsData[current]);
      return acc;
    }, {});
    return checkboxesState2;
  }, [
    modifiedData,
    displayedActions,
    kind
  ]);
  return (0, import_jsx_runtime7.jsx)(Box, {
    paddingBottom: 4,
    paddingTop: 6,
    style: {
      paddingLeft: firstRowWidth
    },
    children: (0, import_jsx_runtime7.jsx)(Flex, {
      gap: 0,
      children: displayedActions.map(({ label, actionId }) => {
        return (0, import_jsx_runtime7.jsxs)(Flex, {
          shrink: 0,
          width: cellWidth,
          direction: "column",
          alignItems: "center",
          justifyContent: "center",
          gap: 3,
          children: [
            (0, import_jsx_runtime7.jsx)(Typography, {
              variant: "sigma",
              textColor: "neutral500",
              children: formatMessage({
                id: `Settings.roles.form.permissions.${label.toLowerCase()}`,
                defaultMessage: label
              })
            }),
            (0, import_jsx_runtime7.jsx)(CheckboxImpl, {
              disabled: isFormDisabled,
              onCheckedChange: (value) => {
                onChangeCollectionTypeGlobalActionCheckbox(kind, actionId, !!value);
              },
              name: actionId,
              "aria-label": formatMessage({
                id: `Settings.permissions.select-all-by-permission`,
                defaultMessage: "Select all {label} permissions"
              }, {
                label: formatMessage({
                  id: `Settings.roles.form.permissions.${label.toLowerCase()}`,
                  defaultMessage: label
                })
              }),
              checked: (0, import_get4.default)(checkboxesState, [
                actionId,
                "hasSomeActionsSelected"
              ], false) ? "indeterminate" : (0, import_get4.default)(checkboxesState, [
                actionId,
                "hasAllActionsSelected"
              ], false)
            })
          ]
        }, actionId);
      })
    })
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/components/ContentTypes.mjs
var ContentTypes = ({ isFormDisabled, kind, layout: { actions, subjects } }) => {
  const sortedSubjects = [
    ...subjects
  ].sort((a, b) => a.label.localeCompare(b.label));
  return (0, import_jsx_runtime8.jsxs)(Box, {
    background: "neutral0",
    children: [
      (0, import_jsx_runtime8.jsx)(GlobalActions, {
        actions,
        kind,
        isFormDisabled
      }),
      (0, import_jsx_runtime8.jsx)(ContentTypeCollapses, {
        actions,
        isFormDisabled,
        pathToData: kind,
        subjects: sortedSubjects
      })
    ]
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/components/PluginsAndSettings.mjs
var import_jsx_runtime9 = __toESM(require_jsx_runtime(), 1);
var React6 = __toESM(require_react(), 1);
var import_get5 = __toESM(require_get(), 1);
var PluginsAndSettingsPermissions = ({ layout, ...restProps }) => {
  return (0, import_jsx_runtime9.jsx)(Box, {
    padding: 6,
    background: "neutral0",
    children: (0, import_jsx_runtime9.jsx)(Accordion.Root, {
      size: "M",
      children: layout.map(({ category, categoryId, childrenForm }, index) => {
        return (0, import_jsx_runtime9.jsx)(Row, {
          childrenForm,
          variant: index % 2 === 1 ? "primary" : "secondary",
          name: category,
          pathToData: [
            restProps.kind,
            categoryId
          ],
          ...restProps
        }, category);
      })
    })
  });
};
var Row = ({ childrenForm, kind, name, isFormDisabled = false, variant, pathToData }) => {
  const { formatMessage } = useIntl();
  const categoryName = name.split("::").pop() ?? "";
  const categoryDisplayName = categoryName === "upload" ? "Media Library" : capitalise(categoryName.replace(/-/g, " "));
  return (0, import_jsx_runtime9.jsxs)(Accordion.Item, {
    value: name,
    children: [
      (0, import_jsx_runtime9.jsx)(Accordion.Header, {
        variant,
        children: (0, import_jsx_runtime9.jsx)(Accordion.Trigger, {
          caretPosition: "right",
          description: `${formatMessage({
            id: "Settings.permissions.category",
            defaultMessage: categoryName
          }, {
            category: categoryName
          })} ${kind === "plugins" ? "plugin" : kind}`,
          children: categoryDisplayName
        })
      }),
      (0, import_jsx_runtime9.jsx)(Accordion.Content, {
        children: (0, import_jsx_runtime9.jsx)(Box, {
          padding: 6,
          children: childrenForm.map(({ actions, subCategoryName, subCategoryId }) => (0, import_jsx_runtime9.jsx)(SubCategory, {
            actions,
            categoryName,
            isFormDisabled,
            subCategoryName,
            pathToData: [
              ...pathToData,
              subCategoryId
            ]
          }, subCategoryName))
        })
      })
    ]
  });
};
var SubCategory = ({ actions = [], categoryName, isFormDisabled, subCategoryName, pathToData }) => {
  const { modifiedData, onChangeParentCheckbox, onChangeSimpleCheckbox } = usePermissionsDataManager();
  const [isConditionModalOpen, setIsConditionModalOpen] = React6.useState(false);
  const { formatMessage } = useIntl();
  const mainData = (0, import_get5.default)(modifiedData, pathToData, {});
  const dataWithoutCondition = React6.useMemo(() => {
    return Object.keys(mainData).reduce((acc, current) => {
      acc[current] = removeConditionKeyFromData(mainData[current]);
      return acc;
    }, {});
  }, [
    mainData
  ]);
  const { hasAllActionsSelected, hasSomeActionsSelected } = getCheckboxState(dataWithoutCondition);
  const formattedActions = React6.useMemo(() => {
    return actions.map((action) => {
      const checkboxName = [
        ...pathToData,
        action.action,
        "properties",
        "enabled"
      ];
      const checkboxValue = (0, import_get5.default)(modifiedData, checkboxName, false);
      const conditionValue = (0, import_get5.default)(modifiedData, [
        ...pathToData,
        action.action,
        "conditions"
      ], {});
      const hasConditions = createArrayOfValues(conditionValue).some((val) => val);
      return {
        ...action,
        isDisplayed: checkboxValue,
        checkboxName: checkboxName.join(".."),
        hasSomeActionsSelected: checkboxValue,
        value: checkboxValue,
        hasConditions,
        label: action.displayName,
        actionId: action.action,
        pathToConditionsObject: [
          ...pathToData,
          action.action
        ]
      };
    });
  }, [
    actions,
    modifiedData,
    pathToData
  ]);
  const datum = (0, import_get5.default)(modifiedData, [
    ...pathToData
  ], {});
  const doesButtonHasCondition = createArrayOfValues(Object.entries(datum).reduce((acc, current) => {
    const [catName, { conditions }] = current;
    acc[catName] = conditions;
    return acc;
  }, {})).some((val) => val);
  return (0, import_jsx_runtime9.jsx)(import_jsx_runtime9.Fragment, {
    children: (0, import_jsx_runtime9.jsxs)(Box, {
      children: [
        (0, import_jsx_runtime9.jsxs)(Flex, {
          justifyContent: "space-between",
          alignItems: "center",
          children: [
            (0, import_jsx_runtime9.jsx)(Box, {
              paddingRight: 4,
              children: (0, import_jsx_runtime9.jsx)(Typography, {
                variant: "sigma",
                textColor: "neutral600",
                children: subCategoryName
              })
            }),
            (0, import_jsx_runtime9.jsx)(Border, {
              flex: 1
            }),
            (0, import_jsx_runtime9.jsx)(Box, {
              paddingLeft: 4,
              children: (0, import_jsx_runtime9.jsx)(CheckboxImpl, {
                name: pathToData.join(".."),
                disabled: isFormDisabled,
                // Keep same signature as packages/core/admin/admin/src/components/Roles/Permissions/index.js l.91
                onCheckedChange: (value) => {
                  onChangeParentCheckbox({
                    target: {
                      name: pathToData.join(".."),
                      value: !!value
                    }
                  });
                },
                checked: hasSomeActionsSelected ? "indeterminate" : hasAllActionsSelected,
                children: formatMessage({
                  id: "app.utils.select-all",
                  defaultMessage: "Select all"
                })
              })
            })
          ]
        }),
        (0, import_jsx_runtime9.jsxs)(Flex, {
          paddingTop: 6,
          paddingBottom: 6,
          children: [
            (0, import_jsx_runtime9.jsx)(Grid.Root, {
              gap: 2,
              style: {
                flex: 1
              },
              children: formattedActions.map(({ checkboxName, value, action, displayName, hasConditions }) => {
                return (0, import_jsx_runtime9.jsx)(Grid.Item, {
                  col: 3,
                  direction: "column",
                  alignItems: "start",
                  children: (0, import_jsx_runtime9.jsx)(CheckboxWrapper, {
                    $disabled: isFormDisabled,
                    $hasConditions: hasConditions,
                    children: (0, import_jsx_runtime9.jsx)(CheckboxImpl, {
                      name: checkboxName,
                      disabled: isFormDisabled,
                      // Keep same signature as packages/core/admin/admin/src/components/Roles/Permissions/index.js l.91
                      onCheckedChange: (value2) => {
                        onChangeSimpleCheckbox({
                          target: {
                            name: checkboxName,
                            value: !!value2
                          }
                        });
                      },
                      checked: value,
                      children: displayName
                    })
                  })
                }, action);
              })
            }),
            (0, import_jsx_runtime9.jsxs)(Modal.Root, {
              open: isConditionModalOpen,
              onOpenChange: () => {
                setIsConditionModalOpen((prev) => !prev);
              },
              children: [
                (0, import_jsx_runtime9.jsx)(Modal.Trigger, {
                  children: (0, import_jsx_runtime9.jsx)(ConditionsButton, {
                    hasConditions: doesButtonHasCondition
                  })
                }),
                (0, import_jsx_runtime9.jsx)(ConditionsModal, {
                  headerBreadCrumbs: [
                    categoryName,
                    subCategoryName
                  ],
                  actions: formattedActions,
                  isFormDisabled,
                  onClose: () => {
                    setIsConditionModalOpen(false);
                  }
                })
              ]
            })
          ]
        })
      ]
    })
  });
};
var Border = dt(Box)`
  align-self: center;
  border-top: 1px solid ${({ theme }) => theme.colors.neutral150};
`;
var CheckboxWrapper = dt.div`
  position: relative;
  word-break: keep-all;
  ${({ $hasConditions, $disabled, theme }) => $hasConditions && `
    &:before {
      content: '';
      position: absolute;
      top: -0.4rem;
      left: -0.8rem;
      width: 0.6rem;
      height: 0.6rem;
      border-radius: 2rem;
      background: ${$disabled ? theme.colors.neutral100 : theme.colors.primary600};
    }
  `}
`;

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/components/Permissions.mjs
var TAB_LABELS = [
  {
    labelId: "app.components.LeftMenuLinkContainer.collectionTypes",
    defaultMessage: "Collection Types",
    id: "collectionTypes"
  },
  {
    labelId: "app.components.LeftMenuLinkContainer.singleTypes",
    id: "singleTypes",
    defaultMessage: "Single Types"
  },
  {
    labelId: "app.components.LeftMenuLinkContainer.plugins",
    defaultMessage: "Plugins",
    id: "plugins"
  },
  {
    labelId: "app.components.LeftMenuLinkContainer.settings",
    defaultMessage: "Settings",
    id: "settings"
  }
];
var Permissions = React7.forwardRef(({ layout, isFormDisabled, permissions = [] }, api) => {
  const [{ initialData, layouts, modifiedData }, dispatch] = React7.useReducer(reducer, initialState, () => init(layout, permissions));
  const { formatMessage } = useIntl();
  React7.useImperativeHandle(api, () => {
    return {
      getPermissions() {
        const collectionTypesDiff = difference(initialData.collectionTypes, modifiedData.collectionTypes);
        const singleTypesDiff = difference(initialData.singleTypes, modifiedData.singleTypes);
        const contentTypesDiff = {
          ...collectionTypesDiff,
          ...singleTypesDiff
        };
        let didUpdateConditions;
        if ((0, import_isEmpty3.default)(contentTypesDiff)) {
          didUpdateConditions = false;
        } else {
          didUpdateConditions = Object.values(contentTypesDiff).some((permission = {}) => {
            return Object.values(permission).some((permissionValue) => (0, import_has2.default)(permissionValue, "conditions"));
          });
        }
        return {
          permissionsToSend: formatPermissionsForAPI(modifiedData),
          didUpdateConditions
        };
      },
      resetForm() {
        dispatch({
          type: "RESET_FORM"
        });
      },
      setFormAfterSubmit() {
        dispatch({
          type: "SET_FORM_AFTER_SUBMIT"
        });
      }
    };
  });
  const handleChangeCollectionTypeLeftActionRowCheckbox = (pathToCollectionType, propertyName, rowName, value) => {
    dispatch({
      type: "ON_CHANGE_COLLECTION_TYPE_ROW_LEFT_CHECKBOX",
      pathToCollectionType,
      propertyName,
      rowName,
      value
    });
  };
  const handleChangeCollectionTypeGlobalActionCheckbox = (collectionTypeKind, actionId, value) => {
    dispatch({
      type: "ON_CHANGE_COLLECTION_TYPE_GLOBAL_ACTION_CHECKBOX",
      collectionTypeKind,
      actionId,
      value
    });
  };
  const handleChangeConditions = (conditions) => {
    dispatch({
      type: "ON_CHANGE_CONDITIONS",
      conditions
    });
  };
  const handleChangeSimpleCheckbox = React7.useCallback(({ target: { name, value } }) => {
    dispatch({
      type: "ON_CHANGE_SIMPLE_CHECKBOX",
      keys: name,
      value
    });
  }, []);
  const handleChangeParentCheckbox = React7.useCallback(({ target: { name, value } }) => {
    dispatch({
      type: "ON_CHANGE_TOGGLE_PARENT_CHECKBOX",
      keys: name,
      value
    });
  }, []);
  return (0, import_jsx_runtime10.jsx)(PermissionsDataManagerProvider, {
    availableConditions: layout.conditions,
    modifiedData,
    onChangeConditions: handleChangeConditions,
    onChangeSimpleCheckbox: handleChangeSimpleCheckbox,
    onChangeParentCheckbox: handleChangeParentCheckbox,
    onChangeCollectionTypeLeftActionRowCheckbox: handleChangeCollectionTypeLeftActionRowCheckbox,
    onChangeCollectionTypeGlobalActionCheckbox: handleChangeCollectionTypeGlobalActionCheckbox,
    children: (0, import_jsx_runtime10.jsxs)(Tabs.Root, {
      defaultValue: TAB_LABELS[0].id,
      children: [
        (0, import_jsx_runtime10.jsx)(Tabs.List, {
          "aria-label": formatMessage({
            id: "Settings.permissions.users.tabs.label",
            defaultMessage: "Tabs Permissions"
          }),
          children: TAB_LABELS.map((tabLabel) => (0, import_jsx_runtime10.jsx)(Tabs.Trigger, {
            value: tabLabel.id,
            children: formatMessage({
              id: tabLabel.labelId,
              defaultMessage: tabLabel.defaultMessage
            })
          }, tabLabel.id))
        }),
        (0, import_jsx_runtime10.jsx)(Tabs.Content, {
          value: TAB_LABELS[0].id,
          children: (0, import_jsx_runtime10.jsx)(ContentTypes, {
            layout: layouts.collectionTypes,
            kind: "collectionTypes",
            isFormDisabled
          })
        }),
        (0, import_jsx_runtime10.jsx)(Tabs.Content, {
          value: TAB_LABELS[1].id,
          children: (0, import_jsx_runtime10.jsx)(ContentTypes, {
            layout: layouts.singleTypes,
            kind: "singleTypes",
            isFormDisabled
          })
        }),
        (0, import_jsx_runtime10.jsx)(Tabs.Content, {
          value: TAB_LABELS[2].id,
          children: (0, import_jsx_runtime10.jsx)(PluginsAndSettingsPermissions, {
            layout: layouts.plugins,
            kind: "plugins",
            isFormDisabled
          })
        }),
        (0, import_jsx_runtime10.jsx)(Tabs.Content, {
          value: TAB_LABELS[3].id,
          children: (0, import_jsx_runtime10.jsx)(PluginsAndSettingsPermissions, {
            layout: layouts.settings,
            kind: "settings",
            isFormDisabled
          })
        })
      ]
    })
  });
});
var initialState = {
  initialData: {},
  modifiedData: {},
  layouts: {}
};
var reducer = (state, action) => fn(state, (draftState) => {
  switch (action.type) {
    case "ON_CHANGE_COLLECTION_TYPE_GLOBAL_ACTION_CHECKBOX": {
      const { collectionTypeKind, actionId, value } = action;
      const pathToData = [
        "modifiedData",
        collectionTypeKind
      ];
      Object.keys((0, import_get6.default)(state, pathToData)).forEach((collectionType) => {
        const collectionTypeActionData = (0, import_get6.default)(state, [
          ...pathToData,
          collectionType,
          actionId
        ], void 0);
        if (collectionTypeActionData) {
          let updatedValues = updateValues(collectionTypeActionData, value);
          if (!value && updatedValues.conditions) {
            const updatedConditions = updateValues(updatedValues.conditions, false);
            updatedValues = {
              ...updatedValues,
              conditions: updatedConditions
            };
          }
          (0, import_set.default)(draftState, [
            ...pathToData,
            collectionType,
            actionId
          ], updatedValues);
        }
      });
      break;
    }
    case "ON_CHANGE_COLLECTION_TYPE_ROW_LEFT_CHECKBOX": {
      const { pathToCollectionType, propertyName, rowName, value } = action;
      let nextModifiedDataState = (0, import_cloneDeep.default)(state.modifiedData);
      const pathToModifiedDataCollectionType = pathToCollectionType.split("..");
      const objToUpdate = (0, import_get6.default)(nextModifiedDataState, pathToModifiedDataCollectionType, {});
      Object.keys(objToUpdate).forEach((actionId) => {
        if ((0, import_has2.default)(objToUpdate[actionId], `properties.${propertyName}`)) {
          const objValue = (0, import_get6.default)(objToUpdate, [
            actionId,
            "properties",
            propertyName,
            rowName
          ]);
          const pathToDataToSet = [
            ...pathToModifiedDataCollectionType,
            actionId,
            "properties",
            propertyName,
            rowName
          ];
          if (!isObject(objValue)) {
            (0, import_set.default)(nextModifiedDataState, pathToDataToSet, value);
          } else {
            const updatedValue = updateValues(objValue, value);
            (0, import_set.default)(nextModifiedDataState, pathToDataToSet, updatedValue);
          }
        }
      });
      if (!value) {
        nextModifiedDataState = updateConditionsToFalse(nextModifiedDataState);
      }
      (0, import_set.default)(draftState, "modifiedData", nextModifiedDataState);
      break;
    }
    case "ON_CHANGE_CONDITIONS": {
      Object.entries(action.conditions).forEach((array) => {
        const [stringPathToData, conditionsToUpdate] = array;
        (0, import_set.default)(draftState, [
          "modifiedData",
          ...stringPathToData.split(".."),
          "conditions"
        ], conditionsToUpdate);
      });
      break;
    }
    case "ON_CHANGE_SIMPLE_CHECKBOX": {
      let nextModifiedDataState = (0, import_cloneDeep.default)(state.modifiedData);
      (0, import_set.default)(nextModifiedDataState, [
        ...action.keys.split("..")
      ], action.value);
      if (!action.value) {
        nextModifiedDataState = updateConditionsToFalse(nextModifiedDataState);
      }
      (0, import_set.default)(draftState, "modifiedData", nextModifiedDataState);
      break;
    }
    case "ON_CHANGE_TOGGLE_PARENT_CHECKBOX": {
      const { keys, value } = action;
      const pathToValue = [
        ...keys.split("..")
      ];
      let nextModifiedDataState = (0, import_cloneDeep.default)(state.modifiedData);
      const oldValues = (0, import_get6.default)(nextModifiedDataState, pathToValue, {});
      const updatedValues = updateValues(oldValues, value);
      (0, import_set.default)(nextModifiedDataState, pathToValue, updatedValues);
      if (!value) {
        nextModifiedDataState = updateConditionsToFalse(nextModifiedDataState);
      }
      (0, import_set.default)(draftState, [
        "modifiedData"
      ], nextModifiedDataState);
      break;
    }
    case "RESET_FORM": {
      draftState.modifiedData = state.initialData;
      break;
    }
    case "SET_FORM_AFTER_SUBMIT": {
      draftState.initialData = state.modifiedData;
      break;
    }
    default:
      return draftState;
  }
});
var init = (layout, permissions) => {
  const { conditions, sections: { collectionTypes, singleTypes, plugins, settings } } = layout;
  const layouts = {
    collectionTypes,
    singleTypes,
    plugins: formatLayout(plugins, "plugin"),
    settings: formatLayout(settings, "category")
  };
  const defaultForm = {
    collectionTypes: createDefaultCTForm(collectionTypes, conditions, permissions),
    singleTypes: createDefaultCTForm(singleTypes, conditions, permissions),
    plugins: createDefaultForm(layouts.plugins, conditions, permissions),
    settings: createDefaultForm(layouts.settings, conditions, permissions)
  };
  return {
    initialData: defaultForm,
    modifiedData: defaultForm,
    layouts
  };
};

export {
  Permissions
};
//# sourceMappingURL=chunk-PFR3HBO7.js.map
