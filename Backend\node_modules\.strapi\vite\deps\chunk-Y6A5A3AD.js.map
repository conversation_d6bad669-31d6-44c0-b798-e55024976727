{"version": 3, "sources": ["../../../@strapi/admin/admin/src/components/GuidedTour/constants.ts", "../../../@strapi/admin/admin/src/components/GuidedTour/Ornaments.tsx"], "sourcesContent": ["const LAYOUT_DATA = {\n  contentTypeBuilder: {\n    home: {\n      title: {\n        id: 'app.components.GuidedTour.home.CTB.title',\n        defaultMessage: '🧠 Build the content structure',\n      },\n      cta: {\n        title: {\n          id: 'app.components.GuidedTour.home.CTB.cta.title',\n          defaultMessage: 'Go to the Content type Builder',\n        },\n        type: 'REDIRECT',\n        target: '/plugins/content-type-builder',\n      },\n      trackingEvent: 'didClickGuidedTourHomepageContentTypeBuilder',\n    },\n    create: {\n      title: {\n        id: 'app.components.GuidedTour.CTB.create.title',\n        defaultMessage: '🧠 Create a first Collection type',\n      },\n      content: {\n        id: 'app.components.GuidedTour.CTB.create.content',\n        defaultMessage:\n          '<p>Collection types help you manage several entries, Single types are suitable to manage only one entry.</p> <p>Ex: For a Blog website, Articles would be a Collection type whereas a Homepage would be a Single type.</p>',\n      },\n      cta: {\n        title: {\n          id: 'app.components.GuidedTour.CTB.create.cta.title',\n          defaultMessage: 'Build a Collection type',\n        },\n        type: 'CLOSE',\n      },\n      trackingEvent: 'didClickGuidedTourStep1CollectionType',\n    },\n    success: {\n      title: {\n        id: 'app.components.GuidedTour.CTB.success.title',\n        defaultMessage: 'Step 1: Completed ✅',\n      },\n      content: {\n        id: 'app.components.GuidedTour.CTB.success.content',\n        defaultMessage: '<p>Good going!</p><b>⚡️ What would you like to share with the world?</b>',\n      },\n      cta: {\n        title: {\n          id: 'app.components.GuidedTour.create-content',\n          defaultMessage: 'Create content',\n        },\n        type: 'REDIRECT',\n        target: '/content-manager',\n      },\n      trackingEvent: 'didCreateGuidedTourCollectionType',\n    },\n  },\n  contentManager: {\n    home: {\n      title: {\n        id: 'app.components.GuidedTour.home.CM.title',\n        defaultMessage: '⚡️ What would you like to share with the world?',\n      },\n      cta: {\n        title: {\n          id: 'app.components.GuidedTour.create-content',\n          defaultMessage: 'Create content',\n        },\n        type: 'REDIRECT',\n        target: '/content-manager',\n      },\n      trackingEvent: 'didClickGuidedTourHomepageContentManager',\n    },\n    create: {\n      title: {\n        id: 'app.components.GuidedTour.CM.create.title',\n        defaultMessage: '⚡️ Create content',\n      },\n      content: {\n        id: 'app.components.GuidedTour.CM.create.content',\n        defaultMessage:\n          \"<p>Create and manage all the content here in the Content Manager.</p><p>Ex: Taking the Blog website example further, one can write an Article, save and publish it as they like.</p><p>💡 Quick tip - Don't forget to hit publish on the content you create.</p>\",\n      },\n      cta: {\n        title: {\n          id: 'app.components.GuidedTour.create-content',\n          defaultMessage: 'Create content',\n        },\n        type: 'CLOSE',\n      },\n      trackingEvent: 'didClickGuidedTourStep2ContentManager',\n    },\n    success: {\n      title: {\n        id: 'app.components.GuidedTour.CM.success.title',\n        defaultMessage: 'Step 2: Completed ✅',\n      },\n      content: {\n        id: 'app.components.GuidedTour.CM.success.content',\n        defaultMessage: '<p>Awesome, one last step to go!</p><b>🚀  See content in action</b>',\n      },\n      cta: {\n        title: {\n          id: 'app.components.GuidedTour.CM.success.cta.title',\n          defaultMessage: 'Test the API',\n        },\n        type: 'REDIRECT',\n        target: '/settings/api-tokens',\n      },\n      trackingEvent: 'didCreateGuidedTourEntry',\n    },\n  },\n  apiTokens: {\n    home: {\n      title: {\n        id: 'app.components.GuidedTour.apiTokens.create.title',\n        defaultMessage: '🚀 See content in action',\n      },\n      cta: {\n        title: {\n          id: 'app.components.GuidedTour.home.apiTokens.cta.title',\n          defaultMessage: 'Test the API',\n        },\n        type: 'REDIRECT',\n        target: '/settings/api-tokens',\n      },\n      trackingEvent: 'didClickGuidedTourHomepageApiTokens',\n    },\n    create: {\n      title: {\n        id: 'app.components.GuidedTour.apiTokens.create.title',\n        defaultMessage: '🚀 See content in action',\n      },\n      content: {\n        id: 'app.components.GuidedTour.apiTokens.create.content',\n        defaultMessage:\n          '<p>Generate an authentication token here and retrieve the content you just created.</p>',\n      },\n      cta: {\n        title: {\n          id: 'app.components.GuidedTour.apiTokens.create.cta.title',\n          defaultMessage: 'Generate an API Token',\n        },\n        type: 'CLOSE',\n      },\n      trackingEvent: 'didClickGuidedTourStep3ApiTokens',\n    },\n    success: {\n      title: {\n        id: 'app.components.GuidedTour.apiTokens.success.title',\n        defaultMessage: 'Step 3: Completed ✅',\n      },\n      content: {\n        id: 'app.components.GuidedTour.apiTokens.success.content',\n        defaultMessage:\n          \"<p>See content in action by making an HTTP request:</p><ul><li><p>To this URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>With the header: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>For more ways to interact with content, see the <documentationLink>documentation</documentationLink>.</p>\",\n      },\n      trackingEvent: 'didGenerateGuidedTourApiTokens',\n    },\n  },\n} as const;\n\nconst STATES = {\n  IS_DONE: 'IS_DONE',\n  IS_ACTIVE: 'IS_ACTIVE',\n  IS_NOT_DONE: 'IS_NOT_DONE',\n} as const;\n\ntype LayoutData = typeof LAYOUT_DATA;\ntype States = keyof typeof STATES;\n\nexport { LAYOUT_DATA, STATES };\nexport type { LayoutData, States };\n", "import { Box, BoxProps, Flex, FlexProps, Typography } from '@strapi/design-system';\nimport { Check } from '@strapi/icons';\n\nimport { STATES, States } from './constants';\n\n/* -------------------------------------------------------------------------------------------------\n * Number\n * -----------------------------------------------------------------------------------------------*/\n\ninterface NumberProps extends FlexProps {\n  children: number;\n  state: States;\n}\n\nconst Number = ({ children, state, ...props }: NumberProps) => {\n  return state === STATES.IS_DONE || state === STATES.IS_ACTIVE ? (\n    <Flex\n      background=\"primary600\"\n      padding={2}\n      borderRadius=\"50%\"\n      width={`3rem`}\n      height={`3rem`}\n      justifyContent=\"center\"\n      {...props}\n    >\n      {state === STATES.IS_DONE ? (\n        <Check aria-hidden width={`1.6rem`} fill=\"neutral0\" />\n      ) : (\n        <Typography fontWeight=\"semiBold\" textColor=\"neutral0\">\n          {children}\n        </Typography>\n      )}\n    </Flex>\n  ) : (\n    <Flex\n      borderColor=\"neutral500\"\n      borderWidth=\"1px\"\n      borderStyle=\"solid\"\n      padding={2}\n      borderRadius=\"50%\"\n      width={`3rem`}\n      height={`3rem`}\n      justifyContent=\"center\"\n      {...props}\n    >\n      <Typography fontWeight=\"semiBold\" textColor=\"neutral600\">\n        {children}\n      </Typography>\n    </Flex>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * VerticalDivider\n * -----------------------------------------------------------------------------------------------*/\n\ninterface VerticalDividerProps extends BoxProps {\n  state: States;\n}\n\nconst VerticalDivider = ({ state, ...props }: VerticalDividerProps) => (\n  <Box\n    width={`0.2rem`}\n    height=\"100%\"\n    background={state === STATES.IS_NOT_DONE ? 'neutral300' : 'primary500'}\n    hasRadius\n    minHeight={state === STATES.IS_ACTIVE ? `8.5rem` : `6.5rem`}\n    {...props}\n  />\n);\n\nexport { Number, VerticalDivider };\nexport type { NumberProps, VerticalDividerProps };\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAAMA,cAAc;EAClBC,oBAAoB;IAClBC,MAAM;MACJC,OAAO;QACLC,IAAI;QACJC,gBAAgB;MAClB;MACAC,KAAK;QACHH,OAAO;UACLC,IAAI;UACJC,gBAAgB;QAClB;QACAE,MAAM;QACNC,QAAQ;MACV;MACAC,eAAe;IACjB;IACAC,QAAQ;MACNP,OAAO;QACLC,IAAI;QACJC,gBAAgB;MAClB;MACAM,SAAS;QACPP,IAAI;QACJC,gBACE;MACJ;MACAC,KAAK;QACHH,OAAO;UACLC,IAAI;UACJC,gBAAgB;QAClB;QACAE,MAAM;MACR;MACAE,eAAe;IACjB;IACAG,SAAS;MACPT,OAAO;QACLC,IAAI;QACJC,gBAAgB;MAClB;MACAM,SAAS;QACPP,IAAI;QACJC,gBAAgB;MAClB;MACAC,KAAK;QACHH,OAAO;UACLC,IAAI;UACJC,gBAAgB;QAClB;QACAE,MAAM;QACNC,QAAQ;MACV;MACAC,eAAe;IACjB;EACF;EACAI,gBAAgB;IACdX,MAAM;MACJC,OAAO;QACLC,IAAI;QACJC,gBAAgB;MAClB;MACAC,KAAK;QACHH,OAAO;UACLC,IAAI;UACJC,gBAAgB;QAClB;QACAE,MAAM;QACNC,QAAQ;MACV;MACAC,eAAe;IACjB;IACAC,QAAQ;MACNP,OAAO;QACLC,IAAI;QACJC,gBAAgB;MAClB;MACAM,SAAS;QACPP,IAAI;QACJC,gBACE;MACJ;MACAC,KAAK;QACHH,OAAO;UACLC,IAAI;UACJC,gBAAgB;QAClB;QACAE,MAAM;MACR;MACAE,eAAe;IACjB;IACAG,SAAS;MACPT,OAAO;QACLC,IAAI;QACJC,gBAAgB;MAClB;MACAM,SAAS;QACPP,IAAI;QACJC,gBAAgB;MAClB;MACAC,KAAK;QACHH,OAAO;UACLC,IAAI;UACJC,gBAAgB;QAClB;QACAE,MAAM;QACNC,QAAQ;MACV;MACAC,eAAe;IACjB;EACF;EACAK,WAAW;IACTZ,MAAM;MACJC,OAAO;QACLC,IAAI;QACJC,gBAAgB;MAClB;MACAC,KAAK;QACHH,OAAO;UACLC,IAAI;UACJC,gBAAgB;QAClB;QACAE,MAAM;QACNC,QAAQ;MACV;MACAC,eAAe;IACjB;IACAC,QAAQ;MACNP,OAAO;QACLC,IAAI;QACJC,gBAAgB;MAClB;MACAM,SAAS;QACPP,IAAI;QACJC,gBACE;MACJ;MACAC,KAAK;QACHH,OAAO;UACLC,IAAI;UACJC,gBAAgB;QAClB;QACAE,MAAM;MACR;MACAE,eAAe;IACjB;IACAG,SAAS;MACPT,OAAO;QACLC,IAAI;QACJC,gBAAgB;MAClB;MACAM,SAAS;QACPP,IAAI;QACJC,gBACE;MACJ;MACAI,eAAe;IACjB;EACF;AACF;AAEA,IAAMM,SAAS;EACbC,SAAS;EACTC,WAAW;EACXC,aAAa;AACf;;;;ACvJMC,IAAAA,SAAS,CAAC,EAAEC,UAAUC,OAAO,GAAGC,MAAoB,MAAA;AACxD,SAAOD,UAAUE,OAAOC,WAAWH,UAAUE,OAAOE,gBAClDC,wBAACC,MAAAA;IACCC,YAAW;IACXC,SAAS;IACTC,cAAa;IACbC,OAAO;IACPC,QAAQ;IACRC,gBAAe;IACd,GAAGX;cAEHD,UAAUE,OAAOC,cAChBE,wBAACQ,eAAAA;MAAMC,eAAW;MAACJ,OAAO;MAAUK,MAAK;aAEzCV,wBAACW,YAAAA;MAAWC,YAAW;MAAWC,WAAU;MACzCnB;;WAKPM,wBAACC,MAAAA;IACCa,aAAY;IACZC,aAAY;IACZC,aAAY;IACZb,SAAS;IACTC,cAAa;IACbC,OAAO;IACPC,QAAQ;IACRC,gBAAe;IACd,GAAGX;IAEJ,cAAAI,wBAACW,YAAAA;MAAWC,YAAW;MAAWC,WAAU;MACzCnB;;;AAIT;AAUMuB,IAAAA,kBAAkB,CAAC,EAAEtB,OAAO,GAAGC,MAAAA,UACnCI,wBAACkB,KAAAA;EACCb,OAAO;EACPC,QAAO;EACPJ,YAAYP,UAAUE,OAAOsB,cAAc,eAAe;EAC1DC,WAAS;EACTC,WAAW1B,UAAUE,OAAOE,YAAY,WAAW;EAClD,GAAGH;;", "names": ["LAYOUT_DATA", "contentTypeBuilder", "home", "title", "id", "defaultMessage", "cta", "type", "target", "trackingEvent", "create", "content", "success", "contentManager", "apiTokens", "STATES", "IS_DONE", "IS_ACTIVE", "IS_NOT_DONE", "Number", "children", "state", "props", "STATES", "IS_DONE", "IS_ACTIVE", "_jsx", "Flex", "background", "padding", "borderRadius", "width", "height", "justifyContent", "Check", "aria-hidden", "fill", "Typography", "fontWeight", "textColor", "borderColor", "borderWidth", "borderStyle", "VerticalDivider", "Box", "IS_NOT_DONE", "hasRadius", "minHeight"]}