import {
  require_castSlice,
  require_stringToArray
} from "./chunk-YXDCVYVT.js";
import {
  admin<PERSON>pi,
  require_baseIndexOf,
  require_trimmedEndIndex
} from "./chunk-WH6VCVXU.js";
import {
  require_baseToString,
  require_toString
} from "./chunk-CE4VABH2.js";
import {
  __commonJS,
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/_charsEndIndex.js
var require_charsEndIndex = __commonJS({
  "node_modules/lodash/_charsEndIndex.js"(exports, module) {
    var baseIndexOf = require_baseIndexOf();
    function charsEndIndex(strSymbols, chrSymbols) {
      var index = strSymbols.length;
      while (index-- && baseIndexOf(chrSymbols, strSymbols[index], 0) > -1) {
      }
      return index;
    }
    module.exports = charsEndIndex;
  }
});

// node_modules/lodash/trimEnd.js
var require_trimEnd = __commonJS({
  "node_modules/lodash/trimEnd.js"(exports, module) {
    var baseToString = require_baseToString();
    var castSlice = require_castSlice();
    var charsEndIndex = require_charsEndIndex();
    var stringToArray = require_stringToArray();
    var toString = require_toString();
    var trimmedEndIndex = require_trimmedEndIndex();
    function trimEnd2(string, chars, guard) {
      string = toString(string);
      if (string && (guard || chars === void 0)) {
        return string.slice(0, trimmedEndIndex(string) + 1);
      }
      if (!string || !(chars = baseToString(chars))) {
        return string;
      }
      var strSymbols = stringToArray(string), end = charsEndIndex(strSymbols, stringToArray(chars)) + 1;
      return castSlice(strSymbols, 0, end).join("");
    }
    module.exports = trimEnd2;
  }
});

// node_modules/@strapi/admin/dist/admin/admin/src/utils/urls.mjs
var import_trimEnd = __toESM(require_trimEnd(), 1);
var prefixFileUrlWithBackendUrl = (fileURL) => {
  return !!fileURL && fileURL.startsWith("/") ? `${window.strapi.backendURL}${fileURL}` : fileURL;
};
var createAbsoluteUrl = (url) => {
  if (!url) {
    return window.location.origin;
  }
  if (url.startsWith("/")) {
    return (0, import_trimEnd.default)(new URL(url, window.location.origin).toString(), "/");
  } else {
    return url;
  }
};

// node_modules/@strapi/admin/dist/admin/admin/src/services/admin.mjs
var admin = adminApi.enhanceEndpoints({
  addTagTypes: [
    "ProjectSettings",
    "LicenseLimits",
    "LicenseTrialTimeLeft"
  ]
}).injectEndpoints({
  endpoints: (builder) => ({
    init: builder.query({
      query: () => ({
        url: "/admin/init",
        method: "GET"
      }),
      transformResponse(res) {
        return res.data;
      }
    }),
    information: builder.query({
      query: () => ({
        url: "/admin/information",
        method: "GET"
      }),
      transformResponse(res) {
        return res.data;
      }
    }),
    telemetryProperties: builder.query({
      query: () => ({
        url: "/admin/telemetry-properties",
        method: "GET",
        config: {
          validateStatus: (status) => status < 500
        }
      }),
      transformResponse(res) {
        return res.data;
      }
    }),
    projectSettings: builder.query({
      query: () => ({
        url: "/admin/project-settings",
        method: "GET"
      }),
      providesTags: [
        "ProjectSettings"
      ],
      transformResponse(data) {
        return {
          authLogo: data.authLogo ? {
            name: data.authLogo.name,
            url: prefixFileUrlWithBackendUrl(data.authLogo.url)
          } : void 0,
          menuLogo: data.menuLogo ? {
            name: data.menuLogo.name,
            url: prefixFileUrlWithBackendUrl(data.menuLogo.url)
          } : void 0
        };
      }
    }),
    updateProjectSettings: builder.mutation({
      query: (data) => ({
        url: "/admin/project-settings",
        method: "POST",
        data,
        config: {
          headers: {
            "Content-Type": "multipart/form-data"
          }
        }
      }),
      invalidatesTags: [
        "ProjectSettings"
      ]
    }),
    getPlugins: builder.query({
      query: () => ({
        url: "/admin/plugins",
        method: "GET"
      })
    }),
    getLicenseLimits: builder.query({
      query: () => ({
        url: "/admin/license-limit-information",
        method: "GET"
      }),
      providesTags: [
        "LicenseLimits"
      ]
    }),
    getLicenseTrialTimeLeft: builder.query({
      query: () => ({
        url: "/admin/license-trial-time-left",
        method: "GET"
      }),
      providesTags: [
        "LicenseTrialTimeLeft"
      ]
    })
  }),
  overrideExisting: false
});
var { useInitQuery, useTelemetryPropertiesQuery, useInformationQuery, useProjectSettingsQuery, useUpdateProjectSettingsMutation, useGetPluginsQuery, useGetLicenseLimitsQuery, useGetLicenseTrialTimeLeftQuery } = admin;

export {
  createAbsoluteUrl,
  useInitQuery,
  useTelemetryPropertiesQuery,
  useInformationQuery,
  useProjectSettingsQuery,
  useUpdateProjectSettingsMutation,
  useGetPluginsQuery,
  useGetLicenseLimitsQuery,
  useGetLicenseTrialTimeLeftQuery
};
//# sourceMappingURL=chunk-EZSYDDUK.js.map
