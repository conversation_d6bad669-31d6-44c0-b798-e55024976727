{"version": 3, "sources": ["../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/Users/<USER>"], "sourcesContent": ["import { ListPageCE } from '../../../../../../../admin/src/pages/Settings/pages/Users/<USER>';\nimport { useLicenseLimitNotification } from '../../../../hooks/useLicenseLimitNotification';\n\nexport const UserListPageEE = () => {\n  useLicenseLimitNotification();\n\n  return <ListPageCE />;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAGaA,iBAAiB,MAAA;AAC5BC,8BAAAA;AAEA,aAAOC,wBAACC,YAAAA,CAAAA,CAAAA;AACV;", "names": ["UserListPageEE", "useLicenseLimitNotification", "_jsx", "ListPageCE"]}