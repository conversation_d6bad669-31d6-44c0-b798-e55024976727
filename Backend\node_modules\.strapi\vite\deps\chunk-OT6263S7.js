import {
  Flex,
  Grid,
  TooltipImpl,
  Typography,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  Link
} from "./chunk-S65ZWNEO.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-ACIMPXWY.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/ee/admin/src/pages/AuthPage/components/SSOProviders.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var SSOProviders = ({ providers, displayAllProviders }) => {
  const { formatMessage } = useIntl();
  if (displayAllProviders) {
    return (0, import_jsx_runtime.jsx)(Grid.Root, {
      gap: 4,
      children: providers.map((provider) => (0, import_jsx_runtime.jsx)(Grid.Item, {
        col: 4,
        direction: "column",
        alignItems: "stretch",
        children: (0, import_jsx_runtime.jsx)(SSOProviderButton, {
          provider
        })
      }, provider.uid))
    });
  }
  if (providers.length > 2 && !displayAllProviders) {
    return (0, import_jsx_runtime.jsxs)(Grid.Root, {
      gap: 4,
      children: [
        providers.slice(0, 2).map((provider) => (0, import_jsx_runtime.jsx)(Grid.Item, {
          col: 4,
          direction: "column",
          alignItems: "stretch",
          children: (0, import_jsx_runtime.jsx)(SSOProviderButton, {
            provider
          })
        }, provider.uid)),
        (0, import_jsx_runtime.jsx)(Grid.Item, {
          col: 4,
          direction: "column",
          alignItems: "stretch",
          children: (0, import_jsx_runtime.jsx)(TooltipImpl, {
            label: formatMessage({
              id: "global.see-more"
            }),
            children: (0, import_jsx_runtime.jsx)(SSOButton, {
              as: Link,
              to: "/auth/providers",
              children: (0, import_jsx_runtime.jsx)("span", {
                "aria-hidden": true,
                children: "•••"
              })
            })
          })
        })
      ]
    });
  }
  return (0, import_jsx_runtime.jsx)(SSOProvidersWrapper, {
    justifyContent: "center",
    children: providers.map((provider) => (0, import_jsx_runtime.jsx)(SSOProviderButton, {
      provider
    }, provider.uid))
  });
};
var SSOProvidersWrapper = dt(Flex)`
  & a:not(:first-child):not(:last-child) {
    margin: 0 ${({ theme }) => theme.spaces[2]};
  }
  & a:first-child {
    margin-right: ${({ theme }) => theme.spaces[2]};
  }
  & a:last-child {
    margin-left: ${({ theme }) => theme.spaces[2]};
  }
`;
var SSOProviderButton = ({ provider }) => {
  return (0, import_jsx_runtime.jsx)(TooltipImpl, {
    label: provider.displayName,
    children: (0, import_jsx_runtime.jsx)(SSOButton, {
      href: `${window.strapi.backendURL}/admin/connect/${provider.uid}`,
      children: provider.icon ? (0, import_jsx_runtime.jsx)("img", {
        src: provider.icon,
        "aria-hidden": true,
        alt: "",
        height: "32px"
      }) : (0, import_jsx_runtime.jsx)(Typography, {
        children: provider.displayName
      })
    })
  });
};
var SSOButton = dt.a`
  width: 13.6rem;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 4.8rem;
  border: 1px solid ${({ theme }) => theme.colors.neutral150};
  border-radius: ${({ theme }) => theme.borderRadius};
  text-decoration: inherit;
  &:link {
    text-decoration: none;
  }
  color: ${({ theme }) => theme.colors.neutral600};
`;

export {
  SSOProviders
};
//# sourceMappingURL=chunk-OT6263S7.js.map
