import {
  useConfig
} from "./chunk-6T62IZSE.js";
import {
  getTrad,
  pageSizes,
  pluginId,
  sortOptions
} from "./chunk-MEKEEUV3.js";
import "./chunk-EKXSMIUH.js";
import "./chunk-YV3ONWF5.js";
import "./chunk-UMW22TSS.js";
import "./chunk-4QC3H4VA.js";
import "./chunk-75D2ZJP5.js";
import "./chunk-VCTAT6B3.js";
import "./chunk-ROZIXYJG.js";
import "./chunk-C72RZIDJ.js";
import "./chunk-HZKRK7AR.js";
import "./chunk-LRN6A2UC.js";
import "./chunk-D2TGW5YS.js";
import "./chunk-M27D4U76.js";
import "./chunk-HX66WGOY.js";
import "./chunk-Y4UEUAII.js";
import "./chunk-BN2UQHMJ.js";
import {
  ConfirmDialog
} from "./chunk-NWWGC2Z2.js";
import "./chunk-DD3MYA7D.js";
import "./chunk-O5ZNSDDU.js";
import "./chunk-MBK4V2X7.js";
import "./chunk-DY2RJG3P.js";
import "./chunk-K65KIEAL.js";
import "./chunk-BUDFB33L.js";
import "./chunk-7MILHJ3J.js";
import {
  require_set
} from "./chunk-SGQJOZK5.js";
import "./chunk-AFM2NWPO.js";
import "./chunk-DUGZ4WIW.js";
import "./chunk-IFOFBKTA.js";
import "./chunk-376QHLWZ.js";
import "./chunk-EGNP2T5O.js";
import {
  useTracking
} from "./chunk-XDCEA27D.js";
import "./chunk-EZSYDDUK.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-QIJGNK42.js";
import "./chunk-CJHUGFLE.js";
import "./chunk-IQGHPIIW.js";
import "./chunk-DWSGKQEK.js";
import "./chunk-W6ZGCRX6.js";
import "./chunk-PVCRV2LE.js";
import "./chunk-HWAQQGJJ.js";
import "./chunk-L5JCPKMP.js";
import "./chunk-ZJEMJY2Q.js";
import "./chunk-D4WYVNVM.js";
import "./chunk-MMOBCIZG.js";
import "./chunk-6DRYEU2W.js";
import {
  Layouts
} from "./chunk-MTTHLNPH.js";
import "./chunk-PQINNV4N.js";
import {
  require_isEqual
} from "./chunk-VYSYYPOB.js";
import {
  Page
} from "./chunk-7LKLOY7A.js";
import "./chunk-ODQFI753.js";
import "./chunk-ZOP4VV6J.js";
import {
  require_get,
  require_lib
} from "./chunk-WH6VCVXU.js";
import "./chunk-IL5G2D22.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-76QM3EFM.js";
import "./chunk-CE4VABH2.js";
import {
  fn
} from "./chunk-QOUV5O5E.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  Box,
  Button,
  Dialog,
  Field,
  Grid,
  Link,
  SingleSelect,
  SingleSelectOption,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import "./chunk-5ZC4PE57.js";
import {
  NavLink
} from "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$4F,
  ForwardRef$5j
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/upload/dist/admin/pages/App/ConfigureTheView/ConfigureTheView.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_isEqual = __toESM(require_isEqual(), 1);
var import_qs2 = __toESM(require_lib(), 1);

// node_modules/@strapi/upload/dist/admin/pages/App/ConfigureTheView/components/Settings.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_qs = __toESM(require_lib(), 1);
var Settings = ({ sort = "", pageSize = 10, onChange: onChange2 }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsx)(Box, {
    background: "neutral0",
    hasRadius: true,
    shadow: "tableShadow",
    paddingTop: 6,
    paddingBottom: 6,
    paddingLeft: 7,
    paddingRight: 7,
    children: (0, import_jsx_runtime.jsxs)(Grid.Root, {
      gap: 4,
      children: [
        (0, import_jsx_runtime.jsx)(Grid.Item, {
          s: 12,
          col: 6,
          direction: "column",
          alignItems: "stretch",
          children: (0, import_jsx_runtime.jsxs)(Field.Root, {
            hint: formatMessage({
              id: getTrad("config.entries.note"),
              defaultMessage: "Number of assets displayed by default in the Media Library"
            }),
            name: "pageSize",
            children: [
              (0, import_jsx_runtime.jsx)(Field.Label, {
                children: formatMessage({
                  id: getTrad("config.entries.title"),
                  defaultMessage: "Entries per page"
                })
              }),
              (0, import_jsx_runtime.jsx)(SingleSelect, {
                onChange: (value) => onChange2({
                  target: {
                    name: "pageSize",
                    value
                  }
                }),
                value: pageSize,
                children: pageSizes.map((pageSize2) => (0, import_jsx_runtime.jsx)(SingleSelectOption, {
                  value: pageSize2,
                  children: pageSize2
                }, pageSize2))
              }),
              (0, import_jsx_runtime.jsx)(Field.Hint, {})
            ]
          })
        }),
        (0, import_jsx_runtime.jsx)(Grid.Item, {
          s: 12,
          col: 6,
          direction: "column",
          alignItems: "stretch",
          children: (0, import_jsx_runtime.jsxs)(Field.Root, {
            hint: formatMessage({
              id: getTrad("config.note"),
              defaultMessage: "Note: You can override this value in the media library."
            }),
            name: "sort",
            children: [
              (0, import_jsx_runtime.jsx)(Field.Label, {
                children: formatMessage({
                  id: getTrad("config.sort.title"),
                  defaultMessage: "Default sort order"
                })
              }),
              (0, import_jsx_runtime.jsx)(SingleSelect, {
                onChange: (value) => onChange2({
                  target: {
                    name: "sort",
                    value
                  }
                }),
                value: sort,
                "test-sort": sort,
                "data-testid": "sort-select",
                children: sortOptions.map((filter) => (0, import_jsx_runtime.jsx)(SingleSelectOption, {
                  "data-testid": `sort-option-${filter.value}`,
                  value: filter.value,
                  children: formatMessage({
                    id: getTrad(filter.key),
                    defaultMessage: `${filter.value}`
                  })
                }, filter.key))
              }),
              (0, import_jsx_runtime.jsx)(Field.Hint, {})
            ]
          })
        })
      ]
    })
  });
};

// node_modules/@strapi/upload/dist/admin/pages/App/ConfigureTheView/state/actionTypes.mjs
var ON_CHANGE = `${pluginId}/ON_CHANGE`;
var SET_LOADED = `${pluginId}/SET_LOADED`;

// node_modules/@strapi/upload/dist/admin/pages/App/ConfigureTheView/state/actions.mjs
var onChange = ({ name, value }) => ({
  type: ON_CHANGE,
  keys: name,
  value
});
var setLoaded = () => ({
  type: SET_LOADED
});

// node_modules/@strapi/upload/dist/admin/pages/App/ConfigureTheView/state/init.mjs
var initialState = {
  initialData: {},
  modifiedData: {}
};
var init = (configData) => {
  return {
    ...initialState,
    initialData: configData,
    modifiedData: configData
  };
};

// node_modules/@strapi/upload/dist/admin/pages/App/ConfigureTheView/state/reducer.mjs
var import_get = __toESM(require_get(), 1);
var import_set = __toESM(require_set(), 1);
var reducer = (state = initialState, action = {
  type: ""
}) => (
  // eslint-disable-next-line consistent-return
  fn(state, (draftState) => {
    switch (action.type) {
      case ON_CHANGE: {
        if ("keys" in action && "value" in action && action.keys) {
          (0, import_set.default)(draftState, [
            "modifiedData",
            ...action.keys.split(".")
          ], action.value);
        }
        break;
      }
      case SET_LOADED: {
        const reInitialise = init((0, import_get.default)(draftState, [
          "modifiedData"
        ], {}));
        draftState.initialData = reInitialise.initialData;
        draftState.modifiedData = reInitialise.modifiedData;
        break;
      }
      default:
        return draftState;
    }
  })
);

// node_modules/@strapi/upload/dist/admin/pages/App/ConfigureTheView/ConfigureTheView.mjs
var ConfigureTheView = ({ config }) => {
  const { trackUsage } = useTracking();
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { mutateConfig } = useConfig();
  const { isLoading: isSubmittingForm } = mutateConfig;
  const [showWarningSubmit, setWarningSubmit] = React.useState(false);
  const toggleWarningSubmit = () => setWarningSubmit((prevState) => !prevState);
  const [reducerState, dispatch] = React.useReducer(reducer, initialState, () => init(config));
  const typedDispatch = dispatch;
  const { initialData, modifiedData } = reducerState;
  const handleSubmit = (e) => {
    e.preventDefault();
    toggleWarningSubmit();
  };
  const handleConfirm = async () => {
    trackUsage("willEditMediaLibraryConfig");
    await mutateConfig.mutateAsync(modifiedData);
    setWarningSubmit(false);
    typedDispatch(setLoaded());
    toggleNotification({
      type: "success",
      message: formatMessage({
        id: "notification.form.success.fields",
        defaultMessage: "Changes saved"
      })
    });
  };
  const handleChange = ({ target: { name, value } }) => {
    typedDispatch(onChange({
      name,
      value
    }));
  };
  return (0, import_jsx_runtime2.jsx)(Layouts.Root, {
    children: (0, import_jsx_runtime2.jsx)(Page.Main, {
      "aria-busy": isSubmittingForm,
      children: (0, import_jsx_runtime2.jsxs)("form", {
        onSubmit: handleSubmit,
        children: [
          (0, import_jsx_runtime2.jsx)(Layouts.Header, {
            navigationAction: (0, import_jsx_runtime2.jsx)(Link, {
              tag: NavLink,
              startIcon: (0, import_jsx_runtime2.jsx)(ForwardRef$5j, {}),
              to: `/plugins/${pluginId}`,
              id: "go-back",
              children: formatMessage({
                id: getTrad("config.back"),
                defaultMessage: "Back"
              })
            }),
            primaryAction: (0, import_jsx_runtime2.jsx)(Button, {
              size: "S",
              startIcon: (0, import_jsx_runtime2.jsx)(ForwardRef$4F, {}),
              disabled: (0, import_isEqual.default)(modifiedData, initialData),
              type: "submit",
              children: formatMessage({
                id: "global.save",
                defaultMessage: "Save"
              })
            }),
            subtitle: formatMessage({
              id: getTrad("config.subtitle"),
              defaultMessage: "Define the view settings of the media library."
            }),
            title: formatMessage({
              id: getTrad("config.title"),
              defaultMessage: "Configure the view - Media Library"
            })
          }),
          (0, import_jsx_runtime2.jsx)(Layouts.Content, {
            children: (0, import_jsx_runtime2.jsx)(Settings, {
              "data-testid": "settings",
              pageSize: modifiedData.pageSize || "",
              sort: modifiedData.sort || "",
              onChange: handleChange
            })
          }),
          "x",
          (0, import_jsx_runtime2.jsx)(Dialog.Root, {
            open: showWarningSubmit,
            onOpenChange: toggleWarningSubmit,
            children: (0, import_jsx_runtime2.jsx)(ConfirmDialog, {
              onConfirm: handleConfirm,
              variant: "default",
              children: formatMessage({
                id: getTrad("config.popUpWarning.warning.updateAllSettings"),
                defaultMessage: "This will modify all your settings"
              })
            })
          })
        ]
      })
    })
  });
};
export {
  ConfigureTheView
};
//# sourceMappingURL=ConfigureTheView-TSAIQBSB.js.map
