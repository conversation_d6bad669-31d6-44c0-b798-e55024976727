{"version": 3, "sources": ["../../../@strapi/admin/admin/src/components/SubNav.tsx", "../../../@strapi/admin/admin/src/constants.ts"], "sourcesContent": ["import { useId, useState } from 'react';\n\nimport { Box, SubNav as DSSubNav, Flex, Typography, IconButton } from '@strapi/design-system';\nimport { ChevronDown, Plus } from '@strapi/icons';\nimport { NavLink } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nconst Main = styled(DSSubNav)`\n  background-color: ${({ theme }) => theme.colors.neutral0};\n  border-right: 1px solid ${({ theme }) => theme.colors.neutral150};\n\n  scrollbar-width: none;\n  -ms-overflow-style: none;\n\n  &::-webkit-scrollbar {\n    display: none;\n  }\n`;\n\nconst StyledLink = styled(NavLink)`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  text-decoration: none;\n  height: 32px;\n\n  color: ${({ theme }) => theme.colors.neutral800};\n\n  &.active > div {\n    ${({ theme }) => {\n      return `\n        background-color: ${theme.colors.primary100};\n        color: ${theme.colors.primary700};\n        font-weight: 500;\n      `;\n    }}\n  }\n\n  &:hover.active > div {\n    ${({ theme }) => {\n      return `\n        background-color: ${theme.colors.primary100};\n      `;\n    }}\n  }\n\n  &:hover > div {\n    ${({ theme }) => {\n      return `\n        background-color: ${theme.colors.neutral100};\n      `;\n    }}\n  }\n\n  &:focus-visible {\n    outline-offset: -2px;\n  }\n`;\n\nconst Link = (\n  props: Omit<React.ComponentProps<typeof StyledLink>, 'label'> & {\n    label: React.ReactNode;\n    endAction?: React.ReactNode;\n  }\n) => {\n  const { label, endAction, ...rest } = props;\n  return (\n    <StyledLink {...rest}>\n      <Box width={'100%'} paddingLeft={3} paddingRight={3} borderRadius={1}>\n        <Flex justifyContent=\"space-between\" width=\"100%\" gap={1}>\n          <Typography\n            tag=\"div\"\n            lineHeight=\"32px\"\n            width=\"100%\"\n            overflow=\"hidden\"\n            style={{ textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}\n          >\n            {label}\n          </Typography>\n          <Flex gap={2}>{endAction}</Flex>\n        </Flex>\n      </Box>\n    </StyledLink>\n  );\n};\n\nconst StyledHeader = styled(Box)`\n  height: 56px;\n  display: flex;\n  align-items: center;\n  padding-left: ${({ theme }) => theme.spaces[5]};\n`;\n\nconst Header = ({ label }: { label: string }) => {\n  return (\n    <StyledHeader>\n      <Typography variant=\"beta\" tag=\"h2\">\n        {label}\n      </Typography>\n    </StyledHeader>\n  );\n};\n\nconst Sections = ({ children, ...props }: { children: React.ReactNode[]; [key: string]: any }) => {\n  return (\n    <Box paddingBottom={4}>\n      <Flex tag=\"ol\" gap=\"5\" direction=\"column\" alignItems=\"stretch\" {...props}>\n        {children.map((child, index) => {\n          return <li key={index}>{child}</li>;\n        })}\n      </Flex>\n    </Box>\n  );\n};\n\nconst Section = ({\n  label,\n  children,\n  link,\n}: {\n  label: string;\n  children: React.ReactNode[];\n  link?: { label: string; onClik: () => void };\n}) => {\n  const listId = useId();\n\n  return (\n    <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n      <Box paddingLeft={5} paddingRight={5}>\n        <Flex position=\"relative\" justifyContent=\"space-between\">\n          <Flex>\n            <Box>\n              <Typography variant=\"sigma\" textColor=\"neutral600\">\n                {label}\n              </Typography>\n            </Box>\n          </Flex>\n          {link && (\n            <IconButton\n              label={link.label}\n              variant=\"ghost\"\n              withTooltip\n              onClick={link.onClik}\n              size=\"XS\"\n            >\n              <Plus />\n            </IconButton>\n          )}\n        </Flex>\n      </Box>\n      <Flex\n        tag=\"ol\"\n        id={listId}\n        direction=\"column\"\n        gap=\"2px\"\n        alignItems={'stretch'}\n        marginLeft={2}\n        marginRight={2}\n      >\n        {children.map((child, index) => {\n          return <li key={index}>{child}</li>;\n        })}\n      </Flex>\n    </Flex>\n  );\n};\n\nconst SubSectionHeader = styled.button`\n  cursor: pointer;\n  width: 100%;\n  border: none;\n  padding: 0;\n  background: transparent;\n  display: flex;\n  align-items: center;\n\n  height: 32px;\n\n  border-radius: ${({ theme }) => theme.borderRadius};\n\n  padding-left: ${({ theme }) => theme.spaces[3]};\n  padding-right: ${({ theme }) => theme.spaces[3]};\n  padding-top: ${({ theme }) => theme.spaces[2]};\n  padding-bottom: ${({ theme }) => theme.spaces[2]};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.neutral100};\n  }\n`;\n\nconst SubSectionLinkWrapper = styled.li`\n  ${StyledLink} > div {\n    padding-left: 36px;\n  }\n`;\n\nconst SubSection = ({ label, children }: { label: string; children: React.ReactNode[] }) => {\n  const [isOpen, setOpenLinks] = useState(true);\n  const listId = useId();\n\n  const handleClick = () => {\n    setOpenLinks((prev) => !prev);\n  };\n\n  return (\n    <Box>\n      <Flex justifyContent=\"space-between\">\n        <SubSectionHeader onClick={handleClick} aria-expanded={isOpen} aria-controls={listId}>\n          <ChevronDown\n            aria-hidden\n            fill=\"neutral500\"\n            style={{\n              transform: `rotate(${isOpen ? '0deg' : '-90deg'})`,\n              transition: 'transform 0.5s',\n            }}\n          />\n          <Box paddingLeft={2}>\n            <Typography tag=\"span\" fontWeight=\"semiBold\" textColor=\"neutral800\">\n              {label}\n            </Typography>\n          </Box>\n        </SubSectionHeader>\n      </Flex>\n      {\n        <Flex\n          tag=\"ul\"\n          id={listId}\n          direction=\"column\"\n          gap=\"2px\"\n          alignItems={'stretch'}\n          style={{\n            maxHeight: isOpen ? '1000px' : 0,\n            overflow: 'hidden',\n            transition: isOpen\n              ? 'max-height 1s ease-in-out'\n              : 'max-height 0.5s cubic-bezier(0, 1, 0, 1)',\n          }}\n        >\n          {children.map((child, index) => {\n            return <SubSectionLinkWrapper key={index}>{child}</SubSectionLinkWrapper>;\n          })}\n        </Flex>\n      }\n    </Box>\n  );\n};\n\nexport const SubNav = {\n  Main,\n  Header,\n  Link,\n  Sections,\n  Section,\n  SubSection,\n};\n", "import { PermissionMap } from './types/permissions';\n\nimport type { StrapiAppSettingLink } from './core/apis/router';\n\nexport const ADMIN_PERMISSIONS_CE = {\n  contentManager: {\n    main: [],\n    collectionTypesConfigurations: [\n      {\n        action: 'plugin::content-manager.collection-types.configure-view',\n        subject: null,\n      },\n    ],\n    componentsConfigurations: [\n      {\n        action: 'plugin::content-manager.components.configure-layout',\n        subject: null,\n      },\n    ],\n    singleTypesConfigurations: [\n      {\n        action: 'plugin::content-manager.single-types.configure-view',\n        subject: null,\n      },\n    ],\n  },\n  marketplace: {\n    main: [{ action: 'admin::marketplace.read', subject: null }],\n    read: [{ action: 'admin::marketplace.read', subject: null }],\n  },\n  settings: {\n    roles: {\n      main: [\n        { action: 'admin::roles.create', subject: null },\n        { action: 'admin::roles.update', subject: null },\n        { action: 'admin::roles.read', subject: null },\n        { action: 'admin::roles.delete', subject: null },\n      ],\n      create: [{ action: 'admin::roles.create', subject: null }],\n      delete: [{ action: 'admin::roles.delete', subject: null }],\n      read: [{ action: 'admin::roles.read', subject: null }],\n      update: [{ action: 'admin::roles.update', subject: null }],\n    },\n    users: {\n      main: [\n        { action: 'admin::users.create', subject: null },\n        { action: 'admin::users.read', subject: null },\n        { action: 'admin::users.update', subject: null },\n        { action: 'admin::users.delete', subject: null },\n      ],\n      create: [{ action: 'admin::users.create', subject: null }],\n      delete: [{ action: 'admin::users.delete', subject: null }],\n      read: [{ action: 'admin::users.read', subject: null }],\n      update: [{ action: 'admin::users.update', subject: null }],\n    },\n    webhooks: {\n      main: [\n        { action: 'admin::webhooks.create', subject: null },\n        { action: 'admin::webhooks.read', subject: null },\n        { action: 'admin::webhooks.update', subject: null },\n        { action: 'admin::webhooks.delete', subject: null },\n      ],\n      create: [{ action: 'admin::webhooks.create', subject: null }],\n      delete: [{ action: 'admin::webhooks.delete', subject: null }],\n      read: [\n        { action: 'admin::webhooks.read', subject: null },\n        // NOTE: We need to check with the API\n        { action: 'admin::webhooks.update', subject: null },\n        { action: 'admin::webhooks.delete', subject: null },\n      ],\n      update: [{ action: 'admin::webhooks.update', subject: null }],\n    },\n    'api-tokens': {\n      main: [{ action: 'admin::api-tokens.access', subject: null }],\n      create: [{ action: 'admin::api-tokens.create', subject: null }],\n      delete: [{ action: 'admin::api-tokens.delete', subject: null }],\n      read: [{ action: 'admin::api-tokens.read', subject: null }],\n      update: [{ action: 'admin::api-tokens.update', subject: null }],\n      regenerate: [{ action: 'admin::api-tokens.regenerate', subject: null }],\n    },\n    'transfer-tokens': {\n      main: [{ action: 'admin::transfer.tokens.access', subject: null }],\n      create: [{ action: 'admin::transfer.tokens.create', subject: null }],\n      delete: [{ action: 'admin::transfer.tokens.delete', subject: null }],\n      read: [{ action: 'admin::transfer.tokens.read', subject: null }],\n      update: [{ action: 'admin::transfer.tokens.update', subject: null }],\n      regenerate: [{ action: 'admin::transfer.tokens.regenerate', subject: null }],\n    },\n    'project-settings': {\n      read: [{ action: 'admin::project-settings.read', subject: null }],\n      update: [{ action: 'admin::project-settings.update', subject: null }],\n    },\n    plugins: {\n      main: [{ action: 'admin::marketplace.read', subject: null }],\n      read: [{ action: 'admin::marketplace.read', subject: null }],\n    },\n  },\n} satisfies Partial<PermissionMap>;\n\nexport const HOOKS = {\n  /**\n   * Hook that allows to mutate the displayed headers of the list view table\n   * @constant\n   * @type {string}\n   */\n  INJECT_COLUMN_IN_TABLE: 'Admin/CM/pages/ListView/inject-column-in-table',\n\n  /**\n   * Hook that allows to mutate the CM's collection types links pre-set filters\n   * @constant\n   * @type {string}\n   */\n  MUTATE_COLLECTION_TYPES_LINKS: 'Admin/CM/pages/App/mutate-collection-types-links',\n\n  /**\n   * Hook that allows to mutate the CM's edit view layout\n   * @constant\n   * @type {string}\n   */\n  MUTATE_EDIT_VIEW_LAYOUT: 'Admin/CM/pages/EditView/mutate-edit-view-layout',\n\n  /**\n   * Hook that allows to mutate the CM's single types links pre-set filters\n   * @constant\n   * @type {string}\n   */\n  MUTATE_SINGLE_TYPES_LINKS: 'Admin/CM/pages/App/mutate-single-types-links',\n};\n\nexport interface SettingsMenuLink\n  extends Omit<StrapiAppSettingLink, 'Component' | 'permissions' | 'licenseOnly'> {\n  licenseOnly?: boolean;\n}\n\nexport type SettingsMenu = {\n  admin: SettingsMenuLink[];\n  global: SettingsMenuLink[];\n};\n\nexport const SETTINGS_LINKS_CE = (): SettingsMenu => ({\n  global: [\n    {\n      intlLabel: { id: 'Settings.application.title', defaultMessage: 'Overview' },\n      to: '/settings/application-infos',\n      id: '000-application-infos',\n    },\n    {\n      intlLabel: { id: 'Settings.webhooks.title', defaultMessage: 'Webhooks' },\n      to: '/settings/webhooks',\n      id: 'webhooks',\n    },\n    {\n      intlLabel: { id: 'Settings.apiTokens.title', defaultMessage: 'API Tokens' },\n      to: '/settings/api-tokens?sort=name:ASC',\n      id: 'api-tokens',\n    },\n    {\n      intlLabel: { id: 'Settings.transferTokens.title', defaultMessage: 'Transfer Tokens' },\n      to: '/settings/transfer-tokens?sort=name:ASC',\n      id: 'transfer-tokens',\n    },\n    {\n      intlLabel: {\n        id: 'global.plugins',\n        defaultMessage: 'Plugins',\n      },\n      to: '/settings/list-plugins',\n      id: 'plugins',\n    },\n    // If the Enterprise/Cloud feature is not enabled and if the config doesn't disable it, we promote the Enterprise/Cloud feature by displaying them in the settings menu.\n    // Disable this by adding \"promoteEE: false\" to your `./config/admin.js` file\n    ...(!window.strapi.features.isEnabled(window.strapi.features.SSO) &&\n    window.strapi?.flags?.promoteEE\n      ? [\n          {\n            intlLabel: { id: 'Settings.sso.title', defaultMessage: 'Single Sign-On' },\n            to: '/settings/purchase-single-sign-on',\n            id: 'sso-purchase-page',\n            licenseOnly: true,\n          },\n        ]\n      : []),\n    ...(!window.strapi.features.isEnabled('cms-content-history') && window.strapi?.flags?.promoteEE\n      ? [\n          {\n            intlLabel: { id: 'Settings.content-history.title', defaultMessage: 'Content History' },\n            to: '/settings/purchase-content-history',\n            id: 'content-history-purchase-page',\n            licenseOnly: true,\n          },\n        ]\n      : []),\n  ],\n\n  admin: [\n    {\n      intlLabel: { id: 'global.roles', defaultMessage: 'Roles' },\n      to: '/settings/roles',\n      id: 'roles',\n    },\n    {\n      intlLabel: { id: 'global.users', defaultMessage: 'Users' },\n      // Init the search params directly\n      to: '/settings/users?pageSize=10&page=1&sort=firstname',\n      id: 'users',\n    },\n    ...(!window.strapi.features.isEnabled(window.strapi.features.AUDIT_LOGS) &&\n    window.strapi?.flags?.promoteEE\n      ? [\n          {\n            intlLabel: { id: 'global.auditLogs', defaultMessage: 'Audit Logs' },\n            to: '/settings/purchase-audit-logs',\n            id: 'auditLogs-purchase-page',\n            licenseOnly: true,\n          },\n        ]\n      : []),\n  ],\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAMA,OAAOC,GAAOC,MAAAA;sBACE,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAOC,QAAQ;4BAC9B,CAAC,EAAEF,MAAK,MAAOA,MAAMC,OAAOE,UAAU;;;;;;;;;AAUlE,IAAMC,aAAaN,GAAOO,OAAAA;;;;;;;WAOf,CAAC,EAAEL,MAAK,MAAOA,MAAMC,OAAOK,UAAU;;;MAG3C,CAAC,EAAEN,MAAK,MAAE;AACV,SAAO;4BACeA,MAAMC,OAAOM,UAAU;iBAClCP,MAAMC,OAAOO,UAAU;;;AAGpC,CAAE;;;;MAIA,CAAC,EAAER,MAAK,MAAE;AACV,SAAO;4BACeA,MAAMC,OAAOM,UAAU;;AAE/C,CAAE;;;;MAIA,CAAC,EAAEP,MAAK,MAAE;AACV,SAAO;4BACeA,MAAMC,OAAOQ,UAAU;;AAE/C,CAAE;;;;;;;AAQN,IAAMC,OAAO,CACXC,UAAAA;AAKA,QAAM,EAAEC,OAAOC,WAAW,GAAGC,KAAAA,IAASH;AACtC,aACEI,wBAACX,YAAAA;IAAY,GAAGU;IACd,cAAAC,wBAACC,KAAAA;MAAIC,OAAO;MAAQC,aAAa;MAAGC,cAAc;MAAGC,cAAc;MACjE,cAAAC,yBAACC,MAAAA;QAAKC,gBAAe;QAAgBN,OAAM;QAAOO,KAAK;;cACrDT,wBAACU,YAAAA;YACCC,KAAI;YACJC,YAAW;YACXV,OAAM;YACNW,UAAS;YACTC,OAAO;cAAEC,cAAc;cAAYC,YAAY;YAAS;YAEvDnB,UAAAA;;cAEHG,wBAACO,MAAAA;YAAKE,KAAK;YAAIX,UAAAA;;;;;;AAKzB;AAEA,IAAMmB,eAAelC,GAAOkB,GAAAA;;;;kBAIV,CAAC,EAAEhB,MAAK,MAAOA,MAAMiC,OAAO,CAAA,CAAE;;AAGhD,IAAMC,SAAS,CAAC,EAAEtB,MAAK,MAAqB;AAC1C,aACEG,wBAACiB,cAAAA;IACC,cAAAjB,wBAACU,YAAAA;MAAWU,SAAQ;MAAOT,KAAI;MAC5Bd,UAAAA;;;AAIT;AAEA,IAAMwB,WAAW,CAAC,EAAEC,UAAU,GAAG1B,MAA4D,MAAA;AAC3F,aACEI,wBAACC,KAAAA;IAAIsB,eAAe;IAClB,cAAAvB,wBAACO,MAAAA;MAAKI,KAAI;MAAKF,KAAI;MAAIe,WAAU;MAASC,YAAW;MAAW,GAAG7B;gBAChE0B,SAASI,IAAI,CAACC,OAAOC,UAAAA;AACpB,mBAAO5B,wBAAC6B,MAAAA;UAAgBF,UAAAA;QAARC,GAAAA,KAAAA;MAClB,CAAA;;;AAIR;AAEA,IAAME,UAAU,CAAC,EACfjC,OACAyB,UACAS,KAAI,MAKL;AACC,QAAMC,aAASC,oBAAAA;AAEf,aACE3B,yBAACC,MAAAA;IAAKiB,WAAU;IAASC,YAAW;IAAUhB,KAAK;;UACjDT,wBAACC,KAAAA;QAAIE,aAAa;QAAGC,cAAc;QACjC,cAAAE,yBAACC,MAAAA;UAAK2B,UAAS;UAAW1B,gBAAe;;gBACvCR,wBAACO,MAAAA;cACC,cAAAP,wBAACC,KAAAA;gBACC,cAAAD,wBAACU,YAAAA;kBAAWU,SAAQ;kBAAQe,WAAU;kBACnCtC,UAAAA;;;;YAINkC,YACC/B,wBAACoC,YAAAA;cACCvC,OAAOkC,KAAKlC;cACZuB,SAAQ;cACRiB,aAAW;cACXC,SAASP,KAAKQ;cACdC,MAAK;cAEL,cAAAxC,wBAACyC,eAAAA,CAAAA,CAAAA;;;;;UAKTzC,wBAACO,MAAAA;QACCI,KAAI;QACJ+B,IAAIV;QACJR,WAAU;QACVf,KAAI;QACJgB,YAAY;QACZkB,YAAY;QACZC,aAAa;kBAEZtB,SAASI,IAAI,CAACC,OAAOC,UAAAA;AACpB,qBAAO5B,wBAAC6B,MAAAA;YAAgBF,UAAAA;UAARC,GAAAA,KAAAA;QAClB,CAAA;;;;AAIR;AAEA,IAAMiB,mBAAmB9D,GAAO+D;;;;;;;;;;;mBAWb,CAAC,EAAE7D,MAAK,MAAOA,MAAMoB,YAAY;;kBAElC,CAAC,EAAEpB,MAAK,MAAOA,MAAMiC,OAAO,CAAA,CAAE;mBAC7B,CAAC,EAAEjC,MAAK,MAAOA,MAAMiC,OAAO,CAAA,CAAE;iBAChC,CAAC,EAAEjC,MAAK,MAAOA,MAAMiC,OAAO,CAAA,CAAE;oBAC3B,CAAC,EAAEjC,MAAK,MAAOA,MAAMiC,OAAO,CAAA,CAAE;;;wBAG1B,CAAC,EAAEjC,MAAK,MAAOA,MAAMC,OAAOQ,UAAU;;;AAI9D,IAAMqD,wBAAwBhE,GAAO8C;IACjCxC,UAAW;;;;AAKf,IAAM2D,aAAa,CAAC,EAAEnD,OAAOyB,SAAQ,MAAkD;AACrF,QAAM,CAAC2B,QAAQC,YAAa,QAAGC,uBAAS,IAAA;AACxC,QAAMnB,aAASC,oBAAAA;AAEf,QAAMmB,cAAc,MAAA;AAClBF,iBAAa,CAACG,SAAS,CAACA,IAAAA;EAC1B;AAEA,aACE/C,yBAACL,KAAAA;;UACCD,wBAACO,MAAAA;QAAKC,gBAAe;QACnB,cAAAF,yBAACuC,kBAAAA;UAAiBP,SAASc;UAAaE,iBAAeL;UAAQM,iBAAevB;;gBAC5EhC,wBAACwD,eAAAA;cACCC,eAAW;cACXC,MAAK;cACL5C,OAAO;gBACL6C,WAAW,UAAUV,SAAS,SAAS,QAAA;gBACvCW,YAAY;cACd;;gBAEF5D,wBAACC,KAAAA;cAAIE,aAAa;cAChB,cAAAH,wBAACU,YAAAA;gBAAWC,KAAI;gBAAOkD,YAAW;gBAAW1B,WAAU;gBACpDtC,UAAAA;;;;;;UAMPG,wBAACO,MAAAA;QACCI,KAAI;QACJ+B,IAAIV;QACJR,WAAU;QACVf,KAAI;QACJgB,YAAY;QACZX,OAAO;UACLgD,WAAWb,SAAS,WAAW;UAC/BpC,UAAU;UACV+C,YAAYX,SACR,8BACA;QACN;kBAEC3B,SAASI,IAAI,CAACC,OAAOC,UAAAA;AACpB,qBAAO5B,wBAAC+C,uBAAAA;YAAmCpB,UAAAA;UAARC,GAAAA,KAAAA;QACrC,CAAA;;;;AAKV;IAEamC,UAAS;EACpBjF;EACAqC;EACAxB;EACA0B;EACAS;EACAkB;AACF;;;IC1PagB,uBAAuB;EAClCC,gBAAgB;IACdC,MAAM,CAAA;IACNC,+BAA+B;MAC7B;QACEC,QAAQ;QACRC,SAAS;MACX;IACD;IACDC,0BAA0B;MACxB;QACEF,QAAQ;QACRC,SAAS;MACX;IACD;IACDE,2BAA2B;MACzB;QACEH,QAAQ;QACRC,SAAS;MACX;IACD;EACH;EACAG,aAAa;IACXN,MAAM;MAAC;QAAEE,QAAQ;QAA2BC,SAAS;MAAK;IAAE;IAC5DI,MAAM;MAAC;QAAEL,QAAQ;QAA2BC,SAAS;MAAK;IAAE;EAC9D;EACAK,UAAU;IACRC,OAAO;MACLT,MAAM;QACJ;UAAEE,QAAQ;UAAuBC,SAAS;QAAK;QAC/C;UAAED,QAAQ;UAAuBC,SAAS;QAAK;QAC/C;UAAED,QAAQ;UAAqBC,SAAS;QAAK;QAC7C;UAAED,QAAQ;UAAuBC,SAAS;QAAK;MAChD;MACDO,QAAQ;QAAC;UAAER,QAAQ;UAAuBC,SAAS;QAAK;MAAE;MAC1DQ,QAAQ;QAAC;UAAET,QAAQ;UAAuBC,SAAS;QAAK;MAAE;MAC1DI,MAAM;QAAC;UAAEL,QAAQ;UAAqBC,SAAS;QAAK;MAAE;MACtDS,QAAQ;QAAC;UAAEV,QAAQ;UAAuBC,SAAS;QAAK;MAAE;IAC5D;IACAU,OAAO;MACLb,MAAM;QACJ;UAAEE,QAAQ;UAAuBC,SAAS;QAAK;QAC/C;UAAED,QAAQ;UAAqBC,SAAS;QAAK;QAC7C;UAAED,QAAQ;UAAuBC,SAAS;QAAK;QAC/C;UAAED,QAAQ;UAAuBC,SAAS;QAAK;MAChD;MACDO,QAAQ;QAAC;UAAER,QAAQ;UAAuBC,SAAS;QAAK;MAAE;MAC1DQ,QAAQ;QAAC;UAAET,QAAQ;UAAuBC,SAAS;QAAK;MAAE;MAC1DI,MAAM;QAAC;UAAEL,QAAQ;UAAqBC,SAAS;QAAK;MAAE;MACtDS,QAAQ;QAAC;UAAEV,QAAQ;UAAuBC,SAAS;QAAK;MAAE;IAC5D;IACAW,UAAU;MACRd,MAAM;QACJ;UAAEE,QAAQ;UAA0BC,SAAS;QAAK;QAClD;UAAED,QAAQ;UAAwBC,SAAS;QAAK;QAChD;UAAED,QAAQ;UAA0BC,SAAS;QAAK;QAClD;UAAED,QAAQ;UAA0BC,SAAS;QAAK;MACnD;MACDO,QAAQ;QAAC;UAAER,QAAQ;UAA0BC,SAAS;QAAK;MAAE;MAC7DQ,QAAQ;QAAC;UAAET,QAAQ;UAA0BC,SAAS;QAAK;MAAE;MAC7DI,MAAM;QACJ;UAAEL,QAAQ;UAAwBC,SAAS;QAAK;;QAEhD;UAAED,QAAQ;UAA0BC,SAAS;QAAK;QAClD;UAAED,QAAQ;UAA0BC,SAAS;QAAK;MACnD;MACDS,QAAQ;QAAC;UAAEV,QAAQ;UAA0BC,SAAS;QAAK;MAAE;IAC/D;IACA,cAAc;MACZH,MAAM;QAAC;UAAEE,QAAQ;UAA4BC,SAAS;QAAK;MAAE;MAC7DO,QAAQ;QAAC;UAAER,QAAQ;UAA4BC,SAAS;QAAK;MAAE;MAC/DQ,QAAQ;QAAC;UAAET,QAAQ;UAA4BC,SAAS;QAAK;MAAE;MAC/DI,MAAM;QAAC;UAAEL,QAAQ;UAA0BC,SAAS;QAAK;MAAE;MAC3DS,QAAQ;QAAC;UAAEV,QAAQ;UAA4BC,SAAS;QAAK;MAAE;MAC/DY,YAAY;QAAC;UAAEb,QAAQ;UAAgCC,SAAS;QAAK;MAAE;IACzE;IACA,mBAAmB;MACjBH,MAAM;QAAC;UAAEE,QAAQ;UAAiCC,SAAS;QAAK;MAAE;MAClEO,QAAQ;QAAC;UAAER,QAAQ;UAAiCC,SAAS;QAAK;MAAE;MACpEQ,QAAQ;QAAC;UAAET,QAAQ;UAAiCC,SAAS;QAAK;MAAE;MACpEI,MAAM;QAAC;UAAEL,QAAQ;UAA+BC,SAAS;QAAK;MAAE;MAChES,QAAQ;QAAC;UAAEV,QAAQ;UAAiCC,SAAS;QAAK;MAAE;MACpEY,YAAY;QAAC;UAAEb,QAAQ;UAAqCC,SAAS;QAAK;MAAE;IAC9E;IACA,oBAAoB;MAClBI,MAAM;QAAC;UAAEL,QAAQ;UAAgCC,SAAS;QAAK;MAAE;MACjES,QAAQ;QAAC;UAAEV,QAAQ;UAAkCC,SAAS;QAAK;MAAE;IACvE;IACAa,SAAS;MACPhB,MAAM;QAAC;UAAEE,QAAQ;UAA2BC,SAAS;QAAK;MAAE;MAC5DI,MAAM;QAAC;UAAEL,QAAQ;UAA2BC,SAAS;QAAK;MAAE;IAC9D;EACF;AACF;IAEac,QAAQ;;;;;;EAMnBC,wBAAwB;;;;;;EAOxBC,+BAA+B;;;;;;EAO/BC,yBAAyB;;;;;;EAOzBC,2BAA2B;AAC7B;AAYO,IAAMC,oBAAoB,MAAA;;AAAqB;IACpDC,QAAQ;MACN;QACEC,WAAW;UAAEC,IAAI;UAA8BC,gBAAgB;QAAW;QAC1EC,IAAI;QACJF,IAAI;MACN;MACA;QACED,WAAW;UAAEC,IAAI;UAA2BC,gBAAgB;QAAW;QACvEC,IAAI;QACJF,IAAI;MACN;MACA;QACED,WAAW;UAAEC,IAAI;UAA4BC,gBAAgB;QAAa;QAC1EC,IAAI;QACJF,IAAI;MACN;MACA;QACED,WAAW;UAAEC,IAAI;UAAiCC,gBAAgB;QAAkB;QACpFC,IAAI;QACJF,IAAI;MACN;MACA;QACED,WAAW;UACTC,IAAI;UACJC,gBAAgB;QAClB;QACAC,IAAI;QACJF,IAAI;MACN;;;MAGI,GAAA,CAACG,OAAOC,OAAOC,SAASC,UAAUH,OAAOC,OAAOC,SAASE,GAAG,OAChEJ,kBAAOC,WAAPD,mBAAeK,UAAfL,mBAAsBM,aAClB;QACE;UACEV,WAAW;YAAEC,IAAI;YAAsBC,gBAAgB;UAAiB;UACxEC,IAAI;UACJF,IAAI;UACJU,aAAa;QACf;MACD,IACD,CAAA;MACA,GAAA,CAACP,OAAOC,OAAOC,SAASC,UAAU,qBAAA,OAA0BH,kBAAOC,WAAPD,mBAAeK,UAAfL,mBAAsBM,aAClF;QACE;UACEV,WAAW;YAAEC,IAAI;YAAkCC,gBAAgB;UAAkB;UACrFC,IAAI;UACJF,IAAI;UACJU,aAAa;QACf;MACD,IACD,CAAA;IACL;IAEDC,OAAO;MACL;QACEZ,WAAW;UAAEC,IAAI;UAAgBC,gBAAgB;QAAQ;QACzDC,IAAI;QACJF,IAAI;MACN;MACA;QACED,WAAW;UAAEC,IAAI;UAAgBC,gBAAgB;QAAQ;;QAEzDC,IAAI;QACJF,IAAI;MACN;MACI,GAAA,CAACG,OAAOC,OAAOC,SAASC,UAAUH,OAAOC,OAAOC,SAASO,UAAU,OACvET,kBAAOC,WAAPD,mBAAeK,UAAfL,mBAAsBM,aAClB;QACE;UACEV,WAAW;YAAEC,IAAI;YAAoBC,gBAAgB;UAAa;UAClEC,IAAI;UACJF,IAAI;UACJU,aAAa;QACf;MACD,IACD,CAAA;IACL;EACH;;", "names": ["Main", "styled", "DSSubNav", "theme", "colors", "neutral0", "neutral150", "StyledLink", "NavLink", "neutral800", "primary100", "primary700", "neutral100", "Link", "props", "label", "endAction", "rest", "_jsx", "Box", "width", "paddingLeft", "paddingRight", "borderRadius", "_jsxs", "Flex", "justifyContent", "gap", "Typography", "tag", "lineHeight", "overflow", "style", "textOverflow", "whiteSpace", "StyledHeader", "spaces", "Header", "variant", "Sections", "children", "paddingBottom", "direction", "alignItems", "map", "child", "index", "li", "Section", "link", "listId", "useId", "position", "textColor", "IconButton", "withTooltip", "onClick", "onClik", "size", "Plus", "id", "marginLeft", "marginRight", "SubSectionHeader", "button", "SubSectionLinkWrapper", "SubSection", "isOpen", "setOpenLinks", "useState", "handleClick", "prev", "aria-expanded", "aria-controls", "ChevronDown", "aria-hidden", "fill", "transform", "transition", "fontWeight", "maxHeight", "SubNav", "ADMIN_PERMISSIONS_CE", "contentManager", "main", "collectionTypesConfigurations", "action", "subject", "componentsConfigurations", "singleTypesConfigurations", "marketplace", "read", "settings", "roles", "create", "delete", "update", "users", "webhooks", "regenerate", "plugins", "HOOKS", "INJECT_COLUMN_IN_TABLE", "MUTATE_COLLECTION_TYPES_LINKS", "MUTATE_EDIT_VIEW_LAYOUT", "MUTATE_SINGLE_TYPES_LINKS", "SETTINGS_LINKS_CE", "global", "intlLabel", "id", "defaultMessage", "to", "window", "strapi", "features", "isEnabled", "SSO", "flags", "promoteEE", "licenseOnly", "admin", "AUDIT_LOGS"]}