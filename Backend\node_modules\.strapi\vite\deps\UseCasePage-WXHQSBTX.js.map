{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/UseCasePage.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {\n  Box,\n  Button,\n  Flex,\n  Main,\n  SingleSelectOption,\n  SingleSelect,\n  TextButton,\n  TextInput,\n  Typography,\n  Field,\n} from '@strapi/design-system';\nimport { parse } from 'qs';\nimport { useIntl } from 'react-intl';\nimport { useLocation, useNavigate } from 'react-router-dom';\n\nimport { PrivateRoute } from '../components/PrivateRoute';\nimport { Logo } from '../components/UnauthenticatedLogo';\nimport { useAuth } from '../features/Auth';\nimport { useNotification } from '../features/Notifications';\nimport { LayoutContent, UnauthenticatedLayout } from '../layouts/UnauthenticatedLayout';\n\nexport const options = [\n  {\n    intlLabel: {\n      id: 'Usecase.front-end',\n      defaultMessage: 'Front-end developer',\n    },\n    value: 'front_end_developer',\n  },\n  {\n    intlLabel: {\n      id: 'Usecase.back-end',\n      defaultMessage: 'Back-end developer',\n    },\n    value: 'back_end_developer',\n  },\n  {\n    intlLabel: {\n      id: 'Usecase.full-stack',\n      defaultMessage: 'Full-stack developer',\n    },\n    value: 'full_stack_developer',\n  },\n  {\n    intlLabel: {\n      id: 'global.content-manager',\n      defaultMessage: 'Content Manager',\n    },\n    value: 'content_manager',\n  },\n  {\n    intlLabel: {\n      id: 'Usecase.content-creator',\n      defaultMessage: 'Content Creator',\n    },\n    value: 'content_creator',\n  },\n  {\n    intlLabel: {\n      id: 'Usecase.other',\n      defaultMessage: 'Other',\n    },\n    value: 'other',\n  },\n];\n\nconst UseCasePage = () => {\n  const { toggleNotification } = useNotification();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { formatMessage } = useIntl();\n  const [role, setRole] = React.useState<string | number | null>(null);\n  const [otherRole, setOtherRole] = React.useState('');\n\n  const { firstname, email } = useAuth('UseCasePage', (state) => state.user) ?? {};\n  const { hasAdmin } = parse(location.search, { ignoreQueryPrefix: true });\n  const isOther = role === 'other';\n\n  const handleSubmit = async (event: React.FormEvent, skipPersona: boolean) => {\n    event.preventDefault();\n    try {\n      await fetch('https://analytics.strapi.io/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          email,\n          username: firstname,\n          firstAdmin: Boolean(!hasAdmin),\n          persona: {\n            role: skipPersona ? undefined : role,\n            otherRole: skipPersona ? undefined : otherRole,\n          },\n        }),\n      });\n\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({\n          id: 'Usecase.notification.success.project-created',\n          defaultMessage: 'Project has been successfully created',\n        }),\n      });\n      navigate('/');\n    } catch (err) {\n      // Silent\n    }\n  };\n\n  return (\n    <UnauthenticatedLayout>\n      <Main labelledBy=\"usecase-title\">\n        <LayoutContent>\n          <form onSubmit={(e) => handleSubmit(e, false)}>\n            <Flex direction=\"column\" paddingBottom={7}>\n              <Logo />\n              <Box paddingTop={6} paddingBottom={1} width={`25rem`}>\n                <Typography textAlign=\"center\" variant=\"alpha\" tag=\"h1\" id=\"usecase-title\">\n                  {formatMessage({\n                    id: 'Usecase.title',\n                    defaultMessage: 'Tell us a bit more about yourself',\n                  })}\n                </Typography>\n              </Box>\n            </Flex>\n            <Flex direction=\"column\" alignItems=\"stretch\" gap={6}>\n              <Field.Root name=\"usecase\">\n                <Field.Label>\n                  {formatMessage({\n                    id: 'Usecase.input.work-type',\n                    defaultMessage: 'What type of work do you do?',\n                  })}\n                </Field.Label>\n                <SingleSelect onChange={(value) => setRole(value)} value={role}>\n                  {options.map(({ intlLabel, value }) => (\n                    <SingleSelectOption key={value} value={value}>\n                      {formatMessage(intlLabel)}\n                    </SingleSelectOption>\n                  ))}\n                </SingleSelect>\n              </Field.Root>\n              {isOther && (\n                <Field.Root name=\"other\">\n                  <Field.Label>\n                    {formatMessage({ id: 'Usecase.other', defaultMessage: 'Other' })}\n                  </Field.Label>\n                  <TextInput value={otherRole} onChange={(e) => setOtherRole(e.target.value)} />\n                </Field.Root>\n              )}\n              <Button type=\"submit\" size=\"L\" fullWidth disabled={!role}>\n                {formatMessage({ id: 'global.finish', defaultMessage: 'Finish' })}\n              </Button>\n            </Flex>\n          </form>\n        </LayoutContent>\n        <Flex justifyContent=\"center\">\n          <Box paddingTop={4}>\n            <TextButton\n              onClick={(event: React.MouseEvent<HTMLButtonElement>) => handleSubmit(event, true)}\n            >\n              {formatMessage({\n                id: 'Usecase.button.skip',\n                defaultMessage: 'Skip this question',\n              })}\n            </TextButton>\n          </Box>\n        </Flex>\n      </Main>\n    </UnauthenticatedLayout>\n  );\n};\n\nconst PrivateUseCasePage = () => {\n  return (\n    <PrivateRoute>\n      <UseCasePage />\n    </PrivateRoute>\n  );\n};\n\nexport { PrivateUseCasePage, UseCasePage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAwBaA,UAAU;EACrB;IACEC,WAAW;MACTC,IAAI;MACJC,gBAAgB;IAClB;IACAC,OAAO;EACT;EACA;IACEH,WAAW;MACTC,IAAI;MACJC,gBAAgB;IAClB;IACAC,OAAO;EACT;EACA;IACEH,WAAW;MACTC,IAAI;MACJC,gBAAgB;IAClB;IACAC,OAAO;EACT;EACA;IACEH,WAAW;MACTC,IAAI;MACJC,gBAAgB;IAClB;IACAC,OAAO;EACT;EACA;IACEH,WAAW;MACTC,IAAI;MACJC,gBAAgB;IAClB;IACAC,OAAO;EACT;EACA;IACEH,WAAW;MACTC,IAAI;MACJC,gBAAgB;IAClB;IACAC,OAAO;EACT;;AAGF,IAAMC,cAAc,MAAA;AAClB,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAMC,WAAWC,YAAAA;AACjB,QAAMC,WAAWC,YAAAA;AACjB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,CAACC,MAAMC,OAAAA,IAAiBC,eAAiC,IAAA;AAC/D,QAAM,CAACC,WAAWC,YAAAA,IAAsBF,eAAS,EAAA;AAEjD,QAAM,EAAEG,WAAWC,MAAK,IAAKC,QAAQ,eAAe,CAACC,UAAUA,MAAMC,IAAI,KAAK,CAAA;AAC9E,QAAM,EAAEC,SAAQ,QAAKC,iBAAMjB,SAASkB,QAAQ;IAAEC,mBAAmB;EAAK,CAAA;AACtE,QAAMC,UAAUd,SAAS;AAEzB,QAAMe,eAAe,OAAOC,OAAwBC,gBAAAA;AAClDD,UAAME,eAAc;AACpB,QAAI;AACF,YAAMC,MAAM,wCAAwC;QAClDC,QAAQ;QACRC,SAAS;UACP,gBAAgB;QAClB;QACAC,MAAMC,KAAKC,UAAU;UACnBlB;UACAmB,UAAUpB;UACVqB,YAAYC,QAAQ,CAACjB,QAAAA;UACrBkB,SAAS;YACP5B,MAAMiB,cAAcY,SAAY7B;YAChCG,WAAWc,cAAcY,SAAY1B;UACvC;QACF,CAAA;MACF,CAAA;AAEAX,yBAAmB;QACjBsC,MAAM;QACNC,SAASjC,cAAc;UACrBV,IAAI;UACJC,gBAAgB;QAClB,CAAA;MACF,CAAA;AACAO,eAAS,GAAA;IACX,SAASoC,KAAK;IAEd;EACF;AAEA,aACEC,wBAACC,uBAAAA;IACC,cAAAC,yBAACC,MAAAA;MAAKC,YAAW;;YACfJ,wBAACK,eAAAA;UACC,cAAAH,yBAACI,QAAAA;YAAKC,UAAU,CAACC,MAAM1B,aAAa0B,GAAG,KAAA;;kBACrCN,yBAACO,MAAAA;gBAAKC,WAAU;gBAASC,eAAe;;sBACtCX,wBAACY,MAAAA,CAAAA,CAAAA;sBACDZ,wBAACa,KAAAA;oBAAIC,YAAY;oBAAGH,eAAe;oBAAGI,OAAO;oBAC3C,cAAAf,wBAACgB,YAAAA;sBAAWC,WAAU;sBAASC,SAAQ;sBAAQC,KAAI;sBAAKhE,IAAG;gCACxDU,cAAc;wBACbV,IAAI;wBACJC,gBAAgB;sBAClB,CAAA;;;;;kBAIN8C,yBAACO,MAAAA;gBAAKC,WAAU;gBAASU,YAAW;gBAAUC,KAAK;;sBACjDnB,yBAACoB,MAAMC,MAAI;oBAACC,MAAK;;0BACfxB,wBAACsB,MAAMG,OAAK;kCACT5D,cAAc;0BACbV,IAAI;0BACJC,gBAAgB;wBAClB,CAAA;;0BAEF4C,wBAAC0B,cAAAA;wBAAaC,UAAU,CAACtE,UAAUW,QAAQX,KAAAA;wBAAQA,OAAOU;kCACvDd,QAAQ2E,IAAI,CAAC,EAAE1E,WAAWG,MAAK,UAC9B2C,wBAAC6B,oBAAAA;0BAA+BxE;oCAC7BQ,cAAcX,SAAAA;wBADQG,GAAAA,KAAAA,CAAAA;;;;kBAM9BwB,eACCqB,yBAACoB,MAAMC,MAAI;oBAACC,MAAK;;0BACfxB,wBAACsB,MAAMG,OAAK;kCACT5D,cAAc;0BAAEV,IAAI;0BAAiBC,gBAAgB;wBAAQ,CAAA;;0BAEhE4C,wBAAC8B,WAAAA;wBAAUzE,OAAOa;wBAAWyD,UAAU,CAACnB,MAAMrC,aAAaqC,EAAEuB,OAAO1E,KAAK;;;;sBAG7E2C,wBAACgC,QAAAA;oBAAOnC,MAAK;oBAASoC,MAAK;oBAAIC,WAAS;oBAACC,UAAU,CAACpE;8BACjDF,cAAc;sBAAEV,IAAI;sBAAiBC,gBAAgB;oBAAS,CAAA;;;;;;;YAKvE4C,wBAACS,MAAAA;UAAK2B,gBAAe;UACnB,cAAApC,wBAACa,KAAAA;YAAIC,YAAY;YACf,cAAAd,wBAACqC,YAAAA;cACCC,SAAS,CAACvD,UAA+CD,aAAaC,OAAO,IAAA;wBAE5ElB,cAAc;gBACbV,IAAI;gBACJC,gBAAgB;cAClB,CAAA;;;;;;;AAOd;AAEA,IAAMmF,qBAAqB,MAAA;AACzB,aACEvC,wBAACwC,cAAAA;IACC,cAAAxC,wBAAC1C,aAAAA,CAAAA,CAAAA;;AAGP;", "names": ["options", "intlLabel", "id", "defaultMessage", "value", "UseCasePage", "toggleNotification", "useNotification", "location", "useLocation", "navigate", "useNavigate", "formatMessage", "useIntl", "role", "setRole", "useState", "otherRole", "setOtherRole", "firstname", "email", "useAuth", "state", "user", "has<PERSON>dmin", "parse", "search", "ignoreQueryPrefix", "isOther", "handleSubmit", "event", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "fetch", "method", "headers", "body", "JSON", "stringify", "username", "firstAdmin", "Boolean", "persona", "undefined", "type", "message", "err", "_jsx", "UnauthenticatedLayout", "_jsxs", "Main", "labelledBy", "LayoutContent", "form", "onSubmit", "e", "Flex", "direction", "paddingBottom", "Logo", "Box", "paddingTop", "width", "Typography", "textAlign", "variant", "tag", "alignItems", "gap", "Field", "Root", "name", "Label", "SingleSelect", "onChange", "map", "SingleSelectOption", "TextInput", "target", "<PERSON><PERSON>", "size", "fullWidth", "disabled", "justifyContent", "TextButton", "onClick", "PrivateUseCasePage", "PrivateRoute"]}