{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/admin/translations/en.json.mjs"], "sourcesContent": ["var configurations = \"Configurations\";\nvar from = \"from\";\nvar en = {\n    \"attribute.boolean\": \"Boolean\",\n    \"attribute.boolean.description\": \"Yes or no, 1 or 0, true or false\",\n    \"attribute.component\": \"Component\",\n    \"attribute.component.description\": \"Group of fields that you can repeat or reuse\",\n    \"attribute.customField\": \"Custom field\",\n    \"attribute.date\": \"Date\",\n    \"attribute.date.description\": \"A date picker with hours, minutes and seconds\",\n    \"attribute.datetime\": \"Datetime\",\n    \"attribute.dynamiczone\": \"Dynamic zone\",\n    \"attribute.dynamiczone.description\": \"Dynamically pick component when editing content\",\n    \"attribute.email\": \"Email\",\n    \"attribute.email.description\": \"Email field with validations format\",\n    \"attribute.enumeration\": \"Enumeration\",\n    \"attribute.enumeration.description\": \"List of values, then pick one\",\n    \"attribute.json\": \"JSON\",\n    \"attribute.json.description\": \"Data in JSON format\",\n    \"attribute.media\": \"Media\",\n    \"attribute.media.description\": \"Files like images, videos, etc\",\n    \"attribute.null\": \" \",\n    \"attribute.number\": \"Number\",\n    \"attribute.number.description\": \"Numbers (integer, float, decimal)\",\n    \"attribute.password\": \"Password\",\n    \"attribute.password.description\": \"Password field with encryption\",\n    \"attribute.relation\": \"Relation\",\n    \"attribute.relation.description\": \"Refers to a Collection Type\",\n    \"attribute.richtext\": \"Rich text (Markdown)\",\n    \"attribute.richtext.description\": \"The classic rich text editor\",\n    \"attribute.blocks\": \"Rich text (Blocks)\",\n    \"attribute.blocks.description\": \"The new JSON-based rich text editor\",\n    \"attribute.text\": \"Text\",\n    \"attribute.text.description\": \"Small or long text like title or description\",\n    \"attribute.time\": \"Time\",\n    \"attribute.timestamp\": \"Timestamp\",\n    \"attribute.uid\": \"UID\",\n    \"attribute.uid.description\": \"Unique identifier\",\n    \"button.attributes.add.another\": \"Add another field\",\n    \"button.component.add\": \"Add a component\",\n    \"button.component.create\": \"Create new component\",\n    \"button.model.create\": \"Create new collection type\",\n    \"button.single-types.create\": \"Create new single type\",\n    \"media.multiple\": \"Multiple\",\n    \"component.repeatable\": \"Repeatable\",\n    \"components.SelectComponents.displayed-value\": \"{number, plural, =0 {# components} one {# component} other {# components}} selected\",\n    \"components.componentSelect.no-component-available\": \"You have already added all your components\",\n    \"components.componentSelect.no-component-available.with-search\": \"There is no component matching your search\",\n    \"components.componentSelect.value-component\": \"{number} component selected (type to search for a component)\",\n    \"components.componentSelect.value-components\": \"{number} components selected\",\n    configurations: configurations,\n    \"contentType.apiId-plural.description\": \"Pluralized API ID\",\n    \"contentType.apiId-plural.label\": \"API ID (Plural)\",\n    \"contentType.apiId-singular.description\": \"The UID is used to generate the API routes and databases tables/collections\",\n    \"contentType.apiId-singular.label\": \"API ID (Singular)\",\n    \"contentType.collectionName.description\": \"Useful when the name of your Content Type and your table name differ\",\n    \"contentType.collectionName.label\": \"Collection name\",\n    \"contentType.displayName.label\": \"Display name\",\n    \"contentType.kind.change.warning\": \"You just changed the kind of a content type: API will be reset (routes, controllers, and services will be overwritten).\",\n    \"error.attributeName.reserved-name\": \"This name cannot be used in your content type as it might break other functionalities\",\n    \"error.contentType.pluralName-used\": \"This value cannot be the same as the singular one\",\n    \"error.contentType.singularName-used\": \"This value cannot be the same as the plural one\",\n    \"error.contentType.singularName-equals-pluralName\": \"This value cannot be the same as the Plural API ID of another content type.\",\n    \"error.contentType.pluralName-equals-singularName\": \"This value cannot be the same as the Singular API ID of another content type.\",\n    \"error.contentType.pluralName-equals-collectionName\": \"This value is already in use by another content type.\",\n    \"error.contentTypeName.reserved-name\": \"This name cannot be used in your project as it might break other functionalities\",\n    \"error.validation.enum-duplicate\": \"Duplicate values are not allowed (only alphanumeric characters are taken into account).\",\n    \"error.validation.enum-empty-string\": \"Empty strings are not allowed\",\n    \"error.validation.enum-regex\": \"At least one value is invalid. Values should have at least one alphabetical character preceding the first occurence of a number.\",\n    \"error.validation.minSupMax\": \"Min can't be superior to max\",\n    \"error.validation.positive\": \"Must be a positive number\",\n    \"error.validation.regex\": \"Regex pattern is invalid\",\n    \"error.validation.relation.targetAttribute-taken\": \"This name exists in the target\",\n    \"form.attribute.component.option.add\": \"Add a component\",\n    \"form.attribute.component.option.create\": \"Create a new component\",\n    \"form.attribute.component.option.create.description\": \"A component is shared across types and components, it will be available and accessible everywhere.\",\n    \"form.attribute.component.option.repeatable\": \"Repeatable component\",\n    \"form.attribute.component.option.repeatable.description\": \"Best for multiple instances (array) of ingredients, meta tags, etc..\",\n    \"form.attribute.component.option.reuse-existing\": \"Use an existing component\",\n    \"form.attribute.component.option.reuse-existing.description\": \"Reuse a component already created to keep your data consistent across content-types.\",\n    \"form.attribute.component.option.single\": \"Single component\",\n    \"form.attribute.component.option.single.description\": \"Best for grouping fields like full address, main information, etc...\",\n    \"form.attribute.item.customColumnName\": \"Custom column names\",\n    \"form.attribute.item.customColumnName.description\": \"This is useful to rename database column names in a more comprehensive format for the API's responses\",\n    \"form.attribute.item.date.type.date\": \"date (ex: 01/01/{currentYear})\",\n    \"form.attribute.item.date.type.datetime\": \"datetime (ex: 01/01/{currentYear} 00:00 AM)\",\n    \"form.attribute.item.date.type.time\": \"time (ex: 00:00 AM)\",\n    \"form.attribute.item.defineRelation.fieldName\": \"Field name\",\n    \"form.attribute.item.enumeration.graphql\": \"Name override for GraphQL\",\n    \"form.attribute.item.enumeration.graphql.description\": \"Allows you to override the default generated name for GraphQL\",\n    \"form.attribute.item.enumeration.placeholder\": \"Ex:\\nmorning\\nnoon\\nevening\",\n    \"form.attribute.item.enumeration.rules\": \"Values (one line per value)\",\n    \"form.attribute.item.maximum\": \"Maximum value\",\n    \"form.attribute.item.maximumComponents\": \"Maximum components\",\n    \"form.attribute.item.maximumLength\": \"Maximum length\",\n    \"form.attribute.item.minimum\": \"Minimum value\",\n    \"form.attribute.item.minimumComponents\": \"Minimum components\",\n    \"form.attribute.item.minimumLength\": \"Minimum length\",\n    \"form.attribute.item.number.type\": \"Number format\",\n    \"form.attribute.item.number.type.biginteger\": \"big integer (ex: 123456789)\",\n    \"form.attribute.item.number.type.decimal\": \"decimal (ex: 2.22)\",\n    \"form.attribute.item.number.type.float\": \"float (ex: 3.33333333)\",\n    \"form.attribute.item.number.type.integer\": \"integer (ex: 10)\",\n    \"form.attribute.item.privateField\": \"Private field\",\n    \"form.attribute.item.privateField.description\": \"This field will not show up in the API response\",\n    \"form.attribute.item.requiredField\": \"Required field\",\n    \"form.attribute.item.requiredField.description\": \"You won't be able to create an entry if this field is empty\",\n    \"form.attribute.item.text.regex\": \"RegExp pattern\",\n    \"form.attribute.item.text.regex.description\": \"The text of the regular expression\",\n    \"form.attribute.item.uniqueField\": \"Unique field\",\n    \"form.attribute.item.uniqueField.description\": \"You won't be able to create an entry if there is an existing entry with identical content\",\n    \"form.attribute.item.uniqueField.v5.willBeDisabled'\": \"Currently unique fields don't work correctly in components. If you disable this feature, the field will be disabled until this is fixed.\",\n    \"form.attribute.item.uniqueField.v5.disabled\": \"Currently unique fields don't work correctly in components. This field has been disabled until it's fixed.\",\n    \"form.attribute.media.allowed-types\": \"Select allowed types of media\",\n    \"form.attribute.media.allowed-types.option-files\": \"Files\",\n    \"form.attribute.media.allowed-types.option-images\": \"Images\",\n    \"form.attribute.media.allowed-types.option-videos\": \"Videos\",\n    \"form.attribute.media.option.multiple\": \"Multiple media\",\n    \"form.attribute.media.option.multiple.description\": \"Best for sliders, carousels or multiple files download\",\n    \"form.attribute.media.option.single\": \"Single media\",\n    \"form.attribute.media.option.single.description\": \"Best for avatar, profile picture or cover\",\n    \"form.attribute.settings.default\": \"Default value\",\n    \"form.attribute.text.option.long-text\": \"Long text\",\n    \"form.attribute.text.option.long-text.description\": \"Best for descriptions, biography. Exact search is disabled.\",\n    \"form.attribute.text.option.short-text\": \"Short text\",\n    \"form.attribute.text.option.short-text.description\": \"Best for titles, names, links (URL). It also enables exact search on the field.\",\n    \"form.button.add-components-to-dynamiczone\": \"Add components to the zone\",\n    \"form.button.add-field\": \"Add another field\",\n    \"form.button.add-first-field-to-created-component\": \"Add first field to the component\",\n    \"form.button.add.field.to.collectionType\": \"Add another field to this collection type\",\n    \"form.button.add.field.to.component\": \"Add another field to this component\",\n    \"form.button.add.field.to.contentType\": \"Add another field to this content type\",\n    \"form.button.add.field.to.singleType\": \"Add another field to this single type\",\n    \"form.button.cancel\": \"Cancel\",\n    \"form.button.collection-type.description\": \"Best for multiple instances like articles, products, comments, etc.\",\n    \"form.button.collection-type.name\": \"Collection Type\",\n    \"form.button.configure-component\": \"Configure the component\",\n    \"form.button.configure-view\": \"Configure the view\",\n    \"form.button.select-component\": \"Select a component\",\n    \"form.button.single-type.description\": \"Best for single instance like about us, homepage, etc.\",\n    \"form.button.single-type.name\": \"Single Type\",\n    from: from,\n    \"menu.section.components.name\": \"Components\",\n    \"menu.section.models.name\": \"Collection Types\",\n    \"menu.section.single-types.name\": \"Single Types\",\n    \"modalForm.attribute.form.base.name.description\": \"No space is allowed for the name of the attribute\",\n    \"modalForm.attribute.form.base.name.placeholder\": \"e.g. slug, seoUrl, canonicalUrl\",\n    \"modalForm.attribute.target-field\": \"Attached field\",\n    \"modalForm.attributes.select-component\": \"Select a component\",\n    \"modalForm.attributes.select-components\": \"Select the components\",\n    \"modalForm.collectionType.header-create\": \"Create a collection type\",\n    \"modalForm.component.header-create\": \"Create a component\",\n    \"modalForm.components.create-component.category.label\": \"Select a category or enter a name to create a new one\",\n    \"modalForm.components.icon.label\": \"Icon\",\n    \"modalForm.empty.button\": \"Add custom fields\",\n    \"modalForm.empty.heading\": \"Nothing in here yet.\",\n    \"modalForm.empty.sub-heading\": \"Find what you are looking for through a wide range of extensions.\",\n    \"modalForm.editCategory.base.name.description\": \"No space is allowed for the name of the category\",\n    \"modalForm.header-edit\": \"Edit {name}\",\n    \"modalForm.header.categories\": \"Categories\",\n    \"modalForm.header.back\": \"Back\",\n    \"modalForm.singleType.header-create\": \"Create a single type\",\n    \"modalForm.sub-header.addComponentToDynamicZone\": \"Add new component to the dynamic zone\",\n    \"modalForm.sub-header.attribute.create\": \"Add new {type} field\",\n    \"modalForm.sub-header.attribute.create.step\": \"Add new component ({step}/2)\",\n    \"modalForm.sub-header.attribute.edit\": \"Edit {name}\",\n    \"modalForm.sub-header.chooseAttribute.collectionType\": \"Select a field for your collection type\",\n    \"modalForm.sub-header.chooseAttribute.component\": \"Select a field for your component\",\n    \"modalForm.sub-header.chooseAttribute.singleType\": \"Select a field for your single type\",\n    \"modalForm.custom-fields.advanced.settings.extended\": \"Extended settings\",\n    \"modalForm.tabs.custom\": \"Custom\",\n    \"modalForm.tabs.custom.howToLink\": \"How to add custom fields\",\n    \"modalForm.tabs.default\": \"Default\",\n    \"modalForm.tabs.label\": \"Default and Custom types tabs\",\n    \"modelPage.attribute.relation-polymorphic\": \"Relation (polymorphic)\",\n    \"modelPage.attribute.relationWith\": \"Relation with\",\n    \"modelPage.attribute.with\": \"with\",\n    \"notification.error.dynamiczone-min.validation\": \"At least one component is required in a dynamic zone to be able to save a content type\",\n    \"notification.info.autoreaload-disable\": \"Strapi is in production mode, editing content types is disabled. Please switch to development mode by starting your server with strapi develop.\",\n    \"notification.info.creating.notSaved\": \"Please save your work before creating a new collection type or component\",\n    \"plugin.description.long\": \"Modelize the data structure of your API. Create new fields and relations in just a minute. The files are automatically created and updated in your project.\",\n    \"plugin.description.short\": \"Modelize the data structure of your API.\",\n    \"plugin.name\": \"Content-Type Builder\",\n    \"popUpForm.navContainer.advanced\": \"Advanced settings\",\n    \"popUpForm.navContainer.base\": \"Basic settings\",\n    \"popUpWarning.bodyMessage.cancel-modifications\": \"Are you sure you want to cancel your modifications?\",\n    \"popUpWarning.bodyMessage.cancel-modifications.with-components\": \"Are you sure you want to cancel your modifications? Some components have been created or modified...\",\n    \"popUpWarning.bodyMessage.category.delete\": \"Are you sure you want to delete this category? All the components will also be deleted.\",\n    \"popUpWarning.bodyMessage.component.delete\": \"Are you sure you want to delete this component?\",\n    \"popUpWarning.bodyMessage.contentType.delete\": \"Are you sure you want to delete this collection type?\",\n    \"popUpWarning.draft-publish.button.confirm\": \"Yes, disable\",\n    \"popUpWarning.draft-publish.message\": \"If you disable the Draft & publish, your drafts will be deleted.\",\n    \"popUpWarning.draft-publish.second-message\": \"Are you sure you want to disable it?\",\n    \"popUpWarning.discardAll.message\": \"Are you sure you want to discard all changes?\",\n    \"prompt.unsaved\": \"Are you sure you want to leave? All your modifications will be lost.\",\n    \"relation.attributeName.placeholder\": \"Ex: author, category, tag\",\n    \"relation.manyToMany\": \"has and belongs to many\",\n    \"relation.manyToOne\": \"has many\",\n    \"relation.manyWay\": \"has many\",\n    \"relation.oneToMany\": \"belongs to many\",\n    \"relation.oneToOne\": \"has and belongs to one\",\n    \"relation.oneWay\": \"has one\",\n    \"table.button.no-fields\": \"Add new field\",\n    \"table.content.create-first-content-type\": \"Create your first Collection-Type\",\n    \"table.content.no-fields.collection-type\": \"Add your first field to this Collection-Type\",\n    \"table.content.no-fields.component\": \"Add your first field to this component\",\n    \"IconPicker.search.placeholder.label\": \"Search for an icon\",\n    \"IconPicker.search.clear.label\": \"Clear the icon search\",\n    \"IconPicker.search.button.label\": \"Search icon button\",\n    \"IconPicker.remove.tooltip\": \"Remove the selected icon\",\n    \"IconPicker.remove.button\": \"Remove the selected icon button\",\n    \"IconPicker.emptyState.label\": \"No icon found\",\n    \"IconPicker.icon.label\": \"Select {icon} icon\"\n};\n\nexport { configurations, en as default, from };\n//# sourceMappingURL=en.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,iBAAiB;AACrB,IAAI,OAAO;AACX,IAAI,KAAK;AAAA,EACL,qBAAqB;AAAA,EACrB,iCAAiC;AAAA,EACjC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,gCAAgC;AAAA,EAChC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,oBAAoB;AAAA,EACpB,gCAAgC;AAAA,EAChC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,wBAAwB;AAAA,EACxB,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,iEAAiE;AAAA,EACjE,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C;AAAA,EACA,wCAAwC;AAAA,EACxC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,sDAAsD;AAAA,EACtD,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,8DAA8D;AAAA,EAC9D,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,0CAA0C;AAAA,EAC1C,sCAAsC;AAAA,EACtC,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,8CAA8C;AAAA,EAC9C,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,+CAA+C;AAAA,EAC/C,sCAAsC;AAAA,EACtC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,6CAA6C;AAAA,EAC7C,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,sBAAsB;AAAA,EACtB,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC;AAAA,EACA,gCAAgC;AAAA,EAChC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,wDAAwD;AAAA,EACxD,mCAAmC;AAAA,EACnC,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,gDAAgD;AAAA,EAChD,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,sDAAsD;AAAA,EACtD,yBAAyB;AAAA,EACzB,mCAAmC;AAAA,EACnC,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,iDAAiD;AAAA,EACjD,iEAAiE;AAAA,EACjE,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,kBAAkB;AAAA,EAClB,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,yBAAyB;AAC7B;", "names": []}