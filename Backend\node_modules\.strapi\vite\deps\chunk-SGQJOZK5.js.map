{"version": 3, "sources": ["../../../lodash/set.js"], "sourcesContent": ["var baseSet = require('./_baseSet');\n\n/**\n * Sets the value at `path` of `object`. If a portion of `path` doesn't exist,\n * it's created. Arrays are created for missing index properties while objects\n * are created for all other missing properties. Use `_.setWith` to customize\n * `path` creation.\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @memberOf _\n * @since 3.7.0\n * @category Object\n * @param {Object} object The object to modify.\n * @param {Array|string} path The path of the property to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns `object`.\n * @example\n *\n * var object = { 'a': [{ 'b': { 'c': 3 } }] };\n *\n * _.set(object, 'a[0].b.c', 4);\n * console.log(object.a[0].b.c);\n * // => 4\n *\n * _.set(object, ['x', '0', 'y', 'z'], 5);\n * console.log(object.x[0].y.z);\n * // => 5\n */\nfunction set(object, path, value) {\n  return object == null ? object : baseSet(object, path, value);\n}\n\nmodule.exports = set;\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,QAAI,UAAU;AA8Bd,aAAS,IAAI,QAAQ,MAAM,OAAO;AAChC,aAAO,UAAU,OAAO,SAAS,QAAQ,QAAQ,MAAM,KAAK;AAAA,IAC9D;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}