{"version": 3, "sources": ["../../../fractional-indexing/src/index.js", "../../../@strapi/admin/admin/src/components/Form.tsx"], "sourcesContent": ["// License: CC0 (no rights reserved).\n\n// This is based on https://observablehq.com/@dgreensp/implementing-fractional-indexing\n\nexport const BASE_62_DIGITS =\n  \"**********ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz\";\n\n// `a` may be empty string, `b` is null or non-empty string.\n// `a < b` lexicographically if `b` is non-null.\n// no trailing zeros allowed.\n// digits is a string such as '**********' for base 10.  Digits must be in\n// ascending character code order!\n/**\n * @param {string} a\n * @param {string | null | undefined} b\n * @param {string} digits\n * @returns {string}\n */\nfunction midpoint(a, b, digits) {\n  const zero = digits[0];\n  if (b != null && a >= b) {\n    throw new Error(a + \" >= \" + b);\n  }\n  if (a.slice(-1) === zero || (b && b.slice(-1) === zero)) {\n    throw new Error(\"trailing zero\");\n  }\n  if (b) {\n    // remove longest common prefix.  pad `a` with 0s as we\n    // go.  note that we don't need to pad `b`, because it can't\n    // end before `a` while traversing the common prefix.\n    let n = 0;\n    while ((a[n] || zero) === b[n]) {\n      n++;\n    }\n    if (n > 0) {\n      return b.slice(0, n) + midpoint(a.slice(n), b.slice(n), digits);\n    }\n  }\n  // first digits (or lack of digit) are different\n  const digitA = a ? digits.indexOf(a[0]) : 0;\n  const digitB = b != null ? digits.indexOf(b[0]) : digits.length;\n  if (digitB - digitA > 1) {\n    const midDigit = Math.round(0.5 * (digitA + digitB));\n    return digits[midDigit];\n  } else {\n    // first digits are consecutive\n    if (b && b.length > 1) {\n      return b.slice(0, 1);\n    } else {\n      // `b` is null or has length 1 (a single digit).\n      // the first digit of `a` is the previous digit to `b`,\n      // or 9 if `b` is null.\n      // given, for example, midpoint('49', '5'), return\n      // '4' + midpoint('9', null), which will become\n      // '4' + '9' + midpoint('', null), which is '495'\n      return digits[digitA] + midpoint(a.slice(1), null, digits);\n    }\n  }\n}\n\n/**\n * @param {string} int\n * @return {void}\n */\n\nfunction validateInteger(int) {\n  if (int.length !== getIntegerLength(int[0])) {\n    throw new Error(\"invalid integer part of order key: \" + int);\n  }\n}\n\n/**\n * @param {string} head\n * @return {number}\n */\n\nfunction getIntegerLength(head) {\n  if (head >= \"a\" && head <= \"z\") {\n    return head.charCodeAt(0) - \"a\".charCodeAt(0) + 2;\n  } else if (head >= \"A\" && head <= \"Z\") {\n    return \"Z\".charCodeAt(0) - head.charCodeAt(0) + 2;\n  } else {\n    throw new Error(\"invalid order key head: \" + head);\n  }\n}\n\n/**\n * @param {string} key\n * @return {string}\n */\n\nfunction getIntegerPart(key) {\n  const integerPartLength = getIntegerLength(key[0]);\n  if (integerPartLength > key.length) {\n    throw new Error(\"invalid order key: \" + key);\n  }\n  return key.slice(0, integerPartLength);\n}\n\n/**\n * @param {string} key\n * @param {string} digits\n * @return {void}\n */\n\nfunction validateOrderKey(key, digits) {\n  if (key === \"A\" + digits[0].repeat(26)) {\n    throw new Error(\"invalid order key: \" + key);\n  }\n  // getIntegerPart will throw if the first character is bad,\n  // or the key is too short.  we'd call it to check these things\n  // even if we didn't need the result\n  const i = getIntegerPart(key);\n  const f = key.slice(i.length);\n  if (f.slice(-1) === digits[0]) {\n    throw new Error(\"invalid order key: \" + key);\n  }\n}\n\n// note that this may return null, as there is a largest integer\n/**\n * @param {string} x\n * @param {string} digits\n * @return {string | null}\n */\nfunction incrementInteger(x, digits) {\n  validateInteger(x);\n  const [head, ...digs] = x.split(\"\");\n  let carry = true;\n  for (let i = digs.length - 1; carry && i >= 0; i--) {\n    const d = digits.indexOf(digs[i]) + 1;\n    if (d === digits.length) {\n      digs[i] = digits[0];\n    } else {\n      digs[i] = digits[d];\n      carry = false;\n    }\n  }\n  if (carry) {\n    if (head === \"Z\") {\n      return \"a\" + digits[0];\n    }\n    if (head === \"z\") {\n      return null;\n    }\n    const h = String.fromCharCode(head.charCodeAt(0) + 1);\n    if (h > \"a\") {\n      digs.push(digits[0]);\n    } else {\n      digs.pop();\n    }\n    return h + digs.join(\"\");\n  } else {\n    return head + digs.join(\"\");\n  }\n}\n\n// note that this may return null, as there is a smallest integer\n/**\n * @param {string} x\n * @param {string} digits\n * @return {string | null}\n */\n\nfunction decrementInteger(x, digits) {\n  validateInteger(x);\n  const [head, ...digs] = x.split(\"\");\n  let borrow = true;\n  for (let i = digs.length - 1; borrow && i >= 0; i--) {\n    const d = digits.indexOf(digs[i]) - 1;\n    if (d === -1) {\n      digs[i] = digits.slice(-1);\n    } else {\n      digs[i] = digits[d];\n      borrow = false;\n    }\n  }\n  if (borrow) {\n    if (head === \"a\") {\n      return \"Z\" + digits.slice(-1);\n    }\n    if (head === \"A\") {\n      return null;\n    }\n    const h = String.fromCharCode(head.charCodeAt(0) - 1);\n    if (h < \"Z\") {\n      digs.push(digits.slice(-1));\n    } else {\n      digs.pop();\n    }\n    return h + digs.join(\"\");\n  } else {\n    return head + digs.join(\"\");\n  }\n}\n\n// `a` is an order key or null (START).\n// `b` is an order key or null (END).\n// `a < b` lexicographically if both are non-null.\n// digits is a string such as '**********' for base 10.  Digits must be in\n// ascending character code order!\n/**\n * @param {string | null | undefined} a\n * @param {string | null | undefined} b\n * @param {string=} digits\n * @return {string}\n */\nexport function generateKeyBetween(a, b, digits = BASE_62_DIGITS) {\n  if (a != null) {\n    validateOrderKey(a, digits);\n  }\n  if (b != null) {\n    validateOrderKey(b, digits);\n  }\n  if (a != null && b != null && a >= b) {\n    throw new Error(a + \" >= \" + b);\n  }\n  if (a == null) {\n    if (b == null) {\n      return \"a\" + digits[0];\n    }\n\n    const ib = getIntegerPart(b);\n    const fb = b.slice(ib.length);\n    if (ib === \"A\" + digits[0].repeat(26)) {\n      return ib + midpoint(\"\", fb, digits);\n    }\n    if (ib < b) {\n      return ib;\n    }\n    const res = decrementInteger(ib, digits);\n    if (res == null) {\n      throw new Error(\"cannot decrement any more\");\n    }\n    return res;\n  }\n\n  if (b == null) {\n    const ia = getIntegerPart(a);\n    const fa = a.slice(ia.length);\n    const i = incrementInteger(ia, digits);\n    return i == null ? ia + midpoint(fa, null, digits) : i;\n  }\n\n  const ia = getIntegerPart(a);\n  const fa = a.slice(ia.length);\n  const ib = getIntegerPart(b);\n  const fb = b.slice(ib.length);\n  if (ia === ib) {\n    return ia + midpoint(fa, fb, digits);\n  }\n  const i = incrementInteger(ia, digits);\n  if (i == null) {\n    throw new Error(\"cannot increment any more\");\n  }\n  if (i < b) {\n    return i;\n  }\n  return ia + midpoint(fa, null, digits);\n}\n\n/**\n * same preconditions as generateKeysBetween.\n * n >= 0.\n * Returns an array of n distinct keys in sorted order.\n * If a and b are both null, returns [a0, a1, ...]\n * If one or the other is null, returns consecutive \"integer\"\n * keys.  Otherwise, returns relatively short keys between\n * a and b.\n * @param {string | null | undefined} a\n * @param {string | null | undefined} b\n * @param {number} n\n * @param {string} digits\n * @return {string[]}\n */\nexport function generateNKeysBetween(a, b, n, digits = BASE_62_DIGITS) {\n  if (n === 0) {\n    return [];\n  }\n  if (n === 1) {\n    return [generateKeyBetween(a, b, digits)];\n  }\n  if (b == null) {\n    let c = generateKeyBetween(a, b, digits);\n    const result = [c];\n    for (let i = 0; i < n - 1; i++) {\n      c = generateKeyBetween(c, b, digits);\n      result.push(c);\n    }\n    return result;\n  }\n  if (a == null) {\n    let c = generateKeyBetween(a, b, digits);\n    const result = [c];\n    for (let i = 0; i < n - 1; i++) {\n      c = generateKeyBetween(a, c, digits);\n      result.push(c);\n    }\n    result.reverse();\n    return result;\n  }\n  const mid = Math.floor(n / 2);\n  const c = generateKeyBetween(a, b, digits);\n  return [\n    ...generateNKeysBetween(a, c, mid, digits),\n    c,\n    ...generateNKeysBetween(c, b, n - mid - 1, digits),\n  ];\n}\n", "import * as React from 'react';\n\nimport {\n  Box,\n  type BoxProps,\n  Button,\n  Dialog,\n  useCallbackRef,\n  useComposedRefs,\n} from '@strapi/design-system';\nimport { WarningCircle } from '@strapi/icons';\nimport { generateNKeysBetween } from 'fractional-indexing';\nimport { produce } from 'immer';\nimport isEqual from 'lodash/isEqual';\nimport { useIntl, type MessageDescriptor, type PrimitiveType } from 'react-intl';\nimport { useBlocker } from 'react-router-dom';\n\nimport { getIn, setIn } from '../utils/objects';\n\nimport { createContext } from './Context';\n\nimport type {\n  InputProps as InputPropsImpl,\n  StringProps,\n  EnumerationProps,\n} from './FormInputs/types';\nimport type * as Yup from 'yup';\n\n/* -------------------------------------------------------------------------------------------------\n * FormContext\n * -----------------------------------------------------------------------------------------------*/\ntype InputProps = InputPropsImpl | StringProps | EnumerationProps;\n\ninterface TranslationMessage extends MessageDescriptor {\n  values?: Record<string, PrimitiveType>;\n}\n\ninterface FormValues {\n  [field: string]: any;\n}\n\ninterface FormContextValue<TFormValues extends FormValues = FormValues>\n  extends FormState<TFormValues> {\n  disabled: boolean;\n  initialValues: TFormValues;\n  modified: boolean;\n  /**\n   * The default behaviour is to add the row to the end of the array, if you want to add it to a\n   * specific index you can pass the index.\n   */\n  addFieldRow: (field: string, value: any, addAtIndex?: number) => void;\n  moveFieldRow: (field: string, fromIndex: number, toIndex: number) => void;\n  onChange: (eventOrPath: React.ChangeEvent<any> | string, value?: any) => void;\n  /*\n   * The default behaviour is to remove the last row, if you want to remove a specific index you can\n   * pass the index.\n   */\n  removeFieldRow: (field: string, removeAtIndex?: number) => void;\n  resetForm: () => void;\n  setErrors: (errors: FormErrors<TFormValues>) => void;\n  setSubmitting: (isSubmitting: boolean) => void;\n  setValues: (values: TFormValues) => void;\n  validate: (\n    shouldSetErrors?: boolean,\n    options?: Record<string, string>\n  ) => Promise<\n    { data: TFormValues; errors?: never } | { data?: never; errors: FormErrors<TFormValues> }\n  >;\n}\n\n/**\n * @internal\n * @description We use this just to warn people that they're using the useForm\n * methods outside of a Form component, but we don't want to throw an error\n * because otherwise the DocumentActions list cannot be rendered in our list-view.\n */\nconst ERR_MSG =\n  'The Form Component has not been initialised, ensure you are using this hook within a Form component';\n\nconst [FormProvider, useForm] = createContext<FormContextValue>('Form', {\n  disabled: false,\n  errors: {},\n  initialValues: {},\n  isSubmitting: false,\n  modified: false,\n  addFieldRow: () => {\n    throw new Error(ERR_MSG);\n  },\n  moveFieldRow: () => {\n    throw new Error(ERR_MSG);\n  },\n  onChange: () => {\n    throw new Error(ERR_MSG);\n  },\n  removeFieldRow: () => {\n    throw new Error(ERR_MSG);\n  },\n  resetForm: () => {\n    throw new Error(ERR_MSG);\n  },\n  setErrors: () => {\n    throw new Error(ERR_MSG);\n  },\n  setValues: () => {\n    throw new Error(ERR_MSG);\n  },\n  setSubmitting: () => {\n    throw new Error(ERR_MSG);\n  },\n  validate: async () => {\n    throw new Error(ERR_MSG);\n  },\n  values: {},\n});\n\n/* -------------------------------------------------------------------------------------------------\n * Form\n * -----------------------------------------------------------------------------------------------*/\n\ninterface FormHelpers<TFormValues extends FormValues = FormValues>\n  extends Pick<FormContextValue<TFormValues>, 'setErrors' | 'setValues' | 'resetForm'> {}\n\ninterface FormProps<TFormValues extends FormValues = FormValues>\n  extends Partial<Pick<FormContextValue<TFormValues>, 'disabled' | 'initialValues'>>,\n    Pick<BoxProps, 'width' | 'height'> {\n  children:\n    | React.ReactNode\n    | ((\n        props: Pick<\n          FormContextValue<TFormValues>,\n          | 'disabled'\n          | 'errors'\n          | 'isSubmitting'\n          | 'modified'\n          | 'values'\n          | 'resetForm'\n          | 'onChange'\n          | 'setErrors'\n        >\n      ) => React.ReactNode);\n  method: 'POST' | 'PUT';\n  onSubmit?: (values: TFormValues, helpers: FormHelpers<TFormValues>) => Promise<void> | void;\n  // TODO: type the return value for a validation schema func from Yup.\n  validationSchema?: Yup.AnySchema;\n  initialErrors?: FormErrors<TFormValues>;\n  // NOTE: we don't know what return type it can be here\n  validate?: (values: TFormValues, options: Record<string, string>) => Promise<any>;\n}\n\n/**\n * @alpha\n * @description A form component that handles form state, validation and submission.\n * It can additionally handle nested fields and arrays. To access the data you can either\n * use the generic useForm hook or the useField hook when providing the name of your field.\n */\nconst Form = React.forwardRef<HTMLFormElement, FormProps>(\n  ({ disabled = false, method, onSubmit, initialErrors, ...props }, ref) => {\n    const formRef = React.useRef<HTMLFormElement>(null!);\n    const initialValues = React.useRef(props.initialValues ?? {});\n    const [state, dispatch] = React.useReducer(reducer, {\n      errors: initialErrors ?? {},\n      isSubmitting: false,\n      values: props.initialValues ?? {},\n    });\n\n    React.useEffect(() => {\n      /**\n       * ONLY update the initialValues if the prop has changed.\n       */\n      if (!isEqual(initialValues.current, props.initialValues)) {\n        initialValues.current = props.initialValues ?? {};\n\n        dispatch({\n          type: 'SET_INITIAL_VALUES',\n          payload: props.initialValues ?? {},\n        });\n      }\n    }, [props.initialValues]);\n\n    const setErrors = React.useCallback((errors: FormErrors) => {\n      dispatch({\n        type: 'SET_ERRORS',\n        payload: errors,\n      });\n    }, []);\n\n    const setValues = React.useCallback((values: FormValues) => {\n      dispatch({\n        type: 'SET_VALUES',\n        payload: values,\n      });\n    }, []);\n\n    React.useEffect(() => {\n      if (Object.keys(state.errors).length === 0) return;\n\n      /**\n       * Small timeout to ensure the form has been\n       * rendered before we try to focus on the first\n       */\n      const ref = setTimeout(() => {\n        const [firstError] = formRef.current.querySelectorAll('[data-strapi-field-error]');\n\n        if (firstError) {\n          const errorId = firstError.getAttribute('id');\n          const formElementInError = formRef.current.querySelector(\n            `[aria-describedby=\"${errorId}\"]`\n          );\n\n          if (formElementInError && formElementInError instanceof HTMLElement) {\n            formElementInError.focus();\n          }\n        }\n      });\n\n      return () => clearTimeout(ref);\n    }, [state.errors]);\n\n    /**\n     * Uses the provided validation schema\n     */\n    const validate = React.useCallback(\n      async (shouldSetErrors: boolean = true, options: Record<string, string> = {}) => {\n        setErrors({});\n\n        if (!props.validationSchema && !props.validate) {\n          return { data: state.values };\n        }\n\n        try {\n          let data;\n          if (props.validationSchema) {\n            data = await props.validationSchema.validate(state.values, { abortEarly: false });\n          } else if (props.validate) {\n            data = await props.validate(state.values, options);\n          } else {\n            throw new Error('No validation schema or validate function provided');\n          }\n\n          return { data };\n        } catch (err) {\n          if (isErrorYupValidationError(err)) {\n            const errors = getYupValidationErrors(err);\n\n            if (shouldSetErrors) {\n              setErrors(errors);\n            }\n\n            return { errors };\n          } else {\n            // We throw any other errors\n            if (process.env.NODE_ENV !== 'production') {\n              console.warn(\n                `Warning: An unhandled error was caught during validation in <Form validationSchema />`,\n                err\n              );\n            }\n\n            throw err;\n          }\n        }\n      },\n      [props, setErrors, state.values]\n    );\n\n    const handleSubmit: React.FormEventHandler<HTMLFormElement> = async (e) => {\n      e.stopPropagation();\n      e.preventDefault();\n\n      if (!onSubmit) {\n        return;\n      }\n\n      dispatch({\n        type: 'SUBMIT_ATTEMPT',\n      });\n\n      try {\n        const { data, errors } = await validate();\n\n        if (errors) {\n          setErrors(errors);\n\n          throw new Error('Submission failed');\n        }\n\n        await onSubmit(data, {\n          setErrors,\n          setValues,\n          resetForm,\n        });\n\n        dispatch({\n          type: 'SUBMIT_SUCCESS',\n        });\n      } catch (err) {\n        dispatch({\n          type: 'SUBMIT_FAILURE',\n        });\n\n        if (err instanceof Error && err.message === 'Submission failed') {\n          return;\n        }\n      }\n    };\n\n    const modified = React.useMemo(\n      () => !isEqual(initialValues.current, state.values),\n      [state.values]\n    );\n\n    const handleChange: FormContextValue['onChange'] = useCallbackRef((eventOrPath, v) => {\n      if (typeof eventOrPath === 'string') {\n        dispatch({\n          type: 'SET_FIELD_VALUE',\n          payload: {\n            field: eventOrPath,\n            value: v,\n          },\n        });\n\n        return;\n      }\n\n      const target = eventOrPath.target || eventOrPath.currentTarget;\n\n      const { type, name, id, value, options, multiple } = target;\n\n      const field = name || id;\n\n      if (!field && process.env.NODE_ENV !== 'production') {\n        console.warn(\n          `\\`onChange\\` was called with an event, but you forgot to pass a \\`name\\` or \\`id'\\` attribute to your input. The field to update cannot be determined`\n        );\n      }\n\n      /**\n       * Because we handle any field from this function, we run through a series\n       * of checks to understand how to use the value.\n       */\n      let val;\n\n      if (/number|range/.test(type)) {\n        const parsed = parseFloat(value);\n        // If the value isn't a number for whatever reason, don't let it through because that will break the API.\n        val = isNaN(parsed) ? '' : parsed;\n      } else if (/checkbox/.test(type)) {\n        // Get & invert the current value of the checkbox.\n        val = !getIn(state.values, field);\n      } else if (options && multiple) {\n        // This will handle native select elements incl. ones with mulitple options.\n        val = Array.from<HTMLOptionElement>(options)\n          .filter((el) => el.selected)\n          .map((el) => el.value);\n      } else {\n        // NOTE: reset value to null so it failes required checks.\n        // The API only considers a required field invalid if the value is null|undefined, to differentiate from min 1\n        if (value === '') {\n          val = null;\n        } else {\n          val = value;\n        }\n      }\n\n      if (field) {\n        dispatch({\n          type: 'SET_FIELD_VALUE',\n          payload: {\n            field,\n            value: val,\n          },\n        });\n      }\n    });\n\n    const addFieldRow: FormContextValue['addFieldRow'] = React.useCallback(\n      (field, value, addAtIndex) => {\n        dispatch({\n          type: 'ADD_FIELD_ROW',\n          payload: {\n            field,\n            value,\n            addAtIndex,\n          },\n        });\n      },\n      []\n    );\n\n    const removeFieldRow: FormContextValue['removeFieldRow'] = React.useCallback(\n      (field, removeAtIndex) => {\n        dispatch({\n          type: 'REMOVE_FIELD_ROW',\n          payload: {\n            field,\n            removeAtIndex,\n          },\n        });\n      },\n      []\n    );\n\n    const moveFieldRow: FormContextValue['moveFieldRow'] = React.useCallback(\n      (field, fromIndex, toIndex) => {\n        dispatch({\n          type: 'MOVE_FIELD_ROW',\n          payload: {\n            field,\n            fromIndex,\n            toIndex,\n          },\n        });\n      },\n      []\n    );\n\n    const resetForm: FormContextValue['resetForm'] = React.useCallback(() => {\n      dispatch({\n        type: 'RESET_FORM',\n        payload: {\n          errors: {},\n          isSubmitting: false,\n          values: initialValues.current,\n        },\n      });\n    }, []);\n\n    const setSubmitting = React.useCallback((isSubmitting: boolean) => {\n      dispatch({ type: 'SET_ISSUBMITTING', payload: isSubmitting });\n    }, []);\n\n    const composedRefs = useComposedRefs(formRef, ref);\n\n    return (\n      <Box\n        tag=\"form\"\n        ref={composedRefs}\n        method={method}\n        noValidate\n        onSubmit={handleSubmit}\n        width={props.width}\n        height={props.height}\n      >\n        <FormProvider\n          disabled={disabled}\n          onChange={handleChange}\n          initialValues={initialValues.current}\n          modified={modified}\n          addFieldRow={addFieldRow}\n          moveFieldRow={moveFieldRow}\n          removeFieldRow={removeFieldRow}\n          resetForm={resetForm}\n          setErrors={setErrors}\n          setValues={setValues}\n          setSubmitting={setSubmitting}\n          validate={validate}\n          {...state}\n        >\n          {typeof props.children === 'function'\n            ? props.children({\n                modified,\n                disabled,\n                onChange: handleChange,\n                ...state,\n                setErrors,\n                resetForm,\n              })\n            : props.children}\n        </FormProvider>\n      </Box>\n    );\n  }\n) as <TFormValues extends FormValues>(\n  p: FormProps<TFormValues> & { ref?: React.Ref<HTMLFormElement> }\n) => React.ReactElement; // we've cast this because we need the generic to infer the type of the form values.\n\n/**\n * @internal\n * @description Checks if the error is a Yup validation error.\n */\nconst isErrorYupValidationError = (err: any): err is Yup.ValidationError =>\n  typeof err === 'object' &&\n  err !== null &&\n  'name' in err &&\n  typeof err.name === 'string' &&\n  err.name === 'ValidationError';\n\n/* -------------------------------------------------------------------------------------------------\n * getYupValidationErrors\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * @description handy utility to convert a yup validation error into a form\n * error object. To be used elsewhere.\n */\nconst getYupValidationErrors = (err: Yup.ValidationError): FormErrors => {\n  let errors: FormErrors = {};\n\n  if (err.inner) {\n    if (err.inner.length === 0) {\n      return setIn(errors, err.path!, err.message);\n    }\n    for (const error of err.inner) {\n      if (!getIn(errors, error.path!)) {\n        errors = setIn(errors, error.path!, error.message);\n      }\n    }\n  }\n\n  return errors;\n};\n\n/* -------------------------------------------------------------------------------------------------\n * reducer\n * -----------------------------------------------------------------------------------------------*/\n\ntype FormErrors<TFormValues extends FormValues = FormValues> = {\n  // is it a repeatable component or dynamic zone?\n  [Key in keyof TFormValues]?: TFormValues[Key] extends any[]\n    ? TFormValues[Key][number] extends object\n      ? FormErrors<TFormValues[Key][number]>[] | string | string[]\n      : string // this would let us support errors for the dynamic zone or repeatable component not the components within.\n    : TFormValues[Key] extends object // is it a regular component?\n      ? FormErrors<TFormValues[Key]> // handles nested components\n      : string | TranslationMessage; // otherwise its just a field or a translation message.\n};\n\ninterface FormState<TFormValues extends FormValues = FormValues> {\n  /**\n   * TODO: make this a better type explaining errors could be nested because it follows the same\n   * structure as the values.\n   */\n  errors: FormErrors<TFormValues>;\n  isSubmitting: boolean;\n  values: TFormValues;\n}\n\ntype FormActions<TFormValues extends FormValues = FormValues> =\n  | { type: 'SUBMIT_ATTEMPT' }\n  | { type: 'SUBMIT_FAILURE' }\n  | { type: 'SUBMIT_SUCCESS' }\n  | { type: 'SET_FIELD_VALUE'; payload: { field: string; value: any } }\n  | { type: 'ADD_FIELD_ROW'; payload: { field: string; value: any; addAtIndex?: number } }\n  | { type: 'REMOVE_FIELD_ROW'; payload: { field: string; removeAtIndex?: number } }\n  | { type: 'MOVE_FIELD_ROW'; payload: { field: string; fromIndex: number; toIndex: number } }\n  | { type: 'SET_ERRORS'; payload: FormErrors<TFormValues> }\n  | { type: 'SET_ISSUBMITTING'; payload: boolean }\n  | { type: 'SET_INITIAL_VALUES'; payload: TFormValues }\n  | { type: 'SET_VALUES'; payload: TFormValues }\n  | { type: 'RESET_FORM'; payload: FormState<TFormValues> };\n\nconst reducer = <TFormValues extends FormValues = FormValues>(\n  state: FormState<TFormValues>,\n  action: FormActions<TFormValues>\n) =>\n  produce(state, (draft) => {\n    switch (action.type) {\n      case 'SET_INITIAL_VALUES':\n        // @ts-expect-error – TODO: figure out why this fails ts.\n        draft.values = action.payload;\n        break;\n      case 'SET_VALUES':\n        // @ts-expect-error – TODO: figure out why this fails ts.\n        draft.values = action.payload;\n        break;\n      case 'SUBMIT_ATTEMPT':\n        draft.isSubmitting = true;\n        break;\n      case 'SUBMIT_FAILURE':\n        draft.isSubmitting = false;\n        break;\n      case 'SUBMIT_SUCCESS':\n        draft.isSubmitting = false;\n        break;\n      case 'SET_FIELD_VALUE':\n        draft.values = setIn(state.values, action.payload.field, action.payload.value);\n        break;\n      case 'ADD_FIELD_ROW': {\n        /**\n         * TODO: add check for if the field is an array?\n         */\n        const currentField = getIn(state.values, action.payload.field, []) as Array<any>;\n\n        let position = action.payload.addAtIndex;\n\n        if (position === undefined) {\n          position = currentField.length;\n        } else if (position < 0) {\n          position = 0;\n        }\n\n        const [key] = generateNKeysBetween(\n          position > 0 ? currentField.at(position - 1)?.__temp_key__ : null,\n          currentField.at(position)?.__temp_key__,\n          1\n        );\n\n        draft.values = setIn(\n          state.values,\n          action.payload.field,\n          currentField.toSpliced(position, 0, {\n            ...action.payload.value,\n            __temp_key__: key,\n          })\n        );\n\n        break;\n      }\n      case 'MOVE_FIELD_ROW': {\n        const { field, fromIndex, toIndex } = action.payload;\n        /**\n         * TODO: add check for if the field is an array?\n         */\n        const currentField = [...(getIn(state.values, field, []) as Array<any>)];\n        const currentRow = currentField[fromIndex];\n\n        const startKey =\n          fromIndex > toIndex\n            ? currentField[toIndex - 1]?.__temp_key__\n            : currentField[toIndex]?.__temp_key__;\n        const endKey =\n          fromIndex > toIndex\n            ? currentField[toIndex]?.__temp_key__\n            : currentField[toIndex + 1]?.__temp_key__;\n        const [newKey] = generateNKeysBetween(startKey, endKey, 1);\n\n        currentField.splice(fromIndex, 1);\n        currentField.splice(toIndex, 0, { ...currentRow, __temp_key__: newKey });\n\n        draft.values = setIn(state.values, field, currentField);\n\n        break;\n      }\n      case 'REMOVE_FIELD_ROW': {\n        /**\n         * TODO: add check for if the field is an array?\n         */\n        const currentField = getIn(state.values, action.payload.field, []) as Array<any>;\n\n        let position = action.payload.removeAtIndex;\n\n        if (position === undefined) {\n          position = currentField.length - 1;\n        } else if (position < 0) {\n          position = 0;\n        }\n\n        /**\n         * filter out empty values from the array, the setIn function only deletes the value\n         * when we pass undefined as opposed to \"removing\" it from said array.\n         */\n        const newValue = setIn(currentField, position.toString(), undefined).filter(\n          (val: unknown) => val\n        );\n\n        draft.values = setIn(\n          state.values,\n          action.payload.field,\n          newValue.length > 0 ? newValue : []\n        );\n\n        break;\n      }\n      case 'SET_ERRORS':\n        if (!isEqual(state.errors, action.payload)) {\n          // @ts-expect-error – TODO: figure out why this fails a TS check.\n          draft.errors = action.payload;\n        }\n        break;\n      case 'SET_ISSUBMITTING':\n        draft.isSubmitting = action.payload;\n        break;\n      case 'RESET_FORM':\n        // @ts-expect-error – TODO: figure out why this fails ts.\n        draft.values = action.payload.values;\n        // @ts-expect-error – TODO: figure out why this fails ts.\n        draft.errors = action.payload.errors;\n        draft.isSubmitting = action.payload.isSubmitting;\n        break;\n      default:\n        break;\n    }\n  });\n\n/* -------------------------------------------------------------------------------------------------\n * useField\n * -----------------------------------------------------------------------------------------------*/\ninterface FieldValue<TValue = any> {\n  error?: string;\n  initialValue: TValue;\n  onChange: (eventOrPath: React.ChangeEvent<any> | string, value?: TValue) => void;\n  value: TValue;\n  rawError?: any;\n}\n\nfunction useField<TValue = any>(path: string): FieldValue<TValue | undefined> {\n  const { formatMessage } = useIntl();\n\n  const initialValue = useForm(\n    'useField',\n    (state) => getIn(state.initialValues, path) as FieldValue<TValue>['initialValue']\n  );\n\n  const value = useForm(\n    'useField',\n    (state) => getIn(state.values, path) as FieldValue<TValue>['value']\n  );\n\n  const handleChange = useForm('useField', (state) => state.onChange);\n\n  const rawError = useForm('useField', (state) => getIn(state.errors, path));\n\n  const error = useForm('useField', (state) => {\n    const error = getIn(state.errors, path);\n\n    if (isErrorMessageDescriptor(error)) {\n      const { values, ...message } = error;\n      return formatMessage(message, values);\n    }\n\n    return error;\n  });\n\n  return {\n    initialValue,\n    /**\n     * Errors can be a string, or a MessageDescriptor, so we need to handle both cases.\n     * If it's anything else, we don't return it.\n     */\n    rawError,\n    error: isErrorMessageDescriptor(error)\n      ? formatMessage(\n          {\n            id: error.id,\n            defaultMessage: error.defaultMessage,\n          },\n          error.values\n        )\n      : typeof error === 'string'\n        ? error\n        : undefined,\n    onChange: handleChange,\n    value: value,\n  };\n}\n\nconst isErrorMessageDescriptor = (object?: object): object is TranslationMessage => {\n  return (\n    typeof object === 'object' &&\n    object !== null &&\n    !Array.isArray(object) &&\n    'id' in object &&\n    'defaultMessage' in object\n  );\n};\n\n/**\n * Props for the Blocker component.\n * @param onProceed Function to be called when the user confirms the action that triggered the blocker.\n * @param onCancel Function to be called when the user cancels the action that triggered the blocker.\n */\ninterface BlockerProps {\n  onProceed?: () => void;\n  onCancel?: () => void;\n}\n/* -------------------------------------------------------------------------------------------------\n * Blocker\n * -----------------------------------------------------------------------------------------------*/\nconst Blocker = ({ onProceed = () => {}, onCancel = () => {} }: BlockerProps) => {\n  const { formatMessage } = useIntl();\n  const modified = useForm('Blocker', (state) => state.modified);\n  const isSubmitting = useForm('Blocker', (state) => state.isSubmitting);\n\n  const blocker = useBlocker(({ currentLocation, nextLocation }) => {\n    return (\n      !isSubmitting &&\n      modified &&\n      (currentLocation.pathname !== nextLocation.pathname ||\n        currentLocation.search !== nextLocation.search)\n    );\n  });\n\n  if (blocker.state === 'blocked') {\n    const handleCancel = (isOpen: boolean) => {\n      if (!isOpen) {\n        onCancel();\n        blocker.reset();\n      }\n    };\n\n    return (\n      <Dialog.Root open onOpenChange={handleCancel}>\n        <Dialog.Content>\n          <Dialog.Header>\n            {formatMessage({\n              id: 'app.components.ConfirmDialog.title',\n              defaultMessage: 'Confirmation',\n            })}\n          </Dialog.Header>\n          <Dialog.Body icon={<WarningCircle width=\"24px\" height=\"24px\" fill=\"danger600\" />}>\n            {formatMessage({\n              id: 'global.prompt.unsaved',\n              defaultMessage: 'You have unsaved changes, are you sure you want to leave?',\n            })}\n          </Dialog.Body>\n          <Dialog.Footer>\n            <Dialog.Cancel>\n              <Button variant=\"tertiary\">\n                {formatMessage({\n                  id: 'app.components.Button.cancel',\n                  defaultMessage: 'Cancel',\n                })}\n              </Button>\n            </Dialog.Cancel>\n            <Button\n              onClick={() => {\n                onProceed();\n                blocker.proceed();\n              }}\n              variant=\"danger\"\n            >\n              {formatMessage({\n                id: 'app.components.Button.confirm',\n                defaultMessage: 'Confirm',\n              })}\n            </Button>\n          </Dialog.Footer>\n        </Dialog.Content>\n      </Dialog.Root>\n    );\n  }\n\n  return null;\n};\n\nexport { Form, Blocker, useField, useForm, getYupValidationErrors };\nexport type {\n  FormErrors,\n  FormHelpers,\n  FormProps,\n  FormValues,\n  FormContextValue,\n  FormState,\n  FieldValue,\n  InputProps,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIO,IAAM,iBACX;AAaF,SAAS,SAAS,GAAG,GAAG,QAAQ;AAC9B,QAAM,OAAO,OAAO,CAAC;AACrB,MAAI,KAAK,QAAQ,KAAK,GAAG;AACvB,UAAM,IAAI,MAAM,IAAI,SAAS,CAAC;AAAA,EAChC;AACA,MAAI,EAAE,MAAM,EAAE,MAAM,QAAS,KAAK,EAAE,MAAM,EAAE,MAAM,MAAO;AACvD,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AACA,MAAI,GAAG;AAIL,QAAI,IAAI;AACR,YAAQ,EAAE,CAAC,KAAK,UAAU,EAAE,CAAC,GAAG;AAC9B;AAAA,IACF;AACA,QAAI,IAAI,GAAG;AACT,aAAO,EAAE,MAAM,GAAG,CAAC,IAAI,SAAS,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,MAAM;AAAA,IAChE;AAAA,EACF;AAEA,QAAM,SAAS,IAAI,OAAO,QAAQ,EAAE,CAAC,CAAC,IAAI;AAC1C,QAAM,SAAS,KAAK,OAAO,OAAO,QAAQ,EAAE,CAAC,CAAC,IAAI,OAAO;AACzD,MAAI,SAAS,SAAS,GAAG;AACvB,UAAM,WAAW,KAAK,MAAM,OAAO,SAAS,OAAO;AACnD,WAAO,OAAO,QAAQ;AAAA,EACxB,OAAO;AAEL,QAAI,KAAK,EAAE,SAAS,GAAG;AACrB,aAAO,EAAE,MAAM,GAAG,CAAC;AAAA,IACrB,OAAO;AAOL,aAAO,OAAO,MAAM,IAAI,SAAS,EAAE,MAAM,CAAC,GAAG,MAAM,MAAM;AAAA,IAC3D;AAAA,EACF;AACF;AAOA,SAAS,gBAAgB,KAAK;AAC5B,MAAI,IAAI,WAAW,iBAAiB,IAAI,CAAC,CAAC,GAAG;AAC3C,UAAM,IAAI,MAAM,wCAAwC,GAAG;AAAA,EAC7D;AACF;AAOA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,QAAQ,OAAO,QAAQ,KAAK;AAC9B,WAAO,KAAK,WAAW,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI;AAAA,EAClD,WAAW,QAAQ,OAAO,QAAQ,KAAK;AACrC,WAAO,IAAI,WAAW,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI;AAAA,EAClD,OAAO;AACL,UAAM,IAAI,MAAM,6BAA6B,IAAI;AAAA,EACnD;AACF;AAOA,SAAS,eAAe,KAAK;AAC3B,QAAM,oBAAoB,iBAAiB,IAAI,CAAC,CAAC;AACjD,MAAI,oBAAoB,IAAI,QAAQ;AAClC,UAAM,IAAI,MAAM,wBAAwB,GAAG;AAAA,EAC7C;AACA,SAAO,IAAI,MAAM,GAAG,iBAAiB;AACvC;AAQA,SAAS,iBAAiB,KAAK,QAAQ;AACrC,MAAI,QAAQ,MAAM,OAAO,CAAC,EAAE,OAAO,EAAE,GAAG;AACtC,UAAM,IAAI,MAAM,wBAAwB,GAAG;AAAA,EAC7C;AAIA,QAAM,IAAI,eAAe,GAAG;AAC5B,QAAM,IAAI,IAAI,MAAM,EAAE,MAAM;AAC5B,MAAI,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC,GAAG;AAC7B,UAAM,IAAI,MAAM,wBAAwB,GAAG;AAAA,EAC7C;AACF;AAQA,SAAS,iBAAiB,GAAG,QAAQ;AACnC,kBAAgB,CAAC;AACjB,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI,EAAE,MAAM,EAAE;AAClC,MAAI,QAAQ;AACZ,WAAS,IAAI,KAAK,SAAS,GAAG,SAAS,KAAK,GAAG,KAAK;AAClD,UAAM,IAAI,OAAO,QAAQ,KAAK,CAAC,CAAC,IAAI;AACpC,QAAI,MAAM,OAAO,QAAQ;AACvB,WAAK,CAAC,IAAI,OAAO,CAAC;AAAA,IACpB,OAAO;AACL,WAAK,CAAC,IAAI,OAAO,CAAC;AAClB,cAAQ;AAAA,IACV;AAAA,EACF;AACA,MAAI,OAAO;AACT,QAAI,SAAS,KAAK;AAChB,aAAO,MAAM,OAAO,CAAC;AAAA,IACvB;AACA,QAAI,SAAS,KAAK;AAChB,aAAO;AAAA,IACT;AACA,UAAM,IAAI,OAAO,aAAa,KAAK,WAAW,CAAC,IAAI,CAAC;AACpD,QAAI,IAAI,KAAK;AACX,WAAK,KAAK,OAAO,CAAC,CAAC;AAAA,IACrB,OAAO;AACL,WAAK,IAAI;AAAA,IACX;AACA,WAAO,IAAI,KAAK,KAAK,EAAE;AAAA,EACzB,OAAO;AACL,WAAO,OAAO,KAAK,KAAK,EAAE;AAAA,EAC5B;AACF;AASA,SAAS,iBAAiB,GAAG,QAAQ;AACnC,kBAAgB,CAAC;AACjB,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI,EAAE,MAAM,EAAE;AAClC,MAAI,SAAS;AACb,WAAS,IAAI,KAAK,SAAS,GAAG,UAAU,KAAK,GAAG,KAAK;AACnD,UAAM,IAAI,OAAO,QAAQ,KAAK,CAAC,CAAC,IAAI;AACpC,QAAI,MAAM,IAAI;AACZ,WAAK,CAAC,IAAI,OAAO,MAAM,EAAE;AAAA,IAC3B,OAAO;AACL,WAAK,CAAC,IAAI,OAAO,CAAC;AAClB,eAAS;AAAA,IACX;AAAA,EACF;AACA,MAAI,QAAQ;AACV,QAAI,SAAS,KAAK;AAChB,aAAO,MAAM,OAAO,MAAM,EAAE;AAAA,IAC9B;AACA,QAAI,SAAS,KAAK;AAChB,aAAO;AAAA,IACT;AACA,UAAM,IAAI,OAAO,aAAa,KAAK,WAAW,CAAC,IAAI,CAAC;AACpD,QAAI,IAAI,KAAK;AACX,WAAK,KAAK,OAAO,MAAM,EAAE,CAAC;AAAA,IAC5B,OAAO;AACL,WAAK,IAAI;AAAA,IACX;AACA,WAAO,IAAI,KAAK,KAAK,EAAE;AAAA,EACzB,OAAO;AACL,WAAO,OAAO,KAAK,KAAK,EAAE;AAAA,EAC5B;AACF;AAaO,SAAS,mBAAmB,GAAG,GAAG,SAAS,gBAAgB;AAChE,MAAI,KAAK,MAAM;AACb,qBAAiB,GAAG,MAAM;AAAA,EAC5B;AACA,MAAI,KAAK,MAAM;AACb,qBAAiB,GAAG,MAAM;AAAA,EAC5B;AACA,MAAI,KAAK,QAAQ,KAAK,QAAQ,KAAK,GAAG;AACpC,UAAM,IAAI,MAAM,IAAI,SAAS,CAAC;AAAA,EAChC;AACA,MAAI,KAAK,MAAM;AACb,QAAI,KAAK,MAAM;AACb,aAAO,MAAM,OAAO,CAAC;AAAA,IACvB;AAEA,UAAMA,MAAK,eAAe,CAAC;AAC3B,UAAMC,MAAK,EAAE,MAAMD,IAAG,MAAM;AAC5B,QAAIA,QAAO,MAAM,OAAO,CAAC,EAAE,OAAO,EAAE,GAAG;AACrC,aAAOA,MAAK,SAAS,IAAIC,KAAI,MAAM;AAAA,IACrC;AACA,QAAID,MAAK,GAAG;AACV,aAAOA;AAAA,IACT;AACA,UAAM,MAAM,iBAAiBA,KAAI,MAAM;AACvC,QAAI,OAAO,MAAM;AACf,YAAM,IAAI,MAAM,2BAA2B;AAAA,IAC7C;AACA,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,MAAM;AACb,UAAME,MAAK,eAAe,CAAC;AAC3B,UAAMC,MAAK,EAAE,MAAMD,IAAG,MAAM;AAC5B,UAAME,KAAI,iBAAiBF,KAAI,MAAM;AACrC,WAAOE,MAAK,OAAOF,MAAK,SAASC,KAAI,MAAM,MAAM,IAAIC;AAAA,EACvD;AAEA,QAAM,KAAK,eAAe,CAAC;AAC3B,QAAM,KAAK,EAAE,MAAM,GAAG,MAAM;AAC5B,QAAM,KAAK,eAAe,CAAC;AAC3B,QAAM,KAAK,EAAE,MAAM,GAAG,MAAM;AAC5B,MAAI,OAAO,IAAI;AACb,WAAO,KAAK,SAAS,IAAI,IAAI,MAAM;AAAA,EACrC;AACA,QAAM,IAAI,iBAAiB,IAAI,MAAM;AACrC,MAAI,KAAK,MAAM;AACb,UAAM,IAAI,MAAM,2BAA2B;AAAA,EAC7C;AACA,MAAI,IAAI,GAAG;AACT,WAAO;AAAA,EACT;AACA,SAAO,KAAK,SAAS,IAAI,MAAM,MAAM;AACvC;AAgBO,SAAS,qBAAqB,GAAG,GAAG,GAAG,SAAS,gBAAgB;AACrE,MAAI,MAAM,GAAG;AACX,WAAO,CAAC;AAAA,EACV;AACA,MAAI,MAAM,GAAG;AACX,WAAO,CAAC,mBAAmB,GAAG,GAAG,MAAM,CAAC;AAAA,EAC1C;AACA,MAAI,KAAK,MAAM;AACb,QAAIC,KAAI,mBAAmB,GAAG,GAAG,MAAM;AACvC,UAAM,SAAS,CAACA,EAAC;AACjB,aAAS,IAAI,GAAG,IAAI,IAAI,GAAG,KAAK;AAC9B,MAAAA,KAAI,mBAAmBA,IAAG,GAAG,MAAM;AACnC,aAAO,KAAKA,EAAC;AAAA,IACf;AACA,WAAO;AAAA,EACT;AACA,MAAI,KAAK,MAAM;AACb,QAAIA,KAAI,mBAAmB,GAAG,GAAG,MAAM;AACvC,UAAM,SAAS,CAACA,EAAC;AACjB,aAAS,IAAI,GAAG,IAAI,IAAI,GAAG,KAAK;AAC9B,MAAAA,KAAI,mBAAmB,GAAGA,IAAG,MAAM;AACnC,aAAO,KAAKA,EAAC;AAAA,IACf;AACA,WAAO,QAAQ;AACf,WAAO;AAAA,EACT;AACA,QAAM,MAAM,KAAK,MAAM,IAAI,CAAC;AAC5B,QAAM,IAAI,mBAAmB,GAAG,GAAG,MAAM;AACzC,SAAO;AAAA,IACL,GAAG,qBAAqB,GAAG,GAAG,KAAK,MAAM;AAAA,IACzC;AAAA,IACA,GAAG,qBAAqB,GAAG,GAAG,IAAI,MAAM,GAAG,MAAM;AAAA,EACnD;AACF;;;;;;ACxOA,IAAMC,UACJ;AAEF,IAAM,CAACC,cAAcC,OAAQ,IAAGC,cAAgC,QAAQ;EACtEC,UAAU;EACVC,QAAQ,CAAA;EACRC,eAAe,CAAA;EACfC,cAAc;EACdC,UAAU;EACVC,aAAa,MAAA;AACX,UAAM,IAAIC,MAAMV,OAAAA;EAClB;EACAW,cAAc,MAAA;AACZ,UAAM,IAAID,MAAMV,OAAAA;EAClB;EACAY,UAAU,MAAA;AACR,UAAM,IAAIF,MAAMV,OAAAA;EAClB;EACAa,gBAAgB,MAAA;AACd,UAAM,IAAIH,MAAMV,OAAAA;EAClB;EACAc,WAAW,MAAA;AACT,UAAM,IAAIJ,MAAMV,OAAAA;EAClB;EACAe,WAAW,MAAA;AACT,UAAM,IAAIL,MAAMV,OAAAA;EAClB;EACAgB,WAAW,MAAA;AACT,UAAM,IAAIN,MAAMV,OAAAA;EAClB;EACAiB,eAAe,MAAA;AACb,UAAM,IAAIP,MAAMV,OAAAA;EAClB;EACAkB,UAAU,YAAA;AACR,UAAM,IAAIR,MAAMV,OAAAA;EAClB;EACAmB,QAAQ,CAAA;AACV,CAAA;AAyCC,IACKC,OAAaC,iBACjB,CAAC,EAAEjB,WAAW,OAAOkB,QAAQC,UAAUC,eAAe,GAAGC,MAAAA,GAASC,QAAAA;AAChE,QAAMC,UAAgBC,aAAwB,IAAA;AAC9C,QAAMtB,gBAAsBsB,aAAOH,MAAMnB,iBAAiB,CAAA,CAAC;AAC3D,QAAM,CAACuB,OAAOC,QAAAA,IAAkBC,iBAAWC,SAAS;IAClD3B,QAAQmB,iBAAiB,CAAA;IACzBjB,cAAc;IACdY,QAAQM,MAAMnB,iBAAiB,CAAA;EACjC,CAAA;AAEA2B,EAAMC,gBAAU,MAAA;AAId,QAAI,KAACC,eAAAA,SAAQ7B,cAAc8B,SAASX,MAAMnB,aAAa,GAAG;AACxDA,oBAAc8B,UAAUX,MAAMnB,iBAAiB,CAAA;AAE/CwB,eAAS;QACPO,MAAM;QACNC,SAASb,MAAMnB,iBAAiB,CAAA;MAClC,CAAA;IACF;KACC;IAACmB,MAAMnB;EAAc,CAAA;AAExB,QAAMS,YAAkBwB,kBAAY,CAAClC,WAAAA;AACnCyB,aAAS;MACPO,MAAM;MACNC,SAASjC;IACX,CAAA;EACF,GAAG,CAAA,CAAE;AAEL,QAAMW,YAAkBuB,kBAAY,CAACpB,WAAAA;AACnCW,aAAS;MACPO,MAAM;MACNC,SAASnB;IACX,CAAA;EACF,GAAG,CAAA,CAAE;AAELc,EAAMC,gBAAU,MAAA;AACd,QAAIM,OAAOC,KAAKZ,MAAMxB,MAAM,EAAEqC,WAAW,EAAG;AAM5C,UAAMhB,OAAMiB,WAAW,MAAA;AACrB,YAAM,CAACC,UAAW,IAAGjB,QAAQS,QAAQS,iBAAiB,2BAAA;AAEtD,UAAID,YAAY;AACd,cAAME,UAAUF,WAAWG,aAAa,IAAA;AACxC,cAAMC,qBAAqBrB,QAAQS,QAAQa,cACzC,sBAAsBH,OAAQ,IAAG;AAGnC,YAAIE,sBAAsBA,8BAA8BE,aAAa;AACnEF,6BAAmBG,MAAK;QAC1B;MACF;IACF,CAAA;AAEA,WAAO,MAAMC,aAAa1B,IAAAA;KACzB;IAACG,MAAMxB;EAAO,CAAA;AAKjB,QAAMa,WAAiBqB,kBACrB,OAAOc,kBAA2B,MAAMC,UAAkC,CAAA,MAAE;AAC1EvC,cAAU,CAAA,CAAC;AAEX,QAAI,CAACU,MAAM8B,oBAAoB,CAAC9B,MAAMP,UAAU;AAC9C,aAAO;QAAEsC,MAAM3B,MAAMV;MAAO;IAC9B;AAEA,QAAI;AACF,UAAIqC;AACJ,UAAI/B,MAAM8B,kBAAkB;AAC1BC,eAAO,MAAM/B,MAAM8B,iBAAiBrC,SAASW,MAAMV,QAAQ;UAAEsC,YAAY;QAAM,CAAA;iBACtEhC,MAAMP,UAAU;AACzBsC,eAAO,MAAM/B,MAAMP,SAASW,MAAMV,QAAQmC,OAAAA;aACrC;AACL,cAAM,IAAI5C,MAAM,oDAAA;MAClB;AAEA,aAAO;QAAE8C;MAAK;IAChB,SAASE,KAAK;AACZ,UAAIC,0BAA0BD,GAAM,GAAA;AAClC,cAAMrD,SAASuD,uBAAuBF,GAAAA;AAEtC,YAAIL,iBAAiB;AACnBtC,oBAAUV,MAAAA;QACZ;AAEA,eAAO;UAAEA;QAAO;aACX;AAEL,YAAIwD,MAAuC;AACzCC,kBAAQC,KACN,yFACAL,GAAAA;QAEJ;AAEA,cAAMA;MACR;IACF;KAEF;IAACjC;IAAOV;IAAWc,MAAMV;EAAO,CAAA;AAGlC,QAAM6C,eAAwD,OAAOC,MAAAA;AACnEA,MAAEC,gBAAe;AACjBD,MAAEE,eAAc;AAEhB,QAAI,CAAC5C,UAAU;AACb;IACF;AAEAO,aAAS;MACPO,MAAM;IACR,CAAA;AAEA,QAAI;AACF,YAAM,EAAEmB,MAAMnD,OAAM,IAAK,MAAMa,SAAAA;AAE/B,UAAIb,QAAQ;AACVU,kBAAUV,MAAAA;AAEV,cAAM,IAAIK,MAAM,mBAAA;MAClB;AAEA,YAAMa,SAASiC,MAAM;QACnBzC;QACAC;QACAF;MACF,CAAA;AAEAgB,eAAS;QACPO,MAAM;MACR,CAAA;IACF,SAASqB,KAAK;AACZ5B,eAAS;QACPO,MAAM;MACR,CAAA;AAEA,UAAIqB,eAAehD,SAASgD,IAAIU,YAAY,qBAAqB;AAC/D;MACF;IACF;EACF;AAEA,QAAM5D,WAAiB6D,cACrB,MAAM,KAAClC,eAAAA,SAAQ7B,cAAc8B,SAASP,MAAMV,MAAM,GAClD;IAACU,MAAMV;EAAO,CAAA;AAGhB,QAAMmD,eAA6CC,eAAe,CAACC,aAAaC,MAAAA;AAC9E,QAAI,OAAOD,gBAAgB,UAAU;AACnC1C,eAAS;QACPO,MAAM;QACNC,SAAS;UACPoC,OAAOF;UACPG,OAAOF;QACT;MACF,CAAA;AAEA;IACF;AAEA,UAAMG,SAASJ,YAAYI,UAAUJ,YAAYK;AAEjD,UAAM,EAAExC,MAAMyC,MAAMC,IAAIJ,OAAOrB,SAAS0B,SAAQ,IAAKJ;AAErD,UAAMF,QAAQI,QAAQC;AAEtB,QAAI,CAACL,SAASb,MAAuC;AACnDC,cAAQC,KACN,uJAAuJ;IAE3J;AAMA,QAAIkB;AAEJ,QAAI,eAAeC,KAAK7C,IAAO,GAAA;AAC7B,YAAM8C,SAASC,WAAWT,KAAAA;AAE1BM,YAAMI,MAAMF,MAAAA,IAAU,KAAKA;IAC7B,WAAW,WAAWD,KAAK7C,IAAO,GAAA;AAEhC4C,YAAM,CAACK,MAAMzD,MAAMV,QAAQuD,KAAAA;eAClBpB,WAAW0B,UAAU;AAE9BC,YAAMM,MAAMC,KAAwBlC,OACjCmC,EAAAA,OAAO,CAACC,OAAOA,GAAGC,QAAQ,EAC1BC,IAAI,CAACF,OAAOA,GAAGf,KAAK;WAClB;AAGL,UAAIA,UAAU,IAAI;AAChBM,cAAM;aACD;AACLA,cAAMN;MACR;IACF;AAEA,QAAID,OAAO;AACT5C,eAAS;QACPO,MAAM;QACNC,SAAS;UACPoC;UACAC,OAAOM;QACT;MACF,CAAA;IACF;EACF,CAAA;AAEA,QAAMxE,cAAqD8B,kBACzD,CAACmC,OAAOC,OAAOkB,eAAAA;AACb/D,aAAS;MACPO,MAAM;MACNC,SAAS;QACPoC;QACAC;QACAkB;MACF;IACF,CAAA;EACF,GACA,CAAA,CAAE;AAGJ,QAAMhF,iBAA2D0B,kBAC/D,CAACmC,OAAOoB,kBAAAA;AACNhE,aAAS;MACPO,MAAM;MACNC,SAAS;QACPoC;QACAoB;MACF;IACF,CAAA;EACF,GACA,CAAA,CAAE;AAGJ,QAAMnF,eAAuD4B,kBAC3D,CAACmC,OAAOqB,WAAWC,YAAAA;AACjBlE,aAAS;MACPO,MAAM;MACNC,SAAS;QACPoC;QACAqB;QACAC;MACF;IACF,CAAA;EACF,GACA,CAAA,CAAE;AAGJ,QAAMlF,YAAiDyB,kBAAY,MAAA;AACjET,aAAS;MACPO,MAAM;MACNC,SAAS;QACPjC,QAAQ,CAAA;QACRE,cAAc;QACdY,QAAQb,cAAc8B;MACxB;IACF,CAAA;EACF,GAAG,CAAA,CAAE;AAEL,QAAMnB,gBAAsBsB,kBAAY,CAAChC,iBAAAA;AACvCuB,aAAS;MAAEO,MAAM;MAAoBC,SAAS/B;IAAa,CAAA;EAC7D,GAAG,CAAA,CAAE;AAEL,QAAM0F,eAAeC,gBAAgBvE,SAASD,GAAAA;AAE9C,aACEyE,wBAACC,KAAAA;IACCC,KAAI;IACJ3E,KAAKuE;IACL3E;IACAgF,YAAU;IACV/E,UAAUyC;IACVuC,OAAO9E,MAAM8E;IACbC,QAAQ/E,MAAM+E;IAEd,cAAAL,wBAAClG,cAAAA;MACCG;MACAQ,UAAU0D;MACVhE,eAAeA,cAAc8B;MAC7B5B;MACAC;MACAE;MACAE;MACAC;MACAC;MACAC;MACAC;MACAC;MACC,GAAGW;MAEH,UAAA,OAAOJ,MAAMgF,aAAa,aACvBhF,MAAMgF,SAAS;QACbjG;QACAJ;QACAQ,UAAU0D;QACV,GAAGzC;QACHd;QACAD;MACF,CAAA,IACAW,MAAMgF;;;AAIlB,CAAA;AASF,IAAM9C,4BAA4B,CAACD,QACjC,OAAOA,QAAQ,YACfA,QAAQ,QACR,UAAUA,OACV,OAAOA,IAAIoB,SAAS,YACpBpB,IAAIoB,SAAS;AAUf,IAAMlB,yBAAyB,CAACF,QAAAA;AAC9B,MAAIrD,SAAqB,CAAA;AAEzB,MAAIqD,IAAIgD,OAAO;AACb,QAAIhD,IAAIgD,MAAMhE,WAAW,GAAG;AAC1B,aAAOiE,MAAMtG,QAAQqD,IAAIkD,MAAOlD,IAAIU,OAAO;IAC7C;AACA,eAAWyC,SAASnD,IAAIgD,OAAO;AAC7B,UAAI,CAACpB,MAAMjF,QAAQwG,MAAMD,IAAI,GAAI;AAC/BvG,iBAASsG,MAAMtG,QAAQwG,MAAMD,MAAOC,MAAMzC,OAAO;MACnD;IACF;EACF;AAEA,SAAO/D;AACT;AAyCA,IAAM2B,UAAU,CACdH,OACAiF,WAEAC,GAAQlF,OAAO,CAACmF,UAAAA;;AACd,UAAQF,OAAOzE,MAAI;IACjB,KAAK;AAEH2E,YAAM7F,SAAS2F,OAAOxE;AACtB;IACF,KAAK;AAEH0E,YAAM7F,SAAS2F,OAAOxE;AACtB;IACF,KAAK;AACH0E,YAAMzG,eAAe;AACrB;IACF,KAAK;AACHyG,YAAMzG,eAAe;AACrB;IACF,KAAK;AACHyG,YAAMzG,eAAe;AACrB;IACF,KAAK;AACHyG,YAAM7F,SAASwF,MAAM9E,MAAMV,QAAQ2F,OAAOxE,QAAQoC,OAAOoC,OAAOxE,QAAQqC,KAAK;AAC7E;IACF,KAAK,iBAAiB;AAIpB,YAAMsC,eAAe3B,MAAMzD,MAAMV,QAAQ2F,OAAOxE,QAAQoC,OAAO,CAAA,CAAE;AAEjE,UAAIwC,WAAWJ,OAAOxE,QAAQuD;AAE9B,UAAIqB,aAAaC,QAAW;AAC1BD,mBAAWD,aAAavE;iBACfwE,WAAW,GAAG;AACvBA,mBAAW;MACb;AAEA,YAAM,CAACE,GAAI,IAAGC,qBACZH,WAAW,KAAID,kBAAaK,GAAGJ,WAAW,CAAA,MAA3BD,mBAA+BM,eAAe,OAC7DN,kBAAaK,GAAGJ,QAAAA,MAAhBD,mBAA2BM,cAC3B,CAAA;AAGFP,YAAM7F,SAASwF,MACb9E,MAAMV,QACN2F,OAAOxE,QAAQoC,OACfuC,aAAaO,UAAUN,UAAU,GAAG;QAClC,GAAGJ,OAAOxE,QAAQqC;QAClB4C,cAAcH;MAChB,CAAA,CAAA;AAGF;IACF;IACA,KAAK,kBAAkB;AACrB,YAAM,EAAE1C,OAAOqB,WAAWC,QAAO,IAAKc,OAAOxE;AAI7C,YAAM2E,eAAe;QAAK3B,GAAAA,MAAMzD,MAAMV,QAAQuD,OAAO,CAAA,CAAE;MAAiB;AACxE,YAAM+C,aAAaR,aAAalB,SAAU;AAE1C,YAAM2B,WACJ3B,YAAYC,WACRiB,kBAAajB,UAAU,CAAE,MAAzBiB,mBAA2BM,gBAC3BN,kBAAajB,OAAAA,MAAbiB,mBAAuBM;AAC7B,YAAMI,SACJ5B,YAAYC,WACRiB,kBAAajB,OAAAA,MAAbiB,mBAAuBM,gBACvBN,kBAAajB,UAAU,CAAA,MAAvBiB,mBAA2BM;AACjC,YAAM,CAACK,MAAAA,IAAUP,qBAAqBK,UAAUC,QAAQ,CAAA;AAExDV,mBAAaY,OAAO9B,WAAW,CAAA;AAC/BkB,mBAAaY,OAAO7B,SAAS,GAAG;QAAE,GAAGyB;QAAYF,cAAcK;MAAO,CAAA;AAEtEZ,YAAM7F,SAASwF,MAAM9E,MAAMV,QAAQuD,OAAOuC,YAAAA;AAE1C;IACF;IACA,KAAK,oBAAoB;AAIvB,YAAMA,eAAe3B,MAAMzD,MAAMV,QAAQ2F,OAAOxE,QAAQoC,OAAO,CAAA,CAAE;AAEjE,UAAIwC,WAAWJ,OAAOxE,QAAQwD;AAE9B,UAAIoB,aAAaC,QAAW;AAC1BD,mBAAWD,aAAavE,SAAS;iBACxBwE,WAAW,GAAG;AACvBA,mBAAW;MACb;AAMA,YAAMY,WAAWnB,MAAMM,cAAcC,SAASa,SAAQ,GAAIZ,MAAW1B,EAAAA,OACnE,CAACR,QAAiBA,GAAAA;AAGpB+B,YAAM7F,SAASwF,MACb9E,MAAMV,QACN2F,OAAOxE,QAAQoC,OACfoD,SAASpF,SAAS,IAAIoF,WAAW,CAAA,CAAE;AAGrC;IACF;IACA,KAAK;AACH,UAAI,KAAC3F,eAAAA,SAAQN,MAAMxB,QAAQyG,OAAOxE,OAAO,GAAG;AAE1C0E,cAAM3G,SAASyG,OAAOxE;MACxB;AACA;IACF,KAAK;AACH0E,YAAMzG,eAAeuG,OAAOxE;AAC5B;IACF,KAAK;AAEH0E,YAAM7F,SAAS2F,OAAOxE,QAAQnB;AAE9B6F,YAAM3G,SAASyG,OAAOxE,QAAQjC;AAC9B2G,YAAMzG,eAAeuG,OAAOxE,QAAQ/B;AACpC;EAGJ;AACF,CAAA;AAaF,SAASyH,SAAuBpB,MAAY;AAC1C,QAAM,EAAEqB,cAAa,IAAKC,QAAAA;AAE1B,QAAMC,eAAejI,QACnB,YACA,CAAC2B,UAAUyD,MAAMzD,MAAMvB,eAAesG,IAAAA,CAAAA;AAGxC,QAAMjC,QAAQzE,QACZ,YACA,CAAC2B,UAAUyD,MAAMzD,MAAMV,QAAQyF,IAAAA,CAAAA;AAGjC,QAAMtC,eAAepE,QAAQ,YAAY,CAAC2B,UAAUA,MAAMjB,QAAQ;AAElE,QAAMwH,WAAWlI,QAAQ,YAAY,CAAC2B,UAAUyD,MAAMzD,MAAMxB,QAAQuG,IAAAA,CAAAA;AAEpE,QAAMC,QAAQ3G,QAAQ,YAAY,CAAC2B,UAAAA;AACjC,UAAMgF,SAAQvB,MAAMzD,MAAMxB,QAAQuG,IAAAA;AAElC,QAAIyB,yBAAyBxB,MAAQ,GAAA;AACnC,YAAM,EAAE1F,QAAQ,GAAGiD,QAAAA,IAAYyC;AAC/B,aAAOoB,cAAc7D,SAASjD,MAAAA;IAChC;AAEA,WAAO0F;EACT,CAAA;AAEA,SAAO;IACLsB;;;;;IAKAC;IACAvB,OAAOwB,yBAAyBxB,KAAAA,IAC5BoB,cACE;MACElD,IAAI8B,MAAM9B;MACVuD,gBAAgBzB,MAAMyB;IACxB,GACAzB,MAAM1F,MAAM,IAEd,OAAO0F,UAAU,WACfA,QACAM;IACNvG,UAAU0D;IACVK;EACF;AACF;AAEA,IAAM0D,2BAA2B,CAACE,WAAAA;AAChC,SACE,OAAOA,WAAW,YAClBA,WAAW,QACX,CAAChD,MAAMiD,QAAQD,MAAAA,KACf,QAAQA,UACR,oBAAoBA;AAExB;AAcA,IAAME,UAAU,CAAC,EAAEC,YAAY,MAAO;AAAA,GAAGC,WAAW,MAAO;AAAA,EAAC,MAAgB;AAC1E,QAAM,EAAEV,cAAa,IAAKC,QAAAA;AAC1B,QAAM1H,WAAWN,QAAQ,WAAW,CAAC2B,UAAUA,MAAMrB,QAAQ;AAC7D,QAAMD,eAAeL,QAAQ,WAAW,CAAC2B,UAAUA,MAAMtB,YAAY;AAErE,QAAMqI,UAAUC,WAAW,CAAC,EAAEC,iBAAiBC,aAAY,MAAE;AAC3D,WACE,CAACxI,gBACDC,aACCsI,gBAAgBE,aAAaD,aAAaC,YACzCF,gBAAgBG,WAAWF,aAAaE;EAE9C,CAAA;AAEA,MAAIL,QAAQ/G,UAAU,WAAW;AAC/B,UAAMqH,eAAe,CAACC,WAAAA;AACpB,UAAI,CAACA,QAAQ;AACXR,iBAAAA;AACAC,gBAAQQ,MAAK;MACf;IACF;AAEA,eACEjD,wBAACkD,OAAOC,MAAI;MAACC,MAAI;MAACC,cAAcN;oBAC9BO,yBAACJ,OAAOK,SAAO;;cACbvD,wBAACkD,OAAOM,QAAM;sBACX1B,cAAc;cACblD,IAAI;cACJuD,gBAAgB;YAClB,CAAA;;cAEFnC,wBAACkD,OAAOO,MAAI;YAACC,UAAM1D,wBAAC2D,cAAAA;cAAcvD,OAAM;cAAOC,QAAO;cAAOuD,MAAK;;sBAC/D9B,cAAc;cACblD,IAAI;cACJuD,gBAAgB;YAClB,CAAA;;cAEFmB,yBAACJ,OAAOW,QAAM;;kBACZ7D,wBAACkD,OAAOY,QAAM;gBACZ,cAAA9D,wBAAC+D,QAAAA;kBAAOC,SAAQ;4BACblC,cAAc;oBACblD,IAAI;oBACJuD,gBAAgB;kBAClB,CAAA;;;kBAGJnC,wBAAC+D,QAAAA;gBACCE,SAAS,MAAA;AACP1B,4BAAAA;AACAE,0BAAQyB,QAAO;gBACjB;gBACAF,SAAQ;0BAEPlC,cAAc;kBACblD,IAAI;kBACJuD,gBAAgB;gBAClB,CAAA;;;;;;;EAMZ;AAEA,SAAO;AACT;", "names": ["ib", "fb", "ia", "fa", "i", "c", "ERR_MSG", "FormProvider", "useForm", "createContext", "disabled", "errors", "initialValues", "isSubmitting", "modified", "addFieldRow", "Error", "moveFieldRow", "onChange", "removeFieldRow", "resetForm", "setErrors", "set<PERSON><PERSON><PERSON>", "setSubmitting", "validate", "values", "Form", "forwardRef", "method", "onSubmit", "initialErrors", "props", "ref", "formRef", "useRef", "state", "dispatch", "useReducer", "reducer", "React", "useEffect", "isEqual", "current", "type", "payload", "useCallback", "Object", "keys", "length", "setTimeout", "firstError", "querySelectorAll", "errorId", "getAttribute", "formElementInError", "querySelector", "HTMLElement", "focus", "clearTimeout", "shouldSetErrors", "options", "validationSchema", "data", "abort<PERSON><PERSON><PERSON>", "err", "isErrorYupValidationError", "getYupValidationErrors", "process", "console", "warn", "handleSubmit", "e", "stopPropagation", "preventDefault", "message", "useMemo", "handleChange", "useCallbackRef", "eventOr<PERSON>ath", "v", "field", "value", "target", "currentTarget", "name", "id", "multiple", "val", "test", "parsed", "parseFloat", "isNaN", "getIn", "Array", "from", "filter", "el", "selected", "map", "addAtIndex", "removeAtIndex", "fromIndex", "toIndex", "composedRefs", "useComposedRefs", "_jsx", "Box", "tag", "noValidate", "width", "height", "children", "inner", "setIn", "path", "error", "action", "produce", "draft", "current<PERSON><PERSON>", "position", "undefined", "key", "generateNKeysBetween", "at", "__temp_key__", "toSpliced", "currentRow", "startKey", "<PERSON><PERSON><PERSON>", "new<PERSON>ey", "splice", "newValue", "toString", "useField", "formatMessage", "useIntl", "initialValue", "rawError", "isErrorMessageDescriptor", "defaultMessage", "object", "isArray", "Blocker", "onProceed", "onCancel", "blocker", "useBlocker", "currentLocation", "nextLocation", "pathname", "search", "handleCancel", "isOpen", "reset", "Dialog", "Root", "open", "onOpenChange", "_jsxs", "Content", "Header", "Body", "icon", "WarningCircle", "fill", "Footer", "Cancel", "<PERSON><PERSON>", "variant", "onClick", "proceed"]}