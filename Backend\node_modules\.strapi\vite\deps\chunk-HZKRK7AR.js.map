{"version": 3, "sources": ["../../../@strapi/admin/admin/src/hooks/useEnterprise.ts"], "sourcesContent": ["import * as React from 'react';\n\nimport { useCallbackRef } from '@strapi/design-system';\n\nfunction isEnterprise() {\n  return window.strapi.isEE;\n}\n\nexport interface UseEnterpriseOptions<TCEData, TEEData, TDefaultValue, TCombinedValue> {\n  defaultValue?: TDefaultValue;\n  combine?: (ceData: TCEData, eeData: TEEData) => TCombinedValue;\n  enabled?: boolean;\n}\n\ntype UseEnterpriseReturn<TCEData, TEEData, TDefaultValue, TCombinedValue> =\n  TDefaultValue extends null\n    ? TCEData | TEEData | TCombinedValue | null\n    : TCEData | TEEData | TCombinedValue | TDefaultValue;\n\nexport const useEnterprise = <\n  TCEData,\n  TEEData = TCEData,\n  TCombinedValue = TEEData,\n  TDefaultValue = TCEData,\n>(\n  ceData: TCEData,\n  eeCallback: () => Promise<TEEData>,\n  opts: UseEnterpriseOptions<TCEData, TEEData, TDefaultValue, TCombinedValue> = {}\n): UseEnterpriseReturn<TCEData, TEEData, TDefaultValue, TCombinedValue> => {\n  const { defaultValue = null, combine = (_ceData, eeData) => eeData, enabled = true } = opts;\n  const eeCallbackRef = useCallbackRef(eeCallback);\n  const combineCallbackRef = useCallbackRef(combine);\n\n  // We have to use a nested object here, because functions (e.g. Components)\n  // can not be stored as value directly\n  const [{ data }, setData] = React.useState<{\n    data: TCEData | TEEData | TDefaultValue | TCombinedValue | null;\n  }>({\n    data: isEnterprise() && enabled ? defaultValue : ceData,\n  });\n\n  React.useEffect(() => {\n    async function importEE() {\n      const eeData = await eeCallbackRef();\n      const combinedValue = combineCallbackRef(ceData, eeData);\n\n      setData({ data: combinedValue ? combinedValue : eeData });\n    }\n\n    if (isEnterprise() && enabled) {\n      importEE();\n    }\n  }, [ceData, eeCallbackRef, combineCallbackRef, enabled]);\n\n  // @ts-expect-error – the hook type assertion works in practice. But seems to have issues here...\n  return data;\n};\n"], "mappings": ";;;;;;;;;;;;AAIA,SAASA,eAAAA;AACP,SAAOC,OAAOC,OAAOC;AACvB;IAaaC,gBAAgB,CAM3BC,QACAC,YACAC,OAA8E,CAAA,MAAE;AAEhF,QAAM,EAAEC,eAAe,MAAMC,UAAU,CAACC,SAASC,WAAWA,QAAQC,UAAU,KAAI,IAAKL;AACvF,QAAMM,gBAAgBC,eAAeR,UAAAA;AACrC,QAAMS,qBAAqBD,eAAeL,OAAAA;AAI1C,QAAM,CAAC,EAAEO,KAAI,GAAIC,OAAQ,IAASC,eAE/B;IACDF,MAAMhB,aAAAA,KAAkBY,UAAUJ,eAAeH;EACnD,CAAA;AAEAc,EAAMC,gBAAU,MAAA;AACd,mBAAeC,WAAAA;AACb,YAAMV,SAAS,MAAME,cAAAA;AACrB,YAAMS,gBAAgBP,mBAAmBV,QAAQM,MAAAA;AAEjDM,cAAQ;QAAED,MAAMM,gBAAgBA,gBAAgBX;MAAO,CAAA;IACzD;AAEA,QAAIX,aAAAA,KAAkBY,SAAS;AAC7BS,eAAAA;IACF;KACC;IAAChB;IAAQQ;IAAeE;IAAoBH;EAAQ,CAAA;AAGvD,SAAOI;AACT;", "names": ["isEnterprise", "window", "strapi", "isEE", "useEnterprise", "ceData", "ee<PERSON><PERSON>back", "opts", "defaultValue", "combine", "_ceData", "eeData", "enabled", "eeCallbackRef", "useCallbackRef", "combineCallbackRef", "data", "setData", "useState", "React", "useEffect", "importEE", "combinedValue"]}