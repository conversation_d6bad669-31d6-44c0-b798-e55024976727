{"version": 3, "sources": ["../../../@strapi/admin/admin/src/features/Configuration.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { createContext } from '@radix-ui/react-context';\nimport { useIntl } from 'react-intl';\n\nimport { UpdateProjectSettings } from '../../../shared/contracts/admin';\nimport { Page } from '../components/PageHelpers';\nimport { useTypedSelector } from '../core/store/hooks';\nimport { useAPIErrorHandler } from '../hooks/useAPIErrorHandler';\nimport { useRBAC } from '../hooks/useRBAC';\nimport {\n  ConfigurationLogo,\n  useInitQuery,\n  useProjectSettingsQuery,\n  useUpdateProjectSettingsMutation,\n} from '../services/admin';\n\nimport { useAuth } from './Auth';\nimport { useNotification } from './Notifications';\nimport { useTracking } from './Tracking';\n\nimport type { StrapiApp } from '../StrapiApp';\n\n/* -------------------------------------------------------------------------------------------------\n * Configuration Context\n * -----------------------------------------------------------------------------------------------*/\n\ninterface UpdateProjectSettingsBody {\n  authLogo:\n    | ((UpdateProjectSettings.Request['body']['authLogo'] | ConfigurationLogo['custom']) & {\n        rawFile?: File;\n      })\n    | null;\n  menuLogo:\n    | ((UpdateProjectSettings.Request['body']['menuLogo'] | ConfigurationLogo['custom']) & {\n        rawFile?: File;\n      })\n    | null;\n}\n\ninterface ConfigurationContextValue {\n  logos: {\n    auth: ConfigurationLogo;\n    menu: ConfigurationLogo;\n  };\n  showReleaseNotification: boolean;\n  updateProjectSettings: (body: UpdateProjectSettingsBody) => Promise<void>;\n}\n\nconst [ConfigurationContextProvider, useConfiguration] =\n  createContext<ConfigurationContextValue>('ConfigurationContext');\n\n/* -------------------------------------------------------------------------------------------------\n * ConfigurationProvider\n * -----------------------------------------------------------------------------------------------*/\n\ninterface ConfigurationProviderProps {\n  children: React.ReactNode;\n  defaultAuthLogo: StrapiApp['configurations']['authLogo'];\n  defaultMenuLogo: StrapiApp['configurations']['menuLogo'];\n  showReleaseNotification?: boolean;\n}\n\nconst ConfigurationProvider = ({\n  children,\n  defaultAuthLogo,\n  defaultMenuLogo,\n  showReleaseNotification = false,\n}: ConfigurationProviderProps) => {\n  const { trackUsage } = useTracking();\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.['project-settings']\n  );\n  const token = useAuth('ConfigurationProvider', (state) => state.token);\n\n  const {\n    allowedActions: { canRead },\n  } = useRBAC(permissions);\n\n  const {\n    data: { authLogo: customAuthLogo, menuLogo: customMenuLogo } = {},\n    error,\n    isLoading,\n  } = useInitQuery();\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({ id: 'app.containers.App.notification.error.init' }),\n      });\n    }\n  }, [error, formatMessage, toggleNotification]);\n\n  const { data, isSuccess } = useProjectSettingsQuery(undefined, {\n    skip: !token || !canRead,\n  });\n\n  const [updateProjectSettingsMutation] = useUpdateProjectSettingsMutation();\n\n  const updateProjectSettings = React.useCallback(\n    async (body: UpdateProjectSettingsBody) => {\n      const formData = new FormData();\n\n      /**\n       * We either only send files or we send null values.\n       * Null removes the logo. If you don't want to effect\n       * an existing logo, don't send anything.\n       */\n      Object.entries(body).forEach(([key, value]) => {\n        if (value?.rawFile) {\n          formData.append(key, value.rawFile);\n        } else if (value === null) {\n          formData.append(key, JSON.stringify(value));\n        }\n      });\n\n      const res = await updateProjectSettingsMutation(formData);\n\n      if ('data' in res) {\n        const updatedMenuLogo = !!res.data.menuLogo && !!body.menuLogo?.rawFile;\n        const updatedAuthLogo = !!res.data.authLogo && !!body.authLogo?.rawFile;\n\n        if (updatedMenuLogo) {\n          trackUsage('didChangeLogo', {\n            logo: 'menu',\n          });\n        }\n\n        if (updatedAuthLogo) {\n          trackUsage('didChangeLogo', {\n            logo: 'auth',\n          });\n        }\n\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({ id: 'app', defaultMessage: 'Saved' }),\n        });\n      } else {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(res.error),\n        });\n      }\n    },\n    [formatAPIError, formatMessage, toggleNotification, trackUsage, updateProjectSettingsMutation]\n  );\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <ConfigurationContextProvider\n      showReleaseNotification={showReleaseNotification}\n      logos={{\n        menu: {\n          custom: isSuccess\n            ? data?.menuLogo\n            : {\n                url: customMenuLogo ?? '',\n              },\n          default: defaultMenuLogo,\n        },\n        auth: {\n          custom: isSuccess\n            ? data?.authLogo\n            : {\n                url: customAuthLogo ?? '',\n              },\n          default: defaultAuthLogo,\n        },\n      }}\n      updateProjectSettings={updateProjectSettings}\n    >\n      {children}\n    </ConfigurationContextProvider>\n  );\n};\n\nexport {\n  ConfigurationContextProvider as _internalConfigurationContextProvider,\n  ConfigurationProvider,\n  useConfiguration,\n};\nexport type {\n  ConfigurationProviderProps,\n  ConfigurationContextValue,\n  ConfigurationLogo,\n  UpdateProjectSettingsBody,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDA,IAAM,CAACA,8BAA8BC,gBAAiB,IACpDC,0CAAyC,sBAAA;AAarCC,IAAAA,wBAAwB,CAAC,EAC7BC,UACAC,iBACAC,iBACAC,0BAA0B,MAAK,MACJ;AAC3B,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,yBAAyBC,eAAc,IAAKC,mBAAAA;AACpD,QAAMC,cAAcC,iBAClB,CAACC,UAAAA;;AAAUA,uBAAMC,UAAUH,YAAYI,aAA5BF,mBAAuC;GAAmB;AAEvE,QAAMG,QAAQC,QAAQ,yBAAyB,CAACJ,UAAUA,MAAMG,KAAK;AAErE,QAAM,EACJE,gBAAgB,EAAEC,QAAO,EAAE,IACzBC,QAAQT,WAAAA;AAEZ,QAAM,EACJU,MAAM,EAAEC,UAAUC,gBAAgBC,UAAUC,eAAc,IAAK,CAAA,GAC/DC,OACAC,UAAS,IACPC,aAAAA;AAEJC,EAAMC,gBAAU,MAAA;AACd,QAAIJ,OAAO;AACTpB,yBAAmB;QACjByB,MAAM;QACNC,SAAS5B,cAAc;UAAE6B,IAAI;QAA6C,CAAA;MAC5E,CAAA;IACF;KACC;IAACP;IAAOtB;IAAeE;EAAmB,CAAA;AAE7C,QAAM,EAAEe,MAAMa,UAAS,IAAKC,wBAAwBC,QAAW;IAC7DC,MAAM,CAACrB,SAAS,CAACG;EACnB,CAAA;AAEA,QAAM,CAACmB,6BAAAA,IAAiCC,iCAAAA;AAExC,QAAMC,wBAA8BC,kBAClC,OAAOC,SAAAA;;AACL,UAAMC,WAAW,IAAIC,SAAAA;AAOrBC,WAAOC,QAAQJ,IAAAA,EAAMK,QAAQ,CAAC,CAACC,KAAKC,KAAM,MAAA;AACxC,UAAIA,+BAAOC,SAAS;AAClBP,iBAASQ,OAAOH,KAAKC,MAAMC,OAAO;iBACzBD,UAAU,MAAM;AACzBN,iBAASQ,OAAOH,KAAKI,KAAKC,UAAUJ,KAAAA,CAAAA;MACtC;IACF,CAAA;AAEA,UAAMK,MAAM,MAAMhB,8BAA8BK,QAAAA;AAEhD,QAAI,UAAUW,KAAK;AACjB,YAAMC,kBAAkB,CAAC,CAACD,IAAIjC,KAAKG,YAAY,CAAC,GAACkB,UAAKlB,aAALkB,mBAAeQ;AAChE,YAAMM,kBAAkB,CAAC,CAACF,IAAIjC,KAAKC,YAAY,CAAC,GAACoB,UAAKpB,aAALoB,mBAAeQ;AAEhE,UAAIK,iBAAiB;AACnBrD,mBAAW,iBAAiB;UAC1BuD,MAAM;QACR,CAAA;MACF;AAEA,UAAID,iBAAiB;AACnBtD,mBAAW,iBAAiB;UAC1BuD,MAAM;QACR,CAAA;MACF;AAEAnD,yBAAmB;QACjByB,MAAM;QACNC,SAAS5B,cAAc;UAAE6B,IAAI;UAAOyB,gBAAgB;QAAQ,CAAA;MAC9D,CAAA;WACK;AACLpD,yBAAmB;QACjByB,MAAM;QACNC,SAASvB,eAAe6C,IAAI5B,KAAK;MACnC,CAAA;IACF;KAEF;IAACjB;IAAgBL;IAAeE;IAAoBJ;IAAYoC;EAA8B,CAAA;AAGhG,MAAIX,WAAW;AACb,eAAOgC,wBAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,aACEF,wBAACjE,8BAAAA;IACCO;IACA6D,OAAO;MACLC,MAAM;QACJC,QAAQ9B,YACJb,6BAAMG,WACN;UACEyC,KAAKxC,kBAAkB;QACzB;QACJyC,SAASlE;MACX;MACAmE,MAAM;QACJH,QAAQ9B,YACJb,6BAAMC,WACN;UACE2C,KAAK1C,kBAAkB;QACzB;QACJ2C,SAASnE;MACX;IACF;IACAyC;IAEC1C;;AAGP;", "names": ["ConfigurationContextProvider", "useConfiguration", "createContext", "ConfigurationProvider", "children", "defaultAuthLogo", "defaultMenuLogo", "showReleaseNotification", "trackUsage", "useTracking", "formatMessage", "useIntl", "toggleNotification", "useNotification", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "permissions", "useTypedSelector", "state", "admin_app", "settings", "token", "useAuth", "allowedActions", "canRead", "useRBAC", "data", "auth<PERSON><PERSON>", "customAuthLogo", "menuLogo", "customMenuLogo", "error", "isLoading", "useInitQuery", "React", "useEffect", "type", "message", "id", "isSuccess", "useProjectSettingsQuery", "undefined", "skip", "updateProjectSettingsMutation", "useUpdateProjectSettingsMutation", "updateProjectSettings", "useCallback", "body", "formData", "FormData", "Object", "entries", "for<PERSON>ach", "key", "value", "rawFile", "append", "JSON", "stringify", "res", "updatedMenuLogo", "updatedAuthLogo", "logo", "defaultMessage", "_jsx", "Page", "Loading", "logos", "menu", "custom", "url", "default", "auth"]}