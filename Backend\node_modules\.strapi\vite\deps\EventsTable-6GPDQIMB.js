import {
  Events
} from "./chunk-3I64ZOER.js";
import "./chunk-L5JCPKMP.js";
import "./chunk-VYSYYPOB.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-76QM3EFM.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import "./chunk-7GC3Y62Q.js";
import "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import "./chunk-ACIMPXWY.js";
import "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/ee/admin/src/pages/SettingsPage/pages/Webhooks/components/EventsTable.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var eeTables = {
  "review-workflows": {
    "review-workflows": [
      "review-workflows.updateEntryStage"
    ]
  },
  releases: {
    releases: [
      "releases.publish"
    ]
  }
};
var getHeaders = (table) => {
  switch (table) {
    case "review-workflows":
      return () => [
        {
          id: "review-workflows.updateEntryStage",
          defaultMessage: "Stage Change"
        }
      ];
    case "releases":
      return () => [
        {
          id: "releases.publish",
          defaultMessage: "Publish"
        }
      ];
  }
};
var EventsTableEE = () => {
  return (0, import_jsx_runtime.jsxs)(Events.Root, {
    children: [
      (0, import_jsx_runtime.jsx)(Events.Headers, {}),
      (0, import_jsx_runtime.jsx)(Events.Body, {}),
      Object.keys(eeTables).map((table) => (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, {
        children: [
          (0, import_jsx_runtime.jsx)(Events.Headers, {
            getHeaders: getHeaders(table)
          }),
          (0, import_jsx_runtime.jsx)(Events.Body, {
            providedEvents: eeTables[table]
          })
        ]
      }))
    ]
  });
};
export {
  EventsTableEE
};
//# sourceMappingURL=EventsTable-6GPDQIMB.js.map
