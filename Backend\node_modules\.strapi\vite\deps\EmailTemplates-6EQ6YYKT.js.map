{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/admin/src/pages/EmailTemplates/utils/schema.js", "../../../@strapi/plugin-users-permissions/admin/src/pages/EmailTemplates/components/EmailForm.jsx", "../../../@strapi/plugin-users-permissions/admin/src/pages/EmailTemplates/components/EmailTable.jsx", "../../../@strapi/plugin-users-permissions/admin/src/pages/EmailTemplates/index.jsx"], "sourcesContent": ["import { translatedErrors } from '@strapi/strapi/admin';\nimport * as yup from 'yup';\n\nconst schema = yup.object().shape({\n  options: yup\n    .object()\n    .shape({\n      from: yup\n        .object()\n        .shape({\n          name: yup.string().required({\n            id: translatedErrors.required.id,\n            defaultMessage: 'This field is required',\n          }),\n          email: yup.string().email(translatedErrors.email).required({\n            id: translatedErrors.required.id,\n            defaultMessage: 'This field is required',\n          }),\n        })\n        .required(),\n      response_email: yup.string().email(translatedErrors.email),\n      object: yup.string().required({\n        id: translatedErrors.required.id,\n        defaultMessage: 'This field is required',\n      }),\n      message: yup.string().required({\n        id: translatedErrors.required.id,\n        defaultMessage: 'This field is required',\n      }),\n    })\n    .required(translatedErrors.required.id),\n});\n\nexport default schema;\n", "import * as React from 'react';\n\nimport { Button, Grid, Modal, Breadcrumbs, Crumb, VisuallyHidden } from '@strapi/design-system';\nimport { Form, InputRenderer } from '@strapi/strapi/admin';\nimport PropTypes from 'prop-types';\nimport { useIntl } from 'react-intl';\n\nimport { getTrad } from '../../../utils';\nimport schema from '../utils/schema';\n\nconst EmailForm = ({ template = {}, onToggle, open, onSubmit }) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Modal.Root open={open} onOpenChange={onToggle}>\n      <Modal.Content>\n        <Modal.Header>\n          <Breadcrumbs\n            label={`${formatMessage({\n              id: getTrad('PopUpForm.header.edit.email-templates'),\n              defaultMessage: 'Edit email template',\n            })}, ${\n              template.display\n                ? formatMessage({\n                    id: getTrad(template.display),\n                    defaultMessage: template.display,\n                  })\n                : ''\n            }`}\n          >\n            <Crumb>\n              {formatMessage({\n                id: getTrad('PopUpForm.header.edit.email-templates'),\n                defaultMessage: 'Edit email template',\n              })}\n            </Crumb>\n            <Crumb isCurrent>\n              {template.display\n                ? formatMessage({ id: getTrad(template.display), defaultMessage: template.display })\n                : ''}\n            </Crumb>\n          </Breadcrumbs>\n          <VisuallyHidden>\n            <Modal.Title>\n              {`${formatMessage({\n                id: getTrad('PopUpForm.header.edit.email-templates'),\n                defaultMessage: 'Edit email template',\n              })}, ${template.display ? formatMessage({ id: getTrad(template.display), defaultMessage: template.display }) : ''}`}\n            </Modal.Title>\n          </VisuallyHidden>\n        </Modal.Header>\n        <Form onSubmit={onSubmit} initialValues={template} validationSchema={schema}>\n          {({ isSubmitting }) => {\n            return (\n              <>\n                <Modal.Body>\n                  <Grid.Root gap={5}>\n                    {[\n                      {\n                        label: formatMessage({\n                          id: getTrad('PopUpForm.Email.options.from.name.label'),\n                          defaultMessage: 'Shipper name',\n                        }),\n                        name: 'options.from.name',\n                        size: 6,\n                        type: 'string',\n                      },\n                      {\n                        label: formatMessage({\n                          id: getTrad('PopUpForm.Email.options.from.email.label'),\n                          defaultMessage: 'Shipper email',\n                        }),\n                        name: 'options.from.email',\n                        size: 6,\n                        type: 'string',\n                      },\n                      {\n                        label: formatMessage({\n                          id: getTrad('PopUpForm.Email.options.response_email.label'),\n                          defaultMessage: 'Response email',\n                        }),\n                        name: 'options.response_email',\n                        size: 6,\n                        type: 'string',\n                      },\n                      {\n                        label: formatMessage({\n                          id: getTrad('PopUpForm.Email.options.object.label'),\n                          defaultMessage: 'Subject',\n                        }),\n                        name: 'options.object',\n                        size: 6,\n                        type: 'string',\n                      },\n                      {\n                        label: formatMessage({\n                          id: getTrad('PopUpForm.Email.options.message.label'),\n                          defaultMessage: 'Message',\n                        }),\n                        name: 'options.message',\n                        size: 12,\n                        type: 'text',\n                      },\n                    ].map(({ size, ...field }) => (\n                      <Grid.Item\n                        key={field.name}\n                        col={size}\n                        direction=\"column\"\n                        alignItems=\"stretch\"\n                      >\n                        <InputRenderer {...field} />\n                      </Grid.Item>\n                    ))}\n                  </Grid.Root>\n                </Modal.Body>\n                <Modal.Footer>\n                  <Modal.Close>\n                    <Button variant=\"tertiary\">Cancel</Button>\n                  </Modal.Close>\n                  <Button loading={isSubmitting} type=\"submit\">\n                    Finish\n                  </Button>\n                </Modal.Footer>\n              </>\n            );\n          }}\n        </Form>\n      </Modal.Content>\n    </Modal.Root>\n  );\n};\n\nEmailForm.defaultProps = {\n  template: {},\n};\n\nEmailForm.propTypes = {\n  template: PropTypes.shape({\n    display: PropTypes.string,\n    icon: PropTypes.string,\n    options: PropTypes.shape({\n      from: PropTypes.shape({\n        name: PropTypes.string,\n        email: PropTypes.string,\n      }),\n      message: PropTypes.string,\n      object: PropTypes.string,\n      response_email: PropTypes.string,\n    }),\n  }),\n  open: PropTypes.bool.isRequired,\n  onSubmit: PropTypes.func.isRequired,\n  onToggle: PropTypes.func.isRequired,\n};\n\nexport default EmailForm;\n", "import * as React from 'react';\n\nimport {\n  <PERSON><PERSON><PERSON>utton,\n  Table,\n  Tbody,\n  Td,\n  Th,\n  Thead,\n  Tr,\n  Typography,\n  VisuallyHidden,\n  Box,\n} from '@strapi/design-system';\nimport { Check, Pencil, ArrowClockwise as Refresh } from '@strapi/icons';\nimport PropTypes from 'prop-types';\nimport { useIntl } from 'react-intl';\n\nimport { getTrad } from '../../../utils';\n\nconst EmailTable = ({ canUpdate, onEditClick }) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Table colCount={3} rowCount={3}>\n      <Thead>\n        <Tr>\n          <Th width=\"1%\">\n            <VisuallyHidden>\n              {formatMessage({\n                id: getTrad('Email.template.table.icon.label'),\n                defaultMessage: 'icon',\n              })}\n            </VisuallyHidden>\n          </Th>\n          <Th>\n            <Typography variant=\"sigma\" textColor=\"neutral600\">\n              {formatMessage({\n                id: getTrad('Email.template.table.name.label'),\n                defaultMessage: 'name',\n              })}\n            </Typography>\n          </Th>\n          <Th width=\"1%\">\n            <VisuallyHidden>\n              {formatMessage({\n                id: getTrad('Email.template.table.action.label'),\n                defaultMessage: 'action',\n              })}\n            </VisuallyHidden>\n          </Th>\n        </Tr>\n      </Thead>\n      <Tbody>\n        <Tr cursor=\"pointer\" onClick={() => onEditClick('reset_password')}>\n          <Td>\n            <Box width=\"3.2rem\" height=\"3.2rem\" padding=\"0.8rem\">\n              <Refresh\n                aria-label={formatMessage({\n                  id: 'global.reset-password',\n                  defaultMessage: 'Reset password',\n                })}\n              />\n            </Box>\n          </Td>\n          <Td>\n            <Typography>\n              {formatMessage({\n                id: 'global.reset-password',\n                defaultMessage: 'Reset password',\n              })}\n            </Typography>\n          </Td>\n          <Td onClick={(e) => e.stopPropagation()}>\n            <IconButton\n              onClick={() => onEditClick('reset_password')}\n              label={formatMessage({\n                id: getTrad('Email.template.form.edit.label'),\n                defaultMessage: 'Edit a template',\n              })}\n              variant=\"ghost\"\n              disabled={!canUpdate}\n            >\n              <Pencil />\n            </IconButton>\n          </Td>\n        </Tr>\n        <Tr cursor=\"pointer\" onClick={() => onEditClick('email_confirmation')}>\n          <Td>\n            <Box width=\"3.2rem\" height=\"3.2rem\" padding=\"0.8rem\">\n              <Check\n                aria-label={formatMessage({\n                  id: getTrad('Email.template.email_confirmation'),\n                  defaultMessage: 'Email address confirmation',\n                })}\n              />\n            </Box>\n          </Td>\n          <Td>\n            <Typography>\n              {formatMessage({\n                id: getTrad('Email.template.email_confirmation'),\n                defaultMessage: 'Email address confirmation',\n              })}\n            </Typography>\n          </Td>\n          <Td onClick={(e) => e.stopPropagation()}>\n            <IconButton\n              onClick={() => onEditClick('email_confirmation')}\n              label={formatMessage({\n                id: getTrad('Email.template.form.edit.label'),\n                defaultMessage: 'Edit a template',\n              })}\n              variant=\"ghost\"\n              disabled={!canUpdate}\n            >\n              <Pencil />\n            </IconButton>\n          </Td>\n        </Tr>\n      </Tbody>\n    </Table>\n  );\n};\n\nEmailTable.propTypes = {\n  canUpdate: PropTypes.bool.isRequired,\n  onEditClick: PropTypes.func.isRequired,\n};\n\nexport default EmailTable;\n", "import * as React from 'react';\n\nimport { useTracking } from '@strapi/admin/strapi-admin';\nimport { useNotifyAT } from '@strapi/design-system';\nimport {\n  Page,\n  useAPIErrorHandler,\n  useNotification,\n  useFetchClient,\n  useRBAC,\n  Layouts,\n} from '@strapi/strapi/admin';\nimport { useIntl } from 'react-intl';\nimport { useMutation, useQuery, useQueryClient } from 'react-query';\n\nimport { PERMISSIONS } from '../../constants';\nimport { getTrad } from '../../utils';\n\nimport EmailForm from './components/EmailForm';\nimport EmailTable from './components/EmailTable';\n\nconst ProtectedEmailTemplatesPage = () => (\n  <Page.Protect permissions={PERMISSIONS.readEmailTemplates}>\n    <EmailTemplatesPage />\n  </Page.Protect>\n);\nconst EmailTemplatesPage = () => {\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n  const { notifyStatus } = useNotifyAT();\n  const { toggleNotification } = useNotification();\n  const queryClient = useQueryClient();\n  const { get, put } = useFetchClient();\n  const { formatAPIError } = useAPIErrorHandler();\n\n  const [isModalOpen, setIsModalOpen] = React.useState(false);\n  const [templateToEdit, setTemplateToEdit] = React.useState(null);\n\n  const {\n    isLoading: isLoadingForPermissions,\n    allowedActions: { canUpdate },\n  } = useRBAC({ update: PERMISSIONS.updateEmailTemplates });\n\n  const { isLoading: isLoadingData, data } = useQuery(\n    ['users-permissions', 'email-templates'],\n    async () => {\n      const { data } = await get('/users-permissions/email-templates');\n\n      return data;\n    },\n    {\n      onSuccess() {\n        notifyStatus(\n          formatMessage({\n            id: getTrad('Email.template.data.loaded'),\n            defaultMessage: 'Email templates has been loaded',\n          })\n        );\n      },\n      onError(error) {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(error),\n        });\n      },\n    }\n  );\n\n  const isLoading = isLoadingForPermissions || isLoadingData;\n\n  const handleToggle = () => {\n    setIsModalOpen((prev) => !prev);\n  };\n\n  const handleEditClick = (template) => {\n    setTemplateToEdit(template);\n    handleToggle();\n  };\n\n  const submitMutation = useMutation(\n    (body) => put('/users-permissions/email-templates', { 'email-templates': body }),\n    {\n      async onSuccess() {\n        await queryClient.invalidateQueries(['users-permissions', 'email-templates']);\n\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({ id: 'notification.success.saved', defaultMessage: 'Saved' }),\n        });\n\n        trackUsage('didEditEmailTemplates');\n\n        handleToggle();\n      },\n      onError(error) {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(error),\n        });\n      },\n      refetchActive: true,\n    }\n  );\n\n  const handleSubmit = (body) => {\n    trackUsage('willEditEmailTemplates');\n\n    const editedTemplates = { ...data, [templateToEdit]: body };\n    submitMutation.mutate(editedTemplates);\n  };\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Page.Main aria-busy={submitMutation.isLoading}>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: formatMessage({\n              id: getTrad('HeaderNav.link.emailTemplates'),\n              defaultMessage: 'Email templates',\n            }),\n          }\n        )}\n      </Page.Title>\n      <Layouts.Header\n        title={formatMessage({\n          id: getTrad('HeaderNav.link.emailTemplates'),\n          defaultMessage: 'Email templates',\n        })}\n      />\n      <Layouts.Content>\n        <EmailTable onEditClick={handleEditClick} canUpdate={canUpdate} />\n        <EmailForm\n          template={data[templateToEdit]}\n          onToggle={handleToggle}\n          open={isModalOpen}\n          onSubmit={handleSubmit}\n        />\n      </Layouts.Content>\n    </Page.Main>\n  );\n};\n\nexport { ProtectedEmailTemplatesPage, EmailTemplatesPage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAMA,SAAaC,QAAM,EAAGC,MAAM;EAChCC,SACGF,QAAM,EACNC,MAAM;IACLE,MACGH,QAAM,EACNC,MAAM;MACLG,MAAUC,OAAM,EAAGC,SAAS;QAC1BC,IAAIC,YAAiBF,SAASC;QAC9BE,gBAAgB;MAClB,CAAA;MACAC,OAAWL,OAAM,EAAGK,MAAMF,YAAiBE,KAAK,EAAEJ,SAAS;QACzDC,IAAIC,YAAiBF,SAASC;QAC9BE,gBAAgB;MAClB,CAAA;IACF,CAAA,EACCH,SAAQ;IACXK,gBAAoBN,OAAM,EAAGK,MAAMF,YAAiBE,KAAK;IACzDV,QAAYK,OAAM,EAAGC,SAAS;MAC5BC,IAAIC,YAAiBF,SAASC;MAC9BE,gBAAgB;IAClB,CAAA;IACAG,SAAaP,OAAM,EAAGC,SAAS;MAC7BC,IAAIC,YAAiBF,SAASC;MAC9BE,gBAAgB;IAClB,CAAA;EACF,CAAA,EACCH,SAASE,YAAiBF,SAASC,EAAE;AAC1C,CAAA;;;ACrBA,IAAMM,YAAY,CAAC,EAAEC,WAAW,CAAA,GAAIC,UAAUC,MAAMC,SAAQ,MAAE;AAC5D,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEC,wBAACC,MAAMC,MAAI;IAACN;IAAYO,cAAcR;kBACpCS,yBAACH,MAAMI,SAAO;;YACZD,yBAACH,MAAMK,QAAM;;gBACXF,yBAACG,aAAAA;cACCC,OAAO,GAAGV,cAAc;gBACtBW,IAAIC,QAAQ,uCAAA;gBACZC,gBAAgB;cAClB,CAAA,CAAA,KACEjB,SAASkB,UACLd,cAAc;gBACZW,IAAIC,QAAQhB,SAASkB,OAAO;gBAC5BD,gBAAgBjB,SAASkB;cAC3B,CAAA,IACA,EAAA;;oBAGNZ,wBAACa,OAAAA;4BACEf,cAAc;oBACbW,IAAIC,QAAQ,uCAAA;oBACZC,gBAAgB;kBAClB,CAAA;;oBAEFX,wBAACa,OAAAA;kBAAMC,WAAS;4BACbpB,SAASkB,UACNd,cAAc;oBAAEW,IAAIC,QAAQhB,SAASkB,OAAO;oBAAGD,gBAAgBjB,SAASkB;mBACxE,IAAA;;;;gBAGRZ,wBAACe,gBAAAA;4BACCf,wBAACC,MAAMe,OAAK;gBACT,UAAA,GAAGlB,cAAc;kBAChBW,IAAIC,QAAQ,uCAAA;kBACZC,gBAAgB;gBAClB,CAAA,CAAA,KAAOjB,SAASkB,UAAUd,cAAc;kBAAEW,IAAIC,QAAQhB,SAASkB,OAAO;kBAAGD,gBAAgBjB,SAASkB;gBAAQ,CAAA,IAAK,EAAA;;;;;YAIrHZ,wBAACiB,MAAAA;UAAKpB;UAAoBqB,eAAexB;UAAUyB,kBAAkBC;oBAClE,CAAC,EAAEC,aAAY,MAAE;AAChB,uBACEjB,yBAAAkB,6BAAA;;oBACEtB,wBAACC,MAAMsB,MAAI;gCACTvB,wBAACwB,KAAKtB,MAAI;oBAACuB,KAAK;oBACb,UAAA;sBACC;wBACEjB,OAAOV,cAAc;0BACnBW,IAAIC,QAAQ,yCAAA;0BACZC,gBAAgB;wBAClB,CAAA;wBACAe,MAAM;wBACNC,MAAM;wBACNC,MAAM;sBACR;sBACA;wBACEpB,OAAOV,cAAc;0BACnBW,IAAIC,QAAQ,0CAAA;0BACZC,gBAAgB;wBAClB,CAAA;wBACAe,MAAM;wBACNC,MAAM;wBACNC,MAAM;sBACR;sBACA;wBACEpB,OAAOV,cAAc;0BACnBW,IAAIC,QAAQ,8CAAA;0BACZC,gBAAgB;wBAClB,CAAA;wBACAe,MAAM;wBACNC,MAAM;wBACNC,MAAM;sBACR;sBACA;wBACEpB,OAAOV,cAAc;0BACnBW,IAAIC,QAAQ,sCAAA;0BACZC,gBAAgB;wBAClB,CAAA;wBACAe,MAAM;wBACNC,MAAM;wBACNC,MAAM;sBACR;sBACA;wBACEpB,OAAOV,cAAc;0BACnBW,IAAIC,QAAQ,uCAAA;0BACZC,gBAAgB;wBAClB,CAAA;wBACAe,MAAM;wBACNC,MAAM;wBACNC,MAAM;sBACR;sBACAC,IAAI,CAAC,EAAEF,MAAM,GAAGG,MAAO,UACvB9B,wBAACwB,KAAKO,MAAI;sBAERC,KAAKL;sBACLM,WAAU;sBACVC,YAAW;sBAEX,cAAAlC,wBAACmC,uBAAAA;wBAAe,GAAGL;;oBALdA,GAAAA,MAAMJ,IAAI,CAAA;;;oBAUvBtB,yBAACH,MAAMmC,QAAM;;wBACXpC,wBAACC,MAAMoC,OAAK;sBACV,cAAArC,wBAACsC,QAAAA;wBAAOC,SAAQ;wBAAW,UAAA;;;wBAE7BvC,wBAACsC,QAAAA;sBAAOE,SAASnB;sBAAcO,MAAK;sBAAS,UAAA;;;;;;UAMrD;;;;;AAKV;AAEAnC,UAAUgD,eAAe;EACvB/C,UAAU,CAAA;AACZ;AAEAD,UAAUiD,YAAY;EACpBhD,UAAUiD,kBAAAA,QAAUC,MAAM;IACxBhC,SAAS+B,kBAAAA,QAAUE;IACnBC,MAAMH,kBAAAA,QAAUE;IAChBE,SAASJ,kBAAAA,QAAUC,MAAM;MACvBI,MAAML,kBAAAA,QAAUC,MAAM;QACpBlB,MAAMiB,kBAAAA,QAAUE;QAChBI,OAAON,kBAAAA,QAAUE;MACnB,CAAA;MACAK,SAASP,kBAAAA,QAAUE;MACnBM,QAAQR,kBAAAA,QAAUE;MAClBO,gBAAgBT,kBAAAA,QAAUE;IAC5B,CAAA;EACF,CAAA;EACAjD,MAAM+C,kBAAAA,QAAUU,KAAKC;EACrBzD,UAAU8C,kBAAAA,QAAUY,KAAKD;EACzB3D,UAAUgD,kBAAAA,QAAUY,KAAKD;AAC3B;;;;;;;ACrIA,IAAME,aAAa,CAAC,EAAEC,WAAWC,YAAW,MAAE;AAC5C,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEC,0BAACC,OAAAA;IAAMC,UAAU;IAAGC,UAAU;;UAC5BC,yBAACC,OAAAA;QACC,cAAAL,0BAACM,IAAAA;;gBACCF,yBAACG,IAAAA;cAAGC,OAAM;cACR,cAAAJ,yBAACK,gBAAAA;0BACEX,cAAc;kBACbY,IAAIC,QAAQ,iCAAA;kBACZC,gBAAgB;gBAClB,CAAA;;;gBAGJR,yBAACG,IAAAA;cACC,cAAAH,yBAACS,YAAAA;gBAAWC,SAAQ;gBAAQC,WAAU;0BACnCjB,cAAc;kBACbY,IAAIC,QAAQ,iCAAA;kBACZC,gBAAgB;gBAClB,CAAA;;;gBAGJR,yBAACG,IAAAA;cAAGC,OAAM;cACR,cAAAJ,yBAACK,gBAAAA;0BACEX,cAAc;kBACbY,IAAIC,QAAQ,mCAAA;kBACZC,gBAAgB;gBAClB,CAAA;;;;;;UAKRZ,0BAACgB,OAAAA;;cACChB,0BAACM,IAAAA;YAAGW,QAAO;YAAUC,SAAS,MAAMrB,YAAY,gBAAA;;kBAC9CO,yBAACe,IAAAA;gBACC,cAAAf,yBAACgB,KAAAA;kBAAIZ,OAAM;kBAASa,QAAO;kBAASC,SAAQ;kBAC1C,cAAAlB,yBAACmB,eAAAA;oBACCC,cAAY1B,cAAc;sBACxBY,IAAI;sBACJE,gBAAgB;oBAClB,CAAA;;;;kBAINR,yBAACe,IAAAA;gBACC,cAAAf,yBAACS,YAAAA;4BACEf,cAAc;oBACbY,IAAI;oBACJE,gBAAgB;kBAClB,CAAA;;;kBAGJR,yBAACe,IAAAA;gBAAGD,SAAS,CAACO,MAAMA,EAAEC,gBAAe;gBACnC,cAAAtB,yBAACuB,YAAAA;kBACCT,SAAS,MAAMrB,YAAY,gBAAA;kBAC3B+B,OAAO9B,cAAc;oBACnBY,IAAIC,QAAQ,gCAAA;oBACZC,gBAAgB;kBAClB,CAAA;kBACAE,SAAQ;kBACRe,UAAU,CAACjC;kBAEX,cAAAQ,yBAAC0B,eAAAA,CAAAA,CAAAA;;;;;cAIP9B,0BAACM,IAAAA;YAAGW,QAAO;YAAUC,SAAS,MAAMrB,YAAY,oBAAA;;kBAC9CO,yBAACe,IAAAA;gBACC,cAAAf,yBAACgB,KAAAA;kBAAIZ,OAAM;kBAASa,QAAO;kBAASC,SAAQ;kBAC1C,cAAAlB,yBAAC2B,eAAAA;oBACCP,cAAY1B,cAAc;sBACxBY,IAAIC,QAAQ,mCAAA;sBACZC,gBAAgB;oBAClB,CAAA;;;;kBAINR,yBAACe,IAAAA;gBACC,cAAAf,yBAACS,YAAAA;4BACEf,cAAc;oBACbY,IAAIC,QAAQ,mCAAA;oBACZC,gBAAgB;kBAClB,CAAA;;;kBAGJR,yBAACe,IAAAA;gBAAGD,SAAS,CAACO,MAAMA,EAAEC,gBAAe;gBACnC,cAAAtB,yBAACuB,YAAAA;kBACCT,SAAS,MAAMrB,YAAY,oBAAA;kBAC3B+B,OAAO9B,cAAc;oBACnBY,IAAIC,QAAQ,gCAAA;oBACZC,gBAAgB;kBAClB,CAAA;kBACAE,SAAQ;kBACRe,UAAU,CAACjC;kBAEX,cAAAQ,yBAAC0B,eAAAA,CAAAA,CAAAA;;;;;;;;;AAOf;AAEAnC,WAAWqC,YAAY;EACrBpC,WAAWqC,mBAAAA,QAAUC,KAAKC;EAC1BtC,aAAaoC,mBAAAA,QAAUG,KAAKD;AAC9B;;;AC3GA,IAAME,8BAA8B,UAClCC,yBAACC,KAAKC,SAAO;EAACC,aAAaC,YAAYC;EACrC,cAAAL,yBAACM,oBAAAA,CAAAA,CAAAA;;AAGL,IAAMA,qBAAqB,MAAA;AACzB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAM,EAAEC,aAAY,IAAKC,YAAAA;AACzB,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAMC,cAAcC,eAAAA;AACpB,QAAM,EAAEC,KAAKC,IAAG,IAAKC,eAAAA;AACrB,QAAM,EAAEC,eAAc,IAAKC,mBAAAA;AAE3B,QAAM,CAACC,aAAaC,cAAAA,IAAwBC,eAAS,KAAA;AACrD,QAAM,CAACC,gBAAgBC,iBAAAA,IAA2BF,eAAS,IAAA;AAE3D,QAAM,EACJG,WAAWC,yBACXC,gBAAgB,EAAEC,UAAS,EAAE,IAC3BC,QAAQ;IAAEC,QAAQ5B,YAAY6B;EAAqB,CAAA;AAEvD,QAAM,EAAEN,WAAWO,eAAeC,KAAI,IAAKC,SACzC;IAAC;IAAqB;KACtB,YAAA;AACE,UAAM,EAAED,MAAAA,MAAI,IAAK,MAAMlB,IAAI,oCAAA;AAE3B,WAAOkB;KAET;IACEE,YAAAA;AACE1B,mBACEJ,cAAc;QACZ+B,IAAIC,QAAQ,4BAAA;QACZC,gBAAgB;MAClB,CAAA,CAAA;IAEJ;IACAC,QAAQC,OAAK;AACX7B,yBAAmB;QACjB8B,MAAM;QACNC,SAASxB,eAAesB,KAAAA;MAC1B,CAAA;IACF;EACF,CAAA;AAGF,QAAMf,YAAYC,2BAA2BM;AAE7C,QAAMW,eAAe,MAAA;AACnBtB,mBAAe,CAACuB,SAAS,CAACA,IAAAA;EAC5B;AAEA,QAAMC,kBAAkB,CAACC,aAAAA;AACvBtB,sBAAkBsB,QAAAA;AAClBH,iBAAAA;EACF;AAEA,QAAMI,iBAAiBC,YACrB,CAACC,SAASjC,IAAI,sCAAsC;IAAE,mBAAmBiC;GACzE,GAAA;IACE,MAAMd,YAAAA;AACJ,YAAMtB,YAAYqC,kBAAkB;QAAC;QAAqB;MAAkB,CAAA;AAE5EvC,yBAAmB;QACjB8B,MAAM;QACNC,SAASrC,cAAc;UAAE+B,IAAI;UAA8BE,gBAAgB;QAAQ,CAAA;MACrF,CAAA;AAEA/B,iBAAW,uBAAA;AAEXoC,mBAAAA;IACF;IACAJ,QAAQC,OAAK;AACX7B,yBAAmB;QACjB8B,MAAM;QACNC,SAASxB,eAAesB,KAAAA;MAC1B,CAAA;IACF;IACAW,eAAe;EACjB,CAAA;AAGF,QAAMC,eAAe,CAACH,SAAAA;AACpB1C,eAAW,wBAAA;AAEX,UAAM8C,kBAAkB;MAAE,GAAGpB;MAAM,CAACV,cAAAA,GAAiB0B;IAAK;AAC1DF,mBAAeO,OAAOD,eAAAA;EACxB;AAEA,MAAI5B,WAAW;AACb,eAAO3B,yBAACC,KAAKwD,SAAO,CAAA,CAAA;EACtB;AAEA,aACEC,0BAACzD,KAAK0D,MAAI;IAACC,aAAWX,eAAetB;;UACnC3B,yBAACC,KAAK4D,OAAK;kBACRtD,cACC;UAAE+B,IAAI;UAAsBE,gBAAgB;WAC5C;UACEsB,MAAMvD,cAAc;YAClB+B,IAAIC,QAAQ,+BAAA;YACZC,gBAAgB;UAClB,CAAA;QACF,CAAA;;UAGJxC,yBAAC+D,QAAQC,QAAM;QACbC,OAAO1D,cAAc;UACnB+B,IAAIC,QAAQ,+BAAA;UACZC,gBAAgB;QAClB,CAAA;;UAEFkB,0BAACK,QAAQG,SAAO;;cACdlE,yBAACmE,YAAAA;YAAWC,aAAarB;YAAiBjB;;cAC1C9B,yBAACqE,WAAAA;YACCrB,UAAUb,KAAKV,cAAe;YAC9B6C,UAAUzB;YACV0B,MAAMjD;YACNkD,UAAUlB;;;;;;AAKpB;", "names": ["schema", "object", "shape", "options", "from", "name", "string", "required", "id", "translatedErrors", "defaultMessage", "email", "response_email", "message", "EmailForm", "template", "onToggle", "open", "onSubmit", "formatMessage", "useIntl", "_jsx", "Modal", "Root", "onOpenChange", "_jsxs", "Content", "Header", "Breadcrumbs", "label", "id", "getTrad", "defaultMessage", "display", "Crumb", "isCurrent", "VisuallyHidden", "Title", "Form", "initialValues", "validationSchema", "schema", "isSubmitting", "_Fragment", "Body", "Grid", "gap", "name", "size", "type", "map", "field", "<PERSON><PERSON>", "col", "direction", "alignItems", "InputR<PERSON><PERSON>", "Footer", "Close", "<PERSON><PERSON>", "variant", "loading", "defaultProps", "propTypes", "PropTypes", "shape", "string", "icon", "options", "from", "email", "message", "object", "response_email", "bool", "isRequired", "func", "EmailTable", "canUpdate", "onEditClick", "formatMessage", "useIntl", "_jsxs", "Table", "col<PERSON>ount", "rowCount", "_jsx", "<PERSON><PERSON>", "Tr", "Th", "width", "VisuallyHidden", "id", "getTrad", "defaultMessage", "Typography", "variant", "textColor", "Tbody", "cursor", "onClick", "Td", "Box", "height", "padding", "Refresh", "aria-label", "e", "stopPropagation", "IconButton", "label", "disabled", "Pencil", "Check", "propTypes", "PropTypes", "bool", "isRequired", "func", "ProtectedEmailTemplatesPage", "_jsx", "Page", "Protect", "permissions", "PERMISSIONS", "readEmailTemplates", "EmailTemplatesPage", "formatMessage", "useIntl", "trackUsage", "useTracking", "notify<PERSON><PERSON><PERSON>", "useNotifyAT", "toggleNotification", "useNotification", "queryClient", "useQueryClient", "get", "put", "useFetchClient", "formatAPIError", "useAPIErrorHandler", "isModalOpen", "setIsModalOpen", "useState", "templateToEdit", "setTemplateToEdit", "isLoading", "isLoadingForPermissions", "allowedActions", "canUpdate", "useRBAC", "update", "updateEmailTemplates", "isLoadingData", "data", "useQuery", "onSuccess", "id", "getTrad", "defaultMessage", "onError", "error", "type", "message", "handleToggle", "prev", "handleEditClick", "template", "submitMutation", "useMutation", "body", "invalidateQueries", "refetchActive", "handleSubmit", "editedTemplates", "mutate", "Loading", "_jsxs", "Main", "aria-busy", "Title", "name", "Layouts", "Header", "title", "Content", "EmailTable", "onEditClick", "EmailForm", "onToggle", "open", "onSubmit"]}