{"version": 3, "sources": ["../../../@strapi/upload/admin/src/pages/App/ConfigureTheView/components/Settings.tsx", "../../../@strapi/upload/admin/src/pages/App/ConfigureTheView/state/actionTypes.ts", "../../../@strapi/upload/admin/src/pages/App/ConfigureTheView/state/actions.ts", "../../../@strapi/upload/admin/src/pages/App/ConfigureTheView/state/init.ts", "../../../@strapi/upload/admin/src/pages/App/ConfigureTheView/state/reducer.ts", "../../../@strapi/upload/admin/src/pages/App/ConfigureTheView/ConfigureTheView.tsx"], "sourcesContent": ["import { Box, Grid, SingleSelectOption, SingleSelect, Field } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { pageSizes, sortOptions } from '../../../../constants';\nimport { getTrad } from '../../../../utils';\n\nimport type { Configuration } from '../../../../../../shared/contracts/configuration';\n\ninterface SettingsProps {\n  sort: string;\n  pageSize: string | number;\n  onChange: ({\n    target: { name, value },\n  }: {\n    target: { name: keyof Configuration; value: string | number };\n  }) => void;\n}\n\nconst Settings = ({ sort = '', pageSize = 10, onChange }: SettingsProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Box\n      background=\"neutral0\"\n      hasRadius\n      shadow=\"tableShadow\"\n      paddingTop={6}\n      paddingBottom={6}\n      paddingLeft={7}\n      paddingRight={7}\n    >\n      <Grid.Root gap={4}>\n        <Grid.Item s={12} col={6} direction=\"column\" alignItems=\"stretch\">\n          <Field.Root\n            hint={formatMessage({\n              id: getTrad('config.entries.note'),\n              defaultMessage: 'Number of assets displayed by default in the Media Library',\n            })}\n            name=\"pageSize\"\n          >\n            <Field.Label>\n              {formatMessage({\n                id: getTrad('config.entries.title'),\n                defaultMessage: 'Entries per page',\n              })}\n            </Field.Label>\n            <SingleSelect\n              onChange={(value) => onChange({ target: { name: 'pageSize', value } })}\n              value={pageSize}\n            >\n              {pageSizes.map((pageSize) => (\n                <SingleSelectOption key={pageSize} value={pageSize}>\n                  {pageSize}\n                </SingleSelectOption>\n              ))}\n            </SingleSelect>\n            <Field.Hint />\n          </Field.Root>\n        </Grid.Item>\n        <Grid.Item s={12} col={6} direction=\"column\" alignItems=\"stretch\">\n          <Field.Root\n            hint={formatMessage({\n              id: getTrad('config.note'),\n              defaultMessage: 'Note: You can override this value in the media library.',\n            })}\n            name=\"sort\"\n          >\n            <Field.Label>\n              {formatMessage({\n                id: getTrad('config.sort.title'),\n                defaultMessage: 'Default sort order',\n              })}\n            </Field.Label>\n            <SingleSelect\n              onChange={(value) => onChange({ target: { name: 'sort', value } })}\n              value={sort}\n              test-sort={sort}\n              data-testid=\"sort-select\"\n            >\n              {sortOptions.map((filter) => (\n                <SingleSelectOption\n                  data-testid={`sort-option-${filter.value}`}\n                  key={filter.key}\n                  value={filter.value}\n                >\n                  {formatMessage({ id: getTrad(filter.key), defaultMessage: `${filter.value}` })}\n                </SingleSelectOption>\n              ))}\n            </SingleSelect>\n            <Field.Hint />\n          </Field.Root>\n        </Grid.Item>\n      </Grid.Root>\n    </Box>\n  );\n};\n\nexport { Settings };\n", "import { pluginId } from '../../../../pluginId';\n\nexport const ON_CHANGE = `${pluginId}/ON_CHANGE`;\nexport const SET_LOADED = `${pluginId}/SET_LOADED`;\n", "import { ON_CHANGE, SET_LOADED } from './actionTypes';\n\nimport type { InitialState } from './init';\n\nexport const onChange = ({\n  name,\n  value,\n}: {\n  name: keyof NonNullable<InitialState['initialData']>;\n  value: number | string;\n}) => ({\n  type: ON_CHANGE,\n  keys: name,\n  value,\n});\n\nexport const setLoaded = () => ({\n  type: SET_LOADED,\n});\n", "import type { Configuration } from '../../../../../../shared/contracts/configuration';\n\nexport type InitialState = {\n  initialData: Partial<Configuration>;\n  modifiedData: Partial<Configuration>;\n};\n\nconst initialState: InitialState = {\n  initialData: {},\n  modifiedData: {},\n};\n\nconst init = (configData: InitialState['initialData']): InitialState => {\n  return {\n    ...initialState,\n    initialData: configData,\n    modifiedData: configData,\n  };\n};\nexport { init, initialState };\n", "import { produce } from 'immer'; // current\nimport get from 'lodash/get';\nimport set from 'lodash/set';\n\nimport { ON_CHANGE, SET_LOADED } from './actionTypes';\nimport { init, initialState } from './init';\n\nimport type { InitialState } from './init';\n\nexport interface ActionOnChange {\n  type: string;\n  keys?: string;\n  value: string | number;\n}\n\nexport interface ActionSetLoaded {\n  type: string;\n}\n\ninterface ActionInitialValue {\n  type: string;\n}\n\nexport type Action = ActionSetLoaded | ActionOnChange | ActionInitialValue;\n\nexport const reducer = (\n  state: InitialState = initialState,\n  action: Action = {\n    type: '',\n  }\n) =>\n  // eslint-disable-next-line consistent-return\n  produce(state, (draftState) => {\n    switch (action.type) {\n      case ON_CHANGE: {\n        if ('keys' in action && 'value' in action && action.keys) {\n          set(draftState, ['modifiedData', ...action.keys.split('.')], action.value);\n        }\n        break;\n      }\n      case SET_LOADED: {\n        // This action re-initialises the state using the current modifiedData.\n        const reInitialise = init(get(draftState, ['modifiedData'], {}));\n        draftState.initialData = reInitialise.initialData;\n        draftState.modifiedData = reInitialise.modifiedData;\n        break;\n      }\n      default:\n        return draftState;\n    }\n  });\n", "// TODO: find a better naming convention for the file that was an index file before\nimport * as React from 'react';\n\nimport {\n  ConfirmDialog,\n  useTracking,\n  useNotification,\n  Page,\n  Layouts,\n} from '@strapi/admin/strapi-admin';\nimport { <PERSON><PERSON>, Dialog, Link } from '@strapi/design-system';\nimport { ArrowLeft, Check } from '@strapi/icons';\nimport isEqual from 'lodash/isEqual';\nimport { useIntl } from 'react-intl';\nimport { NavLink } from 'react-router-dom';\n\nimport { useConfig } from '../../../hooks/useConfig';\nimport { pluginId } from '../../../pluginId';\nimport { getTrad } from '../../../utils';\n\nimport { Settings } from './components/Settings';\nimport { onChange, setLoaded } from './state/actions';\nimport { init, initialState } from './state/init';\nimport { reducer } from './state/reducer';\n\nimport type { InitialState } from './state/init';\nimport type { Action } from './state/reducer';\nimport type { Configuration } from '../../../../../shared/contracts/configuration';\n\ninterface ConfigureTheViewProps {\n  config: Configuration;\n}\n\nexport const ConfigureTheView = ({ config }: ConfigureTheViewProps) => {\n  const { trackUsage } = useTracking();\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { mutateConfig } = useConfig();\n  const { isLoading: isSubmittingForm } = mutateConfig;\n\n  const [showWarningSubmit, setWarningSubmit] = React.useState(false);\n  const toggleWarningSubmit = () => setWarningSubmit((prevState) => !prevState);\n\n  const [reducerState, dispatch] = React.useReducer(\n    reducer,\n    initialState,\n    (): InitialState => init(config)\n  );\n  const typedDispatch: React.Dispatch<Action> = dispatch;\n  const { initialData, modifiedData } = reducerState;\n\n  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    toggleWarningSubmit();\n  };\n\n  const handleConfirm = async () => {\n    trackUsage('willEditMediaLibraryConfig');\n    await mutateConfig.mutateAsync(modifiedData as Configuration);\n    setWarningSubmit(false);\n    typedDispatch(setLoaded());\n    toggleNotification({\n      type: 'success',\n      message: formatMessage({\n        id: 'notification.form.success.fields',\n        defaultMessage: 'Changes saved',\n      }),\n    });\n  };\n\n  const handleChange = ({\n    target: { name, value },\n  }: {\n    target: { name: keyof Configuration; value: string | number };\n  }) => {\n    typedDispatch(onChange({ name, value }));\n  };\n\n  return (\n    <Layouts.Root>\n      <Page.Main aria-busy={isSubmittingForm}>\n        <form onSubmit={handleSubmit}>\n          <Layouts.Header\n            navigationAction={\n              <Link\n                tag={NavLink}\n                startIcon={<ArrowLeft />}\n                to={`/plugins/${pluginId}`}\n                id=\"go-back\"\n              >\n                {formatMessage({ id: getTrad('config.back'), defaultMessage: 'Back' })}\n              </Link>\n            }\n            primaryAction={\n              <Button\n                size=\"S\"\n                startIcon={<Check />}\n                disabled={isEqual(modifiedData, initialData)}\n                type=\"submit\"\n              >\n                {formatMessage({ id: 'global.save', defaultMessage: 'Save' })}\n              </Button>\n            }\n            subtitle={formatMessage({\n              id: getTrad('config.subtitle'),\n              defaultMessage: 'Define the view settings of the media library.',\n            })}\n            title={formatMessage({\n              id: getTrad('config.title'),\n              defaultMessage: 'Configure the view - Media Library',\n            })}\n          />\n          <Layouts.Content>\n            <Settings\n              data-testid=\"settings\"\n              pageSize={modifiedData.pageSize || ''}\n              sort={modifiedData.sort || ''}\n              onChange={handleChange}\n            />\n          </Layouts.Content>\n          x\n          <Dialog.Root open={showWarningSubmit} onOpenChange={toggleWarningSubmit}>\n            <ConfirmDialog onConfirm={handleConfirm} variant=\"default\">\n              {formatMessage({\n                id: getTrad('config.popUpWarning.warning.updateAllSettings'),\n                defaultMessage: 'This will modify all your settings',\n              })}\n            </ConfirmDialog>\n          </Dialog.Root>\n        </form>\n      </Page.Main>\n    </Layouts.Root>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBMA,IAAAA,WAAW,CAAC,EAAEC,OAAO,IAAIC,WAAW,IAAIC,UAAAA,UAAQ,MAAiB;AACrE,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEC,wBAACC,KAAAA;IACCC,YAAW;IACXC,WAAS;IACTC,QAAO;IACPC,YAAY;IACZC,eAAe;IACfC,aAAa;IACbC,cAAc;kBAEdC,yBAACC,KAAKC,MAAI;MAACC,KAAK;;YACdZ,wBAACU,KAAKG,MAAI;UAACC,GAAG;UAAIC,KAAK;UAAGC,WAAU;UAASC,YAAW;wBACtDR,yBAACS,MAAMP,MAAI;YACTQ,MAAMrB,cAAc;cAClBsB,IAAIC,QAAQ,qBAAA;cACZC,gBAAgB;YAClB,CAAA;YACAC,MAAK;;kBAELvB,wBAACkB,MAAMM,OAAK;0BACT1B,cAAc;kBACbsB,IAAIC,QAAQ,sBAAA;kBACZC,gBAAgB;gBAClB,CAAA;;kBAEFtB,wBAACyB,cAAAA;gBACC5B,UAAU,CAAC6B,UAAU7B,UAAS;kBAAE8B,QAAQ;oBAAEJ,MAAM;oBAAYG;kBAAM;gBAAE,CAAA;gBACpEA,OAAO9B;gBAENgC,UAAAA,UAAUC,IAAI,CAACjC,kBACdI,wBAAC8B,oBAAAA;kBAAkCJ,OAAO9B;kBACvCA,UAAAA;gBADsBA,GAAAA,SAAAA,CAAAA;;kBAK7BI,wBAACkB,MAAMa,MAAI,CAAA,CAAA;;;;YAGf/B,wBAACU,KAAKG,MAAI;UAACC,GAAG;UAAIC,KAAK;UAAGC,WAAU;UAASC,YAAW;wBACtDR,yBAACS,MAAMP,MAAI;YACTQ,MAAMrB,cAAc;cAClBsB,IAAIC,QAAQ,aAAA;cACZC,gBAAgB;YAClB,CAAA;YACAC,MAAK;;kBAELvB,wBAACkB,MAAMM,OAAK;0BACT1B,cAAc;kBACbsB,IAAIC,QAAQ,mBAAA;kBACZC,gBAAgB;gBAClB,CAAA;;kBAEFtB,wBAACyB,cAAAA;gBACC5B,UAAU,CAAC6B,UAAU7B,UAAS;kBAAE8B,QAAQ;oBAAEJ,MAAM;oBAAQG;kBAAM;gBAAE,CAAA;gBAChEA,OAAO/B;gBACPqC,aAAWrC;gBACXsC,eAAY;gBAEXC,UAAAA,YAAYL,IAAI,CAACM,eAChBnC,wBAAC8B,oBAAAA;kBACCG,eAAa,eAAeE,OAAOT,KAAK;kBAExCA,OAAOS,OAAOT;4BAEb5B,cAAc;oBAAEsB,IAAIC,QAAQc,OAAOC,GAAG;oBAAGd,gBAAgB,GAAGa,OAAOT,KAAK;kBAAG,CAAA;gBAHvES,GAAAA,OAAOC,GAAG,CAAA;;kBAOrBpC,wBAACkB,MAAMa,MAAI,CAAA,CAAA;;;;;;;AAMvB;;;IC7FaM,YAAY,GAAGC,QAAS;IACxBC,aAAa,GAAGD,QAAS;;;ACC/B,IAAME,WAAW,CAAC,EACvBC,MACAC,MAAK,OAIA;EACLC,MAAMC;EACNC,MAAMJ;EACNC;AACF;AAEO,IAAMI,YAAY,OAAO;EAC9BH,MAAMI;AACR;;;ACXA,IAAMC,eAA6B;EACjCC,aAAa,CAAA;EACbC,cAAc,CAAA;AAChB;AAEA,IAAMC,OAAO,CAACC,eAAAA;AACZ,SAAO;IACL,GAAGJ;IACHC,aAAaG;IACbF,cAAcE;EAChB;AACF;;;;;ICOaC,UAAU,CACrBC,QAAsBC,cACtBC,SAAiB;EACfC,MAAM;AACR;;EAGAC,GAAQJ,OAAO,CAACK,eAAAA;AACd,YAAQH,OAAOC,MAAI;MACjB,KAAKG,WAAW;AACd,YAAI,UAAUJ,UAAU,WAAWA,UAAUA,OAAOK,MAAM;AACxDC,yBAAAA,SAAIH,YAAY;YAAC;eAAmBH,OAAOK,KAAKE,MAAM,GAAA;UAAK,GAAEP,OAAOQ,KAAK;QAC3E;AACA;MACF;MACA,KAAKC,YAAY;AAEf,cAAMC,eAAeC,SAAKC,WAAAA,SAAIT,YAAY;UAAC;QAAe,GAAE,CAAA,CAAC,CAAA;AAC7DA,mBAAWU,cAAcH,aAAaG;AACtCV,mBAAWW,eAAeJ,aAAaI;AACvC;MACF;MACA;AACE,eAAOX;IACX;GACC;;;;ACjBQY,IAAAA,mBAAmB,CAAC,EAAEC,OAAM,MAAyB;AAChE,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,aAAY,IAAKC,UAAAA;AACzB,QAAM,EAAEC,WAAWC,iBAAgB,IAAKH;AAExC,QAAM,CAACI,mBAAmBC,gBAAAA,IAA0BC,eAAS,KAAA;AAC7D,QAAMC,sBAAsB,MAAMF,iBAAiB,CAACG,cAAc,CAACA,SAAAA;AAEnE,QAAM,CAACC,cAAcC,QAAAA,IAAkBC,iBACrCC,SACAC,cACA,MAAoBC,KAAKrB,MAAAA,CAAAA;AAE3B,QAAMsB,gBAAwCL;AAC9C,QAAM,EAAEM,aAAaC,aAAY,IAAKR;AAEtC,QAAMS,eAAe,CAACC,MAAAA;AACpBA,MAAEC,eAAc;AAChBb,wBAAAA;EACF;AAEA,QAAMc,gBAAgB,YAAA;AACpB3B,eAAW,4BAAA;AACX,UAAMM,aAAasB,YAAYL,YAAAA;AAC/BZ,qBAAiB,KAAA;AACjBU,kBAAcQ,UAAAA,CAAAA;AACdzB,uBAAmB;MACjB0B,MAAM;MACNC,SAAS7B,cAAc;QACrB8B,IAAI;QACJC,gBAAgB;MAClB,CAAA;IACF,CAAA;EACF;AAEA,QAAMC,eAAe,CAAC,EACpBC,QAAQ,EAAEC,MAAMC,MAAK,EAAE,MAGxB;AACChB,kBAAciB,SAAS;MAAEF;MAAMC;IAAM,CAAA,CAAA;EACvC;AAEA,aACEE,yBAACC,QAAQC,MAAI;kBACXF,yBAACG,KAAKC,MAAI;MAACC,aAAWnC;MACpB,cAAAoC,0BAACC,QAAAA;QAAKC,UAAUvB;;cACde,yBAACC,QAAQQ,QAAM;YACbC,sBACEV,yBAACW,MAAAA;cACCC,KAAKC;cACLC,eAAWd,yBAACe,eAAAA,CAAAA,CAAAA;cACZC,IAAI,YAAYC,QAAAA;cAChBxB,IAAG;wBAEF9B,cAAc;gBAAE8B,IAAIyB,QAAQ,aAAA;gBAAgBxB,gBAAgB;cAAO,CAAA;;YAGxEyB,mBACEnB,yBAACoB,QAAAA;cACCC,MAAK;cACLP,eAAWd,yBAACsB,eAAAA,CAAAA,CAAAA;cACZC,cAAUC,eAAAA,SAAQxC,cAAcD,WAAAA;cAChCQ,MAAK;wBAEJ5B,cAAc;gBAAE8B,IAAI;gBAAeC,gBAAgB;cAAO,CAAA;;YAG/D+B,UAAU9D,cAAc;cACtB8B,IAAIyB,QAAQ,iBAAA;cACZxB,gBAAgB;YAClB,CAAA;YACAgC,OAAO/D,cAAc;cACnB8B,IAAIyB,QAAQ,cAAA;cACZxB,gBAAgB;YAClB,CAAA;;cAEFM,yBAACC,QAAQ0B,SAAO;YACd,cAAA3B,yBAAC4B,UAAAA;cACCC,eAAY;cACZC,UAAU9C,aAAa8C,YAAY;cACnCC,MAAM/C,aAAa+C,QAAQ;cAC3BhC,UAAUJ;;;UAEI;cAElBK,yBAACgC,OAAO9B,MAAI;YAAC+B,MAAM9D;YAAmB+D,cAAc5D;YAClD,cAAA0B,yBAACmC,eAAAA;cAAcC,WAAWhD;cAAeiD,SAAQ;wBAC9C1E,cAAc;gBACb8B,IAAIyB,QAAQ,+CAAA;gBACZxB,gBAAgB;cAClB,CAAA;;;;;;;AAOd;", "names": ["Settings", "sort", "pageSize", "onChange", "formatMessage", "useIntl", "_jsx", "Box", "background", "hasRadius", "shadow", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "_jsxs", "Grid", "Root", "gap", "<PERSON><PERSON>", "s", "col", "direction", "alignItems", "Field", "hint", "id", "getTrad", "defaultMessage", "name", "Label", "SingleSelect", "value", "target", "pageSizes", "map", "SingleSelectOption", "Hint", "test-sort", "data-testid", "sortOptions", "filter", "key", "ON_CHANGE", "pluginId", "SET_LOADED", "onChange", "name", "value", "type", "ON_CHANGE", "keys", "setLoaded", "SET_LOADED", "initialState", "initialData", "modifiedData", "init", "configData", "reducer", "state", "initialState", "action", "type", "produce", "draftState", "ON_CHANGE", "keys", "set", "split", "value", "SET_LOADED", "reInitialise", "init", "get", "initialData", "modifiedData", "ConfigureTheView", "config", "trackUsage", "useTracking", "formatMessage", "useIntl", "toggleNotification", "useNotification", "mutateConfig", "useConfig", "isLoading", "isSubmittingForm", "showWarningSubmit", "setWarningSubmit", "useState", "toggleWarningSubmit", "prevState", "reducerState", "dispatch", "useReducer", "reducer", "initialState", "init", "typedDispatch", "initialData", "modifiedData", "handleSubmit", "e", "preventDefault", "handleConfirm", "mutateAsync", "setLoaded", "type", "message", "id", "defaultMessage", "handleChange", "target", "name", "value", "onChange", "_jsx", "Layouts", "Root", "Page", "Main", "aria-busy", "_jsxs", "form", "onSubmit", "Header", "navigationAction", "Link", "tag", "NavLink", "startIcon", "ArrowLeft", "to", "pluginId", "getTrad", "primaryAction", "<PERSON><PERSON>", "size", "Check", "disabled", "isEqual", "subtitle", "title", "Content", "Settings", "data-testid", "pageSize", "sort", "Dialog", "open", "onOpenChange", "ConfirmDialog", "onConfirm", "variant"]}