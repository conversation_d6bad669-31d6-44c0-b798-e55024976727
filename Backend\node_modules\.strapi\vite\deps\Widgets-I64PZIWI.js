import {
  DocumentStatus,
  RelativeTime
} from "./chunk-4LPIPK4X.js";
import {
  contentManagerApi
} from "./chunk-A46TUCLT.js";
import "./chunk-EKXSMIUH.js";
import "./chunk-YV3ONWF5.js";
import {
  Widget
} from "./chunk-UMW22TSS.js";
import "./chunk-4QC3H4VA.js";
import "./chunk-75D2ZJP5.js";
import "./chunk-VCTAT6B3.js";
import "./chunk-ROZIXYJG.js";
import "./chunk-C72RZIDJ.js";
import "./chunk-HZKRK7AR.js";
import "./chunk-LRN6A2UC.js";
import "./chunk-D2TGW5YS.js";
import "./chunk-M27D4U76.js";
import "./chunk-HX66WGOY.js";
import "./chunk-Y4UEUAII.js";
import "./chunk-BN2UQHMJ.js";
import "./chunk-NWWGC2Z2.js";
import "./chunk-DD3MYA7D.js";
import "./chunk-O5ZNSDDU.js";
import "./chunk-MBK4V2X7.js";
import "./chunk-DY2RJG3P.js";
import "./chunk-K65KIEAL.js";
import "./chunk-BUDFB33L.js";
import "./chunk-7MILHJ3J.js";
import "./chunk-SGQJOZK5.js";
import "./chunk-AFM2NWPO.js";
import "./chunk-DUGZ4WIW.js";
import "./chunk-IFOFBKTA.js";
import "./chunk-376QHLWZ.js";
import "./chunk-EGNP2T5O.js";
import {
  useTracking
} from "./chunk-XDCEA27D.js";
import "./chunk-EZSYDDUK.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-QIJGNK42.js";
import "./chunk-CJHUGFLE.js";
import "./chunk-IQGHPIIW.js";
import "./chunk-DWSGKQEK.js";
import "./chunk-W6ZGCRX6.js";
import "./chunk-PVCRV2LE.js";
import "./chunk-HWAQQGJJ.js";
import "./chunk-L5JCPKMP.js";
import "./chunk-ZJEMJY2Q.js";
import "./chunk-D4WYVNVM.js";
import "./chunk-MMOBCIZG.js";
import "./chunk-6DRYEU2W.js";
import "./chunk-MTTHLNPH.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import "./chunk-7LKLOY7A.js";
import "./chunk-ODQFI753.js";
import "./chunk-ZOP4VV6J.js";
import "./chunk-WH6VCVXU.js";
import "./chunk-IL5G2D22.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-76QM3EFM.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import "./chunk-UBCTZOSQ.js";
import {
  Box,
  IconButton,
  Table,
  Tbody,
  Td,
  Tr,
  Typography,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import "./chunk-5ZC4PE57.js";
import {
  Link,
  useNavigate
} from "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$1v
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-ACIMPXWY.js";
import "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/dist/admin/components/Widgets.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);

// node_modules/@strapi/content-manager/dist/admin/services/homepage.mjs
var homepageService = contentManagerApi.enhanceEndpoints({
  addTagTypes: [
    "RecentDocumentList"
  ]
}).injectEndpoints({
  /**
   * TODO: Remove overrideExisting when we remove the future flag
   * and delete the old homepage service in the admin
   */
  overrideExisting: true,
  endpoints: (builder) => ({
    getRecentDocuments: builder.query({
      query: (params) => `/content-manager/homepage/recent-documents?action=${params.action}`,
      transformResponse: (response) => response.data,
      providesTags: (res, _err, { action }) => [
        {
          type: "RecentDocumentList",
          id: action
        }
      ]
    })
  })
});
var { useGetRecentDocumentsQuery } = homepageService;

// node_modules/@strapi/content-manager/dist/admin/components/Widgets.mjs
var CellTypography = dt(Typography).attrs({
  maxWidth: "14.4rem",
  display: "block"
})`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;
var RecentDocumentsTable = ({ documents }) => {
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const navigate = useNavigate();
  const getEditViewLink = (document) => {
    const isSingleType = document.kind === "singleType";
    const kindPath = isSingleType ? "single-types" : "collection-types";
    const queryParams = document.locale ? `?plugins[i18n][locale]=${document.locale}` : "";
    return `/content-manager/${kindPath}/${document.contentTypeUid}${isSingleType ? "" : "/" + document.documentId}${queryParams}`;
  };
  const handleRowClick = (document) => () => {
    trackUsage("willEditEntryFromHome");
    const link = getEditViewLink(document);
    navigate(link);
  };
  return (0, import_jsx_runtime.jsx)(Table, {
    colCount: 5,
    rowCount: (documents == null ? void 0 : documents.length) ?? 0,
    children: (0, import_jsx_runtime.jsx)(Tbody, {
      children: documents == null ? void 0 : documents.map((document) => (0, import_jsx_runtime.jsxs)(Tr, {
        onClick: handleRowClick(document),
        cursor: "pointer",
        children: [
          (0, import_jsx_runtime.jsx)(Td, {
            children: (0, import_jsx_runtime.jsx)(CellTypography, {
              title: document.title,
              variant: "omega",
              textColor: "neutral800",
              children: document.title
            })
          }),
          (0, import_jsx_runtime.jsx)(Td, {
            children: (0, import_jsx_runtime.jsx)(CellTypography, {
              variant: "omega",
              textColor: "neutral600",
              children: document.kind === "singleType" ? formatMessage({
                id: "content-manager.widget.last-edited.single-type",
                defaultMessage: "Single-Type"
              }) : formatMessage({
                id: document.contentTypeDisplayName,
                defaultMessage: document.contentTypeDisplayName
              })
            })
          }),
          (0, import_jsx_runtime.jsx)(Td, {
            children: (0, import_jsx_runtime.jsx)(Box, {
              display: "inline-block",
              children: document.status ? (0, import_jsx_runtime.jsx)(DocumentStatus, {
                status: document.status
              }) : (0, import_jsx_runtime.jsx)(Typography, {
                textColor: "neutral600",
                "aria-hidden": true,
                children: "-"
              })
            })
          }),
          (0, import_jsx_runtime.jsx)(Td, {
            children: (0, import_jsx_runtime.jsx)(Typography, {
              textColor: "neutral600",
              children: (0, import_jsx_runtime.jsx)(RelativeTime, {
                timestamp: new Date(document.updatedAt)
              })
            })
          }),
          (0, import_jsx_runtime.jsx)(Td, {
            onClick: (e) => e.stopPropagation(),
            children: (0, import_jsx_runtime.jsx)(Box, {
              display: "inline-block",
              children: (0, import_jsx_runtime.jsx)(IconButton, {
                tag: Link,
                to: getEditViewLink(document),
                onClick: () => trackUsage("willEditEntryFromHome"),
                label: formatMessage({
                  id: "content-manager.actions.edit.label",
                  defaultMessage: "Edit"
                }),
                variant: "ghost",
                children: (0, import_jsx_runtime.jsx)(ForwardRef$1v, {})
              })
            })
          })
        ]
      }, document.documentId))
    })
  });
};
var LastEditedWidget = () => {
  const { formatMessage } = useIntl();
  const { data, isLoading, error } = useGetRecentDocumentsQuery({
    action: "update"
  });
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Widget.Loading, {});
  }
  if (error || !data) {
    return (0, import_jsx_runtime.jsx)(Widget.Error, {});
  }
  if (data.length === 0) {
    return (0, import_jsx_runtime.jsx)(Widget.NoData, {
      children: formatMessage({
        id: "content-manager.widget.last-edited.no-data",
        defaultMessage: "No edited entries"
      })
    });
  }
  return (0, import_jsx_runtime.jsx)(RecentDocumentsTable, {
    documents: data
  });
};
var LastPublishedWidget = () => {
  const { formatMessage } = useIntl();
  const { data, isLoading, error } = useGetRecentDocumentsQuery({
    action: "publish"
  });
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Widget.Loading, {});
  }
  if (error || !data) {
    return (0, import_jsx_runtime.jsx)(Widget.Error, {});
  }
  if (data.length === 0) {
    return (0, import_jsx_runtime.jsx)(Widget.NoData, {
      children: formatMessage({
        id: "content-manager.widget.last-published.no-data",
        defaultMessage: "No published entries"
      })
    });
  }
  return (0, import_jsx_runtime.jsx)(RecentDocumentsTable, {
    documents: data
  });
};
export {
  LastEditedWidget,
  LastPublishedWidget
};
//# sourceMappingURL=Widgets-I64PZIWI.js.map
