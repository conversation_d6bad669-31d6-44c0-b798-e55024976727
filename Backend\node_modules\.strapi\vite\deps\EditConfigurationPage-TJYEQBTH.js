import {
  ConfigurationForm,
  TEMP_FIELD_NAME
} from "./chunk-X73AXI2G.js";
import "./chunk-RTLULQCG.js";
import {
  useTypedSelector
} from "./chunk-SEUNPHZA.js";
import "./chunk-25ZR4HQ4.js";
import "./chunk-QBVVXK5Q.js";
import {
  setIn,
  useDoc,
  useDocLayout,
  useGetInitialDataQuery,
  useUpdateContentTypeConfigurationMutation
} from "./chunk-GZO3GFLN.js";
import "./chunk-A46TUCLT.js";
import "./chunk-HIZVCZYI.js";
import "./chunk-EKXSMIUH.js";
import "./chunk-YV3ONWF5.js";
import "./chunk-UMW22TSS.js";
import "./chunk-4QC3H4VA.js";
import "./chunk-75D2ZJP5.js";
import "./chunk-VCTAT6B3.js";
import "./chunk-ROZIXYJG.js";
import "./chunk-C72RZIDJ.js";
import "./chunk-HZKRK7AR.js";
import "./chunk-LRN6A2UC.js";
import "./chunk-D2TGW5YS.js";
import "./chunk-M27D4U76.js";
import "./chunk-HX66WGOY.js";
import "./chunk-Y4UEUAII.js";
import "./chunk-BN2UQHMJ.js";
import "./chunk-NWWGC2Z2.js";
import "./chunk-MBK4V2X7.js";
import "./chunk-DY2RJG3P.js";
import "./chunk-K65KIEAL.js";
import "./chunk-BUDFB33L.js";
import "./chunk-7MILHJ3J.js";
import "./chunk-SGQJOZK5.js";
import "./chunk-AFM2NWPO.js";
import "./chunk-DUGZ4WIW.js";
import "./chunk-IFOFBKTA.js";
import "./chunk-376QHLWZ.js";
import "./chunk-EGNP2T5O.js";
import {
  useTracking
} from "./chunk-XDCEA27D.js";
import "./chunk-EZSYDDUK.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-QIJGNK42.js";
import "./chunk-CJHUGFLE.js";
import "./chunk-IQGHPIIW.js";
import "./chunk-DWSGKQEK.js";
import "./chunk-W6ZGCRX6.js";
import "./chunk-PVCRV2LE.js";
import "./chunk-HWAQQGJJ.js";
import "./chunk-L5JCPKMP.js";
import "./chunk-ZJEMJY2Q.js";
import "./chunk-6DRYEU2W.js";
import "./chunk-MTTHLNPH.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import {
  Page,
  useAPIErrorHandler
} from "./chunk-7LKLOY7A.js";
import "./chunk-ODQFI753.js";
import "./chunk-ZOP4VV6J.js";
import "./chunk-WH6VCVXU.js";
import "./chunk-IL5G2D22.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-76QM3EFM.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  useIntl
} from "./chunk-7GC3Y62Q.js";
import "./chunk-5ZC4PE57.js";
import "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/dist/admin/pages/EditConfigurationPage.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var EditConfigurationPage = () => {
  const { trackUsage } = useTracking();
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const { isLoading: isLoadingSchema, schema, model } = useDoc();
  const { isLoading: isLoadingLayout, error, list, edit } = useDocLayout();
  const { fieldSizes, error: errorFieldSizes, isLoading: isLoadingFieldSizes, isFetching: isFetchingFieldSizes } = useGetInitialDataQuery(void 0, {
    selectFromResult: (res) => {
      var _a;
      const fieldSizes2 = Object.entries(((_a = res.data) == null ? void 0 : _a.fieldSizes) ?? {}).reduce((acc, [attributeName, { default: size }]) => {
        acc[attributeName] = size;
        return acc;
      }, {});
      return {
        isFetching: res.isFetching,
        isLoading: res.isLoading,
        error: res.error,
        fieldSizes: fieldSizes2
      };
    }
  });
  React.useEffect(() => {
    if (errorFieldSizes) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(errorFieldSizes)
      });
    }
  }, [
    errorFieldSizes,
    formatAPIError,
    toggleNotification
  ]);
  const isLoading = isLoadingSchema || isLoadingLayout || isLoadingFieldSizes || isFetchingFieldSizes;
  const [updateConfiguration] = useUpdateContentTypeConfigurationMutation();
  const handleSubmit = async (data) => {
    try {
      trackUsage("willSaveContentTypeLayout");
      const meta = Object.entries(list.metadatas).reduce((acc, [name, { mainField: _mainField, ...listMeta }]) => {
        const existingEditMeta = edit.metadatas[name];
        const { __temp_key__, size: _size, name: _name, ...editedMetadata } = data.layout.flatMap((row) => row.children).find((field) => field.name === name) ?? {};
        acc[name] = {
          edit: {
            ...existingEditMeta,
            ...editedMetadata
          },
          list: listMeta
        };
        return acc;
      }, {});
      const res = await updateConfiguration({
        layouts: {
          edit: data.layout.map((row) => row.children.reduce((acc, { name, size }) => {
            if (name !== TEMP_FIELD_NAME) {
              return [
                ...acc,
                {
                  name,
                  size
                }
              ];
            }
            return acc;
          }, [])),
          list: list.layout.map((field) => field.name)
        },
        settings: setIn(data.settings, "displayName", void 0),
        metadatas: meta,
        uid: model
      });
      if ("data" in res) {
        trackUsage("didEditEditSettings");
        toggleNotification({
          type: "success",
          message: formatMessage({
            id: "notification.success.saved",
            defaultMessage: "Saved"
          })
        });
      } else {
        toggleNotification({
          type: "danger",
          message: formatAPIError(res.error)
        });
      }
    } catch {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "An error occurred"
        })
      });
    }
  };
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  if (errorFieldSizes || error || !schema) {
    return (0, import_jsx_runtime.jsx)(Page.Error, {});
  }
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, {
    children: [
      (0, import_jsx_runtime.jsx)(Page.Title, {
        children: `Configure ${edit.settings.displayName} Edit View`
      }),
      (0, import_jsx_runtime.jsx)(ConfigurationForm, {
        onSubmit: handleSubmit,
        attributes: schema.attributes,
        fieldSizes,
        layout: edit
      })
    ]
  });
};
var ProtectedEditConfigurationPage = () => {
  const permissions = useTypedSelector((state) => {
    var _a;
    return (_a = state.admin_app.permissions.contentManager) == null ? void 0 : _a.collectionTypesConfigurations;
  });
  return (0, import_jsx_runtime.jsx)(Page.Protect, {
    permissions,
    children: (0, import_jsx_runtime.jsx)(EditConfigurationPage, {})
  });
};
export {
  EditConfigurationPage,
  ProtectedEditConfigurationPage
};
//# sourceMappingURL=EditConfigurationPage-TJYEQBTH.js.map
