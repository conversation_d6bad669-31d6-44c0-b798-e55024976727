import {
  useGetLicenseLimitsQuery
} from "./chunk-EZSYDDUK.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/ee/admin/src/hooks/useLicenseLimits.mjs
var React = __toESM(require_react(), 1);
function useLicenseLimits({ enabled } = {
  enabled: true
}) {
  var _a;
  const { data, isError, isLoading } = useGetLicenseLimitsQuery(void 0, {
    skip: !enabled
  });
  const getFeature = React.useCallback((name) => {
    var _a2;
    const feature = (_a2 = data == null ? void 0 : data.data) == null ? void 0 : _a2.features.find((feature2) => feature2.name === name);
    if (feature && "options" in feature) {
      return feature.options;
    } else {
      return {};
    }
  }, [
    data
  ]);
  return {
    license: data == null ? void 0 : data.data,
    getFeature,
    isError,
    isLoading,
    isTrial: ((_a = data == null ? void 0 : data.data) == null ? void 0 : _a.isTrial) ?? false
  };
}

export {
  useLicenseLimits
};
//# sourceMappingURL=chunk-53OG7EL5.js.map
