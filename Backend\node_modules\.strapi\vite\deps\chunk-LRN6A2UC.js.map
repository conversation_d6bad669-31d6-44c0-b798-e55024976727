{"version": 3, "sources": ["../../../@strapi/admin/admin/src/components/GradientBadge.tsx"], "sourcesContent": ["import { Badge, Flex, Typography } from '@strapi/design-system';\nimport { Lightning } from '@strapi/icons';\nimport { styled } from 'styled-components';\n\nconst GradientBadge = styled(Badge)`\n  background: linear-gradient(\n    90deg,\n    ${({ theme }) => theme.colors.primary600} 0%,\n    ${({ theme }) => theme.colors.alternative600} 121.48%\n  );\n  padding: 0.4rem 1rem;\n`;\n\nconst GradientBadgeWithIcon = ({ label }: { label: string }) => {\n  return (\n    <GradientBadge>\n      <Flex gap={1} alignItems=\"center\">\n        <Lightning width={16} height={16} fill=\"neutral0\" />\n        <Typography textColor=\"neutral0\">{label}</Typography>\n      </Flex>\n    </GradientBadge>\n  );\n};\n\nexport { GradientBadgeWithIcon as GradientBadge };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAIA,IAAMA,gBAAgBC,GAAOC,KAAAA;;;MAGvB,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAOC,UAAU;MACtC,CAAC,EAAEF,MAAK,MAAOA,MAAMC,OAAOE,cAAc;;;;AAKhD,IAAMC,wBAAwB,CAAC,EAAEC,MAAK,MAAqB;AACzD,aACEC,wBAACT,eAAAA;IACC,cAAAU,yBAACC,MAAAA;MAAKC,KAAK;MAAGC,YAAW;;YACvBJ,wBAACK,eAAAA;UAAUC,OAAO;UAAIC,QAAQ;UAAIC,MAAK;;YACvCR,wBAACS,YAAAA;UAAWC,WAAU;UAAYX,UAAAA;;;;;AAI1C;", "names": ["GradientBadge", "styled", "Badge", "theme", "colors", "primary600", "alternative600", "GradientBadgeWithIcon", "label", "_jsx", "_jsxs", "Flex", "gap", "alignItems", "Lightning", "width", "height", "fill", "Typography", "textColor"]}