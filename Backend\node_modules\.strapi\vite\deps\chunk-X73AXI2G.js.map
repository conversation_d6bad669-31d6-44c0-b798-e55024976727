{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/components/ConfigurationForm/EditFieldForm.tsx", "../../../@strapi/content-manager/admin/src/components/ConfigurationForm/Fields.tsx", "../../../@strapi/content-manager/admin/src/components/ConfigurationForm/Form.tsx"], "sourcesContent": ["import {\n  useNotification,\n  InputRenderer,\n  Form,\n  InputProps,\n  useField,\n} from '@strapi/admin/strapi-admin';\nimport { Button, Flex, Grid, Modal } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport * as yup from 'yup';\n\nimport { ATTRIBUTE_TYPES_THAT_CANNOT_BE_MAIN_FIELD } from '../../constants/attributes';\nimport { useGetInitialDataQuery } from '../../services/init';\nimport { capitalise } from '../../utils/strings';\nimport { getTranslation } from '../../utils/translations';\nimport { FieldTypeIcon } from '../FieldTypeIcon';\n\nimport { TEMP_FIELD_NAME } from './Fields';\n\nimport type { ConfigurationFormData } from './Form';\nimport type { Schema } from '@strapi/types';\n\n/* -------------------------------------------------------------------------------------------------\n * Constants\n * -----------------------------------------------------------------------------------------------*/\n\nconst FIELD_SCHEMA = yup.object().shape({\n  label: yup.string().required().nullable(),\n  description: yup.string(),\n  editable: yup.boolean(),\n  size: yup.number().required(),\n});\n\n/* -------------------------------------------------------------------------------------------------\n * EditFieldForm\n * -----------------------------------------------------------------------------------------------*/\n\ninterface EditFieldFormProps {\n  attribute?: Schema.Attribute.AnyAttribute;\n  name: string;\n  onClose: () => void;\n}\n\nconst EditFieldForm = ({ attribute, name, onClose }: EditFieldFormProps) => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n\n  const { value, onChange } =\n    useField<ConfigurationFormData['layout'][number]['children'][number]>(name);\n\n  const { data: mainFieldOptions } = useGetInitialDataQuery(undefined, {\n    selectFromResult: (res) => {\n      if (attribute?.type !== 'relation' || !res.data) {\n        return { data: [] };\n      }\n\n      if ('targetModel' in attribute && typeof attribute.targetModel === 'string') {\n        const targetSchema = res.data.contentTypes.find(\n          (schema) => schema.uid === attribute.targetModel\n        );\n\n        if (targetSchema) {\n          return {\n            data: Object.entries(targetSchema.attributes).reduce<\n              Array<{ label: string; value: string }>\n            >((acc, [key, attribute]) => {\n              /**\n               * Create the list of attributes from the schema as to which can\n               * be our `mainField` and dictate the display name of the schema\n               * we're editing.\n               */\n              if (!ATTRIBUTE_TYPES_THAT_CANNOT_BE_MAIN_FIELD.includes(attribute.type)) {\n                acc.push({\n                  label: key,\n                  value: key,\n                });\n              }\n\n              return acc;\n            }, []),\n          };\n        }\n      }\n\n      return { data: [] };\n    },\n    skip: attribute?.type !== 'relation',\n  });\n\n  if (!value || value.name === TEMP_FIELD_NAME || !attribute) {\n    // This is very unlikely to happen, but it ensures the form is not opened without a value.\n    console.error(\n      \"You've opened a field to edit without it being part of the form, this is likely a bug with Strapi. Please open an issue.\"\n    );\n\n    toggleNotification({\n      message: formatMessage({\n        id: 'content-manager.containers.edit-settings.modal-form.error',\n        defaultMessage: 'An error occurred while trying to open the form.',\n      }),\n      type: 'danger',\n    });\n\n    return null;\n  }\n\n  return (\n    <Modal.Content>\n      <Form\n        method=\"PUT\"\n        initialValues={value}\n        validationSchema={FIELD_SCHEMA}\n        onSubmit={(data) => {\n          onChange(name, data);\n          onClose();\n        }}\n      >\n        <Modal.Header>\n          <Flex gap={3}>\n            <FieldTypeIcon type={attribute.type} />\n            <Modal.Title>\n              {formatMessage(\n                {\n                  id: 'content-manager.containers.edit-settings.modal-form.label',\n                  defaultMessage: 'Edit {fieldName}',\n                },\n                { fieldName: capitalise(value.name) }\n              )}\n            </Modal.Title>\n          </Flex>\n        </Modal.Header>\n        <Modal.Body>\n          <Grid.Root gap={4}>\n            {[\n              {\n                name: 'label',\n                label: formatMessage({\n                  id: getTranslation('containers.edit-settings.modal-form.label'),\n                  defaultMessage: 'Label',\n                }),\n                size: 6,\n                type: 'string' as const,\n              },\n              {\n                name: 'description',\n                label: formatMessage({\n                  id: getTranslation('containers.edit-settings.modal-form.description'),\n                  defaultMessage: 'Description',\n                }),\n                size: 6,\n                type: 'string' as const,\n              },\n              {\n                name: 'placeholder',\n                label: formatMessage({\n                  id: getTranslation('containers.edit-settings.modal-form.placeholder'),\n                  defaultMessage: 'Placeholder',\n                }),\n                size: 6,\n                type: 'string' as const,\n              },\n              {\n                name: 'editable',\n                label: formatMessage({\n                  id: getTranslation('containers.edit-settings.modal-form.editable'),\n                  defaultMessage: 'Editable',\n                }),\n                size: 6,\n                type: 'boolean' as const,\n              },\n              {\n                name: 'mainField',\n                label: formatMessage({\n                  id: getTranslation('containers.edit-settings.modal-form.mainField'),\n                  defaultMessage: 'Entry title',\n                }),\n                hint: formatMessage({\n                  id: getTranslation(\n                    'containers.SettingPage.edit-settings.modal-form.mainField.hint'\n                  ),\n                  defaultMessage: 'Set the displayed field',\n                }),\n                size: 6,\n                options: mainFieldOptions,\n                type: 'enumeration' as const,\n              },\n              {\n                name: 'size',\n                label: formatMessage({\n                  id: getTranslation('containers.ListSettingsView.modal-form.size'),\n                  defaultMessage: 'Size',\n                }),\n                size: 6,\n                options: [\n                  { value: '4', label: '33%' },\n                  { value: '6', label: '50%' },\n                  { value: '8', label: '66%' },\n                  { value: '12', label: '100%' },\n                ],\n                type: 'enumeration' as const,\n              },\n            ]\n              .filter(filterFieldsBasedOnAttributeType(attribute.type))\n              .map(({ size, ...field }) => (\n                <Grid.Item key={field.name} col={size} direction=\"column\" alignItems=\"stretch\">\n                  <InputRenderer {...field} />\n                </Grid.Item>\n              ))}\n          </Grid.Root>\n        </Modal.Body>\n        <Modal.Footer>\n          <Modal.Close>\n            <Button variant=\"tertiary\">\n              {formatMessage({ id: 'app.components.Button.cancel', defaultMessage: 'Cancel' })}\n            </Button>\n          </Modal.Close>\n          <Button type=\"submit\">\n            {formatMessage({ id: 'global.finish', defaultMessage: 'Finish' })}\n          </Button>\n        </Modal.Footer>\n      </Form>\n    </Modal.Content>\n  );\n};\n\n/**\n * @internal\n * @description not all edit fields have the same editable properties, it depends on the type\n * e.g. a dynamic zone can only change it's label.\n */\nconst filterFieldsBasedOnAttributeType = (type: Schema.Attribute.Kind) => (field: InputProps) => {\n  switch (type) {\n    case 'blocks':\n    case 'richtext':\n      return field.name !== 'size' && field.name !== 'mainField';\n    case 'boolean':\n    case 'media':\n      return field.name !== 'placeholder' && field.name !== 'mainField';\n    case 'component':\n    case 'dynamiczone':\n      return field.name === 'label' || field.name === 'editable';\n    case 'json':\n      return field.name !== 'placeholder' && field.name !== 'mainField' && field.name !== 'size';\n    case 'relation':\n      return true;\n    default:\n      return field.name !== 'mainField';\n  }\n};\n\nexport { EditFieldForm };\nexport type { EditFieldFormProps };\n", "import * as React from 'react';\n\nimport { useDroppable, DndContext, UniqueIdentifier, DragOverlay } from '@dnd-kit/core';\nimport { arrayMove, SortableContext, useSortable } from '@dnd-kit/sortable';\nimport { CSS } from '@dnd-kit/utilities';\nimport { useField, useForm } from '@strapi/admin/strapi-admin';\nimport {\n  Modal,\n  Box,\n  Flex,\n  Grid,\n  IconButton,\n  IconButtonComponent,\n  Typography,\n  Link,\n  Menu,\n} from '@strapi/design-system';\nimport { Cog, Cross, Drag, Pencil, Plus } from '@strapi/icons';\nimport { generateNKeysBetween as generateNKeysBetweenImpl } from 'fractional-indexing';\nimport { produce } from 'immer';\nimport { useIntl } from 'react-intl';\nimport { NavLink } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { getTranslation } from '../../utils/translations';\nimport { ComponentIcon } from '../ComponentIcon';\n\nimport { EditFieldForm, EditFieldFormProps } from './EditFieldForm';\n\nimport type { ConfigurationFormData, EditFieldSpacerLayout } from './Form';\nimport type { EditLayout } from '../../hooks/useDocumentLayout';\n\ntype FormField = ConfigurationFormData['layout'][number]['children'][number];\ntype Field = Omit<ConfigurationFormData['layout'][number]['children'][number], '__temp_key__'>;\n\nconst GRID_COLUMNS = 12;\n\n/* -------------------------------------------------------------------------------------------------\n * Drag and Drop\n * -----------------------------------------------------------------------------------------------*/\n\nconst DroppableContainer = ({\n  id,\n  children,\n}: {\n  id: string;\n  children: (props: ReturnType<typeof useDroppable>) => React.ReactNode;\n}) => {\n  const droppable = useDroppable({\n    id,\n  });\n\n  return children(droppable);\n};\n\nexport const SortableItem = ({ id, children }: { id: string; children: React.ReactNode }) => {\n  const { attributes, setNodeRef, transform, transition } = useSortable({\n    id,\n  });\n\n  const style = {\n    transform: CSS.Transform.toString(transform),\n    transition,\n    height: '100%',\n  };\n\n  return (\n    <div ref={setNodeRef} style={style} {...attributes}>\n      {children}\n    </div>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Fields\n * -----------------------------------------------------------------------------------------------*/\n\ninterface FieldsProps extends Pick<EditLayout, 'metadatas'>, Pick<FieldProps, 'components'> {\n  attributes: {\n    [key: string]: FieldProps['attribute'];\n  };\n  fieldSizes: Record<string, number>;\n  components: EditLayout['components'];\n}\n\n/**\n * Compute uids and formName for drag and drop items for the incoming layout\n */\nconst createDragAndDropContainersFromLayout = (layout: ConfigurationFormData['layout']) => {\n  return layout.map((row, containerIndex) => ({\n    ...row,\n    // Use unique ids for drag and drop items\n    dndId: `container-${containerIndex}`,\n    children: row.children.map((child, childIndex) => ({\n      ...child,\n      dndId: `container-${containerIndex}-child-${childIndex}`,\n\n      // The formName must be recomputed each time an item is moved\n      formName: `layout.${containerIndex}.children.${childIndex}`,\n    })),\n  }));\n};\n\nconst Fields = ({ attributes, fieldSizes, components, metadatas = {} }: FieldsProps) => {\n  const { formatMessage } = useIntl();\n\n  const layout = useForm<ConfigurationFormData['layout']>(\n    'Fields',\n    (state) => state.values.layout ?? []\n  );\n\n  const onChange = useForm('Fields', (state) => state.onChange);\n  const addFieldRow = useForm('Fields', (state) => state.addFieldRow);\n  const removeFieldRow = useForm('Fields', (state) => state.removeFieldRow);\n\n  const existingFields = layout.map((row) => row.children.map((field) => field.name)).flat();\n\n  /**\n   * Get the fields that are not already in the layout\n   * But also check that they are visible before we give users\n   * the option to display them. e.g. `id` is not visible.\n   */\n  const remainingFields = Object.entries(metadatas).reduce<Field[]>((acc, current) => {\n    const [name, { visible, ...field }] = current;\n\n    if (!existingFields.includes(name) && visible === true) {\n      const type = attributes[name]?.type;\n      const size = type ? fieldSizes[type] : GRID_COLUMNS;\n\n      acc.push({\n        ...field,\n        label: field.label ?? name,\n        name,\n        size,\n      });\n    }\n\n    return acc;\n  }, []);\n\n  const handleRemoveField =\n    (rowIndex: number, fieldIndex: number): FieldProps['onRemoveField'] =>\n    () => {\n      if (layout[rowIndex].children.length === 1) {\n        removeFieldRow(`layout`, rowIndex);\n      } else {\n        onChange(`layout.${rowIndex}.children`, [\n          ...layout[rowIndex].children.slice(0, fieldIndex),\n          ...layout[rowIndex].children.slice(fieldIndex + 1),\n        ]);\n      }\n    };\n\n  const handleAddField = (field: Field) => () => {\n    addFieldRow('layout', { children: [field] });\n  };\n\n  const [containers, setContainers] = React.useState(() =>\n    createDragAndDropContainersFromLayout(layout)\n  );\n  type Container = (typeof containers)[number];\n  const [activeDragItem, setActiveDragItem] = React.useState<Container['children'][number] | null>(\n    null\n  );\n\n  /**\n   * Finds either the parent container id or the child id within a container\n   */\n  function findContainer(id: UniqueIdentifier, containersAsDictionary: Record<string, Container>) {\n    // If the id is a key, then it is the parent container\n    if (id in containersAsDictionary) {\n      return id;\n    }\n\n    // Otherwise, it is a child inside a container\n    return Object.keys(containersAsDictionary).find((key) =>\n      containersAsDictionary[key].children.find((child) => child.dndId === id)\n    );\n  }\n\n  /**\n   * Gets an item from a container based on its id\n   */\n  const getItemFromContainer = (id: UniqueIdentifier, container: Container) => {\n    return container.children.find((item) => id === item.dndId);\n  };\n\n  /**\n   * Gets the containers as dictionary for quick lookup\n   */\n  const getContainersAsDictionary = () => {\n    return Object.fromEntries(containers.map((container) => [container.dndId, container]));\n  };\n\n  /**\n   * Recomputes the empty space in the grid\n   */\n  const createContainersWithSpacers = (layout: typeof containers) => {\n    return layout\n      .map((row) => ({\n        ...row,\n        children: row.children.filter((field) => field.name !== TEMP_FIELD_NAME),\n      }))\n      .filter((row) => row.children.length > 0)\n      .map((row) => {\n        const totalSpaceTaken = row.children.reduce((acc, curr) => acc + curr.size, 0);\n\n        if (totalSpaceTaken < GRID_COLUMNS) {\n          const [spacerKey] = generateNKeysBetweenImpl(\n            row.children.at(-1)?.__temp_key__,\n            undefined,\n            1\n          );\n\n          return {\n            ...row,\n            children: [\n              ...row.children,\n              {\n                name: TEMP_FIELD_NAME,\n                size: GRID_COLUMNS - totalSpaceTaken,\n                __temp_key__: spacerKey,\n              } satisfies EditFieldSpacerLayout,\n            ],\n          };\n        }\n\n        return row;\n      });\n  };\n\n  /**\n   * When layout changes (e.g. when a field size is changed or the containers are reordered)\n   * we need to update the ids and form names\n   */\n  React.useEffect(() => {\n    const containers = createDragAndDropContainersFromLayout(layout);\n    setContainers(containers);\n  }, [layout, setContainers]);\n\n  return (\n    <DndContext\n      onDragStart={(event) => {\n        const containersAsDictionary = getContainersAsDictionary();\n\n        const activeContainer = findContainer(event.active.id, containersAsDictionary);\n\n        if (!activeContainer) return;\n\n        const activeItem = getItemFromContainer(\n          event.active.id,\n          containersAsDictionary[activeContainer]\n        );\n\n        if (activeItem) {\n          setActiveDragItem(activeItem);\n        }\n      }}\n      onDragOver={({ active, over }) => {\n        const containersAsDictionary = getContainersAsDictionary();\n        const activeContainer = findContainer(active.id, containersAsDictionary);\n        const overContainer = findContainer(over?.id ?? '', containersAsDictionary);\n        const activeContainerIndex = containers.findIndex(\n          (container) => container.dndId === activeContainer\n        );\n        const overContainerIndex = containers.findIndex(\n          (container) => container.dndId === overContainer\n        );\n\n        if (!activeContainer || !overContainer) {\n          return;\n        }\n\n        const draggedItem = getItemFromContainer(\n          active.id,\n          containersAsDictionary[activeContainer]\n        );\n        const overItem = getItemFromContainer(\n          over?.id ?? '',\n          containersAsDictionary[overContainer]\n        );\n        const overIndex = containersAsDictionary[overContainer].children.findIndex(\n          (item) => item.dndId === over?.id\n        );\n\n        if (!draggedItem) return;\n\n        // Handle a full width field being dragged\n        if (draggedItem?.size === GRID_COLUMNS) {\n          // Swap the items in the containers\n          const update = produce(containers, (draft) => {\n            draft[activeContainerIndex].children = containers[overContainerIndex].children;\n            draft[overContainerIndex].children = containers[activeContainerIndex].children;\n          });\n          setContainers(update);\n          return;\n        }\n\n        /**\n         * Handle an item being dragged from one container to another,\n         * the item is removed from its current container, and then added to its new container\n         * An item can only be added in a container if there is enough space.\n         */\n        const update = produce(containers, (draft) => {\n          draft[activeContainerIndex].children = draft[activeContainerIndex].children.filter(\n            (item) => item.dndId !== active.id\n          );\n          const spaceTaken = draft[overContainerIndex].children.reduce((acc, curr) => {\n            if (curr.name === TEMP_FIELD_NAME) {\n              return acc;\n            }\n\n            return acc + curr.size;\n          }, 0);\n\n          // Check the sizes of the children, if there is no room, exit\n          if (spaceTaken + draggedItem.size > GRID_COLUMNS) {\n            // Leave the item where it started\n            draft[activeContainerIndex].children = containers[activeContainerIndex].children;\n            return;\n          }\n\n          if (overItem?.name === TEMP_FIELD_NAME) {\n            // We are over an invisible spacer, replace it with the dragged item\n            draft[overContainerIndex].children.splice(overIndex, 1, draggedItem);\n            return;\n          }\n\n          // There is room for the item in the container, drop it\n          draft[overContainerIndex].children.splice(overIndex, 0, draggedItem);\n        });\n\n        setContainers(update);\n      }}\n      onDragEnd={(event) => {\n        const { active, over } = event;\n        const { id } = active;\n        const overId = over?.id;\n        const containersAsDictionary = getContainersAsDictionary();\n        const activeContainer = findContainer(id, containersAsDictionary);\n        const overContainer = findContainer(overId!, containersAsDictionary);\n\n        if (!activeContainer || !overContainer) {\n          return;\n        }\n\n        const activeIndex = containersAsDictionary[activeContainer].children.findIndex(\n          (children) => children.dndId === id\n        );\n        const overIndex = containersAsDictionary[overContainer].children.findIndex(\n          (children) => children.dndId === overId\n        );\n\n        const movedContainerItems = produce(containersAsDictionary, (draft) => {\n          if (activeIndex !== overIndex && activeContainer === overContainer) {\n            // Move items around inside their own container\n            draft[activeContainer].children = arrayMove(\n              draft[activeContainer].children,\n              activeIndex,\n              overIndex\n            );\n          }\n        });\n\n        // Remove properties the server does not expect before updating the form\n        const updatedContainers = Object.values(movedContainerItems);\n        const updatedContainersWithSpacers = createContainersWithSpacers(\n          updatedContainers\n        ) as typeof containers;\n        const updatedLayout = updatedContainersWithSpacers.map(\n          ({ dndId: _dndId, children, ...container }) => ({\n            ...container,\n            children: children.map(({ dndId: _dndId, formName: _formName, ...child }) => child),\n          })\n        );\n\n        // Update the layout\n        onChange('layout', updatedLayout);\n        setActiveDragItem(null);\n      }}\n    >\n      <Flex paddingTop={6} direction=\"column\" alignItems=\"stretch\" gap={4}>\n        <Flex alignItems=\"flex-start\" direction=\"column\" justifyContent=\"space-between\">\n          <Typography fontWeight=\"bold\">\n            {formatMessage({\n              id: getTranslation('containers.list.displayedFields'),\n              defaultMessage: 'Displayed fields',\n            })}\n          </Typography>\n          <Typography variant=\"pi\" textColor=\"neutral600\">\n            {formatMessage({\n              id: 'containers.SettingPage.editSettings.description',\n              defaultMessage: 'Drag & drop the fields to build the layout',\n            })}\n          </Typography>\n        </Flex>\n        <Box padding={4} hasRadius borderStyle=\"dashed\" borderWidth=\"1px\" borderColor=\"neutral300\">\n          <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n            {containers.map((container, containerIndex) => (\n              <SortableContext\n                key={container.dndId}\n                id={container.dndId}\n                items={container.children.map((child) => ({ id: child.dndId }))}\n              >\n                <DroppableContainer id={container.dndId}>\n                  {({ setNodeRef }) => (\n                    <Grid.Root key={container.dndId} ref={setNodeRef} gap={2}>\n                      {container.children.map((child, childIndex) => (\n                        <Grid.Item\n                          col={child.size}\n                          key={child.dndId}\n                          direction=\"column\"\n                          alignItems=\"stretch\"\n                        >\n                          <SortableItem id={child.dndId}>\n                            <Field\n                              attribute={attributes[child.name]}\n                              components={components}\n                              name={child.formName}\n                              onRemoveField={handleRemoveField(containerIndex, childIndex)}\n                              dndId={child.dndId}\n                            />\n                          </SortableItem>\n                        </Grid.Item>\n                      ))}\n                    </Grid.Root>\n                  )}\n                </DroppableContainer>\n              </SortableContext>\n            ))}\n            <DragOverlay>\n              {activeDragItem ? (\n                <Field\n                  attribute={attributes[activeDragItem.name]}\n                  components={components}\n                  name={activeDragItem.formName}\n                  dndId={activeDragItem.dndId}\n                />\n              ) : null}\n            </DragOverlay>\n            <Menu.Root>\n              <Menu.Trigger\n                startIcon={<Plus />}\n                endIcon={null}\n                disabled={remainingFields.length === 0}\n                fullWidth\n                variant=\"secondary\"\n              >\n                {formatMessage({\n                  id: getTranslation('containers.SettingPage.add.field'),\n                  defaultMessage: 'Insert another field',\n                })}\n              </Menu.Trigger>\n              <Menu.Content>\n                {remainingFields.map((field) => (\n                  <Menu.Item key={field.name} onSelect={handleAddField(field)}>\n                    {field.label}\n                  </Menu.Item>\n                ))}\n              </Menu.Content>\n            </Menu.Root>\n          </Flex>\n        </Box>\n      </Flex>\n    </DndContext>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Field\n * -----------------------------------------------------------------------------------------------*/\n\ninterface FieldProps extends Pick<EditFieldFormProps, 'name' | 'attribute'> {\n  components: EditLayout['components'];\n  dndId: string;\n  onRemoveField?: React.MouseEventHandler<HTMLButtonElement>;\n}\n\nconst TEMP_FIELD_NAME = '_TEMP_';\n\n/**\n * Displays a field in the layout with drag options, also\n * opens a modal  to edit the details of said field.\n */\nconst Field = ({ attribute, components, name, onRemoveField, dndId }: FieldProps) => {\n  const [isModalOpen, setIsModalOpen] = React.useState(false);\n  const { formatMessage } = useIntl();\n  const { value } = useField<FormField>(name);\n  const { listeners, setActivatorNodeRef } = useSortable({\n    id: dndId,\n  });\n\n  const handleRemoveField: React.MouseEventHandler<HTMLButtonElement> = (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (onRemoveField) {\n      onRemoveField?.(e);\n    }\n  };\n\n  const onEditFieldMeta: React.MouseEventHandler<HTMLButtonElement> = (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsModalOpen(true);\n  };\n\n  if (!value) {\n    return null;\n  }\n\n  if (value.name === TEMP_FIELD_NAME) {\n    return <Flex tag=\"span\" height=\"100%\" style={{ opacity: 0 }} />;\n  }\n\n  if (!attribute) {\n    return null;\n  }\n\n  return (\n    <Modal.Root open={isModalOpen} onOpenChange={setIsModalOpen}>\n      <Flex\n        borderColor=\"neutral150\"\n        background=\"neutral100\"\n        hasRadius\n        gap={3}\n        cursor=\"pointer\"\n        onClick={() => {\n          setIsModalOpen(true);\n        }}\n      >\n        <DragButton\n          ref={setActivatorNodeRef}\n          tag=\"span\"\n          withTooltip={false}\n          label={formatMessage(\n            {\n              id: getTranslation('components.DraggableCard.move.field'),\n              defaultMessage: 'Move {item}',\n            },\n            { item: value.label }\n          )}\n          {...listeners}\n        >\n          <Drag />\n        </DragButton>\n        <Flex direction=\"column\" alignItems=\"flex-start\" grow={1} overflow=\"hidden\">\n          <Flex gap={3} justifyContent=\"space-between\" width=\"100%\">\n            <Typography ellipsis fontWeight=\"bold\">\n              {value.label}\n            </Typography>\n            <Flex>\n              <IconButton\n                type=\"button\"\n                variant=\"ghost\"\n                background=\"transparent\"\n                onClick={onEditFieldMeta}\n                withTooltip={false}\n                label={formatMessage(\n                  {\n                    id: getTranslation('components.DraggableCard.edit.field'),\n                    defaultMessage: 'Edit {item}',\n                  },\n                  { item: value.label }\n                )}\n              >\n                <Pencil />\n              </IconButton>\n              <IconButton\n                type=\"button\"\n                variant=\"ghost\"\n                onClick={handleRemoveField}\n                background=\"transparent\"\n                withTooltip={false}\n                label={formatMessage(\n                  {\n                    id: getTranslation('components.DraggableCard.delete.field'),\n                    defaultMessage: 'Delete {item}',\n                  },\n                  { item: value.label }\n                )}\n              >\n                <Cross />\n              </IconButton>\n            </Flex>\n          </Flex>\n          {attribute?.type === 'component' ? (\n            <Flex\n              paddingTop={3}\n              paddingRight={3}\n              paddingBottom={3}\n              paddingLeft={0}\n              alignItems=\"flex-start\"\n              direction=\"column\"\n              gap={2}\n              width=\"100%\"\n            >\n              <Grid.Root gap={4} width=\"100%\">\n                {components[attribute.component].layout.map((row) =>\n                  row.map(({ size, ...field }) => (\n                    <Grid.Item key={field.name} col={size} direction=\"column\" alignItems=\"stretch\">\n                      <Flex\n                        alignItems=\"center\"\n                        background=\"neutral0\"\n                        paddingTop={2}\n                        paddingBottom={2}\n                        paddingLeft={3}\n                        paddingRight={3}\n                        hasRadius\n                        borderColor=\"neutral200\"\n                      >\n                        <Typography textColor=\"neutral800\">{field.name}</Typography>\n                      </Flex>\n                    </Grid.Item>\n                  ))\n                )}\n              </Grid.Root>\n              <Link\n                // used to stop the edit form from appearing when we click here.\n                onClick={(e) => e.stopPropagation()}\n                startIcon={<Cog />}\n                tag={NavLink}\n                to={`../components/${attribute.component}/configurations/edit`}\n              >\n                {formatMessage({\n                  id: getTranslation('components.FieldItem.linkToComponentLayout'),\n                  defaultMessage: \"Set the component's layout\",\n                })}\n              </Link>\n            </Flex>\n          ) : null}\n          {attribute?.type === 'dynamiczone' ? (\n            <Flex\n              paddingTop={3}\n              paddingRight={3}\n              paddingBottom={3}\n              paddingLeft={0}\n              alignItems=\"flex-start\"\n              gap={2}\n              width=\"100%\"\n              wrap=\"wrap\"\n            >\n              {attribute?.components.map((uid) => (\n                <ComponentLink\n                  // used to stop the edit form from appearing when we click here.\n                  onClick={(e) => e.stopPropagation()}\n                  key={uid}\n                  to={`../components/${uid}/configurations/edit`}\n                >\n                  <ComponentIcon icon={components[uid].settings.icon} />\n                  <Typography fontSize={1} textColor=\"neutral600\" fontWeight=\"bold\">\n                    {components[uid].settings.displayName}\n                  </Typography>\n                </ComponentLink>\n              ))}\n            </Flex>\n          ) : null}\n        </Flex>\n      </Flex>\n      {value.name !== TEMP_FIELD_NAME && (\n        <EditFieldForm attribute={attribute} name={name} onClose={() => setIsModalOpen(false)} />\n      )}\n    </Modal.Root>\n  );\n};\n\nconst DragButton = styled<IconButtonComponent<'span'>>(IconButton)`\n  height: unset;\n  align-self: stretch;\n  display: flex;\n  align-items: center;\n  padding: 0;\n  border: none;\n  background-color: transparent;\n  border-radius: 0px;\n  border-right: 1px solid ${({ theme }) => theme.colors.neutral150};\n  cursor: all-scroll;\n\n  svg {\n    width: 1.2rem;\n    height: 1.2rem;\n  }\n`;\n\nconst ComponentLink = styled(NavLink)`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: ${({ theme }) => theme.spaces[1]};\n  padding: ${(props) => props.theme.spaces[2]};\n  border: 1px solid ${({ theme }) => theme.colors.neutral200};\n  background: ${({ theme }) => theme.colors.neutral0};\n  width: 14rem;\n  border-radius: ${({ theme }) => theme.borderRadius};\n  text-decoration: none;\n\n  &:focus,\n  &:hover {\n    ${({ theme }) => `\n      background-color: ${theme.colors.primary100};\n      border-color: ${theme.colors.primary200};\n\n      ${Typography} {\n          color: ${theme.colors.primary600};\n      }\n    `}\n\n    /* > ComponentIcon */\n    > div:first-child {\n      background: ${({ theme }) => theme.colors.primary200};\n      color: ${({ theme }) => theme.colors.primary600};\n\n      svg {\n        path {\n          fill: ${({ theme }) => theme.colors.primary600};\n        }\n      }\n    }\n  }\n`;\n\nexport { Fields, TEMP_FIELD_NAME };\nexport type { FieldsProps };\n", "import * as React from 'react';\n\nimport {\n  Form,\n  FormProps,\n  useForm,\n  Input<PERSON><PERSON>er,\n  <PERSON><PERSON><PERSON>on,\n  Layouts,\n} from '@strapi/admin/strapi-admin';\nimport { But<PERSON>, Divider, Flex, Grid, Main, Typography } from '@strapi/design-system';\nimport { generateNKeysBetween } from 'fractional-indexing';\nimport pipe from 'lodash/fp/pipe';\nimport { useIntl } from 'react-intl';\n\nimport { ATTRIBUTE_TYPES_THAT_CANNOT_BE_MAIN_FIELD } from '../../constants/attributes';\nimport { capitalise } from '../../utils/strings';\nimport { getTranslation } from '../../utils/translations';\n\nimport { Fields, FieldsProps, TEMP_FIELD_NAME } from './Fields';\n\nimport type { EditFieldLayout, EditLayout } from '../../hooks/useDocumentLayout';\n\n/* -------------------------------------------------------------------------------------------------\n * ConfigurationForm\n * -----------------------------------------------------------------------------------------------*/\n\ninterface ConfigurationFormProps extends Pick<FieldsProps, 'attributes' | 'fieldSizes'> {\n  layout: EditLayout;\n  onSubmit: FormProps<ConfigurationFormData>['onSubmit'];\n}\n\n/**\n * Every key in EditFieldLayout is turned to optional never and then we overwrite the ones we are using.\n */\n\ntype EditFieldSpacerLayout = {\n  [key in keyof Omit<EditFieldLayout, 'name' | 'size'>]?: never;\n} & {\n  description?: never;\n  editable?: never;\n  name: '_TEMP_';\n  size: number;\n  __temp_key__: string;\n};\n\ninterface ConfigurationFormData extends Pick<EditLayout, 'settings'> {\n  layout: Array<{\n    __temp_key__: string;\n    children: Array<\n      | (Pick<EditFieldLayout, 'label' | 'size' | 'name' | 'placeholder' | 'mainField'> & {\n          description: EditFieldLayout['hint'];\n          editable: EditFieldLayout['disabled'];\n          __temp_key__: string;\n        })\n      | EditFieldSpacerLayout\n    >;\n  }>;\n}\n\nconst ConfigurationForm = ({\n  attributes,\n  fieldSizes,\n  layout: editLayout,\n  onSubmit,\n}: ConfigurationFormProps) => {\n  const { components, settings, layout, metadatas } = editLayout;\n\n  const { formatMessage } = useIntl();\n\n  const initialValues: ConfigurationFormData = React.useMemo(() => {\n    const transformations = pipe(\n      flattenPanels,\n      replaceMainFieldWithNameOnly,\n      extractMetadata,\n      addTmpSpaceToLayout,\n      addTmpKeysToLayout\n    );\n\n    return {\n      layout: transformations(layout),\n      settings,\n    };\n  }, [layout, settings]);\n\n  return (\n    <Layouts.Root>\n      <Main>\n        <Form initialValues={initialValues} onSubmit={onSubmit} method=\"PUT\">\n          <Header name={settings.displayName ?? ''} />\n          <Layouts.Content>\n            <Flex\n              alignItems=\"stretch\"\n              background=\"neutral0\"\n              direction=\"column\"\n              gap={6}\n              hasRadius\n              shadow=\"tableShadow\"\n              paddingTop={6}\n              paddingBottom={6}\n              paddingLeft={7}\n              paddingRight={7}\n            >\n              <Typography variant=\"delta\" tag=\"h2\">\n                {formatMessage({\n                  id: getTranslation('containers.SettingPage.settings'),\n                  defaultMessage: 'Settings',\n                })}\n              </Typography>\n              <Grid.Root>\n                <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"stretch\">\n                  <InputRenderer\n                    type=\"enumeration\"\n                    label={formatMessage({\n                      id: getTranslation('containers.SettingPage.editSettings.entry.title'),\n                      defaultMessage: 'Entry title',\n                    })}\n                    hint={formatMessage({\n                      id: getTranslation(\n                        'containers.SettingPage.editSettings.entry.title.description'\n                      ),\n                      defaultMessage: 'Set the display field of your entry',\n                    })}\n                    name=\"settings.mainField\"\n                    options={Object.entries(attributes).reduce<\n                      Array<{ label: string; value: string }>\n                    >((acc, [key, attribute]) => {\n                      if (!attribute) {\n                        return acc;\n                      }\n\n                      /**\n                       * Create the list of attributes from the schema as to which can\n                       * be our `mainField` and dictate the display name of the schema\n                       * we're editing.\n                       */\n                      if (!ATTRIBUTE_TYPES_THAT_CANNOT_BE_MAIN_FIELD.includes(attribute.type)) {\n                        acc.push({\n                          label: key,\n                          value: key,\n                        });\n                      }\n\n                      return acc;\n                    }, [])}\n                  />\n                </Grid.Item>\n                <Grid.Item\n                  paddingTop={6}\n                  paddingBottom={6}\n                  col={12}\n                  s={12}\n                  direction=\"column\"\n                  alignItems=\"stretch\"\n                >\n                  <Divider />\n                </Grid.Item>\n                <Grid.Item col={12} s={12} direction=\"column\" alignItems=\"stretch\">\n                  <Typography variant=\"delta\" tag=\"h3\">\n                    {formatMessage({\n                      id: getTranslation('containers.SettingPage.view'),\n                      defaultMessage: 'View',\n                    })}\n                  </Typography>\n                </Grid.Item>\n                <Grid.Item col={12} s={12} direction=\"column\" alignItems=\"stretch\">\n                  <Fields\n                    attributes={attributes}\n                    components={components}\n                    fieldSizes={fieldSizes}\n                    metadatas={metadatas}\n                  />\n                </Grid.Item>\n              </Grid.Root>\n            </Flex>\n          </Layouts.Content>\n        </Form>\n      </Main>\n    </Layouts.Root>\n  );\n};\n\n/**\n * @internal\n * @description Panels don't exist in the layout, so we flatten by one.\n */\nconst flattenPanels = (layout: EditLayout['layout']): EditLayout['layout'][number] =>\n  layout.flat(1);\n\n/**\n * @internal\n * @description We don't need the mainField object in the layout, we only need the name.\n */\nconst replaceMainFieldWithNameOnly = (layout: EditLayout['layout'][number]) =>\n  layout.map((row) =>\n    row.map((field) => ({\n      ...field,\n      mainField: field.mainField?.name,\n    }))\n  );\n\n/**\n * @internal\n * @description We extract the metadata values from the field layout, because these are editable by the user.\n */\nconst extractMetadata = (\n  layout: EditLayout['layout'][number]\n): Array<Exclude<ConfigurationFormData['layout'], { name: '_TEMP_' }>[number]['children']> => {\n  return layout.map((row) =>\n    row.map(({ label, disabled, hint, placeholder, size, name, mainField }) => ({\n      label,\n      editable: !disabled,\n      description: hint,\n      mainField,\n      placeholder,\n      size,\n      name,\n      __temp_key__: '',\n    }))\n  );\n};\n\n/**\n * @internal\n * @description Each row of the layout has a max size of 12 (based on bootstrap grid system)\n * So in order to offer a better drop zone we add the _TEMP_ div to complete the remaining substract (12 - existing)\n */\nconst addTmpSpaceToLayout = (\n  layout: ReturnType<typeof extractMetadata>\n): Array<ConfigurationFormData['layout'][number]['children']> => [\n  ...layout.map((row) => {\n    const totalSpaceTaken = row.reduce((acc, field) => acc + field.size, 0);\n\n    if (totalSpaceTaken < 12) {\n      return [\n        ...row,\n        {\n          name: TEMP_FIELD_NAME,\n          size: 12 - totalSpaceTaken,\n          __temp_key__: '',\n        } satisfies EditFieldSpacerLayout,\n      ];\n    }\n\n    return row;\n  }),\n];\n\n/**\n * @internal\n * @description At this point of the transformations we have Field[][], but each row for the form should have a __temp_key__\n * applied. This means we need to change it so `Field` is nested under the children property.\n */\nconst addTmpKeysToLayout = (\n  layout: ReturnType<typeof addTmpSpaceToLayout>\n): ConfigurationFormData['layout'] => {\n  const keys = generateNKeysBetween(undefined, undefined, layout.length);\n\n  return layout.map((row, rowIndex) => {\n    const fieldKeys = generateNKeysBetween(undefined, undefined, row.length);\n\n    return {\n      __temp_key__: keys[rowIndex],\n      children: row.map((field, fieldIndex) => {\n        return {\n          ...field,\n          __temp_key__: fieldKeys[fieldIndex],\n        };\n      }),\n    };\n  });\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Header\n * -----------------------------------------------------------------------------------------------*/\n\ninterface HeaderProps {\n  name: string;\n}\n\nconst Header = ({ name }: HeaderProps) => {\n  const { formatMessage } = useIntl();\n  const modified = useForm('Header', (state) => state.modified);\n  const isSubmitting = useForm('Header', (state) => state.isSubmitting);\n\n  return (\n    <Layouts.Header\n      title={formatMessage(\n        {\n          id: getTranslation('components.SettingsViewWrapper.pluginHeader.title'),\n          defaultMessage: `Configure the view - {name}`,\n        },\n        { name: capitalise(name) }\n      )}\n      subtitle={formatMessage({\n        id: getTranslation('components.SettingsViewWrapper.pluginHeader.description.edit-settings'),\n        defaultMessage: 'Customize how the edit view will look like.',\n      })}\n      navigationAction={<BackButton />}\n      primaryAction={\n        <Button disabled={!modified} loading={isSubmitting} type=\"submit\">\n          {formatMessage({ id: 'global.save', defaultMessage: 'Save' })}\n        </Button>\n      }\n    />\n  );\n};\n\nexport { ConfigurationForm };\nexport type { ConfigurationFormProps, ConfigurationFormData, EditFieldSpacerLayout };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,IAAMA,eAAmBC,QAAM,EAAGC,MAAM;EACtCC,OAAWC,QAAM,EAAGC,SAAQ,EAAGC,SAAQ;EACvCC,aAAiBH,QAAM;EACvBI,UAAcC,OAAO;EACrBC,MAAUC,QAAM,EAAGN,SAAQ;AAC7B,CAAA;AAYMO,IAAAA,gBAAgB,CAAC,EAAEC,WAAWC,MAAMC,QAAO,MAAsB;AACrE,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAE/B,QAAM,EAAEC,OAAOC,SAAQ,IACrBC,SAAsER,IAAAA;AAExE,QAAM,EAAES,MAAMC,iBAAgB,IAAKC,uBAAuBC,QAAW;IACnEC,kBAAkB,CAACC,QAAAA;AACjB,WAAIf,uCAAWgB,UAAS,cAAc,CAACD,IAAIL,MAAM;AAC/C,eAAO;UAAEA,MAAM,CAAA;QAAG;MACpB;AAEA,UAAI,iBAAiBV,aAAa,OAAOA,UAAUiB,gBAAgB,UAAU;AAC3E,cAAMC,eAAeH,IAAIL,KAAKS,aAAaC,KACzC,CAACC,WAAWA,OAAOC,QAAQtB,UAAUiB,WAAW;AAGlD,YAAIC,cAAc;AAChB,iBAAO;YACLR,MAAMa,OAAOC,QAAQN,aAAaO,UAAU,EAAEC,OAE5C,CAACC,KAAK,CAACC,KAAK5B,UAAU,MAAA;AAMtB,kBAAI,CAAC6B,0CAA0CC,SAAS9B,WAAUgB,IAAI,GAAG;AACvEW,oBAAII,KAAK;kBACPzC,OAAOsC;kBACPrB,OAAOqB;gBACT,CAAA;cACF;AAEA,qBAAOD;YACT,GAAG,CAAA,CAAE;UACP;QACF;MACF;AAEA,aAAO;QAAEjB,MAAM,CAAA;MAAG;IACpB;IACAsB,OAAMhC,uCAAWgB,UAAS;EAC5B,CAAA;AAEA,MAAI,CAACT,SAASA,MAAMN,SAASgC,mBAAmB,CAACjC,WAAW;AAE1DkC,YAAQC,MACN,0HAAA;AAGF9B,uBAAmB;MACjB+B,SAASjC,cAAc;QACrBkC,IAAI;QACJC,gBAAgB;MAClB,CAAA;MACAtB,MAAM;IACR,CAAA;AAEA,WAAO;EACT;AAEA,aACEuB,wBAACC,MAAMC,SAAO;IACZ,cAAAC,yBAACC,MAAAA;MACCC,QAAO;MACPC,eAAetC;MACfuC,kBAAkB3D;MAClB4D,UAAU,CAACrC,SAAAA;AACTF,iBAASP,MAAMS,IAAAA;AACfR,gBAAAA;MACF;;YAEAqC,wBAACC,MAAMQ,QAAM;UACX,cAAAN,yBAACO,MAAAA;YAAKC,KAAK;;kBACTX,wBAACY,eAAAA;gBAAcnC,MAAMhB,UAAUgB;;kBAC/BuB,wBAACC,MAAMY,OAAK;0BACTjD,cACC;kBACEkC,IAAI;kBACJC,gBAAgB;mBAElB;kBAAEe,WAAWC,WAAW/C,MAAMN,IAAI;gBAAE,CAAA;;;;;YAK5CsC,wBAACC,MAAMe,MAAI;wBACThB,wBAACiB,KAAKC,MAAI;YAACP,KAAK;YACb,UAAA;cACC;gBACEjD,MAAM;gBACNX,OAAOa,cAAc;kBACnBkC,IAAIqB,eAAe,2CAAA;kBACnBpB,gBAAgB;gBAClB,CAAA;gBACAzC,MAAM;gBACNmB,MAAM;cACR;cACA;gBACEf,MAAM;gBACNX,OAAOa,cAAc;kBACnBkC,IAAIqB,eAAe,iDAAA;kBACnBpB,gBAAgB;gBAClB,CAAA;gBACAzC,MAAM;gBACNmB,MAAM;cACR;cACA;gBACEf,MAAM;gBACNX,OAAOa,cAAc;kBACnBkC,IAAIqB,eAAe,iDAAA;kBACnBpB,gBAAgB;gBAClB,CAAA;gBACAzC,MAAM;gBACNmB,MAAM;cACR;cACA;gBACEf,MAAM;gBACNX,OAAOa,cAAc;kBACnBkC,IAAIqB,eAAe,8CAAA;kBACnBpB,gBAAgB;gBAClB,CAAA;gBACAzC,MAAM;gBACNmB,MAAM;cACR;cACA;gBACEf,MAAM;gBACNX,OAAOa,cAAc;kBACnBkC,IAAIqB,eAAe,+CAAA;kBACnBpB,gBAAgB;gBAClB,CAAA;gBACAqB,MAAMxD,cAAc;kBAClBkC,IAAIqB,eACF,gEAAA;kBAEFpB,gBAAgB;gBAClB,CAAA;gBACAzC,MAAM;gBACN+D,SAASjD;gBACTK,MAAM;cACR;cACA;gBACEf,MAAM;gBACNX,OAAOa,cAAc;kBACnBkC,IAAIqB,eAAe,6CAAA;kBACnBpB,gBAAgB;gBAClB,CAAA;gBACAzC,MAAM;gBACN+D,SAAS;kBACP;oBAAErD,OAAO;oBAAKjB,OAAO;kBAAM;kBAC3B;oBAAEiB,OAAO;oBAAKjB,OAAO;kBAAM;kBAC3B;oBAAEiB,OAAO;oBAAKjB,OAAO;kBAAM;kBAC3B;oBAAEiB,OAAO;oBAAMjB,OAAO;kBAAO;gBAC9B;gBACD0B,MAAM;cACR;YACD,EACE6C,OAAOC,iCAAiC9D,UAAUgB,IAAI,CAAA,EACtD+C,IAAI,CAAC,EAAElE,MAAM,GAAGmE,MAAAA,UACfzB,wBAACiB,KAAKS,MAAI;cAAkBC,KAAKrE;cAAMsE,WAAU;cAASC,YAAW;cACnE,cAAA7B,wBAAC8B,uBAAAA;gBAAe,GAAGL;;YADLA,GAAAA,MAAM/D,IAAI,CAAA;;;YAMlCyC,yBAACF,MAAM8B,QAAM;;gBACX/B,wBAACC,MAAM+B,OAAK;cACV,cAAAhC,wBAACiC,QAAAA;gBAAOC,SAAQ;0BACbtE,cAAc;kBAAEkC,IAAI;kBAAgCC,gBAAgB;gBAAS,CAAA;;;gBAGlFC,wBAACiC,QAAAA;cAAOxD,MAAK;wBACVb,cAAc;gBAAEkC,IAAI;gBAAiBC,gBAAgB;cAAS,CAAA;;;;;;;AAM3E;AAOA,IAAMwB,mCAAmC,CAAC9C,SAAgC,CAACgD,UAAAA;AACzE,UAAQhD,MAAAA;IACN,KAAK;IACL,KAAK;AACH,aAAOgD,MAAM/D,SAAS,UAAU+D,MAAM/D,SAAS;IACjD,KAAK;IACL,KAAK;AACH,aAAO+D,MAAM/D,SAAS,iBAAiB+D,MAAM/D,SAAS;IACxD,KAAK;IACL,KAAK;AACH,aAAO+D,MAAM/D,SAAS,WAAW+D,MAAM/D,SAAS;IAClD,KAAK;AACH,aAAO+D,MAAM/D,SAAS,iBAAiB+D,MAAM/D,SAAS,eAAe+D,MAAM/D,SAAS;IACtF,KAAK;AACH,aAAO;IACT;AACE,aAAO+D,MAAM/D,SAAS;EAC1B;AACF;;;ACrNA,IAAMyE,eAAe;AAMrB,IAAMC,qBAAqB,CAAC,EAC1BC,IACAC,SAAQ,MAIT;AACC,QAAMC,YAAYC,aAAa;IAC7BH;EACF,CAAA;AAEA,SAAOC,SAASC,SAAAA;AAClB;IAEaE,eAAe,CAAC,EAAEJ,IAAIC,SAAQ,MAA6C;AACtF,QAAM,EAAEI,YAAYC,YAAYC,WAAWC,WAAU,IAAKC,YAAY;IACpET;EACF,CAAA;AAEA,QAAMU,QAAQ;IACZH,WAAWI,IAAIC,UAAUC,SAASN,SAAAA;IAClCC;IACAM,QAAQ;EACV;AAEA,aACEC,yBAACC,OAAAA;IAAIC,KAAKX;IAAYI;IAAe,GAAGL;IACrCJ;;AAGP;AAiBA,IAAMiB,wCAAwC,CAACC,WAAAA;AAC7C,SAAOA,OAAOC,IAAI,CAACC,KAAKC,oBAAoB;IAC1C,GAAGD;;IAEHE,OAAO,aAAaD,cAAAA;IACpBrB,UAAUoB,IAAIpB,SAASmB,IAAI,CAACI,OAAOC,gBAAgB;MACjD,GAAGD;MACHD,OAAO,aAAaD,cAAAA,UAAwBG,UAAAA;;MAG5CC,UAAU,UAAUJ,cAAAA,aAA2BG,UAAAA;MACjD;IACF;AACF;AAEA,IAAME,SAAS,CAAC,EAAEtB,YAAYuB,YAAYC,YAAYC,YAAY,CAAA,EAAE,MAAe;AACjF,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,QAAMb,SAASc,QACb,UACA,CAACC,UAAUA,MAAMC,OAAOhB,UAAU,CAAA,CAAE;AAGtC,QAAMiB,WAAWH,QAAQ,UAAU,CAACC,UAAUA,MAAME,QAAQ;AAC5D,QAAMC,cAAcJ,QAAQ,UAAU,CAACC,UAAUA,MAAMG,WAAW;AAClE,QAAMC,iBAAiBL,QAAQ,UAAU,CAACC,UAAUA,MAAMI,cAAc;AAExE,QAAMC,iBAAiBpB,OAAOC,IAAI,CAACC,QAAQA,IAAIpB,SAASmB,IAAI,CAACoB,UAAUA,MAAMC,IAAI,CAAA,EAAGC,KAAI;AAOxF,QAAMC,kBAAkBC,OAAOC,QAAQf,SAAWgB,EAAAA,OAAgB,CAACC,KAAKC,YAAAA;;AACtE,UAAM,CAACP,MAAM,EAAEQ,SAAS,GAAGT,MAAAA,CAAO,IAAIQ;AAEtC,QAAI,CAACT,eAAeW,SAAST,IAAAA,KAASQ,YAAY,MAAM;AACtD,YAAME,QAAO9C,gBAAWoC,IAAAA,MAAXpC,mBAAkB8C;AAC/B,YAAMC,OAAOD,OAAOvB,WAAWuB,IAAAA,IAAQrD;AAEvCiD,UAAIM,KAAK;QACP,GAAGb;QACHc,OAAOd,MAAMc,SAASb;QACtBA;QACAW;MACF,CAAA;IACF;AAEA,WAAOL;EACT,GAAG,CAAA,CAAE;AAEL,QAAMQ,oBACJ,CAACC,UAAkBC,eACnB,MAAA;AACE,QAAItC,OAAOqC,QAAS,EAACvD,SAASyD,WAAW,GAAG;AAC1CpB,qBAAe,UAAUkB,QAAAA;WACpB;AACLpB,eAAS,UAAUoB,QAAS,aAAY;QACnCrC,GAAAA,OAAOqC,QAAS,EAACvD,SAAS0D,MAAM,GAAGF,UAAAA;QACnCtC,GAAAA,OAAOqC,QAAS,EAACvD,SAAS0D,MAAMF,aAAa,CAAA;MACjD,CAAA;IACH;EACF;AAEF,QAAMG,iBAAiB,CAACpB,UAAiB,MAAA;AACvCH,gBAAY,UAAU;MAAEpC,UAAU;QAACuC;MAAM;IAAC,CAAA;EAC5C;AAEA,QAAM,CAACqB,YAAYC,aAAc,IAASC,eAAS,MACjD7C,sCAAsCC,MAAAA,CAAAA;AAGxC,QAAM,CAAC6C,gBAAgBC,iBAAAA,IAA2BF,eAChD,IAAA;AAMF,WAASG,cAAclE,IAAsBmE,wBAAiD;AAE5F,QAAInE,MAAMmE,wBAAwB;AAChC,aAAOnE;IACT;AAGA,WAAO4C,OAAOwB,KAAKD,sBAAAA,EAAwBE,KAAK,CAACC,QAC/CH,uBAAuBG,GAAAA,EAAKrE,SAASoE,KAAK,CAAC7C,UAAUA,MAAMD,UAAUvB,EAAAA,CAAAA;EAEzE;AAKA,QAAMuE,uBAAuB,CAACvE,IAAsBwE,cAAAA;AAClD,WAAOA,UAAUvE,SAASoE,KAAK,CAACI,SAASzE,OAAOyE,KAAKlD,KAAK;EAC5D;AAKA,QAAMmD,4BAA4B,MAAA;AAChC,WAAO9B,OAAO+B,YAAYd,WAAWzC,IAAI,CAACoD,cAAc;MAACA,UAAUjD;MAAOiD;IAAU,CAAA,CAAA;EACtF;AAKA,QAAMI,8BAA8B,CAACzD,YAAAA;AACnC,WAAOA,QACJC,IAAI,CAACC,SAAS;MACb,GAAGA;MACHpB,UAAUoB,IAAIpB,SAAS4E,OAAO,CAACrC,UAAUA,MAAMC,SAASqC,eAAAA;IAC1D,EACCD,EAAAA,OAAO,CAACxD,QAAQA,IAAIpB,SAASyD,SAAS,CACtCtC,EAAAA,IAAI,CAACC,QAAAA;;AACJ,YAAM0D,kBAAkB1D,IAAIpB,SAAS6C,OAAO,CAACC,KAAKiC,SAASjC,MAAMiC,KAAK5B,MAAM,CAAA;AAE5E,UAAI2B,kBAAkBjF,cAAc;AAClC,cAAM,CAACmF,SAAAA,IAAaC,sBAClB7D,SAAIpB,SAASkF,GAAG,EAAKC,MAArB/D,mBAAqB+D,cACrBC,QACA,CAAA;AAGF,eAAO;UACL,GAAGhE;UACHpB,UAAU;YACLoB,GAAAA,IAAIpB;YACP;cACEwC,MAAMqC;cACN1B,MAAMtD,eAAeiF;cACrBK,cAAcH;YAChB;UACD;QACH;MACF;AAEA,aAAO5D;IACT,CAAA;EACJ;AAMAiE,EAAMC,gBAAU,MAAA;AACd,UAAM1B,cAAa3C,sCAAsCC,MAAAA;AACzD2C,kBAAcD,WAAAA;KACb;IAAC1C;IAAQ2C;EAAc,CAAA;AAE1B,aACE/C,yBAACyE,YAAAA;IACCC,aAAa,CAACC,UAAAA;AACZ,YAAMvB,yBAAyBO,0BAAAA;AAE/B,YAAMiB,kBAAkBzB,cAAcwB,MAAME,OAAO5F,IAAImE,sBAAAA;AAEvD,UAAI,CAACwB,gBAAiB;AAEtB,YAAME,aAAatB,qBACjBmB,MAAME,OAAO5F,IACbmE,uBAAuBwB,eAAgB,CAAA;AAGzC,UAAIE,YAAY;AACd5B,0BAAkB4B,UAAAA;MACpB;IACF;IACAC,YAAY,CAAC,EAAEF,QAAQG,KAAI,MAAE;AAC3B,YAAM5B,yBAAyBO,0BAAAA;AAC/B,YAAMiB,kBAAkBzB,cAAc0B,OAAO5F,IAAImE,sBAAAA;AACjD,YAAM6B,gBAAgB9B,eAAc6B,6BAAM/F,OAAM,IAAImE,sBAAAA;AACpD,YAAM8B,uBAAuBpC,WAAWqC,UACtC,CAAC1B,cAAcA,UAAUjD,UAAUoE,eAAAA;AAErC,YAAMQ,qBAAqBtC,WAAWqC,UACpC,CAAC1B,cAAcA,UAAUjD,UAAUyE,aAAAA;AAGrC,UAAI,CAACL,mBAAmB,CAACK,eAAe;AACtC;MACF;AAEA,YAAMI,cAAc7B,qBAClBqB,OAAO5F,IACPmE,uBAAuBwB,eAAgB,CAAA;AAEzC,YAAMU,WAAW9B,sBACfwB,6BAAM/F,OAAM,IACZmE,uBAAuB6B,aAAc,CAAA;AAEvC,YAAMM,YAAYnC,uBAAuB6B,aAAAA,EAAe/F,SAASiG,UAC/D,CAACzB,SAASA,KAAKlD,WAAUwE,6BAAM/F,GAAAA;AAGjC,UAAI,CAACoG,YAAa;AAGlB,WAAIA,2CAAahD,UAAStD,cAAc;AAEtC,cAAMyG,UAASC,GAAQ3C,YAAY,CAAC4C,UAAAA;AAClCA,gBAAMR,oBAAAA,EAAsBhG,WAAW4D,WAAWsC,kBAAmB,EAAClG;AACtEwG,gBAAMN,kBAAAA,EAAoBlG,WAAW4D,WAAWoC,oBAAqB,EAAChG;QACxE,CAAA;AACA6D,sBAAcyC,OAAAA;AACd;MACF;AAOA,YAAMA,SAASC,GAAQ3C,YAAY,CAAC4C,UAAAA;AAClCA,cAAMR,oBAAqB,EAAChG,WAAWwG,MAAMR,oBAAqB,EAAChG,SAAS4E,OAC1E,CAACJ,SAASA,KAAKlD,UAAUqE,OAAO5F,EAAE;AAEpC,cAAM0G,aAAaD,MAAMN,kBAAmB,EAAClG,SAAS6C,OAAO,CAACC,KAAKiC,SAAAA;AACjE,cAAIA,KAAKvC,SAASqC,iBAAiB;AACjC,mBAAO/B;UACT;AAEA,iBAAOA,MAAMiC,KAAK5B;WACjB,CAAA;AAGH,YAAIsD,aAAaN,YAAYhD,OAAOtD,cAAc;AAEhD2G,gBAAMR,oBAAAA,EAAsBhG,WAAW4D,WAAWoC,oBAAqB,EAAChG;AACxE;QACF;AAEA,aAAIoG,qCAAU5D,UAASqC,iBAAiB;AAEtC2B,gBAAMN,kBAAAA,EAAoBlG,SAAS0G,OAAOL,WAAW,GAAGF,WAAAA;AACxD;QACF;AAGAK,cAAMN,kBAAAA,EAAoBlG,SAAS0G,OAAOL,WAAW,GAAGF,WAAAA;MAC1D,CAAA;AAEAtC,oBAAcyC,MAAAA;IAChB;IACAK,WAAW,CAAClB,UAAAA;AACV,YAAM,EAAEE,QAAQG,KAAI,IAAKL;AACzB,YAAM,EAAE1F,GAAE,IAAK4F;AACf,YAAMiB,SAASd,6BAAM/F;AACrB,YAAMmE,yBAAyBO,0BAAAA;AAC/B,YAAMiB,kBAAkBzB,cAAclE,IAAImE,sBAAAA;AAC1C,YAAM6B,gBAAgB9B,cAAc2C,QAAS1C,sBAAAA;AAE7C,UAAI,CAACwB,mBAAmB,CAACK,eAAe;AACtC;MACF;AAEA,YAAMc,cAAc3C,uBAAuBwB,eAAAA,EAAiB1F,SAASiG,UACnE,CAACjG,aAAaA,SAASsB,UAAUvB,EAAAA;AAEnC,YAAMsG,YAAYnC,uBAAuB6B,aAAAA,EAAe/F,SAASiG,UAC/D,CAACjG,aAAaA,SAASsB,UAAUsF,MAAAA;AAGnC,YAAME,sBAAsBP,GAAQrC,wBAAwB,CAACsC,UAAAA;AAC3D,YAAIK,gBAAgBR,aAAaX,oBAAoBK,eAAe;AAElES,gBAAMd,eAAAA,EAAiB1F,WAAW+G,UAChCP,MAAMd,eAAgB,EAAC1F,UACvB6G,aACAR,SAAAA;QAEJ;MACF,CAAA;AAGA,YAAMW,oBAAoBrE,OAAOT,OAAO4E,mBAAAA;AACxC,YAAMG,+BAA+BtC,4BACnCqC,iBAAAA;AAEF,YAAME,gBAAgBD,6BAA6B9F,IACjD,CAAC,EAAEG,OAAO6F,QAAQnH,UAAU,GAAGuE,UAAAA,OAAiB;QAC9C,GAAGA;QACHvE,UAAUA,SAASmB,IAAI,CAAC,EAAEG,OAAO6F,SAAQ1F,UAAU2F,WAAW,GAAG7F,MAAAA,MAAYA,KAAAA;QAC/E;AAIFY,eAAS,UAAU+E,aAAAA;AACnBlD,wBAAkB,IAAA;IACpB;IAEA,cAAAqD,0BAACC,MAAAA;MAAKC,YAAY;MAAGC,WAAU;MAASC,YAAW;MAAUC,KAAK;;YAChEL,0BAACC,MAAAA;UAAKG,YAAW;UAAaD,WAAU;UAASG,gBAAe;;gBAC9D7G,yBAAC8G,YAAAA;cAAWC,YAAW;wBACpB/F,cAAc;gBACb/B,IAAI+H,eAAe,iCAAA;gBACnBC,gBAAgB;cAClB,CAAA;;gBAEFjH,yBAAC8G,YAAAA;cAAWI,SAAQ;cAAKC,WAAU;wBAChCnG,cAAc;gBACb/B,IAAI;gBACJgI,gBAAgB;cAClB,CAAA;;;;YAGJjH,yBAACoH,KAAAA;UAAIC,SAAS;UAAGC,WAAS;UAACC,aAAY;UAASC,aAAY;UAAMC,aAAY;UAC5E,cAAAlB,0BAACC,MAAAA;YAAKE,WAAU;YAASC,YAAW;YAAUC,KAAK;;cAChD9D,WAAWzC,IAAI,CAACoD,WAAWlD,uBAC1BP,yBAAC0H,iBAAAA;gBAECzI,IAAIwE,UAAUjD;gBACdmH,OAAOlE,UAAUvE,SAASmB,IAAI,CAACI,WAAW;kBAAExB,IAAIwB,MAAMD;kBAAM;gBAE5D,cAAAR,yBAAChB,oBAAAA;kBAAmBC,IAAIwE,UAAUjD;kBAC/B,UAAA,CAAC,EAAEjB,WAAU,UACZS,yBAAC4H,KAAKC,MAAI;oBAAuB3H,KAAKX;oBAAYqH,KAAK;8BACpDnD,UAAUvE,SAASmB,IAAI,CAACI,OAAOC,mBAC9BV,yBAAC4H,KAAKE,MAAI;sBACRC,KAAKtH,MAAM4B;sBAEXqE,WAAU;sBACVC,YAAW;sBAEX,cAAA3G,yBAACX,cAAAA;wBAAaJ,IAAIwB,MAAMD;wBACtB,cAAAR,yBAACgI,OAAAA;0BACCC,WAAW3I,WAAWmB,MAAMiB,IAAI;0BAChCZ;0BACAY,MAAMjB,MAAME;0BACZuH,eAAe1F,kBAAkBjC,gBAAgBG,UAAAA;0BACjDF,OAAOC,MAAMD;;;oBAVZC,GAAAA,MAAMD,KAAK,CAAA;kBAJNiD,GAAAA,UAAUjD,KAAK;;cAN9BiD,GAAAA,UAAUjD,KAAK,CAAA;kBA8BxBR,yBAACmI,aAAAA;gBACElF,UAAAA,qBACCjD,yBAACgI,OAAAA;kBACCC,WAAW3I,WAAW2D,eAAevB,IAAI;kBACzCZ;kBACAY,MAAMuB,eAAetC;kBACrBH,OAAOyC,eAAezC;gBAEtB,CAAA,IAAA;;kBAEN+F,0BAAC6B,KAAKP,MAAI;;sBACR7H,yBAACoI,KAAKC,SAAO;oBACXC,eAAWtI,yBAACuI,eAAAA,CAAAA,CAAAA;oBACZC,SAAS;oBACTC,UAAU7G,gBAAgBe,WAAW;oBACrC+F,WAAS;oBACTxB,SAAQ;8BAEPlG,cAAc;sBACb/B,IAAI+H,eAAe,kCAAA;sBACnBC,gBAAgB;oBAClB,CAAA;;sBAEFjH,yBAACoI,KAAKO,SAAO;oBACV/G,UAAAA,gBAAgBvB,IAAI,CAACoB,cACpBzB,yBAACoI,KAAKN,MAAI;sBAAkBc,UAAU/F,eAAepB,KAAAA;sBAClDA,UAAAA,MAAMc;oBADOd,GAAAA,MAAMC,IAAI,CAAA;;;;;;;;;;AAW5C;AAYA,IAAMqC,kBAAkB;AAMxB,IAAMiE,QAAQ,CAAC,EAAEC,WAAWnH,YAAYY,MAAMwG,eAAe1H,MAAK,MAAc;AAC9E,QAAM,CAACqI,aAAaC,cAAAA,IAAwB9F,eAAS,KAAA;AACrD,QAAM,EAAEhC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAE8H,MAAK,IAAKC,SAAoBtH,IAAAA;AACtC,QAAM,EAAEuH,WAAWC,oBAAmB,IAAKxJ,YAAY;IACrDT,IAAIuB;EACN,CAAA;AAEA,QAAMgC,oBAAgE,CAAC2G,MAAAA;AACrEA,MAAEC,eAAc;AAChBD,MAAEE,gBAAe;AACjB,QAAInB,eAAe;AACjBA,qDAAgBiB;IAClB;EACF;AAEA,QAAMG,kBAA8D,CAACH,MAAAA;AACnEA,MAAEC,eAAc;AAChBD,MAAEE,gBAAe;AACjBP,mBAAe,IAAA;EACjB;AAEA,MAAI,CAACC,OAAO;AACV,WAAO;EACT;AAEA,MAAIA,MAAMrH,SAASqC,iBAAiB;AAClC,eAAO/D,yBAACwG,MAAAA;MAAK+C,KAAI;MAAOxJ,QAAO;MAAOJ,OAAO;QAAE6J,SAAS;MAAE;;EAC5D;AAEA,MAAI,CAACvB,WAAW;AACd,WAAO;EACT;AAEA,aACE1B,0BAACkD,MAAM5B,MAAI;IAAC6B,MAAMb;IAAac,cAAcb;;UAC3CvC,0BAACC,MAAAA;QACCiB,aAAY;QACZmC,YAAW;QACXtC,WAAS;QACTV,KAAK;QACLiD,QAAO;QACPC,SAAS,MAAA;AACPhB,yBAAe,IAAA;QACjB;;cAEA9I,yBAAC+J,YAAAA;YACC7J,KAAKgJ;YACLK,KAAI;YACJS,aAAa;YACbzH,OAAOvB,cACL;cACE/B,IAAI+H,eAAe,qCAAA;cACnBC,gBAAgB;eAElB;cAAEvD,MAAMqF,MAAMxG;YAAM,CAAA;YAErB,GAAG0G;YAEJ,cAAAjJ,yBAACiK,eAAAA,CAAAA,CAAAA;;cAEH1D,0BAACC,MAAAA;YAAKE,WAAU;YAASC,YAAW;YAAauD,MAAM;YAAGC,UAAS;;kBACjE5D,0BAACC,MAAAA;gBAAKI,KAAK;gBAAGC,gBAAe;gBAAgBuD,OAAM;;sBACjDpK,yBAAC8G,YAAAA;oBAAWuD,UAAQ;oBAACtD,YAAW;oBAC7BgC,UAAAA,MAAMxG;;sBAETgE,0BAACC,MAAAA;;0BACCxG,yBAACsK,YAAAA;wBACClI,MAAK;wBACL8E,SAAQ;wBACR0C,YAAW;wBACXE,SAASR;wBACTU,aAAa;wBACbzH,OAAOvB,cACL;0BACE/B,IAAI+H,eAAe,qCAAA;0BACnBC,gBAAgB;2BAElB;0BAAEvD,MAAMqF,MAAMxG;wBAAM,CAAA;wBAGtB,cAAAvC,yBAACuK,eAAAA,CAAAA,CAAAA;;0BAEHvK,yBAACsK,YAAAA;wBACClI,MAAK;wBACL8E,SAAQ;wBACR4C,SAAStH;wBACToH,YAAW;wBACXI,aAAa;wBACbzH,OAAOvB,cACL;0BACE/B,IAAI+H,eAAe,uCAAA;0BACnBC,gBAAgB;2BAElB;0BAAEvD,MAAMqF,MAAMxG;wBAAM,CAAA;wBAGtB,cAAAvC,yBAACwK,eAAAA,CAAAA,CAAAA;;;;;;eAINvC,uCAAW7F,UAAS,kBACnBmE,0BAACC,MAAAA;gBACCC,YAAY;gBACZgE,cAAc;gBACdC,eAAe;gBACfC,aAAa;gBACbhE,YAAW;gBACXD,WAAU;gBACVE,KAAK;gBACLwD,OAAM;;sBAENpK,yBAAC4H,KAAKC,MAAI;oBAACjB,KAAK;oBAAGwD,OAAM;8BACtBtJ,WAAWmH,UAAU2C,SAAS,EAAExK,OAAOC,IAAI,CAACC,QAC3CA,IAAID,IAAI,CAAC,EAAEgC,MAAM,GAAGZ,MAAO,UACzBzB,yBAAC4H,KAAKE,MAAI;sBAAkBC,KAAK1F;sBAAMqE,WAAU;sBAASC,YAAW;sBACnE,cAAA3G,yBAACwG,MAAAA;wBACCG,YAAW;wBACXiD,YAAW;wBACXnD,YAAY;wBACZiE,eAAe;wBACfC,aAAa;wBACbF,cAAc;wBACdnD,WAAS;wBACTG,aAAY;wBAEZ,cAAAzH,yBAAC8G,YAAAA;0BAAWK,WAAU;0BAAc1F,UAAAA,MAAMC;;;oBAX9BD,GAAAA,MAAMC,IAAI,CAAA,CAAA;;sBAiBhC1B,yBAAC6K,MAAAA;;oBAECf,SAAS,CAACX,MAAMA,EAAEE,gBAAe;oBACjCf,eAAWtI,yBAAC8K,eAAAA,CAAAA,CAAAA;oBACZvB,KAAKwB;oBACLC,IAAI,iBAAiB/C,UAAU2C,SAAS;8BAEvC5J,cAAc;sBACb/B,IAAI+H,eAAe,4CAAA;sBACnBC,gBAAgB;oBAClB,CAAA;;;cAGF,CAAA,IAAA;eACHgB,uCAAW7F,UAAS,oBACnBpC,yBAACwG,MAAAA;gBACCC,YAAY;gBACZgE,cAAc;gBACdC,eAAe;gBACfC,aAAa;gBACbhE,YAAW;gBACXC,KAAK;gBACLwD,OAAM;gBACNa,MAAK;gBAEJhD,UAAAA,uCAAWnH,WAAWT,IAAI,CAAC6K,YAC1B3E,0BAAC4E,eAAAA;;kBAECrB,SAAS,CAACX,MAAMA,EAAEE,gBAAe;kBAEjC2B,IAAI,iBAAiBE,GAAAA;;wBAErBlL,yBAACoL,eAAAA;sBAAcC,MAAMvK,WAAWoK,GAAAA,EAAKI,SAASD;;wBAC9CrL,yBAAC8G,YAAAA;sBAAWyE,UAAU;sBAAGpE,WAAU;sBAAaJ,YAAW;sBACxDjG,UAAAA,WAAWoK,GAAAA,EAAKI,SAASE;;;gBALvBN,GAAAA,GAAAA;cAUT,CAAA,IAAA;;;;;MAGPnC,MAAMrH,SAASqC,uBACd/D,yBAACyL,eAAAA;QAAcxD;QAAsBvG;QAAYgK,SAAS,MAAM5C,eAAe,KAAA;;;;AAIvF;AAEA,IAAMiB,aAAa4B,GAAoCrB,UAAAA;;;;;;;;;4BAS3B,CAAC,EAAEsB,MAAK,MAAOA,MAAMC,OAAOC,UAAU;;;;;;;;AASlE,IAAMX,gBAAgBQ,GAAOZ,OAAAA;;;;SAIpB,CAAC,EAAEa,MAAK,MAAOA,MAAMG,OAAO,CAAA,CAAE;aAC1B,CAACC,UAAUA,MAAMJ,MAAMG,OAAO,CAAA,CAAE;sBACvB,CAAC,EAAEH,MAAK,MAAOA,MAAMC,OAAOI,UAAU;gBAC5C,CAAC,EAAEL,MAAK,MAAOA,MAAMC,OAAOK,QAAQ;;mBAEjC,CAAC,EAAEN,MAAK,MAAOA,MAAMO,YAAY;;;;;MAK9C,CAAC,EAAEP,MAAK,MAAO;0BACKA,MAAMC,OAAOO,UAAU;sBAC3BR,MAAMC,OAAOQ,UAAU;;QAErCvF,UAAW;mBACA8E,MAAMC,OAAOS,UAAU;;KAErC;;;;oBAIe,CAAC,EAAEV,MAAK,MAAOA,MAAMC,OAAOQ,UAAU;eAC3C,CAAC,EAAET,MAAK,MAAOA,MAAMC,OAAOS,UAAU;;;;kBAInC,CAAC,EAAEV,MAAK,MAAOA,MAAMC,OAAOS,UAAU;;;;;;;;;;;AC7oBlDC,IAAAA,oBAAoB,CAAC,EACzBC,YACAC,YACAC,QAAQC,YACRC,SAAQ,MACe;AACvB,QAAM,EAAEC,YAAYC,UAAUJ,QAAQK,UAAS,IAAKJ;AAEpD,QAAM,EAAEK,cAAa,IAAKC,QAAAA;AAE1B,QAAMC,gBAA6CC,eAAQ,MAAA;AACzD,UAAMC,sBAAkBC,YAAAA,SACtBC,eACAC,8BACAC,iBACAC,qBACAC,kBAAAA;AAGF,WAAO;MACLhB,QAAQU,gBAAgBV,MAAAA;MACxBI;IACF;KACC;IAACJ;IAAQI;EAAS,CAAA;AAErB,aACEa,yBAACC,QAAQC,MAAI;IACX,cAAAF,yBAACG,MAAAA;MACC,cAAAC,0BAACC,MAAAA;QAAKd;QAA8BN;QAAoBqB,QAAO;;cAC7DN,yBAACO,QAAAA;YAAOC,MAAMrB,SAASsB,eAAe;;cACtCT,yBAACC,QAAQS,SAAO;YACd,cAAAN,0BAACO,MAAAA;cACCC,YAAW;cACXC,YAAW;cACXC,WAAU;cACVC,KAAK;cACLC,WAAS;cACTC,QAAO;cACPC,YAAY;cACZC,eAAe;cACfC,aAAa;cACbC,cAAc;;oBAEdrB,yBAACsB,YAAAA;kBAAWC,SAAQ;kBAAQC,KAAI;4BAC7BnC,cAAc;oBACboC,IAAIC,eAAe,iCAAA;oBACnBC,gBAAgB;kBAClB,CAAA;;oBAEFvB,0BAACwB,KAAK1B,MAAI;;wBACRF,yBAAC4B,KAAKC,MAAI;sBAACC,KAAK;sBAAGC,GAAG;sBAAIjB,WAAU;sBAASF,YAAW;sBACtD,cAAAZ,yBAACgC,uBAAAA;wBACCC,MAAK;wBACLC,OAAO7C,cAAc;0BACnBoC,IAAIC,eAAe,iDAAA;0BACnBC,gBAAgB;wBAClB,CAAA;wBACAQ,MAAM9C,cAAc;0BAClBoC,IAAIC,eACF,6DAAA;0BAEFC,gBAAgB;wBAClB,CAAA;wBACAnB,MAAK;wBACL4B,SAASC,OAAOC,QAAQzD,UAAY0D,EAAAA,OAElC,CAACC,KAAK,CAACC,KAAKC,SAAU,MAAA;AACtB,8BAAI,CAACA,WAAW;AACd,mCAAOF;0BACT;AAOA,8BAAI,CAACG,0CAA0CC,SAASF,UAAUT,IAAI,GAAG;AACvEO,gCAAIK,KAAK;8BACPX,OAAOO;8BACPK,OAAOL;4BACT,CAAA;0BACF;AAEA,iCAAOD;wBACT,GAAG,CAAA,CAAE;;;wBAGTxC,yBAAC4B,KAAKC,MAAI;sBACRX,YAAY;sBACZC,eAAe;sBACfW,KAAK;sBACLC,GAAG;sBACHjB,WAAU;sBACVF,YAAW;sBAEX,cAAAZ,yBAAC+C,SAAAA,CAAAA,CAAAA;;wBAEH/C,yBAAC4B,KAAKC,MAAI;sBAACC,KAAK;sBAAIC,GAAG;sBAAIjB,WAAU;sBAASF,YAAW;sBACvD,cAAAZ,yBAACsB,YAAAA;wBAAWC,SAAQ;wBAAQC,KAAI;kCAC7BnC,cAAc;0BACboC,IAAIC,eAAe,6BAAA;0BACnBC,gBAAgB;wBAClB,CAAA;;;wBAGJ3B,yBAAC4B,KAAKC,MAAI;sBAACC,KAAK;sBAAIC,GAAG;sBAAIjB,WAAU;sBAASF,YAAW;sBACvD,cAAAZ,yBAACgD,QAAAA;wBACCnE;wBACAK;wBACAJ;wBACAM;;;;;;;;;;;;AAUpB;AAMA,IAAMO,gBAAgB,CAACZ,WACrBA,OAAOkE,KAAK,CAAA;AAMd,IAAMrD,+BAA+B,CAACb,WACpCA,OAAOmE,IAAI,CAACC,QACVA,IAAID,IAAI,CAACE,UAAAA;;AAAW;IAClB,GAAGA;IACHC,YAAWD,WAAMC,cAAND,mBAAiB5C;;CAC9B,CAAA;AAOJ,IAAMX,kBAAkB,CACtBd,WAAAA;AAEA,SAAOA,OAAOmE,IAAI,CAACC,QACjBA,IAAID,IAAI,CAAC,EAAEhB,OAAOoB,UAAUnB,MAAMoB,aAAaC,MAAMhD,MAAM6C,UAAS,OAAQ;IAC1EnB;IACAuB,UAAU,CAACH;IACXI,aAAavB;IACbkB;IACAE;IACAC;IACAhD;IACAmD,cAAc;IAChB,CAAA;AAEJ;AAOA,IAAM7D,sBAAsB,CAC1Bf,WAC+D;KAC5DA,OAAOmE,IAAI,CAACC,QAAAA;AACb,UAAMS,kBAAkBT,IAAIZ,OAAO,CAACC,KAAKY,UAAUZ,MAAMY,MAAMI,MAAM,CAAA;AAErE,QAAII,kBAAkB,IAAI;AACxB,aAAO;QACFT,GAAAA;QACH;UACE3C,MAAMqD;UACNL,MAAM,KAAKI;UACXD,cAAc;QAChB;MACD;IACH;AAEA,WAAOR;EACT,CAAA;AACD;AAOD,IAAMpD,qBAAqB,CACzBhB,WAAAA;AAEA,QAAM+E,OAAOC,qBAAqBC,QAAWA,QAAWjF,OAAOkF,MAAM;AAErE,SAAOlF,OAAOmE,IAAI,CAACC,KAAKe,aAAAA;AACtB,UAAMC,YAAYJ,qBAAqBC,QAAWA,QAAWb,IAAIc,MAAM;AAEvE,WAAO;MACLN,cAAcG,KAAKI,QAAS;MAC5BE,UAAUjB,IAAID,IAAI,CAACE,OAAOiB,eAAAA;AACxB,eAAO;UACL,GAAGjB;UACHO,cAAcQ,UAAUE,UAAW;QACrC;MACF,CAAA;IACF;EACF,CAAA;AACF;AAUA,IAAM9D,SAAS,CAAC,EAAEC,KAAI,MAAe;AACnC,QAAM,EAAEnB,cAAa,IAAKC,QAAAA;AAC1B,QAAMgF,WAAWC,QAAQ,UAAU,CAACC,UAAUA,MAAMF,QAAQ;AAC5D,QAAMG,eAAeF,QAAQ,UAAU,CAACC,UAAUA,MAAMC,YAAY;AAEpE,aACEzE,yBAACC,QAAQM,QAAM;IACbmE,OAAOrF,cACL;MACEoC,IAAIC,eAAe,mDAAA;MACnBC,gBAAgB;OAElB;MAAEnB,MAAMmE,WAAWnE,IAAAA;IAAM,CAAA;IAE3BoE,UAAUvF,cAAc;MACtBoC,IAAIC,eAAe,uEAAA;MACnBC,gBAAgB;IAClB,CAAA;IACAkD,sBAAkB7E,yBAAC8E,YAAAA,CAAAA,CAAAA;IACnBC,mBACE/E,yBAACgF,QAAAA;MAAO1B,UAAU,CAACgB;MAAUW,SAASR;MAAcxC,MAAK;gBACtD5C,cAAc;QAAEoC,IAAI;QAAeE,gBAAgB;MAAO,CAAA;;;AAKrE;", "names": ["FIELD_SCHEMA", "object", "shape", "label", "string", "required", "nullable", "description", "editable", "boolean", "size", "number", "EditFieldForm", "attribute", "name", "onClose", "formatMessage", "useIntl", "toggleNotification", "useNotification", "value", "onChange", "useField", "data", "mainFieldOptions", "useGetInitialDataQuery", "undefined", "selectFromResult", "res", "type", "targetModel", "targetSchema", "contentTypes", "find", "schema", "uid", "Object", "entries", "attributes", "reduce", "acc", "key", "ATTRIBUTE_TYPES_THAT_CANNOT_BE_MAIN_FIELD", "includes", "push", "skip", "TEMP_FIELD_NAME", "console", "error", "message", "id", "defaultMessage", "_jsx", "Modal", "Content", "_jsxs", "Form", "method", "initialValues", "validationSchema", "onSubmit", "Header", "Flex", "gap", "FieldTypeIcon", "Title", "fieldName", "capitalise", "Body", "Grid", "Root", "getTranslation", "hint", "options", "filter", "filterFieldsBasedOnAttributeType", "map", "field", "<PERSON><PERSON>", "col", "direction", "alignItems", "InputR<PERSON><PERSON>", "Footer", "Close", "<PERSON><PERSON>", "variant", "GRID_COLUMNS", "DroppableContainer", "id", "children", "droppable", "useDroppable", "SortableItem", "attributes", "setNodeRef", "transform", "transition", "useSortable", "style", "CSS", "Transform", "toString", "height", "_jsx", "div", "ref", "createDragAndDropContainersFromLayout", "layout", "map", "row", "containerIndex", "dndId", "child", "childIndex", "formName", "Fields", "fieldSizes", "components", "metadatas", "formatMessage", "useIntl", "useForm", "state", "values", "onChange", "addFieldRow", "removeFieldRow", "existingFields", "field", "name", "flat", "remainingFields", "Object", "entries", "reduce", "acc", "current", "visible", "includes", "type", "size", "push", "label", "handleRemoveField", "rowIndex", "fieldIndex", "length", "slice", "handleAddField", "containers", "setContainers", "useState", "activeDragItem", "setActiveDragItem", "find<PERSON><PERSON><PERSON>", "containersAsDictionary", "keys", "find", "key", "getItemFromContainer", "container", "item", "getContainersAsDictionary", "fromEntries", "createContainersWithSpacers", "filter", "TEMP_FIELD_NAME", "totalSpaceTaken", "curr", "<PERSON><PERSON><PERSON><PERSON>", "generateNKeysBetweenImpl", "at", "__temp_key__", "undefined", "React", "useEffect", "DndContext", "onDragStart", "event", "activeContainer", "active", "activeItem", "onDragOver", "over", "over<PERSON><PERSON><PERSON>", "activeContainerIndex", "findIndex", "overContainerIndex", "draggedItem", "overItem", "overIndex", "update", "produce", "draft", "spaceTaken", "splice", "onDragEnd", "overId", "activeIndex", "movedContainerItems", "arrayMove", "updatedContainers", "updatedContainersWithSpacers", "updatedLayout", "_dndId", "_formName", "_jsxs", "Flex", "paddingTop", "direction", "alignItems", "gap", "justifyContent", "Typography", "fontWeight", "getTranslation", "defaultMessage", "variant", "textColor", "Box", "padding", "hasRadius", "borderStyle", "borderWidth", "borderColor", "SortableContext", "items", "Grid", "Root", "<PERSON><PERSON>", "col", "Field", "attribute", "onRemoveField", "DragOverlay", "<PERSON><PERSON>", "<PERSON><PERSON>", "startIcon", "Plus", "endIcon", "disabled", "fullWidth", "Content", "onSelect", "isModalOpen", "setIsModalOpen", "value", "useField", "listeners", "setActivatorNodeRef", "e", "preventDefault", "stopPropagation", "onEditFieldMeta", "tag", "opacity", "Modal", "open", "onOpenChange", "background", "cursor", "onClick", "Drag<PERSON><PERSON><PERSON>", "withTooltip", "Drag", "grow", "overflow", "width", "ellipsis", "IconButton", "Pencil", "Cross", "paddingRight", "paddingBottom", "paddingLeft", "component", "Link", "Cog", "NavLink", "to", "wrap", "uid", "ComponentLink", "ComponentIcon", "icon", "settings", "fontSize", "displayName", "EditFieldForm", "onClose", "styled", "theme", "colors", "neutral150", "spaces", "props", "neutral200", "neutral0", "borderRadius", "primary100", "primary200", "primary600", "ConfigurationForm", "attributes", "fieldSizes", "layout", "editLayout", "onSubmit", "components", "settings", "metadatas", "formatMessage", "useIntl", "initialValues", "useMemo", "transformations", "pipe", "flattenPanels", "replaceMainFieldWithNameOnly", "extractMetadata", "addTmpSpaceToLayout", "addTmpKeysToLayout", "_jsx", "Layouts", "Root", "Main", "_jsxs", "Form", "method", "Header", "name", "displayName", "Content", "Flex", "alignItems", "background", "direction", "gap", "hasRadius", "shadow", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "Typography", "variant", "tag", "id", "getTranslation", "defaultMessage", "Grid", "<PERSON><PERSON>", "col", "s", "InputR<PERSON><PERSON>", "type", "label", "hint", "options", "Object", "entries", "reduce", "acc", "key", "attribute", "ATTRIBUTE_TYPES_THAT_CANNOT_BE_MAIN_FIELD", "includes", "push", "value", "Divider", "Fields", "flat", "map", "row", "field", "mainField", "disabled", "placeholder", "size", "editable", "description", "__temp_key__", "totalSpaceTaken", "TEMP_FIELD_NAME", "keys", "generateNKeysBetween", "undefined", "length", "rowIndex", "fieldKeys", "children", "fieldIndex", "modified", "useForm", "state", "isSubmitting", "title", "capitalise", "subtitle", "navigationAction", "BackButton", "primaryAction", "<PERSON><PERSON>", "loading"]}