{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/services/api.ts", "../../../@strapi/content-manager/admin/src/utils/strings.ts"], "sourcesContent": ["import { adminApi } from '@strapi/admin/strapi-admin';\n\nconst contentManagerApi = adminApi.enhanceEndpoints({\n  addTagTypes: [\n    'ComponentConfiguration',\n    'ContentTypesConfiguration',\n    'ContentTypeSettings',\n    'Document',\n    'InitialData',\n    'HistoryVersion',\n    'Relations',\n    'UidAvailability',\n    'RecentDocumentList',\n  ],\n});\n\nexport { contentManagerApi };\n", "const capitalise = (str: string): string => str.charAt(0).toUpperCase() + str.slice(1);\n\nexport { capitalise };\n"], "mappings": ";;;;;AAEMA,IAAAA,oBAAoBC,SAASC,iBAAiB;EAClDC,aAAa;IACX;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACD;AACH,CAAA;;;ACdMC,IAAAA,aAAa,CAACC,QAAwBA,IAAIC,OAAO,CAAA,EAAGC,YAAW,IAAKF,IAAIG,MAAM,CAAA;", "names": ["contentManagerApi", "adminApi", "enhanceEndpoints", "addTagTypes", "capitalise", "str", "char<PERSON>t", "toUpperCase", "slice"]}