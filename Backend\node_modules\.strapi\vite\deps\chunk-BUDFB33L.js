import {
  require_identity,
  require_overRest,
  require_setToString
} from "./chunk-WH6VCVXU.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/_baseRest.js
var require_baseRest = __commonJS({
  "node_modules/lodash/_baseRest.js"(exports, module) {
    var identity = require_identity();
    var overRest = require_overRest();
    var setToString = require_setToString();
    function baseRest(func, start) {
      return setToString(overRest(func, start, identity), func + "");
    }
    module.exports = baseRest;
  }
});

export {
  require_baseRest
};
//# sourceMappingURL=chunk-BUDFB33L.js.map
