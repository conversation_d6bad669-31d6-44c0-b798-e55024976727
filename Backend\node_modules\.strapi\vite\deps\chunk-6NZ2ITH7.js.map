{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/pages/ListView/components/BulkActions/ConfirmBulkActionDialog.tsx", "../../../@strapi/content-manager/admin/src/pages/ListView/components/BulkActions/PublishAction.tsx", "../../../@strapi/content-manager/admin/src/pages/ListView/components/BulkActions/Actions.tsx", "../../../@strapi/content-manager/admin/src/pages/ListView/components/AutoCloneFailureModal.tsx", "../../../@strapi/content-manager/admin/src/pages/ListView/components/TableActions.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {\n  useTable,\n  useNotification,\n  useAPIE<PERSON>r<PERSON><PERSON><PERSON>,\n  useQueryParams,\n} from '@strapi/admin/strapi-admin';\nimport { Button, Flex, Dialog, Typography } from '@strapi/design-system';\nimport { Check, WarningCircle } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport { useDoc } from '../../../../hooks/useDocument';\nimport { useGetManyDraftRelationCountQuery } from '../../../../services/documents';\nimport { getTranslation } from '../../../../utils/translations';\n\nimport { Emphasis } from './Actions';\n\ninterface ConfirmBulkActionDialogProps {\n  endAction: React.ReactNode;\n  onToggleDialog: () => void;\n  isOpen?: boolean;\n  dialogBody: React.ReactNode;\n}\n\nconst ConfirmBulkActionDialog = ({\n  onToggleDialog,\n  isOpen = false,\n  dialogBody,\n  endAction,\n}: ConfirmBulkActionDialogProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Dialog.Root open={isOpen}>\n      <Dialog.Content>\n        <Dialog.Header>\n          {formatMessage({\n            id: 'app.components.ConfirmDialog.title',\n            defaultMessage: 'Confirmation',\n          })}\n        </Dialog.Header>\n        <Dialog.Body>\n          <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n            <Flex justifyContent=\"center\">\n              <WarningCircle width=\"24px\" height=\"24px\" fill=\"danger600\" />\n            </Flex>\n            {dialogBody}\n          </Flex>\n        </Dialog.Body>\n        <Dialog.Footer>\n          <Dialog.Cancel>\n            <Button width={'50%'} onClick={onToggleDialog} variant=\"tertiary\">\n              {formatMessage({\n                id: 'app.components.Button.cancel',\n                defaultMessage: 'Cancel',\n              })}\n            </Button>\n          </Dialog.Cancel>\n          {endAction}\n        </Dialog.Footer>\n      </Dialog.Content>\n    </Dialog.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * BoldChunk\n * -----------------------------------------------------------------------------------------------*/\n\nconst BoldChunk = (chunks: React.ReactNode) => <Typography fontWeight=\"bold\">{chunks}</Typography>;\n\n/* -------------------------------------------------------------------------------------------------\n * ConfirmDialogPublishAll\n * -----------------------------------------------------------------------------------------------*/\n\ninterface ConfirmDialogPublishAllProps\n  extends Pick<ConfirmBulkActionDialogProps, 'isOpen' | 'onToggleDialog'> {\n  isConfirmButtonLoading?: boolean;\n  onConfirm: () => void;\n}\n\nconst ConfirmDialogPublishAll = ({\n  isOpen,\n  onToggleDialog,\n  isConfirmButtonLoading = false,\n  onConfirm,\n}: ConfirmDialogPublishAllProps) => {\n  const { formatMessage } = useIntl();\n  const selectedEntries = useTable('ConfirmDialogPublishAll', (state) => state.selectedRows);\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler(getTranslation);\n  const { model, schema } = useDoc();\n  const [{ query }] = useQueryParams<{\n    plugins?: {\n      i18n?: {\n        locale?: string;\n      };\n    };\n  }>();\n\n  // TODO skipping this for now as there is a bug with the draft relation count that will be worked on separately\n  // see RFC \"Count draft relations\" in Notion\n  const enableDraftRelationsCount = false;\n\n  const {\n    data: countDraftRelations = 0,\n    isLoading,\n    error,\n  } = useGetManyDraftRelationCountQuery(\n    {\n      model,\n      documentIds: selectedEntries.map((entry) => entry.documentId),\n      locale: query?.plugins?.i18n?.locale,\n    },\n    {\n      skip: !enableDraftRelationsCount || selectedEntries.length === 0,\n    }\n  );\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({ type: 'danger', message: formatAPIError(error) });\n    }\n  }, [error, formatAPIError, toggleNotification]);\n\n  if (error) {\n    return null;\n  }\n\n  return (\n    <ConfirmBulkActionDialog\n      isOpen={isOpen && !isLoading}\n      onToggleDialog={onToggleDialog}\n      dialogBody={\n        <>\n          <Typography id=\"confirm-description\" textAlign=\"center\">\n            {countDraftRelations > 0 &&\n              formatMessage(\n                {\n                  id: getTranslation(`popUpwarning.warning.bulk-has-draft-relations.message`),\n                  defaultMessage:\n                    '<b>{count} {count, plural, one { relation } other { relations } } out of {entities} { entities, plural, one { entry } other { entries } } {count, plural, one { is } other { are } }</b> not published yet and might lead to unexpected behavior. ',\n                },\n                {\n                  b: BoldChunk,\n                  count: countDraftRelations,\n                  entities: selectedEntries.length,\n                }\n              )}\n            {formatMessage({\n              id: getTranslation('popUpWarning.bodyMessage.contentType.publish.all'),\n              defaultMessage: 'Are you sure you want to publish these entries?',\n            })}\n          </Typography>\n          {schema?.pluginOptions &&\n            'i18n' in schema.pluginOptions &&\n            schema?.pluginOptions.i18n && (\n              <Typography textColor=\"danger500\" textAlign=\"center\">\n                {formatMessage(\n                  {\n                    id: getTranslation('Settings.list.actions.publishAdditionalInfos'),\n                    defaultMessage:\n                      'This will publish the active locale versions <em>(from Internationalization)</em>',\n                  },\n                  {\n                    em: Emphasis,\n                  }\n                )}\n              </Typography>\n            )}\n        </>\n      }\n      endAction={\n        <Button\n          width={'50%'}\n          onClick={onConfirm}\n          variant=\"secondary\"\n          startIcon={<Check />}\n          loading={isConfirmButtonLoading}\n        >\n          {formatMessage({\n            id: 'app.utils.publish',\n            defaultMessage: 'Publish',\n          })}\n        </Button>\n      }\n    />\n  );\n};\n\nexport { ConfirmDialogPublishAll, ConfirmBulkActionDialog };\nexport type { ConfirmDialogPublishAllProps, ConfirmBulkActionDialogProps };\n", "import * as React from 'react';\n\nimport {\n  FormErrors,\n  getYupValidationErrors,\n  Table,\n  useQueryParams,\n  useTable,\n} from '@strapi/admin/strapi-admin';\nimport {\n  Box,\n  Button,\n  Flex,\n  IconButton,\n  Loader,\n  Modal,\n  Tooltip,\n  Typography,\n  TypographyComponent,\n  RawTable,\n  Tr,\n  Td,\n  Tbody,\n} from '@strapi/design-system';\nimport { ArrowsCounterClockwise, CheckCircle, CrossCircle, Pencil } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { Link, useLocation } from 'react-router-dom';\nimport { styled } from 'styled-components';\nimport { ValidationError } from 'yup';\n\nimport { useDocumentRBAC } from '../../../../features/DocumentRBAC';\nimport { useContentTypeSchema } from '../../../../hooks/useContentTypeSchema';\nimport { useDocumentActions } from '../../../../hooks/useDocumentActions';\nimport { useDocLayout } from '../../../../hooks/useDocumentLayout';\nimport { contentManagerApi } from '../../../../services/api';\nimport {\n  useGetAllDocumentsQuery,\n  usePublishManyDocumentsMutation,\n} from '../../../../services/documents';\nimport { buildValidParams } from '../../../../utils/api';\nimport { getTranslation } from '../../../../utils/translations';\nimport { createYupSchema } from '../../../../utils/validation';\nimport { DocumentStatus } from '../../../EditView/components/DocumentStatus';\n\nimport { ConfirmDialogPublishAll, ConfirmDialogPublishAllProps } from './ConfirmBulkActionDialog';\n\nimport type { BulkActionComponent } from '../../../../content-manager';\nimport type { Document } from '../../../../hooks/useDocument';\n\nconst TypographyMaxWidth = styled<TypographyComponent>(Typography)`\n  max-width: 300px;\n`;\n\nconst TableComponent = styled(RawTable)`\n  width: 100%;\n  table-layout: fixed;\n  td:first-child {\n    border-right: 1px solid ${({ theme }) => theme.colors.neutral150};\n  }\n  td:first-of-type {\n    padding: ${({ theme }) => theme.spaces[4]};\n  }\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * EntryValidationText\n * -----------------------------------------------------------------------------------------------*/\n\nconst formatErrorMessages = (errors: FormErrors, parentKey: string, formatMessage: any) => {\n  const messages: string[] = [];\n\n  Object.entries(errors).forEach(([key, value]) => {\n    const currentKey = parentKey ? `${parentKey}.${key}` : key;\n\n    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {\n      if ('id' in value && 'defaultMessage' in value) {\n        messages.push(\n          formatMessage(\n            {\n              id: `${value.id}.withField`,\n              defaultMessage: value.defaultMessage,\n            },\n            { field: currentKey }\n          )\n        );\n      } else {\n        messages.push(\n          ...formatErrorMessages(\n            // @ts-expect-error TODO: check why value is not compatible with FormErrors\n            value,\n            currentKey,\n            formatMessage\n          )\n        );\n      }\n    } else {\n      messages.push(\n        formatMessage(\n          {\n            id: `${value}.withField`,\n            defaultMessage: value,\n          },\n          { field: currentKey }\n        )\n      );\n    }\n  });\n\n  return messages;\n};\n\ninterface EntryValidationTextProps {\n  validationErrors?: FormErrors;\n  status: string;\n}\n\nconst EntryValidationText = ({ validationErrors, status }: EntryValidationTextProps) => {\n  const { formatMessage } = useIntl();\n\n  if (validationErrors) {\n    const validationErrorsMessages = formatErrorMessages(validationErrors, '', formatMessage).join(\n      ' '\n    );\n\n    return (\n      <Flex gap={2}>\n        <CrossCircle fill=\"danger600\" />\n        <Tooltip description={validationErrorsMessages}>\n          <TypographyMaxWidth textColor=\"danger600\" variant=\"omega\" fontWeight=\"semiBold\" ellipsis>\n            {validationErrorsMessages}\n          </TypographyMaxWidth>\n        </Tooltip>\n      </Flex>\n    );\n  }\n\n  if (status === 'published') {\n    return (\n      <Flex gap={2}>\n        <CheckCircle fill=\"success600\" />\n        <Typography textColor=\"success600\" fontWeight=\"bold\">\n          {formatMessage({\n            id: 'content-manager.bulk-publish.already-published',\n            defaultMessage: 'Already Published',\n          })}\n        </Typography>\n      </Flex>\n    );\n  }\n\n  if (status === 'modified') {\n    return (\n      <Flex gap={2}>\n        <ArrowsCounterClockwise fill=\"alternative600\" />\n        <Typography>\n          {formatMessage({\n            id: 'content-manager.bulk-publish.modified',\n            defaultMessage: 'Ready to publish changes',\n          })}\n        </Typography>\n      </Flex>\n    );\n  }\n\n  return (\n    <Flex gap={2}>\n      <CheckCircle fill=\"success600\" />\n      <Typography>\n        {formatMessage({\n          id: 'app.utils.ready-to-publish',\n          defaultMessage: 'Ready to publish',\n        })}\n      </Typography>\n    </Flex>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * SelectedEntriesTableContent\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SelectedEntriesTableContentProps {\n  isPublishing?: boolean;\n  rowsToDisplay?: TableRow[];\n  entriesToPublish?: TableRow['documentId'][];\n  validationErrors: Record<string, EntryValidationTextProps['validationErrors']>;\n}\n\nconst TABLE_HEADERS = [\n  { name: 'id', label: 'id' },\n  { name: 'name', label: 'name' },\n  { name: 'status', label: 'status' },\n  { name: 'publicationStatus', label: 'Publication status' },\n];\n\nconst SelectedEntriesTableContent = ({\n  isPublishing,\n  rowsToDisplay = [],\n  entriesToPublish = [],\n  validationErrors = {},\n}: SelectedEntriesTableContentProps) => {\n  const { pathname } = useLocation();\n  const { formatMessage } = useIntl();\n\n  const {\n    list: {\n      settings: { mainField },\n    },\n  } = useDocLayout();\n\n  const shouldDisplayMainField = mainField != null && mainField !== 'id';\n\n  return (\n    <Table.Content>\n      <Table.Head>\n        <Table.HeaderCheckboxCell />\n        {TABLE_HEADERS.filter((head) => head.name !== 'name' || shouldDisplayMainField).map(\n          (head) => (\n            <Table.HeaderCell key={head.name} {...head} />\n          )\n        )}\n      </Table.Head>\n      <Table.Loading />\n      <Table.Body>\n        {rowsToDisplay.map((row) => (\n          <Table.Row key={row.id}>\n            <Table.CheckboxCell id={row.id} />\n            <Table.Cell>\n              <Typography>{row.id}</Typography>\n            </Table.Cell>\n            {shouldDisplayMainField && (\n              <Table.Cell>\n                <Typography>{row[mainField as keyof TableRow]}</Typography>\n              </Table.Cell>\n            )}\n            <Table.Cell>\n              <DocumentStatus status={row.status} maxWidth={'min-content'} />\n            </Table.Cell>\n            <Table.Cell>\n              {isPublishing && entriesToPublish.includes(row.documentId) ? (\n                <Flex gap={2}>\n                  <Typography>\n                    {formatMessage({\n                      id: 'content-manager.success.record.publishing',\n                      defaultMessage: 'Publishing...',\n                    })}\n                  </Typography>\n                  <Loader small />\n                </Flex>\n              ) : (\n                <EntryValidationText\n                  validationErrors={validationErrors[row.documentId]}\n                  status={row.status}\n                />\n              )}\n            </Table.Cell>\n            <Table.Cell>\n              <Flex>\n                <IconButton\n                  tag={Link}\n                  to={{\n                    pathname: `${pathname}/${row.documentId}`,\n                    search: row.locale && `?plugins[i18n][locale]=${row.locale}`,\n                  }}\n                  state={{ from: pathname }}\n                  label={formatMessage({\n                    id: 'content-manager.bulk-publish.edit',\n                    defaultMessage: 'Edit',\n                  })}\n                  target=\"_blank\"\n                  marginLeft=\"auto\"\n                  variant=\"ghost\"\n                >\n                  <Pencil width={'1.6rem'} height={'1.6rem'} />\n                </IconButton>\n              </Flex>\n            </Table.Cell>\n          </Table.Row>\n        ))}\n      </Table.Body>\n    </Table.Content>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * PublicationStatusSummary\n * -----------------------------------------------------------------------------------------------*/\n\ninterface PublicationStatusSummaryProps {\n  count: number;\n  icon: React.ReactNode;\n  message: string;\n}\n\nconst PublicationStatusSummary = ({ count, icon, message }: PublicationStatusSummaryProps) => {\n  return (\n    <Flex justifyContent=\"space-between\" flex={1} gap={3}>\n      <Flex gap={2}>\n        {icon}\n        <Typography>{message}</Typography>\n      </Flex>\n      <Typography fontWeight=\"bold\">{count}</Typography>\n    </Flex>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * PublicationStatusGrid\n * -----------------------------------------------------------------------------------------------*/\n\ninterface PublicationStatusGridProps {\n  entriesReadyToPublishCount: number;\n  entriesModifiedCount: number;\n  entriesPublishedCount: number;\n  entriesWithErrorsCount: number;\n}\n\nconst PublicationStatusGrid = ({\n  entriesReadyToPublishCount,\n  entriesPublishedCount,\n  entriesModifiedCount,\n  entriesWithErrorsCount,\n}: PublicationStatusGridProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Box hasRadius borderColor=\"neutral150\">\n      <TableComponent colCount={2} rowCount={2}>\n        <Tbody>\n          <Tr>\n            <Td>\n              <PublicationStatusSummary\n                count={entriesReadyToPublishCount}\n                icon={<CheckCircle fill=\"success600\" />}\n                message={formatMessage({\n                  id: 'app.utils.ready-to-publish',\n                  defaultMessage: 'Ready to publish',\n                })}\n              />\n            </Td>\n            <Td>\n              <PublicationStatusSummary\n                count={entriesPublishedCount}\n                icon={<CheckCircle fill=\"success600\" />}\n                message={formatMessage({\n                  id: 'app.utils.already-published',\n                  defaultMessage: 'Already published',\n                })}\n              />\n            </Td>\n          </Tr>\n          <Tr>\n            <Td>\n              <PublicationStatusSummary\n                count={entriesModifiedCount}\n                icon={<ArrowsCounterClockwise fill=\"alternative600\" />}\n                message={formatMessage({\n                  id: 'content-manager.bulk-publish.modified',\n                  defaultMessage: 'Ready to publish changes',\n                })}\n              />\n            </Td>\n            <Td>\n              <PublicationStatusSummary\n                count={entriesWithErrorsCount}\n                icon={<CrossCircle fill=\"danger600\" />}\n                message={formatMessage({\n                  id: 'content-manager.bulk-publish.waiting-for-action',\n                  defaultMessage: 'Waiting for action',\n                })}\n              />\n            </Td>\n          </Tr>\n        </Tbody>\n      </TableComponent>\n    </Box>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * SelectedEntriesModalContent\n * -----------------------------------------------------------------------------------------------*/\n\ninterface TableRow extends Document {}\n\ninterface SelectedEntriesModalContentProps {\n  listViewSelectedEntries: TableRow[];\n  toggleModal: ConfirmDialogPublishAllProps['onToggleDialog'];\n  setListViewSelectedDocuments: (documents: TableRow[]) => void;\n  model: string;\n}\n\nconst SelectedEntriesModalContent = ({\n  listViewSelectedEntries,\n  toggleModal,\n  setListViewSelectedDocuments,\n  model,\n}: SelectedEntriesModalContentProps) => {\n  const { formatMessage } = useIntl();\n  const { schema, components } = useContentTypeSchema(model);\n  const documentIds = listViewSelectedEntries.map(({ documentId }) => documentId);\n\n  // We want to keep the selected entries order same as the list view\n  const [{ query }] = useQueryParams<{ sort?: string; plugins?: Record<string, any> }>();\n  const params = React.useMemo(() => buildValidParams(query), [query]);\n\n  // Fetch the documents based on the selected entries and update the modal table\n  const { data, isLoading, isFetching, refetch } = useGetAllDocumentsQuery(\n    {\n      model,\n      params: {\n        page: '1',\n        pageSize: documentIds.length.toString(),\n        sort: query.sort,\n        filters: {\n          documentId: {\n            $in: documentIds,\n          },\n        },\n        locale: query.plugins?.i18n?.locale,\n      },\n    },\n    {\n      selectFromResult: ({ data, ...restRes }) => ({ data: data?.results ?? [], ...restRes }),\n    }\n  );\n\n  // Validate the entries based on the schema to show errors if any\n  const { rows, validationErrors } = React.useMemo(() => {\n    if (data.length > 0 && schema) {\n      const validate = createYupSchema(\n        schema.attributes,\n        components,\n        // Since this is the \"Publish\" action, the validation\n        // schema must enforce the rules for published entities\n        { status: 'published' }\n      );\n      const validationErrors: Record<TableRow['documentId'], FormErrors> = {};\n      const rows = data.map((entry: Document) => {\n        try {\n          validate.validateSync(entry, { abortEarly: false });\n\n          return entry;\n        } catch (e) {\n          if (e instanceof ValidationError) {\n            validationErrors[entry.documentId] = getYupValidationErrors(e);\n          }\n\n          return entry;\n        }\n      });\n\n      return { rows, validationErrors };\n    }\n\n    return {\n      rows: [],\n      validationErrors: {},\n    };\n  }, [components, data, schema]);\n\n  const [isDialogOpen, setIsDialogOpen] = React.useState(false);\n\n  const { publishMany: bulkPublishAction, isLoading: isPublishing } = useDocumentActions();\n  const [, { isLoading: isSubmittingForm }] = usePublishManyDocumentsMutation();\n\n  const selectedRows = useTable('publishAction', (state) => state.selectedRows);\n\n  // Filter selected entries from the updated modal table rows\n  const selectedEntries = rows.filter((entry) =>\n    selectedRows.some((selectedEntry) => selectedEntry.documentId === entry.documentId)\n  );\n\n  const entriesToPublish = selectedEntries\n    .filter((entry) => !validationErrors[entry.documentId])\n    .map((entry) => entry.documentId);\n\n  const selectedEntriesWithErrorsCount = selectedEntries.filter(\n    ({ documentId }) => validationErrors[documentId]\n  ).length;\n  const selectedEntriesPublishedCount = selectedEntries.filter(\n    ({ status }) => status === 'published'\n  ).length;\n  const selectedEntriesModifiedCount = selectedEntries.filter(\n    ({ status, documentId }) => status === 'modified' && !validationErrors[documentId]\n  ).length;\n  const selectedEntriesWithNoErrorsCount =\n    selectedEntries.length - selectedEntriesWithErrorsCount - selectedEntriesPublishedCount;\n\n  const toggleDialog = () => setIsDialogOpen((prev) => !prev);\n\n  const handleConfirmBulkPublish = async () => {\n    toggleDialog();\n\n    const res = await bulkPublishAction({ model: model, documentIds: entriesToPublish, params });\n    if (!('error' in res)) {\n      const unpublishedEntries = rows.filter((row) => {\n        return !entriesToPublish.includes(row.documentId);\n      });\n      // Keep selection of the entries in list view that were not published\n      setListViewSelectedDocuments(unpublishedEntries);\n    }\n  };\n\n  return (\n    <>\n      <Modal.Body>\n        <PublicationStatusGrid\n          entriesReadyToPublishCount={\n            selectedEntriesWithNoErrorsCount - selectedEntriesModifiedCount\n          }\n          entriesPublishedCount={selectedEntriesPublishedCount}\n          entriesModifiedCount={selectedEntriesModifiedCount}\n          entriesWithErrorsCount={selectedEntriesWithErrorsCount}\n        />\n        <Box marginTop={7}>\n          <SelectedEntriesTableContent\n            isPublishing={isSubmittingForm}\n            rowsToDisplay={rows}\n            entriesToPublish={entriesToPublish}\n            validationErrors={validationErrors}\n          />\n        </Box>\n      </Modal.Body>\n      <Modal.Footer>\n        <Button onClick={toggleModal} variant=\"tertiary\">\n          {formatMessage({\n            id: 'app.components.Button.cancel',\n            defaultMessage: 'Cancel',\n          })}\n        </Button>\n        <Flex gap={2}>\n          <Button onClick={refetch} variant=\"tertiary\" loading={isFetching}>\n            {formatMessage({ id: 'app.utils.refresh', defaultMessage: 'Refresh' })}\n          </Button>\n          <Button\n            onClick={toggleDialog}\n            disabled={\n              selectedEntries.length === 0 ||\n              selectedEntries.length === selectedEntriesWithErrorsCount ||\n              selectedEntriesPublishedCount === selectedEntries.length ||\n              isLoading\n            }\n            loading={isPublishing || isSubmittingForm}\n          >\n            {formatMessage({ id: 'app.utils.publish', defaultMessage: 'Publish' })}\n          </Button>\n        </Flex>\n      </Modal.Footer>\n      <ConfirmDialogPublishAll\n        isOpen={isDialogOpen}\n        onToggleDialog={toggleDialog}\n        isConfirmButtonLoading={isPublishing || isSubmittingForm}\n        onConfirm={handleConfirmBulkPublish}\n      />\n    </>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * PublishAction\n * -----------------------------------------------------------------------------------------------*/\n\nconst PublishAction: BulkActionComponent = ({ documents, model }) => {\n  const { formatMessage } = useIntl();\n  // Publish button visibility\n  const hasPublishPermission = useDocumentRBAC('unpublishAction', (state) => state.canPublish);\n  const showPublishButton =\n    hasPublishPermission && documents.some(({ status }) => status !== 'published');\n\n  const setListViewSelectedDocuments = useTable('publishAction', (state) => state.selectRow);\n\n  const refetchList = () => {\n    contentManagerApi.util.invalidateTags([{ type: 'Document', id: `${model}_LIST` }]);\n  };\n\n  if (!showPublishButton) return null;\n\n  return {\n    actionType: 'publish',\n    variant: 'tertiary',\n    label: formatMessage({ id: 'app.utils.publish', defaultMessage: 'Publish' }),\n    dialog: {\n      type: 'modal',\n      title: formatMessage({\n        id: getTranslation('containers.ListPage.selectedEntriesModal.title'),\n        defaultMessage: 'Publish entries',\n      }),\n      content: ({ onClose }) => {\n        return (\n          <Table.Root rows={documents} defaultSelectedRows={documents} headers={TABLE_HEADERS}>\n            <SelectedEntriesModalContent\n              listViewSelectedEntries={documents}\n              toggleModal={() => {\n                onClose();\n                refetchList();\n              }}\n              setListViewSelectedDocuments={setListViewSelectedDocuments}\n              model={model}\n            />\n          </Table.Root>\n        );\n      },\n      onClose: () => {\n        refetchList();\n      },\n    },\n  };\n};\n\nexport { PublishAction, SelectedEntriesModalContent };\n", "import * as React from 'react';\n\nimport {\n  useStrapiApp,\n  DescriptionComponentRenderer,\n  useTable,\n  useQueryParams,\n} from '@strapi/admin/strapi-admin';\nimport { Box, ButtonProps, Flex, Typography } from '@strapi/design-system';\nimport { WarningCircle } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport { useDocumentRBAC } from '../../../../features/DocumentRBAC';\nimport { useDoc } from '../../../../hooks/useDocument';\nimport { useDocumentActions } from '../../../../hooks/useDocumentActions';\nimport { buildValidParams } from '../../../../utils/api';\nimport { getTranslation } from '../../../../utils/translations';\nimport {\n  DialogOptions,\n  DocumentActionButton,\n  ModalOptions,\n  NotificationOptions,\n} from '../../../EditView/components/DocumentActions';\n\nimport { PublishAction } from './PublishAction';\n\nimport type { BulkActionComponent, ContentManagerPlugin } from '../../../../content-manager';\n\ninterface BulkActionDescription {\n  dialog?: DialogOptions | NotificationOptions | ModalOptions;\n  disabled?: boolean;\n  icon?: React.ReactNode;\n  label: string;\n  onClick?: (event: React.SyntheticEvent) => void;\n  /**\n   * @default 'default'\n   */\n  type?: 'icon' | 'default';\n  /**\n   * @default 'secondary'\n   */\n  variant?: ButtonProps['variant'];\n}\n\n/* -------------------------------------------------------------------------------------------------\n * BulkActionsRenderer\n * -----------------------------------------------------------------------------------------------*/\n\nconst BulkActionsRenderer = () => {\n  const plugins = useStrapiApp('BulkActionsRenderer', (state) => state.plugins);\n\n  const { model, collectionType } = useDoc();\n  const { selectedRows } = useTable('BulkActionsRenderer', (state) => state);\n\n  return (\n    <Flex gap={2}>\n      <DescriptionComponentRenderer\n        props={{\n          model,\n          collectionType,\n          documents: selectedRows,\n        }}\n        descriptions={(\n          plugins['content-manager'].apis as ContentManagerPlugin['config']['apis']\n        ).getBulkActions()}\n      >\n        {(actions) => actions.map((action) => <DocumentActionButton key={action.id} {...action} />)}\n      </DescriptionComponentRenderer>\n    </Flex>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * DefaultBulkActions\n * -----------------------------------------------------------------------------------------------*/\n\nconst DeleteAction: BulkActionComponent = ({ documents, model }) => {\n  const { formatMessage } = useIntl();\n  const { schema: contentType } = useDoc();\n  const selectRow = useTable('DeleteAction', (state) => state.selectRow);\n  const hasI18nEnabled = Boolean(contentType?.pluginOptions?.i18n);\n  const [{ query }] = useQueryParams<{ plugins?: { i18n?: { locale?: string } } }>();\n  const params = React.useMemo(() => buildValidParams(query), [query]);\n  const hasDeletePermission = useDocumentRBAC('deleteAction', (state) => state.canDelete);\n  const { deleteMany: bulkDeleteAction, isLoading } = useDocumentActions();\n  const documentIds = documents.map(({ documentId }) => documentId);\n\n  const handleConfirmBulkDelete = async () => {\n    const res = await bulkDeleteAction({\n      documentIds,\n      model,\n      params,\n    });\n    if (!('error' in res)) {\n      selectRow([]);\n    }\n  };\n\n  if (!hasDeletePermission) return null;\n\n  return {\n    variant: 'danger-light',\n    label: formatMessage({ id: 'global.delete', defaultMessage: 'Delete' }),\n    dialog: {\n      type: 'dialog',\n      title: formatMessage({\n        id: 'app.components.ConfirmDialog.title',\n        defaultMessage: 'Confirmation',\n      }),\n      loading: isLoading,\n      content: (\n        <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n          <Flex justifyContent=\"center\">\n            <WarningCircle width=\"24px\" height=\"24px\" fill=\"danger600\" />\n          </Flex>\n          <Typography id=\"confirm-description\" textAlign=\"center\">\n            {formatMessage({\n              id: 'popUpWarning.bodyMessage.contentType.delete.all',\n              defaultMessage: 'Are you sure you want to delete these entries?',\n            })}\n          </Typography>\n          {hasI18nEnabled && (\n            <Box textAlign=\"center\" padding={3}>\n              <Typography textColor=\"danger500\">\n                {formatMessage(\n                  {\n                    id: getTranslation('Settings.list.actions.deleteAdditionalInfos'),\n                    defaultMessage:\n                      'This will delete the active locale versions <em>(from Internationalization)</em>',\n                  },\n                  {\n                    em: Emphasis,\n                  }\n                )}\n              </Typography>\n            </Box>\n          )}\n        </Flex>\n      ),\n      onConfirm: handleConfirmBulkDelete,\n    },\n  };\n};\n\nDeleteAction.type = 'delete';\n\nconst UnpublishAction: BulkActionComponent = ({ documents, model }) => {\n  const { formatMessage } = useIntl();\n  const { schema } = useDoc();\n  const selectRow = useTable('UnpublishAction', (state) => state.selectRow);\n  const hasPublishPermission = useDocumentRBAC('unpublishAction', (state) => state.canPublish);\n  const hasI18nEnabled = Boolean(schema?.pluginOptions?.i18n);\n  const hasDraftAndPublishEnabled = Boolean(schema?.options?.draftAndPublish);\n  const { unpublishMany: bulkUnpublishAction, isLoading } = useDocumentActions();\n  const documentIds = documents.map(({ documentId }) => documentId);\n  const [{ query }] = useQueryParams();\n  const params = React.useMemo(() => buildValidParams(query), [query]);\n\n  const handleConfirmBulkUnpublish = async () => {\n    const data = await bulkUnpublishAction({ documentIds, model, params });\n    if (!('error' in data)) {\n      selectRow([]);\n    }\n  };\n\n  const showUnpublishButton =\n    hasDraftAndPublishEnabled &&\n    hasPublishPermission &&\n    documents.some((entry) => entry.status === 'published' || entry.status === 'modified');\n\n  if (!showUnpublishButton) return null;\n\n  return {\n    variant: 'tertiary',\n    label: formatMessage({ id: 'app.utils.unpublish', defaultMessage: 'Unpublish' }),\n    dialog: {\n      type: 'dialog',\n      title: formatMessage({\n        id: 'app.components.ConfirmDialog.title',\n        defaultMessage: 'Confirmation',\n      }),\n      loading: isLoading,\n      content: (\n        <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n          <Flex justifyContent=\"center\">\n            <WarningCircle width=\"24px\" height=\"24px\" fill=\"danger600\" />\n          </Flex>\n          <Typography id=\"confirm-description\" textAlign=\"center\">\n            {formatMessage({\n              id: 'popUpWarning.bodyMessage.contentType.unpublish.all',\n              defaultMessage: 'Are you sure you want to unpublish these entries?',\n            })}\n          </Typography>\n          {hasI18nEnabled && (\n            <Box textAlign=\"center\" padding={3}>\n              <Typography textColor=\"danger500\">\n                {formatMessage(\n                  {\n                    id: getTranslation('Settings.list.actions.unpublishAdditionalInfos'),\n                    defaultMessage:\n                      'This will unpublish the active locale versions <em>(from Internationalization)</em>',\n                  },\n                  {\n                    em: Emphasis,\n                  }\n                )}\n              </Typography>\n            </Box>\n          )}\n        </Flex>\n      ),\n      confirmButton: formatMessage({\n        id: 'app.utils.unpublish',\n        defaultMessage: 'Unpublish',\n      }),\n      onConfirm: handleConfirmBulkUnpublish,\n    },\n  };\n};\n\nUnpublishAction.type = 'unpublish';\n\nconst Emphasis = (chunks: React.ReactNode) => (\n  <Typography fontWeight=\"semiBold\" textColor=\"danger500\">\n    {chunks}\n  </Typography>\n);\n\nconst DEFAULT_BULK_ACTIONS: BulkActionComponent[] = [PublishAction, UnpublishAction, DeleteAction];\n\nexport { DEFAULT_BULK_ACTIONS, BulkActionsRenderer, Emphasis };\nexport type { BulkActionDescription };\n", "import { Box, Flex, Typography } from '@strapi/design-system';\nimport { ChevronRight } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport { getTranslation } from '../../../utils/translations';\n\nimport type { ProhibitedCloningField } from '../../../../../shared/contracts/collection-types';\n\ntype Reason = ProhibitedCloningField[1];\n\ninterface AutoCloneFailureModalBodyProps {\n  prohibitedFields: ProhibitedCloningField[];\n}\n\nconst AutoCloneFailureModalBody = ({ prohibitedFields }: AutoCloneFailureModalBodyProps) => {\n  const { formatMessage } = useIntl();\n\n  const getDefaultErrorMessage = (reason: Reason) => {\n    switch (reason) {\n      case 'relation':\n        return 'Duplicating the relation could remove it from the original entry.';\n      case 'unique':\n        return 'Identical values in a unique field are not allowed';\n      default:\n        return reason;\n    }\n  };\n\n  return (\n    <>\n      <Typography variant=\"beta\">\n        {formatMessage({\n          id: getTranslation('containers.list.autoCloneModal.title'),\n          defaultMessage: \"This entry can't be duplicated directly.\",\n        })}\n      </Typography>\n      <Box marginTop={2}>\n        <Typography textColor=\"neutral600\">\n          {formatMessage({\n            id: getTranslation('containers.list.autoCloneModal.description'),\n            defaultMessage:\n              \"A new entry will be created with the same content, but you'll have to change the following fields to save it.\",\n          })}\n        </Typography>\n      </Box>\n      <Flex marginTop={6} gap={2} direction=\"column\" alignItems=\"stretch\">\n        {prohibitedFields.map(([fieldPath, reason]) => (\n          <Flex\n            direction=\"column\"\n            gap={2}\n            alignItems=\"flex-start\"\n            borderColor=\"neutral200\"\n            hasRadius\n            padding={6}\n            key={fieldPath.join()}\n          >\n            <Flex direction=\"row\" tag=\"ol\">\n              {fieldPath.map((pathSegment, index) => (\n                <Typography fontWeight=\"semiBold\" tag=\"li\" key={index}>\n                  {pathSegment}\n                  {index !== fieldPath.length - 1 && (\n                    <ChevronRight\n                      fill=\"neutral500\"\n                      height=\"0.8rem\"\n                      width=\"0.8rem\"\n                      style={{ margin: '0 0.8rem' }}\n                    />\n                  )}\n                </Typography>\n              ))}\n            </Flex>\n            <Typography tag=\"p\" textColor=\"neutral600\">\n              {formatMessage({\n                id: getTranslation(`containers.list.autoCloneModal.error.${reason}`),\n                defaultMessage: getDefaultErrorMessage(reason),\n              })}\n            </Typography>\n          </Flex>\n        ))}\n      </Flex>\n    </>\n  );\n};\n\nexport { AutoCloneFailureModalBody };\nexport type { AutoCloneFailureModalBodyProps };\n", "import * as React from 'react';\n\nimport {\n  DescriptionComponentRenderer,\n  useNotification,\n  useStrapiApp,\n  useQueryParams,\n} from '@strapi/admin/strapi-admin';\nimport { Button, LinkButton, Modal } from '@strapi/design-system';\nimport { Duplicate, Pencil } from '@strapi/icons';\nimport { stringify } from 'qs';\nimport { useIntl } from 'react-intl';\nimport { NavLink, useNavigate } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { useDocumentRBAC } from '../../../features/DocumentRBAC';\nimport { Document, useDoc } from '../../../hooks/useDocument';\nimport { useDocumentActions } from '../../../hooks/useDocumentActions';\nimport { isBaseQueryError } from '../../../utils/api';\nimport { DocumentActionsMenu } from '../../EditView/components/DocumentActions';\n\nimport { AutoCloneFailureModalBody } from './AutoCloneFailureModal';\n\nimport type { ProhibitedCloningField } from '../../../../../shared/contracts/collection-types';\nimport type {\n  ContentManagerPlugin,\n  DocumentActionComponent,\n  DocumentActionProps,\n} from '../../../content-manager';\n\n/* -------------------------------------------------------------------------------------------------\n * TableActions\n * -----------------------------------------------------------------------------------------------*/\n\ninterface TableActionsProps {\n  document: Document;\n}\n\nconst TableActions = ({ document }: TableActionsProps) => {\n  const { formatMessage } = useIntl();\n  const { model, collectionType } = useDoc();\n  const plugins = useStrapiApp('TableActions', (state) => state.plugins);\n\n  const props: DocumentActionProps = {\n    activeTab: null,\n    model,\n    documentId: document.documentId,\n    collectionType,\n    document,\n  };\n\n  return (\n    <DescriptionComponentRenderer\n      props={props}\n      descriptions={(plugins['content-manager'].apis as ContentManagerPlugin['config']['apis'])\n        .getDocumentActions('table-row')\n        // We explicitly remove the PublishAction from description so we never render it and we don't make unnecessary requests.\n        .filter((action) => action.name !== 'PublishAction')}\n    >\n      {(actions) => {\n        const tableRowActions = actions.filter((action) => {\n          const positions = Array.isArray(action.position) ? action.position : [action.position];\n          return positions.includes('table-row');\n        });\n\n        return (\n          <DocumentActionsMenu\n            actions={tableRowActions}\n            label={formatMessage({\n              id: 'content-manager.containers.list.table.row-actions',\n              defaultMessage: 'Row actions',\n            })}\n            variant=\"ghost\"\n          />\n        );\n      }}\n    </DescriptionComponentRenderer>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * TableActionComponents\n * -----------------------------------------------------------------------------------------------*/\n\nconst EditAction: DocumentActionComponent = ({ documentId }) => {\n  const navigate = useNavigate();\n  const { formatMessage } = useIntl();\n  const { canRead } = useDocumentRBAC('EditAction', ({ canRead }) => ({ canRead }));\n  const { toggleNotification } = useNotification();\n  const [{ query }] = useQueryParams<{ plugins?: object }>();\n\n  return {\n    disabled: !canRead,\n    icon: <StyledPencil />,\n    label: formatMessage({\n      id: 'content-manager.actions.edit.label',\n      defaultMessage: 'Edit',\n    }),\n    position: 'table-row',\n    onClick: async () => {\n      if (!documentId) {\n        console.error(\n          \"You're trying to edit a document without an id, this is likely a bug with Strapi. Please open an issue.\"\n        );\n\n        toggleNotification({\n          message: formatMessage({\n            id: 'content-manager.actions.edit.error',\n            defaultMessage: 'An error occurred while trying to edit the document.',\n          }),\n          type: 'danger',\n        });\n\n        return;\n      }\n\n      navigate({\n        pathname: documentId,\n        search: stringify({\n          plugins: query.plugins,\n        }),\n      });\n    },\n  };\n};\n\nEditAction.type = 'edit';\nEditAction.position = 'table-row';\n\n/**\n * Because the icon system is completely broken, we have to do\n * this to remove the fill from the cog.\n */\nconst StyledPencil = styled(Pencil)`\n  path {\n    fill: currentColor;\n  }\n`;\n\nconst CloneAction: DocumentActionComponent = ({ model, documentId }) => {\n  const navigate = useNavigate();\n  const { formatMessage } = useIntl();\n  const { canCreate } = useDocumentRBAC('CloneAction', ({ canCreate }) => ({ canCreate }));\n  const { toggleNotification } = useNotification();\n  const { autoClone } = useDocumentActions();\n  const [prohibitedFields, setProhibitedFields] = React.useState<ProhibitedCloningField[]>([]);\n  const [{ query }] = useQueryParams<{ plugins?: Record<string, any> }>();\n\n  return {\n    disabled: !canCreate,\n    icon: <StyledDuplicate />,\n    label: formatMessage({\n      id: 'content-manager.actions.clone.label',\n      defaultMessage: 'Duplicate',\n    }),\n    position: 'table-row',\n    onClick: async () => {\n      if (!documentId) {\n        console.error(\n          \"You're trying to clone a document in the table without an id, this is likely a bug with Strapi. Please open an issue.\"\n        );\n\n        toggleNotification({\n          message: formatMessage({\n            id: 'content-manager.actions.clone.error',\n            defaultMessage: 'An error occurred while trying to clone the document.',\n          }),\n          type: 'danger',\n        });\n\n        return;\n      }\n\n      const res = await autoClone({\n        model,\n        sourceId: documentId,\n        locale: query.plugins?.i18n?.locale,\n      });\n\n      if ('data' in res) {\n        navigate({\n          pathname: res.data.documentId,\n          search: stringify({\n            plugins: query.plugins,\n          }),\n        });\n\n        /**\n         * We return true because we don't need to show a modal anymore.\n         */\n        return true;\n      }\n\n      if (\n        isBaseQueryError(res.error) &&\n        res.error.details &&\n        typeof res.error.details === 'object' &&\n        'prohibitedFields' in res.error.details &&\n        Array.isArray(res.error.details.prohibitedFields)\n      ) {\n        const prohibitedFields = res.error.details.prohibitedFields as ProhibitedCloningField[];\n\n        setProhibitedFields(prohibitedFields);\n      }\n    },\n    dialog: {\n      type: 'modal',\n      title: formatMessage({\n        id: 'content-manager.containers.list.autoCloneModal.header',\n        defaultMessage: 'Duplicate',\n      }),\n      content: <AutoCloneFailureModalBody prohibitedFields={prohibitedFields} />,\n      footer: ({ onClose }) => {\n        return (\n          <Modal.Footer>\n            <Button onClick={onClose} variant=\"tertiary\">\n              {formatMessage({\n                id: 'cancel',\n                defaultMessage: 'Cancel',\n              })}\n            </Button>\n            <LinkButton\n              tag={NavLink}\n              to={{\n                pathname: `clone/${documentId}`,\n                search: stringify({\n                  plugins: query.plugins,\n                }),\n              }}\n            >\n              {formatMessage({\n                id: 'content-manager.containers.list.autoCloneModal.create',\n                defaultMessage: 'Create',\n              })}\n            </LinkButton>\n          </Modal.Footer>\n        );\n      },\n    },\n  };\n};\n\nCloneAction.type = 'clone';\nCloneAction.position = 'table-row';\n\n/**\n * Because the icon system is completely broken, we have to do\n * this to remove the fill from the cog.\n */\nconst StyledDuplicate = styled(Duplicate)`\n  path {\n    fill: currentColor;\n  }\n`;\n\nconst DEFAULT_TABLE_ROW_ACTIONS = [EditAction, CloneAction];\n\nexport { TableActions, DEFAULT_TABLE_ROW_ACTIONS };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBMA,IAAAA,0BAA0B,CAAC,EAC/BC,gBACAC,SAAS,OACTC,YACAC,UAAS,MACoB;AAC7B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEC,wBAACC,OAAOC,MAAI;IAACC,MAAMR;kBACjBS,yBAACH,OAAOI,SAAO;;YACbL,wBAACC,OAAOK,QAAM;oBACXR,cAAc;YACbS,IAAI;YACJC,gBAAgB;UAClB,CAAA;;YAEFR,wBAACC,OAAOQ,MAAI;UACV,cAAAL,yBAACM,MAAAA;YAAKC,WAAU;YAASC,YAAW;YAAUC,KAAK;;kBACjDb,wBAACU,MAAAA;gBAAKI,gBAAe;gBACnB,cAAAd,wBAACe,cAAAA;kBAAcC,OAAM;kBAAOC,QAAO;kBAAOC,MAAK;;;cAEhDtB;;;;YAGLQ,yBAACH,OAAOkB,QAAM;;gBACZnB,wBAACC,OAAOmB,QAAM;cACZ,cAAApB,wBAACqB,QAAAA;gBAAOL,OAAO;gBAAOM,SAAS5B;gBAAgB6B,SAAQ;0BACpDzB,cAAc;kBACbS,IAAI;kBACJC,gBAAgB;gBAClB,CAAA;;;YAGHX;;;;;;AAKX;AAMA,IAAM2B,YAAY,CAACC,eAA4BzB,wBAAC0B,YAAAA;EAAWC,YAAW;EAAQF,UAAAA;;AAYxEG,IAAAA,0BAA0B,CAAC,EAC/BjC,QACAD,gBACAmC,yBAAyB,OACzBC,UAAS,MACoB;;AAC7B,QAAM,EAAEhC,cAAa,IAAKC,QAAAA;AAC1B,QAAMgC,kBAAkBC,SAAS,2BAA2B,CAACC,UAAUA,MAAMC,YAAY;AACzF,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,yBAAyBC,eAAc,IAAKC,mBAAmBC,cAAAA;AACvE,QAAM,EAAEC,OAAOC,OAAM,IAAKC,OAAAA;AAC1B,QAAM,CAAC,EAAEC,MAAK,CAAE,IAAIC,eAAAA;AAUpB,QAAMC,4BAA4B;AAElC,QAAM,EACJC,MAAMC,sBAAsB,GAC5BC,WACAC,MAAK,IACHC,kCACF;IACEV;IACAW,aAAarB,gBAAgBsB,IAAI,CAACC,UAAUA,MAAMC,UAAU;IAC5DC,SAAQZ,0CAAOa,YAAPb,mBAAgBc,SAAhBd,mBAAsBY;KAEhC;IACEG,MAAM,CAACb;EACT,CAAA;AAGFc,EAAMC,gBAAU,MAAA;AACd,QAAIX,OAAO;AACTf,yBAAmB;QAAE2B,MAAM;QAAUC,SAASzB,eAAeY,KAAAA;MAAO,CAAA;IACtE;KACC;IAACA;IAAOZ;IAAgBH;EAAmB,CAAA;AAE9C,MAAIe,OAAO;AACT,WAAO;EACT;AAEA,aACElD,wBAACP,yBAAAA;IACCE,QAAQA,UAAU,CAACsD;IACnBvD;IACAE,gBACEQ,yBAAA4D,6BAAA;;YACE5D,yBAACsB,YAAAA;UAAWnB,IAAG;UAAsB0D,WAAU;;YAC5CjB,sBAAsB,KACrBlD,cACE;cACES,IAAIiC,eAAe,uDAAuD;cAC1EhC,gBACE;eAEJ;cACE0D,GAAG1C;cACH2C,OAAOnB;cACPoB,UAAUrC,gBAAgBsC;YAC5B,CAAA;YAEHvE,cAAc;cACbS,IAAIiC,eAAe,kDAAA;cACnBhC,gBAAgB;YAClB,CAAA;;;SAEDkC,iCAAQ4B,kBACP,UAAU5B,OAAO4B,kBACjB5B,iCAAQ4B,cAAcZ,aACpB1D,wBAAC0B,YAAAA;UAAW6C,WAAU;UAAYN,WAAU;oBACzCnE,cACC;YACES,IAAIiC,eAAe,8CAAA;YACnBhC,gBACE;aAEJ;YACEgE,IAAIC;UACN,CAAA;;;;IAMZ5E,eACEG,wBAACqB,QAAAA;MACCL,OAAO;MACPM,SAASQ;MACTP,SAAQ;MACRmD,eAAW1E,wBAAC2E,eAAAA,CAAAA,CAAAA;MACZC,SAAS/C;gBAER/B,cAAc;QACbS,IAAI;QACJC,gBAAgB;MAClB,CAAA;;;AAKV;;;AC5IA,IAAMqE,qBAAqBC,GAA4BC,UAAAA;;;AAIvD,IAAMC,iBAAiBF,GAAOG,QAAAA;;;;8BAIA,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAOC,UAAU;;;eAGrD,CAAC,EAAEF,MAAK,MAAOA,MAAMG,OAAO,CAAA,CAAE;;;AAQ7C,IAAMC,sBAAsB,CAACC,QAAoBC,WAAmBC,kBAAAA;AAClE,QAAMC,WAAqB,CAAA;AAE3BC,SAAOC,QAAQL,MAAQM,EAAAA,QAAQ,CAAC,CAACC,KAAKC,KAAM,MAAA;AAC1C,UAAMC,aAAaR,YAAY,GAAGA,SAAAA,IAAaM,GAAI,KAAIA;AAEvD,QAAI,OAAOC,UAAU,YAAYA,UAAU,QAAQ,CAACE,MAAMC,QAAQH,KAAQ,GAAA;AACxE,UAAI,QAAQA,SAAS,oBAAoBA,OAAO;AAC9CL,iBAASS,KACPV,cACE;UACEW,IAAI,GAAGL,MAAMK,EAAE;UACfC,gBAAgBN,MAAMM;WAExB;UAAEC,OAAON;QAAW,CAAA,CAAA;aAGnB;AACLN,iBAASS,KACJb,GAAAA;;UAEDS;UACAC;UACAP;QAAAA,CAAAA;MAGN;WACK;AACLC,eAASS,KACPV,cACE;QACEW,IAAI,GAAGL,KAAM;QACbM,gBAAgBN;SAElB;QAAEO,OAAON;MAAW,CAAA,CAAA;IAG1B;EACF,CAAA;AAEA,SAAON;AACT;AAOA,IAAMa,sBAAsB,CAAC,EAAEC,kBAAkBC,OAAM,MAA4B;AACjF,QAAM,EAAEhB,cAAa,IAAKiB,QAAAA;AAE1B,MAAIF,kBAAkB;AACpB,UAAMG,2BAA2BrB,oBAAoBkB,kBAAkB,IAAIf,aAAAA,EAAemB,KACxF,GAAA;AAGF,eACEC,0BAACC,MAAAA;MAAKC,KAAK;;YACTC,yBAACC,eAAAA;UAAYC,MAAK;;YAClBF,yBAACG,aAAAA;UAAQC,aAAaT;UACpB,cAAAK,yBAACnC,oBAAAA;YAAmBwC,WAAU;YAAYC,SAAQ;YAAQC,YAAW;YAAWC,UAAQ;YACrFb,UAAAA;;;;;EAKX;AAEA,MAAIF,WAAW,aAAa;AAC1B,eACEI,0BAACC,MAAAA;MAAKC,KAAK;;YACTC,yBAACS,eAAAA;UAAYP,MAAK;;YAClBF,yBAACjC,YAAAA;UAAWsC,WAAU;UAAaE,YAAW;oBAC3C9B,cAAc;YACbW,IAAI;YACJC,gBAAgB;UAClB,CAAA;;;;EAIR;AAEA,MAAII,WAAW,YAAY;AACzB,eACEI,0BAACC,MAAAA;MAAKC,KAAK;;YACTC,yBAACU,eAAAA;UAAuBR,MAAK;;YAC7BF,yBAACjC,YAAAA;oBACEU,cAAc;YACbW,IAAI;YACJC,gBAAgB;UAClB,CAAA;;;;EAIR;AAEA,aACEQ,0BAACC,MAAAA;IAAKC,KAAK;;UACTC,yBAACS,eAAAA;QAAYP,MAAK;;UAClBF,yBAACjC,YAAAA;kBACEU,cAAc;UACbW,IAAI;UACJC,gBAAgB;QAClB,CAAA;;;;AAIR;AAaA,IAAMsB,gBAAgB;EACpB;IAAEC,MAAM;IAAMC,OAAO;EAAK;EAC1B;IAAED,MAAM;IAAQC,OAAO;EAAO;EAC9B;IAAED,MAAM;IAAUC,OAAO;EAAS;EAClC;IAAED,MAAM;IAAqBC,OAAO;EAAqB;AAC1D;AAED,IAAMC,8BAA8B,CAAC,EACnCC,cACAC,gBAAgB,CAAA,GAChBC,mBAAmB,CAAA,GACnBzB,mBAAmB,CAAA,EAAE,MACY;AACjC,QAAM,EAAE0B,SAAQ,IAAKC,YAAAA;AACrB,QAAM,EAAE1C,cAAa,IAAKiB,QAAAA;AAE1B,QAAM,EACJ0B,MAAM,EACJC,UAAU,EAAEC,UAAS,EAAE,EACxB,IACCC,aAAAA;AAEJ,QAAMC,yBAAyBF,aAAa,QAAQA,cAAc;AAElE,aACEzB,0BAAC4B,MAAMC,SAAO;;UACZ7B,0BAAC4B,MAAME,MAAI;;cACT3B,yBAACyB,MAAMG,oBAAkB,CAAA,CAAA;UACxBjB,cAAckB,OAAO,CAACC,SAASA,KAAKlB,SAAS,UAAUY,sBAAAA,EAAwBO,IAC9E,CAACD,aACC9B,yBAACyB,MAAMO,YAAU;YAAkB,GAAGF;UAAfA,GAAAA,KAAKlB,IAAI,CAAA;;;UAItCZ,yBAACyB,MAAMQ,SAAO,CAAA,CAAA;UACdjC,yBAACyB,MAAMS,MAAI;QACRlB,UAAAA,cAAce,IAAI,CAACI,YAClBtC,0BAAC4B,MAAMW,KAAG;;gBACRpC,yBAACyB,MAAMY,cAAY;cAACjD,IAAI+C,IAAI/C;;gBAC5BY,yBAACyB,MAAMa,MAAI;cACT,cAAAtC,yBAACjC,YAAAA;gBAAYoE,UAAAA,IAAI/C;;;YAElBoC,8BACCxB,yBAACyB,MAAMa,MAAI;cACT,cAAAtC,yBAACjC,YAAAA;gBAAYoE,UAAAA,IAAIb,SAA4B;;;gBAGjDtB,yBAACyB,MAAMa,MAAI;cACT,cAAAtC,yBAACuC,gBAAAA;gBAAe9C,QAAQ0C,IAAI1C;gBAAQ+C,UAAU;;;gBAEhDxC,yBAACyB,MAAMa,MAAI;cACRvB,UAAAA,gBAAgBE,iBAAiBwB,SAASN,IAAIO,UAAU,QACvD7C,0BAACC,MAAAA;gBAAKC,KAAK;;sBACTC,yBAACjC,YAAAA;8BACEU,cAAc;sBACbW,IAAI;sBACJC,gBAAgB;oBAClB,CAAA;;sBAEFW,yBAAC2C,QAAAA;oBAAOC,OAAK;;;uBAGf5C,yBAACT,qBAAAA;gBACCC,kBAAkBA,iBAAiB2C,IAAIO,UAAU;gBACjDjD,QAAQ0C,IAAI1C;;;gBAIlBO,yBAACyB,MAAMa,MAAI;cACT,cAAAtC,yBAACF,MAAAA;gBACC,cAAAE,yBAAC6C,YAAAA;kBACCC,KAAKC;kBACLC,IAAI;oBACF9B,UAAU,GAAGA,QAAS,IAAGiB,IAAIO,UAAU;oBACvCO,QAAQd,IAAIe,UAAU,0BAA0Bf,IAAIe,MAAM;kBAC5D;kBACAC,OAAO;oBAAEC,MAAMlC;kBAAS;kBACxBL,OAAOpC,cAAc;oBACnBW,IAAI;oBACJC,gBAAgB;kBAClB,CAAA;kBACAgE,QAAO;kBACPC,YAAW;kBACXhD,SAAQ;kBAER,cAAAN,yBAACuD,eAAAA;oBAAOC,OAAO;oBAAUC,QAAQ;;;;;;QAhDzBtB,GAAAA,IAAI/C,EAAE,CAAA;;;;AAyDhC;AAYA,IAAMsE,2BAA2B,CAAC,EAAEC,OAAOC,MAAMC,QAAO,MAAiC;AACvF,aACEhE,0BAACC,MAAAA;IAAKgE,gBAAe;IAAgBC,MAAM;IAAGhE,KAAK;;UACjDF,0BAACC,MAAAA;QAAKC,KAAK;;UACR6D;cACD5D,yBAACjC,YAAAA;YAAY8F,UAAAA;;;;UAEf7D,yBAACjC,YAAAA;QAAWwC,YAAW;QAAQoD,UAAAA;;;;AAGrC;AAaA,IAAMK,wBAAwB,CAAC,EAC7BC,4BACAC,uBACAC,sBACAC,uBAAsB,MACK;AAC3B,QAAM,EAAE3F,cAAa,IAAKiB,QAAAA;AAE1B,aACEM,yBAACqE,KAAAA;IAAIC,WAAS;IAACC,aAAY;IACzB,cAAAvE,yBAAChC,gBAAAA;MAAewG,UAAU;MAAGC,UAAU;MACrC,cAAA5E,0BAAC6E,OAAAA;;cACC7E,0BAAC8E,IAAAA;;kBACC3E,yBAAC4E,IAAAA;gBACC,cAAA5E,yBAAC0D,0BAAAA;kBACCC,OAAOM;kBACPL,UAAM5D,yBAACS,eAAAA;oBAAYP,MAAK;;kBACxB2D,SAASpF,cAAc;oBACrBW,IAAI;oBACJC,gBAAgB;kBAClB,CAAA;;;kBAGJW,yBAAC4E,IAAAA;gBACC,cAAA5E,yBAAC0D,0BAAAA;kBACCC,OAAOO;kBACPN,UAAM5D,yBAACS,eAAAA;oBAAYP,MAAK;;kBACxB2D,SAASpF,cAAc;oBACrBW,IAAI;oBACJC,gBAAgB;kBAClB,CAAA;;;;;cAINQ,0BAAC8E,IAAAA;;kBACC3E,yBAAC4E,IAAAA;gBACC,cAAA5E,yBAAC0D,0BAAAA;kBACCC,OAAOQ;kBACPP,UAAM5D,yBAACU,eAAAA;oBAAuBR,MAAK;;kBACnC2D,SAASpF,cAAc;oBACrBW,IAAI;oBACJC,gBAAgB;kBAClB,CAAA;;;kBAGJW,yBAAC4E,IAAAA;gBACC,cAAA5E,yBAAC0D,0BAAAA;kBACCC,OAAOS;kBACPR,UAAM5D,yBAACC,eAAAA;oBAAYC,MAAK;;kBACxB2D,SAASpF,cAAc;oBACrBW,IAAI;oBACJC,gBAAgB;kBAClB,CAAA;;;;;;;;;AAQhB;AAeMwF,IAAAA,8BAA8B,CAAC,EACnCC,yBACAC,aACAC,8BACAC,MAAK,MAC4B;;AACjC,QAAM,EAAExG,cAAa,IAAKiB,QAAAA;AAC1B,QAAM,EAAEwF,QAAQC,WAAU,IAAKC,qBAAqBH,KAAAA;AACpD,QAAMI,cAAcP,wBAAwB/C,IAAI,CAAC,EAAEW,WAAU,MAAOA,UAAAA;AAGpE,QAAM,CAAC,EAAE4C,MAAK,CAAE,IAAIC,eAAAA;AACpB,QAAMC,SAAeC,eAAQ,MAAMC,iBAAiBJ,KAAQ,GAAA;IAACA;EAAM,CAAA;AAGnE,QAAM,EAAEK,MAAMC,WAAWC,YAAYC,QAAO,IAAKC,wBAC/C;IACEd;IACAO,QAAQ;MACNQ,MAAM;MACNC,UAAUZ,YAAYa,OAAOC,SAAQ;MACrCC,MAAMd,MAAMc;MACZC,SAAS;QACP3D,YAAY;UACV4D,KAAKjB;QACP;MACF;MACAnC,SAAQoC,iBAAMiB,YAANjB,mBAAekB,SAAflB,mBAAqBpC;IAC/B;KAEF;IACEuD,kBAAkB,CAAC,EAAEd,MAAAA,OAAM,GAAGe,QAAAA,OAAe;MAAEf,OAAMA,SAAAA,gBAAAA,MAAMgB,YAAW,CAAA;MAAI,GAAGD;;EAC/E,CAAA;AAIF,QAAM,EAAEE,MAAMpH,iBAAgB,IAAWiG,eAAQ,MAAA;AAC/C,QAAIE,KAAKO,SAAS,KAAKhB,QAAQ;AAC7B,YAAM2B,WAAWC;QACf5B,OAAO6B;QACP5B;;;QAGA;UAAE1F,QAAQ;QAAY;MAAA;AAExB,YAAMD,oBAA+D,CAAA;AACrE,YAAMoH,QAAOjB,KAAK5D,IAAI,CAACiF,UAAAA;AACrB,YAAI;AACFH,mBAASI,aAAaD,OAAO;YAAEE,YAAY;UAAM,CAAA;AAEjD,iBAAOF;QACT,SAASG,GAAG;AACV,cAAIA,aAAaC,iBAAiB;AAChC5H,YAAAA,kBAAiBwH,MAAMtE,UAAU,IAAI2E,uBAAuBF,CAAAA;UAC9D;AAEA,iBAAOH;QACT;MACF,CAAA;AAEA,aAAO;QAAEJ,MAAAA;QAAMpH,kBAAAA;MAAiB;IAClC;AAEA,WAAO;MACLoH,MAAM,CAAA;MACNpH,kBAAkB,CAAA;IACpB;KACC;IAAC2F;IAAYQ;IAAMT;EAAO,CAAA;AAE7B,QAAM,CAACoC,cAAcC,eAAAA,IAAyBC,gBAAS,KAAA;AAEvD,QAAM,EAAEC,aAAaC,mBAAmB9B,WAAW7E,aAAY,IAAK4G,mBAAAA;AACpE,QAAM,CAAA,EAAG,EAAE/B,WAAWgC,iBAAgB,CAAE,IAAIC,gCAAAA;AAE5C,QAAMC,eAAeC,SAAS,iBAAiB,CAAC5E,UAAUA,MAAM2E,YAAY;AAG5E,QAAME,kBAAkBpB,KAAK/E,OAAO,CAACmF,UACnCc,aAAaG,KAAK,CAACC,kBAAkBA,cAAcxF,eAAesE,MAAMtE,UAAU,CAAA;AAGpF,QAAMzB,mBAAmB+G,gBACtBnG,OAAO,CAACmF,UAAU,CAACxH,iBAAiBwH,MAAMtE,UAAU,CAAC,EACrDX,IAAI,CAACiF,UAAUA,MAAMtE,UAAU;AAElC,QAAMyF,iCAAiCH,gBAAgBnG,OACrD,CAAC,EAAEa,WAAU,MAAOlD,iBAAiBkD,UAAAA,CAAW,EAChDwD;AACF,QAAMkC,gCAAgCJ,gBAAgBnG,OACpD,CAAC,EAAEpC,OAAM,MAAOA,WAAW,WAAA,EAC3ByG;AACF,QAAMmC,+BAA+BL,gBAAgBnG,OACnD,CAAC,EAAEpC,QAAQiD,WAAU,MAAOjD,WAAW,cAAc,CAACD,iBAAiBkD,UAAAA,CAAW,EAClFwD;AACF,QAAMoC,mCACJN,gBAAgB9B,SAASiC,iCAAiCC;AAE5D,QAAMG,eAAe,MAAMhB,gBAAgB,CAACiB,SAAS,CAACA,IAAAA;AAEtD,QAAMC,2BAA2B,YAAA;AAC/BF,iBAAAA;AAEA,UAAMG,MAAM,MAAMhB,kBAAkB;MAAEzC;MAAcI,aAAapE;MAAkBuE;IAAO,CAAA;AAC1F,QAAI,EAAE,WAAWkD,MAAM;AACrB,YAAMC,qBAAqB/B,KAAK/E,OAAO,CAACM,QAAAA;AACtC,eAAO,CAAClB,iBAAiBwB,SAASN,IAAIO,UAAU;MAClD,CAAA;AAEAsC,mCAA6B2D,kBAAAA;IAC/B;EACF;AAEA,aACE9I,0BAAA+I,8BAAA;;UACE/I,0BAACgJ,MAAM3G,MAAI;;cACTlC,yBAACgE,uBAAAA;YACCC,4BACEqE,mCAAmCD;YAErCnE,uBAAuBkE;YACvBjE,sBAAsBkE;YACtBjE,wBAAwB+D;;cAE1BnI,yBAACqE,KAAAA;YAAIyE,WAAW;YACd,cAAA9I,yBAACc,6BAAAA;cACCC,cAAc6G;cACd5G,eAAe4F;cACf3F;cACAzB;;;;;UAINK,0BAACgJ,MAAME,QAAM;;cACX/I,yBAACgJ,QAAAA;YAAOC,SAASlE;YAAazE,SAAQ;sBACnC7B,cAAc;cACbW,IAAI;cACJC,gBAAgB;YAClB,CAAA;;cAEFQ,0BAACC,MAAAA;YAAKC,KAAK;;kBACTC,yBAACgJ,QAAAA;gBAAOC,SAASnD;gBAASxF,SAAQ;gBAAW4I,SAASrD;0BACnDpH,cAAc;kBAAEW,IAAI;kBAAqBC,gBAAgB;gBAAU,CAAA;;kBAEtEW,yBAACgJ,QAAAA;gBACCC,SAASV;gBACTY,UACEnB,gBAAgB9B,WAAW,KAC3B8B,gBAAgB9B,WAAWiC,kCAC3BC,kCAAkCJ,gBAAgB9B,UAClDN;gBAEFsD,SAASnI,gBAAgB6G;0BAExBnJ,cAAc;kBAAEW,IAAI;kBAAqBC,gBAAgB;gBAAU,CAAA;;;;;;UAI1EW,yBAACoJ,yBAAAA;QACCC,QAAQ/B;QACRgC,gBAAgBf;QAChBgB,wBAAwBxI,gBAAgB6G;QACxC4B,WAAWf;;;;AAInB;AAIkG,IAE5FgB,gBAAqC,CAAC,EAAEC,WAAWzE,MAAK,MAAE;AAC9D,QAAM,EAAExG,cAAa,IAAKiB,QAAAA;AAE1B,QAAMiK,uBAAuBC,gBAAgB,mBAAmB,CAACzG,UAAUA,MAAM0G,UAAU;AAC3F,QAAMC,oBACJH,wBAAwBD,UAAUzB,KAAK,CAAC,EAAExI,OAAM,MAAOA,WAAW,WAAA;AAEpE,QAAMuF,+BAA+B+C,SAAS,iBAAiB,CAAC5E,UAAUA,MAAM4G,SAAS;AAEzF,QAAMC,cAAc,MAAA;AAClBC,sBAAkBC,KAAKC,eAAe;MAAC;QAAEC,MAAM;QAAYhL,IAAI,GAAG6F,KAAM;MAAO;IAAE,CAAA;EACnF;AAEA,MAAI,CAAC6E,kBAAmB,QAAO;AAE/B,SAAO;IACLO,YAAY;IACZ/J,SAAS;IACTO,OAAOpC,cAAc;MAAEW,IAAI;MAAqBC,gBAAgB;IAAU,CAAA;IAC1EiL,QAAQ;MACNF,MAAM;MACNG,OAAO9L,cAAc;QACnBW,IAAIoL,eAAe,gDAAA;QACnBnL,gBAAgB;MAClB,CAAA;MACAoL,SAAS,CAAC,EAAEC,QAAO,MAAE;AACnB,mBACE1K,yBAACyB,MAAMkJ,MAAI;UAAC/D,MAAM8C;UAAWkB,qBAAqBlB;UAAWmB,SAASlK;UACpE,cAAAX,yBAAC6E,6BAAAA;YACCC,yBAAyB4E;YACzB3E,aAAa,MAAA;AACX2F,sBAAAA;AACAV,0BAAAA;YACF;YACAhF;YACAC;;;MAIR;MACAyF,SAAS,MAAA;AACPV,oBAAAA;MACF;IACF;EACF;AACF;;;ACljBkG,IAE5Fc,sBAAsB,MAAA;AAC1B,QAAMC,UAAUC,aAAa,uBAAuB,CAACC,UAAUA,MAAMF,OAAO;AAE5E,QAAM,EAAEG,OAAOC,eAAc,IAAKC,OAAAA;AAClC,QAAM,EAAEC,aAAY,IAAKC,SAAS,uBAAuB,CAACL,UAAUA,KAAAA;AAEpE,aACEM,yBAACC,MAAAA;IAAKC,KAAK;IACT,cAAAF,yBAACG,8BAAAA;MACCC,OAAO;QACLT;QACAC;QACAS,WAAWP;MACb;MACAQ,cAAc,QACJ,iBAAA,EAAmBC,KAC3BC,eAAc;MAEf,UAAA,CAACC,YAAYA,QAAQC,IAAI,CAACC,eAAWX,yBAACY,sBAAAA;QAAsC,GAAGD;MAAfA,GAAAA,OAAOE,EAAE,CAAA;;;AAIlF;AAMA,IAAMC,eAAoC,CAAC,EAAET,WAAWV,MAAK,MAAE;;AAC7D,QAAM,EAAEoB,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,QAAQC,YAAW,IAAKrB,OAAAA;AAChC,QAAMsB,YAAYpB,SAAS,gBAAgB,CAACL,UAAUA,MAAMyB,SAAS;AACrE,QAAMC,iBAAiBC,SAAQH,gDAAaI,kBAAbJ,mBAA4BK,IAAAA;AAC3D,QAAM,CAAC,EAAEC,MAAK,CAAE,IAAIC,eAAAA;AACpB,QAAMC,SAAeC,eAAQ,MAAMC,iBAAiBJ,KAAQ,GAAA;IAACA;EAAM,CAAA;AACnE,QAAMK,sBAAsBC,gBAAgB,gBAAgB,CAACpC,UAAUA,MAAMqC,SAAS;AACtF,QAAM,EAAEC,YAAYC,kBAAkBC,UAAS,IAAKC,mBAAAA;AACpD,QAAMC,cAAc/B,UAAUK,IAAI,CAAC,EAAE2B,WAAU,MAAOA,UAAAA;AAEtD,QAAMC,0BAA0B,YAAA;AAC9B,UAAMC,MAAM,MAAMN,iBAAiB;MACjCG;MACAzC;MACA+B;IACF,CAAA;AACA,QAAI,EAAE,WAAWa,MAAM;AACrBpB,gBAAU,CAAA,CAAE;IACd;EACF;AAEA,MAAI,CAACU,oBAAqB,QAAO;AAEjC,SAAO;IACLW,SAAS;IACTC,OAAO1B,cAAc;MAAEF,IAAI;MAAiB6B,gBAAgB;IAAS,CAAA;IACrEC,QAAQ;MACNC,MAAM;MACNC,OAAO9B,cAAc;QACnBF,IAAI;QACJ6B,gBAAgB;MAClB,CAAA;MACAI,SAASZ;MACTa,aACEC,0BAAC/C,MAAAA;QAAKgD,WAAU;QAASC,YAAW;QAAUhD,KAAK;;cACjDF,yBAACC,MAAAA;YAAKkD,gBAAe;YACnB,cAAAnD,yBAACoD,cAAAA;cAAcC,OAAM;cAAOC,QAAO;cAAOC,MAAK;;;cAEjDvD,yBAACwD,YAAAA;YAAW3C,IAAG;YAAsB4C,WAAU;sBAC5C1C,cAAc;cACbF,IAAI;cACJ6B,gBAAgB;YAClB,CAAA;;UAEDtB,sBACCpB,yBAAC0D,KAAAA;YAAID,WAAU;YAASE,SAAS;YAC/B,cAAA3D,yBAACwD,YAAAA;cAAWI,WAAU;wBACnB7C,cACC;gBACEF,IAAIgD,eAAe,6CAAA;gBACnBnB,gBACE;iBAEJ;gBACEoB,IAAIC;cACN,CAAA;;;;;MAOZC,WAAW1B;IACb;EACF;AACF;AAEAxB,aAAa8B,OAAO;AAEpB,IAAMqB,kBAAuC,CAAC,EAAE5D,WAAWV,MAAK,MAAE;;AAChE,QAAM,EAAEoB,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,OAAM,IAAKpB,OAAAA;AACnB,QAAMsB,YAAYpB,SAAS,mBAAmB,CAACL,UAAUA,MAAMyB,SAAS;AACxE,QAAM+C,uBAAuBpC,gBAAgB,mBAAmB,CAACpC,UAAUA,MAAMyE,UAAU;AAC3F,QAAM/C,iBAAiBC,SAAQJ,sCAAQK,kBAARL,mBAAuBM,IAAAA;AACtD,QAAM6C,4BAA4B/C,SAAQJ,sCAAQoD,YAARpD,mBAAiBqD,eAAAA;AAC3D,QAAM,EAAEC,eAAeC,qBAAqBtC,UAAS,IAAKC,mBAAAA;AAC1D,QAAMC,cAAc/B,UAAUK,IAAI,CAAC,EAAE2B,WAAU,MAAOA,UAAAA;AACtD,QAAM,CAAC,EAAEb,MAAK,CAAE,IAAIC,eAAAA;AACpB,QAAMC,SAAeC,eAAQ,MAAMC,iBAAiBJ,KAAQ,GAAA;IAACA;EAAM,CAAA;AAEnE,QAAMiD,6BAA6B,YAAA;AACjC,UAAMC,OAAO,MAAMF,oBAAoB;MAAEpC;MAAazC;MAAO+B;IAAO,CAAA;AACpE,QAAI,EAAE,WAAWgD,OAAO;AACtBvD,gBAAU,CAAA,CAAE;IACd;EACF;AAEA,QAAMwD,sBACJP,6BACAF,wBACA7D,UAAUuE,KAAK,CAACC,UAAUA,MAAMC,WAAW,eAAeD,MAAMC,WAAW,UAAA;AAE7E,MAAI,CAACH,oBAAqB,QAAO;AAEjC,SAAO;IACLnC,SAAS;IACTC,OAAO1B,cAAc;MAAEF,IAAI;MAAuB6B,gBAAgB;IAAY,CAAA;IAC9EC,QAAQ;MACNC,MAAM;MACNC,OAAO9B,cAAc;QACnBF,IAAI;QACJ6B,gBAAgB;MAClB,CAAA;MACAI,SAASZ;MACTa,aACEC,0BAAC/C,MAAAA;QAAKgD,WAAU;QAASC,YAAW;QAAUhD,KAAK;;cACjDF,yBAACC,MAAAA;YAAKkD,gBAAe;YACnB,cAAAnD,yBAACoD,cAAAA;cAAcC,OAAM;cAAOC,QAAO;cAAOC,MAAK;;;cAEjDvD,yBAACwD,YAAAA;YAAW3C,IAAG;YAAsB4C,WAAU;sBAC5C1C,cAAc;cACbF,IAAI;cACJ6B,gBAAgB;YAClB,CAAA;;UAEDtB,sBACCpB,yBAAC0D,KAAAA;YAAID,WAAU;YAASE,SAAS;YAC/B,cAAA3D,yBAACwD,YAAAA;cAAWI,WAAU;wBACnB7C,cACC;gBACEF,IAAIgD,eAAe,gDAAA;gBACnBnB,gBACE;iBAEJ;gBACEoB,IAAIC;cACN,CAAA;;;;;MAOZgB,eAAehE,cAAc;QAC3BF,IAAI;QACJ6B,gBAAgB;MAClB,CAAA;MACAsB,WAAWS;IACb;EACF;AACF;AAEAR,gBAAgBrB,OAAO;AAEjBmB,IAAAA,WAAW,CAACiB,eAChBhF,yBAACwD,YAAAA;EAAWyB,YAAW;EAAWrB,WAAU;EACzCoB,UAAAA;;AAIL,IAAME,uBAA8C;EAACC;EAAelB;EAAiBnD;AAAa;;;;;;;;;ACtNlG,IAAMsE,4BAA4B,CAAC,EAAEC,iBAAgB,MAAkC;AACrF,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,QAAMC,yBAAyB,CAACC,WAAAA;AAC9B,YAAQA,QAAAA;MACN,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT;AACE,eAAOA;IACX;EACF;AAEA,aACEC,0BAAAC,8BAAA;;UACEC,yBAACC,YAAAA;QAAWC,SAAQ;kBACjBR,cAAc;UACbS,IAAIC,eAAe,sCAAA;UACnBC,gBAAgB;QAClB,CAAA;;UAEFL,yBAACM,KAAAA;QAAIC,WAAW;QACd,cAAAP,yBAACC,YAAAA;UAAWO,WAAU;oBACnBd,cAAc;YACbS,IAAIC,eAAe,4CAAA;YACnBC,gBACE;UACJ,CAAA;;;UAGJL,yBAACS,MAAAA;QAAKF,WAAW;QAAGG,KAAK;QAAGC,WAAU;QAASC,YAAW;QACvDnB,UAAAA,iBAAiBoB,IAAI,CAAC,CAACC,WAAWjB,MAAAA,UACjCC,0BAACW,MAAAA;UACCE,WAAU;UACVD,KAAK;UACLE,YAAW;UACXG,aAAY;UACZC,WAAS;UACTC,SAAS;;gBAGTjB,yBAACS,MAAAA;cAAKE,WAAU;cAAMO,KAAI;cACvBJ,UAAAA,UAAUD,IAAI,CAACM,aAAaC,cAC3BtB,0BAACG,YAAAA;gBAAWoB,YAAW;gBAAWH,KAAI;;kBACnCC;kBACAC,UAAUN,UAAUQ,SAAS,SAC5BtB,yBAACuB,eAAAA;oBACCC,MAAK;oBACLC,QAAO;oBACPC,OAAM;oBACNC,OAAO;sBAAEC,QAAQ;oBAAW;;;cAPcR,GAAAA,KAAAA,CAAAA;;gBAapDpB,yBAACC,YAAAA;cAAWiB,KAAI;cAAIV,WAAU;wBAC3Bd,cAAc;gBACbS,IAAIC,eAAe,wCAAwCP,MAAAA,EAAQ;gBACnEQ,gBAAgBT,uBAAuBC,MAAAA;cACzC,CAAA;;;QArBGiB,GAAAA,UAAUe,KAAI,CAAA,CAAA;;;;AA4B/B;;;AC5CA,IAAMC,eAAe,CAAC,EAAEC,SAAQ,MAAqB;AACnD,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,OAAOC,eAAc,IAAKC,OAAAA;AAClC,QAAMC,UAAUC,aAAa,gBAAgB,CAACC,UAAUA,MAAMF,OAAO;AAErE,QAAMG,QAA6B;IACjCC,WAAW;IACXP;IACAQ,YAAYX,SAASW;IACrBP;IACAJ;EACF;AAEA,aACEY,yBAACC,8BAAAA;IACCJ;IACAK,cAAeR,QAAQ,iBAAkB,EAACS,KACvCC,mBAAmB,WAAA,EAEnBC,OAAO,CAACC,WAAWA,OAAOC,SAAS,eAAA;cAErC,CAACC,YAAAA;AACA,YAAMC,kBAAkBD,QAAQH,OAAO,CAACC,WAAAA;AACtC,cAAMI,YAAYC,MAAMC,QAAQN,OAAOO,QAAQ,IAAIP,OAAOO,WAAW;UAACP,OAAOO;QAAS;AACtF,eAAOH,UAAUI,SAAS,WAAA;MAC5B,CAAA;AAEA,iBACEd,yBAACe,qBAAAA;QACCP,SAASC;QACTO,OAAO3B,cAAc;UACnB4B,IAAI;UACJC,gBAAgB;QAClB,CAAA;QACAC,SAAQ;;IAGd;;AAGN;AAMA,IAAMC,aAAsC,CAAC,EAAErB,WAAU,MAAE;AACzD,QAAMsB,WAAWC,YAAAA;AACjB,QAAM,EAAEjC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEiC,QAAO,IAAKC,gBAAgB,cAAc,CAAC,EAAED,SAAAA,SAAO,OAAQ;IAAEA,SAAAA;IAAQ;AAC9E,QAAM,EAAEE,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,CAAC,EAAEC,MAAK,CAAE,IAAIC,eAAAA;AAEpB,SAAO;IACLC,UAAU,CAACN;IACXO,UAAM9B,yBAAC+B,cAAAA,CAAAA,CAAAA;IACPf,OAAO3B,cAAc;MACnB4B,IAAI;MACJC,gBAAgB;IAClB,CAAA;IACAL,UAAU;IACVmB,SAAS,YAAA;AACP,UAAI,CAACjC,YAAY;AACfkC,gBAAQC,MACN,yGAAA;AAGFT,2BAAmB;UACjBU,SAAS9C,cAAc;YACrB4B,IAAI;YACJC,gBAAgB;UAClB,CAAA;UACAkB,MAAM;QACR,CAAA;AAEA;MACF;AAEAf,eAAS;QACPgB,UAAUtC;QACVuC,YAAQC,qBAAU;UAChB7C,SAASiC,MAAMjC;QACjB,CAAA;MACF,CAAA;IACF;EACF;AACF;AAEA0B,WAAWgB,OAAO;AAClBhB,WAAWP,WAAW;AAMtB,IAAMkB,eAAeS,GAAOC,aAAAA;;;;;AAM5B,IAAMC,cAAuC,CAAC,EAAEnD,OAAOQ,WAAU,MAAE;AACjE,QAAMsB,WAAWC,YAAAA;AACjB,QAAM,EAAEjC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEqD,UAAS,IAAKnB,gBAAgB,eAAe,CAAC,EAAEmB,WAAAA,WAAS,OAAQ;IAAEA,WAAAA;IAAU;AACrF,QAAM,EAAElB,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEkB,UAAS,IAAKC,mBAAAA;AACtB,QAAM,CAACC,kBAAkBC,mBAAAA,IAA6BC,gBAAmC,CAAA,CAAE;AAC3F,QAAM,CAAC,EAAErB,MAAK,CAAE,IAAIC,eAAAA;AAEpB,SAAO;IACLC,UAAU,CAACc;IACXb,UAAM9B,yBAACiD,iBAAAA,CAAAA,CAAAA;IACPjC,OAAO3B,cAAc;MACnB4B,IAAI;MACJC,gBAAgB;IAClB,CAAA;IACAL,UAAU;IACVmB,SAAS,YAAA;;AACP,UAAI,CAACjC,YAAY;AACfkC,gBAAQC,MACN,uHAAA;AAGFT,2BAAmB;UACjBU,SAAS9C,cAAc;YACrB4B,IAAI;YACJC,gBAAgB;UAClB,CAAA;UACAkB,MAAM;QACR,CAAA;AAEA;MACF;AAEA,YAAMc,MAAM,MAAMN,UAAU;QAC1BrD;QACA4D,UAAUpD;QACVqD,SAAQzB,iBAAMjC,YAANiC,mBAAe0B,SAAf1B,mBAAqByB;MAC/B,CAAA;AAEA,UAAI,UAAUF,KAAK;AACjB7B,iBAAS;UACPgB,UAAUa,IAAII,KAAKvD;UACnBuC,YAAQC,qBAAU;YAChB7C,SAASiC,MAAMjC;UACjB,CAAA;QACF,CAAA;AAKA,eAAO;MACT;AAEA,UACE6D,iBAAiBL,IAAIhB,KAAK,KAC1BgB,IAAIhB,MAAMsB,WACV,OAAON,IAAIhB,MAAMsB,YAAY,YAC7B,sBAAsBN,IAAIhB,MAAMsB,WAChC7C,MAAMC,QAAQsC,IAAIhB,MAAMsB,QAAQV,gBAAgB,GAChD;AACA,cAAMA,oBAAmBI,IAAIhB,MAAMsB,QAAQV;AAE3CC,4BAAoBD,iBAAAA;MACtB;IACF;IACAW,QAAQ;MACNrB,MAAM;MACNsB,OAAOrE,cAAc;QACnB4B,IAAI;QACJC,gBAAgB;MAClB,CAAA;MACAyC,aAAS3D,yBAAC4D,2BAAAA;QAA0Bd;;MACpCe,QAAQ,CAAC,EAAEC,QAAO,MAAE;AAClB,mBACEC,0BAACC,MAAMC,QAAM;;gBACXjE,yBAACkE,QAAAA;cAAOlC,SAAS8B;cAAS3C,SAAQ;wBAC/B9B,cAAc;gBACb4B,IAAI;gBACJC,gBAAgB;cAClB,CAAA;;gBAEFlB,yBAACmE,YAAAA;cACCC,KAAKC;cACLC,IAAI;gBACFjC,UAAU,SAAStC,UAAAA;gBACnBuC,YAAQC,qBAAU;kBAChB7C,SAASiC,MAAMjC;gBACjB,CAAA;cACF;wBAECL,cAAc;gBACb4B,IAAI;gBACJC,gBAAgB;cAClB,CAAA;;;;MAIR;IACF;EACF;AACF;AAEAwB,YAAYN,OAAO;AACnBM,YAAY7B,WAAW;AAMvB,IAAMoC,kBAAkBT,GAAO+B,aAAAA;;;;;AAM/B,IAAMC,4BAA4B;EAACpD;EAAYsB;AAAY;", "names": ["ConfirmBulkActionDialog", "onToggleDialog", "isOpen", "dialogBody", "endAction", "formatMessage", "useIntl", "_jsx", "Dialog", "Root", "open", "_jsxs", "Content", "Header", "id", "defaultMessage", "Body", "Flex", "direction", "alignItems", "gap", "justifyContent", "WarningCircle", "width", "height", "fill", "Footer", "Cancel", "<PERSON><PERSON>", "onClick", "variant", "BoldChunk", "chunks", "Typography", "fontWeight", "ConfirmDialogPublishAll", "isConfirmButtonLoading", "onConfirm", "selectedEntries", "useTable", "state", "selectedRows", "toggleNotification", "useNotification", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "getTranslation", "model", "schema", "useDoc", "query", "useQueryParams", "enableDraftRelationsCount", "data", "countDraftRelations", "isLoading", "error", "useGetManyDraftRelationCountQuery", "documentIds", "map", "entry", "documentId", "locale", "plugins", "i18n", "skip", "React", "useEffect", "type", "message", "_Fragment", "textAlign", "b", "count", "entities", "length", "pluginOptions", "textColor", "em", "Emphasis", "startIcon", "Check", "loading", "TypographyMaxWidth", "styled", "Typography", "TableComponent", "RawTable", "theme", "colors", "neutral150", "spaces", "formatErrorMessages", "errors", "parent<PERSON><PERSON>", "formatMessage", "messages", "Object", "entries", "for<PERSON>ach", "key", "value", "current<PERSON><PERSON>", "Array", "isArray", "push", "id", "defaultMessage", "field", "EntryValidationText", "validationErrors", "status", "useIntl", "validationErrorsMessages", "join", "_jsxs", "Flex", "gap", "_jsx", "CrossCircle", "fill", "<PERSON><PERSON><PERSON>", "description", "textColor", "variant", "fontWeight", "ellipsis", "CheckCircle", "ArrowsCounterClockwise", "TABLE_HEADERS", "name", "label", "SelectedEntriesTableContent", "isPublishing", "rowsToDisplay", "entriesToPublish", "pathname", "useLocation", "list", "settings", "mainField", "useDocLayout", "shouldDisplayMainField", "Table", "Content", "Head", "HeaderCheckboxCell", "filter", "head", "map", "<PERSON><PERSON><PERSON><PERSON>", "Loading", "Body", "row", "Row", "CheckboxCell", "Cell", "DocumentStatus", "max<PERSON><PERSON><PERSON>", "includes", "documentId", "Loader", "small", "IconButton", "tag", "Link", "to", "search", "locale", "state", "from", "target", "marginLeft", "Pencil", "width", "height", "PublicationStatusSummary", "count", "icon", "message", "justifyContent", "flex", "PublicationStatusGrid", "entriesReadyToPublishCount", "entriesPublishedCount", "entriesModifiedCount", "entriesWithErrorsCount", "Box", "hasRadius", "borderColor", "col<PERSON>ount", "rowCount", "Tbody", "Tr", "Td", "SelectedEntriesModalContent", "listViewSelectedEntries", "toggleModal", "setListViewSelectedDocuments", "model", "schema", "components", "useContentTypeSchema", "documentIds", "query", "useQueryParams", "params", "useMemo", "buildValidParams", "data", "isLoading", "isFetching", "refetch", "useGetAllDocumentsQuery", "page", "pageSize", "length", "toString", "sort", "filters", "$in", "plugins", "i18n", "selectFromResult", "restRes", "results", "rows", "validate", "createYupSchema", "attributes", "entry", "validateSync", "abort<PERSON><PERSON><PERSON>", "e", "ValidationError", "getYupValidationErrors", "isDialogOpen", "setIsDialogOpen", "useState", "publishMany", "bulkPublishAction", "useDocumentActions", "isSubmittingForm", "usePublishManyDocumentsMutation", "selectedRows", "useTable", "selectedEntries", "some", "selected<PERSON><PERSON>ry", "selectedEntriesWithErrorsCount", "selectedEntriesPublishedCount", "selectedEntriesModifiedCount", "selectedEntriesWithNoErrorsCount", "toggleDialog", "prev", "handleConfirmBulkPublish", "res", "unpublishedEntries", "_Fragment", "Modal", "marginTop", "Footer", "<PERSON><PERSON>", "onClick", "loading", "disabled", "ConfirmDialogPublishAll", "isOpen", "onToggleDialog", "isConfirmButtonLoading", "onConfirm", "PublishAction", "documents", "hasPublishPermission", "useDocumentRBAC", "canPublish", "showPublishButton", "selectRow", "refetchList", "contentManagerApi", "util", "invalidateTags", "type", "actionType", "dialog", "title", "getTranslation", "content", "onClose", "Root", "defaultSelectedRows", "headers", "BulkActions<PERSON><PERSON><PERSON>", "plugins", "useStrapiApp", "state", "model", "collectionType", "useDoc", "selectedRows", "useTable", "_jsx", "Flex", "gap", "DescriptionComponent<PERSON><PERSON><PERSON>", "props", "documents", "descriptions", "apis", "getBulkActions", "actions", "map", "action", "DocumentActionButton", "id", "DeleteAction", "formatMessage", "useIntl", "schema", "contentType", "selectRow", "hasI18nEnabled", "Boolean", "pluginOptions", "i18n", "query", "useQueryParams", "params", "useMemo", "buildValidParams", "hasDeletePermission", "useDocumentRBAC", "canDelete", "deleteMany", "bulkDeleteAction", "isLoading", "useDocumentActions", "documentIds", "documentId", "handleConfirmBulkDelete", "res", "variant", "label", "defaultMessage", "dialog", "type", "title", "loading", "content", "_jsxs", "direction", "alignItems", "justifyContent", "WarningCircle", "width", "height", "fill", "Typography", "textAlign", "Box", "padding", "textColor", "getTranslation", "em", "Emphasis", "onConfirm", "UnpublishAction", "hasPublishPermission", "canPublish", "hasDraftAndPublishEnabled", "options", "draftAndPublish", "unpublishMany", "bulkUnpublishAction", "handleConfirmBulkUnpublish", "data", "showUnpublishButton", "some", "entry", "status", "confirmButton", "chunks", "fontWeight", "DEFAULT_BULK_ACTIONS", "PublishAction", "AutoCloneFailureModalBody", "prohibitedFields", "formatMessage", "useIntl", "getDefaultErrorMessage", "reason", "_jsxs", "_Fragment", "_jsx", "Typography", "variant", "id", "getTranslation", "defaultMessage", "Box", "marginTop", "textColor", "Flex", "gap", "direction", "alignItems", "map", "fieldPath", "borderColor", "hasRadius", "padding", "tag", "pathSegment", "index", "fontWeight", "length", "ChevronRight", "fill", "height", "width", "style", "margin", "join", "TableActions", "document", "formatMessage", "useIntl", "model", "collectionType", "useDoc", "plugins", "useStrapiApp", "state", "props", "activeTab", "documentId", "_jsx", "DescriptionComponent<PERSON><PERSON><PERSON>", "descriptions", "apis", "getDocumentActions", "filter", "action", "name", "actions", "tableRowActions", "positions", "Array", "isArray", "position", "includes", "DocumentActionsMenu", "label", "id", "defaultMessage", "variant", "EditAction", "navigate", "useNavigate", "canRead", "useDocumentRBAC", "toggleNotification", "useNotification", "query", "useQueryParams", "disabled", "icon", "StyledPencil", "onClick", "console", "error", "message", "type", "pathname", "search", "stringify", "styled", "Pencil", "CloneAction", "canCreate", "autoClone", "useDocumentActions", "prohibitedFields", "setProhibitedFields", "useState", "StyledDuplicate", "res", "sourceId", "locale", "i18n", "data", "isBaseQueryError", "details", "dialog", "title", "content", "AutoCloneFailureModalBody", "footer", "onClose", "_jsxs", "Modal", "Footer", "<PERSON><PERSON>", "LinkButton", "tag", "NavLink", "to", "Duplicate", "DEFAULT_TABLE_ROW_ACTIONS"]}