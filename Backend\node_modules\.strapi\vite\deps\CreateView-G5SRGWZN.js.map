{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/TransferTokens/CreateView.tsx"], "sourcesContent": ["import { Page } from '../../../../components/PageHelpers';\nimport { useTypedSelector } from '../../../../core/store/hooks';\n\nimport { EditView } from './EditView';\n\nexport const ProtectedCreateView = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.['transfer-tokens'].create\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <EditView />\n    </Page.Protect>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAKaA,sBAAsB,MAAA;AACjC,QAAMC,cAAcC,iBAClB,CAACC,UAAAA;;AAAUA,uBAAMC,UAAUH,YAAYI,aAA5BF,mBAAuC,mBAAmBG;GAAAA;AAGvE,aACEC,wBAACC,KAAKC,SAAO;IAACR;IACZ,cAAAM,wBAACG,UAAAA,CAAAA,CAAAA;;AAGP;", "names": ["ProtectedCreateView", "permissions", "useTypedSelector", "state", "admin_app", "settings", "create", "_jsx", "Page", "Protect", "EditView"]}