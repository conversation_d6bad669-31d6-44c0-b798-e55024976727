import {
  useConfiguration
} from "./chunk-HX66WGOY.js";
import {
  setLocale
} from "./chunk-ODQFI753.js";
import {
  useTypedDispatch,
  useTypedSelector
} from "./chunk-ZOP4VV6J.js";
import {
  Box,
  Flex,
  SingleSelect,
  SingleSelectOption,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/components/UnauthenticatedLogo.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var Img = dt.img`
  height: 7.2rem;
`;
var Logo = () => {
  var _a;
  const { logos: { auth } } = useConfiguration("UnauthenticatedLogo");
  return (0, import_jsx_runtime.jsx)(Img, {
    src: ((_a = auth == null ? void 0 : auth.custom) == null ? void 0 : _a.url) || auth.default,
    "aria-hidden": true,
    alt: ""
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/layouts/UnauthenticatedLayout.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var Wrapper = dt(Box)`
  margin: 0 auto;
  width: 552px;
`;
var Column = dt(Flex)`
  flex-direction: column;
`;
var LocaleToggle = () => {
  const localeNames = useTypedSelector((state) => state.admin_app.language.localeNames);
  const dispatch = useTypedDispatch();
  const { formatMessage, locale } = useIntl();
  return (0, import_jsx_runtime2.jsx)(SingleSelect, {
    "aria-label": formatMessage({
      id: "global.localeToggle.label",
      defaultMessage: "Select interface language"
    }),
    value: locale,
    onChange: (language) => {
      dispatch(setLocale(language));
    },
    children: Object.entries(localeNames).map(([language, name]) => (0, import_jsx_runtime2.jsx)(SingleSelectOption, {
      value: language,
      children: name
    }, language))
  });
};
var LayoutContent = ({ children }) => (0, import_jsx_runtime2.jsx)(Wrapper, {
  shadow: "tableShadow",
  hasRadius: true,
  paddingTop: 9,
  paddingBottom: 9,
  paddingLeft: 10,
  paddingRight: 10,
  background: "neutral0",
  children
});
var UnauthenticatedLayout = ({ children }) => {
  return (0, import_jsx_runtime2.jsxs)("div", {
    children: [
      (0, import_jsx_runtime2.jsx)(Flex, {
        tag: "header",
        justifyContent: "flex-end",
        children: (0, import_jsx_runtime2.jsx)(Box, {
          paddingTop: 6,
          paddingRight: 8,
          children: (0, import_jsx_runtime2.jsx)(LocaleToggle, {})
        })
      }),
      (0, import_jsx_runtime2.jsx)(Box, {
        paddingTop: 2,
        paddingBottom: 11,
        children
      })
    ]
  });
};

export {
  Logo,
  Column,
  LayoutContent,
  UnauthenticatedLayout
};
//# sourceMappingURL=chunk-M27D4U76.js.map
