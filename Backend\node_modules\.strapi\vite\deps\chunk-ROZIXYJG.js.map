{"version": 3, "sources": ["../../../@strapi/admin/admin/src/components/GuidedTour/Provider.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { produce } from 'immer';\nimport get from 'lodash/get';\nimport set from 'lodash/set';\n\nconst GUIDED_TOUR_COMPLETED_STEPS = 'GUIDED_TOUR_COMPLETED_STEPS';\nconst GUIDED_TOUR_CURRENT_STEP = 'GUIDED_TOUR_CURRENT_STEP';\nconst GUIDED_TOUR_SKIPPED = 'GUIDED_TOUR_SKIPPED';\n\nconst GUIDED_TOUR_KEYS = {\n  GUIDED_TOUR_COMPLETED_STEPS,\n  GUIDED_TOUR_CURRENT_STEP,\n  GUIDED_TOUR_SKIPPED,\n} as const;\n\n/* -------------------------------------------------------------------------------------------------\n * GuidedTourProvider\n * -----------------------------------------------------------------------------------------------*/\n\nimport { createContext } from '../Context';\n\ntype SectionKey = keyof GuidedTourContextValue['guidedTourState'];\ntype StepKey = keyof GuidedTourContextValue['guidedTourState'][SectionKey];\ntype Step = `${SectionKey}.${StepKey}`;\ninterface GuidedTourContextValue {\n  currentStep: Step | null;\n  guidedTourState: {\n    contentTypeBuilder: {\n      create: boolean;\n      success: boolean;\n    };\n    contentManager: {\n      create: boolean;\n      success: boolean;\n    };\n    apiTokens: {\n      create: boolean;\n      success: boolean;\n    };\n  };\n  isGuidedTourVisible: boolean;\n  isSkipped: boolean;\n  setCurrentStep: (step: Step | null) => void | null;\n  setGuidedTourVisibility: (isVisible: boolean) => void;\n  setSkipped: (isSkipped: boolean) => void;\n  setStepState: (step: Step, state: boolean) => void;\n  startSection: (section: SectionKey) => void;\n}\n\nconst [GuidedTourProviderImpl, useGuidedTour] = createContext<GuidedTourContextValue>('GuidedTour');\n\ninterface GuidedTourProviderProps {\n  children: React.ReactNode;\n}\n\nconst GuidedTourProvider = ({ children }: GuidedTourProviderProps) => {\n  const [{ currentStep, guidedTourState, isGuidedTourVisible, isSkipped }, dispatch] =\n    React.useReducer(reducer, initialState, initialiseState);\n\n  const setCurrentStep = (step: SetCurrentStepAction['step']) => {\n    // if step is null it is intentional, we need to dispatch it\n    if (step !== null) {\n      const isStepAlreadyDone = get(guidedTourState, step);\n      const [sectionName, stepName] = step.split('.') as [SectionKey, StepKey];\n      const sectionArray = Object.entries(guidedTourState[sectionName]);\n\n      const currentStepIndex = sectionArray.findIndex(([key]) => key === stepName);\n      const previousSteps = sectionArray.slice(0, currentStepIndex);\n\n      const isStepToShow = previousSteps.every(([, sectionValue]) => sectionValue);\n\n      if (isStepAlreadyDone || isSkipped || !isStepToShow) {\n        return null;\n      }\n    }\n\n    window.localStorage.setItem(GUIDED_TOUR_CURRENT_STEP, JSON.stringify(null));\n\n    return dispatch({\n      type: 'SET_CURRENT_STEP',\n      step,\n    });\n  };\n\n  const setGuidedTourVisibility = (value: SetGuidedTourVisibilityAction['value']) => {\n    dispatch({\n      type: 'SET_GUIDED_TOUR_VISIBILITY',\n      value,\n    });\n  };\n\n  const setStepState = (currentStep: Step, value: SetStepStateAction['value']) => {\n    addCompletedStep(currentStep);\n\n    dispatch({\n      type: 'SET_STEP_STATE',\n      currentStep,\n      value,\n    });\n  };\n\n  const startSection = (sectionName: SectionKey) => {\n    const sectionSteps = guidedTourState[sectionName];\n\n    if (sectionSteps) {\n      const guidedTourArray = Object.entries(guidedTourState);\n\n      // Find current section position in the guidedTourArray\n      // Get only previous sections based on current section position\n      const currentSectionIndex = guidedTourArray.findIndex(([key]) => key === sectionName);\n      const previousSections = guidedTourArray.slice(0, currentSectionIndex);\n\n      // Check if every steps from previous section are done\n      const isSectionToShow = previousSections.every(([, sectionValue]) =>\n        Object.values(sectionValue).every(Boolean)\n      );\n\n      const [firstStep] = Object.keys(sectionSteps) as [StepKey];\n      const isFirstStepDone = sectionSteps[firstStep];\n\n      if (isSectionToShow && !currentStep && !isFirstStepDone) {\n        setCurrentStep(`${sectionName}.${firstStep}`);\n      }\n    }\n  };\n\n  const setSkipped = (value: SetSkippedAction['value']) => {\n    window.localStorage.setItem(GUIDED_TOUR_SKIPPED, JSON.stringify(value));\n\n    dispatch({\n      type: 'SET_SKIPPED',\n      value,\n    });\n  };\n\n  return (\n    <GuidedTourProviderImpl\n      guidedTourState={guidedTourState}\n      currentStep={currentStep}\n      setCurrentStep={setCurrentStep}\n      setGuidedTourVisibility={setGuidedTourVisibility}\n      setSkipped={setSkipped}\n      setStepState={setStepState}\n      startSection={startSection}\n      isGuidedTourVisible={isGuidedTourVisible}\n      isSkipped={isSkipped}\n    >\n      {children}\n    </GuidedTourProviderImpl>\n  );\n};\n\ntype State = Pick<\n  GuidedTourContextValue,\n  'guidedTourState' | 'currentStep' | 'isGuidedTourVisible' | 'isSkipped'\n>;\n\nconst initialState = {\n  currentStep: null,\n  guidedTourState: {\n    contentTypeBuilder: {\n      create: false,\n      success: false,\n    },\n    contentManager: {\n      create: false,\n      success: false,\n    },\n    apiTokens: {\n      create: false,\n      success: false,\n    },\n  },\n  isGuidedTourVisible: false,\n  isSkipped: false,\n} satisfies State;\n\ninterface SetCurrentStepAction {\n  type: 'SET_CURRENT_STEP';\n  step: Step | null;\n}\n\ninterface SetStepStateAction {\n  type: 'SET_STEP_STATE';\n  currentStep: Step;\n  value: boolean;\n}\n\ninterface SetSkippedAction {\n  type: 'SET_SKIPPED';\n  value: boolean;\n}\n\ninterface SetGuidedTourVisibilityAction {\n  type: 'SET_GUIDED_TOUR_VISIBILITY';\n  value: boolean;\n}\n\ntype Action =\n  | SetCurrentStepAction\n  | SetStepStateAction\n  | SetSkippedAction\n  | SetGuidedTourVisibilityAction;\n\nconst reducer: React.Reducer<State, Action> = (state: State = initialState, action: Action) =>\n  produce(state, (draftState) => {\n    switch (action.type) {\n      case 'SET_CURRENT_STEP': {\n        draftState.currentStep = action.step;\n        break;\n      }\n      case 'SET_STEP_STATE': {\n        const [section, step] = action.currentStep.split('.') as [SectionKey, StepKey];\n        draftState.guidedTourState[section][step] = action.value;\n        break;\n      }\n      case 'SET_SKIPPED': {\n        draftState.isSkipped = action.value;\n        break;\n      }\n      case 'SET_GUIDED_TOUR_VISIBILITY': {\n        draftState.isGuidedTourVisible = action.value;\n        break;\n      }\n      default: {\n        return draftState;\n      }\n    }\n  });\n\nconst initialiseState = (initialState: State) => {\n  const copyInitialState = { ...initialState };\n  const guidedTourLocaleStorage = JSON.parse(\n    window.localStorage.getItem(GUIDED_TOUR_COMPLETED_STEPS) ?? '[]'\n  );\n  const currentStepLocaleStorage = JSON.parse(\n    window.localStorage.getItem(GUIDED_TOUR_CURRENT_STEP) ?? 'null'\n  );\n  const skippedLocaleStorage = JSON.parse(\n    window.localStorage.getItem(GUIDED_TOUR_SKIPPED) ?? 'null'\n  );\n\n  if (Array.isArray(guidedTourLocaleStorage)) {\n    guidedTourLocaleStorage.forEach((step) => {\n      const [sectionName, stepName] = step.split('.');\n      set(copyInitialState, ['guidedTourState', sectionName, stepName], true);\n    });\n  }\n\n  // if current step when initializing mark it as done\n  if (currentStepLocaleStorage) {\n    const [sectionName, stepName] = currentStepLocaleStorage.split('.') as [SectionKey, StepKey];\n    set(copyInitialState, ['guidedTourState', sectionName, stepName], true);\n\n    addCompletedStep(currentStepLocaleStorage as Step);\n\n    window.localStorage.setItem(GUIDED_TOUR_CURRENT_STEP, JSON.stringify(null));\n  }\n\n  if (skippedLocaleStorage !== null) {\n    set(copyInitialState, 'isSkipped', skippedLocaleStorage);\n  }\n\n  return copyInitialState;\n};\n\n/**\n * @description Add a completed step to the local storage if it does not already exist.\n */\nconst addCompletedStep = (completedStep: Step) => {\n  const currentSteps = JSON.parse(window.localStorage.getItem(GUIDED_TOUR_COMPLETED_STEPS) ?? '[]');\n\n  if (!Array.isArray(currentSteps)) {\n    return;\n  }\n\n  const isAlreadyStored = currentSteps.includes(completedStep);\n\n  if (isAlreadyStored) {\n    return;\n  }\n\n  window.localStorage.setItem(\n    GUIDED_TOUR_COMPLETED_STEPS,\n    JSON.stringify([...currentSteps, completedStep])\n  );\n};\n\nexport { GuidedTourProvider, useGuidedTour, GuidedTourContextValue, GUIDED_TOUR_KEYS };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAMA,8BAA8B;AACpC,IAAMC,2BAA2B;AACjC,IAAMC,sBAAsB;AA0C5B,IAAM,CAACC,wBAAwBC,aAAc,IAAGC,cAAsC,YAAA;AAMtF,IAAMC,qBAAqB,CAAC,EAAEC,SAAQ,MAA2B;AAC/D,QAAM,CAAC,EAAEC,aAAaC,iBAAiBC,qBAAqBC,UAAS,GAAIC,QAAS,IAC1EC,iBAAWC,SAASC,cAAcC,eAAAA;AAE1C,QAAMC,iBAAiB,CAACC,SAAAA;AAEtB,QAAIA,SAAS,MAAM;AACjB,YAAMC,wBAAoBC,WAAAA,SAAIX,iBAAiBS,IAAAA;AAC/C,YAAM,CAACG,aAAaC,QAAAA,IAAYJ,KAAKK,MAAM,GAAA;AAC3C,YAAMC,eAAeC,OAAOC,QAAQjB,gBAAgBY,WAAY,CAAA;AAEhE,YAAMM,mBAAmBH,aAAaI,UAAU,CAAC,CAACC,GAAAA,MAASA,QAAQP,QAAAA;AACnE,YAAMQ,gBAAgBN,aAAaO,MAAM,GAAGJ,gBAAAA;AAE5C,YAAMK,eAAeF,cAAcG,MAAM,CAAC,CAAA,EAAGC,YAAAA,MAAkBA,YAAAA;AAE/D,UAAIf,qBAAqBR,aAAa,CAACqB,cAAc;AACnD,eAAO;MACT;IACF;AAEAG,WAAOC,aAAaC,QAAQpC,0BAA0BqC,KAAKC,UAAU,IAAA,CAAA;AAErE,WAAO3B,SAAS;MACd4B,MAAM;MACNtB;IACF,CAAA;EACF;AAEA,QAAMuB,0BAA0B,CAACC,UAAAA;AAC/B9B,aAAS;MACP4B,MAAM;MACNE;IACF,CAAA;EACF;AAEA,QAAMC,eAAe,CAACnC,cAAmBkC,UAAAA;AACvCE,qBAAiBpC,YAAAA;AAEjBI,aAAS;MACP4B,MAAM;MACNhC,aAAAA;MACAkC;IACF,CAAA;EACF;AAEA,QAAMG,eAAe,CAACxB,gBAAAA;AACpB,UAAMyB,eAAerC,gBAAgBY,WAAY;AAEjD,QAAIyB,cAAc;AAChB,YAAMC,kBAAkBtB,OAAOC,QAAQjB,eAAAA;AAIvC,YAAMuC,sBAAsBD,gBAAgBnB,UAAU,CAAC,CAACC,GAAAA,MAASA,QAAQR,WAAAA;AACzE,YAAM4B,mBAAmBF,gBAAgBhB,MAAM,GAAGiB,mBAAAA;AAGlD,YAAME,kBAAkBD,iBAAiBhB,MAAM,CAAC,CAAA,EAAGC,YAAa,MAC9DT,OAAO0B,OAAOjB,YAAAA,EAAcD,MAAMmB,OAAAA,CAAAA;AAGpC,YAAM,CAACC,SAAAA,IAAa5B,OAAO6B,KAAKR,YAAAA;AAChC,YAAMS,kBAAkBT,aAAaO,SAAU;AAE/C,UAAIH,mBAAmB,CAAC1C,eAAe,CAAC+C,iBAAiB;AACvDtC,uBAAe,GAAGI,WAAAA,IAAegC,SAAAA,EAAW;MAC9C;IACF;EACF;AAEA,QAAMG,aAAa,CAACd,UAAAA;AAClBP,WAAOC,aAAaC,QAAQnC,qBAAqBoC,KAAKC,UAAUG,KAAAA,CAAAA;AAEhE9B,aAAS;MACP4B,MAAM;MACNE;IACF,CAAA;EACF;AAEA,aACEe,wBAACtD,wBAAAA;IACCM;IACAD;IACAS;IACAwB;IACAe;IACAb;IACAE;IACAnC;IACAC;IAECJ;;AAGP;AAOA,IAAMQ,eAAe;EACnBP,aAAa;EACbC,iBAAiB;IACfiD,oBAAoB;MAClBC,QAAQ;MACRC,SAAS;IACX;IACAC,gBAAgB;MACdF,QAAQ;MACRC,SAAS;IACX;IACAE,WAAW;MACTH,QAAQ;MACRC,SAAS;IACX;EACF;EACAlD,qBAAqB;EACrBC,WAAW;AACb;AA6BA,IAAMG,UAAwC,CAACiD,QAAehD,cAAciD,WAC1EC,GAAQF,OAAO,CAACG,eAAAA;AACd,UAAQF,OAAOxB,MAAI;IACjB,KAAK,oBAAoB;AACvB0B,iBAAW1D,cAAcwD,OAAO9C;AAChC;IACF;IACA,KAAK,kBAAkB;AACrB,YAAM,CAACiD,SAASjD,IAAK,IAAG8C,OAAOxD,YAAYe,MAAM,GAAA;AACjD2C,iBAAWzD,gBAAgB0D,OAAAA,EAASjD,IAAK,IAAG8C,OAAOtB;AACnD;IACF;IACA,KAAK,eAAe;AAClBwB,iBAAWvD,YAAYqD,OAAOtB;AAC9B;IACF;IACA,KAAK,8BAA8B;AACjCwB,iBAAWxD,sBAAsBsD,OAAOtB;AACxC;IACF;IACA,SAAS;AACP,aAAOwB;IACT;EACF;AACF,CAAA;AAEF,IAAMlD,kBAAkB,CAACD,kBAAAA;AACvB,QAAMqD,mBAAmB;IAAE,GAAGrD;EAAa;AAC3C,QAAMsD,0BAA0B/B,KAAKgC,MACnCnC,OAAOC,aAAamC,QAAQvE,2BAAgC,KAAA,IAAA;AAE9D,QAAMwE,2BAA2BlC,KAAKgC,MACpCnC,OAAOC,aAAamC,QAAQtE,wBAA6B,KAAA,MAAA;AAE3D,QAAMwE,uBAAuBnC,KAAKgC,MAChCnC,OAAOC,aAAamC,QAAQrE,mBAAwB,KAAA,MAAA;AAGtD,MAAIwE,MAAMC,QAAQN,uBAA0B,GAAA;AAC1CA,4BAAwBO,QAAQ,CAAC1D,SAAAA;AAC/B,YAAM,CAACG,aAAaC,QAAAA,IAAYJ,KAAKK,MAAM,GAAA;AAC3CsD,qBAAAA,SAAIT,kBAAkB;QAAC;QAAmB/C;QAAaC;SAAW,IAAA;IACpE,CAAA;EACF;AAGA,MAAIkD,0BAA0B;AAC5B,UAAM,CAACnD,aAAaC,QAAAA,IAAYkD,yBAAyBjD,MAAM,GAAA;AAC/DsD,mBAAAA,SAAIT,kBAAkB;MAAC;MAAmB/C;MAAaC;OAAW,IAAA;AAElEsB,qBAAiB4B,wBAAAA;AAEjBrC,WAAOC,aAAaC,QAAQpC,0BAA0BqC,KAAKC,UAAU,IAAA,CAAA;EACvE;AAEA,MAAIkC,yBAAyB,MAAM;AACjCI,mBAAAA,SAAIT,kBAAkB,aAAaK,oBAAAA;EACrC;AAEA,SAAOL;AACT;AAKA,IAAMxB,mBAAmB,CAACkC,kBAAAA;AACxB,QAAMC,eAAezC,KAAKgC,MAAMnC,OAAOC,aAAamC,QAAQvE,2BAAgC,KAAA,IAAA;AAE5F,MAAI,CAAC0E,MAAMC,QAAQI,YAAe,GAAA;AAChC;EACF;AAEA,QAAMC,kBAAkBD,aAAaE,SAASH,aAAAA;AAE9C,MAAIE,iBAAiB;AACnB;EACF;AAEA7C,SAAOC,aAAaC,QAClBrC,6BACAsC,KAAKC,UAAU;IAAIwC,GAAAA;IAAcD;EAAc,CAAA,CAAA;AAEnD;", "names": ["GUIDED_TOUR_COMPLETED_STEPS", "GUIDED_TOUR_CURRENT_STEP", "GUIDED_TOUR_SKIPPED", "GuidedTourProviderImpl", "useGuidedTour", "createContext", "GuidedTourProvider", "children", "currentStep", "guidedTourState", "isGuidedTourVisible", "isSkipped", "dispatch", "useReducer", "reducer", "initialState", "initialiseState", "setCurrentStep", "step", "isStepAlreadyDone", "get", "sectionName", "<PERSON><PERSON><PERSON>", "split", "sectionArray", "Object", "entries", "currentStepIndex", "findIndex", "key", "previousSteps", "slice", "isStepToShow", "every", "sectionValue", "window", "localStorage", "setItem", "JSON", "stringify", "type", "setGuidedTourVisibility", "value", "setStepState", "addCompletedStep", "startSection", "sectionSteps", "guidedTourArray", "currentSectionIndex", "previousSections", "isSectionToShow", "values", "Boolean", "firstStep", "keys", "isFirstStepDone", "setSkipped", "_jsx", "contentTypeBuilder", "create", "success", "contentManager", "apiTokens", "state", "action", "produce", "draftState", "section", "copyInitialState", "guidedTourLocaleStorage", "parse", "getItem", "currentStepLocaleStorage", "skippedLocaleStorage", "Array", "isArray", "for<PERSON>ach", "set", "completedStep", "currentSteps", "isAlreadyStored", "includes"]}