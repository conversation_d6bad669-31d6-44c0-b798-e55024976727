import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  useCallbackRef,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/features/Notifications.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var NotificationsContext = React.createContext({
  toggleNotification: () => {
  }
});
var NotificationsProvider = ({ children }) => {
  const notificationIdRef = React.useRef(0);
  const [notifications, setNotifications] = React.useState([]);
  const toggleNotification = React.useCallback(({ type, message, link, timeout, blockTransition, onClose, title }) => {
    setNotifications((s) => [
      ...s,
      {
        id: notificationIdRef.current++,
        type,
        message,
        link,
        timeout,
        blockTransition,
        onClose,
        title
      }
    ]);
  }, []);
  const clearNotification = React.useCallback((id) => {
    setNotifications((s) => s.filter((n) => n.id !== id));
  }, []);
  const value = React.useMemo(() => ({
    toggleNotification
  }), [
    toggleNotification
  ]);
  return (0, import_jsx_runtime.jsxs)(NotificationsContext.Provider, {
    value,
    children: [
      (0, import_jsx_runtime.jsx)(Flex, {
        left: "50%",
        marginLeft: "-250px",
        position: "fixed",
        direction: "column",
        alignItems: "stretch",
        gap: 2,
        top: `4.6rem`,
        width: `50rem`,
        zIndex: "notification",
        children: notifications.map((notification) => {
          return (0, import_jsx_runtime.jsx)(Notification, {
            ...notification,
            clearNotification
          }, notification.id);
        })
      }),
      children
    ]
  });
};
var Notification = ({ clearNotification, blockTransition = false, id, link, message, onClose, timeout = 2500, title, type }) => {
  const { formatMessage } = useIntl();
  const onCloseCallback = useCallbackRef(onClose);
  const handleClose = React.useCallback(() => {
    onCloseCallback();
    clearNotification(id);
  }, [
    clearNotification,
    id,
    onCloseCallback
  ]);
  React.useEffect(() => {
    if (!blockTransition) {
      const timeoutReference = setTimeout(() => {
        handleClose();
      }, timeout);
      return () => {
        clearTimeout(timeoutReference);
      };
    }
  }, [
    blockTransition,
    handleClose,
    timeout
  ]);
  let variant;
  let alertTitle;
  if (type === "info") {
    variant = "default";
    alertTitle = formatMessage({
      id: "notification.default.title",
      defaultMessage: "Information:"
    });
  } else if (type === "danger") {
    variant = "danger";
    alertTitle = formatMessage({
      id: "notification.warning.title",
      defaultMessage: "Warning:"
    });
  } else if (type === "warning") {
    variant = "warning";
    alertTitle = formatMessage({
      id: "notification.warning.title",
      defaultMessage: "Warning:"
    });
  } else {
    variant = "success";
    alertTitle = formatMessage({
      id: "notification.success.title",
      defaultMessage: "Success:"
    });
  }
  if (title) {
    alertTitle = title;
  }
  return (0, import_jsx_runtime.jsx)(Alert, {
    action: link ? (0, import_jsx_runtime.jsx)(Link, {
      href: link.url,
      isExternal: true,
      children: link.label
    }) : void 0,
    onClose: handleClose,
    closeLabel: formatMessage({
      id: "global.close",
      defaultMessage: "Close"
    }),
    title: alertTitle,
    variant,
    children: message
  });
};
var useNotification = () => React.useContext(NotificationsContext);

export {
  NotificationsProvider,
  useNotification
};
/*! Bundled license information:

@strapi/admin/dist/admin/admin/src/features/Notifications.mjs:
  (**
  * @preserve
  * @description Returns an object to interact with the notification
  * system. The callbacks are wrapped in `useCallback` for a stable
  * identity.
  *
  * @example
  * ```tsx
  * import { useNotification } from '@strapi/strapi/admin';
  *
  * const MyComponent = () => {
  *  const { toggleNotification } = useNotification();
  *
  *  return <button onClick={() => toggleNotification({ message: 'Hello world!' })}>Click me</button>;
  *)
*/
//# sourceMappingURL=chunk-UBCTZOSQ.js.map
