{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/TransferTokens/EditView.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { <PERSON>, Flex, Grid, Typography } from '@strapi/design-system';\nimport { Formik, Form, FormikErrors, FormikHelpers } from 'formik';\nimport { useIntl } from 'react-intl';\nimport { useLocation, useNavigate, useMatch } from 'react-router-dom';\nimport * as yup from 'yup';\n\nimport { useGuidedTour } from '../../../../components/GuidedTour/Provider';\nimport { Layouts } from '../../../../components/Layouts/Layout';\nimport { Page } from '../../../../components/PageHelpers';\nimport { useTypedSelector } from '../../../../core/store/hooks';\nimport { useNotification } from '../../../../features/Notifications';\nimport { useTracking } from '../../../../features/Tracking';\nimport { useAPIErrorHandler } from '../../../../hooks/useAPIErrorHandler';\nimport { useRBAC } from '../../../../hooks/useRBAC';\nimport {\n  useCreateTransferTokenMutation,\n  useGetTransferTokenQuery,\n  useUpdateTransferTokenMutation,\n} from '../../../../services/transferTokens';\nimport { isBaseQueryError } from '../../../../utils/baseQuery';\nimport { translatedErrors } from '../../../../utils/translatedErrors';\nimport { TRANSFER_TOKEN_TYPE } from '../../components/Tokens/constants';\nimport { FormHead } from '../../components/Tokens/FormHead';\nimport { LifeSpanInput } from '../../components/Tokens/LifeSpanInput';\nimport { TokenBox } from '../../components/Tokens/TokenBox';\nimport { TokenDescription } from '../../components/Tokens/TokenDescription';\nimport { TokenName } from '../../components/Tokens/TokenName';\nimport { TokenTypeSelect } from '../../components/Tokens/TokenTypeSelect';\n\nimport type {\n  TransferToken,\n  SanitizedTransferToken,\n} from '../../../../../../shared/contracts/transfer';\n\nconst schema = yup.object().shape({\n  name: yup.string().max(100).required(translatedErrors.required.id),\n  description: yup.string().nullable(),\n  lifespan: yup.number().integer().min(0).nullable().defined(translatedErrors.required.id),\n  permissions: yup.string().required(translatedErrors.required.id),\n});\n\n/* -------------------------------------------------------------------------------------------------\n * EditView\n * -----------------------------------------------------------------------------------------------*/\n\nconst EditView = () => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const navigate = useNavigate();\n  const { state: locationState } = useLocation();\n  const [transferToken, setTransferToken] = React.useState<\n    TransferToken | SanitizedTransferToken | null\n  >(\n    locationState && 'accessKey' in locationState.transferToken\n      ? {\n          ...locationState.transferToken,\n        }\n      : null\n  );\n  const { trackUsage } = useTracking();\n  const setCurrentStep = useGuidedTour('EditView', (state) => state.setCurrentStep);\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.['transfer-tokens']\n  );\n  const {\n    allowedActions: { canCreate, canUpdate, canRegenerate },\n  } = useRBAC(permissions);\n  const match = useMatch('/settings/transfer-tokens/:id');\n\n  const id = match?.params?.id;\n  const isCreating = id === 'create';\n\n  const {\n    _unstableFormatAPIError: formatAPIError,\n    _unstableFormatValidationErrors: formatValidationErrors,\n  } = useAPIErrorHandler();\n\n  React.useEffect(() => {\n    trackUsage(isCreating ? 'didAddTokenFromList' : 'didEditTokenFromList', {\n      tokenType: TRANSFER_TOKEN_TYPE,\n    });\n  }, [isCreating, trackUsage]);\n\n  const { data, error } = useGetTransferTokenQuery(id!, {\n    skip: isCreating || transferToken !== null || !id,\n  });\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    }\n  }, [error, formatAPIError, toggleNotification]);\n\n  React.useEffect(() => {\n    if (data) {\n      setTransferToken(data);\n    }\n  }, [data]);\n\n  const [createToken] = useCreateTransferTokenMutation();\n  const [updateToken] = useUpdateTransferTokenMutation();\n\n  const handleSubmit = async (body: FormValues, formik: FormikHelpers<FormValues>) => {\n    trackUsage(isCreating ? 'willCreateToken' : 'willEditToken', {\n      tokenType: TRANSFER_TOKEN_TYPE,\n    });\n\n    const permissions = body.permissions.split('-');\n\n    const isPermissionsTransferPermission = (\n      permission: string[]\n    ): permission is Array<'push' | 'pull'> => {\n      if (permission.length === 1) {\n        return permission[0] === 'push' || permission[0] === 'pull';\n      }\n\n      return permission[0] === 'push' && permission[1] === 'pull';\n    };\n\n    // this type-guard is necessary to satisfy the type for `permissions` in the request body,\n    // because String.split returns stringp[]\n    if (isPermissionsTransferPermission(permissions)) {\n      try {\n        if (isCreating) {\n          const res = await createToken({\n            ...body,\n            // lifespan must be \"null\" for unlimited (0 would mean instantly expired and isn't accepted)\n            lifespan:\n              body?.lifespan && body.lifespan !== '0'\n                ? parseInt(body.lifespan.toString(), 10)\n                : null,\n            permissions,\n          });\n\n          if ('error' in res) {\n            if (isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n              formik.setErrors(formatValidationErrors(res.error));\n            } else {\n              toggleNotification({\n                type: 'danger',\n                message: formatAPIError(res.error),\n              });\n            }\n\n            return;\n          }\n\n          setTransferToken(res.data);\n\n          toggleNotification({\n            type: 'success',\n            message: formatMessage({\n              id: 'notification.success.transfertokencreated',\n              defaultMessage: 'Transfer Token successfully created',\n            }),\n          });\n\n          trackUsage('didCreateToken', {\n            type: transferToken?.permissions,\n            tokenType: TRANSFER_TOKEN_TYPE,\n          });\n\n          navigate(`../transfer-tokens/${res.data.id.toString()}`, {\n            replace: true,\n            state: { transferToken: res.data },\n          });\n        } else {\n          const res = await updateToken({\n            id: id!,\n            name: body.name,\n            description: body.description,\n            permissions,\n          });\n\n          if ('error' in res) {\n            if (isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n              formik.setErrors(formatValidationErrors(res.error));\n            } else {\n              toggleNotification({\n                type: 'danger',\n                message: formatAPIError(res.error),\n              });\n            }\n\n            return;\n          }\n\n          setTransferToken(res.data);\n\n          toggleNotification({\n            type: 'success',\n            message: formatMessage({\n              id: 'notification.success.transfertokenedited',\n              defaultMessage: 'Transfer Token successfully edited',\n            }),\n          });\n\n          trackUsage('didEditToken', {\n            type: transferToken?.permissions,\n            tokenType: TRANSFER_TOKEN_TYPE,\n          });\n        }\n      } catch (err) {\n        toggleNotification({\n          type: 'danger',\n          message: formatMessage({\n            id: 'notification.error',\n            defaultMessage: 'Something went wrong',\n          }),\n        });\n      }\n    }\n  };\n\n  const canEditInputs = (canUpdate && !isCreating) || (canCreate && isCreating);\n  const isLoading = !isCreating && !transferToken;\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Page.Main>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: 'Transfer Tokens',\n          }\n        )}\n      </Page.Title>\n      <Formik\n        validationSchema={schema}\n        validateOnChange={false}\n        initialValues={\n          {\n            name: transferToken?.name || '',\n            description: transferToken?.description || '',\n            lifespan: transferToken?.lifespan || null,\n            /**\n             * We need to cast the permissions to satisfy the type for `permissions`\n             * in the request body incase we don't have a transferToken and instead\n             * use an empty string.\n             */\n            permissions: (transferToken?.permissions.join('-') ?? '') as FormValues['permissions'],\n          } satisfies FormValues\n        }\n        enableReinitialize\n        onSubmit={(body, actions) => handleSubmit(body, actions)}\n      >\n        {({ errors, handleChange, isSubmitting, values }) => {\n          return (\n            <Form>\n              <FormHead\n                title={{\n                  id: 'Settings.transferTokens.createPage.title',\n                  defaultMessage: 'TokenCreate Transfer Token',\n                }}\n                token={transferToken}\n                setToken={setTransferToken}\n                canShowToken={false}\n                canEditInputs={canEditInputs}\n                canRegenerate={canRegenerate}\n                isSubmitting={isSubmitting}\n                regenerateUrl=\"/admin/transfer/tokens/\"\n              />\n              <Layouts.Content>\n                <Flex direction=\"column\" alignItems=\"stretch\" gap={6}>\n                  {transferToken &&\n                    Boolean(transferToken?.name) &&\n                    'accessKey' in transferToken && (\n                      <TokenBox token={transferToken.accessKey} tokenType={TRANSFER_TOKEN_TYPE} />\n                    )}\n                  <FormTransferTokenContainer\n                    errors={errors}\n                    onChange={handleChange}\n                    canEditInputs={canEditInputs}\n                    isCreating={isCreating}\n                    values={values}\n                    transferToken={transferToken}\n                  />\n                </Flex>\n              </Layouts.Content>\n            </Form>\n          );\n        }}\n      </Formik>\n    </Page.Main>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ProtectedEditView\n * -----------------------------------------------------------------------------------------------*/\n\nconst ProtectedEditView = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.['transfer-tokens'].read\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <EditView />\n    </Page.Protect>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * FormTransferTokenContainer\n * -----------------------------------------------------------------------------------------------*/\n\ninterface FormValues extends Pick<TransferToken, 'description' | 'name' | 'lifespan'> {\n  permissions: Extract<TransferToken['permissions'][number], string>;\n}\n\ninterface FormTransferTokenContainerProps {\n  errors: FormikErrors<FormValues>;\n  onChange: ({ target: { name, value } }: { target: { name: string; value: string } }) => void;\n  canEditInputs: boolean;\n  values: FormValues;\n  isCreating: boolean;\n  transferToken: Partial<TransferToken> | null;\n}\n\nconst FormTransferTokenContainer = ({\n  errors = {},\n  onChange,\n  canEditInputs,\n  isCreating,\n  values,\n  transferToken = {},\n}: FormTransferTokenContainerProps) => {\n  const { formatMessage } = useIntl();\n\n  const typeOptions = [\n    {\n      value: 'push',\n      label: {\n        id: 'Settings.transferTokens.types.push',\n        defaultMessage: 'Push',\n      },\n    },\n    {\n      value: 'pull',\n      label: {\n        id: 'Settings.transferTokens.types.pull',\n        defaultMessage: 'Pull',\n      },\n    },\n    {\n      value: 'push-pull',\n      label: {\n        id: 'Settings.transferTokens.types.push-pull',\n        defaultMessage: 'Full Access',\n      },\n    },\n  ];\n\n  return (\n    <Box\n      background=\"neutral0\"\n      hasRadius\n      shadow=\"filterShadow\"\n      paddingTop={6}\n      paddingBottom={6}\n      paddingLeft={7}\n      paddingRight={7}\n    >\n      <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n        <Typography variant=\"delta\" tag=\"h2\">\n          {formatMessage({\n            id: 'global.details',\n            defaultMessage: 'Details',\n          })}\n        </Typography>\n        <Grid.Root gap={5}>\n          <Grid.Item key=\"name\" col={6} xs={12} direction=\"column\" alignItems=\"stretch\">\n            <TokenName\n              error={errors['name']}\n              value={values['name']}\n              canEditInputs={canEditInputs}\n              onChange={onChange}\n            />\n          </Grid.Item>\n          <Grid.Item key=\"description\" col={6} xs={12} direction=\"column\" alignItems=\"stretch\">\n            <TokenDescription\n              error={errors['description']}\n              value={values['description']}\n              canEditInputs={canEditInputs}\n              onChange={onChange}\n            />\n          </Grid.Item>\n          <Grid.Item key=\"lifespan\" col={6} xs={12} direction=\"column\" alignItems=\"stretch\">\n            <LifeSpanInput\n              isCreating={isCreating}\n              error={errors['lifespan']}\n              value={values['lifespan']}\n              onChange={onChange}\n              token={transferToken}\n            />\n          </Grid.Item>\n          <Grid.Item key=\"permissions\" col={6} xs={12} direction=\"column\" alignItems=\"stretch\">\n            <TokenTypeSelect\n              name=\"permissions\"\n              value={values['permissions']}\n              error={errors['permissions']}\n              label={{\n                id: 'Settings.tokens.form.type',\n                defaultMessage: 'Token type',\n              }}\n              // @ts-expect-error – DS Select passes number | string, will be fixed in V2\n              onChange={(value: string) => {\n                onChange({ target: { name: 'permissions', value } });\n              }}\n              options={typeOptions}\n              canEditInputs={canEditInputs}\n            />\n          </Grid.Item>\n        </Grid.Root>\n      </Flex>\n    </Box>\n  );\n};\n\nexport { EditView, ProtectedEditView };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA,IAAMA,SAAaC,QAAM,EAAGC,MAAM;EAChCC,MAAUC,OAAM,EAAGC,IAAI,GAAKC,EAAAA,SAASC,YAAiBD,SAASE,EAAE;EACjEC,aAAiBL,OAAM,EAAGM,SAAQ;EAClCC,UAAcC,QAAM,EAAGC,QAAO,EAAGC,IAAI,CAAGJ,EAAAA,SAAQ,EAAGK,QAAQR,YAAiBD,SAASE,EAAE;EACvFQ,aAAiBZ,OAAM,EAAGE,SAASC,YAAiBD,SAASE,EAAE;AACjE,CAAA;AAIkG,IAE5FS,WAAW,MAAA;;AACf,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAMC,WAAWC,YAAAA;AACjB,QAAM,EAAEC,OAAOC,cAAa,IAAKC,YAAAA;AACjC,QAAM,CAACC,eAAeC,gBAAAA,IAA0BC,eAG9CJ,iBAAiB,eAAeA,cAAcE,gBAC1C;IACE,GAAGF,cAAcE;MAEnB,IAAA;AAEN,QAAM,EAAEG,WAAU,IAAKC,YAAAA;AACAC,gBAAc,YAAY,CAACR,UAAUA,MAAMS,cAAc;AAChF,QAAMjB,cAAckB,iBAClB,CAACV,UAAAA;;AAAUA,YAAAA,MAAAA,MAAMW,UAAUnB,YAAYoB,aAA5BZ,gBAAAA,IAAuC;GAAkB;AAEtE,QAAM,EACJa,gBAAgB,EAAEC,WAAWC,WAAWC,cAAa,EAAE,IACrDC,QAAQzB,WAAAA;AACZ,QAAM0B,QAAQC,SAAS,+BAAA;AAEvB,QAAMnC,MAAKkC,oCAAOE,WAAPF,mBAAelC;AAC1B,QAAMqC,aAAarC,OAAO;AAE1B,QAAM,EACJsC,yBAAyBC,gBACzBC,iCAAiCC,uBAAsB,IACrDC,mBAAAA;AAEJC,EAAMC,gBAAU,MAAA;AACdtB,eAAWe,aAAa,wBAAwB,wBAAwB;MACtEQ,WAAWC;IACb,CAAA;KACC;IAACT;IAAYf;EAAW,CAAA;AAE3B,QAAM,EAAEyB,MAAMC,MAAK,IAAKC,yBAAyBjD,IAAK;IACpDkD,MAAMb,cAAclB,kBAAkB,QAAQ,CAACnB;EACjD,CAAA;AAEA2C,EAAMC,gBAAU,MAAA;AACd,QAAII,OAAO;AACTpC,yBAAmB;QACjBuC,MAAM;QACNC,SAASb,eAAeS,KAAAA;MAC1B,CAAA;IACF;KACC;IAACA;IAAOT;IAAgB3B;EAAmB,CAAA;AAE9C+B,EAAMC,gBAAU,MAAA;AACd,QAAIG,MAAM;AACR3B,uBAAiB2B,IAAAA;IACnB;KACC;IAACA;EAAK,CAAA;AAET,QAAM,CAACM,WAAAA,IAAeC,+BAAAA;AACtB,QAAM,CAACC,WAAAA,IAAeC,+BAAAA;AAEtB,QAAMC,eAAe,OAAOC,MAAkBC,WAAAA;AAC5CrC,eAAWe,aAAa,oBAAoB,iBAAiB;MAC3DQ,WAAWC;IACb,CAAA;AAEA,UAAMtC,eAAckD,KAAKlD,YAAYoD,MAAM,GAAA;AAE3C,UAAMC,kCAAkC,CACtCC,eAAAA;AAEA,UAAIA,WAAWC,WAAW,GAAG;AAC3B,eAAOD,WAAW,CAAE,MAAK,UAAUA,WAAW,CAAA,MAAO;MACvD;AAEA,aAAOA,WAAW,CAAE,MAAK,UAAUA,WAAW,CAAA,MAAO;IACvD;AAIA,QAAID,gCAAgCrD,YAAc,GAAA;AAChD,UAAI;AACF,YAAI6B,YAAY;AACd,gBAAM2B,MAAM,MAAMX,YAAY;YAC5B,GAAGK;;YAEHvD,WACEuD,6BAAMvD,aAAYuD,KAAKvD,aAAa,MAChC8D,SAASP,KAAKvD,SAAS+D,SAAQ,GAAI,EACnC,IAAA;YACN1D,aAAAA;UACF,CAAA;AAEA,cAAI,WAAWwD,KAAK;AAClB,gBAAIG,iBAAiBH,IAAIhB,KAAK,KAAKgB,IAAIhB,MAAMrD,SAAS,mBAAmB;AACvEgE,qBAAOS,UAAU3B,uBAAuBuB,IAAIhB,KAAK,CAAA;mBAC5C;AACLpC,iCAAmB;gBACjBuC,MAAM;gBACNC,SAASb,eAAeyB,IAAIhB,KAAK;cACnC,CAAA;YACF;AAEA;UACF;AAEA5B,2BAAiB4C,IAAIjB,IAAI;AAEzBnC,6BAAmB;YACjBuC,MAAM;YACNC,SAAS1C,cAAc;cACrBV,IAAI;cACJqE,gBAAgB;YAClB,CAAA;UACF,CAAA;AAEA/C,qBAAW,kBAAkB;YAC3B6B,MAAMhC,+CAAeX;YACrBqC,WAAWC;UACb,CAAA;AAEAhC,mBAAS,sBAAsBkD,IAAIjB,KAAK/C,GAAGkE,SAAQ,CAAG,IAAG;YACvDI,SAAS;YACTtD,OAAO;cAAEG,eAAe6C,IAAIjB;YAAK;UACnC,CAAA;eACK;AACL,gBAAMiB,MAAM,MAAMT,YAAY;YAC5BvD;YACAL,MAAM+D,KAAK/D;YACXM,aAAayD,KAAKzD;YAClBO,aAAAA;UACF,CAAA;AAEA,cAAI,WAAWwD,KAAK;AAClB,gBAAIG,iBAAiBH,IAAIhB,KAAK,KAAKgB,IAAIhB,MAAMrD,SAAS,mBAAmB;AACvEgE,qBAAOS,UAAU3B,uBAAuBuB,IAAIhB,KAAK,CAAA;mBAC5C;AACLpC,iCAAmB;gBACjBuC,MAAM;gBACNC,SAASb,eAAeyB,IAAIhB,KAAK;cACnC,CAAA;YACF;AAEA;UACF;AAEA5B,2BAAiB4C,IAAIjB,IAAI;AAEzBnC,6BAAmB;YACjBuC,MAAM;YACNC,SAAS1C,cAAc;cACrBV,IAAI;cACJqE,gBAAgB;YAClB,CAAA;UACF,CAAA;AAEA/C,qBAAW,gBAAgB;YACzB6B,MAAMhC,+CAAeX;YACrBqC,WAAWC;UACb,CAAA;QACF;MACF,SAASyB,KAAK;AACZ3D,2BAAmB;UACjBuC,MAAM;UACNC,SAAS1C,cAAc;YACrBV,IAAI;YACJqE,gBAAgB;UAClB,CAAA;QACF,CAAA;MACF;IACF;EACF;AAEA,QAAMG,gBAAiBzC,aAAa,CAACM,cAAgBP,aAAaO;AAClE,QAAMoC,YAAY,CAACpC,cAAc,CAAClB;AAElC,MAAIsD,WAAW;AACb,eAAOC,wBAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,aACEC,yBAACF,KAAKG,MAAI;;UACRJ,wBAACC,KAAKI,OAAK;kBACRrE,cACC;UAAEV,IAAI;UAAsBqE,gBAAgB;WAC5C;UACE1E,MAAM;QACR,CAAA;;UAGJ+E,wBAACM,QAAAA;QACCC,kBAAkBzF;QAClB0F,kBAAkB;QAClBC,eACE;UACExF,OAAMwB,+CAAexB,SAAQ;UAC7BM,cAAakB,+CAAelB,gBAAe;UAC3CE,WAAUgB,+CAAehB,aAAY;;;;;;UAMrCK,cAAcW,+CAAeX,YAAY4E,KAAK,SAAQ;QACxD;QAEFC,oBAAkB;QAClBC,UAAU,CAAC5B,MAAM6B,YAAY9B,aAAaC,MAAM6B,OAAAA;kBAE/C,CAAC,EAAEC,QAAQC,cAAcC,cAAcC,OAAM,MAAE;AAC9C,qBACEd,yBAACe,MAAAA;;kBACClB,wBAACmB,UAAAA;gBACCC,OAAO;kBACL9F,IAAI;kBACJqE,gBAAgB;gBAClB;gBACA0B,OAAO5E;gBACP6E,UAAU5E;gBACV6E,cAAc;gBACdzB;gBACAxC;gBACA0D;gBACAQ,eAAc;;kBAEhBxB,wBAACyB,QAAQC,SAAO;gBACd,cAAAvB,yBAACwB,MAAAA;kBAAKC,WAAU;kBAASC,YAAW;kBAAUC,KAAK;;oBAChDrF,iBACCsF,QAAQtF,+CAAexB,IACvB,KAAA,eAAewB,qBACbuD,wBAACgC,UAAAA;sBAASX,OAAO5E,cAAcwF;sBAAW9D,WAAWC;;wBAEzD4B,wBAACkC,4BAAAA;sBACCpB;sBACAqB,UAAUpB;sBACVjB;sBACAnC;sBACAsD;sBACAxE;;;;;;;QAMZ;;;;AAIR;AAIkG,IAE5F2F,oBAAoB,MAAA;AACxB,QAAMtG,cAAckB,iBAClB,CAACV,UAAAA;;AAAUA,uBAAMW,UAAUnB,YAAYoB,aAA5BZ,mBAAuC,mBAAmB+F;GAAAA;AAGvE,aACErC,wBAACC,KAAKqC,SAAO;IAACxG;IACZ,cAAAkE,wBAACjE,UAAAA,CAAAA,CAAAA;;AAGP;AAmBA,IAAMmG,6BAA6B,CAAC,EAClCpB,SAAS,CAAA,GACTqB,UACArC,eACAnC,YACAsD,QACAxE,gBAAgB,CAAA,EAAE,MACc;AAChC,QAAM,EAAET,cAAa,IAAKC,QAAAA;AAE1B,QAAMsG,cAAc;IAClB;MACEC,OAAO;MACPC,OAAO;QACLnH,IAAI;QACJqE,gBAAgB;MAClB;IACF;IACA;MACE6C,OAAO;MACPC,OAAO;QACLnH,IAAI;QACJqE,gBAAgB;MAClB;IACF;IACA;MACE6C,OAAO;MACPC,OAAO;QACLnH,IAAI;QACJqE,gBAAgB;MAClB;IACF;EACD;AAED,aACEK,wBAAC0C,KAAAA;IACCC,YAAW;IACXC,WAAS;IACTC,QAAO;IACPC,YAAY;IACZC,eAAe;IACfC,aAAa;IACbC,cAAc;IAEd,cAAA9C,yBAACwB,MAAAA;MAAKC,WAAU;MAASC,YAAW;MAAUC,KAAK;;YACjD9B,wBAACkD,YAAAA;UAAWC,SAAQ;UAAQC,KAAI;oBAC7BpH,cAAc;YACbV,IAAI;YACJqE,gBAAgB;UAClB,CAAA;;YAEFQ,yBAACkD,KAAKC,MAAI;UAACxB,KAAK;;gBACd9B,wBAACqD,KAAKE,MAAI;cAAYC,KAAK;cAAGC,IAAI;cAAI7B,WAAU;cAASC,YAAW;cAClE,cAAA7B,wBAAC0D,WAAAA;gBACCpF,OAAOwC,OAAO,MAAO;gBACrB0B,OAAOvB,OAAO,MAAO;gBACrBnB;gBACAqC;;YALW,GAAA,MAAA;gBAQfnC,wBAACqD,KAAKE,MAAI;cAAmBC,KAAK;cAAGC,IAAI;cAAI7B,WAAU;cAASC,YAAW;cACzE,cAAA7B,wBAAC2D,kBAAAA;gBACCrF,OAAOwC,OAAO,aAAc;gBAC5B0B,OAAOvB,OAAO,aAAc;gBAC5BnB;gBACAqC;;YALW,GAAA,aAAA;gBAQfnC,wBAACqD,KAAKE,MAAI;cAAgBC,KAAK;cAAGC,IAAI;cAAI7B,WAAU;cAASC,YAAW;cACtE,cAAA7B,wBAAC4D,eAAAA;gBACCjG;gBACAW,OAAOwC,OAAO,UAAW;gBACzB0B,OAAOvB,OAAO,UAAW;gBACzBkB;gBACAd,OAAO5E;;YANI,GAAA,UAAA;gBASfuD,wBAACqD,KAAKE,MAAI;cAAmBC,KAAK;cAAGC,IAAI;cAAI7B,WAAU;cAASC,YAAW;cACzE,cAAA7B,wBAAC6D,iBAAAA;gBACC5I,MAAK;gBACLuH,OAAOvB,OAAO,aAAc;gBAC5B3C,OAAOwC,OAAO,aAAc;gBAC5B2B,OAAO;kBACLnH,IAAI;kBACJqE,gBAAgB;gBAClB;;gBAEAwC,UAAU,CAACK,UAAAA;AACTL,2BAAS;oBAAE2B,QAAQ;sBAAE7I,MAAM;sBAAeuH;oBAAM;kBAAE,CAAA;gBACpD;gBACAuB,SAASxB;gBACTzC;;YAdW,GAAA,aAAA;;;;;;AAqBzB;", "names": ["schema", "object", "shape", "name", "string", "max", "required", "translatedErrors", "id", "description", "nullable", "lifespan", "number", "integer", "min", "defined", "permissions", "EditView", "formatMessage", "useIntl", "toggleNotification", "useNotification", "navigate", "useNavigate", "state", "locationState", "useLocation", "transferToken", "setTransferToken", "useState", "trackUsage", "useTracking", "useGuidedTour", "setCurrentStep", "useTypedSelector", "admin_app", "settings", "allowedActions", "canCreate", "canUpdate", "canRegenerate", "useRBAC", "match", "useMatch", "params", "isCreating", "_unstableFormatAPIError", "formatAPIError", "_unstableFormatValidationErrors", "formatValidationErrors", "useAPIErrorHandler", "React", "useEffect", "tokenType", "TRANSFER_TOKEN_TYPE", "data", "error", "useGetTransferTokenQuery", "skip", "type", "message", "createToken", "useCreateTransferTokenMutation", "updateToken", "useUpdateTransferTokenMutation", "handleSubmit", "body", "formik", "split", "isPermissionsTransferPermission", "permission", "length", "res", "parseInt", "toString", "isBaseQueryError", "setErrors", "defaultMessage", "replace", "err", "canEditInputs", "isLoading", "_jsx", "Page", "Loading", "_jsxs", "Main", "Title", "<PERSON><PERSON>", "validationSchema", "validateOnChange", "initialValues", "join", "enableReinitialize", "onSubmit", "actions", "errors", "handleChange", "isSubmitting", "values", "Form", "FormHead", "title", "token", "setToken", "canShowToken", "regenerateUrl", "Layouts", "Content", "Flex", "direction", "alignItems", "gap", "Boolean", "TokenBox", "accessKey", "FormTransferTokenContainer", "onChange", "ProtectedEditView", "read", "Protect", "typeOptions", "value", "label", "Box", "background", "hasRadius", "shadow", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "Typography", "variant", "tag", "Grid", "Root", "<PERSON><PERSON>", "col", "xs", "TokenName", "TokenDescription", "LifeSpanInput", "TokenTypeSelect", "target", "options"]}