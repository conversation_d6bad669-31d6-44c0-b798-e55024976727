{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/content-manager.ts", "../../../@strapi/content-manager/admin/src/history/components/HistoryAction.tsx", "../../../@strapi/content-manager/admin/src/history/index.ts", "../../../@strapi/content-manager/admin/src/modules/reducers.ts", "../../../@strapi/content-manager/admin/src/preview/components/PreviewSidePanel.tsx", "../../../@strapi/content-manager/admin/src/preview/index.ts", "../../../@strapi/content-manager/admin/src/index.ts", "../../../@strapi/content-releases/admin/src/constants.ts", "../../../@strapi/content-releases/admin/src/components/ReleaseActionOptions.tsx", "../../../@strapi/content-releases/admin/src/components/ReleaseActionMenu.tsx", "../../../@strapi/content-releases/admin/src/pluginId.ts"], "sourcesContent": ["/* eslint-disable check-file/filename-naming-convention */\nimport { INJECTION_ZONES } from './components/InjectionZone';\nimport { PLUGIN_ID } from './constants/plugin';\nimport {\n  DEFAULT_ACTIONS,\n  type DocumentActionPosition,\n  type DocumentActionDescription,\n} from './pages/EditView/components/DocumentActions';\nimport {\n  DEFAULT_HEADER_ACTIONS,\n  type HeaderActionDescription,\n} from './pages/EditView/components/Header';\nimport { ActionsPanel, type PanelDescription } from './pages/EditView/components/Panels';\nimport {\n  DEFAULT_BULK_ACTIONS,\n  type BulkActionDescription,\n} from './pages/ListView/components/BulkActions/Actions';\nimport { DEFAULT_TABLE_ROW_ACTIONS } from './pages/ListView/components/TableActions';\n\nimport type { Document } from './hooks/useDocument';\nimport type { DocumentMetadata } from '../../shared/contracts/collection-types';\nimport type { DescriptionComponent, PluginConfig } from '@strapi/admin/strapi-admin';\n\n/* -------------------------------------------------------------------------------------------------\n * Configuration Types\n * -----------------------------------------------------------------------------------------------*/\n\ntype DescriptionReducer<Config extends object> = (prev: Config[]) => Config[];\n\ninterface EditViewContext {\n  /**\n   * This will ONLY be null, if the content-type\n   * does not have draft & published enabled.\n   */\n  activeTab: 'draft' | 'published' | null;\n  /**\n   * Will be either 'single-types' | 'collection-types'\n   */\n  collectionType: string;\n  /**\n   * this will be undefined if someone is creating an entry.\n   */\n  document?: Document;\n  /**\n   * this will be undefined if someone is creating an entry.\n   */\n  documentId?: string;\n  /**\n   * this will be undefined if someone is creating an entry.\n   */\n  meta?: DocumentMetadata;\n  /**\n   * The current content-type's model.\n   */\n  model: string;\n}\n\ninterface ListViewContext {\n  /**\n   * Will be either 'single-types' | 'collection-types'\n   */\n  collectionType: string;\n  /**\n   * The current selected documents in the table\n   */\n  documents: Document[];\n  /**\n   * The current content-type's model.\n   */\n  model: string;\n}\n\ninterface PanelComponentProps extends EditViewContext {}\n\ninterface PanelComponent extends DescriptionComponent<PanelComponentProps, PanelDescription> {\n  /**\n   * The defaults are added by Strapi only, if you're providing your own component,\n   * you do not need to provide this.\n   */\n  type?: 'actions' | 'releases';\n}\n\ninterface DocumentActionProps extends EditViewContext {}\n\ninterface DocumentActionComponent\n  extends DescriptionComponent<DocumentActionProps, DocumentActionDescription> {\n  type?:\n    | 'clone'\n    | 'configure-the-view'\n    | 'delete'\n    | 'discard'\n    | 'edit'\n    | 'edit-the-model'\n    | 'history'\n    | 'publish'\n    | 'unpublish'\n    | 'update';\n  position?: DocumentActionDescription['position'];\n}\n\ninterface HeaderActionProps extends EditViewContext {}\n\ninterface HeaderActionComponent\n  extends DescriptionComponent<HeaderActionProps, HeaderActionDescription> {}\n\ninterface BulkActionComponentProps extends ListViewContext {}\n\ninterface BulkActionComponent\n  extends DescriptionComponent<BulkActionComponentProps, BulkActionDescription> {\n  type?: 'delete' | 'publish' | 'unpublish';\n}\n\n/* -------------------------------------------------------------------------------------------------\n * ContentManager plugin\n * -----------------------------------------------------------------------------------------------*/\n\nclass ContentManagerPlugin {\n  /**\n   * The following properties are the stored ones provided by any plugins registering with\n   * the content-manager. The function calls however, need to be called at runtime in the\n   * application, so instead we collate them and run them later with the complete list incl.\n   * ones already registered & the context of the view.\n   */\n  bulkActions: BulkActionComponent[] = [...DEFAULT_BULK_ACTIONS];\n  documentActions: DocumentActionComponent[] = [\n    ...DEFAULT_ACTIONS,\n    ...DEFAULT_TABLE_ROW_ACTIONS,\n    ...DEFAULT_HEADER_ACTIONS,\n  ];\n  editViewSidePanels: PanelComponent[] = [ActionsPanel];\n  headerActions: HeaderActionComponent[] = [];\n\n  constructor() {}\n\n  addEditViewSidePanel(panels: DescriptionReducer<PanelComponent>): void;\n  addEditViewSidePanel(panels: PanelComponent[]): void;\n  addEditViewSidePanel(panels: DescriptionReducer<PanelComponent> | PanelComponent[]) {\n    if (Array.isArray(panels)) {\n      this.editViewSidePanels = [...this.editViewSidePanels, ...panels];\n    } else if (typeof panels === 'function') {\n      this.editViewSidePanels = panels(this.editViewSidePanels);\n    } else {\n      throw new Error(\n        `Expected the \\`panels\\` passed to \\`addEditViewSidePanel\\` to be an array or a function, but received ${getPrintableType(\n          panels\n        )}`\n      );\n    }\n  }\n\n  addDocumentAction(actions: DescriptionReducer<DocumentActionComponent>): void;\n  addDocumentAction(actions: DocumentActionComponent[]): void;\n  addDocumentAction(\n    actions: DescriptionReducer<DocumentActionComponent> | DocumentActionComponent[]\n  ) {\n    if (Array.isArray(actions)) {\n      this.documentActions = [...this.documentActions, ...actions];\n    } else if (typeof actions === 'function') {\n      this.documentActions = actions(this.documentActions);\n    } else {\n      throw new Error(\n        `Expected the \\`actions\\` passed to \\`addDocumentAction\\` to be an array or a function, but received ${getPrintableType(\n          actions\n        )}`\n      );\n    }\n  }\n\n  addDocumentHeaderAction(actions: DescriptionReducer<HeaderActionComponent>): void;\n  addDocumentHeaderAction(actions: HeaderActionComponent[]): void;\n  addDocumentHeaderAction(\n    actions: DescriptionReducer<HeaderActionComponent> | HeaderActionComponent[]\n  ) {\n    if (Array.isArray(actions)) {\n      this.headerActions = [...this.headerActions, ...actions];\n    } else if (typeof actions === 'function') {\n      this.headerActions = actions(this.headerActions);\n    } else {\n      throw new Error(\n        `Expected the \\`actions\\` passed to \\`addDocumentHeaderAction\\` to be an array or a function, but received ${getPrintableType(\n          actions\n        )}`\n      );\n    }\n  }\n\n  addBulkAction(actions: DescriptionReducer<BulkActionComponent>): void;\n  addBulkAction(actions: BulkActionComponent[]): void;\n  addBulkAction(actions: DescriptionReducer<BulkActionComponent> | BulkActionComponent[]) {\n    if (Array.isArray(actions)) {\n      this.bulkActions = [...this.bulkActions, ...actions];\n    } else if (typeof actions === 'function') {\n      this.bulkActions = actions(this.bulkActions);\n    } else {\n      throw new Error(\n        `Expected the \\`actions\\` passed to \\`addBulkAction\\` to be an array or a function, but received ${getPrintableType(\n          actions\n        )}`\n      );\n    }\n  }\n\n  get config() {\n    return {\n      id: PLUGIN_ID,\n      name: 'Content Manager',\n      injectionZones: INJECTION_ZONES,\n      apis: {\n        addBulkAction: this.addBulkAction.bind(this),\n        addDocumentAction: this.addDocumentAction.bind(this),\n        addDocumentHeaderAction: this.addDocumentHeaderAction.bind(this),\n        addEditViewSidePanel: this.addEditViewSidePanel.bind(this),\n        getBulkActions: () => this.bulkActions,\n        getDocumentActions: (position?: DocumentActionPosition) => {\n          /**\n           * When possible, pre-filter the actions by the components static position property.\n           * This avoids rendering the actions in multiple places where they weren't displayed,\n           * which wasn't visible but created issues with useEffect for instance.\n           * The response should still be filtered by the position, as the static property is new\n           * and not mandatory to avoid a breaking change.\n           */\n          if (position) {\n            return this.documentActions.filter((action) => {\n              return action.position == undefined || [action.position].flat().includes(position);\n            });\n          }\n\n          return this.documentActions;\n        },\n        getEditViewSidePanels: () => this.editViewSidePanels,\n        getHeaderActions: () => this.headerActions,\n      },\n    } satisfies PluginConfig;\n  }\n}\n\n/* -------------------------------------------------------------------------------------------------\n * getPrintableType\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * @internal\n * @description Gets the human-friendly printable type name for the given value, for instance it will yield\n * `array` instead of `object`, as the native `typeof` operator would do.\n */\nconst getPrintableType = (value: unknown): string => {\n  const nativeType = typeof value;\n\n  if (nativeType === 'object') {\n    if (value === null) return 'null';\n    if (Array.isArray(value)) return 'array';\n    if (value instanceof Object && value.constructor.name !== 'Object') {\n      return value.constructor.name;\n    }\n  }\n\n  return nativeType;\n};\n\nexport { ContentManagerPlugin };\nexport type {\n  EditViewContext,\n  ListViewContext,\n  BulkActionComponent,\n  BulkActionComponentProps,\n  BulkActionDescription,\n  DescriptionComponent,\n  DescriptionReducer,\n  PanelComponentProps,\n  PanelComponent,\n  PanelDescription,\n  DocumentActionComponent,\n  DocumentActionDescription,\n  DocumentActionProps,\n  HeaderActionComponent,\n  HeaderActionDescription,\n  HeaderActionProps,\n};\n", "import { useQueryParams, useTracking } from '@strapi/admin/strapi-admin';\nimport { ClockCounterClockwise } from '@strapi/icons';\nimport { stringify } from 'qs';\nimport { useIntl } from 'react-intl';\nimport { useNavigate, useLocation } from 'react-router-dom';\n\nimport type { DocumentActionComponent } from '../../content-manager';\n\nconst HistoryAction: DocumentActionComponent = ({ model, document }) => {\n  const { formatMessage } = useIntl();\n  const [{ query }] = useQueryParams<{ plugins?: Record<string, unknown> }>();\n  const navigate = useNavigate();\n  const { trackUsage } = useTracking();\n  const { pathname } = useLocation();\n  const pluginsQueryParams = stringify({ plugins: query.plugins }, { encode: false });\n\n  if (!window.strapi.features.isEnabled('cms-content-history')) {\n    return null;\n  }\n\n  const handleOnClick = () => {\n    const destination = { pathname: 'history', search: pluginsQueryParams };\n    trackUsage('willNavigate', {\n      from: pathname,\n      to: `${pathname}/${destination.pathname}`,\n    });\n    navigate(destination);\n  };\n\n  return {\n    icon: <ClockCounterClockwise />,\n    label: formatMessage({\n      id: 'content-manager.history.document-action',\n      defaultMessage: 'Content History',\n    }),\n    onClick: handleOnClick,\n    disabled:\n      /**\n       * The user is creating a new document.\n       * It hasn't been saved yet, so there's no history to go to\n       */\n      !document ||\n      /**\n       * The document has been created but the current dimension has never been saved.\n       * For example, the user is creating a new locale in an existing document,\n       * so there's no history for the document in that locale\n       */\n      !document.id ||\n      /**\n       * History is only available for content types created by the user.\n       * These have the `api::` prefix, as opposed to the ones created by Strapi or plugins,\n       * which start with `admin::` or `plugin::`\n       */\n      !model.startsWith('api::'),\n    position: 'header',\n  };\n};\n\nHistoryAction.type = 'history';\nHistoryAction.position = 'header';\n\nexport { HistoryAction };\n", "/* eslint-disable check-file/no-index */\n\nimport { type ContentManagerPlugin } from '../content-manager';\n\nimport { HistoryAction } from './components/HistoryAction';\n\nimport type { StrapiApp } from '@strapi/admin/strapi-admin';\nimport type { Plugin } from '@strapi/types';\n\nconst historyAdmin: Partial<Plugin.Config.AdminInput> = {\n  bootstrap(app: StrapiApp) {\n    const { addDocumentAction } = app.getPlugin('content-manager').apis as {\n      addDocumentAction: ContentManagerPlugin['addDocumentAction'];\n    };\n\n    /**\n     * Register the document action here using the public API, and not by setting the action in the\n     * Content Manager directly, because this API lets us control the order of the actions array.\n     * We want history to be the last non-delete action in the array.\n     */\n    addDocumentAction((actions) => {\n      const indexOfDeleteAction = actions.findIndex((action) => action.type === 'delete');\n      actions.splice(indexOfDeleteAction, 0, HistoryAction);\n      return actions;\n    });\n  },\n};\n\nexport { historyAdmin };\n", "import { combineReducers } from '@reduxjs/toolkit';\n\nimport { reducer as appReducer } from './app';\n\nconst reducer = combineReducers({\n  app: appReducer,\n});\n\ntype State = ReturnType<typeof reducer>;\n\nexport { reducer };\nexport type { State };\n", "import * as React from 'react';\n\nimport { useQueryParams, useTracking, useForm } from '@strapi/admin/strapi-admin';\nimport { Box, Button, Tooltip, type TooltipProps } from '@strapi/design-system';\nimport { stringify } from 'qs';\nimport { useIntl } from 'react-intl';\nimport { Link, useLocation } from 'react-router-dom';\n\nimport { useGetPreviewUrlQuery } from '../services/preview';\n\nimport type { PanelComponent } from '@strapi/content-manager/strapi-admin';\nimport type { UID } from '@strapi/types';\n\ninterface ConditionalTooltipProps {\n  isShown: boolean;\n  label: TooltipProps['label'];\n  children: React.ReactNode;\n}\n\nconst ConditionalTooltip = ({ isShown, label, children }: ConditionalTooltipProps) => {\n  if (isShown) {\n    return <Tooltip label={label}>{children}</Tooltip>;\n  }\n\n  return children;\n};\n\nconst PreviewSidePanel: PanelComponent = ({ model, documentId, document }) => {\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n  const { pathname } = useLocation();\n  const [{ query }] = useQueryParams();\n  const isModified = useForm('PreviewSidePanel', (state) => state.modified);\n\n  /**\n   * The preview URL isn't used in this component, we just fetch it to know if preview is enabled\n   * for the content type. If it's not, the panel is not displayed. If it is, we display a link to\n   * /preview, and the URL will already be loaded in the RTK query cache.\n   */\n  const { data, error } = useGetPreviewUrlQuery({\n    params: {\n      contentType: model as UID.ContentType,\n    },\n    query: {\n      documentId,\n      locale: document?.locale,\n      status: document?.status,\n    },\n  });\n\n  if (!data?.data?.url || error) {\n    return null;\n  }\n\n  const trackNavigation = () => {\n    // Append /preview to the current URL\n    const destinationPathname = pathname.replace(/\\/$/, '') + '/preview';\n    trackUsage('willNavigate', { from: pathname, to: destinationPathname });\n  };\n\n  return {\n    title: formatMessage({ id: 'content-manager.preview.panel.title', defaultMessage: 'Preview' }),\n    content: (\n      <ConditionalTooltip\n        label={formatMessage({\n          id: 'content-manager.preview.panel.button-disabled-tooltip',\n          defaultMessage: 'Please save to open the preview',\n        })}\n        isShown={isModified}\n      >\n        <Box cursor=\"not-allowed\" width=\"100%\">\n          <Button\n            variant=\"tertiary\"\n            tag={Link}\n            to={{ pathname: 'preview', search: stringify(query, { encode: false }) }}\n            onClick={trackNavigation}\n            width=\"100%\"\n            disabled={isModified}\n            pointerEvents={isModified ? 'none' : undefined}\n            tabIndex={isModified ? -1 : undefined}\n          >\n            {formatMessage({\n              id: 'content-manager.preview.panel.button',\n              defaultMessage: 'Open preview',\n            })}\n          </Button>\n        </Box>\n      </ConditionalTooltip>\n    ),\n  };\n};\n\nexport { PreviewSidePanel };\n", "/* eslint-disable check-file/no-index */\n\nimport { PreviewSidePanel } from './components/PreviewSidePanel';\n\nimport type { ContentManagerPlugin } from '../content-manager';\nimport type { PluginDefinition } from '@strapi/admin/strapi-admin';\n\nconst previewAdmin: Partial<PluginDefinition> = {\n  bootstrap(app) {\n    const contentManagerPluginApis = app.getPlugin('content-manager')\n      .apis as ContentManagerPlugin['config']['apis'];\n\n    contentManagerPluginApis.addEditViewSidePanel([PreviewSidePanel]);\n  },\n};\n\nexport { previewAdmin };\n", "import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Pencil } from '@strapi/icons';\n\nimport { PLUGIN_ID } from './constants/plugin';\nimport { ContentManagerPlugin } from './content-manager';\nimport { historyAdmin } from './history';\nimport { reducer } from './modules/reducers';\nimport { previewAdmin } from './preview';\nimport { routes } from './router';\nimport { prefixPluginTranslations } from './utils/translations';\n\n// NOTE: we have to preload it to ensure chunks will have it available as global\nimport 'prismjs';\n\n// eslint-disable-next-line import/no-default-export\nexport default {\n  register(app: any) {\n    const cm = new ContentManagerPlugin();\n\n    app.addReducers({\n      [PLUGIN_ID]: reducer,\n    });\n\n    app.addMenuLink({\n      to: PLUGIN_ID,\n      icon: Feather,\n      intlLabel: {\n        id: `content-manager.plugin.name`,\n        defaultMessage: 'Content Manager',\n      },\n      permissions: [],\n      position: 1,\n    });\n\n    app.router.addRoute({\n      path: 'content-manager/*',\n      lazy: async () => {\n        const { Layout } = await import('./layout');\n\n        return {\n          Component: Layout,\n        };\n      },\n      children: routes,\n    });\n\n    app.registerPlugin(cm.config);\n\n    // Register homepage widgets\n    app.widgets.register([\n      {\n        icon: Pencil,\n        title: {\n          id: `${PLUGIN_ID}.widget.last-edited.title`,\n          defaultMessage: 'Last edited entries',\n        },\n        component: async () => {\n          const { LastEditedWidget } = await import('./components/Widgets');\n          return LastEditedWidget;\n        },\n        pluginId: PLUGIN_ID,\n        id: 'last-edited-entries',\n        permissions: [{ action: 'plugin::content-manager.explorer.read' }],\n      },\n      {\n        icon: CheckCircle,\n        title: {\n          id: `${PLUGIN_ID}.widget.last-published.title`,\n          defaultMessage: 'Last published entries',\n        },\n        component: async () => {\n          const { LastPublishedWidget } = await import('./components/Widgets');\n          return LastPublishedWidget;\n        },\n        pluginId: PLUGIN_ID,\n        id: 'last-published-entries',\n        permissions: [{ action: 'plugin::content-manager.explorer.read' }],\n      },\n    ]);\n  },\n  bootstrap(app: any) {\n    if (typeof historyAdmin.bootstrap === 'function') {\n      historyAdmin.bootstrap(app);\n    }\n    if (typeof previewAdmin.bootstrap === 'function') {\n      previewAdmin.bootstrap(app);\n    }\n  },\n  async registerTrads({ locales }: { locales: string[] }) {\n    const importedTrads = await Promise.all(\n      locales.map((locale) => {\n        return import(`./translations/${locale}.json`)\n          .then(({ default: data }) => {\n            return {\n              data: prefixPluginTranslations(data, PLUGIN_ID),\n              locale,\n            };\n          })\n          .catch(() => {\n            return {\n              data: {},\n              locale,\n            };\n          });\n      })\n    );\n\n    return Promise.resolve(importedTrads);\n  },\n};\n\nexport * from './exports';\n", "import type { Permission as StrapiPermission } from '@strapi/admin/strapi-admin';\n\nexport const PERMISSIONS = {\n  main: [\n    {\n      action: 'plugin::content-releases.read',\n      subject: null,\n      id: '',\n      actionParameters: {},\n      properties: {},\n      conditions: [],\n    },\n  ],\n  create: [\n    {\n      action: 'plugin::content-releases.create',\n      subject: null,\n      id: '',\n      actionParameters: {},\n      properties: {},\n      conditions: [],\n    },\n  ],\n  update: [\n    {\n      action: 'plugin::content-releases.update',\n      subject: null,\n      id: '',\n      actionParameters: {},\n      properties: {},\n      conditions: [],\n    },\n  ],\n  delete: [\n    {\n      action: 'plugin::content-releases.delete',\n      subject: null,\n      id: '',\n      actionParameters: {},\n      properties: {},\n      conditions: [],\n    },\n  ],\n  createAction: [\n    {\n      action: 'plugin::content-releases.create-action',\n      subject: null,\n      id: '',\n      actionParameters: {},\n      properties: {},\n      conditions: [],\n    },\n  ],\n  deleteAction: [\n    {\n      action: 'plugin::content-releases.delete-action',\n      subject: null,\n      id: '',\n      actionParameters: {},\n      properties: {},\n      conditions: [],\n    },\n  ],\n  publish: [\n    {\n      action: 'plugin::content-releases.publish',\n      subject: null,\n      id: '',\n      actionParameters: {},\n      properties: {},\n      conditions: [],\n    },\n  ],\n} satisfies Record<string, StrapiPermission[]>;\n\nexport const PERMISSIONS_SETTINGS = {\n  read: [\n    {\n      action: 'plugin::content-releases.settings.read',\n      subject: null,\n      id: '',\n      actionParameters: {},\n      properties: {},\n      conditions: [],\n    },\n  ],\n  update: [\n    {\n      action: 'plugin::content-releases.settings.update',\n      subject: null,\n      id: '',\n      actionParameters: {},\n      properties: {},\n      conditions: [],\n    },\n  ],\n} satisfies Record<string, StrapiPermission[]>;\n", "import * as React from 'react';\n\nimport { VisuallyHidden, Field, Flex } from '@strapi/design-system';\nimport { styled } from 'styled-components';\n\ninterface FieldWrapperProps extends Field.Props {\n  actionType: 'publish' | 'unpublish';\n}\n\nconst getBorderLeftRadiusValue = (actionType: FieldWrapperProps['actionType']) => {\n  return actionType === 'publish' ? 1 : 0;\n};\n\nconst getBorderRightRadiusValue = (actionType: FieldWrapperProps['actionType']) => {\n  return actionType === 'publish' ? 0 : 1;\n};\n\nconst FieldWrapper = styled(Field.Root)<{\n  $actionType: 'publish' | 'unpublish';\n}>`\n  border-top-left-radius: ${({ $actionType, theme }) =>\n    theme.spaces[getBorderLeftRadiusValue($actionType)]};\n  border-bottom-left-radius: ${({ $actionType, theme }) =>\n    theme.spaces[getBorderLeftRadiusValue($actionType)]};\n  border-top-right-radius: ${({ $actionType, theme }) =>\n    theme.spaces[getBorderRightRadiusValue($actionType)]};\n  border-bottom-right-radius: ${({ $actionType, theme }) =>\n    theme.spaces[getBorderRightRadiusValue($actionType)]};\n\n  > label {\n    color: inherit;\n    padding: ${({ theme }) => `${theme.spaces[2]} ${theme.spaces[3]}`};\n    text-align: center;\n    vertical-align: middle;\n    text-transform: capitalize;\n  }\n\n  &[data-checked='true'] {\n    color: ${({ theme, $actionType }) =>\n      $actionType === 'publish' ? theme.colors.primary700 : theme.colors.danger600};\n    background-color: ${({ theme, $actionType }) =>\n      $actionType === 'publish' ? theme.colors.primary100 : theme.colors.danger100};\n    border-color: ${({ theme, $actionType }) =>\n      $actionType === 'publish' ? theme.colors.primary700 : theme.colors.danger600};\n  }\n\n  &[data-checked='false'] {\n    border-left: ${({ $actionType }) => $actionType === 'unpublish' && 'none'};\n    border-right: ${({ $actionType }) => $actionType === 'publish' && 'none'};\n  }\n\n  &[data-checked='false'][data-disabled='false']:hover {\n    color: ${({ theme }) => theme.colors.neutral700};\n    background-color: ${({ theme }) => theme.colors.neutral100};\n    border-color: ${({ theme }) => theme.colors.neutral200};\n\n    & > label {\n      cursor: pointer;\n    }\n  }\n\n  &[data-disabled='true'] {\n    color: ${({ theme }) => theme.colors.neutral600};\n    background-color: ${({ theme }) => theme.colors.neutral150};\n    border-color: ${({ theme }) => theme.colors.neutral300};\n  }\n`;\n\ninterface ActionOptionProps {\n  selected: 'publish' | 'unpublish';\n  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\n  name: string;\n  disabled?: boolean;\n}\n\ninterface OptionProps extends ActionOptionProps {\n  actionType: 'publish' | 'unpublish';\n}\n\nconst ActionOption = ({\n  selected,\n  actionType,\n  handleChange,\n  name,\n  disabled = false,\n}: OptionProps) => {\n  return (\n    <FieldWrapper\n      $actionType={actionType}\n      background=\"primary0\"\n      borderColor=\"neutral200\"\n      color={selected === actionType ? 'primary600' : 'neutral600'}\n      position=\"relative\"\n      cursor=\"pointer\"\n      data-checked={selected === actionType}\n      data-disabled={disabled && selected !== actionType}\n    >\n      <Field.Label>\n        <VisuallyHidden>\n          <Field.Input\n            type=\"radio\"\n            name={name}\n            checked={selected === actionType}\n            onChange={handleChange}\n            value={actionType}\n            disabled={disabled}\n          />\n        </VisuallyHidden>\n        {actionType}\n      </Field.Label>\n    </FieldWrapper>\n  );\n};\n\nexport const ReleaseActionOptions = ({\n  selected,\n  handleChange,\n  name,\n  disabled = false,\n}: ActionOptionProps) => {\n  return (\n    <Flex>\n      <ActionOption\n        actionType=\"publish\"\n        selected={selected}\n        handleChange={handleChange}\n        name={name}\n        disabled={disabled}\n      />\n      <ActionOption\n        actionType=\"unpublish\"\n        selected={selected}\n        handleChange={handleChange}\n        name={name}\n        disabled={disabled}\n      />\n    </Flex>\n  );\n};\n", "import * as React from 'react';\n\nimport {\n  use<PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON>,\n  useNotification,\n  useAuth,\n  useRB<PERSON>,\n  isFetchError,\n} from '@strapi/admin/strapi-admin';\nimport { Flex, Typography, Menu, AccessibleIcon } from '@strapi/design-system';\nimport { Cross, More, Pencil } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { NavLink } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { DeleteReleaseAction, ReleaseAction } from '../../../shared/contracts/release-actions';\nimport { Release } from '../../../shared/contracts/releases';\nimport { PERMISSIONS } from '../constants';\nimport { useDeleteReleaseActionMutation } from '../services/release';\n\nconst StyledMenuItem = styled(Menu.Item)<{ $variant?: 'neutral' | 'danger' }>`\n  &:hover {\n    background: ${({ theme, $variant = 'neutral' }) => theme.colors[`${$variant}100`]};\n\n    svg {\n      fill: ${({ theme, $variant = 'neutral' }) => theme.colors[`${$variant}600`]};\n    }\n\n    a {\n      color: ${({ theme }) => theme.colors.neutral800};\n    }\n  }\n\n  svg {\n    color: ${({ theme, $variant = 'neutral' }) => theme.colors[`${$variant}500`]};\n  }\n\n  span {\n    color: ${({ theme, $variant = 'neutral' }) => theme.colors[`${$variant}800`]};\n  }\n\n  span,\n  a {\n    width: 100%;\n  }\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * DeleteReleaseActionItemProps\n * -----------------------------------------------------------------------------------------------*/\ninterface DeleteReleaseActionItemProps {\n  releaseId: DeleteReleaseAction.Request['params']['releaseId'];\n  actionId: DeleteReleaseAction.Request['params']['actionId'];\n}\n\nconst DeleteReleaseActionItem = ({ releaseId, actionId }: DeleteReleaseActionItemProps) => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { formatAPIError } = useAPIErrorHandler();\n  const [deleteReleaseAction] = useDeleteReleaseActionMutation();\n  const {\n    allowedActions: { canDeleteAction },\n  } = useRBAC(PERMISSIONS);\n\n  const handleDeleteAction = async () => {\n    const response = await deleteReleaseAction({\n      params: { releaseId, actionId },\n    });\n\n    if ('data' in response) {\n      // Handle success\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({\n          id: 'content-releases.content-manager-edit-view.remove-from-release.notification.success',\n          defaultMessage: 'Entry removed from release',\n        }),\n      });\n\n      return;\n    }\n\n    if ('error' in response) {\n      if (isFetchError(response.error)) {\n        // Handle fetch error\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(response.error),\n        });\n      } else {\n        // Handle generic error\n        toggleNotification({\n          type: 'danger',\n          message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occurred' }),\n        });\n      }\n    }\n  };\n\n  if (!canDeleteAction) {\n    return null;\n  }\n\n  return (\n    <StyledMenuItem $variant=\"danger\" onSelect={handleDeleteAction}>\n      <Flex gap={2}>\n        <Cross width=\"1.6rem\" height=\"1.6rem\" />\n        <Typography textColor=\"danger600\" variant=\"omega\">\n          {formatMessage({\n            id: 'content-releases.content-manager-edit-view.remove-from-release',\n            defaultMessage: 'Remove from release',\n          })}\n        </Typography>\n      </Flex>\n    </StyledMenuItem>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ReleaseActionEntryLinkItem\n * -----------------------------------------------------------------------------------------------*/\ninterface ReleaseActionEntryLinkItemProps {\n  contentTypeUid: ReleaseAction['contentType'];\n  documentId: ReleaseAction['entry']['documentId'];\n  locale: ReleaseAction['locale'];\n}\n\nconst ReleaseActionEntryLinkItem = ({\n  contentTypeUid,\n  documentId,\n  locale,\n}: ReleaseActionEntryLinkItemProps) => {\n  const { formatMessage } = useIntl();\n  const userPermissions = useAuth('ReleaseActionEntryLinkItem', (state) => state.permissions);\n\n  // Confirm user has permissions to access the entry for the given locale\n  const canUpdateEntryForLocale = React.useMemo(() => {\n    const updatePermissions = userPermissions.find(\n      (permission) =>\n        permission.subject === contentTypeUid &&\n        permission.action === 'plugin::content-manager.explorer.update'\n    );\n\n    if (!updatePermissions) {\n      return false;\n    }\n\n    return Boolean(!locale || updatePermissions.properties?.locales?.includes(locale));\n  }, [contentTypeUid, locale, userPermissions]);\n\n  const {\n    allowedActions: { canUpdate: canUpdateContentType },\n  } = useRBAC({\n    updateContentType: [\n      {\n        action: 'plugin::content-manager.explorer.update',\n        subject: contentTypeUid,\n      },\n    ],\n  });\n\n  if (!canUpdateContentType || !canUpdateEntryForLocale) {\n    return null;\n  }\n\n  return (\n    <StyledMenuItem\n      /* @ts-expect-error inference isn't working in DS */\n      tag={NavLink}\n      isLink\n      to={{\n        pathname: `/content-manager/collection-types/${contentTypeUid}/${documentId}`,\n        search: locale && `?plugins[i18n][locale]=${locale}`,\n      }}\n    >\n      <Flex gap={2}>\n        <Pencil width=\"1.6rem\" height=\"1.6rem\" />\n        <Typography variant=\"omega\">\n          {formatMessage({\n            id: 'content-releases.content-manager-edit-view.edit-entry',\n            defaultMessage: 'Edit entry',\n          })}\n        </Typography>\n      </Flex>\n    </StyledMenuItem>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * EditReleaseItem\n * -----------------------------------------------------------------------------------------------*/\ninterface EditReleaseItemProps {\n  releaseId: Release['id'];\n}\n\nconst EditReleaseItem = ({ releaseId }: EditReleaseItemProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    /* @ts-expect-error inference isn't working in DS */\n    <StyledMenuItem tag={NavLink} isLink to={`/plugins/content-releases/${releaseId}`}>\n      <Flex gap={2}>\n        <Pencil width=\"1.6rem\" height=\"1.6rem\" />\n        <Typography textColor=\"neutral800\" variant=\"omega\">\n          {formatMessage({\n            id: 'content-releases.content-manager-edit-view.edit-release',\n            defaultMessage: 'Edit release',\n          })}\n        </Typography>\n      </Flex>\n    </StyledMenuItem>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Root\n * -----------------------------------------------------------------------------------------------*/\n\ninterface RootProps {\n  children: React.ReactNode;\n  hasTriggerBorder?: boolean;\n}\n\nconst Root = ({ children }: RootProps) => {\n  const { formatMessage } = useIntl();\n\n  const { allowedActions } = useRBAC(PERMISSIONS);\n\n  return (\n    // A user can access the dropdown if they have permissions to delete a release-action OR update a release\n    allowedActions.canDeleteAction || allowedActions.canUpdate ? (\n      <Menu.Root>\n        <StyledMoreButton variant=\"tertiary\" endIcon={null} paddingLeft=\"7px\" paddingRight=\"7px\">\n          <AccessibleIcon\n            label={formatMessage({\n              id: 'content-releases.content-manager-edit-view.release-action-menu',\n              defaultMessage: 'Release action options',\n            })}\n          >\n            <More />\n          </AccessibleIcon>\n        </StyledMoreButton>\n        <Menu.Content top={1} popoverPlacement=\"bottom-end\">\n          {children}\n        </Menu.Content>\n      </Menu.Root>\n    ) : null\n  );\n};\n\nconst StyledMoreButton = styled(Menu.Trigger)`\n  & > span {\n    display: flex;\n  }\n`;\n\nexport const ReleaseActionMenu = {\n  Root,\n  EditReleaseItem,\n  DeleteReleaseActionItem,\n  ReleaseActionEntryLinkItem,\n};\n", "export const pluginId = 'content-releases';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoHA,IAAMA,uBAAN,MAAMA;EAoBJC,qBAAqBC,QAA+D;AAClF,QAAIC,MAAMC,QAAQF,MAAS,GAAA;AACzB,WAAKG,qBAAqB;QAAI,GAAA,KAAKA;QAAuBH,GAAAA;MAAO;eACxD,OAAOA,WAAW,YAAY;AACvC,WAAKG,qBAAqBH,OAAO,KAAKG,kBAAkB;WACnD;AACL,YAAM,IAAIC,MACR,yGAAyGC,iBACvGL,MAAAA,CAAAA,EACC;IAEP;EACF;EAIAM,kBACEC,SACA;AACA,QAAIN,MAAMC,QAAQK,OAAU,GAAA;AAC1B,WAAKC,kBAAkB;QAAI,GAAA,KAAKA;QAAoBD,GAAAA;MAAQ;eACnD,OAAOA,YAAY,YAAY;AACxC,WAAKC,kBAAkBD,QAAQ,KAAKC,eAAe;WAC9C;AACL,YAAM,IAAIJ,MACR,uGAAuGC,iBACrGE,OAAAA,CAAAA,EACC;IAEP;EACF;EAIAE,wBACEF,SACA;AACA,QAAIN,MAAMC,QAAQK,OAAU,GAAA;AAC1B,WAAKG,gBAAgB;QAAI,GAAA,KAAKA;QAAkBH,GAAAA;MAAQ;eAC/C,OAAOA,YAAY,YAAY;AACxC,WAAKG,gBAAgBH,QAAQ,KAAKG,aAAa;WAC1C;AACL,YAAM,IAAIN,MACR,6GAA6GC,iBAC3GE,OAAAA,CAAAA,EACC;IAEP;EACF;EAIAI,cAAcJ,SAA0E;AACtF,QAAIN,MAAMC,QAAQK,OAAU,GAAA;AAC1B,WAAKK,cAAc;QAAI,GAAA,KAAKA;QAAgBL,GAAAA;MAAQ;eAC3C,OAAOA,YAAY,YAAY;AACxC,WAAKK,cAAcL,QAAQ,KAAKK,WAAW;WACtC;AACL,YAAM,IAAIR,MACR,mGAAmGC,iBACjGE,OAAAA,CAAAA,EACC;IAEP;EACF;EAEA,IAAIM,SAAS;AACX,WAAO;MACLC,IAAIC;MACJC,MAAM;MACNC,gBAAgBC;MAChBC,MAAM;QACJR,eAAe,KAAKA,cAAcS,KAAK,IAAI;QAC3Cd,mBAAmB,KAAKA,kBAAkBc,KAAK,IAAI;QACnDX,yBAAyB,KAAKA,wBAAwBW,KAAK,IAAI;QAC/DrB,sBAAsB,KAAKA,qBAAqBqB,KAAK,IAAI;QACzDC,gBAAgB,MAAM,KAAKT;QAC3BU,oBAAoB,CAACC,aAAAA;AAQnB,cAAIA,UAAU;AACZ,mBAAO,KAAKf,gBAAgBgB,OAAO,CAACC,WAAAA;AAClC,qBAAOA,OAAOF,YAAYG,UAAa;gBAACD,OAAOF;gBAAUI,KAAI,EAAGC,SAASL,QAAAA;YAC3E,CAAA;UACF;AAEA,iBAAO,KAAKf;QACd;QACAqB,uBAAuB,MAAM,KAAK1B;QAClC2B,kBAAkB,MAAM,KAAKpB;MAC/B;IACF;EACF;EArGAqB,cAAc;AAVb,SACDnB,cAAqC;MAAIoB,GAAAA;IAAqB;SAC9DxB,kBAA6C;MACxCyB,GAAAA;MACAC,GAAAA;MACAC,GAAAA;IACJ;SACDhC,qBAAuC;MAACiC;IAAa;AACrD1B,SAAAA,gBAAyC,CAAA;EAE1B;AAsGjB;AAWA,IAAML,mBAAmB,CAACgC,UAAAA;AACxB,QAAMC,aAAa,OAAOD;AAE1B,MAAIC,eAAe,UAAU;AAC3B,QAAID,UAAU,KAAM,QAAO;AAC3B,QAAIpC,MAAMC,QAAQmC,KAAAA,EAAQ,QAAO;AACjC,QAAIA,iBAAiBE,UAAUF,MAAMN,YAAYf,SAAS,UAAU;AAClE,aAAOqB,MAAMN,YAAYf;IAC3B;EACF;AAEA,SAAOsB;AACT;;;;;ACzPA,IAAME,gBAAyC,CAAC,EAAEC,OAAOC,SAAQ,MAAE;AACjE,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,CAAC,EAAEC,MAAK,CAAE,IAAIC,eAAAA;AACpB,QAAMC,WAAWC,YAAAA;AACjB,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAM,EAAEC,SAAQ,IAAKC,YAAAA;AACrB,QAAMC,yBAAqBC,qBAAU;IAAEC,SAASV,MAAMU;KAAW;IAAEC,QAAQ;EAAM,CAAA;AAEjF,MAAI,CAACC,OAAOC,OAAOC,SAASC,UAAU,qBAAwB,GAAA;AAC5D,WAAO;EACT;AAEA,QAAMC,gBAAgB,MAAA;AACpB,UAAMC,cAAc;MAAEX,UAAU;MAAWY,QAAQV;IAAmB;AACtEJ,eAAW,gBAAgB;MACzBe,MAAMb;MACNc,IAAI,GAAGd,QAAS,IAAGW,YAAYX,QAAQ;IACzC,CAAA;AACAJ,aAASe,WAAAA;EACX;AAEA,SAAO;IACLI,UAAMC,wBAACC,eAAAA,CAAAA,CAAAA;IACPC,OAAO1B,cAAc;MACnB2B,IAAI;MACJC,gBAAgB;IAClB,CAAA;IACAC,SAASX;IACTY;;;;;MAKE,CAAC/B;;;;;MAMD,CAACA,SAAS4B;;;;;MAMV,CAAC7B,MAAMiC,WAAW,OAAA;;IACpBC,UAAU;EACZ;AACF;AAEAnC,cAAcoC,OAAO;AACrBpC,cAAcmC,WAAW;;;AClDzB,IAAME,eAAkD;EACtDC,UAAUC,KAAc;AACtB,UAAM,EAAEC,kBAAiB,IAAKD,IAAIE,UAAU,iBAAA,EAAmBC;AAS/DF,sBAAkB,CAACG,YAAAA;AACjB,YAAMC,sBAAsBD,QAAQE,UAAU,CAACC,WAAWA,OAAOC,SAAS,QAAA;AAC1EJ,cAAQK,OAAOJ,qBAAqB,GAAGK,aAAAA;AACvC,aAAON;IACT,CAAA;EACF;AACF;;;ACtBA,IAAMO,WAAUC,gBAAgB;EAC9BC,KAAKC;AACP,CAAA;;;;;;ACaA,IAAMC,qBAAqB,CAAC,EAAEC,SAASC,OAAOC,SAAQ,MAA2B;AAC/E,MAAIF,SAAS;AACX,eAAOG,yBAACC,aAAAA;MAAQH;MAAeC;;EACjC;AAEA,SAAOA;AACT;AAEMG,IAAAA,mBAAmC,CAAC,EAAEC,OAAOC,YAAYC,SAAQ,MAAE;;AACvE,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAM,EAAEC,SAAQ,IAAKC,YAAAA;AACrB,QAAM,CAAC,EAAEC,MAAK,CAAE,IAAIC,eAAAA;AACpB,QAAMC,aAAaC,QAAQ,oBAAoB,CAACC,UAAUA,MAAMC,QAAQ;AAOxE,QAAM,EAAEC,MAAMC,MAAK,IAAKC,sBAAsB;IAC5CC,QAAQ;MACNC,aAAanB;IACf;IACAS,OAAO;MACLR;MACAmB,QAAQlB,qCAAUkB;MAClBC,QAAQnB,qCAAUmB;IACpB;EACF,CAAA;AAEA,MAAI,GAACN,kCAAMA,SAANA,mBAAYO,QAAON,OAAO;AAC7B,WAAO;EACT;AAEA,QAAMO,kBAAkB,MAAA;AAEtB,UAAMC,sBAAsBjB,SAASkB,QAAQ,OAAO,EAAM,IAAA;AAC1DpB,eAAW,gBAAgB;MAAEqB,MAAMnB;MAAUoB,IAAIH;IAAoB,CAAA;EACvE;AAEA,SAAO;IACLI,OAAOzB,cAAc;MAAE0B,IAAI;MAAuCC,gBAAgB;IAAU,CAAA;IAC5FC,aACElC,yBAACJ,oBAAAA;MACCE,OAAOQ,cAAc;QACnB0B,IAAI;QACJC,gBAAgB;MAClB,CAAA;MACApC,SAASiB;MAET,cAAAd,yBAACmC,KAAAA;QAAIC,QAAO;QAAcC,OAAM;QAC9B,cAAArC,yBAACsC,QAAAA;UACCC,SAAQ;UACRC,KAAKC;UACLX,IAAI;YAAEpB,UAAU;YAAWgC,YAAQC,sBAAU/B,OAAO;cAAEgC,QAAQ;YAAM,CAAA;UAAG;UACvEC,SAASnB;UACTW,OAAM;UACNS,UAAUhC;UACViC,eAAejC,aAAa,SAASkC;UACrCC,UAAUnC,aAAa,KAAKkC;oBAE3B1C,cAAc;YACb0B,IAAI;YACJC,gBAAgB;UAClB,CAAA;;;;EAKV;AACF;;;ACnFA,IAAMiB,eAA0C;EAC9CC,UAAUC,KAAG;AACX,UAAMC,2BAA2BD,IAAIE,UAAU,iBAAA,EAC5CC;AAEHF,6BAAyBG,qBAAqB;MAACC;IAAiB,CAAA;EAClE;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA,IAAA,QAAe;EACbC,SAASC,KAAQ;AACf,UAAMC,KAAK,IAAIC,qBAAAA;AAEfF,QAAIG,YAAY;MACd,CAACC,SAAAA,GAAYC;IACf,CAAA;AAEAL,QAAIM,YAAY;MACdC,IAAIH;MACJI,MAAMC;MACNC,WAAW;QACTC,IAAI;QACJC,gBAAgB;MAClB;MACAC,aAAa,CAAA;MACbC,UAAU;IACZ,CAAA;AAEAd,QAAIe,OAAOC,SAAS;MAClBC,MAAM;MACNC,MAAM,YAAA;AACJ,cAAM,EAAEC,OAAM,IAAK,MAAM,OAAO,sBAAA;AAEhC,eAAO;UACLC,WAAWD;QACb;MACF;MACAE,UAAUC;IACZ,CAAA;AAEAtB,QAAIuB,eAAetB,GAAGuB,MAAM;AAG5BxB,QAAIyB,QAAQ1B,SAAS;MACnB;QACES,MAAMkB;QACNC,OAAO;UACLhB,IAAI,GAAGP,SAAU;UACjBQ,gBAAgB;QAClB;QACAgB,WAAW,YAAA;AACT,gBAAM,EAAEC,iBAAgB,IAAK,MAAM,OAAO,uBAAA;AAC1C,iBAAOA;QACT;QACAC,UAAU1B;QACVO,IAAI;QACJE,aAAa;UAAC;YAAEkB,QAAQ;UAAwC;QAAE;MACpE;MACA;QACEvB,MAAMwB;QACNL,OAAO;UACLhB,IAAI,GAAGP,SAAU;UACjBQ,gBAAgB;QAClB;QACAgB,WAAW,YAAA;AACT,gBAAM,EAAEK,oBAAmB,IAAK,MAAM,OAAO,uBAAA;AAC7C,iBAAOA;QACT;QACAH,UAAU1B;QACVO,IAAI;QACJE,aAAa;UAAC;YAAEkB,QAAQ;UAAwC;QAAE;MACpE;IACD,CAAA;EACH;EACAG,UAAUlC,KAAQ;AAChB,QAAI,OAAOmC,aAAaD,cAAc,YAAY;AAChDC,mBAAaD,UAAUlC,GAAAA;IACzB;AACA,QAAI,OAAOoC,aAAaF,cAAc,YAAY;AAChDE,mBAAaF,UAAUlC,GAAAA;IACzB;EACF;EACA,MAAMqC,cAAc,EAAEC,QAAO,GAAyB;AACpD,UAAMC,gBAAgB,MAAMC,QAAQC,IAClCH,QAAQI,IAAI,CAACC,WAAAA;AACX,aAAO,kCAAO,kBAAkBA,MAAO,OAAM,EAC1CC,KAAK,CAAC,EAAEC,SAASC,KAAI,MAAE;AACtB,eAAO;UACLA,MAAMC,yBAAyBD,MAAM1C,SAAAA;UACrCuC;QACF;MACF,CAAA,EACCK,MAAM,MAAA;AACL,eAAO;UACLF,MAAM,CAAA;UACNH;QACF;MACF,CAAA;IACJ,CAAA,CAAA;AAGF,WAAOH,QAAQS,QAAQV,aAAAA;EACzB;AACF;;;IC1GaW,cAAc;EACzBC,MAAM;IACJ;MACEC,QAAQ;MACRC,SAAS;MACTC,IAAI;MACJC,kBAAkB,CAAA;MAClBC,YAAY,CAAA;MACZC,YAAY,CAAA;IACd;EACD;EACDC,QAAQ;IACN;MACEN,QAAQ;MACRC,SAAS;MACTC,IAAI;MACJC,kBAAkB,CAAA;MAClBC,YAAY,CAAA;MACZC,YAAY,CAAA;IACd;EACD;EACDE,QAAQ;IACN;MACEP,QAAQ;MACRC,SAAS;MACTC,IAAI;MACJC,kBAAkB,CAAA;MAClBC,YAAY,CAAA;MACZC,YAAY,CAAA;IACd;EACD;EACDG,QAAQ;IACN;MACER,QAAQ;MACRC,SAAS;MACTC,IAAI;MACJC,kBAAkB,CAAA;MAClBC,YAAY,CAAA;MACZC,YAAY,CAAA;IACd;EACD;EACDI,cAAc;IACZ;MACET,QAAQ;MACRC,SAAS;MACTC,IAAI;MACJC,kBAAkB,CAAA;MAClBC,YAAY,CAAA;MACZC,YAAY,CAAA;IACd;EACD;EACDK,cAAc;IACZ;MACEV,QAAQ;MACRC,SAAS;MACTC,IAAI;MACJC,kBAAkB,CAAA;MAClBC,YAAY,CAAA;MACZC,YAAY,CAAA;IACd;EACD;EACDM,SAAS;IACP;MACEX,QAAQ;MACRC,SAAS;MACTC,IAAI;MACJC,kBAAkB,CAAA;MAClBC,YAAY,CAAA;MACZC,YAAY,CAAA;IACd;EACD;AACH;;;;;AChEA,IAAMO,2BAA2B,CAACC,eAAAA;AAChC,SAAOA,eAAe,YAAY,IAAI;AACxC;AAEA,IAAMC,4BAA4B,CAACD,eAAAA;AACjC,SAAOA,eAAe,YAAY,IAAI;AACxC;AAEA,IAAME,eAAeC,GAAOC,MAAMC,IAAI;4BAGV,CAAC,EAAEC,aAAaC,MAAK,MAC7CA,MAAMC,OAAOT,yBAAyBO,WAAAA,CAAAA,CAAa;+BACxB,CAAC,EAAEA,aAAaC,MAAK,MAChDA,MAAMC,OAAOT,yBAAyBO,WAAAA,CAAAA,CAAa;6BAC1B,CAAC,EAAEA,aAAaC,MAAK,MAC9CA,MAAMC,OAAOP,0BAA0BK,WAAAA,CAAAA,CAAa;gCACxB,CAAC,EAAEA,aAAaC,MAAK,MACjDA,MAAMC,OAAOP,0BAA0BK,WAAAA,CAAAA,CAAa;;;;eAIzC,CAAC,EAAEC,MAAK,MAAO,GAAGA,MAAMC,OAAO,CAAA,CAAE,IAAID,MAAMC,OAAO,CAAE,CAAA,EAAE;;;;;;;aAOxD,CAAC,EAAED,OAAOD,YAAW,MAC5BA,gBAAgB,YAAYC,MAAME,OAAOC,aAAaH,MAAME,OAAOE,SAAS;wBAC1D,CAAC,EAAEJ,OAAOD,YAAW,MACvCA,gBAAgB,YAAYC,MAAME,OAAOG,aAAaL,MAAME,OAAOI,SAAS;oBAC9D,CAAC,EAAEN,OAAOD,YAAW,MACnCA,gBAAgB,YAAYC,MAAME,OAAOC,aAAaH,MAAME,OAAOE,SAAS;;;;mBAI/D,CAAC,EAAEL,YAAW,MAAOA,gBAAgB,eAAe,MAAO;oBAC1D,CAAC,EAAEA,YAAW,MAAOA,gBAAgB,aAAa,MAAO;;;;aAIhE,CAAC,EAAEC,MAAK,MAAOA,MAAME,OAAOK,UAAU;wBAC3B,CAAC,EAAEP,MAAK,MAAOA,MAAME,OAAOM,UAAU;oBAC1C,CAAC,EAAER,MAAK,MAAOA,MAAME,OAAOO,UAAU;;;;;;;;aAQ7C,CAAC,EAAET,MAAK,MAAOA,MAAME,OAAOQ,UAAU;wBAC3B,CAAC,EAAEV,MAAK,MAAOA,MAAME,OAAOS,UAAU;oBAC1C,CAAC,EAAEX,MAAK,MAAOA,MAAME,OAAOU,UAAU;;;AAe1D,IAAMC,eAAe,CAAC,EACpBC,UACArB,YACAsB,cACAC,MACAC,WAAW,MAAK,MACJ;AACZ,aACEC,yBAACvB,cAAAA;IACCI,aAAaN;IACb0B,YAAW;IACXC,aAAY;IACZC,OAAOP,aAAarB,aAAa,eAAe;IAChD6B,UAAS;IACTC,QAAO;IACPC,gBAAcV,aAAarB;IAC3BgC,iBAAeR,YAAYH,aAAarB;kBAExCiC,0BAAC7B,MAAM8B,OAAK;;YACVT,yBAACU,gBAAAA;wBACCV,yBAACrB,MAAMgC,OAAK;YACVC,MAAK;YACLd;YACAe,SAASjB,aAAarB;YACtBuC,UAAUjB;YACVkB,OAAOxC;YACPwB;;;QAGHxB;;;;AAIT;AAEayC,IAAAA,uBAAuB,CAAC,EACnCpB,UACAC,cACAC,MACAC,WAAW,MAAK,MACE;AAClB,aACES,0BAACS,MAAAA;;UACCjB,yBAACL,cAAAA;QACCpB,YAAW;QACXqB;QACAC;QACAC;QACAC;;UAEFC,yBAACL,cAAAA;QACCpB,YAAW;QACXqB;QACAC;QACAC;QACAC;;;;AAIR;;;;;ACtHA,IAAMmB,iBAAiBC,GAAOC,KAAKC,IAAI;;kBAErB,CAAC,EAAEC,OAAOC,WAAW,UAAS,MAAOD,MAAME,OAAO,GAAGD,QAAAA,KAAa,CAAC;;;cAGvE,CAAC,EAAED,OAAOC,WAAW,UAAS,MAAOD,MAAME,OAAO,GAAGD,QAAAA,KAAa,CAAC;;;;eAIlE,CAAC,EAAED,MAAK,MAAOA,MAAME,OAAOC,UAAU;;;;;aAKxC,CAAC,EAAEH,OAAOC,WAAW,UAAS,MAAOD,MAAME,OAAO,GAAGD,QAAAA,KAAa,CAAC;;;;aAInE,CAAC,EAAED,OAAOC,WAAW,UAAS,MAAOD,MAAME,OAAO,GAAGD,QAAAA,KAAa,CAAC;;;;;;;;AAiBhF,IAAMG,0BAA0B,CAAC,EAAEC,WAAWC,SAAQ,MAAgC;AACpF,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,eAAc,IAAKC,mBAAAA;AAC3B,QAAM,CAACC,mBAAAA,IAAuBC,+BAAAA;AAC9B,QAAM,EACJC,gBAAgB,EAAEC,gBAAe,EAAE,IACjCC,QAAQC,WAAAA;AAEZ,QAAMC,qBAAqB,YAAA;AACzB,UAAMC,WAAW,MAAMP,oBAAoB;MACzCQ,QAAQ;QAAEhB;QAAWC;MAAS;IAChC,CAAA;AAEA,QAAI,UAAUc,UAAU;AAEtBX,yBAAmB;QACjBa,MAAM;QACNC,SAAShB,cAAc;UACrBiB,IAAI;UACJC,gBAAgB;QAClB,CAAA;MACF,CAAA;AAEA;IACF;AAEA,QAAI,WAAWL,UAAU;AACvB,UAAIM,aAAaN,SAASO,KAAK,GAAG;AAEhClB,2BAAmB;UACjBa,MAAM;UACNC,SAASZ,eAAeS,SAASO,KAAK;QACxC,CAAA;aACK;AAELlB,2BAAmB;UACjBa,MAAM;UACNC,SAAShB,cAAc;YAAEiB,IAAI;YAAsBC,gBAAgB;UAAoB,CAAA;QACzF,CAAA;MACF;IACF;EACF;AAEA,MAAI,CAACT,iBAAiB;AACpB,WAAO;EACT;AAEA,aACEY,yBAAChC,gBAAAA;IAAeK,UAAS;IAAS4B,UAAUV;IAC1C,cAAAW,0BAACC,MAAAA;MAAKC,KAAK;;YACTJ,yBAACK,eAAAA;UAAMC,OAAM;UAASC,QAAO;;YAC7BP,yBAACQ,YAAAA;UAAWC,WAAU;UAAYC,SAAQ;oBACvC/B,cAAc;YACbiB,IAAI;YACJC,gBAAgB;UAClB,CAAA;;;;;AAKV;AAWA,IAAMc,6BAA6B,CAAC,EAClCC,gBACAC,YACAC,OAAM,MAC0B;AAChC,QAAM,EAAEnC,cAAa,IAAKC,QAAAA;AAC1B,QAAMmC,kBAAkBC,QAAQ,8BAA8B,CAACC,UAAUA,MAAMC,WAAW;AAG1F,QAAMC,0BAAgCC,cAAQ,MAAA;;AAC5C,UAAMC,oBAAoBN,gBAAgBO,KACxC,CAACC,eACCA,WAAWC,YAAYZ,kBACvBW,WAAWE,WAAW,yCAAA;AAG1B,QAAI,CAACJ,mBAAmB;AACtB,aAAO;IACT;AAEA,WAAOK,QAAQ,CAACZ,YAAUO,6BAAkBM,eAAlBN,mBAA8BO,YAA9BP,mBAAuCQ,SAASf,QAAAA;KACzE;IAACF;IAAgBE;IAAQC;EAAgB,CAAA;AAE5C,QAAM,EACJ5B,gBAAgB,EAAE2C,WAAWC,qBAAoB,EAAE,IACjD1C,QAAQ;IACV2C,mBAAmB;MACjB;QACEP,QAAQ;QACRD,SAASZ;MACX;IACD;EACH,CAAA;AAEA,MAAI,CAACmB,wBAAwB,CAACZ,yBAAyB;AACrD,WAAO;EACT;AAEA,aACEnB,yBAAChC,gBAAAA;;IAECiE,KAAKC;IACLC,QAAM;IACNC,IAAI;MACFC,UAAU,qCAAqCzB,cAAAA,IAAkBC,UAAAA;MACjEyB,QAAQxB,UAAU,0BAA0BA,MAAAA;IAC9C;IAEA,cAAAZ,0BAACC,MAAAA;MAAKC,KAAK;;YACTJ,yBAACuC,eAAAA;UAAOjC,OAAM;UAASC,QAAO;;YAC9BP,yBAACQ,YAAAA;UAAWE,SAAQ;oBACjB/B,cAAc;YACbiB,IAAI;YACJC,gBAAgB;UAClB,CAAA;;;;;AAKV;AASA,IAAM2C,kBAAkB,CAAC,EAAE/D,UAAS,MAAwB;AAC1D,QAAM,EAAEE,cAAa,IAAKC,QAAAA;AAE1B;;QAEEoB,yBAAChC,gBAAAA;MAAeiE,KAAKC;MAASC,QAAM;MAACC,IAAI,6BAA6B3D,SAAAA;MACpE,cAAAyB,0BAACC,MAAAA;QAAKC,KAAK;;cACTJ,yBAACuC,eAAAA;YAAOjC,OAAM;YAASC,QAAO;;cAC9BP,yBAACQ,YAAAA;YAAWC,WAAU;YAAaC,SAAQ;sBACxC/B,cAAc;cACbiB,IAAI;cACJC,gBAAgB;YAClB,CAAA;;;;;;AAKV;AAWA,IAAM4C,OAAO,CAAC,EAAEC,SAAQ,MAAa;AACnC,QAAM,EAAE/D,cAAa,IAAKC,QAAAA;AAE1B,QAAM,EAAEO,eAAc,IAAKE,QAAQC,WAAAA;AAEnC;;IAEEH,eAAeC,mBAAmBD,eAAe2C,gBAC/C5B,0BAAChC,KAAKuE,MAAI;;YACRzC,yBAAC2C,kBAAAA;UAAiBjC,SAAQ;UAAWkC,SAAS;UAAMC,aAAY;UAAMC,cAAa;UACjF,cAAA9C,yBAAC+C,gBAAAA;YACCC,OAAOrE,cAAc;cACnBiB,IAAI;cACJC,gBAAgB;YAClB,CAAA;YAEA,cAAAG,yBAACiD,eAAAA,CAAAA,CAAAA;;;YAGLjD,yBAAC9B,KAAKgF,SAAO;UAACC,KAAK;UAAGC,kBAAiB;UACpCV;;;IAGH,CAAA,IAAA;;AAER;AAEA,IAAMC,mBAAmB1E,GAAOC,KAAKmF,OAAO;;;;;IAM/BC,oBAAoB;EAC/Bb;EACAD;EACAhE;EACAmC;AACF;;;ACrQO,IAAM4C,WAAW;", "names": ["ContentManagerPlugin", "addEditViewSidePanel", "panels", "Array", "isArray", "editViewSidePanels", "Error", "getPrintableType", "addDocumentAction", "actions", "documentActions", "addDocumentHeaderAction", "headerActions", "addBulkAction", "bulkActions", "config", "id", "PLUGIN_ID", "name", "injectionZones", "INJECTION_ZONES", "apis", "bind", "getBulkActions", "getDocumentActions", "position", "filter", "action", "undefined", "flat", "includes", "getEditViewSidePanels", "getHeaderActions", "constructor", "DEFAULT_BULK_ACTIONS", "DEFAULT_ACTIONS", "DEFAULT_TABLE_ROW_ACTIONS", "DEFAULT_HEADER_ACTIONS", "ActionsPanel", "value", "nativeType", "Object", "HistoryAction", "model", "document", "formatMessage", "useIntl", "query", "useQueryParams", "navigate", "useNavigate", "trackUsage", "useTracking", "pathname", "useLocation", "pluginsQueryParams", "stringify", "plugins", "encode", "window", "strapi", "features", "isEnabled", "handleOnClick", "destination", "search", "from", "to", "icon", "_jsx", "ClockCounterClockwise", "label", "id", "defaultMessage", "onClick", "disabled", "startsWith", "position", "type", "historyAdmin", "bootstrap", "app", "addDocumentAction", "getPlugin", "apis", "actions", "indexOfDeleteAction", "findIndex", "action", "type", "splice", "HistoryAction", "reducer", "combineReducers", "app", "appReducer", "ConditionalTooltip", "isShown", "label", "children", "_jsx", "<PERSON><PERSON><PERSON>", "PreviewSidePanel", "model", "documentId", "document", "formatMessage", "useIntl", "trackUsage", "useTracking", "pathname", "useLocation", "query", "useQueryParams", "isModified", "useForm", "state", "modified", "data", "error", "useGetPreviewUrlQuery", "params", "contentType", "locale", "status", "url", "trackNavigation", "destinationPathname", "replace", "from", "to", "title", "id", "defaultMessage", "content", "Box", "cursor", "width", "<PERSON><PERSON>", "variant", "tag", "Link", "search", "stringify", "encode", "onClick", "disabled", "pointerEvents", "undefined", "tabIndex", "previewAdmin", "bootstrap", "app", "contentManagerPluginApis", "getPlugin", "apis", "addEditViewSidePanel", "PreviewSidePanel", "register", "app", "cm", "ContentManagerPlugin", "addReducers", "PLUGIN_ID", "reducer", "addMenuLink", "to", "icon", "<PERSON><PERSON>", "intlLabel", "id", "defaultMessage", "permissions", "position", "router", "addRoute", "path", "lazy", "Layout", "Component", "children", "routes", "registerPlugin", "config", "widgets", "Pencil", "title", "component", "LastEditedWidget", "pluginId", "action", "CheckCircle", "LastPublishedWidget", "bootstrap", "historyAdmin", "previewAdmin", "registerTrads", "locales", "importedTrads", "Promise", "all", "map", "locale", "then", "default", "data", "prefixPluginTranslations", "catch", "resolve", "PERMISSIONS", "main", "action", "subject", "id", "actionParameters", "properties", "conditions", "create", "update", "delete", "createAction", "deleteAction", "publish", "getBorderLeftRadiusValue", "actionType", "getBorderRightRadiusValue", "FieldWrapper", "styled", "Field", "Root", "$actionType", "theme", "spaces", "colors", "primary700", "danger600", "primary100", "danger100", "neutral700", "neutral100", "neutral200", "neutral600", "neutral150", "neutral300", "ActionOption", "selected", "handleChange", "name", "disabled", "_jsx", "background", "borderColor", "color", "position", "cursor", "data-checked", "data-disabled", "_jsxs", "Label", "VisuallyHidden", "Input", "type", "checked", "onChange", "value", "ReleaseActionOptions", "Flex", "StyledMenuItem", "styled", "<PERSON><PERSON>", "<PERSON><PERSON>", "theme", "$variant", "colors", "neutral800", "DeleteReleaseActionItem", "releaseId", "actionId", "formatMessage", "useIntl", "toggleNotification", "useNotification", "formatAPIError", "useAPIErrorHandler", "deleteReleaseAction", "useDeleteReleaseActionMutation", "allowedActions", "canDeleteAction", "useRBAC", "PERMISSIONS", "handleDeleteAction", "response", "params", "type", "message", "id", "defaultMessage", "isFetchError", "error", "_jsx", "onSelect", "_jsxs", "Flex", "gap", "Cross", "width", "height", "Typography", "textColor", "variant", "ReleaseActionEntryLinkItem", "contentTypeUid", "documentId", "locale", "userPermissions", "useAuth", "state", "permissions", "canUpdateEntryForLocale", "useMemo", "updatePermissions", "find", "permission", "subject", "action", "Boolean", "properties", "locales", "includes", "canUpdate", "canUpdateContentType", "updateContentType", "tag", "NavLink", "isLink", "to", "pathname", "search", "Pencil", "EditReleaseItem", "Root", "children", "StyledMoreButton", "endIcon", "paddingLeft", "paddingRight", "AccessibleIcon", "label", "More", "Content", "top", "popoverPlacement", "<PERSON><PERSON>", "ReleaseActionMenu", "pluginId"]}