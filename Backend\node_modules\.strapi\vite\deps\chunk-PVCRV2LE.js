import {
  ForwardRef$J
} from "./chunk-7LKLOY7A.js";
import {
  useQueryParams
} from "./chunk-ODQFI753.js";
import {
  createContext
} from "./chunk-76QM3EFM.js";
import {
  CheckboxImpl,
  EmptyStateLayout,
  Flex,
  IconButton,
  Loader,
  Table,
  Tbody,
  Td,
  Th,
  Thead,
  TooltipImpl,
  Tr,
  Typography,
  useCallbackRef,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  ForwardRef$4T
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/components/Table.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React2 = __toESM(require_react(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useControllableState.mjs
var React = __toESM(require_react(), 1);
function useControllableState({ prop, defaultProp, onChange = () => {
} }) {
  const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({
    defaultProp,
    onChange
  });
  const isControlled = prop !== void 0;
  const value = isControlled ? prop : uncontrolledProp;
  const handleChange = useCallbackRef(onChange);
  const setValue = React.useCallback((nextValue) => {
    if (isControlled) {
      const setter = nextValue;
      const value2 = typeof nextValue === "function" ? setter(prop) : nextValue;
      if (value2 !== prop) handleChange(value2);
    } else {
      setUncontrolledProp(nextValue);
    }
  }, [
    isControlled,
    prop,
    setUncontrolledProp,
    handleChange
  ]);
  return [
    value,
    setValue
  ];
}
function useUncontrolledState({ defaultProp, onChange }) {
  const uncontrolledState = React.useState(defaultProp);
  const [value] = uncontrolledState;
  const prevValueRef = React.useRef(value);
  const handleChange = useCallbackRef(onChange);
  React.useEffect(() => {
    if (prevValueRef.current !== value) {
      handleChange(value);
      prevValueRef.current = value;
    }
  }, [
    value,
    prevValueRef,
    handleChange
  ]);
  return uncontrolledState;
}

// node_modules/@strapi/admin/dist/admin/admin/src/components/Table.mjs
var [TableProvider, useTable] = createContext("Table");
var Root = ({ children, defaultSelectedRows, footer, headers = [], isLoading = false, onSelectedRowsChange, rows = [], selectedRows: selectedRowsProps }) => {
  const [selectedRows = [], setSelectedRows] = useControllableState({
    prop: selectedRowsProps,
    defaultProp: defaultSelectedRows,
    onChange: onSelectedRowsChange
  });
  const [hasHeaderCheckbox, setHasHeaderCheckbox] = React2.useState(false);
  const rowCount = rows.length + 1;
  const colCount = hasHeaderCheckbox ? headers.length + 1 : headers.length;
  const selectRow = (row) => {
    if (Array.isArray(row)) {
      setSelectedRows(row);
    } else {
      setSelectedRows((prev = []) => {
        const currentRowIndex = prev.findIndex((r) => r.id === row.id);
        if (currentRowIndex > -1) {
          return prev.toSpliced(currentRowIndex, 1);
        }
        return [
          ...prev,
          row
        ];
      });
    }
  };
  return (0, import_jsx_runtime.jsx)(TableProvider, {
    colCount,
    hasHeaderCheckbox,
    setHasHeaderCheckbox,
    footer,
    headers,
    isLoading,
    rowCount,
    rows,
    selectedRows,
    selectRow,
    children
  });
};
var Content = ({ children }) => {
  const rowCount = useTable("Content", (state) => state.rowCount);
  const colCount = useTable("Content", (state) => state.colCount);
  const footer = useTable("Content", (state) => state.footer);
  return (0, import_jsx_runtime.jsx)(Table, {
    rowCount,
    colCount,
    footer,
    children
  });
};
var Head = ({ children }) => {
  return (0, import_jsx_runtime.jsx)(Thead, {
    children: (0, import_jsx_runtime.jsx)(Tr, {
      children
    })
  });
};
var HeaderCell = ({ name, label, sortable }) => {
  const [{ query }, setQuery] = useQueryParams();
  const sort = (query == null ? void 0 : query.sort) ?? "";
  const [sortBy, sortOrder] = sort.split(":");
  const { formatMessage } = useIntl();
  const isSorted = sortBy === name;
  const sortLabel = formatMessage({
    id: "components.TableHeader.sort",
    defaultMessage: "Sort on {label}"
  }, {
    label
  });
  const handleClickSort = () => {
    if (sortable) {
      setQuery({
        sort: `${name}:${isSorted && sortOrder === "ASC" ? "DESC" : "ASC"}`
      });
    }
  };
  return (0, import_jsx_runtime.jsx)(Th, {
    action: isSorted && sortable && (0, import_jsx_runtime.jsx)(IconButton, {
      label: sortLabel,
      onClick: handleClickSort,
      variant: "ghost",
      children: (0, import_jsx_runtime.jsx)(SortIcon, {
        $isUp: sortOrder === "ASC"
      })
    }),
    children: (0, import_jsx_runtime.jsx)(TooltipImpl, {
      label: sortable ? sortLabel : label,
      children: (0, import_jsx_runtime.jsx)(Typography, {
        textColor: "neutral600",
        tag: !isSorted && sortable ? "button" : "span",
        onClick: handleClickSort,
        variant: "sigma",
        children: label
      })
    })
  });
};
var SortIcon = dt(ForwardRef$4T)`
  transform: ${({ $isUp }) => `rotate(${$isUp ? "180" : "0"}deg)`};
`;
var ActionBar = ({ children }) => {
  const { formatMessage } = useIntl();
  const selectedRows = useTable("ActionBar", (state) => state.selectedRows);
  if (selectedRows.length === 0) return null;
  return (0, import_jsx_runtime.jsxs)(Flex, {
    gap: 2,
    children: [
      (0, import_jsx_runtime.jsx)(Typography, {
        variant: "omega",
        textColor: "neutral500",
        children: formatMessage({
          id: "content-manager.components.TableDelete.label",
          defaultMessage: "{number, plural, one {# row} other {# rows}} selected"
        }, {
          number: selectedRows.length
        })
      }),
      children
    ]
  });
};
var HeaderCheckboxCell = () => {
  const rows = useTable("HeaderCheckboxCell", (state) => state.rows);
  const selectedRows = useTable("HeaderCheckboxCell", (state) => state.selectedRows);
  const selectRow = useTable("HeaderCheckboxCell", (state) => state.selectRow);
  const setHasHeaderCheckbox = useTable("HeaderCheckboxCell", (state) => state.setHasHeaderCheckbox);
  const { formatMessage } = useIntl();
  const areAllEntriesSelected = selectedRows.length === rows.length && rows.length > 0;
  const isIndeterminate = !areAllEntriesSelected && selectedRows.length > 0;
  React2.useEffect(() => {
    setHasHeaderCheckbox(true);
    return () => setHasHeaderCheckbox(false);
  }, [
    setHasHeaderCheckbox
  ]);
  const handleSelectAll = () => {
    if (!areAllEntriesSelected) {
      selectRow(rows);
    } else {
      selectRow([]);
    }
  };
  return (0, import_jsx_runtime.jsx)(Th, {
    children: (0, import_jsx_runtime.jsx)(CheckboxImpl, {
      "aria-label": formatMessage({
        id: "global.select-all-entries",
        defaultMessage: "Select all entries"
      }),
      disabled: rows.length === 0,
      checked: isIndeterminate ? "indeterminate" : areAllEntriesSelected,
      onCheckedChange: handleSelectAll
    })
  });
};
var Empty = (props) => {
  const { formatMessage } = useIntl();
  const rows = useTable("Empty", (state) => state.rows);
  const isLoading = useTable("Empty", (state) => state.isLoading);
  const colCount = useTable("Empty", (state) => state.colCount);
  if (rows.length > 0 || isLoading) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)(Tbody, {
    children: (0, import_jsx_runtime.jsx)(Tr, {
      children: (0, import_jsx_runtime.jsx)(Td, {
        colSpan: colCount,
        children: (0, import_jsx_runtime.jsx)(EmptyStateLayout, {
          content: formatMessage({
            id: "app.components.EmptyStateLayout.content-document",
            defaultMessage: "No content found"
          }),
          hasRadius: true,
          icon: (0, import_jsx_runtime.jsx)(ForwardRef$J, {
            width: "16rem"
          }),
          ...props
        })
      })
    })
  });
};
var Loading = ({ children = "Loading content" }) => {
  const isLoading = useTable("Loading", (state) => state.isLoading);
  const colCount = useTable("Loading", (state) => state.colCount);
  if (!isLoading) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)(Tbody, {
    children: (0, import_jsx_runtime.jsx)(Tr, {
      children: (0, import_jsx_runtime.jsx)(Td, {
        colSpan: colCount,
        children: (0, import_jsx_runtime.jsx)(Flex, {
          justifyContent: "center",
          padding: 11,
          background: "neutral0",
          children: (0, import_jsx_runtime.jsx)(Loader, {
            children
          })
        })
      })
    })
  });
};
var Body = ({ children }) => {
  const isLoading = useTable("Body", (state) => state.isLoading);
  const rows = useTable("Body", (state) => state.rows);
  if (isLoading || rows.length === 0) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)(Tbody, {
    children
  });
};
var Row = (props) => {
  return (0, import_jsx_runtime.jsx)(Tr, {
    ...props
  });
};
var Cell = (props) => {
  return (0, import_jsx_runtime.jsx)(Td, {
    ...props
  });
};
var CheckboxCell = ({ id, ...props }) => {
  const rows = useTable("CheckboxCell", (state) => state.rows);
  const selectedRows = useTable("CheckboxCell", (state) => state.selectedRows);
  const selectRow = useTable("CheckboxCell", (state) => state.selectRow);
  const { formatMessage } = useIntl();
  const handleSelectRow = () => {
    selectRow(rows.find((row) => row.id === id));
  };
  const isChecked = selectedRows.findIndex((row) => row.id === id) > -1;
  return (0, import_jsx_runtime.jsx)(Cell, {
    ...props,
    onClick: (e) => e.stopPropagation(),
    children: (0, import_jsx_runtime.jsx)(CheckboxImpl, {
      "aria-label": formatMessage({
        id: "app.component.table.select.one-entry",
        defaultMessage: `Select {target}`
      }, {
        target: id
      }),
      disabled: rows.length === 0,
      checked: isChecked,
      onCheckedChange: handleSelectRow
    })
  });
};
var Table2 = {
  Root,
  Content,
  ActionBar,
  Head,
  HeaderCell,
  HeaderCheckboxCell,
  Body,
  CheckboxCell,
  Cell,
  Row,
  Loading,
  Empty
};

export {
  useControllableState,
  useTable,
  Table2 as Table
};
//# sourceMappingURL=chunk-PVCRV2LE.js.map
