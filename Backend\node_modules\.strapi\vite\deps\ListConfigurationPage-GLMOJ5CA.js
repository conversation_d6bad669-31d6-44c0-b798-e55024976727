import {
  CardDragPreview
} from "./chunk-OFMRQYXG.js";
import {
  FieldTypeIcon
} from "./chunk-RTLULQCG.js";
import {
  useTypedSelector
} from "./chunk-SEUNPHZA.js";
import {
  ItemTypes,
  useDragAndDrop
} from "./chunk-2D65YUEQ.js";
import {
  COLLECTION_TYPES,
  SINGLE_TYPES,
  checkIfAttributeIsDisplayable,
  setIn,
  useDoc,
  useDocLayout,
  useGetContentTypeConfigurationQuery,
  useUpdateContentTypeConfigurationMutation
} from "./chunk-GZO3GFLN.js";
import {
  capitalise
} from "./chunk-A46TUCLT.js";
import {
  getTranslation
} from "./chunk-HIZVCZYI.js";
import "./chunk-EKXSMIUH.js";
import {
  getEmptyImage
} from "./chunk-SYWYLB7I.js";
import "./chunk-YL7CEHOK.js";
import "./chunk-YV3ONWF5.js";
import "./chunk-UMW22TSS.js";
import "./chunk-4QC3H4VA.js";
import "./chunk-75D2ZJP5.js";
import "./chunk-VCTAT6B3.js";
import "./chunk-ROZIXYJG.js";
import "./chunk-C72RZIDJ.js";
import "./chunk-HZKRK7AR.js";
import "./chunk-LRN6A2UC.js";
import "./chunk-D2TGW5YS.js";
import "./chunk-M27D4U76.js";
import "./chunk-HX66WGOY.js";
import "./chunk-Y4UEUAII.js";
import "./chunk-BN2UQHMJ.js";
import "./chunk-NWWGC2Z2.js";
import "./chunk-MBK4V2X7.js";
import "./chunk-DY2RJG3P.js";
import "./chunk-K65KIEAL.js";
import "./chunk-BUDFB33L.js";
import "./chunk-7MILHJ3J.js";
import "./chunk-SGQJOZK5.js";
import "./chunk-AFM2NWPO.js";
import {
  BackButton
} from "./chunk-DUGZ4WIW.js";
import "./chunk-IFOFBKTA.js";
import {
  create3 as create,
  create4 as create2,
  create6 as create3
} from "./chunk-376QHLWZ.js";
import "./chunk-EGNP2T5O.js";
import {
  useTracking
} from "./chunk-XDCEA27D.js";
import "./chunk-EZSYDDUK.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-QIJGNK42.js";
import "./chunk-CJHUGFLE.js";
import "./chunk-IQGHPIIW.js";
import "./chunk-DWSGKQEK.js";
import "./chunk-W6ZGCRX6.js";
import "./chunk-PVCRV2LE.js";
import {
  MemoizedInputRenderer
} from "./chunk-HWAQQGJJ.js";
import {
  Form,
  useField,
  useForm
} from "./chunk-L5JCPKMP.js";
import "./chunk-ZJEMJY2Q.js";
import "./chunk-6DRYEU2W.js";
import {
  Layouts
} from "./chunk-MTTHLNPH.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import {
  Page,
  useAPIErrorHandler
} from "./chunk-7LKLOY7A.js";
import "./chunk-ODQFI753.js";
import "./chunk-ZOP4VV6J.js";
import "./chunk-WH6VCVXU.js";
import "./chunk-IL5G2D22.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-76QM3EFM.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  Box,
  Button,
  Divider,
  Flex,
  Grid,
  Main,
  Menu,
  Modal,
  Typography,
  VisuallyHidden,
  useCollator,
  useComposedRefs,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import "./chunk-5ZC4PE57.js";
import {
  Navigate,
  useParams
} from "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$1h,
  ForwardRef$1v,
  ForwardRef$3T,
  ForwardRef$45
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/dist/admin/pages/ListConfiguration/ListConfigurationPage.mjs
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var React4 = __toESM(require_react(), 1);

// node_modules/@strapi/content-manager/dist/admin/pages/ListConfiguration/components/Header.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var Header = ({ name }) => {
  const { formatMessage } = useIntl();
  const params = useParams();
  const modified = useForm("Header", (state) => state.modified);
  const isSubmitting = useForm("Header", (state) => state.isSubmitting);
  return (0, import_jsx_runtime.jsx)(Layouts.Header, {
    navigationAction: (0, import_jsx_runtime.jsx)(BackButton, {
      fallback: `../${COLLECTION_TYPES}/${params.slug}`
    }),
    primaryAction: (0, import_jsx_runtime.jsx)(Button, {
      size: "S",
      disabled: !modified,
      type: "submit",
      loading: isSubmitting,
      children: formatMessage({
        id: "global.save",
        defaultMessage: "Save"
      })
    }),
    subtitle: formatMessage({
      id: getTranslation("components.SettingsViewWrapper.pluginHeader.description.list-settings"),
      defaultMessage: "Define the settings of the list view."
    }),
    title: formatMessage({
      id: getTranslation("components.SettingsViewWrapper.pluginHeader.title"),
      defaultMessage: "Configure the view - {name}"
    }, {
      name: capitalise(name)
    })
  });
};

// node_modules/@strapi/content-manager/dist/admin/pages/ListConfiguration/components/Settings.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var EXCLUDED_SORT_ATTRIBUTE_TYPES = [
  "media",
  "richtext",
  "dynamiczone",
  "relation",
  "component",
  "json",
  "blocks"
];
var Settings = () => {
  const { formatMessage, locale } = useIntl();
  const formatter = useCollator(locale, {
    sensitivity: "base"
  });
  const { schema } = useDoc();
  const layout = useForm("Settings", (state) => state.values.layout ?? []);
  const currentSortBy = useForm("Settings", (state) => state.values.settings.defaultSortBy);
  const onChange = useForm("Settings", (state) => state.onChange);
  const sortOptions = React.useMemo(() => Object.values(layout).reduce((acc, field) => {
    if (schema && !EXCLUDED_SORT_ATTRIBUTE_TYPES.includes(schema.attributes[field.name].type)) {
      acc.push({
        value: field.name,
        label: typeof field.label !== "string" ? formatMessage(field.label) : field.label
      });
    }
    return acc;
  }, []), [
    formatMessage,
    layout,
    schema
  ]);
  const sortOptionsSorted = sortOptions.sort((a, b) => formatter.compare(a.label, b.label));
  React.useEffect(() => {
    var _a;
    if (sortOptionsSorted.findIndex((opt) => opt.value === currentSortBy) === -1) {
      onChange("settings.defaultSortBy", (_a = sortOptionsSorted[0]) == null ? void 0 : _a.value);
    }
  }, [
    currentSortBy,
    onChange,
    sortOptionsSorted
  ]);
  const formLayout = React.useMemo(() => SETTINGS_FORM_LAYOUT.map((row) => row.map((field) => {
    if (field.type === "enumeration") {
      return {
        ...field,
        hint: field.hint ? formatMessage(field.hint) : void 0,
        label: formatMessage(field.label),
        options: field.name === "settings.defaultSortBy" ? sortOptionsSorted : field.options
      };
    } else {
      return {
        ...field,
        hint: field.hint ? formatMessage(field.hint) : void 0,
        label: formatMessage(field.label)
      };
    }
  })), [
    formatMessage,
    sortOptionsSorted
  ]);
  return (0, import_jsx_runtime2.jsxs)(Flex, {
    direction: "column",
    alignItems: "stretch",
    gap: 4,
    children: [
      (0, import_jsx_runtime2.jsx)(Typography, {
        variant: "delta",
        tag: "h2",
        children: formatMessage({
          id: getTranslation("containers.SettingPage.settings"),
          defaultMessage: "Settings"
        })
      }),
      (0, import_jsx_runtime2.jsx)(Grid.Root, {
        gap: 4,
        children: formLayout.map((row) => row.map(({ size, ...field }) => (0, import_jsx_runtime2.jsx)(Grid.Item, {
          s: 12,
          col: size,
          direction: "column",
          alignItems: "stretch",
          children: (0, import_jsx_runtime2.jsx)(MemoizedInputRenderer, {
            ...field
          })
        }, field.name)))
      }, "bottom")
    ]
  });
};
var SETTINGS_FORM_LAYOUT = [
  [
    {
      label: {
        id: getTranslation("form.Input.search"),
        defaultMessage: "Enable search"
      },
      name: "settings.searchable",
      size: 4,
      type: "boolean"
    },
    {
      label: {
        id: getTranslation("form.Input.filters"),
        defaultMessage: "Enable filters"
      },
      name: "settings.filterable",
      size: 4,
      type: "boolean"
    },
    {
      label: {
        id: getTranslation("form.Input.bulkActions"),
        defaultMessage: "Enable bulk actions"
      },
      name: "settings.bulkable",
      size: 4,
      type: "boolean"
    }
  ],
  [
    {
      hint: {
        id: getTranslation("form.Input.pageEntries.inputDescription"),
        defaultMessage: "Note: You can override this value in the Collection Type settings page."
      },
      label: {
        id: getTranslation("form.Input.pageEntries"),
        defaultMessage: "Entries per page"
      },
      name: "settings.pageSize",
      options: [
        "10",
        "20",
        "50",
        "100"
      ].map((value) => ({
        value,
        label: value
      })),
      size: 6,
      type: "enumeration"
    },
    {
      label: {
        id: getTranslation("form.Input.defaultSort"),
        defaultMessage: "Default sort attribute"
      },
      name: "settings.defaultSortBy",
      options: [],
      size: 3,
      type: "enumeration"
    },
    {
      label: {
        id: getTranslation("form.Input.sort.order"),
        defaultMessage: "Default sort order"
      },
      name: "settings.defaultSortOrder",
      options: [
        "ASC",
        "DESC"
      ].map((value) => ({
        value,
        label: value
      })),
      size: 3,
      type: "enumeration"
    }
  ]
];

// node_modules/@strapi/content-manager/dist/admin/pages/ListConfiguration/components/SortDisplayedFields.mjs
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var React3 = __toESM(require_react(), 1);

// node_modules/@strapi/content-manager/dist/admin/pages/ListConfiguration/components/DraggableCard.mjs
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var React2 = __toESM(require_react(), 1);

// node_modules/@strapi/content-manager/dist/admin/pages/ListConfiguration/components/EditFieldForm.mjs
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var FIELD_SCHEMA = create3().shape({
  label: create2().required(),
  sortable: create()
});
var EditFieldForm = ({ attribute, name, onClose }) => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { value, onChange } = useField(name);
  if (!value) {
    console.error("You've opened a field to edit without it being part of the form, this is likely a bug with Strapi. Please open an issue.");
    toggleNotification({
      message: formatMessage({
        id: "content-manager.containers.list-settings.modal-form.error",
        defaultMessage: "An error occurred while trying to open the form."
      }),
      type: "danger"
    });
    return null;
  }
  let shouldDisplaySortToggle = ![
    "media",
    "relation"
  ].includes(attribute.type);
  if ("relation" in attribute && [
    "oneWay",
    "oneToOne",
    "manyToOne"
  ].includes(attribute.relation)) {
    shouldDisplaySortToggle = true;
  }
  return (0, import_jsx_runtime3.jsx)(Modal.Content, {
    children: (0, import_jsx_runtime3.jsxs)(Form, {
      method: "PUT",
      initialValues: value,
      validationSchema: FIELD_SCHEMA,
      onSubmit: (data) => {
        onChange(name, data);
        onClose();
      },
      children: [
        (0, import_jsx_runtime3.jsx)(Modal.Header, {
          children: (0, import_jsx_runtime3.jsxs)(HeaderContainer, {
            children: [
              (0, import_jsx_runtime3.jsx)(FieldTypeIcon, {
                type: attribute.type
              }),
              (0, import_jsx_runtime3.jsx)(Modal.Title, {
                children: formatMessage({
                  id: getTranslation("containers.list-settings.modal-form.label"),
                  defaultMessage: "Edit {fieldName}"
                }, {
                  fieldName: capitalise(value.label)
                })
              })
            ]
          })
        }),
        (0, import_jsx_runtime3.jsx)(Modal.Body, {
          children: (0, import_jsx_runtime3.jsx)(Grid.Root, {
            gap: 4,
            children: [
              {
                name: "label",
                label: formatMessage({
                  id: getTranslation("form.Input.label"),
                  defaultMessage: "Label"
                }),
                hint: formatMessage({
                  id: getTranslation("form.Input.label.inputDescription"),
                  defaultMessage: "This value overrides the label displayed in the table's head"
                }),
                size: 6,
                type: "string"
              },
              {
                label: formatMessage({
                  id: getTranslation("form.Input.sort.field"),
                  defaultMessage: "Enable sort on this field"
                }),
                name: "sortable",
                size: 6,
                type: "boolean"
              }
            ].filter((field) => field.name !== "sortable" || field.name === "sortable" && shouldDisplaySortToggle).map(({ size, ...field }) => (0, import_jsx_runtime3.jsx)(Grid.Item, {
              s: 12,
              col: size,
              direction: "column",
              alignItems: "stretch",
              children: (0, import_jsx_runtime3.jsx)(MemoizedInputRenderer, {
                ...field
              })
            }, field.name))
          })
        }),
        (0, import_jsx_runtime3.jsxs)(Modal.Footer, {
          children: [
            (0, import_jsx_runtime3.jsx)(Button, {
              onClick: onClose,
              variant: "tertiary",
              children: formatMessage({
                id: "app.components.Button.cancel",
                defaultMessage: "Cancel"
              })
            }),
            (0, import_jsx_runtime3.jsx)(Button, {
              type: "submit",
              children: formatMessage({
                id: "global.finish",
                defaultMessage: "Finish"
              })
            })
          ]
        })
      ]
    })
  });
};
var HeaderContainer = dt(Flex)`
  svg {
    width: 3.2rem;
    margin-right: ${({ theme }) => theme.spaces[3]};
  }
`;

// node_modules/@strapi/content-manager/dist/admin/pages/ListConfiguration/components/DraggableCard.mjs
var DraggableCard = ({ attribute, index, isDraggingSibling, label, name, onMoveField, onRemoveField, setIsDraggingSibling }) => {
  const [isModalOpen, setIsModalOpen] = React2.useState(false);
  const { formatMessage } = useIntl();
  const [, forceRerenderAfterDnd] = React2.useState(false);
  const [{ isDragging }, objectRef, dropRef, dragRef, dragPreviewRef] = useDragAndDrop(true, {
    type: ItemTypes.FIELD,
    item: {
      index,
      label,
      name
    },
    index,
    onMoveItem: onMoveField,
    onEnd: () => setIsDraggingSibling(false)
  });
  React2.useEffect(() => {
    dragPreviewRef(getEmptyImage(), {
      captureDraggingState: false
    });
  }, [
    dragPreviewRef
  ]);
  React2.useEffect(() => {
    if (isDragging) {
      setIsDraggingSibling(true);
    }
  }, [
    isDragging,
    setIsDraggingSibling
  ]);
  React2.useEffect(() => {
    if (!isDraggingSibling) {
      forceRerenderAfterDnd((prev) => !prev);
    }
  }, [
    isDraggingSibling
  ]);
  const composedRefs = useComposedRefs(dropRef, objectRef);
  return (0, import_jsx_runtime4.jsxs)(FieldWrapper, {
    ref: composedRefs,
    children: [
      isDragging && (0, import_jsx_runtime4.jsx)(CardDragPreview, {
        label
      }),
      !isDragging && isDraggingSibling && (0, import_jsx_runtime4.jsx)(CardDragPreview, {
        isSibling: true,
        label
      }),
      !isDragging && !isDraggingSibling && (0, import_jsx_runtime4.jsxs)(FieldContainer, {
        borderColor: "neutral150",
        background: "neutral100",
        hasRadius: true,
        justifyContent: "space-between",
        onClick: () => setIsModalOpen(true),
        children: [
          (0, import_jsx_runtime4.jsxs)(Flex, {
            gap: 3,
            children: [
              (0, import_jsx_runtime4.jsx)(DragButton, {
                ref: dragRef,
                "aria-label": formatMessage({
                  id: getTranslation("components.DraggableCard.move.field"),
                  defaultMessage: "Move {item}"
                }, {
                  item: label
                }),
                onClick: (e) => e.stopPropagation(),
                children: (0, import_jsx_runtime4.jsx)(ForwardRef$3T, {})
              }),
              (0, import_jsx_runtime4.jsx)(Typography, {
                fontWeight: "bold",
                children: label
              })
            ]
          }),
          (0, import_jsx_runtime4.jsxs)(Flex, {
            paddingLeft: 3,
            onClick: (e) => e.stopPropagation(),
            children: [
              (0, import_jsx_runtime4.jsxs)(Modal.Root, {
                open: isModalOpen,
                onOpenChange: setIsModalOpen,
                children: [
                  (0, import_jsx_runtime4.jsx)(Modal.Trigger, {
                    children: (0, import_jsx_runtime4.jsx)(ActionButton, {
                      onClick: (e) => {
                        e.stopPropagation();
                      },
                      "aria-label": formatMessage({
                        id: getTranslation("components.DraggableCard.edit.field"),
                        defaultMessage: "Edit {item}"
                      }, {
                        item: label
                      }),
                      type: "button",
                      children: (0, import_jsx_runtime4.jsx)(ForwardRef$1v, {
                        width: "1.2rem",
                        height: "1.2rem"
                      })
                    })
                  }),
                  (0, import_jsx_runtime4.jsx)(EditFieldForm, {
                    attribute,
                    name: `layout.${index}`,
                    onClose: () => {
                      setIsModalOpen(false);
                    }
                  })
                ]
              }),
              (0, import_jsx_runtime4.jsx)(ActionButton, {
                onClick: onRemoveField,
                "data-testid": `delete-${name}`,
                "aria-label": formatMessage({
                  id: getTranslation("components.DraggableCard.delete.field"),
                  defaultMessage: "Delete {item}"
                }, {
                  item: label
                }),
                type: "button",
                children: (0, import_jsx_runtime4.jsx)(ForwardRef$45, {
                  width: "1.2rem",
                  height: "1.2rem"
                })
              })
            ]
          })
        ]
      })
    ]
  });
};
var ActionButton = dt.button`
  display: flex;
  align-items: center;
  height: ${({ theme }) => theme.spaces[7]};
  color: ${({ theme }) => theme.colors.neutral600};

  &:hover {
    color: ${({ theme }) => theme.colors.neutral700};
  }

  &:last-child {
    padding: 0 ${({ theme }) => theme.spaces[3]};
  }
`;
var DragButton = dt(ActionButton)`
  padding: 0 ${({ theme }) => theme.spaces[3]};
  border-right: 1px solid ${({ theme }) => theme.colors.neutral150};
  cursor: all-scroll;
`;
var FieldContainer = dt(Flex)`
  max-height: 3.2rem;
  cursor: pointer;
`;
var FieldWrapper = dt(Box)`
  &:last-child {
    padding-right: ${({ theme }) => theme.spaces[3]};
  }
`;

// node_modules/@strapi/content-manager/dist/admin/pages/ListConfiguration/components/SortDisplayedFields.mjs
var SortDisplayedFields = () => {
  const { formatMessage } = useIntl();
  const { model, schema } = useDoc();
  const [isDraggingSibling, setIsDraggingSibling] = React3.useState(false);
  const [lastAction, setLastAction] = React3.useState(null);
  const scrollableContainerRef = React3.useRef(null);
  const values = useForm("SortDisplayedFields", (state) => state.values.layout ?? []);
  const addFieldRow = useForm("SortDisplayedFields", (state) => state.addFieldRow);
  const removeFieldRow = useForm("SortDisplayedFields", (state) => state.removeFieldRow);
  const moveFieldRow = useForm("SortDisplayedFields", (state) => state.moveFieldRow);
  const { metadata: allMetadata } = useGetContentTypeConfigurationQuery(model, {
    selectFromResult: ({ data }) => ({
      metadata: (data == null ? void 0 : data.contentType.metadatas) ?? {}
    })
  });
  const nonDisplayedFields = React3.useMemo(() => {
    if (!schema) {
      return [];
    }
    const displayedFieldNames = values.map((field) => field.name);
    return Object.entries(schema.attributes).reduce((acc, [name, attribute]) => {
      if (!displayedFieldNames.includes(name) && checkIfAttributeIsDisplayable(attribute)) {
        const { list: metadata } = allMetadata[name];
        acc.push({
          name,
          label: metadata.label || name,
          sortable: metadata.sortable
        });
      }
      return acc;
    }, []);
  }, [
    allMetadata,
    values,
    schema
  ]);
  const handleAddField = (field) => {
    setLastAction("add");
    addFieldRow("layout", field);
  };
  const handleRemoveField = (index) => {
    setLastAction("remove");
    removeFieldRow("layout", index);
  };
  const handleMoveField = (dragIndex, hoverIndex) => {
    moveFieldRow("layout", dragIndex, hoverIndex);
  };
  React3.useEffect(() => {
    if (lastAction === "add" && (scrollableContainerRef == null ? void 0 : scrollableContainerRef.current)) {
      scrollableContainerRef.current.scrollLeft = scrollableContainerRef.current.scrollWidth;
    }
  }, [
    lastAction
  ]);
  return (0, import_jsx_runtime5.jsxs)(Flex, {
    alignItems: "stretch",
    direction: "column",
    gap: 4,
    children: [
      (0, import_jsx_runtime5.jsx)(Typography, {
        variant: "delta",
        tag: "h2",
        children: formatMessage({
          id: getTranslation("containers.SettingPage.view"),
          defaultMessage: "View"
        })
      }),
      (0, import_jsx_runtime5.jsxs)(Flex, {
        padding: 4,
        borderColor: "neutral300",
        borderStyle: "dashed",
        borderWidth: "1px",
        hasRadius: true,
        children: [
          (0, import_jsx_runtime5.jsx)(Box, {
            flex: "1",
            overflow: "auto hidden",
            ref: scrollableContainerRef,
            children: (0, import_jsx_runtime5.jsx)(Flex, {
              gap: 3,
              children: values.map((field, index) => (0, import_jsx_runtime5.jsx)(DraggableCard, {
                index,
                isDraggingSibling,
                onMoveField: handleMoveField,
                onRemoveField: () => handleRemoveField(index),
                setIsDraggingSibling,
                ...field,
                attribute: schema.attributes[field.name],
                label: typeof field.label === "object" ? formatMessage(field.label) : field.label
              }, field.name))
            })
          }),
          (0, import_jsx_runtime5.jsxs)(Menu.Root, {
            children: [
              (0, import_jsx_runtime5.jsxs)(Menu.Trigger, {
                paddingLeft: 2,
                paddingRight: 2,
                justifyContent: "center",
                endIcon: null,
                disabled: nonDisplayedFields.length === 0,
                variant: "tertiary",
                children: [
                  (0, import_jsx_runtime5.jsx)(VisuallyHidden, {
                    tag: "span",
                    children: formatMessage({
                      id: getTranslation("components.FieldSelect.label"),
                      defaultMessage: "Add a field"
                    })
                  }),
                  (0, import_jsx_runtime5.jsx)(ForwardRef$1h, {
                    "aria-hidden": true,
                    focusable: false,
                    style: {
                      position: "relative",
                      top: 2
                    }
                  })
                ]
              }),
              (0, import_jsx_runtime5.jsx)(Menu.Content, {
                children: nonDisplayedFields.map((field) => (0, import_jsx_runtime5.jsx)(Menu.Item, {
                  onSelect: () => handleAddField(field),
                  children: typeof field.label === "object" ? formatMessage(field.label) : field.label
                }, field.name))
              })
            ]
          })
        ]
      })
    ]
  });
};

// node_modules/@strapi/content-manager/dist/admin/pages/ListConfiguration/ListConfigurationPage.mjs
var ListConfiguration = () => {
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const { model, collectionType } = useDoc();
  const { isLoading: isLoadingLayout, list, edit } = useDocLayout();
  const [updateContentTypeConfiguration] = useUpdateContentTypeConfigurationMutation();
  const handleSubmit = async (data) => {
    try {
      trackUsage("willSaveContentTypeLayout");
      const layoutData = data.layout ?? [];
      const meta = Object.entries(edit.metadatas).reduce((acc, [name, editMeta]) => {
        const { mainField: _mainField, ...listMeta } = list.metadatas[name];
        const { label, sortable } = layoutData.find((field) => field.name === name) ?? {};
        acc[name] = {
          edit: editMeta,
          list: {
            ...listMeta,
            label: label || listMeta.label,
            sortable: sortable || listMeta.sortable
          }
        };
        return acc;
      }, {});
      const res = await updateContentTypeConfiguration({
        layouts: {
          edit: edit.layout.flatMap((panel) => panel.map((row) => row.map(({ name, size }) => ({
            name,
            size
          })))),
          list: layoutData.map((field) => field.name)
        },
        settings: setIn(data.settings, "displayName", void 0),
        metadatas: meta,
        uid: model
      });
      if ("data" in res) {
        trackUsage("didEditListSettings");
        toggleNotification({
          type: "success",
          message: formatMessage({
            id: "notification.success.saved",
            defaultMessage: "Saved"
          })
        });
      } else {
        toggleNotification({
          type: "danger",
          message: formatAPIError(res.error)
        });
      }
    } catch (err) {
      console.error(err);
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "An error occurred"
        })
      });
    }
  };
  const initialValues = React4.useMemo(() => {
    return {
      layout: list.layout.map(({ label, sortable, name }) => ({
        label: typeof label === "string" ? label : formatMessage(label),
        sortable,
        name
      })),
      settings: list.settings
    };
  }, [
    formatMessage,
    list.layout,
    list.settings
  ]);
  if (collectionType === SINGLE_TYPES) {
    return (0, import_jsx_runtime6.jsx)(Navigate, {
      to: `/single-types/${model}`
    });
  }
  if (isLoadingLayout) {
    return (0, import_jsx_runtime6.jsx)(Page.Loading, {});
  }
  return (0, import_jsx_runtime6.jsxs)(Layouts.Root, {
    children: [
      (0, import_jsx_runtime6.jsx)(Page.Title, {
        children: `Configure ${list.settings.displayName} List View`
      }),
      (0, import_jsx_runtime6.jsx)(Main, {
        children: (0, import_jsx_runtime6.jsxs)(Form, {
          initialValues,
          onSubmit: handleSubmit,
          method: "PUT",
          children: [
            (0, import_jsx_runtime6.jsx)(Header, {
              collectionType,
              model,
              name: list.settings.displayName ?? ""
            }),
            (0, import_jsx_runtime6.jsx)(Layouts.Content, {
              children: (0, import_jsx_runtime6.jsxs)(Flex, {
                alignItems: "stretch",
                background: "neutral0",
                direction: "column",
                gap: 6,
                hasRadius: true,
                shadow: "tableShadow",
                paddingTop: 6,
                paddingBottom: 6,
                paddingLeft: 7,
                paddingRight: 7,
                children: [
                  (0, import_jsx_runtime6.jsx)(Settings, {}),
                  (0, import_jsx_runtime6.jsx)(Divider, {}),
                  (0, import_jsx_runtime6.jsx)(SortDisplayedFields, {})
                ]
              })
            })
          ]
        })
      })
    ]
  });
};
var ProtectedListConfiguration = () => {
  const permissions = useTypedSelector((state) => {
    var _a;
    return (_a = state.admin_app.permissions.contentManager) == null ? void 0 : _a.collectionTypesConfigurations;
  });
  return (0, import_jsx_runtime6.jsx)(Page.Protect, {
    permissions,
    children: (0, import_jsx_runtime6.jsx)(ListConfiguration, {})
  });
};
export {
  ListConfiguration,
  ProtectedListConfiguration
};
//# sourceMappingURL=ListConfigurationPage-GLMOJ5CA.js.map
