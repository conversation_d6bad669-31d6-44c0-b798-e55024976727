import {
  require_prop_types
} from "./chunk-ZYDILIPQ.js";
import "./chunk-3P5SDLSO.js";
import "./chunk-2GFELOUK.js";
import "./chunk-NX5ETKE4.js";
import "./chunk-ZHZ667KP.js";
import "./chunk-K2IFNJMI.js";
import "./chunk-HPPKX7ND.js";
import "./chunk-DLRJHTTW.js";
import "./chunk-5ELMBTTI.js";
import "./chunk-6T62IZSE.js";
import "./chunk-MEKEEUV3.js";
import "./chunk-PDGPTUUZ.js";
import "./chunk-TGZYNOOA.js";
import "./chunk-6NZ2ITH7.js";
import "./chunk-6OM5ZNLL.js";
import "./chunk-4LPIPK4X.js";
import "./chunk-2D65YUEQ.js";
import "./chunk-QBVVXK5Q.js";
import "./chunk-GZO3GFLN.js";
import "./chunk-A46TUCLT.js";
import "./chunk-HIZVCZYI.js";
import "./chunk-PPYENV7T.js";
import "./chunk-6LY4MOO2.js";
import "./chunk-H4GPAAVJ.js";
import "./chunk-EKXSMIUH.js";
import "./chunk-C7H2BX76.js";
import "./chunk-SYWYLB7I.js";
import "./chunk-YL7CEHOK.js";
import "./chunk-YV3ONWF5.js";
import "./chunk-UMW22TSS.js";
import "./chunk-4QC3H4VA.js";
import "./chunk-XNACAI67.js";
import {
  useMutation,
  useQuery,
  useQueryClient
} from "./chunk-75D2ZJP5.js";
import "./chunk-VCTAT6B3.js";
import "./chunk-ROZIXYJG.js";
import "./chunk-53OG7EL5.js";
import "./chunk-C72RZIDJ.js";
import "./chunk-HZKRK7AR.js";
import "./chunk-LRN6A2UC.js";
import "./chunk-D2TGW5YS.js";
import "./chunk-M27D4U76.js";
import "./chunk-HX66WGOY.js";
import {
  useFetchClient
} from "./chunk-Y4UEUAII.js";
import "./chunk-BN2UQHMJ.js";
import "./chunk-NWWGC2Z2.js";
import "./chunk-DD3MYA7D.js";
import "./chunk-O5ZNSDDU.js";
import "./chunk-MBK4V2X7.js";
import {
  require_isEmpty
} from "./chunk-YJEURQPS.js";
import "./chunk-DY2RJG3P.js";
import "./chunk-GGK2TLCV.js";
import "./chunk-K65KIEAL.js";
import "./chunk-BUDFB33L.js";
import "./chunk-7MILHJ3J.js";
import "./chunk-SGQJOZK5.js";
import "./chunk-AFM2NWPO.js";
import "./chunk-DUGZ4WIW.js";
import {
  errorsTrads
} from "./chunk-IFOFBKTA.js";
import {
  create4 as create,
  create6 as create2
} from "./chunk-376QHLWZ.js";
import "./chunk-EGNP2T5O.js";
import {
  useTracking
} from "./chunk-XDCEA27D.js";
import "./chunk-EZSYDDUK.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-QIJGNK42.js";
import "./chunk-CJHUGFLE.js";
import "./chunk-IQGHPIIW.js";
import "./chunk-DWSGKQEK.js";
import "./chunk-W6ZGCRX6.js";
import "./chunk-PVCRV2LE.js";
import {
  MemoizedInputRenderer
} from "./chunk-HWAQQGJJ.js";
import {
  Form
} from "./chunk-L5JCPKMP.js";
import {
  useRBAC
} from "./chunk-ZJEMJY2Q.js";
import "./chunk-D4WYVNVM.js";
import "./chunk-MMOBCIZG.js";
import "./chunk-6DRYEU2W.js";
import {
  Layouts
} from "./chunk-MTTHLNPH.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import {
  Page,
  useAPIErrorHandler
} from "./chunk-7LKLOY7A.js";
import "./chunk-ODQFI753.js";
import "./chunk-ZOP4VV6J.js";
import "./chunk-WH6VCVXU.js";
import "./chunk-IL5G2D22.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-76QM3EFM.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  Box,
  Breadcrumbs,
  Button,
  Crumb,
  Grid,
  IconButton,
  Modal,
  Table,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
  Typography,
  VisuallyHidden,
  useIntl,
  useNotifyAT
} from "./chunk-7GC3Y62Q.js";
import "./chunk-5ZC4PE57.js";
import "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$1v,
  ForwardRef$4F,
  ForwardRef$5n
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  PERMISSIONS,
  getTrad
} from "./chunk-DJJSG3NG.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/plugin-users-permissions/dist/admin/pages/EmailTemplates/index.mjs
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_isEmpty3 = __toESM(require_isEmpty(), 1);

// node_modules/@strapi/plugin-users-permissions/dist/admin/pages/EmailTemplates/components/EmailForm.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_prop_types = __toESM(require_prop_types(), 1);
var import_isEmpty = __toESM(require_isEmpty(), 1);

// node_modules/@strapi/plugin-users-permissions/dist/admin/pages/EmailTemplates/utils/schema.mjs
var schema = create2().shape({
  options: create2().shape({
    from: create2().shape({
      name: create().required({
        id: errorsTrads.required.id,
        defaultMessage: "This field is required"
      }),
      email: create().email(errorsTrads.email).required({
        id: errorsTrads.required.id,
        defaultMessage: "This field is required"
      })
    }).required(),
    response_email: create().email(errorsTrads.email),
    object: create().required({
      id: errorsTrads.required.id,
      defaultMessage: "This field is required"
    }),
    message: create().required({
      id: errorsTrads.required.id,
      defaultMessage: "This field is required"
    })
  }).required(errorsTrads.required.id)
});

// node_modules/@strapi/plugin-users-permissions/dist/admin/pages/EmailTemplates/components/EmailForm.mjs
var EmailForm = ({ template = {}, onToggle, open, onSubmit }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsx)(Modal.Root, {
    open,
    onOpenChange: onToggle,
    children: (0, import_jsx_runtime.jsxs)(Modal.Content, {
      children: [
        (0, import_jsx_runtime.jsxs)(Modal.Header, {
          children: [
            (0, import_jsx_runtime.jsxs)(Breadcrumbs, {
              label: `${formatMessage({
                id: getTrad("PopUpForm.header.edit.email-templates"),
                defaultMessage: "Edit email template"
              })}, ${template.display ? formatMessage({
                id: getTrad(template.display),
                defaultMessage: template.display
              }) : ""}`,
              children: [
                (0, import_jsx_runtime.jsx)(Crumb, {
                  children: formatMessage({
                    id: getTrad("PopUpForm.header.edit.email-templates"),
                    defaultMessage: "Edit email template"
                  })
                }),
                (0, import_jsx_runtime.jsx)(Crumb, {
                  isCurrent: true,
                  children: template.display ? formatMessage({
                    id: getTrad(template.display),
                    defaultMessage: template.display
                  }) : ""
                })
              ]
            }),
            (0, import_jsx_runtime.jsx)(VisuallyHidden, {
              children: (0, import_jsx_runtime.jsx)(Modal.Title, {
                children: `${formatMessage({
                  id: getTrad("PopUpForm.header.edit.email-templates"),
                  defaultMessage: "Edit email template"
                })}, ${template.display ? formatMessage({
                  id: getTrad(template.display),
                  defaultMessage: template.display
                }) : ""}`
              })
            })
          ]
        }),
        (0, import_jsx_runtime.jsx)(Form, {
          onSubmit,
          initialValues: template,
          validationSchema: schema,
          children: ({ isSubmitting }) => {
            return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, {
              children: [
                (0, import_jsx_runtime.jsx)(Modal.Body, {
                  children: (0, import_jsx_runtime.jsx)(Grid.Root, {
                    gap: 5,
                    children: [
                      {
                        label: formatMessage({
                          id: getTrad("PopUpForm.Email.options.from.name.label"),
                          defaultMessage: "Shipper name"
                        }),
                        name: "options.from.name",
                        size: 6,
                        type: "string"
                      },
                      {
                        label: formatMessage({
                          id: getTrad("PopUpForm.Email.options.from.email.label"),
                          defaultMessage: "Shipper email"
                        }),
                        name: "options.from.email",
                        size: 6,
                        type: "string"
                      },
                      {
                        label: formatMessage({
                          id: getTrad("PopUpForm.Email.options.response_email.label"),
                          defaultMessage: "Response email"
                        }),
                        name: "options.response_email",
                        size: 6,
                        type: "string"
                      },
                      {
                        label: formatMessage({
                          id: getTrad("PopUpForm.Email.options.object.label"),
                          defaultMessage: "Subject"
                        }),
                        name: "options.object",
                        size: 6,
                        type: "string"
                      },
                      {
                        label: formatMessage({
                          id: getTrad("PopUpForm.Email.options.message.label"),
                          defaultMessage: "Message"
                        }),
                        name: "options.message",
                        size: 12,
                        type: "text"
                      }
                    ].map(({ size, ...field }) => (0, import_jsx_runtime.jsx)(Grid.Item, {
                      col: size,
                      direction: "column",
                      alignItems: "stretch",
                      children: (0, import_jsx_runtime.jsx)(MemoizedInputRenderer, {
                        ...field
                      })
                    }, field.name))
                  })
                }),
                (0, import_jsx_runtime.jsxs)(Modal.Footer, {
                  children: [
                    (0, import_jsx_runtime.jsx)(Modal.Close, {
                      children: (0, import_jsx_runtime.jsx)(Button, {
                        variant: "tertiary",
                        children: "Cancel"
                      })
                    }),
                    (0, import_jsx_runtime.jsx)(Button, {
                      loading: isSubmitting,
                      type: "submit",
                      children: "Finish"
                    })
                  ]
                })
              ]
            });
          }
        })
      ]
    })
  });
};
EmailForm.defaultProps = {
  template: {}
};
EmailForm.propTypes = {
  template: import_prop_types.default.shape({
    display: import_prop_types.default.string,
    icon: import_prop_types.default.string,
    options: import_prop_types.default.shape({
      from: import_prop_types.default.shape({
        name: import_prop_types.default.string,
        email: import_prop_types.default.string
      }),
      message: import_prop_types.default.string,
      object: import_prop_types.default.string,
      response_email: import_prop_types.default.string
    })
  }),
  open: import_prop_types.default.bool.isRequired,
  onSubmit: import_prop_types.default.func.isRequired,
  onToggle: import_prop_types.default.func.isRequired
};

// node_modules/@strapi/plugin-users-permissions/dist/admin/pages/EmailTemplates/components/EmailTable.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_prop_types2 = __toESM(require_prop_types(), 1);
var import_isEmpty2 = __toESM(require_isEmpty(), 1);
var EmailTable = ({ canUpdate, onEditClick }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime2.jsxs)(Table, {
    colCount: 3,
    rowCount: 3,
    children: [
      (0, import_jsx_runtime2.jsx)(Thead, {
        children: (0, import_jsx_runtime2.jsxs)(Tr, {
          children: [
            (0, import_jsx_runtime2.jsx)(Th, {
              width: "1%",
              children: (0, import_jsx_runtime2.jsx)(VisuallyHidden, {
                children: formatMessage({
                  id: getTrad("Email.template.table.icon.label"),
                  defaultMessage: "icon"
                })
              })
            }),
            (0, import_jsx_runtime2.jsx)(Th, {
              children: (0, import_jsx_runtime2.jsx)(Typography, {
                variant: "sigma",
                textColor: "neutral600",
                children: formatMessage({
                  id: getTrad("Email.template.table.name.label"),
                  defaultMessage: "name"
                })
              })
            }),
            (0, import_jsx_runtime2.jsx)(Th, {
              width: "1%",
              children: (0, import_jsx_runtime2.jsx)(VisuallyHidden, {
                children: formatMessage({
                  id: getTrad("Email.template.table.action.label"),
                  defaultMessage: "action"
                })
              })
            })
          ]
        })
      }),
      (0, import_jsx_runtime2.jsxs)(Tbody, {
        children: [
          (0, import_jsx_runtime2.jsxs)(Tr, {
            cursor: "pointer",
            onClick: () => onEditClick("reset_password"),
            children: [
              (0, import_jsx_runtime2.jsx)(Td, {
                children: (0, import_jsx_runtime2.jsx)(Box, {
                  width: "3.2rem",
                  height: "3.2rem",
                  padding: "0.8rem",
                  children: (0, import_jsx_runtime2.jsx)(ForwardRef$5n, {
                    "aria-label": formatMessage({
                      id: "global.reset-password",
                      defaultMessage: "Reset password"
                    })
                  })
                })
              }),
              (0, import_jsx_runtime2.jsx)(Td, {
                children: (0, import_jsx_runtime2.jsx)(Typography, {
                  children: formatMessage({
                    id: "global.reset-password",
                    defaultMessage: "Reset password"
                  })
                })
              }),
              (0, import_jsx_runtime2.jsx)(Td, {
                onClick: (e) => e.stopPropagation(),
                children: (0, import_jsx_runtime2.jsx)(IconButton, {
                  onClick: () => onEditClick("reset_password"),
                  label: formatMessage({
                    id: getTrad("Email.template.form.edit.label"),
                    defaultMessage: "Edit a template"
                  }),
                  variant: "ghost",
                  disabled: !canUpdate,
                  children: (0, import_jsx_runtime2.jsx)(ForwardRef$1v, {})
                })
              })
            ]
          }),
          (0, import_jsx_runtime2.jsxs)(Tr, {
            cursor: "pointer",
            onClick: () => onEditClick("email_confirmation"),
            children: [
              (0, import_jsx_runtime2.jsx)(Td, {
                children: (0, import_jsx_runtime2.jsx)(Box, {
                  width: "3.2rem",
                  height: "3.2rem",
                  padding: "0.8rem",
                  children: (0, import_jsx_runtime2.jsx)(ForwardRef$4F, {
                    "aria-label": formatMessage({
                      id: getTrad("Email.template.email_confirmation"),
                      defaultMessage: "Email address confirmation"
                    })
                  })
                })
              }),
              (0, import_jsx_runtime2.jsx)(Td, {
                children: (0, import_jsx_runtime2.jsx)(Typography, {
                  children: formatMessage({
                    id: getTrad("Email.template.email_confirmation"),
                    defaultMessage: "Email address confirmation"
                  })
                })
              }),
              (0, import_jsx_runtime2.jsx)(Td, {
                onClick: (e) => e.stopPropagation(),
                children: (0, import_jsx_runtime2.jsx)(IconButton, {
                  onClick: () => onEditClick("email_confirmation"),
                  label: formatMessage({
                    id: getTrad("Email.template.form.edit.label"),
                    defaultMessage: "Edit a template"
                  }),
                  variant: "ghost",
                  disabled: !canUpdate,
                  children: (0, import_jsx_runtime2.jsx)(ForwardRef$1v, {})
                })
              })
            ]
          })
        ]
      })
    ]
  });
};
EmailTable.propTypes = {
  canUpdate: import_prop_types2.default.bool.isRequired,
  onEditClick: import_prop_types2.default.func.isRequired
};

// node_modules/@strapi/plugin-users-permissions/dist/admin/pages/EmailTemplates/index.mjs
var ProtectedEmailTemplatesPage = () => (0, import_jsx_runtime3.jsx)(Page.Protect, {
  permissions: PERMISSIONS.readEmailTemplates,
  children: (0, import_jsx_runtime3.jsx)(EmailTemplatesPage, {})
});
var EmailTemplatesPage = () => {
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const { notifyStatus } = useNotifyAT();
  const { toggleNotification } = useNotification();
  const queryClient = useQueryClient();
  const { get, put } = useFetchClient();
  const { formatAPIError } = useAPIErrorHandler();
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [templateToEdit, setTemplateToEdit] = React.useState(null);
  const { isLoading: isLoadingForPermissions, allowedActions: { canUpdate } } = useRBAC({
    update: PERMISSIONS.updateEmailTemplates
  });
  const { isLoading: isLoadingData, data } = useQuery([
    "users-permissions",
    "email-templates"
  ], async () => {
    const { data: data2 } = await get("/users-permissions/email-templates");
    return data2;
  }, {
    onSuccess() {
      notifyStatus(formatMessage({
        id: getTrad("Email.template.data.loaded"),
        defaultMessage: "Email templates has been loaded"
      }));
    },
    onError(error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    }
  });
  const isLoading = isLoadingForPermissions || isLoadingData;
  const handleToggle = () => {
    setIsModalOpen((prev) => !prev);
  };
  const handleEditClick = (template) => {
    setTemplateToEdit(template);
    handleToggle();
  };
  const submitMutation = useMutation((body) => put("/users-permissions/email-templates", {
    "email-templates": body
  }), {
    async onSuccess() {
      await queryClient.invalidateQueries([
        "users-permissions",
        "email-templates"
      ]);
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: "notification.success.saved",
          defaultMessage: "Saved"
        })
      });
      trackUsage("didEditEmailTemplates");
      handleToggle();
    },
    onError(error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    },
    refetchActive: true
  });
  const handleSubmit = (body) => {
    trackUsage("willEditEmailTemplates");
    const editedTemplates = {
      ...data,
      [templateToEdit]: body
    };
    submitMutation.mutate(editedTemplates);
  };
  if (isLoading) {
    return (0, import_jsx_runtime3.jsx)(Page.Loading, {});
  }
  return (0, import_jsx_runtime3.jsxs)(Page.Main, {
    "aria-busy": submitMutation.isLoading,
    children: [
      (0, import_jsx_runtime3.jsx)(Page.Title, {
        children: formatMessage({
          id: "Settings.PageTitle",
          defaultMessage: "Settings - {name}"
        }, {
          name: formatMessage({
            id: getTrad("HeaderNav.link.emailTemplates"),
            defaultMessage: "Email templates"
          })
        })
      }),
      (0, import_jsx_runtime3.jsx)(Layouts.Header, {
        title: formatMessage({
          id: getTrad("HeaderNav.link.emailTemplates"),
          defaultMessage: "Email templates"
        })
      }),
      (0, import_jsx_runtime3.jsxs)(Layouts.Content, {
        children: [
          (0, import_jsx_runtime3.jsx)(EmailTable, {
            onEditClick: handleEditClick,
            canUpdate
          }),
          (0, import_jsx_runtime3.jsx)(EmailForm, {
            template: data[templateToEdit],
            onToggle: handleToggle,
            open: isModalOpen,
            onSubmit: handleSubmit
          })
        ]
      })
    ]
  });
};
export {
  EmailTemplatesPage,
  ProtectedEmailTemplatesPage
};
//# sourceMappingURL=EmailTemplates-6EQ6YYKT.js.map
