{"version": 3, "sources": ["../../../@strapi/admin/admin/src/features/Notifications.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, use<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\ninterface NotificationLink {\n  label: string;\n  target?: string;\n  url: string;\n}\n\ninterface NotificationConfig {\n  blockTransition?: boolean;\n  link?: NotificationLink;\n  message?: string;\n  onClose?: () => void;\n  timeout?: number;\n  title?: string;\n  type?: 'info' | 'warning' | 'danger' | 'success';\n}\n\n/* -------------------------------------------------------------------------------------------------\n * Context\n * -----------------------------------------------------------------------------------------------*/\n\ninterface NotificationsContextValue {\n  /**\n   * Toggles a notification, wrapped in `useCallback` for a stable identity.\n   */\n  toggleNotification: (config: NotificationConfig) => void;\n}\n\nconst NotificationsContext = React.createContext<NotificationsContextValue>({\n  toggleNotification: () => {},\n});\n\n/* -------------------------------------------------------------------------------------------------\n * Provider\n * -----------------------------------------------------------------------------------------------*/\n\ninterface NotificationsProviderProps {\n  children: React.ReactNode;\n}\ninterface Notification extends NotificationConfig {\n  id: number;\n}\n\n/**\n * @internal\n * @description DO NOT USE. This will be removed before stable release of v5.\n */\nconst NotificationsProvider = ({ children }: NotificationsProviderProps) => {\n  const notificationIdRef = React.useRef(0);\n\n  const [notifications, setNotifications] = React.useState<Notification[]>([]);\n\n  const toggleNotification = React.useCallback(\n    ({ type, message, link, timeout, blockTransition, onClose, title }: NotificationConfig) => {\n      setNotifications((s) => [\n        ...s,\n        {\n          id: notificationIdRef.current++,\n          type,\n          message,\n          link,\n          timeout,\n          blockTransition,\n          onClose,\n          title,\n        },\n      ]);\n    },\n    []\n  );\n\n  const clearNotification = React.useCallback((id: number) => {\n    setNotifications((s) => s.filter((n) => n.id !== id));\n  }, []);\n\n  const value = React.useMemo(() => ({ toggleNotification }), [toggleNotification]);\n\n  return (\n    <NotificationsContext.Provider value={value}>\n      <Flex\n        left=\"50%\"\n        marginLeft=\"-250px\"\n        position=\"fixed\"\n        direction=\"column\"\n        alignItems=\"stretch\"\n        gap={2}\n        top={`4.6rem`}\n        width={`50rem`}\n        zIndex=\"notification\"\n      >\n        {notifications.map((notification) => {\n          return (\n            <Notification\n              key={notification.id}\n              {...notification}\n              clearNotification={clearNotification}\n            />\n          );\n        })}\n      </Flex>\n      {children}\n    </NotificationsContext.Provider>\n  );\n};\n\ninterface NotificationProps extends Notification {\n  clearNotification: (id: number) => void;\n}\n\nconst Notification = ({\n  clearNotification,\n  blockTransition = false,\n  id,\n  link,\n  message,\n  onClose,\n  timeout = 2500,\n  title,\n  type,\n}: NotificationProps) => {\n  const { formatMessage } = useIntl();\n  /**\n   * Chances are `onClose` won't be classed as stabilised,\n   * so we use `useCallbackRef` to avoid make it stable.\n   */\n  const onCloseCallback = useCallbackRef(onClose);\n\n  const handleClose = React.useCallback(() => {\n    onCloseCallback();\n\n    clearNotification(id);\n  }, [clearNotification, id, onCloseCallback]);\n\n  // eslint-disable-next-line consistent-return\n  React.useEffect(() => {\n    if (!blockTransition) {\n      const timeoutReference = setTimeout(() => {\n        handleClose();\n      }, timeout);\n\n      return () => {\n        clearTimeout(timeoutReference);\n      };\n    }\n  }, [blockTransition, handleClose, timeout]);\n\n  let variant: AlertVariant;\n  let alertTitle: string;\n\n  if (type === 'info') {\n    variant = 'default';\n    alertTitle = formatMessage({\n      id: 'notification.default.title',\n      defaultMessage: 'Information:',\n    });\n  } else if (type === 'danger') {\n    variant = 'danger';\n    alertTitle = formatMessage({\n      id: 'notification.warning.title',\n      defaultMessage: 'Warning:',\n    });\n  } else if (type === 'warning') {\n    variant = 'warning';\n    alertTitle = formatMessage({\n      id: 'notification.warning.title',\n      defaultMessage: 'Warning:',\n    });\n  } else {\n    variant = 'success';\n    alertTitle = formatMessage({\n      id: 'notification.success.title',\n      defaultMessage: 'Success:',\n    });\n  }\n\n  if (title) {\n    alertTitle = title;\n  }\n\n  return (\n    <Alert\n      action={\n        link ? (\n          <Link href={link.url} isExternal>\n            {link.label}\n          </Link>\n        ) : undefined\n      }\n      onClose={handleClose}\n      closeLabel={formatMessage({\n        id: 'global.close',\n        defaultMessage: 'Close',\n      })}\n      title={alertTitle}\n      variant={variant}\n    >\n      {message}\n    </Alert>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Hook\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * @preserve\n * @description Returns an object to interact with the notification\n * system. The callbacks are wrapped in `useCallback` for a stable\n * identity.\n *\n * @example\n * ```tsx\n * import { useNotification } from '@strapi/strapi/admin';\n *\n * const MyComponent = () => {\n *  const { toggleNotification } = useNotification();\n *\n *  return <button onClick={() => toggleNotification({ message: 'Hello world!' })}>Click me</button>;\n */\nconst useNotification = () => React.useContext(NotificationsContext);\n\nexport { NotificationsProvider, useNotification };\nexport type { NotificationConfig, NotificationsContextValue };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAgCA,IAAMA,uBAA6BC,oBAAyC;EAC1EC,oBAAoB,MAAO;EAAA;AAC7B,CAAA;AAiBMC,IAAAA,wBAAwB,CAAC,EAAEC,SAAQ,MAA8B;AACrE,QAAMC,oBAA0BC,aAAO,CAAA;AAEvC,QAAM,CAACC,eAAeC,gBAAAA,IAA0BC,eAAyB,CAAA,CAAE;AAE3E,QAAMP,qBAA2BQ,kBAC/B,CAAC,EAAEC,MAAMC,SAASC,MAAMC,SAASC,iBAAiBC,SAASC,MAAK,MAAsB;AACpFT,qBAAiB,CAACU,MAAM;MACnBA,GAAAA;MACH;QACEC,IAAId,kBAAkBe;QACtBT;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACF;IACD,CAAA;EACH,GACA,CAAA,CAAE;AAGJ,QAAMI,oBAA0BX,kBAAY,CAACS,OAAAA;AAC3CX,qBAAiB,CAACU,MAAMA,EAAEI,OAAO,CAACC,MAAMA,EAAEJ,OAAOA,EAAAA,CAAAA;EACnD,GAAG,CAAA,CAAE;AAEL,QAAMK,QAAcC,cAAQ,OAAO;IAAEvB;EAAmB,IAAI;IAACA;EAAmB,CAAA;AAEhF,aACEwB,yBAAC1B,qBAAqB2B,UAAQ;IAACH;;UAC7BI,wBAACC,MAAAA;QACCC,MAAK;QACLC,YAAW;QACXC,UAAS;QACTC,WAAU;QACVC,YAAW;QACXC,KAAK;QACLC,KAAK;QACLC,OAAO;QACPC,QAAO;kBAEN/B,cAAcgC,IAAI,CAACC,iBAAAA;AAClB,qBACEZ,wBAACa,cAAAA;YAEE,GAAGD;YACJnB;UAFKmB,GAAAA,aAAarB,EAAE;QAK1B,CAAA;;MAEDf;;;AAGP;AAMA,IAAMqC,eAAe,CAAC,EACpBpB,mBACAN,kBAAkB,OAClBI,IACAN,MACAD,SACAI,SACAF,UAAU,MACVG,OACAN,KAAI,MACc;AAClB,QAAM,EAAE+B,cAAa,IAAKC,QAAAA;AAK1B,QAAMC,kBAAkBC,eAAe7B,OAAAA;AAEvC,QAAM8B,cAAoBpC,kBAAY,MAAA;AACpCkC,oBAAAA;AAEAvB,sBAAkBF,EAAAA;KACjB;IAACE;IAAmBF;IAAIyB;EAAgB,CAAA;AAG3CG,EAAMC,gBAAU,MAAA;AACd,QAAI,CAACjC,iBAAiB;AACpB,YAAMkC,mBAAmBC,WAAW,MAAA;AAClCJ,oBAAAA;SACChC,OAAAA;AAEH,aAAO,MAAA;AACLqC,qBAAaF,gBAAAA;MACf;IACF;KACC;IAAClC;IAAiB+B;IAAahC;EAAQ,CAAA;AAE1C,MAAIsC;AACJ,MAAIC;AAEJ,MAAI1C,SAAS,QAAQ;AACnByC,cAAU;AACVC,iBAAaX,cAAc;MACzBvB,IAAI;MACJmC,gBAAgB;IAClB,CAAA;aACS3C,SAAS,UAAU;AAC5ByC,cAAU;AACVC,iBAAaX,cAAc;MACzBvB,IAAI;MACJmC,gBAAgB;IAClB,CAAA;aACS3C,SAAS,WAAW;AAC7ByC,cAAU;AACVC,iBAAaX,cAAc;MACzBvB,IAAI;MACJmC,gBAAgB;IAClB,CAAA;SACK;AACLF,cAAU;AACVC,iBAAaX,cAAc;MACzBvB,IAAI;MACJmC,gBAAgB;IAClB,CAAA;EACF;AAEA,MAAIrC,OAAO;AACToC,iBAAapC;EACf;AAEA,aACEW,wBAAC2B,OAAAA;IACCC,QACE3C,WACEe,wBAAC6B,MAAAA;MAAKC,MAAM7C,KAAK8C;MAAKC,YAAU;MAC7B/C,UAAAA,KAAKgD;IAENC,CAAAA,IAAAA;IAEN9C,SAAS8B;IACTiB,YAAYrB,cAAc;MACxBvB,IAAI;MACJmC,gBAAgB;IAClB,CAAA;IACArC,OAAOoC;IACPD;IAECxC,UAAAA;;AAGP;AAqBMoD,IAAAA,kBAAkB,MAAYC,iBAAWjE,oBAAAA;", "names": ["NotificationsContext", "createContext", "toggleNotification", "NotificationsProvider", "children", "notificationIdRef", "useRef", "notifications", "setNotifications", "useState", "useCallback", "type", "message", "link", "timeout", "blockTransition", "onClose", "title", "s", "id", "current", "clearNotification", "filter", "n", "value", "useMemo", "_jsxs", "Provider", "_jsx", "Flex", "left", "marginLeft", "position", "direction", "alignItems", "gap", "top", "width", "zIndex", "map", "notification", "Notification", "formatMessage", "useIntl", "onCloseCallback", "useCallbackRef", "handleClose", "React", "useEffect", "timeoutReference", "setTimeout", "clearTimeout", "variant", "alertTitle", "defaultMessage", "<PERSON><PERSON>", "action", "Link", "href", "url", "isExternal", "label", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "useNotification", "useContext"]}