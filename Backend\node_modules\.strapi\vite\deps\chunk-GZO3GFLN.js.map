{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/constants/collections.ts", "../../../@strapi/content-manager/admin/src/services/documents.ts", "../../../@strapi/content-manager/admin/src/utils/api.ts", "../../../@strapi/content-manager/admin/src/constants/attributes.ts", "../../../@strapi/content-manager/admin/src/utils/validation.ts", "../../../@strapi/content-manager/admin/src/services/init.ts", "../../../@strapi/content-manager/admin/src/hooks/useContentTypeSchema.ts", "../../../@strapi/content-manager/admin/src/constants/hooks.ts", "../../../@strapi/content-manager/admin/src/services/contentTypes.ts", "../../../@strapi/content-manager/admin/src/utils/attributes.ts", "../../../@strapi/content-manager/admin/src/pages/EditView/utils/data.ts", "../../../@strapi/content-manager/admin/src/pages/EditView/utils/forms.ts", "../../../@strapi/content-manager/admin/src/hooks/useDocument.ts", "../../../@strapi/content-manager/admin/src/hooks/useDocumentLayout.ts", "../../../@strapi/content-manager/admin/src/utils/objects.ts"], "sourcesContent": ["const SINGLE_TYPES = 'single-types';\nconst COLLECTION_TYPES = 'collection-types';\n\nexport { SINGLE_TYPES, COLLECTION_TYPES };\n", "/**\n * Related to fetching the actual content of a collection type or single type.\n */\nimport { stringify } from 'qs';\n\nimport { SINGLE_TYPES } from '../constants/collections';\n\nimport { contentManagerApi } from './api';\n\nimport type {\n  <PERSON>lone,\n  Create,\n  Delete,\n  Find,\n  FindOne,\n  BulkDelete,\n  BulkPublish,\n  BulkUnpublish,\n  Discard,\n  CountDraftRelations,\n  CountManyEntriesDraftRelations,\n  Publish,\n  Unpublish,\n  Update,\n} from '../../../shared/contracts/collection-types';\n\nconst documentApi = contentManagerApi.injectEndpoints({\n  overrideExisting: true,\n  endpoints: (builder) => ({\n    autoCloneDocument: builder.mutation<\n      Clone.Response,\n      Clone.Params & {\n        params?: Find.Request['query'] & Clone.Request['query'];\n      }\n    >({\n      query: ({ model, sourceId, params }) => ({\n        url: `/content-manager/collection-types/${model}/auto-clone/${sourceId}`,\n        method: 'POST',\n        config: {\n          params,\n        },\n      }),\n      invalidatesTags: (_result, error, { model }) => {\n        if (error) {\n          return [];\n        }\n\n        return [{ type: 'Document', id: `${model}_LIST` }, 'RecentDocumentList'];\n      },\n    }),\n    cloneDocument: builder.mutation<\n      Clone.Response,\n      Clone.Params & {\n        data: Clone.Request['body'];\n        params?: Clone.Request['query'];\n      }\n    >({\n      query: ({ model, sourceId, data, params }) => ({\n        url: `/content-manager/collection-types/${model}/clone/${sourceId}`,\n        method: 'POST',\n        data,\n        config: {\n          params,\n        },\n      }),\n      invalidatesTags: (_result, _error, { model }) => [\n        { type: 'Document', id: `${model}_LIST` },\n        { type: 'UidAvailability', id: model },\n        'RecentDocumentList',\n      ],\n    }),\n    /**\n     * Creates a new collection-type document. This should ONLY be used for collection-types.\n     * single-types should always be using `updateDocument` since they always exist.\n     */\n    createDocument: builder.mutation<\n      Create.Response,\n      Create.Params & {\n        data: Create.Request['body'];\n        params?: Create.Request['query'];\n      }\n    >({\n      query: ({ model, data, params }) => ({\n        url: `/content-manager/collection-types/${model}`,\n        method: 'POST',\n        data,\n        config: {\n          params,\n        },\n      }),\n      invalidatesTags: (result, _error, { model }) => [\n        { type: 'Document', id: `${model}_LIST` },\n        'Relations',\n        { type: 'UidAvailability', id: model },\n        'RecentDocumentList',\n      ],\n      transformResponse: (response: Create.Response, meta, arg): Create.Response => {\n        /**\n         * TODO v6\n         * Adapt plugin:users-permissions.user to return the same response\n         * shape as all other requests. The error is returned as expected.\n         */\n        if (!('data' in response) && arg.model === 'plugin::users-permissions.user') {\n          return {\n            data: response,\n            meta: {\n              availableStatus: [],\n              availableLocales: [],\n            },\n          };\n        }\n\n        return response;\n      },\n    }),\n    deleteDocument: builder.mutation<\n      Delete.Response,\n      Pick<Delete.Params, 'model'> &\n        Pick<Partial<Delete.Params>, 'documentId'> & {\n          collectionType: string;\n          params?: Find.Request['query'];\n        }\n    >({\n      query: ({ collectionType, model, documentId, params }) => ({\n        url: `/content-manager/${collectionType}/${model}${\n          collectionType !== SINGLE_TYPES && documentId ? `/${documentId}` : ''\n        }`,\n        method: 'DELETE',\n        config: {\n          params,\n        },\n      }),\n      invalidatesTags: (_result, _error, { collectionType, model }) => [\n        { type: 'Document', id: collectionType !== SINGLE_TYPES ? `${model}_LIST` : model },\n        'RecentDocumentList',\n      ],\n    }),\n    deleteManyDocuments: builder.mutation<\n      BulkDelete.Response,\n      BulkDelete.Params & BulkDelete.Request['body'] & { params?: Find.Request['query'] }\n    >({\n      query: ({ model, params, ...body }) => ({\n        url: `/content-manager/collection-types/${model}/actions/bulkDelete`,\n        method: 'POST',\n        data: body,\n        config: {\n          params,\n        },\n      }),\n      invalidatesTags: (_res, _error, { model }) => [\n        { type: 'Document', id: `${model}_LIST` },\n        'RecentDocumentList',\n      ],\n    }),\n    discardDocument: builder.mutation<\n      Discard.Response,\n      Pick<Discard.Params, 'model'> &\n        Partial<Pick<Discard.Params, 'documentId'>> & {\n          collectionType: string;\n          params?: Find.Request['query'] & {\n            [key: string]: any;\n          };\n        }\n    >({\n      query: ({ collectionType, model, documentId, params }) => ({\n        url: documentId\n          ? `/content-manager/${collectionType}/${model}/${documentId}/actions/discard`\n          : `/content-manager/${collectionType}/${model}/actions/discard`,\n        method: 'POST',\n        config: {\n          params,\n        },\n      }),\n      invalidatesTags: (_result, _error, { collectionType, model, documentId }) => {\n        return [\n          {\n            type: 'Document',\n            id: collectionType !== SINGLE_TYPES ? `${model}_${documentId}` : model,\n          },\n          { type: 'Document', id: `${model}_LIST` },\n          'Relations',\n          { type: 'UidAvailability', id: model },\n          'RecentDocumentList',\n        ];\n      },\n    }),\n    /**\n     * Gets all documents of a collection type or single type.\n     * By passing different params you can get different results e.g. only published documents or 'es' documents.\n     */\n    getAllDocuments: builder.query<\n      Find.Response,\n      Find.Params & {\n        params?: Find.Request['query'] & {\n          [key: string]: any;\n        };\n      }\n    >({\n      query: ({ model, params }) => ({\n        url: `/content-manager/collection-types/${model}`,\n        method: 'GET',\n        config: {\n          params: stringify(params, { encode: true }),\n        },\n      }),\n      providesTags: (result, _error, arg) => {\n        return [\n          { type: 'Document', id: `ALL_LIST` },\n          { type: 'Document', id: `${arg.model}_LIST` },\n          ...(result?.results.map(({ documentId }) => ({\n            type: 'Document' as const,\n            id: `${arg.model}_${documentId}`,\n          })) ?? []),\n        ];\n      },\n    }),\n    getDraftRelationCount: builder.query<\n      CountDraftRelations.Response,\n      {\n        collectionType: string;\n        model: string;\n        /**\n         * You don't pass the documentId if the document is a single-type\n         */\n        documentId?: string;\n        params?: CountDraftRelations.Request['query'];\n      }\n    >({\n      query: ({ collectionType, model, documentId, params }) => ({\n        url: documentId\n          ? `/content-manager/${collectionType}/${model}/${documentId}/actions/countDraftRelations`\n          : `/content-manager/${collectionType}/${model}/actions/countDraftRelations`,\n        method: 'GET',\n        config: {\n          params,\n        },\n      }),\n    }),\n    getDocument: builder.query<\n      FindOne.Response,\n      Pick<FindOne.Params, 'model'> &\n        Partial<Pick<FindOne.Params, 'documentId'>> & {\n          collectionType: string;\n          params?: FindOne.Request['query'];\n        }\n    >({\n      // @ts-expect-error – TODO: fix ts error where data unknown doesn't work with response via an assertion?\n      queryFn: async (\n        { collectionType, model, documentId, params },\n        _api,\n        _extraOpts,\n        baseQuery\n      ) => {\n        const res = await baseQuery({\n          url: `/content-manager/${collectionType}/${model}${documentId ? `/${documentId}` : ''}`,\n          method: 'GET',\n          config: {\n            params,\n          },\n        });\n\n        /**\n         * To stop the query from locking itself in multiple retries, we intercept the error here and manage correctly.\n         * This is because single-types don't have a list view and fetching them with the route `/single-types/:model`\n         * never returns a list, just a single document but this won't exist if you've not made one before.\n         */\n        if (res.error && res.error.name === 'NotFoundError' && collectionType === SINGLE_TYPES) {\n          return { data: { document: undefined }, error: undefined };\n        }\n\n        return res;\n      },\n      providesTags: (result, _error, { collectionType, model, documentId }) => {\n        return [\n          // we prefer the result's id because we don't fetch single-types with an ID.\n          {\n            type: 'Document',\n            id:\n              collectionType !== SINGLE_TYPES\n                ? `${model}_${result && 'documentId' in result ? result.documentId : documentId}`\n                : model,\n          },\n          // Make it easy to invalidate all individual documents queries for a model\n          {\n            type: 'Document',\n            id: `${model}_ALL_ITEMS`,\n          },\n        ];\n      },\n    }),\n    getManyDraftRelationCount: builder.query<\n      CountManyEntriesDraftRelations.Response['data'],\n      CountManyEntriesDraftRelations.Request['query'] & {\n        model: string;\n      }\n    >({\n      query: ({ model, ...params }) => ({\n        url: `/content-manager/collection-types/${model}/actions/countManyEntriesDraftRelations`,\n        method: 'GET',\n        config: {\n          params,\n        },\n      }),\n      transformResponse: (response: CountManyEntriesDraftRelations.Response) => response.data,\n    }),\n    /**\n     * This endpoint will either create or update documents at the same time as publishing.\n     */\n    publishDocument: builder.mutation<\n      Publish.Response,\n      Pick<Publish.Params, 'model'> &\n        Partial<Pick<Publish.Params, 'documentId'>> & {\n          collectionType: string;\n          data: Publish.Request['body'];\n          params?: Publish.Request['query'];\n        }\n    >({\n      query: ({ collectionType, model, documentId, params, data }) => ({\n        url: documentId\n          ? `/content-manager/${collectionType}/${model}/${documentId}/actions/publish`\n          : `/content-manager/${collectionType}/${model}/actions/publish`,\n        method: 'POST',\n        data,\n        config: {\n          params,\n        },\n      }),\n      invalidatesTags: (_result, _error, { collectionType, model, documentId }) => {\n        return [\n          {\n            type: 'Document',\n            id: collectionType !== SINGLE_TYPES ? `${model}_${documentId}` : model,\n          },\n          { type: 'Document', id: `${model}_LIST` },\n          'Relations',\n          'RecentDocumentList',\n        ];\n      },\n    }),\n    publishManyDocuments: builder.mutation<\n      BulkPublish.Response,\n      BulkPublish.Params & BulkPublish.Request['body'] & { params?: BulkPublish.Request['query'] }\n    >({\n      query: ({ model, params, ...body }) => ({\n        url: `/content-manager/collection-types/${model}/actions/bulkPublish`,\n        method: 'POST',\n        data: body,\n        config: {\n          params,\n        },\n      }),\n      invalidatesTags: (_res, _error, { model, documentIds }) =>\n        documentIds.map((id) => ({ type: 'Document', id: `${model}_${id}` })),\n    }),\n    updateDocument: builder.mutation<\n      Update.Response,\n      Pick<Update.Params, 'model'> &\n        Partial<Pick<Update.Params, 'documentId'>> & {\n          collectionType: string;\n          data: Update.Request['body'];\n          params?: Update.Request['query'];\n        }\n    >({\n      query: ({ collectionType, model, documentId, data, params }) => ({\n        url: `/content-manager/${collectionType}/${model}${documentId ? `/${documentId}` : ''}`,\n        method: 'PUT',\n        data,\n        config: {\n          params,\n        },\n      }),\n      invalidatesTags: (_result, _error, { collectionType, model, documentId }) => {\n        return [\n          {\n            type: 'Document',\n            id: collectionType !== SINGLE_TYPES ? `${model}_${documentId}` : model,\n          },\n          'Relations',\n          { type: 'UidAvailability', id: model },\n          'RecentDocumentList',\n          'RecentDocumentList',\n        ];\n      },\n      async onQueryStarted({ data, ...patch }, { dispatch, queryFulfilled }) {\n        // Optimistically update the cache with the new data\n        const patchResult = dispatch(\n          documentApi.util.updateQueryData('getDocument', patch, (draft) => {\n            Object.assign(draft.data, data);\n          })\n        );\n        try {\n          await queryFulfilled;\n        } catch {\n          // Rollback the optimistic update if there's an error\n          patchResult.undo();\n        }\n      },\n      transformResponse: (response: Update.Response, meta, arg): Update.Response => {\n        /**\n         * TODO v6\n         * Adapt plugin:users-permissions.user to return the same response\n         * shape as all other requests. The error is returned as expected.\n         */\n        if (!('data' in response) && arg.model === 'plugin::users-permissions.user') {\n          return {\n            data: response,\n            meta: {\n              availableStatus: [],\n              availableLocales: [],\n            },\n          };\n        }\n\n        return response;\n      },\n    }),\n    unpublishDocument: builder.mutation<\n      Unpublish.Response,\n      Pick<Unpublish.Params, 'model'> &\n        Partial<Pick<Unpublish.Params, 'documentId'>> & {\n          collectionType: string;\n          params?: Unpublish.Request['query'];\n          data: Unpublish.Request['body'];\n        }\n    >({\n      query: ({ collectionType, model, documentId, params, data }) => ({\n        url: documentId\n          ? `/content-manager/${collectionType}/${model}/${documentId}/actions/unpublish`\n          : `/content-manager/${collectionType}/${model}/actions/unpublish`,\n        method: 'POST',\n        data,\n        config: {\n          params,\n        },\n      }),\n      invalidatesTags: (_result, _error, { collectionType, model, documentId }) => {\n        return [\n          {\n            type: 'Document',\n            id: collectionType !== SINGLE_TYPES ? `${model}_${documentId}` : model,\n          },\n          'RecentDocumentList',\n        ];\n      },\n    }),\n    unpublishManyDocuments: builder.mutation<\n      BulkUnpublish.Response,\n      Pick<BulkUnpublish.Params, 'model'> &\n        BulkUnpublish.Request['body'] & {\n          params?: BulkUnpublish.Request['query'];\n        }\n    >({\n      query: ({ model, params, ...body }) => ({\n        url: `/content-manager/collection-types/${model}/actions/bulkUnpublish`,\n        method: 'POST',\n        data: body,\n        config: {\n          params,\n        },\n      }),\n      invalidatesTags: (_res, _error, { model, documentIds }) => [\n        ...documentIds.map((id) => ({ type: 'Document' as const, id: `${model}_${id}` })),\n        'RecentDocumentList',\n      ],\n    }),\n  }),\n});\n\nconst {\n  useAutoCloneDocumentMutation,\n  useCloneDocumentMutation,\n  useCreateDocumentMutation,\n  useDeleteDocumentMutation,\n  useDeleteManyDocumentsMutation,\n  useDiscardDocumentMutation,\n  useGetAllDocumentsQuery,\n  useLazyGetDocumentQuery,\n  useGetDocumentQuery,\n  useLazyGetDraftRelationCountQuery,\n  useGetManyDraftRelationCountQuery,\n  usePublishDocumentMutation,\n  usePublishManyDocumentsMutation,\n  useUpdateDocumentMutation,\n  useUnpublishDocumentMutation,\n  useUnpublishManyDocumentsMutation,\n} = documentApi;\n\nexport {\n  useAutoCloneDocumentMutation,\n  useCloneDocumentMutation,\n  useCreateDocumentMutation,\n  useDeleteDocumentMutation,\n  useDeleteManyDocumentsMutation,\n  useDiscardDocumentMutation,\n  useGetAllDocumentsQuery,\n  useLazyGetDocumentQuery,\n  useGetDocumentQuery,\n  useLazyGetDraftRelationCountQuery as useGetDraftRelationCountQuery,\n  useGetManyDraftRelationCountQuery,\n  usePublishDocumentMutation,\n  usePublishManyDocumentsMutation,\n  useUpdateDocumentMutation,\n  useUnpublishDocumentMutation,\n  useUnpublishManyDocumentsMutation,\n};\n", "import { SerializedError } from '@reduxjs/toolkit';\nimport { ApiError, type UnknownApiError } from '@strapi/admin/strapi-admin';\n\ninterface Query {\n  plugins?: Record<string, unknown>;\n  _q?: string;\n  [key: string]: any;\n}\n\n/**\n * This type extracts the plugin options from the query\n * and appends them to the root of the query\n */\ntype TransformedQuery<TQuery extends Query> = Omit<TQuery, 'plugins'> & {\n  [key: string]: string;\n};\n\n/**\n * @description\n * Creates a valid query params object for get requests\n * ie. plugins[18n][locale]=en becomes locale=en\n */\nconst buildValidParams = <TQuery extends Query>(query: TQuery): TransformedQuery<TQuery> => {\n  if (!query) return query;\n\n  // Extract pluginOptions from the query, they shouldn't be part of the URL\n  const { plugins: _, ...validQueryParams } = {\n    ...query,\n    ...Object.values(query?.plugins ?? {}).reduce<Record<string, string>>(\n      (acc, current) => Object.assign(acc, current),\n      {}\n    ),\n  };\n\n  return validQueryParams;\n};\n\ntype BaseQueryError = ApiError | UnknownApiError;\n\nconst isBaseQueryError = (error: BaseQueryError | SerializedError): error is BaseQueryError => {\n  return error.name !== undefined;\n};\n\nexport { isBaseQueryError, buildValidParams };\nexport type { BaseQueryError, UnknownApiError };\n", "const ID = 'id';\n\nconst CREATED_BY_ATTRIBUTE_NAME = 'createdBy';\nconst UPDATED_BY_ATTRIBUTE_NAME = 'updatedBy';\n\nconst CREATOR_FIELDS = [CREATED_BY_ATTRIBUTE_NAME, UPDATED_BY_ATTRIBUTE_NAME];\n\nconst PUBLISHED_BY_ATTRIBUTE_NAME = 'publishedBy';\nconst CREATED_AT_ATTRIBUTE_NAME = 'createdAt';\nconst UPDATED_AT_ATTRIBUTE_NAME = 'updatedAt';\nconst PUBLISHED_AT_ATTRIBUTE_NAME = 'publishedAt';\n\nconst DOCUMENT_META_FIELDS = [\n  ID,\n  ...CREATOR_FIELDS,\n  PUBLISHED_BY_ATTRIBUTE_NAME,\n  CREATED_AT_ATTRIBUTE_NAME,\n  UPDATED_AT_ATTRIBUTE_NAME,\n  PUBLISHED_AT_ATTRIBUTE_NAME,\n];\n\n/**\n * List of attribute types that cannot be used as the main field.\n * Not sure the name could be any clearer.\n */\nconst ATTRIBUTE_TYPES_THAT_CANNOT_BE_MAIN_FIELD = [\n  'dynamiczone',\n  'json',\n  'text',\n  'relation',\n  'component',\n  'boolean',\n  'media',\n  'password',\n  'richtext',\n  'timestamp',\n  'blocks',\n];\n\nexport {\n  ATTRIBUTE_TYPES_THAT_CANNOT_BE_MAIN_FIELD,\n  CREATED_AT_ATTRIBUTE_NAME,\n  UPDATED_AT_ATTRIBUTE_NAME,\n  PUBLISHED_AT_ATTRIBUTE_NAME,\n  CREATED_BY_ATTRIBUTE_NAME,\n  UPDATED_BY_ATTRIBUTE_NAME,\n  PUBLISHED_BY_ATTRIBUTE_NAME,\n  CREATOR_FIELDS,\n  DOCUMENT_META_FIELDS,\n};\n", "import { translatedErrors } from '@strapi/admin/strapi-admin';\nimport pipe from 'lodash/fp/pipe';\nimport * as yup from 'yup';\n\nimport { DOCUMENT_META_FIELDS } from '../constants/attributes';\n\nimport type { ComponentsDictionary, Schema } from '../hooks/useDocument';\nimport type { Schema as SchemaUtils } from '@strapi/types';\nimport type { ObjectShape } from 'yup/lib/object';\n\ntype AnySchema =\n  | yup.StringSchema\n  | yup.NumberSchema\n  | yup.BooleanSchema\n  | yup.DateSchema\n  | yup.ArraySchema<any>\n  | yup.ObjectSchema<any>;\n\n/* -------------------------------------------------------------------------------------------------\n * createYupSchema\n * -----------------------------------------------------------------------------------------------*/\n\ninterface ValidationOptions {\n  status: 'draft' | 'published' | null;\n}\n\nconst arrayValidator = (attribute: Schema['attributes'][string], options: ValidationOptions) => ({\n  message: translatedErrors.required,\n  test(value: unknown) {\n    if (options.status === 'draft') {\n      return true;\n    }\n\n    if (!attribute.required) {\n      return true;\n    }\n\n    if (!value) {\n      return false;\n    }\n\n    if (Array.isArray(value) && value.length === 0) {\n      return false;\n    }\n\n    return true;\n  },\n});\n\n/**\n * TODO: should we create a Map to store these based on the hash of the schema?\n */\nconst createYupSchema = (\n  attributes: Schema['attributes'] = {},\n  components: ComponentsDictionary = {},\n  options: ValidationOptions = { status: null }\n): yup.ObjectSchema<any> => {\n  const createModelSchema = (attributes: Schema['attributes']): yup.ObjectSchema<any> =>\n    yup\n      .object()\n      .shape(\n        Object.entries(attributes).reduce<ObjectShape>((acc, [name, attribute]) => {\n          if (DOCUMENT_META_FIELDS.includes(name)) {\n            return acc;\n          }\n\n          /**\n           * These validations won't apply to every attribute\n           * and that's okay, in that case we just return the\n           * schema as it was passed.\n           */\n          const validations = [\n            addNullableValidation,\n            addRequiredValidation,\n            addMinLengthValidation,\n            addMaxLengthValidation,\n            addMinValidation,\n            addMaxValidation,\n            addRegexValidation,\n          ].map((fn) => fn(attribute, options));\n\n          const transformSchema = pipe(...validations);\n\n          switch (attribute.type) {\n            case 'component': {\n              const { attributes } = components[attribute.component];\n\n              if (attribute.repeatable) {\n                return {\n                  ...acc,\n                  [name]: transformSchema(\n                    yup.array().of(createModelSchema(attributes).nullable(false))\n                  ).test(arrayValidator(attribute, options)),\n                };\n              } else {\n                return {\n                  ...acc,\n                  [name]: transformSchema(createModelSchema(attributes).nullable()),\n                };\n              }\n            }\n            case 'dynamiczone':\n              return {\n                ...acc,\n                [name]: transformSchema(\n                  yup.array().of(\n                    yup.lazy(\n                      (\n                        data: SchemaUtils.Attribute.Value<SchemaUtils.Attribute.DynamicZone>[number]\n                      ) => {\n                        const attributes = components?.[data?.__component]?.attributes;\n\n                        const validation = yup\n                          .object()\n                          .shape({\n                            __component: yup.string().required().oneOf(Object.keys(components)),\n                          })\n                          .nullable(false);\n                        if (!attributes) {\n                          return validation;\n                        }\n\n                        return validation.concat(createModelSchema(attributes));\n                      }\n                    ) as unknown as yup.ObjectSchema<any>\n                  )\n                ).test(arrayValidator(attribute, options)),\n              };\n            case 'relation':\n              return {\n                ...acc,\n                [name]: transformSchema(\n                  yup.lazy((value) => {\n                    if (!value) {\n                      return yup.mixed().nullable(true);\n                    } else if (Array.isArray(value)) {\n                      // If a relation value is an array, we expect\n                      // an array of objects with {id} properties, representing the related entities.\n                      return yup.array().of(\n                        yup.object().shape({\n                          id: yup.number().required(),\n                        })\n                      );\n                    } else if (typeof value === 'object') {\n                      // A realtion value can also be an object. Some API\n                      // repsonses return the number of entities in the relation\n                      // as { count: x }\n                      return yup.object();\n                    } else {\n                      return yup\n                        .mixed()\n                        .test(\n                          'type-error',\n                          'Relation values must be either null, an array of objects with {id} or an object.',\n                          () => false\n                        );\n                    }\n                  })\n                ),\n              };\n            default:\n              return {\n                ...acc,\n                [name]: transformSchema(createAttributeSchema(attribute)),\n              };\n          }\n        }, {})\n      )\n      /**\n       * TODO: investigate why an undefined object fails a check of `nullable`.\n       */\n      .default(null);\n\n  return createModelSchema(attributes);\n};\n\nconst createAttributeSchema = (\n  attribute: Exclude<\n    SchemaUtils.Attribute.AnyAttribute,\n    { type: 'dynamiczone' } | { type: 'component' } | { type: 'relation' }\n  >\n) => {\n  switch (attribute.type) {\n    case 'biginteger':\n      return yup.string().matches(/^-?\\d*$/);\n    case 'boolean':\n      return yup.boolean();\n    case 'blocks':\n      return yup.mixed().test('isBlocks', translatedErrors.json, (value) => {\n        if (!value || Array.isArray(value)) {\n          return true;\n        } else {\n          return false;\n        }\n      });\n    case 'decimal':\n    case 'float':\n    case 'integer':\n      return yup.number();\n    case 'email':\n      return yup.string().email(translatedErrors.email);\n    case 'enumeration':\n      return yup.string().oneOf([...attribute.enum, null]);\n    case 'json':\n      return yup.mixed().test('isJSON', translatedErrors.json, (value) => {\n        /**\n         * We don't want to validate the JSON field if it's empty.\n         */\n        if (!value || (typeof value === 'string' && value.length === 0)) {\n          return true;\n        }\n\n        // If the value was created via content API and wasn't changed, then it's still an object\n        if (typeof value === 'object') {\n          try {\n            JSON.stringify(value);\n            return true;\n          } catch (err) {\n            return false;\n          }\n        }\n\n        try {\n          JSON.parse(value);\n\n          return true;\n        } catch (err) {\n          return false;\n        }\n      });\n    case 'password':\n    case 'richtext':\n    case 'string':\n    case 'text':\n      return yup.string();\n    case 'uid':\n      return yup\n        .string()\n        .matches(attribute.regex ? new RegExp(attribute.regex) : /^[A-Za-z0-9-_.~]*$/);\n    default:\n      /**\n       * This allows any value.\n       */\n      return yup.mixed();\n  }\n};\n\n// Helper function to return schema.nullable() if it exists, otherwise return schema\nconst nullableSchema = <TSchema extends AnySchema>(schema: TSchema) => {\n  return schema?.nullable\n    ? schema.nullable()\n    : // In some cases '.nullable' will not be available on the schema.\n      // e.g. when the schema has been built using yup.lazy (e.g. for relations).\n      // In these cases we should just return the schema as it is.\n      schema;\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Validators\n * -----------------------------------------------------------------------------------------------*/\n/**\n * Our validator functions can be preped with the\n * attribute and then have the schema piped through them.\n */\ntype ValidationFn = (\n  attribute: Schema['attributes'][string],\n  options: ValidationOptions\n) => <TSchema extends AnySchema>(schema: TSchema) => TSchema;\n\nconst addNullableValidation: ValidationFn = () => (schema) => {\n  return nullableSchema(schema);\n};\n\nconst addRequiredValidation: ValidationFn = (attribute, options) => (schema) => {\n  if (options.status === 'draft' || !attribute.required) {\n    return schema;\n  }\n\n  if (attribute.required && 'required' in schema) {\n    return schema.required(translatedErrors.required);\n  }\n\n  return schema;\n};\n\nconst addMinLengthValidation: ValidationFn =\n  (attribute, options) =>\n  <TSchema extends AnySchema>(schema: TSchema): TSchema => {\n    // Skip minLength validation for draft\n    if (options.status === 'draft') {\n      return schema;\n    }\n\n    if (\n      'minLength' in attribute &&\n      attribute.minLength &&\n      Number.isInteger(attribute.minLength) &&\n      'min' in schema\n    ) {\n      return schema.min(attribute.minLength, {\n        ...translatedErrors.minLength,\n        values: {\n          min: attribute.minLength,\n        },\n      }) as TSchema;\n    }\n\n    return schema;\n  };\n\nconst addMaxLengthValidation: ValidationFn =\n  (attribute) =>\n  <TSchema extends AnySchema>(schema: TSchema): TSchema => {\n    if (\n      'maxLength' in attribute &&\n      attribute.maxLength &&\n      Number.isInteger(attribute.maxLength) &&\n      'max' in schema\n    ) {\n      return schema.max(attribute.maxLength, {\n        ...translatedErrors.maxLength,\n        values: {\n          max: attribute.maxLength,\n        },\n      }) as TSchema;\n    }\n\n    return schema;\n  };\n\nconst addMinValidation: ValidationFn =\n  (attribute, options) =>\n  <TSchema extends AnySchema>(schema: TSchema): TSchema => {\n    // do not validate min for draft\n    if (options.status === 'draft') {\n      return schema;\n    }\n\n    if ('min' in attribute && 'min' in schema) {\n      const min = toInteger(attribute.min);\n\n      if (min) {\n        return schema.min(min, {\n          ...translatedErrors.min,\n          values: {\n            min,\n          },\n        }) as TSchema;\n      }\n    }\n\n    return schema;\n  };\n\nconst addMaxValidation: ValidationFn =\n  (attribute) =>\n  <TSchema extends AnySchema>(schema: TSchema): TSchema => {\n    if ('max' in attribute) {\n      const max = toInteger(attribute.max);\n\n      if ('max' in schema && max) {\n        return schema.max(max, {\n          ...translatedErrors.max,\n          values: {\n            max,\n          },\n        }) as TSchema;\n      }\n    }\n\n    return schema;\n  };\n\nconst toInteger = (val?: string | number): number | undefined => {\n  if (typeof val === 'number' || val === undefined) {\n    return val;\n  } else {\n    const num = Number(val);\n    return isNaN(num) ? undefined : num;\n  }\n};\n\nconst addRegexValidation: ValidationFn =\n  (attribute) =>\n  <TSchema extends AnySchema>(schema: TSchema): TSchema => {\n    if ('regex' in attribute && attribute.regex && 'matches' in schema) {\n      return schema.matches(new RegExp(attribute.regex), {\n        message: {\n          id: translatedErrors.regex.id,\n          defaultMessage: 'The value does not match the defined pattern.',\n        },\n\n        excludeEmptyString: !attribute.required,\n      }) as TSchema;\n    }\n\n    return schema;\n  };\n\nexport { createYupSchema };\n", "import { contentManagerApi } from './api';\n\nimport type { GetInitData } from '../../../shared/contracts/init';\n\nconst initApi = contentManagerApi.injectEndpoints({\n  endpoints: (builder) => ({\n    getInitialData: builder.query<GetInitData.Response['data'], void>({\n      query: () => '/content-manager/init',\n      transformResponse: (response: GetInitData.Response) => response.data,\n      providesTags: ['InitialData'],\n    }),\n  }),\n});\n\nconst { useGetInitialDataQuery } = initApi;\n\nexport { useGetInitialDataQuery };\n", "import * as React from 'react';\n\nimport { useNotification, useAP<PERSON><PERSON>r<PERSON><PERSON><PERSON> } from '@strapi/admin/strapi-admin';\n\nimport { useGetInitialDataQuery } from '../services/init';\n\nimport type { Component } from '../../../shared/contracts/components';\nimport type { ContentType } from '../../../shared/contracts/content-types';\nimport type { Schema } from '@strapi/types';\n\n/* -------------------------------------------------------------------------------------------------\n * useContentTypeSchema\n * -----------------------------------------------------------------------------------------------*/\ntype ComponentsDictionary = Record<string, Component>;\n\n/**\n * @internal\n * @description Given a model UID, return the schema and the schemas\n * of the associated components within said model's schema. A wrapper\n * implementation around the `useGetInitialDataQuery` with a unique\n * `selectFromResult` function to memoize the calculation.\n *\n * If no model is provided, the hook will return all the schemas.\n */\nconst useContentTypeSchema = (model?: string) => {\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n\n  const { data, error, isLoading, isFetching } = useGetInitialDataQuery(undefined);\n\n  const { components, contentType, contentTypes } = React.useMemo(() => {\n    const contentType = data?.contentTypes.find((ct) => ct.uid === model);\n\n    const componentsByKey = data?.components.reduce<ComponentsDictionary>((acc, component) => {\n      acc[component.uid] = component;\n\n      return acc;\n    }, {});\n\n    const components = extractContentTypeComponents(contentType?.attributes, componentsByKey);\n\n    return {\n      components: Object.keys(components).length === 0 ? undefined : components,\n      contentType,\n      contentTypes: data?.contentTypes ?? [],\n    };\n  }, [model, data]);\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    }\n  }, [toggleNotification, error, formatAPIError]);\n\n  return {\n    // This must be memoized to avoid inifiinite re-renders where the empty object is different everytime.\n    components: React.useMemo(() => components ?? {}, [components]),\n    schema: contentType,\n    schemas: contentTypes,\n    isLoading: isLoading || isFetching,\n  };\n};\n\n/* -------------------------------------------------------------------------------------------------\n * extractContentTypeComponents\n * -----------------------------------------------------------------------------------------------*/\n/**\n * @internal\n * @description Extracts the components used in a content type's attributes recursively.\n */\nconst extractContentTypeComponents = (\n  attributes: ContentType['attributes'] = {},\n  allComponents: ComponentsDictionary = {}\n): ComponentsDictionary => {\n  const getComponents = (attributes: Schema.Attribute.AnyAttribute[]) => {\n    return attributes.reduce<string[]>((acc, attribute) => {\n      /**\n       * If the attribute is a component or dynamiczone, we need to recursively\n       * extract the component UIDs from its attributes.\n       */\n      if (attribute.type === 'component') {\n        const componentAttributes = Object.values(\n          allComponents[attribute.component]?.attributes ?? {}\n        );\n\n        acc.push(attribute.component, ...getComponents(componentAttributes));\n      } else if (attribute.type === 'dynamiczone') {\n        acc.push(\n          ...attribute.components,\n          /**\n           * Dynamic zones have an array of components, so we flatMap over them\n           * performing the same search as above.\n           */\n          ...attribute.components.flatMap((componentUid) => {\n            const componentAttributes = Object.values(\n              allComponents[componentUid]?.attributes ?? {}\n            );\n\n            return getComponents(componentAttributes);\n          })\n        );\n      }\n\n      return acc;\n    }, []);\n  };\n\n  const componentUids = getComponents(Object.values(attributes));\n\n  const uniqueComponentUids = [...new Set(componentUids)];\n\n  const componentsByKey = uniqueComponentUids.reduce<ComponentsDictionary>((acc, uid) => {\n    acc[uid] = allComponents[uid];\n\n    return acc;\n  }, {});\n\n  return componentsByKey;\n};\n\nexport { useContentTypeSchema, extractContentTypeComponents };\nexport type { ComponentsDictionary };\n", "export const HOOKS = {\n  /**\n   * Hook that allows to mutate the displayed headers of the list view table\n   * @constant\n   * @type {string}\n   */\n  INJECT_COLUMN_IN_TABLE: 'Admin/CM/pages/ListView/inject-column-in-table',\n\n  /**\n   * Hook that allows to mutate the CM's collection types links pre-set filters\n   * @constant\n   * @type {string}\n   */\n  MUTATE_COLLECTION_TYPES_LINKS: 'Admin/CM/pages/App/mutate-collection-types-links',\n\n  /**\n   * Hook that allows to mutate the CM's edit view layout\n   * @constant\n   * @type {string}\n   */\n  MUTATE_EDIT_VIEW_LAYOUT: 'Admin/CM/pages/EditView/mutate-edit-view-layout',\n\n  /**\n   * Hook that allows to mutate the CM's single types links pre-set filters\n   * @constant\n   * @type {string}\n   */\n  MUTATE_SINGLE_TYPES_LINKS: 'Admin/CM/pages/App/mutate-single-types-links',\n};\n", "import {\n  FindContentTypeConfiguration,\n  UpdateContentTypeConfiguration,\n  FindContentTypesSettings,\n} from '../../../shared/contracts/content-types';\n\nimport { contentManagerApi } from './api';\n\nconst contentTypesApi = contentManagerApi.injectEndpoints({\n  endpoints: (builder) => ({\n    getContentTypeConfiguration: builder.query<\n      FindContentTypeConfiguration.Response['data'],\n      string\n    >({\n      query: (uid) => ({\n        url: `/content-manager/content-types/${uid}/configuration`,\n        method: 'GET',\n      }),\n      transformResponse: (response: FindContentTypeConfiguration.Response) => response.data,\n      providesTags: (_result, _error, uid) => [\n        { type: 'ContentTypesConfiguration', id: uid },\n        { type: 'ContentTypeSettings', id: 'LIST' },\n      ],\n    }),\n    getAllContentTypeSettings: builder.query<FindContentTypesSettings.Response['data'], void>({\n      query: () => '/content-manager/content-types-settings',\n      transformResponse: (response: FindContentTypesSettings.Response) => response.data,\n      providesTags: [{ type: 'ContentTypeSettings', id: 'LIST' }],\n    }),\n    updateContentTypeConfiguration: builder.mutation<\n      UpdateContentTypeConfiguration.Response['data'],\n      UpdateContentTypeConfiguration.Request['body'] & {\n        uid: string;\n      }\n    >({\n      query: ({ uid, ...body }) => ({\n        url: `/content-manager/content-types/${uid}/configuration`,\n        method: 'PUT',\n        data: body,\n      }),\n      transformResponse: (response: UpdateContentTypeConfiguration.Response) => response.data,\n      invalidatesTags: (_result, _error, { uid }) => [\n        { type: 'ContentTypesConfiguration', id: uid },\n        { type: 'ContentTypeSettings', id: 'LIST' },\n        // Is this necessary?\n        { type: 'InitialData' },\n      ],\n    }),\n  }),\n});\n\nconst {\n  useGetContentTypeConfigurationQuery,\n  useGetAllContentTypeSettingsQuery,\n  useUpdateContentTypeConfigurationMutation,\n} = contentTypesApi;\n\nexport {\n  useGetContentTypeConfigurationQuery,\n  useGetAllContentTypeSettingsQuery,\n  useUpdateContentTypeConfigurationMutation,\n};\n", "import type { ComponentsDictionary, Schema } from '../hooks/useDocument';\nimport type { Schema as SchemaUtils } from '@strapi/types';\n\nconst checkIfAttributeIsDisplayable = (attribute: SchemaUtils.Attribute.AnyAttribute) => {\n  const { type } = attribute;\n\n  if (type === 'relation') {\n    return !attribute.relation.toLowerCase().includes('morph');\n  }\n\n  return !['json', 'dynamiczone', 'richtext', 'password', 'blocks'].includes(type) && !!type;\n};\n\ninterface MainField {\n  name: string;\n  type: SchemaUtils.Attribute.Kind | 'custom';\n}\n\n/**\n * @internal\n * @description given an attribute, content-type schemas & component schemas, find the mainField name & type.\n * If the attribute does not need a `mainField` then we return undefined. If we do not find the type\n * of the field, we assume it's a string #sensible-defaults\n */\nconst getMainField = (\n  attribute: SchemaUtils.Attribute.AnyAttribute,\n  mainFieldName: string | undefined,\n  { schemas, components }: { schemas: Schema[]; components: ComponentsDictionary }\n): MainField | undefined => {\n  if (!mainFieldName) {\n    return undefined;\n  }\n\n  const mainFieldType =\n    attribute.type === 'component'\n      ? components[attribute.component].attributes[mainFieldName].type\n      : // @ts-expect-error – `targetModel` does exist on the attribute for a relation.\n        schemas.find((schema) => schema.uid === attribute.targetModel)?.attributes[mainFieldName]\n          .type;\n\n  return {\n    name: mainFieldName,\n    type: mainFieldType ?? 'string',\n  };\n};\n\nexport { checkIfAttributeIsDisplayable, getMainField };\nexport type { MainField };\n", "import { generateN<PERSON>eysBetween } from 'fractional-indexing';\nimport pipe from 'lodash/fp/pipe';\n\nimport { DOCUMENT_META_FIELDS } from '../../../constants/attributes';\n\nimport type { ComponentsDictionary, Document } from '../../../hooks/useDocument';\nimport type { Schema, UID } from '@strapi/types';\n\n/* -------------------------------------------------------------------------------------------------\n * traverseData\n * -----------------------------------------------------------------------------------------------*/\n\n// Make only attributes required since it's the only one Content History has\ntype PartialSchema = Partial<Schema.Schema> & Pick<Schema.Schema, 'attributes'>;\n\ntype Predicate = <TAttribute extends Schema.Attribute.AnyAttribute>(\n  attribute: TAttribute,\n  value: Schema.Attribute.Value<TAttribute>\n) => boolean;\ntype Transform = <TAttribute extends Schema.Attribute.AnyAttribute>(\n  value: any,\n  attribute: TAttribute\n) => any;\ntype AnyData = Omit<Document, 'id'>;\n\nconst BLOCK_LIST_ATTRIBUTE_KEYS = ['__component', '__temp_key__'];\n\n/**\n * @internal This function is used to traverse the data and transform the values.\n * Given a predicate function, it will transform the value (using the given transform function)\n * if the predicate returns true. If it finds that the attribute is a component or dynamiczone,\n * it will recursively traverse those data structures as well.\n *\n * It is possible to break the ContentManager by using this function incorrectly, for example,\n * if you transform a number into a string but the attribute type is a number, the ContentManager\n * will not be able to save the data and the Form will likely crash because the component it's\n * passing the data too won't succesfully be able to handle the value.\n */\nconst traverseData =\n  (predicate: Predicate, transform: Transform) =>\n  (schema: PartialSchema, components: ComponentsDictionary = {}) =>\n  (data: AnyData = {}) => {\n    const traverse = (datum: AnyData, attributes: Schema.Schema['attributes']) => {\n      return Object.entries(datum).reduce<AnyData>((acc, [key, value]) => {\n        const attribute = attributes[key];\n\n        /**\n         * If the attribute is a block list attribute, we don't want to transform it.\n         * We also don't want to transform null or undefined values.\n         */\n        if (BLOCK_LIST_ATTRIBUTE_KEYS.includes(key) || value === null || value === undefined) {\n          acc[key] = value;\n          return acc;\n        }\n\n        if (attribute.type === 'component') {\n          if (attribute.repeatable) {\n            const componentValue = (\n              predicate(attribute, value) ? transform(value, attribute) : value\n            ) as Schema.Attribute.Value<Schema.Attribute.Component<UID.Component, true>>;\n            acc[key] = componentValue.map((componentData) =>\n              traverse(componentData, components[attribute.component]?.attributes ?? {})\n            );\n          } else {\n            const componentValue = (\n              predicate(attribute, value) ? transform(value, attribute) : value\n            ) as Schema.Attribute.Value<Schema.Attribute.Component<UID.Component, false>>;\n\n            acc[key] = traverse(componentValue, components[attribute.component]?.attributes ?? {});\n          }\n        } else if (attribute.type === 'dynamiczone') {\n          const dynamicZoneValue = (\n            predicate(attribute, value) ? transform(value, attribute) : value\n          ) as Schema.Attribute.Value<Schema.Attribute.DynamicZone>;\n\n          acc[key] = dynamicZoneValue.map((componentData) =>\n            traverse(componentData, components[componentData.__component]?.attributes ?? {})\n          );\n        } else if (predicate(attribute, value)) {\n          acc[key] = transform(value, attribute);\n        } else {\n          acc[key] = value;\n        }\n\n        return acc;\n      }, {});\n    };\n\n    return traverse(data, schema.attributes);\n  };\n\n/* -------------------------------------------------------------------------------------------------\n * removeProhibitedFields\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * @internal Removes all the fields that are not allowed.\n */\nconst removeProhibitedFields = (prohibitedFields: Schema.Attribute.Kind[]) =>\n  traverseData(\n    (attribute) => prohibitedFields.includes(attribute.type),\n    () => ''\n  );\n\n/* -------------------------------------------------------------------------------------------------\n * prepareRelations\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * @internal\n * @description Sets all relation values to an empty array.\n */\nconst prepareRelations = traverseData(\n  (attribute) => attribute.type === 'relation',\n  () => ({\n    connect: [],\n    disconnect: [],\n  })\n);\n\n/* -------------------------------------------------------------------------------------------------\n * prepareTempKeys\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * @internal\n * @description Adds a `__temp_key__` to each component and dynamiczone item. This gives us\n * a stable identifier regardless of its ids etc. that we can then use for drag and drop.\n */\nconst prepareTempKeys = traverseData(\n  (attribute) =>\n    (attribute.type === 'component' && attribute.repeatable) || attribute.type === 'dynamiczone',\n  (data) => {\n    if (Array.isArray(data) && data.length > 0) {\n      const keys = generateNKeysBetween(undefined, undefined, data.length);\n\n      return data.map((datum, index) => ({\n        ...datum,\n        __temp_key__: keys[index],\n      }));\n    }\n\n    return data;\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * removeFieldsThatDontExistOnSchema\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * @internal\n * @description Fields that don't exist in the schema like createdAt etc. are only on the first level (not nested),\n * as such we don't need to traverse the components to remove them.\n */\nconst removeFieldsThatDontExistOnSchema = (schema: PartialSchema) => (data: AnyData) => {\n  const schemaKeys = Object.keys(schema.attributes);\n  const dataKeys = Object.keys(data);\n\n  const keysToRemove = dataKeys.filter((key) => !schemaKeys.includes(key));\n\n  const revisedData = [...keysToRemove, ...DOCUMENT_META_FIELDS].reduce((acc, key) => {\n    delete acc[key];\n\n    return acc;\n  }, structuredClone(data));\n\n  return revisedData;\n};\n\n/**\n * @internal\n * @description We need to remove null fields from the data-structure because it will pass it\n * to the specific inputs breaking them as most would prefer empty strings or `undefined` if\n * they're controlled / uncontrolled.\n */\nconst removeNullValues = (data: AnyData) => {\n  return Object.entries(data).reduce<AnyData>((acc, [key, value]) => {\n    if (value === null) {\n      return acc;\n    }\n\n    acc[key] = value;\n\n    return acc;\n  }, {});\n};\n\n/* -------------------------------------------------------------------------------------------------\n * transformDocuments\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * @internal\n * @description Takes a document data structure (this could be from the API or a default form structure)\n * and applies consistent data transformations to it. This is also used when we add new components to the\n * form to ensure the data is correctly prepared from their default state e.g. relations are set to an empty array.\n */\nconst transformDocument =\n  (schema: PartialSchema, components: ComponentsDictionary = {}) =>\n  (document: AnyData) => {\n    const transformations = pipe(\n      removeFieldsThatDontExistOnSchema(schema),\n      removeProhibitedFields(['password'])(schema, components),\n      removeNullValues,\n      prepareRelations(schema, components),\n      prepareTempKeys(schema, components)\n    );\n\n    return transformations(document);\n  };\n\nexport {\n  removeProhibitedFields,\n  prepareRelations,\n  prepareTempKeys,\n  removeFieldsThatDontExistOnSchema,\n  transformDocument,\n};\nexport type { AnyData };\n", "import type { ComponentsDictionary, Document } from '../../../hooks/useDocument';\nimport type { Schema } from '@strapi/types';\n\ntype AnyData = Omit<Document, 'id'>;\n\n/* -------------------------------------------------------------------------------------------------\n * createDefaultForm\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * @internal Using the content-type schema & the components dictionary of the content-type,\n * creates a form with pre-filled default values. This is used when creating a new entry.\n */\nconst createDefaultForm = (\n  contentType: Schema.Schema,\n  components: ComponentsDictionary = {}\n): AnyData => {\n  const traverseSchema = (attributes: Schema.Schema['attributes']): AnyData => {\n    return Object.entries(attributes).reduce<AnyData>((acc, [key, attribute]) => {\n      if ('default' in attribute) {\n        acc[key] = attribute.default;\n      } else if (attribute.type === 'component' && attribute.required) {\n        const defaultComponentForm = traverseSchema(components[attribute.component].attributes);\n\n        if (attribute.repeatable) {\n          acc[key] = attribute.min ? [...Array(attribute.min).fill(defaultComponentForm)] : [];\n        } else {\n          acc[key] = defaultComponentForm;\n        }\n      } else if (attribute.type === 'dynamiczone' && attribute.required) {\n        acc[key] = [];\n      }\n\n      return acc;\n    }, {});\n  };\n\n  return traverseSchema(contentType.attributes);\n};\n\nexport { createDefaultForm };\n", "/**\n * This hook doesn't use a context provider because we fetch directly from the server,\n * this sounds expensive but actually, it's really not. Because we have redux-toolkit-query\n * being a cache layer so if nothing invalidates the cache, we don't fetch again.\n */\n\nimport * as React from 'react';\n\nimport {\n  useNotification,\n  use<PERSON>IError<PERSON>and<PERSON>,\n  useQueryParams,\n  FormErrors,\n  getYupValidationErrors,\n  useForm,\n} from '@strapi/admin/strapi-admin';\nimport { useIntl } from 'react-intl';\nimport { useParams } from 'react-router-dom';\nimport { ValidationError } from 'yup';\n\nimport { SINGLE_TYPES } from '../constants/collections';\nimport { type AnyData, transformDocument } from '../pages/EditView/utils/data';\nimport { createDefaultForm } from '../pages/EditView/utils/forms';\nimport { useGetDocumentQuery } from '../services/documents';\nimport { buildValidParams } from '../utils/api';\nimport { createYupSchema } from '../utils/validation';\n\nimport { useContentTypeSchema, ComponentsDictionary } from './useContentTypeSchema';\nimport { useDocumentLayout } from './useDocumentLayout';\n\nimport type { FindOne } from '../../../shared/contracts/collection-types';\nimport type { ContentType } from '../../../shared/contracts/content-types';\nimport type { Modules } from '@strapi/types';\n\ninterface UseDocumentArgs {\n  collectionType: string;\n  model: string;\n  documentId?: string;\n  params?: object;\n}\n\ntype UseDocumentOpts = Parameters<typeof useGetDocumentQuery>[1];\n\ntype Document = FindOne.Response['data'];\n\ntype Schema = ContentType;\n\ntype UseDocument = (\n  args: UseDocumentArgs,\n  opts?: UseDocumentOpts\n) => {\n  /**\n   * These are the schemas of the components used in the content type, organised\n   * by their uid.\n   */\n  components: ComponentsDictionary;\n  document?: Document;\n  meta?: FindOne.Response['meta'];\n  isLoading: boolean;\n  /**\n   * This is the schema of the content type, it is not the same as the layout.\n   */\n  schema?: Schema;\n  schemas?: Schema[];\n  hasError?: boolean;\n  refetch: () => void;\n  validate: (document: Document) => null | FormErrors;\n  /**\n   * Get the document's title\n   */\n  getTitle: (mainField: string) => string;\n  /**\n   * Get the initial form values for the document\n   */\n  getInitialFormValues: (isCreatingDocument?: boolean) => AnyData | undefined;\n};\n\n/* -------------------------------------------------------------------------------------------------\n * useDocument\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * @alpha\n * @public\n * @description Returns a document based on the model, collection type & id passed as arguments.\n * Also extracts its schema from the redux cache to be used for creating a validation schema.\n * @example\n * ```tsx\n * const { id, model, collectionType } = useParams<{ id: string; model: string; collectionType: string }>();\n *\n * if(!model || !collectionType) return null;\n *\n * const { document, isLoading, validate } = useDocument({ documentId: id, model, collectionType, params: { locale: 'en-GB' } })\n * const { update } = useDocumentActions()\n *\n * const onSubmit = async (document: Document) => {\n *  const errors = validate(document);\n *\n *  if(errors) {\n *      // handle errors\n *  }\n *\n *  await update({ collectionType, model, id }, document)\n * }\n * ```\n *\n * @see {@link https://contributor.strapi.io/docs/core/content-manager/hooks/use-document} for more information\n */\nconst useDocument: UseDocument = (args, opts) => {\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n  const { formatMessage } = useIntl();\n\n  const {\n    currentData: data,\n    isLoading: isLoadingDocument,\n    isFetching: isFetchingDocument,\n    error,\n    refetch,\n  } = useGetDocumentQuery(args, {\n    ...opts,\n    skip: (!args.documentId && args.collectionType !== SINGLE_TYPES) || opts?.skip,\n  });\n  const document = data?.data;\n  const meta = data?.meta;\n\n  const {\n    components,\n    schema,\n    schemas,\n    isLoading: isLoadingSchema,\n  } = useContentTypeSchema(args.model);\n  const isSingleType = schema?.kind === 'singleType';\n\n  const getTitle = (mainField: string) => {\n    // Always use mainField if it's not an id\n    if (mainField !== 'id' && document?.[mainField]) {\n      return document[mainField];\n    }\n\n    // When it's a singleType without a mainField, use the contentType displayName\n    if (isSingleType && schema?.info.displayName) {\n      return schema.info.displayName;\n    }\n\n    // Otherwise, use a fallback\n    return formatMessage({\n      id: 'content-manager.containers.untitled',\n      defaultMessage: 'Untitled',\n    });\n  };\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    }\n  }, [toggleNotification, error, formatAPIError, args.collectionType]);\n\n  const validationSchema = React.useMemo(() => {\n    if (!schema) {\n      return null;\n    }\n\n    return createYupSchema(schema.attributes, components);\n  }, [schema, components]);\n\n  const validate = React.useCallback(\n    (document: Modules.Documents.AnyDocument): FormErrors | null => {\n      if (!validationSchema) {\n        throw new Error(\n          'There is no validation schema generated, this is likely due to the schema not being loaded yet.'\n        );\n      }\n\n      try {\n        validationSchema.validateSync(document, { abortEarly: false, strict: true });\n        return null;\n      } catch (error) {\n        if (error instanceof ValidationError) {\n          return getYupValidationErrors(error);\n        }\n\n        throw error;\n      }\n    },\n    [validationSchema]\n  );\n\n  /**\n   * Here we prepare the form for editing, we need to:\n   * - remove prohibited fields from the document (passwords | ADD YOURS WHEN THERES A NEW ONE)\n   * - swap out count objects on relations for empty arrays\n   * - set __temp_key__ on array objects for drag & drop\n   *\n   * We also prepare the form for new documents, so we need to:\n   * - set default values on fields\n   */\n  const getInitialFormValues = React.useCallback(\n    (isCreatingDocument: boolean = false) => {\n      if ((!document && !isCreatingDocument && !isSingleType) || !schema) {\n        return undefined;\n      }\n\n      /**\n       * Check that we have an ID so we know the\n       * document has been created in some way.\n       */\n      const form = document?.id ? document : createDefaultForm(schema, components);\n\n      return transformDocument(schema, components)(form);\n    },\n    [document, isSingleType, schema, components]\n  );\n\n  const isLoading = isLoadingDocument || isFetchingDocument || isLoadingSchema;\n  const hasError = !!error;\n\n  return {\n    components,\n    document,\n    meta,\n    isLoading,\n    hasError,\n    schema,\n    schemas,\n    validate,\n    getTitle,\n    getInitialFormValues,\n    refetch,\n  } satisfies ReturnType<UseDocument>;\n};\n\n/* -------------------------------------------------------------------------------------------------\n * useDoc\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * @internal this hook uses the router to extract the model, collection type & id from the url.\n * therefore, it shouldn't be used outside of the content-manager because it won't work as intended.\n */\nconst useDoc = () => {\n  const { id, slug, collectionType, origin } = useParams<{\n    id: string;\n    origin: string;\n    slug: string;\n    collectionType: string;\n  }>();\n  const [{ query }] = useQueryParams();\n  const params = React.useMemo(() => buildValidParams(query), [query]);\n\n  if (!collectionType) {\n    throw new Error('Could not find collectionType in url params');\n  }\n\n  if (!slug) {\n    throw new Error('Could not find model in url params');\n  }\n\n  const document = useDocument(\n    { documentId: origin || id, model: slug, collectionType, params },\n    {\n      skip: id === 'create' || (!origin && !id && collectionType !== SINGLE_TYPES),\n    }\n  );\n\n  const returnId = origin || id === 'create' ? undefined : id;\n\n  return {\n    collectionType,\n    model: slug,\n    id: returnId,\n    ...document,\n  };\n};\n\n/**\n * @public\n * @experimental\n * Content manager context hooks for plugin development.\n * Make sure to use this hook inside the content manager.\n */\nconst useContentManagerContext = () => {\n  const {\n    collectionType,\n    model,\n    id,\n    components,\n    isLoading: isLoadingDoc,\n    schema,\n    schemas,\n  } = useDoc();\n\n  const layout = useDocumentLayout(model);\n\n  const form = useForm<unknown>('useContentManagerContext', (state) => state);\n\n  const isSingleType = collectionType === SINGLE_TYPES;\n  const slug = model;\n  const isCreatingEntry = id === 'create';\n\n  const {} = useContentTypeSchema();\n\n  const isLoading = isLoadingDoc || layout.isLoading;\n  const error = layout.error;\n\n  return {\n    error,\n    isLoading,\n\n    // Base metadata\n    model,\n    collectionType,\n    id,\n    slug,\n    isCreatingEntry,\n    isSingleType,\n    hasDraftAndPublish: schema?.options?.draftAndPublish ?? false,\n\n    // All schema infos\n    components,\n    contentType: schema,\n    contentTypes: schemas,\n\n    // Form state\n    form,\n\n    // layout infos\n    layout,\n  };\n};\n\nexport { useDocument, useDoc, useContentManagerContext };\nexport type { UseDocument, UseDocumentArgs, Document, Schema, ComponentsDictionary };\n", "import * as React from 'react';\n\nimport { SerializedError } from '@reduxjs/toolkit';\nimport {\n  useNotification,\n  useStrapiApp,\n  useAPIErrorHandler,\n  useQueryParams,\n} from '@strapi/admin/strapi-admin';\n\nimport { HOOKS } from '../constants/hooks';\nimport { useGetContentTypeConfigurationQuery } from '../services/contentTypes';\nimport { BaseQueryError } from '../utils/api';\nimport { getMainField } from '../utils/attributes';\n\nimport { useContentTypeSchema } from './useContentTypeSchema';\nimport {\n  type ComponentsDictionary,\n  type Document,\n  type Schema,\n  useDoc,\n  useDocument,\n} from './useDocument';\n\nimport type { ComponentConfiguration } from '../../../shared/contracts/components';\nimport type {\n  Metadatas,\n  FindContentTypeConfiguration,\n  Settings,\n} from '../../../shared/contracts/content-types';\nimport type { Filters, InputProps, Table } from '@strapi/admin/strapi-admin';\nimport type { Schema as SchemaUtils } from '@strapi/types';\n\ntype LayoutOptions = Schema['options'] & Schema['pluginOptions'] & object;\n\ninterface LayoutSettings extends Settings {\n  displayName?: string;\n  icon?: never;\n}\n\ninterface ListFieldLayout\n  extends Table.Header<Document, ListFieldLayout>,\n    Pick<Filters.Filter, 'mainField'> {\n  attribute: SchemaUtils.Attribute.AnyAttribute | { type: 'custom' };\n}\n\ninterface ListLayout {\n  layout: ListFieldLayout[];\n  components?: never;\n  metadatas: {\n    [K in keyof Metadatas]: Metadatas[K]['list'];\n  };\n  options: LayoutOptions;\n  settings: LayoutSettings;\n}\ninterface EditFieldSharedProps\n  extends Omit<InputProps, 'hint' | 'label' | 'type'>,\n    Pick<Filters.Filter, 'mainField'> {\n  hint?: string;\n  label: string;\n  size: number;\n  unique?: boolean;\n  visible?: boolean;\n}\n\n/**\n * Map over all the types in Attribute Types and use that to create a union of new types where the attribute type\n * is under the property attribute and the type is under the property type.\n */\ntype EditFieldLayout = {\n  [K in SchemaUtils.Attribute.Kind]: EditFieldSharedProps & {\n    attribute: Extract<SchemaUtils.Attribute.AnyAttribute, { type: K }>;\n    type: K;\n  };\n}[SchemaUtils.Attribute.Kind];\n\ninterface EditLayout {\n  layout: Array<Array<EditFieldLayout[]>>;\n  components: {\n    [uid: string]: {\n      layout: Array<EditFieldLayout[]>;\n      settings: ComponentConfiguration['settings'] & {\n        displayName?: string;\n        icon?: string;\n      };\n    };\n  };\n  metadatas: {\n    [K in keyof Metadatas]: Metadatas[K]['edit'];\n  };\n  options: LayoutOptions;\n  settings: LayoutSettings;\n}\n\ntype UseDocumentLayout = (model: string) => {\n  error?: BaseQueryError | SerializedError;\n  isLoading: boolean;\n  /**\n   * This is the layout for the edit view,\n   */\n  edit: EditLayout;\n  list: ListLayout;\n};\n\n/* -------------------------------------------------------------------------------------------------\n * useDocumentLayout\n * -----------------------------------------------------------------------------------------------*/\n\nconst DEFAULT_SETTINGS = {\n  bulkable: false,\n  filterable: false,\n  searchable: false,\n  pagination: false,\n  defaultSortBy: '',\n  defaultSortOrder: 'asc',\n  mainField: 'id',\n  pageSize: 10,\n};\n\n/**\n * @alpha\n * @description This hook is used to get the layouts for either the edit view or list view of a specific content-type\n * including the layouts for the components used in the content-type. It also runs the mutation hook waterfall so the data\n * is consistent wherever it is used. It's a light wrapper around the `useDocument` hook, but provides the `skip` option a document\n * is not fetched, however, it does fetch the schemas & components if they do not already exist in the cache.\n *\n * If the fetch fails, it will display a notification to the user.\n *\n * @example\n * ```tsx\n * const { model } = useParams<{ model: string }>();\n * const { edit: { schema: layout } } = useDocumentLayout(model);\n *\n * return layout.map(panel => panel.map(row => row.map(field => <Field.Root {...field} />)))\n * ```\n *\n */\nconst useDocumentLayout: UseDocumentLayout = (model) => {\n  const { schema, components } = useDocument({ model, collectionType: '' }, { skip: true });\n  const [{ query }] = useQueryParams();\n  const runHookWaterfall = useStrapiApp('useDocumentLayout', (state) => state.runHookWaterfall);\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n  const { isLoading: isLoadingSchemas, schemas } = useContentTypeSchema();\n\n  const {\n    data,\n    isLoading: isLoadingConfigs,\n    error,\n    isFetching: isFetchingConfigs,\n  } = useGetContentTypeConfigurationQuery(model);\n\n  const isLoading = isLoadingSchemas || isFetchingConfigs || isLoadingConfigs;\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    }\n  }, [error, formatAPIError, toggleNotification]);\n\n  const editLayout = React.useMemo(\n    () =>\n      data && !isLoading\n        ? formatEditLayout(data, { schemas, schema, components })\n        : ({\n            layout: [],\n            components: {},\n            metadatas: {},\n            options: {},\n            settings: DEFAULT_SETTINGS,\n          } as EditLayout),\n    [data, isLoading, schemas, schema, components]\n  );\n\n  const listLayout = React.useMemo(() => {\n    return data && !isLoading\n      ? formatListLayout(data, { schemas, schema, components })\n      : ({\n          layout: [],\n          metadatas: {},\n          options: {},\n          settings: DEFAULT_SETTINGS,\n        } as ListLayout);\n  }, [data, isLoading, schemas, schema, components]);\n\n  const { layout: edit } = React.useMemo(\n    () =>\n      runHookWaterfall(HOOKS.MUTATE_EDIT_VIEW_LAYOUT, {\n        layout: editLayout,\n        query,\n      }),\n    [editLayout, query, runHookWaterfall]\n  );\n\n  return {\n    error,\n    isLoading,\n    edit,\n    list: listLayout,\n  } satisfies ReturnType<UseDocumentLayout>;\n};\n\n/* -------------------------------------------------------------------------------------------------\n * useDocLayout\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * @internal this hook uses the internal useDoc hook, as such it shouldn't be used outside of the\n * content-manager because it won't work as intended.\n */\nconst useDocLayout = () => {\n  const { model } = useDoc();\n  return useDocumentLayout(model);\n};\n\n/* -------------------------------------------------------------------------------------------------\n * formatEditLayout\n * -----------------------------------------------------------------------------------------------*/\ntype LayoutData = FindContentTypeConfiguration.Response['data'];\n\n/**\n * @internal\n * @description takes the configuration data, the schema & the components used in the schema and formats the edit view\n * versions of the schema & components. This is then used to render the edit view of the content-type.\n */\nconst formatEditLayout = (\n  data: LayoutData,\n  {\n    schemas,\n    schema,\n    components,\n  }: { schemas: Schema[]; schema?: Schema; components: ComponentsDictionary }\n): EditLayout => {\n  let currentPanelIndex = 0;\n  /**\n   * The fields arranged by the panels, new panels are made for dynamic zones only.\n   */\n  const panelledEditAttributes = convertEditLayoutToFieldLayouts(\n    data.contentType.layouts.edit,\n    schema?.attributes,\n    data.contentType.metadatas,\n    { configurations: data.components, schemas: components },\n    schemas\n  ).reduce<Array<EditFieldLayout[][]>>((panels, row) => {\n    if (row.some((field) => field.type === 'dynamiczone')) {\n      panels.push([row]);\n      currentPanelIndex += 2;\n    } else {\n      if (!panels[currentPanelIndex]) {\n        panels.push([row]);\n      } else {\n        panels[currentPanelIndex].push(row);\n      }\n    }\n\n    return panels;\n  }, []);\n\n  const componentEditAttributes = Object.entries(data.components).reduce<EditLayout['components']>(\n    (acc, [uid, configuration]) => {\n      acc[uid] = {\n        layout: convertEditLayoutToFieldLayouts(\n          configuration.layouts.edit,\n          components[uid].attributes,\n          configuration.metadatas,\n          { configurations: data.components, schemas: components }\n        ),\n        settings: {\n          ...configuration.settings,\n          icon: components[uid].info.icon,\n          displayName: components[uid].info.displayName,\n        },\n      };\n      return acc;\n    },\n    {}\n  );\n\n  const editMetadatas = Object.entries(data.contentType.metadatas).reduce<EditLayout['metadatas']>(\n    (acc, [attribute, metadata]) => {\n      return {\n        ...acc,\n        [attribute]: metadata.edit,\n      };\n    },\n    {}\n  );\n\n  return {\n    layout: panelledEditAttributes,\n    components: componentEditAttributes,\n    metadatas: editMetadatas,\n    settings: {\n      ...data.contentType.settings,\n      displayName: schema?.info.displayName,\n    },\n    options: {\n      ...schema?.options,\n      ...schema?.pluginOptions,\n      ...data.contentType.options,\n    },\n  };\n};\n\n/* -------------------------------------------------------------------------------------------------\n * convertEditLayoutToFieldLayouts\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * @internal\n * @description takes the edit layout from either a content-type or a component\n * and formats it into a generic object that can be used to correctly render\n * the form fields.\n */\nconst convertEditLayoutToFieldLayouts = (\n  rows: LayoutData['contentType']['layouts']['edit'],\n  attributes: Schema['attributes'] = {},\n  metadatas: Metadatas,\n  components?: {\n    configurations: Record<string, ComponentConfiguration>;\n    schemas: ComponentsDictionary;\n  },\n  schemas: Schema[] = []\n) => {\n  return rows.map((row) =>\n    row\n      .map((field) => {\n        const attribute = attributes[field.name];\n\n        if (!attribute) {\n          return null;\n        }\n\n        const { edit: metadata } = metadatas[field.name];\n\n        const settings: Partial<Settings> =\n          attribute.type === 'component' && components\n            ? components.configurations[attribute.component].settings\n            : {};\n\n        return {\n          attribute,\n          disabled: !metadata.editable,\n          hint: metadata.description,\n          label: metadata.label ?? '',\n          name: field.name,\n          // @ts-expect-error – mainField does exist on the metadata for a relation.\n          mainField: getMainField(attribute, metadata.mainField || settings.mainField, {\n            schemas,\n            components: components?.schemas ?? {},\n          }),\n          placeholder: metadata.placeholder ?? '',\n          required: attribute.required ?? false,\n          size: field.size,\n          unique: 'unique' in attribute ? attribute.unique : false,\n          visible: metadata.visible ?? true,\n          type: attribute.type,\n        };\n      })\n      .filter((field) => field !== null)\n  ) as EditFieldLayout[][];\n};\n\n/* -------------------------------------------------------------------------------------------------\n * formatListLayout\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * @internal\n * @description takes the complete configuration data, the schema & the components used in the schema and\n * formats a list view layout for the content-type. This is much simpler than the edit view layout as there\n * are less options to consider.\n */\nconst formatListLayout = (\n  data: LayoutData,\n  {\n    schemas,\n    schema,\n    components,\n  }: { schemas: Schema[]; schema?: Schema; components: ComponentsDictionary }\n): ListLayout => {\n  const listMetadatas = Object.entries(data.contentType.metadatas).reduce<ListLayout['metadatas']>(\n    (acc, [attribute, metadata]) => {\n      return {\n        ...acc,\n        [attribute]: metadata.list,\n      };\n    },\n    {}\n  );\n  /**\n   * The fields arranged by the panels, new panels are made for dynamic zones only.\n   */\n  const listAttributes = convertListLayoutToFieldLayouts(\n    data.contentType.layouts.list,\n    schema?.attributes,\n    listMetadatas,\n    { configurations: data.components, schemas: components },\n    schemas\n  );\n\n  return {\n    layout: listAttributes,\n    settings: { ...data.contentType.settings, displayName: schema?.info.displayName },\n    metadatas: listMetadatas,\n    options: {\n      ...schema?.options,\n      ...schema?.pluginOptions,\n      ...data.contentType.options,\n    },\n  };\n};\n\n/* -------------------------------------------------------------------------------------------------\n * convertListLayoutToFieldLayouts\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * @internal\n * @description takes the columns from the list view configuration and formats them into a generic object\n * combinining metadata and attribute data.\n *\n * @note We do use this to reformat the list of strings when updating the displayed headers for the list view.\n */\nconst convertListLayoutToFieldLayouts = (\n  columns: LayoutData['contentType']['layouts']['list'],\n  attributes: Schema['attributes'] = {},\n  metadatas: ListLayout['metadatas'],\n  components?: {\n    configurations: Record<string, ComponentConfiguration>;\n    schemas: ComponentsDictionary;\n  },\n  schemas: Schema[] = []\n) => {\n  return columns\n    .map((name) => {\n      const attribute = attributes[name];\n\n      if (!attribute) {\n        return null;\n      }\n\n      const metadata = metadatas[name];\n\n      const settings: Partial<Settings> =\n        attribute.type === 'component' && components\n          ? components.configurations[attribute.component].settings\n          : {};\n\n      return {\n        attribute,\n        label: metadata.label ?? '',\n        mainField: getMainField(attribute, metadata.mainField || settings.mainField, {\n          schemas,\n          components: components?.schemas ?? {},\n        }),\n        name: name,\n        searchable: metadata.searchable ?? true,\n        sortable: metadata.sortable ?? true,\n      } satisfies ListFieldLayout;\n    })\n    .filter((field) => field !== null) as ListFieldLayout[];\n};\n\nexport {\n  useDocLayout,\n  useDocumentLayout,\n  convertListLayoutToFieldLayouts,\n  convertEditLayoutToFieldLayouts,\n  DEFAULT_SETTINGS,\n};\nexport type { EditLayout, EditFieldLayout, ListLayout, ListFieldLayout, UseDocumentLayout };\n", "import clone from 'lodash/clone';\nimport toPath from 'lodash/toPath';\n\n/**\n * Deeply get a value from an object via its path.\n */\nexport function getIn(obj: any, key: string | string[], def?: any, pathStartIndex: number = 0) {\n  const path = toPath(key);\n  while (obj && pathStartIndex < path.length) {\n    obj = obj[path[pathStartIndex++]];\n  }\n\n  // check if path is not in the end\n  if (pathStartIndex !== path.length && !obj) {\n    return def;\n  }\n\n  return obj === undefined ? def : obj;\n}\n\n/** @internal is the given object an Object? */\nexport const isObject = (obj: any): obj is object =>\n  obj !== null && typeof obj === 'object' && !Array.isArray(obj);\n\n/** @internal is the given object an integer? */\nexport const isInteger = (obj: any): boolean => String(Math.floor(Number(obj))) === obj;\n\n/**\n * Deeply set a value from in object via its path. If the value at `path`\n * has changed, return a shallow copy of obj with `value` set at `path`.\n * If `value` has not changed, return the original `obj`.\n *\n * Existing objects / arrays along `path` are also shallow copied. Sibling\n * objects along path retain the same internal js reference. Since new\n * objects / arrays are only created along `path`, we can test if anything\n * changed in a nested structure by comparing the object's reference in\n * the old and new object, similar to how russian doll cache invalidation\n * works.\n *\n * In earlier versions of this function, which used cloneDeep, there were\n * issues whereby settings a nested value would mutate the parent\n * instead of creating a new object. `clone` avoids that bug making a\n * shallow copy of the objects along the update path\n * so no object is mutated in place.\n *\n * Before changing this function, please read through the following\n * discussions.\n *\n * @see https://github.com/developit/linkstate\n * @see https://github.com/jaredpalmer/formik/pull/123\n */\nexport function setIn(obj: any, path: string, value: any): any {\n  const res: any = clone(obj); // this keeps inheritance when obj is a class\n  let resVal: any = res;\n  let i = 0;\n  const pathArray = toPath(path);\n\n  for (; i < pathArray.length - 1; i++) {\n    const currentPath: string = pathArray[i];\n    const currentObj: any = getIn(obj, pathArray.slice(0, i + 1));\n\n    if (currentObj && (isObject(currentObj) || Array.isArray(currentObj))) {\n      resVal = resVal[currentPath] = clone(currentObj);\n    } else {\n      const nextPath: string = pathArray[i + 1];\n      resVal = resVal[currentPath] = isInteger(nextPath) && Number(nextPath) >= 0 ? [] : {};\n    }\n  }\n\n  // Return original object if new value is the same as current\n  if ((i === 0 ? obj : resVal)[pathArray[i]] === value) {\n    return obj;\n  }\n\n  if (value === undefined) {\n    delete resVal[pathArray[i]];\n  } else {\n    resVal[pathArray[i]] = value;\n  }\n\n  // If the path array has a single element, the loop did not run.\n  // Deleting on `resVal` had no effect in this scenario, so we delete on the result instead.\n  if (i === 0 && value === undefined) {\n    delete res[pathArray[i]];\n  }\n\n  return res;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAMA,eAAe;AACrB,IAAMC,mBAAmB;;;;ACyBzB,IAAMC,cAAcC,kBAAkBC,gBAAgB;EACpDC,kBAAkB;EAClBC,WAAW,CAACC,aAAa;IACvBC,mBAAmBD,QAAQE,SAKzB;MACAC,OAAO,CAAC,EAAEC,OAAOC,UAAUC,OAAM,OAAQ;QACvCC,KAAK,qCAAqCH,KAAAA,eAAoBC,QAAAA;QAC9DG,QAAQ;QACRC,QAAQ;UACNH;QACF;;MAEFI,iBAAiB,CAACC,SAASC,OAAO,EAAER,MAAK,MAAE;AACzC,YAAIQ,OAAO;AACT,iBAAO,CAAA;QACT;AAEA,eAAO;UAAC;YAAEC,MAAM;YAAYC,IAAI,GAAGV,KAAM;UAAO;UAAG;QAAqB;MAC1E;IACF,CAAA;IACAW,eAAef,QAAQE,SAMrB;MACAC,OAAO,CAAC,EAAEC,OAAOC,UAAUW,MAAMV,OAAM,OAAQ;QAC7CC,KAAK,qCAAqCH,KAAAA,UAAeC,QAAAA;QACzDG,QAAQ;QACRQ;QACAP,QAAQ;UACNH;QACF;;MAEFI,iBAAiB,CAACC,SAASM,QAAQ,EAAEb,MAAK,MAAO;QAC/C;UAAES,MAAM;UAAYC,IAAI,GAAGV,KAAM;QAAO;QACxC;UAAES,MAAM;UAAmBC,IAAIV;QAAM;QACrC;MACD;IACH,CAAA;;;;;IAKAc,gBAAgBlB,QAAQE,SAMtB;MACAC,OAAO,CAAC,EAAEC,OAAOY,MAAMV,OAAM,OAAQ;QACnCC,KAAK,qCAAqCH,KAAAA;QAC1CI,QAAQ;QACRQ;QACAP,QAAQ;UACNH;QACF;;MAEFI,iBAAiB,CAACS,QAAQF,QAAQ,EAAEb,MAAK,MAAO;QAC9C;UAAES,MAAM;UAAYC,IAAI,GAAGV,KAAM;QAAO;QACxC;QACA;UAAES,MAAM;UAAmBC,IAAIV;QAAM;QACrC;MACD;MACDgB,mBAAmB,CAACC,UAA2BC,MAAMC,QAAAA;AAMnD,YAAI,EAAE,UAAUF,aAAaE,IAAInB,UAAU,kCAAkC;AAC3E,iBAAO;YACLY,MAAMK;YACNC,MAAM;cACJE,iBAAiB,CAAA;cACjBC,kBAAkB,CAAA;YACpB;UACF;QACF;AAEA,eAAOJ;MACT;IACF,CAAA;IACAK,gBAAgB1B,QAAQE,SAOtB;MACAC,OAAO,CAAC,EAAEwB,gBAAgBvB,OAAOwB,YAAYtB,OAAM,OAAQ;QACzDC,KAAK,oBAAoBoB,cAAAA,IAAkBvB,KAAAA,GACzCuB,mBAAmBE,gBAAgBD,aAAa,IAAIA,UAAAA,KAAe,EAAA;QAErEpB,QAAQ;QACRC,QAAQ;UACNH;QACF;;MAEFI,iBAAiB,CAACC,SAASM,QAAQ,EAAEU,gBAAgBvB,MAAK,MAAO;QAC/D;UAAES,MAAM;UAAYC,IAAIa,mBAAmBE,eAAe,GAAGzB,KAAM,UAASA;QAAM;QAClF;MACD;IACH,CAAA;IACA0B,qBAAqB9B,QAAQE,SAG3B;MACAC,OAAO,CAAC,EAAEC,OAAOE,QAAQ,GAAGyB,KAAM,OAAM;QACtCxB,KAAK,qCAAqCH,KAAAA;QAC1CI,QAAQ;QACRQ,MAAMe;QACNtB,QAAQ;UACNH;QACF;;MAEFI,iBAAiB,CAACsB,MAAMf,QAAQ,EAAEb,MAAK,MAAO;QAC5C;UAAES,MAAM;UAAYC,IAAI,GAAGV,KAAM;QAAO;QACxC;MACD;IACH,CAAA;IACA6B,iBAAiBjC,QAAQE,SASvB;MACAC,OAAO,CAAC,EAAEwB,gBAAgBvB,OAAOwB,YAAYtB,OAAM,OAAQ;QACzDC,KAAKqB,aACD,oBAAoBD,cAAAA,IAAkBvB,KAAM,IAAGwB,UAAAA,qBAC/C,oBAAoBD,cAAAA,IAAkBvB,KAAM;QAChDI,QAAQ;QACRC,QAAQ;UACNH;QACF;;MAEFI,iBAAiB,CAACC,SAASM,QAAQ,EAAEU,gBAAgBvB,OAAOwB,WAAU,MAAE;AACtE,eAAO;UACL;YACEf,MAAM;YACNC,IAAIa,mBAAmBE,eAAe,GAAGzB,KAAAA,IAASwB,UAAW,KAAIxB;UACnE;UACA;YAAES,MAAM;YAAYC,IAAI,GAAGV,KAAM;UAAO;UACxC;UACA;YAAES,MAAM;YAAmBC,IAAIV;UAAM;UACrC;QACD;MACH;IACF,CAAA;;;;;IAKA8B,iBAAiBlC,QAAQG,MAOvB;MACAA,OAAO,CAAC,EAAEC,OAAOE,OAAM,OAAQ;QAC7BC,KAAK,qCAAqCH,KAAAA;QAC1CI,QAAQ;QACRC,QAAQ;UACNH,YAAQ6B,qBAAU7B,QAAQ;YAAE8B,QAAQ;UAAK,CAAA;QAC3C;;MAEFC,cAAc,CAAClB,QAAQF,QAAQM,QAAAA;AAC7B,eAAO;UACL;YAAEV,MAAM;YAAYC,IAAI;UAAW;UACnC;YAAED,MAAM;YAAYC,IAAI,GAAGS,IAAInB,KAAK;UAAQ;UACxCe,IAAAA,iCAAQmB,QAAQC,IAAI,CAAC,EAAEX,WAAU,OAAQ;YAC3Cf,MAAM;YACNC,IAAI,GAAGS,IAAInB,KAAK,IAAIwB,UAAAA;UACtB,QAAO,CAAA;QACR;MACH;IACF,CAAA;IACAY,uBAAuBxC,QAAQG,MAW7B;MACAA,OAAO,CAAC,EAAEwB,gBAAgBvB,OAAOwB,YAAYtB,OAAM,OAAQ;QACzDC,KAAKqB,aACD,oBAAoBD,cAAAA,IAAkBvB,KAAM,IAAGwB,UAAAA,iCAC/C,oBAAoBD,cAAAA,IAAkBvB,KAAM;QAChDI,QAAQ;QACRC,QAAQ;UACNH;QACF;;IAEJ,CAAA;IACAmC,aAAazC,QAAQG,MAOnB;;MAEAuC,SAAS,OACP,EAAEf,gBAAgBvB,OAAOwB,YAAYtB,OAAM,GAC3CqC,MACAC,YACAC,cAAAA;AAEA,cAAMC,MAAM,MAAMD,UAAU;UAC1BtC,KAAK,oBAAoBoB,cAAe,IAAGvB,KAAM,GAAEwB,aAAa,IAAIA,UAAAA,KAAe,EAAA;UACnFpB,QAAQ;UACRC,QAAQ;YACNH;UACF;QACF,CAAA;AAOA,YAAIwC,IAAIlC,SAASkC,IAAIlC,MAAMmC,SAAS,mBAAmBpB,mBAAmBE,cAAc;AACtF,iBAAO;YAAEb,MAAM;cAAEgC,UAAUC;YAAU;YAAGrC,OAAOqC;UAAU;QAC3D;AAEA,eAAOH;MACT;MACAT,cAAc,CAAClB,QAAQF,QAAQ,EAAEU,gBAAgBvB,OAAOwB,WAAU,MAAE;AAClE,eAAO;;UAEL;YACEf,MAAM;YACNC,IACEa,mBAAmBE,eACf,GAAGzB,KAAAA,IAASe,UAAU,gBAAgBA,SAASA,OAAOS,aAAaA,UAAAA,KACnExB;UACR;;UAEA;YACES,MAAM;YACNC,IAAI,GAAGV,KAAM;UACf;QACD;MACH;IACF,CAAA;IACA8C,2BAA2BlD,QAAQG,MAKjC;MACAA,OAAO,CAAC,EAAEC,OAAO,GAAGE,OAAAA,OAAc;QAChCC,KAAK,qCAAqCH,KAAAA;QAC1CI,QAAQ;QACRC,QAAQ;UACNH;QACF;;MAEFc,mBAAmB,CAACC,aAAsDA,SAASL;IACrF,CAAA;;;;IAIAmC,iBAAiBnD,QAAQE,SAQvB;MACAC,OAAO,CAAC,EAAEwB,gBAAgBvB,OAAOwB,YAAYtB,QAAQU,KAAI,OAAQ;QAC/DT,KAAKqB,aACD,oBAAoBD,cAAAA,IAAkBvB,KAAM,IAAGwB,UAAAA,qBAC/C,oBAAoBD,cAAAA,IAAkBvB,KAAM;QAChDI,QAAQ;QACRQ;QACAP,QAAQ;UACNH;QACF;;MAEFI,iBAAiB,CAACC,SAASM,QAAQ,EAAEU,gBAAgBvB,OAAOwB,WAAU,MAAE;AACtE,eAAO;UACL;YACEf,MAAM;YACNC,IAAIa,mBAAmBE,eAAe,GAAGzB,KAAAA,IAASwB,UAAW,KAAIxB;UACnE;UACA;YAAES,MAAM;YAAYC,IAAI,GAAGV,KAAM;UAAO;UACxC;UACA;QACD;MACH;IACF,CAAA;IACAgD,sBAAsBpD,QAAQE,SAG5B;MACAC,OAAO,CAAC,EAAEC,OAAOE,QAAQ,GAAGyB,KAAM,OAAM;QACtCxB,KAAK,qCAAqCH,KAAAA;QAC1CI,QAAQ;QACRQ,MAAMe;QACNtB,QAAQ;UACNH;QACF;;MAEFI,iBAAiB,CAACsB,MAAMf,QAAQ,EAAEb,OAAOiD,YAAW,MAClDA,YAAYd,IAAI,CAACzB,QAAQ;QAAED,MAAM;QAAYC,IAAI,GAAGV,KAAAA,IAASU,EAAAA;QAAK;IACtE,CAAA;IACAwC,gBAAgBtD,QAAQE,SAQtB;MACAC,OAAO,CAAC,EAAEwB,gBAAgBvB,OAAOwB,YAAYZ,MAAMV,OAAM,OAAQ;QAC/DC,KAAK,oBAAoBoB,cAAe,IAAGvB,KAAM,GAAEwB,aAAa,IAAIA,UAAAA,KAAe,EAAA;QACnFpB,QAAQ;QACRQ;QACAP,QAAQ;UACNH;QACF;;MAEFI,iBAAiB,CAACC,SAASM,QAAQ,EAAEU,gBAAgBvB,OAAOwB,WAAU,MAAE;AACtE,eAAO;UACL;YACEf,MAAM;YACNC,IAAIa,mBAAmBE,eAAe,GAAGzB,KAAAA,IAASwB,UAAW,KAAIxB;UACnE;UACA;UACA;YAAES,MAAM;YAAmBC,IAAIV;UAAM;UACrC;UACA;QACD;MACH;MACA,MAAMmD,eAAe,EAAEvC,MAAM,GAAGwC,MAAO,GAAE,EAAEC,UAAUC,eAAc,GAAE;AAEnE,cAAMC,cAAcF,SAClB9D,YAAYiE,KAAKC,gBAAgB,eAAeL,OAAO,CAACM,UAAAA;AACtDC,iBAAOC,OAAOF,MAAM9C,MAAMA,IAAAA;QAC5B,CAAA,CAAA;AAEF,YAAI;AACF,gBAAM0C;QACR,QAAQ;AAENC,sBAAYM,KAAI;QAClB;MACF;MACA7C,mBAAmB,CAACC,UAA2BC,MAAMC,QAAAA;AAMnD,YAAI,EAAE,UAAUF,aAAaE,IAAInB,UAAU,kCAAkC;AAC3E,iBAAO;YACLY,MAAMK;YACNC,MAAM;cACJE,iBAAiB,CAAA;cACjBC,kBAAkB,CAAA;YACpB;UACF;QACF;AAEA,eAAOJ;MACT;IACF,CAAA;IACA6C,mBAAmBlE,QAAQE,SAQzB;MACAC,OAAO,CAAC,EAAEwB,gBAAgBvB,OAAOwB,YAAYtB,QAAQU,KAAI,OAAQ;QAC/DT,KAAKqB,aACD,oBAAoBD,cAAAA,IAAkBvB,KAAM,IAAGwB,UAAAA,uBAC/C,oBAAoBD,cAAAA,IAAkBvB,KAAM;QAChDI,QAAQ;QACRQ;QACAP,QAAQ;UACNH;QACF;;MAEFI,iBAAiB,CAACC,SAASM,QAAQ,EAAEU,gBAAgBvB,OAAOwB,WAAU,MAAE;AACtE,eAAO;UACL;YACEf,MAAM;YACNC,IAAIa,mBAAmBE,eAAe,GAAGzB,KAAAA,IAASwB,UAAW,KAAIxB;UACnE;UACA;QACD;MACH;IACF,CAAA;IACA+D,wBAAwBnE,QAAQE,SAM9B;MACAC,OAAO,CAAC,EAAEC,OAAOE,QAAQ,GAAGyB,KAAM,OAAM;QACtCxB,KAAK,qCAAqCH,KAAAA;QAC1CI,QAAQ;QACRQ,MAAMe;QACNtB,QAAQ;UACNH;QACF;;MAEFI,iBAAiB,CAACsB,MAAMf,QAAQ,EAAEb,OAAOiD,YAAW,MAAO;QACtDA,GAAAA,YAAYd,IAAI,CAACzB,QAAQ;UAAED,MAAM;UAAqBC,IAAI,GAAGV,KAAAA,IAASU,EAAAA;UAAK;QAC9E;MACD;IACH,CAAA;;AAEJ,CAAA;AAEA,IAAM,EACJsD,8BACAC,0BACAC,2BACAC,2BACAC,gCACAC,4BACAC,yBACAC,yBACAC,qBACAC,mCACAC,mCACAC,4BACAC,iCACAC,2BACAC,8BACAC,kCAAiC,IAC/BxF;;;AC/cJ,IAAMyF,mBAAmB,CAAuBC,UAAAA;AAC9C,MAAI,CAACA,MAAO,QAAOA;AAGnB,QAAM,EAAEC,SAASC,GAAG,GAAGC,iBAAAA,IAAqB;IAC1C,GAAGH;IACH,GAAGI,OAAOC,QAAOL,+BAAOC,YAAW,CAAA,CAAA,EAAIK,OACrC,CAACC,KAAKC,YAAYJ,OAAOK,OAAOF,KAAKC,OAAAA,GACrC,CAAA,CACD;EACH;AAEA,SAAOL;AACT;AAIA,IAAMO,mBAAmB,CAACC,UAAAA;AACxB,SAAOA,MAAMC,SAASC;AACxB;;;;;;ACzCA,IAAMC,KAAK;AAEX,IAAMC,4BAA4B;AAClC,IAAMC,4BAA4B;AAElC,IAAMC,iBAAiB;EAACF;EAA2BC;AAA0B;AAE7E,IAAME,8BAA8B;AACpC,IAAMC,4BAA4B;AAClC,IAAMC,4BAA4B;AAClC,IAAMC,8BAA8B;AAEpC,IAAMC,uBAAuB;EAC3BR;EACGG,GAAAA;EACHC;EACAC;EACAC;EACAC;AACD;AAKA,IACKE,4CAA4C;EAChD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACD;;;ACXD,IAAMC,iBAAiB,CAACC,WAAyCC,aAAgC;EAC/FC,SAASC,YAAiBC;EAC1BC,KAAKC,OAAc;AACjB,QAAIL,QAAQM,WAAW,SAAS;AAC9B,aAAO;IACT;AAEA,QAAI,CAACP,UAAUI,UAAU;AACvB,aAAO;IACT;AAEA,QAAI,CAACE,OAAO;AACV,aAAO;IACT;AAEA,QAAIE,MAAMC,QAAQH,KAAAA,KAAUA,MAAMI,WAAW,GAAG;AAC9C,aAAO;IACT;AAEA,WAAO;EACT;;AAMF,IAAMC,kBAAkB,CACtBC,aAAmC,CAAA,GACnCC,aAAmC,CAAA,GACnCZ,UAA6B;EAAEM,QAAQ;AAAK,MAAC;AAE7C,QAAMO,oBAAoB,CAACF,gBAEtBG,QAAM,EACNC,MACCC,OAAOC,QAAQN,WAAAA,EAAYO,OAAoB,CAACC,KAAK,CAACC,MAAMrB,SAAU,MAAA;AACpE,QAAIsB,qBAAqBC,SAASF,IAAO,GAAA;AACvC,aAAOD;IACT;AAOA,UAAMI,cAAc;MAClBC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACD,EAACC,IAAI,CAACC,OAAOA,GAAGjC,WAAWC,OAAAA,CAAAA;AAE5B,UAAMiC,sBAAkBC,YAAAA,SAAQX,GAAAA,WAAAA;AAEhC,YAAQxB,UAAUoC,MAAI;MACpB,KAAK,aAAa;AAChB,cAAM,EAAExB,YAAAA,YAAU,IAAKC,WAAWb,UAAUqC,SAAS;AAErD,YAAIrC,UAAUsC,YAAY;AACxB,iBAAO;YACL,GAAGlB;YACH,CAACC,IAAAA,GAAOa,gBACFK,QAAK,EAAGC,GAAG1B,kBAAkBF,WAAAA,EAAY6B,SAAS,KAAA,CAAA,CAAA,EACtDpC,KAAKN,eAAeC,WAAWC,OAAAA,CAAAA;UACnC;eACK;AACL,iBAAO;YACL,GAAGmB;YACH,CAACC,IAAK,GAAEa,gBAAgBpB,kBAAkBF,WAAAA,EAAY6B,SAAQ,CAAA;UAChE;QACF;MACF;MACA,KAAK;AACH,eAAO;UACL,GAAGrB;UACH,CAACC,IAAAA,GAAOa,gBACFK,QAAK,EAAGC,GACNE,QACF,CACEC,SAAAA;;AAEA,kBAAM/B,eAAaC,8CAAa8B,6BAAMC,iBAAnB/B,mBAAiCD;AAEpD,kBAAMiC,aACH9B,QAAM,EACNC,MAAM;cACL4B,aAAiBE,QAAM,EAAG1C,SAAQ,EAAG2C,MAAM9B,OAAO+B,KAAKnC,UAAAA,CAAAA;YACzD,CAAA,EACC4B,SAAS,KAAA;AACZ,gBAAI,CAAC7B,aAAY;AACf,qBAAOiC;YACT;AAEA,mBAAOA,WAAWI,OAAOnC,kBAAkBF,WAAAA,CAAAA;WAIjDP,CAAAA,CAAAA,EAAAA,KAAKN,eAAeC,WAAWC,OAAAA,CAAAA;QACnC;MACF,KAAK;AACH,eAAO;UACL,GAAGmB;UACH,CAACC,IAAAA,GAAOa,gBACFQ,QAAK,CAACpC,UAAAA;AACR,gBAAI,CAACA,OAAO;AACV,qBAAW4C,OAAK,EAAGT,SAAS,IAAA;YAC9B,WAAWjC,MAAMC,QAAQH,KAAQ,GAAA;AAG/B,qBAAWiC,QAAK,EAAGC,GACbzB,QAAM,EAAGC,MAAM;gBACjBmC,IAAQC,QAAM,EAAGhD,SAAQ;cAC3B,CAAA,CAAA;uBAEO,OAAOE,UAAU,UAAU;AAIpC,qBAAWS,QAAM;mBACZ;AACL,qBACGmC,OAAK,EACL7C,KACC,cACA,oFACA,MAAM,KAAA;YAEZ;UACF,CAAA,CAAA;QAEJ;MACF;AACE,eAAO;UACL,GAAGe;UACH,CAACC,IAAAA,GAAOa,gBAAgBmB,sBAAsBrD,SAAAA,CAAAA;QAChD;IACJ;EACF,GAAG,CAAA,CAEL,CAAA,EAGCsD,QAAQ,IAAA;AAEb,SAAOxC,kBAAkBF,UAAAA;AAC3B;AAEA,IAAMyC,wBAAwB,CAC5BrD,cAAAA;AAKA,UAAQA,UAAUoC,MAAI;IACpB,KAAK;AACH,aAAWU,QAAM,EAAGS,QAAQ,SAAA;IAC9B,KAAK;AACH,aAAWC,QAAO;IACpB,KAAK;AACH,aAAWN,OAAK,EAAG7C,KAAK,YAAYF,YAAiBsD,MAAM,CAACnD,UAAAA;AAC1D,YAAI,CAACA,SAASE,MAAMC,QAAQH,KAAQ,GAAA;AAClC,iBAAO;eACF;AACL,iBAAO;QACT;MACF,CAAA;IACF,KAAK;IACL,KAAK;IACL,KAAK;AACH,aAAW8C,QAAM;IACnB,KAAK;AACH,aAAWN,QAAM,EAAGY,MAAMvD,YAAiBuD,KAAK;IAClD,KAAK;AACH,aAAWZ,QAAM,EAAGC,MAAM;QAAI/C,GAAAA,UAAU2D;QAAM;MAAK,CAAA;IACrD,KAAK;AACH,aAAWT,OAAK,EAAG7C,KAAK,UAAUF,YAAiBsD,MAAM,CAACnD,UAAAA;AAIxD,YAAI,CAACA,SAAU,OAAOA,UAAU,YAAYA,MAAMI,WAAW,GAAI;AAC/D,iBAAO;QACT;AAGA,YAAI,OAAOJ,UAAU,UAAU;AAC7B,cAAI;AACFsD,iBAAKC,UAAUvD,KAAAA;AACf,mBAAO;UACT,SAASwD,KAAK;AACZ,mBAAO;UACT;QACF;AAEA,YAAI;AACFF,eAAKG,MAAMzD,KAAAA;AAEX,iBAAO;QACT,SAASwD,KAAK;AACZ,iBAAO;QACT;MACF,CAAA;IACF,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACH,aAAWhB,QAAM;IACnB,KAAK;AACH,aACGA,QAAM,EACNS,QAAQvD,UAAUgE,QAAQ,IAAIC,OAAOjE,UAAUgE,KAAK,IAAI,oBAAA;IAC7D;AAIE,aAAWd,OAAK;EACpB;AACF;AAGA,IAAMgB,iBAAiB,CAA4BC,WAAAA;AACjD,UAAOA,iCAAQ1B,YACX0B,OAAO1B,SAAQ;;;IAIf0B;;AACN;AAcA,IAAM1C,wBAAsC,MAAM,CAAC0C,WAAAA;AACjD,SAAOD,eAAeC,MAAAA;AACxB;AAEA,IAAMzC,wBAAsC,CAAC1B,WAAWC,YAAY,CAACkE,WAAAA;AACnE,MAAIlE,QAAQM,WAAW,WAAW,CAACP,UAAUI,UAAU;AACrD,WAAO+D;EACT;AAEA,MAAInE,UAAUI,YAAY,cAAc+D,QAAQ;AAC9C,WAAOA,OAAO/D,SAASD,YAAiBC,QAAQ;EAClD;AAEA,SAAO+D;AACT;AAEA,IAAMxC,yBACJ,CAAC3B,WAAWC,YACZ,CAA4BkE,WAAAA;AAE1B,MAAIlE,QAAQM,WAAW,SAAS;AAC9B,WAAO4D;EACT;AAEA,MACE,eAAenE,aACfA,UAAUoE,aACVC,OAAOC,UAAUtE,UAAUoE,SAAS,KACpC,SAASD,QACT;AACA,WAAOA,OAAOI,IAAIvE,UAAUoE,WAAW;MACrC,GAAGjE,YAAiBiE;MACpBI,QAAQ;QACND,KAAKvE,UAAUoE;MACjB;IACF,CAAA;EACF;AAEA,SAAOD;AACT;AAEF,IAAMvC,yBACJ,CAAC5B,cACD,CAA4BmE,WAAAA;AAC1B,MACE,eAAenE,aACfA,UAAUyE,aACVJ,OAAOC,UAAUtE,UAAUyE,SAAS,KACpC,SAASN,QACT;AACA,WAAOA,OAAOO,IAAI1E,UAAUyE,WAAW;MACrC,GAAGtE,YAAiBsE;MACpBD,QAAQ;QACNE,KAAK1E,UAAUyE;MACjB;IACF,CAAA;EACF;AAEA,SAAON;AACT;AAEF,IAAMtC,mBACJ,CAAC7B,WAAWC,YACZ,CAA4BkE,WAAAA;AAE1B,MAAIlE,QAAQM,WAAW,SAAS;AAC9B,WAAO4D;EACT;AAEA,MAAI,SAASnE,aAAa,SAASmE,QAAQ;AACzC,UAAMI,MAAMI,UAAU3E,UAAUuE,GAAG;AAEnC,QAAIA,KAAK;AACP,aAAOJ,OAAOI,IAAIA,KAAK;QACrB,GAAGpE,YAAiBoE;QACpBC,QAAQ;UACND;QACF;MACF,CAAA;IACF;EACF;AAEA,SAAOJ;AACT;AAEF,IAAMrC,mBACJ,CAAC9B,cACD,CAA4BmE,WAAAA;AAC1B,MAAI,SAASnE,WAAW;AACtB,UAAM0E,MAAMC,UAAU3E,UAAU0E,GAAG;AAEnC,QAAI,SAASP,UAAUO,KAAK;AAC1B,aAAOP,OAAOO,IAAIA,KAAK;QACrB,GAAGvE,YAAiBuE;QACpBF,QAAQ;UACNE;QACF;MACF,CAAA;IACF;EACF;AAEA,SAAOP;AACT;AAEF,IAAMQ,YAAY,CAACC,QAAAA;AACjB,MAAI,OAAOA,QAAQ,YAAYA,QAAQC,QAAW;AAChD,WAAOD;SACF;AACL,UAAME,MAAMT,OAAOO,GAAAA;AACnB,WAAOG,MAAMD,GAAAA,IAAOD,SAAYC;EAClC;AACF;AAEA,IAAM/C,qBACJ,CAAC/B,cACD,CAA4BmE,WAAAA;AAC1B,MAAI,WAAWnE,aAAaA,UAAUgE,SAAS,aAAaG,QAAQ;AAClE,WAAOA,OAAOZ,QAAQ,IAAIU,OAAOjE,UAAUgE,KAAK,GAAG;MACjD9D,SAAS;QACPiD,IAAIhD,YAAiB6D,MAAMb;QAC3B6B,gBAAgB;MAClB;MAEAC,oBAAoB,CAACjF,UAAUI;IACjC,CAAA;EACF;AAEA,SAAO+D;AACT;;;ACzYF,IAAMe,UAAUC,kBAAkBC,gBAAgB;EAChDC,WAAW,CAACC,aAAa;IACvBC,gBAAgBD,QAAQE,MAA0C;MAChEA,OAAO,MAAM;MACbC,mBAAmB,CAACC,aAAmCA,SAASC;MAChEC,cAAc;QAAC;MAAc;IAC/B,CAAA;;AAEJ,CAAA;AAEM,IAAA,EAAEC,uBAAsB,IAAKX;;;;ACUnC,IAAMY,uBAAuB,CAACC,UAAAA;AAC5B,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,yBAAyBC,eAAc,IAAKC,mBAAAA;AAEpD,QAAM,EAAEC,MAAMC,OAAOC,WAAWC,WAAU,IAAKC,uBAAuBC,MAAAA;AAEtE,QAAM,EAAEC,YAAYC,aAAaC,aAAY,IAAWC,cAAQ,MAAA;AAC9D,UAAMF,eAAcP,6BAAMQ,aAAaE,KAAK,CAACC,OAAOA,GAAGC,QAAQlB;AAE/D,UAAMmB,kBAAkBb,6BAAMM,WAAWQ,OAA6B,CAACC,KAAKC,cAAAA;AAC1ED,UAAIC,UAAUJ,GAAG,IAAII;AAErB,aAAOD;IACT,GAAG,CAAA;AAEH,UAAMT,cAAaW,6BAA6BV,gBAAAA,gBAAAA,aAAaW,YAAYL,eAAAA;AAEzE,WAAO;MACLP,YAAYa,OAAOC,KAAKd,WAAAA,EAAYe,WAAW,IAAIhB,SAAYC;MAC/DC,aAAAA;MACAC,eAAcR,6BAAMQ,iBAAgB,CAAA;IACtC;KACC;IAACd;IAAOM;EAAK,CAAA;AAEhBsB,EAAMC,gBAAU,MAAA;AACd,QAAItB,OAAO;AACTN,yBAAmB;QACjB6B,MAAM;QACNC,SAAS3B,eAAeG,KAAAA;MAC1B,CAAA;IACF;KACC;IAACN;IAAoBM;IAAOH;EAAe,CAAA;AAE9C,SAAO;;IAELQ,YAAkBG,cAAQ,MAAMH,cAAc,CAAA,GAAI;MAACA;IAAW,CAAA;IAC9DoB,QAAQnB;IACRoB,SAASnB;IACTN,WAAWA,aAAaC;EAC1B;AACF;AASA,IAAMc,+BAA+B,CACnCC,aAAwC,CAAA,GACxCU,gBAAsC,CAAA,MAAE;AAExC,QAAMC,gBAAgB,CAACX,gBAAAA;AACrB,WAAOA,YAAWJ,OAAiB,CAACC,KAAKe,cAAAA;;AAKvC,UAAIA,UAAUN,SAAS,aAAa;AAClC,cAAMO,sBAAsBZ,OAAOa,SACjCJ,mBAAcE,UAAUd,SAAS,MAAjCY,mBAAoCV,eAAc,CAAA,CAAC;AAGrDH,YAAIkB,KAAKH,UAAUd,WAAS,GAAKa,cAAcE,mBAAAA,CAAAA;MACjD,WAAWD,UAAUN,SAAS,eAAe;AAC3CT,YAAIkB;UAAI,GACHH,UAAUxB;UAIZ,GACEwB,UAAUxB,WAAW4B,QAAQ,CAACC,iBAAAA;;AAC/B,kBAAMJ,sBAAsBZ,OAAOa,SACjCJ,MAAAA,cAAcO,YAAAA,MAAdP,gBAAAA,IAA6BV,eAAc,CAAA,CAAC;AAG9C,mBAAOW,cAAcE,mBAAAA;UACvB,CAAA;QAAA;MAEJ;AAEA,aAAOhB;IACT,GAAG,CAAA,CAAE;EACP;AAEA,QAAMqB,gBAAgBP,cAAcV,OAAOa,OAAOd,UAAAA,CAAAA;AAElD,QAAMmB,sBAAsB;IAAI,GAAA,IAAIC,IAAIF,aAAAA;EAAe;AAEvD,QAAMvB,kBAAkBwB,oBAAoBvB,OAA6B,CAACC,KAAKH,QAAAA;AAC7EG,QAAIH,GAAAA,IAAOgB,cAAchB,GAAI;AAE7B,WAAOG;EACT,GAAG,CAAA,CAAC;AAEJ,SAAOF;AACT;;;ICzHa0B,QAAQ;;;;;;EAMnBC,wBAAwB;;;;;;EAOxBC,+BAA+B;;;;;;EAO/BC,yBAAyB;;;;;;EAOzBC,2BAA2B;AAC7B;;;ACpBA,IAAMC,kBAAkBC,kBAAkBC,gBAAgB;EACxDC,WAAW,CAACC,aAAa;IACvBC,6BAA6BD,QAAQE,MAGnC;MACAA,OAAO,CAACC,SAAS;QACfC,KAAK,kCAAkCD,GAAAA;QACvCE,QAAQ;;MAEVC,mBAAmB,CAACC,aAAoDA,SAASC;MACjFC,cAAc,CAACC,SAASC,QAAQR,QAAQ;QACtC;UAAES,MAAM;UAA6BC,IAAIV;QAAI;QAC7C;UAAES,MAAM;UAAuBC,IAAI;QAAO;MAC3C;IACH,CAAA;IACAC,2BAA2Bd,QAAQE,MAAuD;MACxFA,OAAO,MAAM;MACbI,mBAAmB,CAACC,aAAgDA,SAASC;MAC7EC,cAAc;QAAC;UAAEG,MAAM;UAAuBC,IAAI;QAAO;MAAE;IAC7D,CAAA;IACAE,gCAAgCf,QAAQgB,SAKtC;MACAd,OAAO,CAAC,EAAEC,KAAK,GAAGc,KAAAA,OAAY;QAC5Bb,KAAK,kCAAkCD,GAAAA;QACvCE,QAAQ;QACRG,MAAMS;;MAERX,mBAAmB,CAACC,aAAsDA,SAASC;MACnFU,iBAAiB,CAACR,SAASC,QAAQ,EAAER,IAAG,MAAO;QAC7C;UAAES,MAAM;UAA6BC,IAAIV;QAAI;QAC7C;UAAES,MAAM;UAAuBC,IAAI;QAAO;;QAE1C;UAAED,MAAM;QAAc;MACvB;IACH,CAAA;;AAEJ,CAAA;AAEA,IAAM,EACJO,qCACAC,mCACAC,0CAAyC,IACvCzB;;;;;;ACpDJ,IAAM0B,gCAAgC,CAACC,cAAAA;AACrC,QAAM,EAAEC,KAAI,IAAKD;AAEjB,MAAIC,SAAS,YAAY;AACvB,WAAO,CAACD,UAAUE,SAASC,YAAW,EAAGC,SAAS,OAAA;EACpD;AAEA,SAAO,CAAC;IAAC;IAAQ;IAAe;IAAY;IAAY;EAAS,EAACA,SAASH,IAAS,KAAA,CAAC,CAACA;AACxF;AAaA,IAAMI,eAAe,CACnBL,WACAM,eACA,EAAEC,SAASC,WAAU,MAA2D;AAxBlF;AA0BE,MAAI,CAACF,eAAe;AAClB,WAAOG;EACT;AAEA,QAAMC,gBACJV,UAAUC,SAAS,cACfO,WAAWR,UAAUW,SAAS,EAAEC,WAAWN,aAAc,EAACL,QAE1DM,aAAQM,KAAK,CAACC,WAAWA,OAAOC,QAAQf,UAAUgB,WAAW,MAA7DT,mBAAgEK,WAAWN,eACxEL;AAET,SAAO;IACLgB,MAAMX;IACNL,MAAMS,iBAAiB;EACzB;AACF;;;;;;;ACnBA,IAAMQ,4BAA4B;EAAC;EAAe;AAAe;AAajE,IAAMC,eACJ,CAACC,WAAsBC,cACvB,CAACC,QAAuBC,aAAmC,CAAA,MAC3D,CAACC,OAAgB,CAAA,MAAE;AACjB,QAAMC,WAAW,CAACC,OAAgBC,eAAAA;AAChC,WAAOC,OAAOC,QAAQH,KAAOI,EAAAA,OAAgB,CAACC,KAAK,CAACC,KAAKC,KAAM,MAAA;;AAC7D,YAAMC,YAAYP,WAAWK,GAAI;AAMjC,UAAId,0BAA0BiB,SAASH,GAAAA,KAAQC,UAAU,QAAQA,UAAUG,QAAW;AACpFL,YAAIC,GAAAA,IAAOC;AACX,eAAOF;MACT;AAEA,UAAIG,UAAUG,SAAS,aAAa;AAClC,YAAIH,UAAUI,YAAY;AACxB,gBAAMC,iBACJnB,UAAUc,WAAWD,KAASZ,IAAAA,UAAUY,OAAOC,SAAaD,IAAAA;AAE9DF,cAAIC,GAAI,IAAGO,eAAeC,IAAI,CAACC,kBAAAA;;AAC7BhB,4BAASgB,iBAAelB,MAAAA,WAAWW,UAAUQ,SAAS,MAA9BnB,gBAAAA,IAAiCI,eAAc,CAAA,CAAC;WAAA;eAErE;AACL,gBAAMY,iBACJnB,UAAUc,WAAWD,KAASZ,IAAAA,UAAUY,OAAOC,SAAaD,IAAAA;AAG9DF,cAAIC,GAAAA,IAAOP,SAASc,kBAAgBhB,gBAAWW,UAAUQ,SAAS,MAA9BnB,mBAAiCI,eAAc,CAAA,CAAC;QACtF;MACF,WAAWO,UAAUG,SAAS,eAAe;AAC3C,cAAMM,mBACJvB,UAAUc,WAAWD,KAASZ,IAAAA,UAAUY,OAAOC,SAAaD,IAAAA;AAG9DF,YAAIC,GAAI,IAAGW,iBAAiBH,IAAI,CAACC,kBAAAA;;AAC/BhB,0BAASgB,iBAAelB,MAAAA,WAAWkB,cAAcG,WAAW,MAApCrB,gBAAAA,IAAuCI,eAAc,CAAA,CAAC;SAAA;iBAEvEP,UAAUc,WAAWD,KAAQ,GAAA;AACtCF,YAAIC,GAAAA,IAAOX,UAAUY,OAAOC,SAAAA;aACvB;AACLH,YAAIC,GAAAA,IAAOC;MACb;AAEA,aAAOF;IACT,GAAG,CAAA,CAAC;EACN;AAEA,SAAON,SAASD,MAAMF,OAAOK,UAAU;AACzC;AASIkB,IAAAA,yBAAyB,CAACC,qBAC9B3B,aACE,CAACe,cAAcY,iBAAiBX,SAASD,UAAUG,IAAI,GACvD,MAAM,EAAA;AAWV,IAAMU,mBAAmB5B,aACvB,CAACe,cAAcA,UAAUG,SAAS,YAClC,OAAO;EACLW,SAAS,CAAA;EACTC,YAAY,CAAA;EACd;AAWD,IACKC,kBAAkB/B,aACtB,CAACe,cACC,UAAWG,SAAS,eAAeH,UAAUI,cAAeJ,UAAUG,SAAS,eACjF,CAACb,SAAAA;AACC,MAAI2B,MAAMC,QAAQ5B,IAAAA,KAASA,KAAK6B,SAAS,GAAG;AAC1C,UAAMC,OAAOC,qBAAqBnB,QAAWA,QAAWZ,KAAK6B,MAAM;AAEnE,WAAO7B,KAAKgB,IAAI,CAACd,OAAO8B,WAAW;MACjC,GAAG9B;MACH+B,cAAcH,KAAKE,KAAM;MAC3B;EACF;AAEA,SAAOhC;AACT,CAAA;AAYIkC,IAAAA,oCAAoC,CAACpC,WAA0B,CAACE,SAAAA;AACpE,QAAMmC,aAAa/B,OAAO0B,KAAKhC,OAAOK,UAAU;AAChD,QAAMiC,WAAWhC,OAAO0B,KAAK9B,IAAAA;AAE7B,QAAMqC,eAAeD,SAASE,OAAO,CAAC9B,QAAQ,CAAC2B,WAAWxB,SAASH,GAAAA,CAAAA;AAEnE,QAAM+B,cAAc;IAAIF,GAAAA;IAAiBG,GAAAA;IAAsBlC,OAAO,CAACC,KAAKC,QAAAA;AAC1E,WAAOD,IAAIC,GAAI;AAEf,WAAOD;EACT,GAAGkC,gBAAgBzC,IAAAA,CAAAA;AAEnB,SAAOuC;AACT;AAQA,IAAMG,mBAAmB,CAAC1C,SAAAA;AACxB,SAAOI,OAAOC,QAAQL,IAAMM,EAAAA,OAAgB,CAACC,KAAK,CAACC,KAAKC,KAAM,MAAA;AAC5D,QAAIA,UAAU,MAAM;AAClB,aAAOF;IACT;AAEAA,QAAIC,GAAAA,IAAOC;AAEX,WAAOF;EACT,GAAG,CAAA,CAAC;AACN;AAYA,IAAMoC,oBACJ,CAAC7C,QAAuBC,aAAmC,CAAA,MAC3D,CAAC6C,aAAAA;AACC,QAAMC,sBAAkBC,aAAAA,SACtBZ,kCAAkCpC,MAAAA,GAClCuB,uBAAuB;IAAC;EAAW,CAAA,EAAEvB,QAAQC,UAC7C2C,GAAAA,kBACAnB,iBAAiBzB,QAAQC,UAAAA,GACzB2B,gBAAgB5B,QAAQC,UAAAA,CAAAA;AAG1B,SAAO8C,gBAAgBD,QAAAA;AACzB;;;ACtMD,IACKG,oBAAoB,CACxBC,aACAC,aAAmC,CAAA,MAAE;AAErC,QAAMC,iBAAiB,CAACC,eAAAA;AACtB,WAAOC,OAAOC,QAAQF,UAAYG,EAAAA,OAAgB,CAACC,KAAK,CAACC,KAAKC,SAAU,MAAA;AACtE,UAAI,aAAaA,WAAW;AAC1BF,YAAIC,GAAAA,IAAOC,UAAUC;MACvB,WAAWD,UAAUE,SAAS,eAAeF,UAAUG,UAAU;AAC/D,cAAMC,uBAAuBX,eAAeD,WAAWQ,UAAUK,SAAS,EAAEX,UAAU;AAEtF,YAAIM,UAAUM,YAAY;AACxBR,cAAIC,GAAAA,IAAOC,UAAUO,MAAM;YAAIC,GAAAA,MAAMR,UAAUO,GAAG,EAAEE,KAAKL,oBAAAA;UAAsB,IAAG,CAAA;eAC7E;AACLN,cAAIC,GAAAA,IAAOK;QACb;MACF,WAAWJ,UAAUE,SAAS,iBAAiBF,UAAUG,UAAU;AACjEL,YAAIC,GAAI,IAAG,CAAA;MACb;AAEA,aAAOD;IACT,GAAG,CAAA,CAAC;EACN;AAEA,SAAOL,eAAeF,YAAYG,UAAU;AAC9C;;;ACsEA,IAAMgB,cAA2B,CAACC,MAAMC,SAAAA;AACtC,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,yBAAyBC,eAAc,IAAKC,mBAAAA;AACpD,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,QAAM,EACJC,aAAaC,MACbC,WAAWC,mBACXC,YAAYC,oBACZC,OACAC,QAAO,IACLC,oBAAoBjB,MAAM;IAC5B,GAAGC;IACHiB,MAAO,CAAClB,KAAKmB,cAAcnB,KAAKoB,mBAAmBC,iBAAiBpB,6BAAMiB;EAC5E,CAAA;AACA,QAAMI,WAAWZ,6BAAMA;AACvB,QAAMa,OAAOb,6BAAMa;AAEnB,QAAM,EACJC,YACAC,QACAC,SACAf,WAAWgB,gBAAe,IACxBC,qBAAqB5B,KAAK6B,KAAK;AACnC,QAAMC,gBAAeL,iCAAQM,UAAS;AAEtC,QAAMC,WAAW,CAACC,cAAAA;AAEhB,QAAIA,cAAc,SAAQX,qCAAWW,aAAY;AAC/C,aAAOX,SAASW,SAAU;IAC5B;AAGA,QAAIH,iBAAgBL,iCAAQS,KAAKC,cAAa;AAC5C,aAAOV,OAAOS,KAAKC;IACrB;AAGA,WAAO5B,cAAc;MACnB6B,IAAI;MACJC,gBAAgB;IAClB,CAAA;EACF;AAEAC,EAAMC,iBAAU,MAAA;AACd,QAAIxB,OAAO;AACTb,yBAAmB;QACjBsC,MAAM;QACNC,SAASpC,eAAeU,KAAAA;MAC1B,CAAA;IACF;KACC;IAACb;IAAoBa;IAAOV;IAAgBL,KAAKoB;EAAe,CAAA;AAEnE,QAAMsB,mBAAyBC,eAAQ,MAAA;AACrC,QAAI,CAAClB,QAAQ;AACX,aAAO;IACT;AAEA,WAAOmB,gBAAgBnB,OAAOoB,YAAYrB,UAAAA;KACzC;IAACC;IAAQD;EAAW,CAAA;AAEvB,QAAMsB,WAAiBC,mBACrB,CAACzB,cAAAA;AACC,QAAI,CAACoB,kBAAkB;AACrB,YAAM,IAAIM,MACR,iGAAA;IAEJ;AAEA,QAAI;AACFN,uBAAiBO,aAAa3B,WAAU;QAAE4B,YAAY;QAAOC,QAAQ;MAAK,CAAA;AAC1E,aAAO;IACT,SAASpC,QAAO;AACd,UAAIA,kBAAiBqC,iBAAiB;AACpC,eAAOC,uBAAuBtC,MAAAA;MAChC;AAEA,YAAMA;IACR;KAEF;IAAC2B;EAAiB,CAAA;AAYpB,QAAMY,uBAA6BP,mBACjC,CAACQ,qBAA8B,UAAK;AAClC,QAAK,CAACjC,YAAY,CAACiC,sBAAsB,CAACzB,gBAAiB,CAACL,QAAQ;AAClE,aAAO+B;IACT;AAMA,UAAMC,QAAOnC,qCAAUc,MAAKd,WAAWoC,kBAAkBjC,QAAQD,UAAAA;AAEjE,WAAOmC,kBAAkBlC,QAAQD,UAAYiC,EAAAA,IAAAA;KAE/C;IAACnC;IAAUQ;IAAcL;IAAQD;EAAW,CAAA;AAG9C,QAAMb,YAAYC,qBAAqBE,sBAAsBa;AAC7D,QAAMiC,WAAW,CAAC,CAAC7C;AAEnB,SAAO;IACLS;IACAF;IACAC;IACAZ;IACAiD;IACAnC;IACAC;IACAoB;IACAd;IACAsB;IACAtC;EACF;AACF;AASC,IACK6C,SAAS,MAAA;AACb,QAAM,EAAEzB,IAAI0B,MAAM1C,gBAAgB2C,OAAM,IAAKC,UAAAA;AAM7C,QAAM,CAAC,EAAEC,MAAK,CAAE,IAAIC,eAAAA;AACpB,QAAMC,SAAexB,eAAQ,MAAMyB,iBAAiBH,KAAQ,GAAA;IAACA;EAAM,CAAA;AAEnE,MAAI,CAAC7C,gBAAgB;AACnB,UAAM,IAAI4B,MAAM,6CAAA;EAClB;AAEA,MAAI,CAACc,MAAM;AACT,UAAM,IAAId,MAAM,oCAAA;EAClB;AAEA,QAAM1B,WAAWvB,YACf;IAAEoB,YAAY4C,UAAU3B;IAAIP,OAAOiC;IAAM1C;IAAgB+C;KACzD;IACEjD,MAAMkB,OAAO,YAAa,CAAC2B,UAAU,CAAC3B,MAAMhB,mBAAmBC;EACjE,CAAA;AAGF,QAAMgD,WAAWN,UAAU3B,OAAO,WAAWoB,SAAYpB;AAEzD,SAAO;IACLhB;IACAS,OAAOiC;IACP1B,IAAIiC;IACJ,GAAG/C;EACL;AACF;AAOC,IACKgD,2BAA2B,MAAA;;AAC/B,QAAM,EACJlD,gBACAS,OACAO,IACAZ,YACAb,WAAW4D,cACX9C,QACAC,QAAO,IACLmC,OAAAA;AAEJ,QAAMW,SAASC,kBAAkB5C,KAAAA;AAEjC,QAAM4B,OAAOiB,QAAiB,4BAA4B,CAACC,UAAUA,KAAAA;AAErE,QAAM7C,eAAeV,mBAAmBC;AACxC,QAAMyC,OAAOjC;AACb,QAAM+C,kBAAkBxC,OAAO;AAEpBR,uBAAAA;AAEX,QAAMjB,YAAY4D,gBAAgBC,OAAO7D;AACzC,QAAMI,QAAQyD,OAAOzD;AAErB,SAAO;IACLA;IACAJ;;IAGAkB;IACAT;IACAgB;IACA0B;IACAc;IACA9C;IACA+C,sBAAoBpD,sCAAQqD,YAARrD,mBAAiBsD,oBAAmB;;IAGxDvD;IACAwD,aAAavD;IACbwD,cAAcvD;;IAGd+B;;IAGAe;EACF;AACF;;;AClOkG,IAE5FU,mBAAmB;EACvBC,UAAU;EACVC,YAAY;EACZC,YAAY;EACZC,YAAY;EACZC,eAAe;EACfC,kBAAkB;EAClBC,WAAW;EACXC,UAAU;AACZ;AAoBA,IAAMC,oBAAuC,CAACC,UAAAA;AAC5C,QAAM,EAAEC,QAAQC,WAAU,IAAKC,YAAY;IAAEH;IAAOI,gBAAgB;KAAM;IAAEC,MAAM;EAAK,CAAA;AACvF,QAAM,CAAC,EAAEC,MAAK,CAAE,IAAIC,eAAAA;AACpB,QAAMC,mBAAmBC,aAAa,qBAAqB,CAACC,UAAUA,MAAMF,gBAAgB;AAC5F,QAAM,EAAEG,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,yBAAyBC,eAAc,IAAKC,mBAAAA;AACpD,QAAM,EAAEC,WAAWC,kBAAkBC,QAAO,IAAKC,qBAAAA;AAEjD,QAAM,EACJC,MACAJ,WAAWK,kBACXC,OACAC,YAAYC,kBAAiB,IAC3BC,oCAAoCzB,KAAAA;AAExC,QAAMgB,YAAYC,oBAAoBO,qBAAqBH;AAE3DK,EAAMC,iBAAU,MAAA;AACd,QAAIL,OAAO;AACTX,yBAAmB;QACjBiB,MAAM;QACNC,SAASf,eAAeQ,KAAAA;MAC1B,CAAA;IACF;KACC;IAACA;IAAOR;IAAgBH;EAAmB,CAAA;AAE9C,QAAMmB,aAAmBC,eACvB,MACEX,QAAQ,CAACJ,YACLgB,iBAAiBZ,MAAM;IAAEF;IAASjB;IAAQC;GACzC,IAAA;IACC+B,QAAQ,CAAA;IACR/B,YAAY,CAAA;IACZgC,WAAW,CAAA;IACXC,SAAS,CAAA;IACTC,UAAU9C;KAElB;IAAC8B;IAAMJ;IAAWE;IAASjB;IAAQC;EAAW,CAAA;AAGhD,QAAMmC,aAAmBN,eAAQ,MAAA;AAC/B,WAAOX,QAAQ,CAACJ,YACZsB,iBAAiBlB,MAAM;MAAEF;MAASjB;MAAQC;KACzC,IAAA;MACC+B,QAAQ,CAAA;MACRC,WAAW,CAAA;MACXC,SAAS,CAAA;MACTC,UAAU9C;IACZ;KACH;IAAC8B;IAAMJ;IAAWE;IAASjB;IAAQC;EAAW,CAAA;AAEjD,QAAM,EAAE+B,QAAQM,KAAI,IAAWR,eAC7B,MACEvB,iBAAiBgC,MAAMC,yBAAyB;IAC9CR,QAAQH;IACRxB;GAEJ,GAAA;IAACwB;IAAYxB;IAAOE;EAAiB,CAAA;AAGvC,SAAO;IACLc;IACAN;IACAuB;IACAG,MAAML;EACR;AACF;AASC,IACKM,eAAe,MAAA;AACnB,QAAM,EAAE3C,MAAK,IAAK4C,OAAAA;AAClB,SAAO7C,kBAAkBC,KAAAA;AAC3B;AAYA,IAAMgC,mBAAmB,CACvBZ,MACA,EACEF,SACAjB,QACAC,WAAU,MAC+D;AAE3E,MAAI2C,oBAAoB;AAIxB,QAAMC,yBAAyBC,gCAC7B3B,KAAK4B,YAAYC,QAAQV,MACzBtC,iCAAQiD,YACR9B,KAAK4B,YAAYd,WACjB;IAAEiB,gBAAgB/B,KAAKlB;IAAYgB,SAAShB;EAAW,GACvDgB,OACAkC,EAAAA,OAAmC,CAACC,QAAQC,QAAAA;AAC5C,QAAIA,IAAIC,KAAK,CAACC,UAAUA,MAAM5B,SAAS,aAAgB,GAAA;AACrDyB,aAAOI,KAAK;QAACH;MAAI,CAAA;AACjBT,2BAAqB;WAChB;AACL,UAAI,CAACQ,OAAOR,iBAAAA,GAAoB;AAC9BQ,eAAOI,KAAK;UAACH;QAAI,CAAA;aACZ;AACLD,eAAOR,iBAAAA,EAAmBY,KAAKH,GAAAA;MACjC;IACF;AAEA,WAAOD;EACT,GAAG,CAAA,CAAE;AAEL,QAAMK,0BAA0BC,OAAOC,QAAQxC,KAAKlB,UAAU,EAAEkD,OAC9D,CAACS,KAAK,CAACC,KAAKC,aAAc,MAAA;AACxBF,QAAIC,GAAAA,IAAO;MACT7B,QAAQc,gCACNgB,cAAcd,QAAQV,MACtBrC,WAAW4D,GAAAA,EAAKZ,YAChBa,cAAc7B,WACd;QAAEiB,gBAAgB/B,KAAKlB;QAAYgB,SAAShB;MAAW,CAAA;MAEzDkC,UAAU;QACR,GAAG2B,cAAc3B;QACjB4B,MAAM9D,WAAW4D,GAAAA,EAAKG,KAAKD;QAC3BE,aAAahE,WAAW4D,GAAAA,EAAKG,KAAKC;MACpC;IACF;AACA,WAAOL;EACT,GACA,CAAA,CAAC;AAGH,QAAMM,gBAAgBR,OAAOC,QAAQxC,KAAK4B,YAAYd,SAAS,EAAEkB,OAC/D,CAACS,KAAK,CAACO,WAAWC,QAAS,MAAA;AACzB,WAAO;MACL,GAAGR;MACH,CAACO,SAAAA,GAAYC,SAAS9B;IACxB;EACF,GACA,CAAA,CAAC;AAGH,SAAO;IACLN,QAAQa;IACR5C,YAAYwD;IACZxB,WAAWiC;IACX/B,UAAU;MACR,GAAGhB,KAAK4B,YAAYZ;MACpB8B,aAAajE,iCAAQgE,KAAKC;IAC5B;IACA/B,SAAS;MACP,GAAGlC,iCAAQkC;MACX,GAAGlC,iCAAQqE;MACX,GAAGlD,KAAK4B,YAAYb;IACtB;EACF;AACF;AAYA,IAAMY,kCAAkC,CACtCwB,MACArB,aAAmC,CAAA,GACnChB,WACAhC,YAIAgB,UAAoB,CAAA,MAAE;AAEtB,SAAOqD,KAAKC,IAAI,CAAClB,QACfA,IACGkB,IAAI,CAAChB,UAAAA;AACJ,UAAMY,YAAYlB,WAAWM,MAAMiB,IAAI;AAEvC,QAAI,CAACL,WAAW;AACd,aAAO;IACT;AAEA,UAAM,EAAE7B,MAAM8B,SAAQ,IAAKnC,UAAUsB,MAAMiB,IAAI;AAE/C,UAAMrC,WACJgC,UAAUxC,SAAS,eAAe1B,aAC9BA,WAAWiD,eAAeiB,UAAUM,SAAS,EAAEtC,WAC/C,CAAA;AAEN,WAAO;MACLgC;MACAO,UAAU,CAACN,SAASO;MACpBC,MAAMR,SAASS;MACfC,OAAOV,SAASU,SAAS;MACzBN,MAAMjB,MAAMiB;;MAEZ5E,WAAWmF,aAAaZ,WAAWC,SAASxE,aAAauC,SAASvC,WAAW;QAC3EqB;QACAhB,aAAYA,yCAAYgB,YAAW,CAAA;MACrC,CAAA;MACA+D,aAAaZ,SAASY,eAAe;MACrCC,UAAUd,UAAUc,YAAY;MAChCC,MAAM3B,MAAM2B;MACZC,QAAQ,YAAYhB,YAAYA,UAAUgB,SAAS;MACnDC,SAAShB,SAASgB,WAAW;MAC7BzD,MAAMwC,UAAUxC;IAClB;EACF,CAAA,EACC0D,OAAO,CAAC9B,UAAUA,UAAU,IAAA,CAAA;AAEnC;AAYA,IAAMlB,mBAAmB,CACvBlB,MACA,EACEF,SACAjB,QACAC,WAAU,MAC+D;AAE3E,QAAMqF,gBAAgB5B,OAAOC,QAAQxC,KAAK4B,YAAYd,SAAS,EAAEkB,OAC/D,CAACS,KAAK,CAACO,WAAWC,QAAS,MAAA;AACzB,WAAO;MACL,GAAGR;MACH,CAACO,SAAAA,GAAYC,SAAS3B;IACxB;EACF,GACA,CAAA,CAAC;AAKH,QAAM8C,iBAAiBC,gCACrBrE,KAAK4B,YAAYC,QAAQP,MACzBzC,iCAAQiD,YACRqC,eACA;IAAEpC,gBAAgB/B,KAAKlB;IAAYgB,SAAShB;KAC5CgB,OAAAA;AAGF,SAAO;IACLe,QAAQuD;IACRpD,UAAU;MAAE,GAAGhB,KAAK4B,YAAYZ;MAAU8B,aAAajE,iCAAQgE,KAAKC;IAAY;IAChFhC,WAAWqD;IACXpD,SAAS;MACP,GAAGlC,iCAAQkC;MACX,GAAGlC,iCAAQqE;MACX,GAAGlD,KAAK4B,YAAYb;IACtB;EACF;AACF;AAaA,IAAMsD,kCAAkC,CACtCC,SACAxC,aAAmC,CAAA,GACnChB,WACAhC,YAIAgB,UAAoB,CAAA,MAAE;AAEtB,SAAOwE,QACJlB,IAAI,CAACC,SAAAA;AACJ,UAAML,YAAYlB,WAAWuB,IAAK;AAElC,QAAI,CAACL,WAAW;AACd,aAAO;IACT;AAEA,UAAMC,WAAWnC,UAAUuC,IAAK;AAEhC,UAAMrC,WACJgC,UAAUxC,SAAS,eAAe1B,aAC9BA,WAAWiD,eAAeiB,UAAUM,SAAS,EAAEtC,WAC/C,CAAA;AAEN,WAAO;MACLgC;MACAW,OAAOV,SAASU,SAAS;MACzBlF,WAAWmF,aAAaZ,WAAWC,SAASxE,aAAauC,SAASvC,WAAW;QAC3EqB;QACAhB,aAAYA,yCAAYgB,YAAW,CAAA;MACrC,CAAA;MACAuD;MACAhF,YAAY4E,SAAS5E,cAAc;MACnCkG,UAAUtB,SAASsB,YAAY;IACjC;EACF,CAAA,EACCL,OAAO,CAAC9B,UAAUA,UAAU,IAAA;AACjC;;;;;AC3cO,SAASoC,MAAMC,KAAUC,KAAwBC,KAAWC,iBAAyB,GAAC;AAC3F,QAAMC,WAAOC,cAAAA,SAAOJ,GAAAA;AACpB,SAAOD,OAAOG,iBAAiBC,KAAKE,QAAQ;AAC1CN,UAAMA,IAAII,KAAKD,gBAAAA,CAAiB;EAClC;AAGA,MAAIA,mBAAmBC,KAAKE,UAAU,CAACN,KAAK;AAC1C,WAAOE;EACT;AAEA,SAAOF,QAAQO,SAAYL,MAAMF;AACnC;AAGaQ,IAAAA,WAAW,CAACR,QACvBA,QAAQ,QAAQ,OAAOA,QAAQ,YAAY,CAACS,MAAMC,QAAQV,GAAK;AAGpDW,IAAAA,YAAY,CAACX,QAAsBY,OAAOC,KAAKC,MAAMC,OAAOf,GAAAA,CAAAA,CAAAA,MAAWA;AA0B7E,SAASgB,MAAMhB,KAAUI,MAAca,OAAU;AACtD,QAAMC,UAAWC,aAAAA,SAAMnB,GAAAA;AACvB,MAAIoB,SAAcF;AAClB,MAAIG,IAAI;AACR,QAAMC,gBAAYjB,cAAAA,SAAOD,IAAAA;AAEzB,SAAOiB,IAAIC,UAAUhB,SAAS,GAAGe,KAAK;AACpC,UAAME,cAAsBD,UAAUD,CAAE;AACxC,UAAMG,aAAkBzB,MAAMC,KAAKsB,UAAUG,MAAM,GAAGJ,IAAI,CAAA,CAAA;AAE1D,QAAIG,eAAehB,SAASgB,UAAAA,KAAef,MAAMC,QAAQc,UAAAA,IAAc;AACrEJ,eAASA,OAAOG,WAAY,QAAGJ,aAAAA,SAAMK,UAAAA;WAChC;AACL,YAAME,WAAmBJ,UAAUD,IAAI,CAAE;AACzCD,eAASA,OAAOG,WAAAA,IAAeZ,UAAUe,QAAaX,KAAAA,OAAOW,QAAa,KAAA,IAAI,CAAA,IAAK,CAAA;IACrF;EACF;AAGA,OAAKL,MAAM,IAAIrB,MAAMoB,QAAQE,UAAUD,CAAE,CAAA,MAAMJ,OAAO;AACpD,WAAOjB;EACT;AAEyB;AACvB,WAAOoB,OAAOE,UAAUD,CAAAA,CAAE;;AAO5B,MAAIA,MAAM,KAAKJ,UAAUV,QAAW;AAClC,WAAOW,IAAII,UAAUD,CAAAA,CAAE;EACzB;AAEA,SAAOH;AACT;", "names": ["SINGLE_TYPES", "COLLECTION_TYPES", "documentApi", "contentManagerApi", "injectEndpoints", "overrideExisting", "endpoints", "builder", "autoCloneDocument", "mutation", "query", "model", "sourceId", "params", "url", "method", "config", "invalidatesTags", "_result", "error", "type", "id", "cloneDocument", "data", "_error", "createDocument", "result", "transformResponse", "response", "meta", "arg", "availableStatus", "availableLocales", "deleteDocument", "collectionType", "documentId", "SINGLE_TYPES", "deleteManyDocuments", "body", "_res", "discardDocument", "getAllDocuments", "stringify", "encode", "providesTags", "results", "map", "getDraftRelationCount", "getDocument", "queryFn", "_api", "_extraOpts", "base<PERSON><PERSON>y", "res", "name", "document", "undefined", "getManyDraftRelationCount", "publishDocument", "publishManyDocuments", "documentIds", "updateDocument", "onQueryStarted", "patch", "dispatch", "queryFulfilled", "patchResult", "util", "updateQueryData", "draft", "Object", "assign", "undo", "unpublishDocument", "unpublishManyDocuments", "useAutoCloneDocumentMutation", "useCloneDocumentMutation", "useCreateDocumentMutation", "useDeleteDocumentMutation", "useDeleteManyDocumentsMutation", "useDiscardDocumentMutation", "useGetAllDocumentsQuery", "useLazyGetDocumentQuery", "useGetDocumentQuery", "useLazyGetDraftRelationCountQuery", "useGetManyDraftRelationCountQuery", "usePublishDocumentMutation", "usePublishManyDocumentsMutation", "useUpdateDocumentMutation", "useUnpublishDocumentMutation", "useUnpublishManyDocumentsMutation", "buildValidParams", "query", "plugins", "_", "validQueryParams", "Object", "values", "reduce", "acc", "current", "assign", "isBaseQueryError", "error", "name", "undefined", "ID", "CREATED_BY_ATTRIBUTE_NAME", "UPDATED_BY_ATTRIBUTE_NAME", "CREATOR_FIELDS", "PUBLISHED_BY_ATTRIBUTE_NAME", "CREATED_AT_ATTRIBUTE_NAME", "UPDATED_AT_ATTRIBUTE_NAME", "PUBLISHED_AT_ATTRIBUTE_NAME", "DOCUMENT_META_FIELDS", "ATTRIBUTE_TYPES_THAT_CANNOT_BE_MAIN_FIELD", "arrayValidator", "attribute", "options", "message", "translatedErrors", "required", "test", "value", "status", "Array", "isArray", "length", "createYupSchema", "attributes", "components", "createModelSchema", "object", "shape", "Object", "entries", "reduce", "acc", "name", "DOCUMENT_META_FIELDS", "includes", "validations", "addNullableValidation", "addRequiredValidation", "addMinLengthValidation", "addMaxLengthValidation", "addMinValidation", "addMaxValidation", "addRegexValidation", "map", "fn", "transformSchema", "pipe", "type", "component", "repeatable", "array", "of", "nullable", "lazy", "data", "__component", "validation", "string", "oneOf", "keys", "concat", "mixed", "id", "number", "createAttributeSchema", "default", "matches", "boolean", "json", "email", "enum", "JSON", "stringify", "err", "parse", "regex", "RegExp", "nullableSchema", "schema", "<PERSON><PERSON><PERSON><PERSON>", "Number", "isInteger", "min", "values", "max<PERSON><PERSON><PERSON>", "max", "toInteger", "val", "undefined", "num", "isNaN", "defaultMessage", "excludeEmptyString", "initApi", "contentManagerApi", "injectEndpoints", "endpoints", "builder", "getInitialData", "query", "transformResponse", "response", "data", "providesTags", "useGetInitialDataQuery", "useContentTypeSchema", "model", "toggleNotification", "useNotification", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "data", "error", "isLoading", "isFetching", "useGetInitialDataQuery", "undefined", "components", "contentType", "contentTypes", "useMemo", "find", "ct", "uid", "componentsByKey", "reduce", "acc", "component", "extractContentTypeComponents", "attributes", "Object", "keys", "length", "React", "useEffect", "type", "message", "schema", "schemas", "allComponents", "getComponents", "attribute", "componentAttributes", "values", "push", "flatMap", "componentUid", "componentUids", "uniqueComponentUids", "Set", "HOOKS", "INJECT_COLUMN_IN_TABLE", "MUTATE_COLLECTION_TYPES_LINKS", "MUTATE_EDIT_VIEW_LAYOUT", "MUTATE_SINGLE_TYPES_LINKS", "contentTypesApi", "contentManagerApi", "injectEndpoints", "endpoints", "builder", "getContentTypeConfiguration", "query", "uid", "url", "method", "transformResponse", "response", "data", "providesTags", "_result", "_error", "type", "id", "getAllContentTypeSettings", "updateContentTypeConfiguration", "mutation", "body", "invalidatesTags", "useGetContentTypeConfigurationQuery", "useGetAllContentTypeSettingsQuery", "useUpdateContentTypeConfigurationMutation", "checkIfAttributeIsDisplayable", "attribute", "type", "relation", "toLowerCase", "includes", "getMainField", "mainFieldName", "schemas", "components", "undefined", "mainFieldType", "component", "attributes", "find", "schema", "uid", "targetModel", "name", "BLOCK_LIST_ATTRIBUTE_KEYS", "traverseData", "predicate", "transform", "schema", "components", "data", "traverse", "datum", "attributes", "Object", "entries", "reduce", "acc", "key", "value", "attribute", "includes", "undefined", "type", "repeatable", "componentValue", "map", "componentData", "component", "dynamicZoneValue", "__component", "removeProhibitedFields", "prohibitedFields", "prepareRelations", "connect", "disconnect", "prepareTempKeys", "Array", "isArray", "length", "keys", "generateNKeysBetween", "index", "__temp_key__", "removeFieldsThatDontExistOnSchema", "schema<PERSON>eys", "dataKeys", "keysToRemove", "filter", "revisedData", "DOCUMENT_META_FIELDS", "structuredClone", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transformDocument", "document", "transformations", "pipe", "createDefaultForm", "contentType", "components", "traverseSchema", "attributes", "Object", "entries", "reduce", "acc", "key", "attribute", "default", "type", "required", "defaultComponentForm", "component", "repeatable", "min", "Array", "fill", "useDocument", "args", "opts", "toggleNotification", "useNotification", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "formatMessage", "useIntl", "currentData", "data", "isLoading", "isLoadingDocument", "isFetching", "isFetchingDocument", "error", "refetch", "useGetDocumentQuery", "skip", "documentId", "collectionType", "SINGLE_TYPES", "document", "meta", "components", "schema", "schemas", "isLoadingSchema", "useContentTypeSchema", "model", "isSingleType", "kind", "getTitle", "mainField", "info", "displayName", "id", "defaultMessage", "React", "useEffect", "type", "message", "validationSchema", "useMemo", "createYupSchema", "attributes", "validate", "useCallback", "Error", "validateSync", "abort<PERSON><PERSON><PERSON>", "strict", "ValidationError", "getYupValidationErrors", "getInitialFormValues", "isCreatingDocument", "undefined", "form", "createDefaultForm", "transformDocument", "<PERSON><PERSON><PERSON><PERSON>", "useDoc", "slug", "origin", "useParams", "query", "useQueryParams", "params", "buildValidParams", "returnId", "useContentManagerContext", "isLoadingDoc", "layout", "useDocumentLayout", "useForm", "state", "isCreatingEntry", "hasDraftAndPublish", "options", "draftAndPublish", "contentType", "contentTypes", "DEFAULT_SETTINGS", "bulkable", "filterable", "searchable", "pagination", "defaultSortBy", "defaultSortOrder", "mainField", "pageSize", "useDocumentLayout", "model", "schema", "components", "useDocument", "collectionType", "skip", "query", "useQueryParams", "runHookWaterfall", "useStrapiApp", "state", "toggleNotification", "useNotification", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "isLoading", "isLoadingSchemas", "schemas", "useContentTypeSchema", "data", "isLoadingConfigs", "error", "isFetching", "isFetchingConfigs", "useGetContentTypeConfigurationQuery", "React", "useEffect", "type", "message", "editLayout", "useMemo", "formatEditLayout", "layout", "metadatas", "options", "settings", "listLayout", "formatListLayout", "edit", "HOOKS", "MUTATE_EDIT_VIEW_LAYOUT", "list", "useDocLayout", "useDoc", "currentPanelIndex", "panelledEditAttributes", "convertEditLayoutToFieldLayouts", "contentType", "layouts", "attributes", "configurations", "reduce", "panels", "row", "some", "field", "push", "componentEditAttributes", "Object", "entries", "acc", "uid", "configuration", "icon", "info", "displayName", "editMetadatas", "attribute", "metadata", "pluginOptions", "rows", "map", "name", "component", "disabled", "editable", "hint", "description", "label", "getMainField", "placeholder", "required", "size", "unique", "visible", "filter", "listMetadatas", "listAttributes", "convertListLayoutToFieldLayouts", "columns", "sortable", "getIn", "obj", "key", "def", "pathStartIndex", "path", "to<PERSON><PERSON>", "length", "undefined", "isObject", "Array", "isArray", "isInteger", "String", "Math", "floor", "Number", "setIn", "value", "res", "clone", "resVal", "i", "pathArray", "currentPath", "currentObj", "slice", "nextPath"]}