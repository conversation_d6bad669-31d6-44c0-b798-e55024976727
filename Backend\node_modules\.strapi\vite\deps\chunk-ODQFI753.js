import {
  useTypedDispatch,
  useTypedSelector
} from "./chunk-ZOP4VV6J.js";
import {
  adminApi,
  deleteCookie,
  getCookieValue,
  require_lib,
  setCookie
} from "./chunk-WH6VCVXU.js";
import {
  createSlice
} from "./chunk-IL5G2D22.js";
import {
  createContext
} from "./chunk-76QM3EFM.js";
import {
  useLocation,
  useNavigate
} from "./chunk-S65ZWNEO.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/features/StrapiApp.mjs
var [StrapiAppProvider, useStrapiApp] = createContext("StrapiApp");

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useQueryParams.mjs
var import_react = __toESM(require_react(), 1);
var import_qs = __toESM(require_lib(), 1);
var useSearch = () => {
  const { search } = useLocation();
  return (0, import_react.useMemo)(() => search, [
    search
  ]);
};
var useQueryParams = (initialParams) => {
  const search = useSearch();
  const navigate = useNavigate();
  const query = (0, import_react.useMemo)(() => {
    const searchQuery = search.startsWith("?") ? search.slice(1) : search;
    if (!search && initialParams) {
      return initialParams;
    }
    return {
      ...initialParams,
      ...(0, import_qs.parse)(searchQuery)
    };
  }, [
    search,
    initialParams
  ]);
  const setQuery = (0, import_react.useCallback)((nextParams, method = "push", replace = false) => {
    let nextQuery = {
      ...query
    };
    if (method === "remove") {
      Object.keys(nextParams).forEach((key) => {
        if (Object.prototype.hasOwnProperty.call(nextQuery, key)) {
          delete nextQuery[key];
        }
      });
    } else {
      nextQuery = {
        ...query,
        ...nextParams
      };
    }
    navigate({
      search: (0, import_qs.stringify)(nextQuery, {
        encode: false
      })
    }, {
      replace
    });
  }, [
    navigate,
    query
  ]);
  return [
    {
      query,
      rawQuery: search
    },
    setQuery
  ];
};

// node_modules/@strapi/admin/dist/admin/admin/src/reducer.mjs
var STORAGE_KEYS = {
  TOKEN: "jwtToken",
  STATUS: "isLoggedIn"
};
var THEME_LOCAL_STORAGE_KEY = "STRAPI_THEME";
var LANGUAGE_LOCAL_STORAGE_KEY = "strapi-admin-language";
var getStoredToken = () => {
  const fromLocalStorage = localStorage.getItem(STORAGE_KEYS.TOKEN);
  if (fromLocalStorage) {
    return JSON.parse(fromLocalStorage);
  }
  const fromCookie = getCookieValue(STORAGE_KEYS.TOKEN);
  return fromCookie ?? null;
};
var adminSlice = createSlice({
  name: "admin",
  initialState: () => {
    return {
      language: {
        locale: "en",
        localeNames: {
          en: "English"
        }
      },
      permissions: {},
      theme: {
        availableThemes: [],
        currentTheme: localStorage.getItem(THEME_LOCAL_STORAGE_KEY) || "system"
      },
      token: null
    };
  },
  reducers: {
    setAppTheme(state, action) {
      state.theme.currentTheme = action.payload;
      window.localStorage.setItem(THEME_LOCAL_STORAGE_KEY, action.payload);
    },
    setAvailableThemes(state, action) {
      state.theme.availableThemes = action.payload;
    },
    setLocale(state, action) {
      state.language.locale = action.payload;
      window.localStorage.setItem(LANGUAGE_LOCAL_STORAGE_KEY, action.payload);
      document.documentElement.setAttribute("lang", action.payload);
    },
    setToken(state, action) {
      state.token = action.payload;
    },
    login(state, action) {
      const { token, persist } = action.payload;
      if (!persist) {
        setCookie(STORAGE_KEYS.TOKEN, token);
      } else {
        window.localStorage.setItem(STORAGE_KEYS.TOKEN, JSON.stringify(token));
      }
      window.localStorage.setItem(STORAGE_KEYS.STATUS, "true");
      state.token = token;
    },
    logout(state) {
      state.token = null;
      deleteCookie(STORAGE_KEYS.TOKEN);
      window.localStorage.removeItem(STORAGE_KEYS.TOKEN);
      window.localStorage.removeItem(STORAGE_KEYS.STATUS);
    }
  }
});
var reducer = adminSlice.reducer;
var { setAppTheme, setAvailableThemes, setLocale, setToken, logout, login } = adminSlice.actions;

// node_modules/@strapi/admin/dist/admin/admin/src/services/auth.mjs
var authService = adminApi.enhanceEndpoints({
  addTagTypes: [
    "User",
    "Me",
    "ProvidersOptions"
  ]
}).injectEndpoints({
  endpoints: (builder) => ({
    /**
    * ME
    */
    getMe: builder.query({
      query: () => ({
        method: "GET",
        url: "/admin/users/me"
      }),
      transformResponse(res) {
        return res.data;
      },
      providesTags: (res) => res ? [
        "Me",
        {
          type: "User",
          id: res.id
        }
      ] : [
        "Me"
      ]
    }),
    getMyPermissions: builder.query({
      query: () => ({
        method: "GET",
        url: "/admin/users/me/permissions"
      }),
      transformResponse(res) {
        return res.data;
      }
    }),
    updateMe: builder.mutation({
      query: (body) => ({
        method: "PUT",
        url: "/admin/users/me",
        data: body
      }),
      transformResponse(res) {
        return res.data;
      },
      invalidatesTags: [
        "Me"
      ]
    }),
    /**
    * Permissions
    */
    checkPermissions: builder.query({
      query: (permissions) => ({
        method: "POST",
        url: "/admin/permissions/check",
        data: permissions
      })
    }),
    /**
    * Auth methods
    */
    login: builder.mutation({
      query: (body) => ({
        method: "POST",
        url: "/admin/login",
        data: body
      }),
      transformResponse(res) {
        return res.data;
      },
      invalidatesTags: [
        "Me"
      ]
    }),
    logout: builder.mutation({
      query: () => ({
        method: "POST",
        url: "/admin/logout"
      })
    }),
    resetPassword: builder.mutation({
      query: (body) => ({
        method: "POST",
        url: "/admin/reset-password",
        data: body
      }),
      transformResponse(res) {
        return res.data;
      }
    }),
    renewToken: builder.mutation({
      query: (body) => ({
        method: "POST",
        url: "/admin/renew-token",
        data: body
      }),
      transformResponse(res) {
        return res.data;
      }
    }),
    getRegistrationInfo: builder.query({
      query: (registrationToken) => ({
        url: "/admin/registration-info",
        method: "GET",
        config: {
          params: {
            registrationToken
          }
        }
      }),
      transformResponse(res) {
        return res.data;
      }
    }),
    registerAdmin: builder.mutation({
      query: (body) => ({
        method: "POST",
        url: "/admin/register-admin",
        data: body
      }),
      transformResponse(res) {
        return res.data;
      }
    }),
    registerUser: builder.mutation({
      query: (body) => ({
        method: "POST",
        url: "/admin/register",
        data: body
      }),
      transformResponse(res) {
        return res.data;
      }
    }),
    forgotPassword: builder.mutation({
      query: (body) => ({
        url: "/admin/forgot-password",
        method: "POST",
        data: body
      })
    }),
    isSSOLocked: builder.query({
      query: () => ({
        url: "/admin/providers/isSSOLocked",
        method: "GET"
      }),
      transformResponse(res) {
        return res.data;
      }
    }),
    getProviders: builder.query({
      query: () => ({
        url: "/admin/providers",
        method: "GET"
      })
    }),
    getProviderOptions: builder.query({
      query: () => ({
        url: "/admin/providers/options",
        method: "GET"
      }),
      transformResponse(res) {
        return res.data;
      },
      providesTags: [
        "ProvidersOptions"
      ]
    }),
    updateProviderOptions: builder.mutation({
      query: (body) => ({
        url: "/admin/providers/options",
        method: "PUT",
        data: body
      }),
      transformResponse(res) {
        return res.data;
      },
      invalidatesTags: [
        "ProvidersOptions"
      ]
    })
  }),
  overrideExisting: false
});
var { useCheckPermissionsQuery, useLazyCheckPermissionsQuery, useGetMeQuery, useLoginMutation, useRenewTokenMutation, useLogoutMutation, useUpdateMeMutation, useResetPasswordMutation, useRegisterAdminMutation, useRegisterUserMutation, useGetRegistrationInfoQuery, useForgotPasswordMutation, useGetMyPermissionsQuery, useIsSSOLockedQuery, useGetProvidersQuery, useGetProviderOptionsQuery, useUpdateProviderOptionsMutation } = authService;

// node_modules/@strapi/admin/dist/admin/admin/src/features/Auth.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var [Provider, useAuth] = createContext("Auth");
var STORAGE_KEYS2 = {
  TOKEN: "jwtToken",
  STATUS: "isLoggedIn"
};
var AuthProvider = ({ children, _defaultPermissions = [], _disableRenewToken = false }) => {
  const dispatch = useTypedDispatch();
  const runRbacMiddleware = useStrapiApp("AuthProvider", (state) => state.rbac.run);
  const location = useLocation();
  const [{ rawQuery }] = useQueryParams();
  const locationRef = React.useRef(location);
  React.useEffect(() => {
    locationRef.current = location;
  }, [
    location
  ]);
  const token = useTypedSelector((state) => state.admin_app.token ?? null);
  const { data: user, isLoading: isLoadingUser } = useGetMeQuery(void 0, {
    /**
    * If there's no token, we don't try to fetch
    * the user data because it will fail.
    */
    skip: !token
  });
  const { data: userPermissions = _defaultPermissions, refetch, isUninitialized, isLoading: isLoadingPermissions } = useGetMyPermissionsQuery(void 0, {
    skip: !token
  });
  const navigate = useNavigate();
  const [loginMutation] = useLoginMutation();
  const [renewTokenMutation] = useRenewTokenMutation();
  const [logoutMutation] = useLogoutMutation();
  const clearStateAndLogout = React.useCallback(() => {
    dispatch(adminApi.util.resetApiState());
    dispatch(logout());
    navigate("/auth/login");
  }, [
    dispatch,
    navigate
  ]);
  React.useEffect(() => {
    if (token && !_disableRenewToken) {
      renewTokenMutation({
        token
      }).then((res) => {
        if ("data" in res) {
          dispatch(login({
            token: res.data.token
          }));
        } else {
          clearStateAndLogout();
        }
      });
    }
  }, [
    token,
    dispatch,
    renewTokenMutation,
    clearStateAndLogout,
    _disableRenewToken
  ]);
  React.useEffect(() => {
    if (user) {
      if (user.preferedLanguage) {
        dispatch(setLocale(user.preferedLanguage));
      }
    }
  }, [
    dispatch,
    user
  ]);
  React.useEffect(() => {
    const handleUserStorageChange = (event) => {
      if (event.key === STORAGE_KEYS2.STATUS && event.newValue === null) {
        clearStateAndLogout();
      }
    };
    window.addEventListener("storage", handleUserStorageChange);
    return () => {
      window.removeEventListener("storage", handleUserStorageChange);
    };
  });
  const login$1 = React.useCallback(async ({ rememberMe, ...body }) => {
    const res = await loginMutation(body);
    if ("data" in res) {
      const { token: token2 } = res.data;
      dispatch(login({
        token: token2,
        persist: rememberMe
      }));
    }
    return res;
  }, [
    dispatch,
    loginMutation
  ]);
  const logout$1 = React.useCallback(async () => {
    await logoutMutation();
    clearStateAndLogout();
  }, [
    clearStateAndLogout,
    logoutMutation
  ]);
  const refetchPermissions = React.useCallback(async () => {
    if (!isUninitialized) {
      await refetch();
    }
  }, [
    isUninitialized,
    refetch
  ]);
  const [checkPermissions] = useLazyCheckPermissionsQuery();
  const checkUserHasPermissions = React.useCallback(async (permissions, passedPermissions, rawQueryContext) => {
    if (!permissions || permissions.length === 0) {
      return [
        {
          action: "",
          subject: ""
        }
      ];
    }
    const actualUserPermissions = passedPermissions ?? userPermissions;
    const matchingPermissions = actualUserPermissions.filter((permission) => permissions.findIndex((perm) => perm.action === permission.action && // Only check the subject if it's provided
    (perm.subject == void 0 || perm.subject === permission.subject)) >= 0);
    const middlewaredPermissions = await runRbacMiddleware({
      user,
      permissions: userPermissions,
      pathname: locationRef.current.pathname,
      search: (rawQueryContext || rawQuery).split("?")[1] ?? ""
    }, matchingPermissions);
    const shouldCheckConditions = middlewaredPermissions.some((perm) => Array.isArray(perm.conditions) && perm.conditions.length > 0);
    if (!shouldCheckConditions) {
      return middlewaredPermissions;
    }
    const { data, error } = await checkPermissions({
      permissions: middlewaredPermissions.map((perm) => ({
        action: perm.action,
        subject: perm.subject
      }))
    });
    if (error) {
      throw error;
    } else {
      return middlewaredPermissions.filter((_, index) => (data == null ? void 0 : data.data[index]) === true);
    }
  }, [
    checkPermissions,
    rawQuery,
    runRbacMiddleware,
    user,
    userPermissions
  ]);
  const isLoading = isLoadingUser || isLoadingPermissions;
  return (0, import_jsx_runtime.jsx)(Provider, {
    token,
    user,
    login: login$1,
    logout: logout$1,
    permissions: userPermissions,
    checkUserHasPermissions,
    refetchPermissions,
    isLoading,
    children
  });
};

export {
  StrapiAppProvider,
  useStrapiApp,
  useQueryParams,
  THEME_LOCAL_STORAGE_KEY,
  LANGUAGE_LOCAL_STORAGE_KEY,
  getStoredToken,
  reducer,
  setAppTheme,
  setAvailableThemes,
  setLocale,
  logout,
  login,
  useCheckPermissionsQuery,
  useUpdateMeMutation,
  useResetPasswordMutation,
  useRegisterAdminMutation,
  useRegisterUserMutation,
  useGetRegistrationInfoQuery,
  useForgotPasswordMutation,
  useIsSSOLockedQuery,
  useGetProvidersQuery,
  useGetProviderOptionsQuery,
  useUpdateProviderOptionsMutation,
  useAuth,
  AuthProvider
};
//# sourceMappingURL=chunk-ODQFI753.js.map
