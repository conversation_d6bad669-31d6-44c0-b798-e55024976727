import {
  EditPage
} from "./chunk-ZQNTOT55.js";
import "./chunk-3I64ZOER.js";
import "./chunk-VOSBK4YM.js";
import "./chunk-2TUVRMRA.js";
import "./chunk-HZKRK7AR.js";
import "./chunk-DUGZ4WIW.js";
import "./chunk-376QHLWZ.js";
import "./chunk-EGNP2T5O.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-HWAQQGJJ.js";
import "./chunk-L5JCPKMP.js";
import "./chunk-MTTHLNPH.js";
import "./chunk-VYSYYPOB.js";
import {
  Page
} from "./chunk-7LKLOY7A.js";
import "./chunk-ODQFI753.js";
import {
  useTypedSelector
} from "./chunk-ZOP4VV6J.js";
import "./chunk-WH6VCVXU.js";
import "./chunk-IL5G2D22.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-76QM3EFM.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import "./chunk-UBCTZOSQ.js";
import "./chunk-7GC3Y62Q.js";
import "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import "./chunk-ACIMPXWY.js";
import "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Webhooks/CreatePage.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var ProtectedCreatePage = () => {
  const permissions = useTypedSelector((state) => {
    var _a;
    return (_a = state.admin_app.permissions.settings) == null ? void 0 : _a.webhooks.create;
  });
  return (0, import_jsx_runtime.jsx)(Page.Protect, {
    permissions,
    children: (0, import_jsx_runtime.jsx)(EditPage, {})
  });
};
export {
  ProtectedCreatePage
};
//# sourceMappingURL=CreatePage-FHQP53ZN.js.map
