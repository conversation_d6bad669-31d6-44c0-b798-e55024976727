import {
  ContentBox,
  useClipboard
} from "./chunk-CJHUGFLE.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  IconButton,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  ForwardRef$3R
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Users/<USER>/MagicLinkWrapper.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var MagicLinkWrapper = ({ children, target }) => {
  const { toggleNotification } = useNotification();
  const { formatMessage } = useIntl();
  const { copy } = useClipboard();
  const copyLabel = formatMessage({
    id: "app.component.CopyToClipboard.label",
    defaultMessage: "Copy to clipboard"
  });
  const handleClick = async (e) => {
    e.preventDefault();
    const didCopy = await copy(target);
    if (didCopy) {
      toggleNotification({
        type: "info",
        message: formatMessage({
          id: "notification.link-copied"
        })
      });
    }
  };
  return (0, import_jsx_runtime.jsx)(ContentBox, {
    endAction: (0, import_jsx_runtime.jsx)(IconButton, {
      label: copyLabel,
      variant: "ghost",
      onClick: handleClick,
      children: (0, import_jsx_runtime.jsx)(ForwardRef$3R, {})
    }),
    title: target,
    titleEllipsis: true,
    subtitle: children,
    icon: (0, import_jsx_runtime.jsx)("span", {
      style: {
        fontSize: 32
      },
      children: "✉️"
    }),
    iconBackground: "neutral100"
  });
};

export {
  MagicLinkWrapper
};
//# sourceMappingURL=chunk-BGSCGOFP.js.map
