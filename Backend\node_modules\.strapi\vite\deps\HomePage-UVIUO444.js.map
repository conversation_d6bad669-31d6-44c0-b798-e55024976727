{"version": 3, "sources": ["../../../@strapi/admin/ee/admin/src/pages/HomePage.tsx"], "sourcesContent": ["import { HomePageCE } from '../../../../admin/src/pages/Home/HomePage';\nimport { useLicenseLimitNotification } from '../hooks/useLicenseLimitNotification';\n\nexport const HomePageEE = () => {\n  useLicenseLimitNotification();\n\n  return <HomePageCE />;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAGaA,aAAa,MAAA;AACxBC,8BAAAA;AAEA,aAAOC,wBAACC,YAAAA,CAAAA,CAAAA;AACV;", "names": ["HomePageEE", "useLicenseLimitNotification", "_jsx", "HomePageCE"]}