import {
  getFetchClient
} from "./chunk-WH6VCVXU.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useFetchClient.mjs
var React = __toESM(require_react(), 1);
var useFetchClient = () => {
  const controller = React.useRef(null);
  if (controller.current === null) {
    controller.current = new AbortController();
  }
  React.useEffect(() => {
    return () => {
      controller.current.abort();
    };
  }, []);
  return React.useMemo(() => getFetchClient({
    signal: controller.current.signal
  }), []);
};

export {
  useFetchClient
};
//# sourceMappingURL=chunk-Y4UEUAII.js.map
