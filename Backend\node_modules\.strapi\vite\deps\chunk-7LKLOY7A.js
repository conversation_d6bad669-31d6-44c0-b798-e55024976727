import {
  useAuth,
  useCheckPermissionsQuery
} from "./chunk-ODQFI753.js";
import {
  setIn
} from "./chunk-BHLYCXQ7.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  Box,
  EmptyStateLayout,
  Flex,
  Loader,
  Main,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  ForwardRef$3
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/icons/dist/symbols-index.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var SvgBlocksField = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("rect", { width: 31, height: 23, x: 0.5, y: 4.5, fill: "#EAF5FF", stroke: "#B8E1FF", rx: 2.5 }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#0C75AF",
      d: "M14.75 11.75a1 1 0 1 1-2 0 1 1 0 0 1 2 0m3.5 1a1 1 0 1 0 0-2 1 1 0 0 0 0 2M13.75 15a1 1 0 1 0 0 2 1 1 0 0 0 0-2m4.5 0a1 1 0 1 0 0 2 1 1 0 0 0 0-2m-4.5 4.25a1 1 0 1 0 0 2 1 1 0 0 0 0-2m4.5 0a1 1 0 1 0 0 2 1 1 0 0 0 0-2"
    }
  )
] });
var ForwardRef$12 = (0, import_react.forwardRef)(SvgBlocksField);
var ForwardRef$13 = ForwardRef$12;
var SvgBooleanField = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("rect", { width: 31, height: 23, x: 0.5, y: 4.5, fill: "#EAFBE7", stroke: "#C6F0C2", rx: 2.5 }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#328048",
      d: "M19 11.5h-6a4.5 4.5 0 1 0 0 9h6a4.5 4.5 0 1 0 0-9m0 7a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5"
    }
  )
] });
var ForwardRef$10 = (0, import_react.forwardRef)(SvgBooleanField);
var ForwardRef$11 = ForwardRef$10;
var SvgCodeSquare = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("path", { fill: "#D9822F", d: "M0 4a4 4 0 0 1 4-4h24a4 4 0 0 1 4 4v24a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4z" }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#fff",
      fillRule: "evenodd",
      d: "M17.143 18.659v2.912l6.856-3.878v-2.815L17.143 11v2.906l4.16 2.38zm-2.287 0-4.16-2.374 4.16-2.38V11L8 14.877v2.816l6.856 3.878z",
      clipRule: "evenodd"
    }
  )
] });
var ForwardRef$_ = (0, import_react.forwardRef)(SvgCodeSquare);
var SvgCollectionType = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("rect", { width: 31, height: 23, x: 0.5, y: 4.5, fill: "#4945FF", stroke: "#4945FF", rx: 2.5 }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#fff",
      d: "M14.328 14.54v-.083c-.04-.937-.75-1.559-1.787-1.559-1.535 0-2.725 1.57-2.725 3.65 0 1.302.71 2.104 1.846 2.104.961 0 1.787-.545 2.063-1.37h1.752c-.37 1.78-1.922 2.935-3.967 2.935-2.121 0-3.504-1.395-3.504-3.545 0-3.123 1.951-5.344 4.646-5.344 1.94 0 3.41 1.283 3.41 2.96 0 .087 0 .163-.011.251zM20.053 20H18.27l1.489-6.943h-2.532l.311-1.512h6.844l-.31 1.512H21.54z"
    }
  )
] });
var ForwardRef$Y = (0, import_react.forwardRef)(SvgCollectionType);
var ForwardRef$Z = ForwardRef$Y;
var SvgComponentField = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("rect", { width: 31, height: 23, x: 0.5, y: 4.5, fill: "#F6F6F9", stroke: "#DCDCE4", rx: 2.5 }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#666687",
      d: "M20.5 17.5c-.358 0-.71.085-1.029.25l-1.337-1.04q.11-.326.116-.67l.647-.214a2.25 2.25 0 1 0-.637-1.37l-.486.162A2.25 2.25 0 0 0 16 13.75c-.062 0-.117 0-.176.008l-.278-.625A2.25 2.25 0 1 0 14 13.75c.063 0 .117 0 .176-.008l.278.625a2.24 2.24 0 0 0-.537 2.482l-1.33 1.182a2.25 2.25 0 1 0 .996 1.12l1.33-1.182a2.25 2.25 0 0 0 2.3-.075l1.224.954A2.25 2.25 0 1 0 20.5 17.5m0-4a.75.75 0 1 1 0 1.5.75.75 0 0 1 0-1.5m-7.25-2a.75.75 0 1 1 1.5 0 .75.75 0 0 1-1.5 0m-1.75 9.25a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5M15.25 16a.75.75 0 1 1 1.5 0 .75.75 0 0 1-1.5 0m5.25 4.5a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5"
    }
  )
] });
var ForwardRef$W = (0, import_react.forwardRef)(SvgComponentField);
var ForwardRef$X = ForwardRef$W;
var SvgDateField = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("rect", { width: 31, height: 23, x: 0.5, y: 4.5, fill: "#FDF4DC", stroke: "#FAE7B9", rx: 2.5 }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#D9822F",
      d: "M21 10h-1.5v-.5a.5.5 0 0 0-1 0v.5h-5v-.5a.5.5 0 0 0-1 0v.5H11a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V11a1 1 0 0 0-1-1m0 3H11v-2h1.5v.5a.5.5 0 0 0 1 0V11h5v.5a.5.5 0 0 0 1 0V11H21z"
    }
  )
] });
var ForwardRef$U = (0, import_react.forwardRef)(SvgDateField);
var ForwardRef$V = ForwardRef$U;
var SvgDiscord = (props, ref) => (0, import_jsx_runtime.jsx)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: (0, import_jsx_runtime.jsx)(
  "path",
  {
    fill: "#5865F2",
    d: "M27.107 5.911a26.5 26.5 0 0 0-6.602-2.031 18 18 0 0 0-.845 1.72 24.6 24.6 0 0 0-7.327 0 18 18 0 0 0-.846-1.72A26.7 26.7 0 0 0 4.88 5.916C.702 12.098-.43 18.126.136 24.068a26.6 26.6 0 0 0 8.097 4.065 19.6 19.6 0 0 0 1.734-2.796c-.947-.354-1.86-.79-2.73-1.304.228-.166.452-.337.669-.504a19.02 19.02 0 0 0 16.188 0q.33.271.67.504c-.872.515-1.788.952-2.736 1.306a19.4 19.4 0 0 0 1.734 2.794 26.5 26.5 0 0 0 8.102-4.062c.665-6.892-1.135-12.864-4.757-18.16M10.684 20.414c-1.578 0-2.882-1.433-2.882-3.194 0-1.762 1.259-3.207 2.877-3.207 1.619 0 2.912 1.445 2.885 3.207s-1.271 3.194-2.88 3.194m10.632 0c-1.581 0-2.88-1.433-2.88-3.194 0-1.762 1.259-3.207 2.88-3.207s2.904 1.445 2.877 3.207-1.269 3.194-2.877 3.194"
  }
) });
var ForwardRef$S = (0, import_react.forwardRef)(SvgDiscord);
var SvgDiscourse = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#231F20",
      d: "M15.659.302C7.158.302 0 7.194 0 15.698v15.943l15.656-.015c8.501 0 15.396-7.158 15.396-15.66 0-8.5-6.901-15.664-15.393-15.664"
    }
  ),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#FFF9AE",
      d: "M15.81 6.261a9.546 9.546 0 0 0-8.39 14.09l-1.726 5.554 6.2-1.4A9.541 9.541 0 1 0 15.82 6.26z"
    }
  ),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#00AEEF",
      d: "M23.381 9.999a9.54 9.54 0 0 1-11.487 14.49l-6.2 1.419 6.312-.746A9.54 9.54 0 0 0 23.381 10"
    }
  ),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#00A94F",
      d: "M21.624 8.239a9.54 9.54 0 0 1-9.91 15.61l-6.02 2.059 6.2-1.404a9.54 9.54 0 0 0 9.73-16.265"
    }
  ),
  (0, import_jsx_runtime.jsx)("path", { fill: "#F15D22", d: "M7.991 20.562A9.542 9.542 0 0 1 23.387 9.994 9.543 9.543 0 0 0 7.42 20.35l-1.726 5.555z" }),
  (0, import_jsx_runtime.jsx)("path", { fill: "#E31B23", d: "M7.42 20.35A9.543 9.543 0 0 1 21.624 8.238 9.543 9.543 0 0 0 6.832 20.202l-1.135 5.706z" })
] });
var ForwardRef$Q = (0, import_react.forwardRef)(SvgDiscourse);
var SvgDynamicZoneField = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("rect", { width: 31, height: 23, x: 0.5, y: 4.5, fill: "#F6F6F9", stroke: "#DCDCE4", rx: 2.5 }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#666687",
      d: "M23.75 16a3.75 3.75 0 0 1-6.402 2.652l-.03-.033-3.742-4.225a2.25 2.25 0 1 0 0 3.212l.193-.218a.75.75 0 1 1 1.125.994l-.21.237-.03.033a3.75 3.75 0 1 1 0-5.304l.03.033 3.742 4.225a2.25 2.25 0 1 0 0-3.212l-.193.218a.751.751 0 1 1-1.125-.995l.21-.236.03-.033A3.75 3.75 0 0 1 23.75 16"
    }
  )
] });
var ForwardRef$O = (0, import_react.forwardRef)(SvgDynamicZoneField);
var ForwardRef$P = ForwardRef$O;
var SvgEmailField = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("rect", { width: 31, height: 23, x: 0.5, y: 4.5, fill: "#FCECEA", stroke: "#F5C0B8", rx: 2.5 }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#D02B20",
      d: "M16 9.25a6.75 6.75 0 0 0 0 13.5c1.392 0 2.856-.42 3.915-1.125a.75.75 0 1 0-.83-1.25c-.813.54-1.994.875-3.085.875A5.25 5.25 0 1 1 21.25 16c0 .58-.104 1.067-.293 1.372-.165.265-.375.378-.707.378s-.542-.113-.707-.378c-.187-.305-.293-.791-.293-1.372v-2.5a.75.75 0 0 0-1.468-.216 3.25 3.25 0 1 0 .554 4.973c.433.637 1.09.993 1.914.993 1.542 0 2.5-1.245 2.5-3.25A6.76 6.76 0 0 0 16 9.25m0 8.5a1.75 1.75 0 1 1 0-3.5 1.75 1.75 0 0 1 0 3.5"
    }
  )
] });
var ForwardRef$M = (0, import_react.forwardRef)(SvgEmailField);
var ForwardRef$N = ForwardRef$M;
var SvgEmptyData = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 16, fill: "none", viewBox: "0 0 217 121", ref, ...props, children: [
  (0, import_jsx_runtime.jsxs)("g", { clipPath: "url(#EmptyData_svg__a)", children: [
    (0, import_jsx_runtime.jsx)(
      "path",
      {
        stroke: "#EEEEFA",
        strokeDasharray: "3.93 4.91",
        strokeLinecap: "round",
        strokeWidth: 0.982,
        d: "M1.158 99.652h215.018M1.158 80.015h215.018M1.158 59.397h215.018M1.158 38.779h215.018M1.158 20.124h215.018"
      }
    ),
    (0, import_jsx_runtime.jsx)(
      "path",
      {
        fill: "#D9D8FF",
        fillOpacity: 0.8,
        fillRule: "evenodd",
        d: "M182.63 25.086c4.112 0 7.446 3.279 7.446 7.323s-3.334 7.324-7.446 7.324h-42.545c4.112 0 7.445 3.279 7.445 7.323s-3.333 7.324-7.445 7.324h23.4c4.112 0 7.445 3.278 7.445 7.323 0 4.044-3.333 7.323-7.445 7.323h-10.821c-5.185 0-9.388 3.28-9.388 7.324q0 2.847 3.162 5.314c1.968 1.536 4.676 1.736 6.861 2.943 2.27 1.255 3.804 3.646 3.804 6.39 0 4.044-3.333 7.323-7.445 7.323H61.376c-4.112 0-7.446-3.279-7.446-7.323s3.334-7.324 7.446-7.324H19.894c-4.112 0-7.445-3.279-7.445-7.323s3.333-7.324 7.445-7.324H62.44c4.112 0 7.445-3.278 7.445-7.323s-3.334-7.324-7.446-7.324H35.85c-4.112 0-7.446-3.278-7.446-7.323 0-4.044 3.334-7.323 7.446-7.323h42.545c-4.112 0-7.445-3.28-7.445-7.324s3.333-7.323 7.445-7.323zm0 29.294c4.112 0 7.446 3.278 7.446 7.323 0 4.044-3.334 7.323-7.446 7.323s-7.445-3.278-7.445-7.323 3.333-7.324 7.445-7.324",
        clipRule: "evenodd"
      }
    ),
    (0, import_jsx_runtime.jsx)(
      "rect",
      {
        width: 27,
        height: 79.036,
        x: 109.403,
        y: 33.133,
        fill: "#fff",
        stroke: "#7B79FF",
        strokeWidth: 2.455,
        rx: 4.173
      }
    ),
    (0, import_jsx_runtime.jsx)("rect", { width: 27, height: 102.6, x: 74.058, y: 9.57, fill: "#fff", stroke: "#7B79FF", strokeWidth: 2.455, rx: 4.173 }),
    (0, import_jsx_runtime.jsx)(
      "rect",
      {
        width: 27,
        height: 58.418,
        x: 39.203,
        y: 53.26,
        fill: "#fff",
        stroke: "#7B79FF",
        strokeWidth: 2.455,
        rx: 4.173
      }
    ),
    (0, import_jsx_runtime.jsx)(
      "rect",
      {
        width: 27,
        height: 55.473,
        x: 144.748,
        y: 56.697,
        fill: "#fff",
        stroke: "#7B79FF",
        strokeWidth: 2.455,
        rx: 4.173
      }
    ),
    (0, import_jsx_runtime.jsx)("rect", { width: 21.6, height: 53.018, x: 41.903, y: 55.961, fill: "#EEEEFA", rx: 1.964 }),
    (0, import_jsx_runtime.jsx)("rect", { width: 21.6, height: 73.636, x: 112.103, y: 35.833, fill: "#EEEEFA", rx: 1.964 })
  ] }),
  (0, import_jsx_runtime.jsx)("defs", { children: (0, import_jsx_runtime.jsx)("clipPath", { id: "EmptyData_svg__a", children: (0, import_jsx_runtime.jsx)("path", { fill: "#fff", d: "M.667.797h216v120h-216z" }) }) })
] });
var ForwardRef$K = (0, import_react.forwardRef)(SvgEmptyData);
var SvgEmptyDocuments = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 16, fill: "none", viewBox: "0 0 217 121", ref, ...props, children: [
  (0, import_jsx_runtime.jsxs)("g", { clipPath: "url(#EmptyDocuments_svg__a)", opacity: 0.84, children: [
    (0, import_jsx_runtime.jsx)(
      "path",
      {
        fill: "#D9D8FF",
        fillOpacity: 0.8,
        fillRule: "evenodd",
        d: "M189.917 20.442a7.583 7.583 0 0 1 0 15.167h-43.334a7.584 7.584 0 1 1 0 15.167h23.834a7.583 7.583 0 0 1 0 15.166h-11.022c-5.281 0-9.562 3.396-9.562 7.584q0 2.934 3.19 5.479c2.017 1.608 4.824 1.818 7.065 3.097a7.584 7.584 0 0 1-3.755 14.174H66.417a7.583 7.583 0 1 1 0-15.167h-42.25a7.583 7.583 0 0 1 0-15.167H67.5a7.583 7.583 0 0 0 0-15.166H40.417a7.583 7.583 0 0 1 0-15.167H83.75a7.583 7.583 0 0 1 0-15.167zm0 30.334a7.583 7.583 0 0 1 0 15.166 7.584 7.584 0 0 1 0-15.166",
        clipRule: "evenodd"
      }
    ),
    (0, import_jsx_runtime.jsx)(
      "path",
      {
        fill: "#fff",
        fillRule: "evenodd",
        d: "m133.228 20.443 10.077 73.496.905 7.373a4.33 4.33 0 0 1-3.773 4.829l-63.44 7.79a4.334 4.334 0 0 1-4.83-3.773l-9.766-79.547a2.167 2.167 0 0 1 1.886-2.414l.023-.003 5.263-.59zm-59.4 6.683 4.97-.557z",
        clipRule: "evenodd"
      }
    ),
    (0, import_jsx_runtime.jsx)(
      "path",
      {
        stroke: "#7B79FF",
        strokeWidth: 2.5,
        d: "m73.829 27.126 4.97-.557m54.429-6.126 10.077 73.496.905 7.373a4.33 4.33 0 0 1-3.773 4.829l-63.44 7.79a4.334 4.334 0 0 1-4.83-3.773l-9.766-79.547a2.167 2.167 0 0 1 1.886-2.414l.023-.003 5.263-.59z"
      }
    ),
    (0, import_jsx_runtime.jsx)(
      "path",
      {
        fill: "#F0F0FF",
        fillRule: "evenodd",
        d: "m130.485 25.068 9.121 66.607.821 6.683c.264 2.152-1.246 4.109-3.373 4.37l-56.812 6.976c-2.128.261-4.066-1.272-4.33-3.425l-8.83-71.908a2.166 2.166 0 0 1 1.887-2.414l7.028-.863",
        clipRule: "evenodd"
      }
    ),
    (0, import_jsx_runtime.jsx)(
      "path",
      {
        fill: "#fff",
        fillRule: "evenodd",
        stroke: "#7B79FF",
        strokeWidth: 2.5,
        d: "M135.998 6.63H86.645a2.97 2.97 0 0 0-2.107.872 2.97 2.97 0 0 0-.873 2.107v82.333c0 .823.334 1.568.873 2.107a2.97 2.97 0 0 0 2.106.872h63.917a2.97 2.97 0 0 0 2.107-.872 2.97 2.97 0 0 0 .872-2.107V24.164a2.98 2.98 0 0 0-.873-2.108L138.104 7.502a2.98 2.98 0 0 0-2.106-.872Z",
        clipRule: "evenodd"
      }
    ),
    (0, import_jsx_runtime.jsx)(
      "path",
      {
        stroke: "#7B79FF",
        strokeLinecap: "round",
        strokeLinejoin: "round",
        strokeWidth: 2.5,
        d: "M136.478 7.879v12.563a3.25 3.25 0 0 0 3.25 3.25h8.595M95.311 78.942h28.167m-28.167-55.25h28.167zm0 13h46.583zm0 14.084h46.583zm0 14.083h46.583z"
      }
    )
  ] }),
  (0, import_jsx_runtime.jsx)("defs", { children: (0, import_jsx_runtime.jsx)("clipPath", { id: "EmptyDocuments_svg__a", children: (0, import_jsx_runtime.jsx)("path", { fill: "#fff", d: "M.667.797h216v120h-216z" }) }) })
] });
var ForwardRef$I = (0, import_react.forwardRef)(SvgEmptyDocuments);
var ForwardRef$J = ForwardRef$I;
var SvgEmptyPermissions = (props, ref) => (0, import_jsx_runtime.jsx)("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 16, fill: "none", viewBox: "0 0 193 121", ref, ...props, children: (0, import_jsx_runtime.jsxs)("g", { opacity: 0.88, children: [
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#DBDBFA",
      fillRule: "evenodd",
      d: "M160.947 53.823a4 4 0 0 0-.15-.281c-3.5-5.96-7.289-11.263-11.52-15.858h18.096c4.445 0 8.048 3.613 8.048 8.07 0 4.456-3.603 8.069-8.048 8.069zm-8.493 16.139c-11.562 11.57-31.953 19.597-55.21 19.597-31.435 0-54.384-16.705-55.701-35.736H29.409c-4.445 0-8.048 3.613-8.048 8.07 0 4.456 3.603 8.069 8.048 8.069h11.697c5.604 0 10.148 3.612 10.148 8.069q0 3.786-4.98 6.963c-.942.601-2.034.876-3.123 1.15-.33.083-.658.166-.983.257a8.07 8.07 0 0 0-5.86 7.768c0 4.457 3.602 8.07 8.047 8.07h95.425c4.445 0 8.048-3.613 8.048-8.07 0-4.456-3.603-8.069-8.048-8.069h44.839c4.444 0 8.048-3.613 8.048-8.07 0-4.456-3.604-8.068-8.048-8.068zM68.723 21.546H8.715c-4.445 0-8.048 3.613-8.048 8.07 0 4.456 3.603 8.068 8.048 8.068h37.314c4.606-6.843 12.5-12.477 22.694-16.138M.667 61.892c0-4.456 3.603-8.069 8.048-8.069 4.444 0 8.047 3.613 8.047 8.07 0 4.456-3.603 8.069-8.047 8.069S.667 66.349.667 61.892",
      clipRule: "evenodd"
    }
  ),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      stroke: "#7B79FF",
      strokeLinecap: "round",
      strokeLinejoin: "round",
      strokeWidth: 2.5,
      d: "m158.091 49.212 1.446 2.131"
    }
  ),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#fff",
      d: "M154.862 59.69c-45.683-57.683-95.639-25.812-117.374.01-1.404 1.668-1.426 4.117-.15 5.882 47.31 65.454 96.71 29.044 117.704-.133 1.24-1.722 1.138-4.094-.18-5.758"
    }
  ),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#7B79FF",
      fillRule: "evenodd",
      d: "M113.536 28.091c-9.141-2.486-18.085-2.558-26.571-.94-21.301 4.058-39.458 18.707-50.43 31.742-1.794 2.132-1.798 5.217-.206 7.42C48.246 82.798 60.36 92.962 72.218 98.41q1.186.544 2.367 1.027l1.206-2.209a57 57 0 0 1-2.53-1.09c-11.366-5.222-23.168-15.052-34.905-31.291-.961-1.33-.92-3.143.092-4.345 10.764-12.787 28.453-26.985 48.985-30.897 7.905-1.506 16.271-1.495 24.869.745zm-28.277 71.966c7.097 1.397 13.98 1.155 20.536-.279 20.46-4.474 37.826-20.593 48.237-35.062.899-1.25.84-3.007-.146-4.252-10.728-13.547-21.653-22.062-32.351-26.854l1.201-2.2c11.026 4.98 22.21 13.738 33.11 27.502 1.649 2.083 1.795 5.069.215 7.265-10.584 14.708-28.416 31.382-49.732 36.044-7.115 1.556-14.607 1.77-22.323.131z",
      clipRule: "evenodd"
    }
  ),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      stroke: "#7B79FF",
      strokeLinecap: "round",
      strokeLinejoin: "round",
      strokeWidth: 2.5,
      d: "M127.159 22.08c13.23 6.32 21.864 14.378 27.624 21.78M34.068 48.642C53.002 23.337 89.197 8.3 117.953 18.635"
    }
  ),
  (0, import_jsx_runtime.jsx)("ellipse", { cx: 94.98, cy: 66.505, fill: "#F0F0FF", rx: 18.395, ry: 18.444 }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#7B79FF",
      fillRule: "evenodd",
      d: "M104.76 44.362a24 24 0 0 0-9.776-2.067c-13.334 0-24.144 10.838-24.144 24.208 0 8.428 4.296 15.85 10.813 20.186l1.203-2.204c-5.741-3.903-9.516-10.498-9.516-17.982 0-11.995 9.696-21.708 21.644-21.708 3.045 0 5.944.631 8.574 1.77zM91.95 87.999q1.488.211 3.034.212c11.947 0 21.643-9.713 21.643-21.708a21.66 21.66 0 0 0-5.222-14.142l1.272-2.33a24.16 24.16 0 0 1 6.45 16.472c0 13.37-10.809 24.207-24.143 24.207-1.47 0-2.908-.131-4.305-.383z",
      clipRule: "evenodd"
    }
  ),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#fff",
      stroke: "#7B79FF",
      strokeWidth: 2.5,
      d: "M116.729 54.977c0 4.406-3.562 7.972-7.948 7.972s-7.948-3.566-7.948-7.972 3.562-7.972 7.948-7.972 7.948 3.566 7.948 7.972Z"
    }
  ),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#fff",
      d: "M110.472 33.635c1.275-2.324 4.078-3.019 6.296-1.56 2.264 1.49 3.053 4.654 1.751 7.027l-31.287 57.05c-1.275 2.324-4.079 3.018-6.296 1.559-2.265-1.49-3.053-4.653-1.751-7.027z"
    }
  ),
  (0, import_jsx_runtime.jsx)(
    "rect",
    {
      width: 3.668,
      height: 134.376,
      fill: "#7B79FF",
      rx: 1.834,
      transform: "matrix(.82817 .56048 -.47918 .87772 127.181 .797)"
    }
  )
] }) });
var ForwardRef$G = (0, import_react.forwardRef)(SvgEmptyPermissions);
var ForwardRef$H = ForwardRef$G;
var SvgEmptyPictures = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 16, fill: "none", viewBox: "0 0 217 121", ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("g", { clipPath: "url(#EmptyPictures_svg__a)", children: (0, import_jsx_runtime.jsxs)("g", { clipPath: "url(#EmptyPictures_svg__b)", opacity: 0.88, children: [
    (0, import_jsx_runtime.jsx)(
      "path",
      {
        fill: "#D9D8FF",
        fillOpacity: 0.8,
        fillRule: "evenodd",
        d: "M119.667 28.797a7 7 0 1 1 0 14h64a7 7 0 1 1 0 14h22a7 7 0 1 1 0 14h-19a7 7 0 1 0 0 14h6a7 7 0 1 1 0 14h-52a7 7 0 0 1-1.5-.161 7 7 0 0 1-1.5.16h-91a7 7 0 0 1 0-14h-39a7 7 0 1 1 0-14h40a7 7 0 0 0 0-14h-25a7 7 0 1 1 0-14h40a7 7 0 1 1 0-14zm90 56a7 7 0 1 1 0 14 7 7 0 0 1 0-14",
        clipRule: "evenodd"
      }
    ),
    (0, import_jsx_runtime.jsx)(
      "path",
      {
        fill: "#fff",
        fillRule: "evenodd",
        d: "m74.497 103.07-8.622 1.422a4 4 0 0 1-4.518-3.404L50.224 21.866a4 4 0 0 1 3.404-4.518l78.231-10.994a4 4 0 0 1 4.518 3.404c.474 3.377 2.408 16.468 2.571 17.63",
        clipRule: "evenodd"
      }
    ),
    (0, import_jsx_runtime.jsx)(
      "path",
      {
        fill: "#F0F0FF",
        fillRule: "evenodd",
        d: "m72.472 99.51-3.696.525a3.62 3.62 0 0 1-4.096-3.085l-9.996-71.925a3.646 3.646 0 0 1 3.097-4.107L128.82 10.82a3.62 3.62 0 0 1 4.096 3.085l.859 6.18 9.206 66.599c.306 2.212-1.22 4.257-3.408 4.566l-.07.01z",
        clipRule: "evenodd"
      }
    ),
    (0, import_jsx_runtime.jsx)(
      "path",
      {
        stroke: "#7B79FF",
        strokeLinecap: "round",
        strokeWidth: 2.5,
        d: "m69.945 103.92-4.07.572a4 4 0 0 1-4.518-3.405L50.223 21.866a4 4 0 0 1 3.405-4.518l78.231-10.994a4 4 0 0 1 4.518 3.404l.956 6.808M138.167 21.177l.5 3.12"
      }
    ),
    (0, import_jsx_runtime.jsx)(
      "path",
      {
        fill: "#fff",
        fillRule: "evenodd",
        stroke: "#7B79FF",
        strokeWidth: 2.5,
        d: "m165.078 31.096-78.567-8.258a2.74 2.74 0 0 0-2.018.598 2.74 2.74 0 0 0-1.005 1.85l-8.362 79.561a2.748 2.748 0 0 0 2.447 3.023l78.568 8.258a2.74 2.74 0 0 0 2.018-.598 2.74 2.74 0 0 0 1.004-1.85l8.362-79.562a2.74 2.74 0 0 0-.597-2.018 2.74 2.74 0 0 0-1.85-1.004Z",
        clipRule: "evenodd"
      }
    ),
    (0, import_jsx_runtime.jsx)(
      "path",
      {
        fill: "#fff",
        fillRule: "evenodd",
        d: "m93.657 31.382 62.655 6.585a3 3 0 0 1 2.67 3.297l-5.54 52.71a3 3 0 0 1-3.298 2.67L87.49 90.059a3 3 0 0 1-2.67-3.297l5.54-52.71a3 3 0 0 1 3.297-2.67",
        clipRule: "evenodd"
      }
    ),
    (0, import_jsx_runtime.jsx)(
      "path",
      {
        fill: "#F0F0FF",
        fillRule: "evenodd",
        d: "m93.407 74.676 9.798-6.609a4 4 0 0 1 5.167.595l7.174 7.722a1 1 0 0 0 1.362.097l15.34-12.43a4 4 0 0 1 5.877.936l9.981 15.438 1.433 2.392-.686 8.124a1 1 0 0 1-1.107.91l-56.963-6.329a1 1 0 0 1-.885-1.085l.755-8.199z",
        clipRule: "evenodd"
      }
    ),
    (0, import_jsx_runtime.jsx)(
      "path",
      {
        stroke: "#7B79FF",
        strokeWidth: 2.5,
        d: "m156.181 39.21-62.655-6.585c-.48-.05-.936.099-1.284.38a1.75 1.75 0 0 0-.64 1.178l-5.54 52.71c-.05.48.1.936.381 1.284s.697.588 1.177.639l62.655 6.585c.481.05.936-.099 1.284-.38s.589-.697.639-1.177l5.54-52.71a1.74 1.74 0 0 0-.38-1.284 1.74 1.74 0 0 0-1.177-.64Z",
        clipRule: "evenodd"
      }
    ),
    (0, import_jsx_runtime.jsx)(
      "path",
      {
        fill: "#F0F0FF",
        stroke: "#7B79FF",
        strokeWidth: 2.5,
        d: "M105.071 56.714a6 6 0 1 0 1.254-11.936 6 6 0 0 0-1.254 11.936Z"
      }
    ),
    (0, import_jsx_runtime.jsx)(
      "path",
      {
        stroke: "#7B79FF",
        strokeLinecap: "round",
        strokeWidth: 2.5,
        d: "m91.396 76.222 11.809-8.155a4 4 0 0 1 5.167.594l7.174 7.723a1 1 0 0 0 1.362.096l15.34-12.43a4 4 0 0 1 5.877.936l11.064 17.556"
      }
    )
  ] }) }),
  (0, import_jsx_runtime.jsxs)("defs", { children: [
    (0, import_jsx_runtime.jsx)("clipPath", { id: "EmptyPictures_svg__a", children: (0, import_jsx_runtime.jsx)("path", { fill: "#fff", d: "M.667.797h216v120h-216z" }) }),
    (0, import_jsx_runtime.jsx)("clipPath", { id: "EmptyPictures_svg__b", children: (0, import_jsx_runtime.jsx)("path", { fill: "#fff", d: "M.667.797h216v120h-216z" }) })
  ] })
] });
var ForwardRef$E = (0, import_react.forwardRef)(SvgEmptyPictures);
var ForwardRef$F = ForwardRef$E;
var SvgEnumerationField = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("rect", { width: 31, height: 23, x: 0.5, y: 4.5, fill: "#F6ECFC", stroke: "#E0C1F4", rx: 2.5 }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#9736E8",
      d: "M12.75 12a.75.75 0 0 1 .75-.75h8a.75.75 0 1 1 0 1.5h-8a.75.75 0 0 1-.75-.75m8.75 3.25h-8a.75.75 0 1 0 0 1.5h8a.75.75 0 1 0 0-1.5m0 4h-8a.75.75 0 1 0 0 1.5h8a.75.75 0 1 0 0-1.5M10.75 15a1 1 0 1 0 0 2 1 1 0 0 0 0-2m0-4a1 1 0 1 0 0 2 1 1 0 0 0 0-2m0 8a1 1 0 1 0 0 2 1 1 0 0 0 0-2"
    }
  )
] });
var ForwardRef$C = (0, import_react.forwardRef)(SvgEnumerationField);
var ForwardRef$D = ForwardRef$C;
var SvgFacebook = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#1977F3",
      d: "M32 16c0-8.836-7.164-16-16-16S0 7.164 0 16c0 7.985 5.85 14.605 13.5 15.807v-11.18H9.437V16H13.5v-3.526c0-4.01 2.39-6.226 6.044-6.226 1.75 0 3.582.313 3.582.313V10.5h-2.018c-1.987 0-2.608 1.233-2.608 2.5V16h4.437l-.709 4.626H18.5v11.18C26.15 30.607 32 23.989 32 16"
    }
  ),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#FEFEFE",
      d: "M22.228 20.626 22.937 16H18.5v-3.002c0-1.264.619-2.5 2.608-2.5h2.018V6.562s-1.832-.313-3.582-.313c-3.654 0-6.044 2.214-6.044 6.226V16H9.437v4.626H13.5v11.18Q14.724 32 16 32c.85 0 1.685-.068 2.5-.194v-11.18z"
    }
  )
] });
var ForwardRef$A = (0, import_react.forwardRef)(SvgFacebook);
var SvgFeatherSquare = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("path", { fill: "#9736E8", d: "M0 4a4 4 0 0 1 4-4h24a4 4 0 0 1 4 4v24a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4z" }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#fff",
      d: "M18.037 11.774a28.6 28.6 0 0 0-2.948 2.706c-1.995 2.109-3.55 4.093-4.761 6.06-.289.469-.574.945-.855 1.418a9 9 0 0 0-.463 1.536c-.074.37.275.68.577.395.312-.299.587-.64.851-.985.467-.608.906-1.237 1.342-1.867 3.37.242 7.27-2.048 8.933-4.857a.2.2 0 0 0 .017-.167.18.18 0 0 0-.114-.118c-.809-.27-1.798-.44-2.207-.462-.017 0-.034-.014-.037-.035a.04.04 0 0 1 .024-.043c1.113-.58 1.924-.647 2.877-.505.07.01.134-.046.16-.114.095-.217.356-.87.537-1.404a.2.2 0 0 0-.087-.239c-.71-.384-1.656-.643-2.035-.682-.017 0-.03-.018-.034-.036a.04.04 0 0 1 .024-.043c1.1-.483 1.485-.497 2.364-.302.087.018.17-.05.19-.142.433-1.714.574-3.197.608-3.68a.2.2 0 0 0-.057-.157.18.18 0 0 0-.148-.05c-2.444.356-4.403.865-6.093 1.55-.057.022-.11.072-.11.136.144.551-.242 1.209-.845 1.703a.04.04 0 0 1-.044.018.05.05 0 0 1-.027-.043c.004-.046.158-.665.067-1.116-.013-.064-.033-.125-.084-.16a.17.17 0 0 0-.17-.014c-7.924 3.811-5.922 10.098-5.922 10.098q.015.004.03.007c.895-1.86 1.904-3.232 3.49-5.035 1.178-1.337 2.331-2.425 3.525-3.325.75-.565 2.448-1.738 3.51-2.144a.3.3 0 0 1 .105-.021c.097 0 .177.064.2.16a.26.26 0 0 1-.046.228z"
    }
  )
] });
var ForwardRef$y = (0, import_react.forwardRef)(SvgFeatherSquare);
var SvgGitHub = (props, ref) => (0, import_jsx_runtime.jsx)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: (0, import_jsx_runtime.jsx)(
  "path",
  {
    fill: "#24292F",
    fillRule: "evenodd",
    d: "M15.952 0C7.132 0 0 7.184 0 16.07c0 7.105 4.57 13.118 10.908 15.247.792.16 1.083-.346 1.083-.772 0-.372-.027-1.65-.027-2.98-4.437.958-5.361-1.916-5.361-1.916-.713-1.862-1.77-2.34-1.77-2.34-1.452-.985.106-.985.106-.985 1.61.106 2.456 1.65 2.456 1.65 1.426 2.447 3.724 1.755 4.648 1.33.132-1.038.555-1.757 1.004-2.156-3.54-.372-7.263-1.756-7.263-7.929 0-1.756.634-3.193 1.637-4.31-.158-.399-.713-2.049.16-4.257 0 0 1.346-.426 4.383 1.65 1.3-.352 2.641-.531 3.988-.533 1.347 0 2.72.187 3.988.532 3.038-2.075 4.385-1.65 4.385-1.65.871 2.21.316 3.859.158 4.258 1.03 1.117 1.637 2.554 1.637 4.31 0 6.173-3.723 7.53-7.289 7.93.581.505 1.083 1.463 1.083 2.98 0 2.154-.026 3.884-.026 4.416 0 .426.29.932 1.082.772 6.34-2.13 10.908-8.142 10.908-15.246C31.904 7.184 24.748 0 15.952 0",
    clipRule: "evenodd"
  }
) });
var ForwardRef$w = (0, import_react.forwardRef)(SvgGitHub);
var ForwardRef$x = ForwardRef$w;
var SvgGlassesSquare = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("path", { fill: "#AC73E6", d: "M0 4a4 4 0 0 1 4-4h24a4 4 0 0 1 4 4v24a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4z" }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#fff",
      fillRule: "evenodd",
      d: "M15.027 13.839c-3.19-.836-6.305-1.064-10.18-.608-1.215.152-1.063 1.975.076 2.203.304.836.456 2.355.912 3.267.987 2.279 5.622 1.975 7.369.835 1.14-.683 1.443-2.279 1.9-3.494.227-.684 1.595-.684 1.822 0 .38 1.215.76 2.81 1.9 3.494 1.747 1.14 6.381 1.444 7.369-.835.456-.912.607-2.431.911-3.267 1.14-.228 1.216-2.051.076-2.203-3.874-.456-6.989-.228-10.18.608-.455.075-1.519.075-1.975 0",
      clipRule: "evenodd"
    }
  )
] });
var ForwardRef$u = (0, import_react.forwardRef)(SvgGlassesSquare);
var ForwardRef$v = ForwardRef$u;
var SvgInformationSquare = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("path", { fill: "#4945FF", d: "M0 4a4 4 0 0 1 4-4h24a4 4 0 0 1 4 4v24a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4z" }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#fff",
      d: "M15.733 8c.343 0 .678.108.963.31s.507.49.639.826c.13.337.165.707.098 1.064a1.9 1.9 0 0 1-.474.942 1.7 1.7 0 0 1-.887.504 1.64 1.64 0 0 1-1.002-.105 1.76 1.76 0 0 1-.778-.678A1.9 1.9 0 0 1 14 9.841a1.9 1.9 0 0 1 .508-1.302c.325-.345.766-.539 1.225-.539M20 24h-8v-2.265h2.933v-6.23H12.8v-2.266h4.267v8.496H20z"
    }
  )
] });
var ForwardRef$s = (0, import_react.forwardRef)(SvgInformationSquare);
var SvgJsonField = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("rect", { width: 31, height: 23, x: 0.5, y: 4.5, fill: "#EAF5FF", stroke: "#B8E1FF", rx: 2.5 }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#0C75AF",
      d: "M11.425 15.468a2.2 2.2 0 0 1-.36.532q.22.24.36.532c.325.67.325 1.457.325 2.218 0 1.621.115 2 1.25 2a.75.75 0 1 1 0 1.5c-1.196 0-2.012-.431-2.425-1.282-.325-.67-.325-1.457-.325-2.218 0-1.621-.115-2-1.25-2a.75.75 0 1 1 0-1.5c1.135 0 1.25-.379 1.25-2 0-.76 0-1.547.325-2.218.413-.85 1.229-1.282 2.425-1.282a.75.75 0 1 1 0 1.5c-1.135 0-1.25.379-1.25 2 0 .76 0 1.547-.325 2.218M23 15.25c-1.135 0-1.25-.379-1.25-2 0-.76 0-1.547-.325-2.218-.413-.85-1.229-1.282-2.425-1.282a.75.75 0 1 0 0 1.5c1.135 0 1.25.379 1.25 2 0 .76 0 1.547.325 2.218q.142.292.363.532a2.2 2.2 0 0 0-.36.532c-.328.67-.328 1.457-.328 2.218 0 1.621-.115 2-1.25 2a.75.75 0 1 0 0 1.5c1.196 0 2.012-.431 2.425-1.282.325-.67.325-1.457.325-2.218 0-1.621.115-2 1.25-2a.75.75 0 1 0 0-1.5"
    }
  )
] });
var ForwardRef$q = (0, import_react.forwardRef)(SvgJsonField);
var ForwardRef$r = ForwardRef$q;
var SvgMarkdownField = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("rect", { width: 31, height: 23, x: 0.5, y: 4.5, fill: "#EAF5FF", stroke: "#B8E1FF", rx: 2.5 }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#0C75AF",
      d: "M9.75 12a.75.75 0 0 1 .75-.75h11a.75.75 0 1 1 0 1.5h-11a.75.75 0 0 1-.75-.75m.75 3.25h8a.75.75 0 1 0 0-1.5h-8a.75.75 0 1 0 0 1.5m11 1h-11a.75.75 0 1 0 0 1.5h11a.75.75 0 1 0 0-1.5m-3 2.5h-8a.75.75 0 1 0 0 1.5h8a.75.75 0 1 0 0-1.5"
    }
  )
] });
var ForwardRef$o = (0, import_react.forwardRef)(SvgMarkdownField);
var ForwardRef$p = ForwardRef$o;
var SvgMediaField = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("rect", { width: 31, height: 23, x: 0.5, y: 4.5, fill: "#F6ECFC", stroke: "#E0C1F4", rx: 2.5 }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#9736E8",
      d: "M21.5 10.5h-11a1 1 0 0 0-1 1v9a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-9a1 1 0 0 0-1-1m-3.75 3a.75.75 0 1 1 0 1.5.75.75 0 0 1 0-1.5m-7.25 7v-1.75l3.25-3.25 5 5zm11 0h-1.336l-2.25-2.25 1.25-1.25 2.336 2.336z"
    }
  )
] });
var ForwardRef$m = (0, import_react.forwardRef)(SvgMediaField);
var ForwardRef$n = ForwardRef$m;
var SvgMedium = (props, ref) => (0, import_jsx_runtime.jsx)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: (0, import_jsx_runtime.jsx)(
  "path",
  {
    fill: "#32324D",
    d: "M18.05 16.007c0 5.019-4.04 9.087-9.025 9.087-4.984 0-9.025-4.07-9.025-9.087C0 10.99 4.04 6.92 9.025 6.92s9.025 4.069 9.025 9.087M27.95 16.007c0 4.724-2.02 8.555-4.512 8.555s-4.513-3.831-4.513-8.555 2.02-8.555 4.513-8.555 4.512 3.83 4.512 8.555M32 16.007c0 4.231-.71 7.664-1.587 7.664s-1.587-3.432-1.587-7.664.71-7.664 1.587-7.664c.876 0 1.587 3.432 1.587 7.664"
  }
) });
var ForwardRef$k = (0, import_react.forwardRef)(SvgMedium);
var ForwardRef$l = ForwardRef$k;
var SvgNumberField = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("rect", { width: 31, height: 23, x: 0.5, y: 4.5, fill: "#FCECEA", stroke: "#F5C0B8", rx: 2.5 }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#D02B20",
      d: "M8.68 20v-6.22h-.096l-1.902 1.322V13.64l2.004-1.392h1.616V20zm3.733 0v-1.09l2.498-2.466c1.09-1.058 1.385-1.45 1.385-1.992v-.017c0-.66-.45-1.122-1.192-1.122-.757 0-1.278.505-1.278 1.24v.028h-1.499l-.005-.022c0-1.488 1.16-2.508 2.857-2.508 1.595 0 2.713.913 2.713 2.25v.016c0 .881-.457 1.612-1.87 2.917l-1.434 1.337v.124h3.416V20zm9.974.172c-1.75 0-2.906-.94-3.013-2.326l-.005-.07h1.552l.005.06c.07.601.623 1.03 1.461 1.03.827 0 1.37-.461 1.37-1.116v-.011c0-.741-.553-1.15-1.493-1.15h-.887v-1.154h.865c.817 0 1.343-.43 1.343-1.059v-.01c0-.645-.446-1.042-1.209-1.042-.762 0-1.273.413-1.337 1.058l-.005.048H19.54l.005-.064c.113-1.386 1.203-2.288 2.83-2.288 1.665 0 2.74.838 2.74 2.073v.01c0 .967-.71 1.596-1.617 1.784v.032c1.155.107 1.907.773 1.907 1.826v.011c0 1.407-1.209 2.358-3.019 2.358"
    }
  )
] });
var ForwardRef$i = (0, import_react.forwardRef)(SvgNumberField);
var ForwardRef$j = ForwardRef$i;
var SvgPasswordField = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("rect", { width: 31, height: 23, x: 0.5, y: 4.5, fill: "#FDF4DC", stroke: "#FAE7B9", rx: 2.5 }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#D9822F",
      d: "M21 13h-2v-1.5a3 3 0 0 0-6 0V13h-2a1 1 0 0 0-1 1v7a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1v-7a1 1 0 0 0-1-1m-5 5.25a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5M18 13h-4v-1.5a2 2 0 0 1 4 0z"
    }
  )
] });
var ForwardRef$g = (0, import_react.forwardRef)(SvgPasswordField);
var ForwardRef$h = ForwardRef$g;
var SvgPlaySquare = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("path", { fill: "#66B7F1", d: "M0 4a4 4 0 0 1 4-4h24a4 4 0 0 1 4 4v24a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4z" }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#fff",
      fillRule: "evenodd",
      d: "M12 10.921a.5.5 0 0 1 .773-.419l8.582 5.579a.5.5 0 0 1 0 .838l-8.582 5.579a.5.5 0 0 1-.773-.42z",
      clipRule: "evenodd"
    }
  )
] });
var ForwardRef$e = (0, import_react.forwardRef)(SvgPlaySquare);
var SvgReddit = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#FF4500",
      d: "M16 0C7.164 0 0 7.164 0 16a15.95 15.95 0 0 0 4.686 11.314L1.64 30.36c-.605.605-.177 1.639.678 1.639H16c8.836 0 16-7.164 16-16S24.836 0 16 0"
    }
  ),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#fff",
      d: "M19.255 7.545a2.668 2.668 0 0 0 5.261-.614 2.666 2.666 0 0 0-5.277-.54 4.307 4.307 0 0 0-3.84 4.277v.013c-2.345.099-4.487.767-6.187 1.82a3.736 3.736 0 1 0-3.869 6.34c.124 4.338 4.85 7.826 10.664 7.826s10.547-3.492 10.664-7.833a3.737 3.737 0 0 0-1.602-7.111c-.857 0-1.645.288-2.275.773-1.715-1.061-3.88-1.729-6.25-1.817v-.01a3.16 3.16 0 0 1 2.71-3.121zM9.062 17.829c.063-1.355.963-2.395 2.01-2.395 1.045 0 1.845 1.098 1.783 2.454-.063 1.354-.844 1.847-1.891 1.847S9 19.184 9.062 17.829m11.883-2.395c1.047 0 1.947 1.04 2.009 2.395s-.855 1.906-1.902 1.906-1.828-.491-1.89-1.848c-.063-1.355.735-2.453 1.783-2.453m-1.245 5.53c.196.02.321.224.245.406a4.268 4.268 0 0 1-7.875 0 .296.296 0 0 1 .245-.406c1.15-.116 2.394-.18 3.692-.18 1.3 0 2.542.064 3.693.18"
    }
  )
] });
var ForwardRef$c = (0, import_react.forwardRef)(SvgReddit);
var SvgRelationField = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("rect", { width: 31, height: 23, x: 0.5, y: 4.5, fill: "#F0F0FF", stroke: "#D9D8FF", rx: 2.5 }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#4945FF",
      d: "M16.523 19.72a.75.75 0 0 1 0 1.063l-.371.371a3.751 3.751 0 1 1-5.305-5.305l1.507-1.507a3.75 3.75 0 0 1 5.146-.155.753.753 0 0 1-1 1.126 2.25 2.25 0 0 0-3.086.091l-1.506 1.505a2.25 2.25 0 0 0 3.183 3.183l.37-.371a.747.747 0 0 1 1.062 0m4.63-8.874a3.755 3.755 0 0 0-5.305 0l-.371.37a.751.751 0 1 0 1.062 1.063l.372-.37a2.25 2.25 0 1 1 3.182 3.182l-1.507 1.507a2.25 2.25 0 0 1-3.086.09.755.755 0 0 0-1.211.315.75.75 0 0 0 .211.81 3.75 3.75 0 0 0 5.144-.152l1.507-1.507a3.756 3.756 0 0 0 .002-5.307z"
    }
  )
] });
var ForwardRef$a = (0, import_react.forwardRef)(SvgRelationField);
var ForwardRef$b = ForwardRef$a;
var SvgSingleType = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("rect", { width: 31, height: 23, x: 0.5, y: 4.5, fill: "#0C75AF", stroke: "#0C75AF", rx: 2.5 }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#fff",
      d: "M8.523 17.586h1.711c.123.727.844 1.195 1.758 1.195.95 0 1.606-.445 1.606-1.107 0-.492-.352-.797-1.266-1.084l-.879-.276c-1.248-.386-1.963-1.218-1.963-2.308 0-1.547 1.418-2.678 3.328-2.678 1.858 0 3.164 1.078 3.217 2.62h-1.67c-.105-.71-.744-1.184-1.617-1.184-.826 0-1.459.433-1.459 1.03 0 .47.34.815 1.137 1.067l.867.27c1.436.451 2.086 1.154 2.086 2.297 0 1.675-1.418 2.789-3.516 2.789-1.922 0-3.234-.99-3.34-2.631M20.107 20h-1.78l1.487-6.943h-2.53l.31-1.512h6.843l-.31 1.512h-2.531z"
    }
  )
] });
var ForwardRef$8 = (0, import_react.forwardRef)(SvgSingleType);
var ForwardRef$9 = ForwardRef$8;
var SvgStrapi = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#4945FF",
      d: "M0 11.093c0-5.23 0-7.844 1.625-9.468C3.249 0 5.864 0 11.093 0h9.814c5.23 0 7.844 0 9.468 1.625C32 3.249 32 5.864 32 11.093v9.814c0 5.23 0 7.844-1.625 9.468C28.751 32 26.136 32 20.907 32h-9.814c-5.23 0-7.844 0-9.468-1.625C0 28.751 0 26.136 0 20.907z"
    }
  ),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#fff",
      fillRule: "evenodd",
      d: "M22.08 9.707H11.307V15.2H16.8v5.493h5.493V9.92a.213.213 0 0 0-.213-.213",
      clipRule: "evenodd"
    }
  ),
  (0, import_jsx_runtime.jsx)("path", { fill: "#fff", d: "M16.8 15.2h-.213v.213h.213z" }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#9593FF",
      d: "M11.307 15.2h5.28c.117 0 .213.096.213.213v5.28h-5.28a.213.213 0 0 1-.213-.213zM16.8 20.693h5.493l-5.31 5.312a.107.107 0 0 1-.183-.076zM11.307 15.2H6.07a.107.107 0 0 1-.076-.182l5.312-5.311z"
    }
  )
] });
var ForwardRef$6 = (0, import_react.forwardRef)(SvgStrapi);
var SvgTextField = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("rect", { width: 31, height: 23, x: 0.5, y: 4.5, fill: "#EAFBE7", stroke: "#C6F0C2", rx: 2.5 }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#328048",
      d: "M13.679 11.18a.75.75 0 0 0-1.358 0l-4 8.5a.75.75 0 0 0 1.357.64l.974-2.07h4.695l.974 2.07a.75.75 0 1 0 1.358-.64zm-2.32 5.57 1.64-3.489 1.643 3.489zm9.14-3c-.865 0-1.547.241-2.027.717a.749.749 0 1 0 1.056 1.063c.188-.187.516-.283.972-.283.584 0 1.074.323 1.21.757a3 3 0 0 0-1.21-.254c-1.516 0-2.75 1.121-2.75 2.5s1.234 2.5 2.75 2.5c.479.001.95-.114 1.375-.336A.75.75 0 0 0 23.25 20v-3.75c0-1.379-1.234-2.5-2.75-2.5m0 5.5c-.687 0-1.25-.449-1.25-1s.563-1 1.25-1 1.25.449 1.25 1-.562 1-1.25 1"
    }
  )
] });
var ForwardRef$4 = (0, import_react.forwardRef)(SvgTextField);
var ForwardRef$5 = ForwardRef$4;
var SvgUidField = (props, ref) => (0, import_jsx_runtime.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: [
  (0, import_jsx_runtime.jsx)("rect", { width: 31, height: 23, x: 0.5, y: 4.5, fill: "#F0F0FF", stroke: "#D9D8FF", rx: 2.5 }),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#4945FF",
      d: "M18 9a5.005 5.005 0 0 0-4.756 6.549l-3.598 3.597a.5.5 0 0 0-.146.354V22a.5.5 0 0 0 .5.5h2.5a.5.5 0 0 0 .5-.5v-1h1a.5.5 0 0 0 .5-.5v-1h1a.5.5 0 0 0 .354-.146l.597-.598A5 5 0 1 0 18 9m1.25 4.75a1 1 0 1 1 0-2 1 1 0 0 1 0 2"
    }
  )
] });
var ForwardRef$2 = (0, import_react.forwardRef)(SvgUidField);
var ForwardRef$32 = ForwardRef$2;
var SvgX = (props, ref) => (0, import_jsx_runtime.jsx)("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 32 32", width: 16, height: 16, ref, ...props, children: (0, import_jsx_runtime.jsxs)("g", { fillRule: "evenodd", clipRule: "evenodd", children: [
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#FAFAFA",
      d: "M6.566 6.533c.064.092 1.557 2.264 3.317 4.828l3.617 5.268c.23.334.418.614.418.622s-.086.114-.19.234-.4.462-.654.76l-3.258 3.787c-1.153 1.34-1.32 1.534-2.197 2.556-.47.546-.919 1.068-1 1.16s-.146.177-.146.189c0 .014.295.021.83.021h.83l.911-1.062 1.1-1.279a888 888 0 0 0 2.243-2.61c.043-.048.377-.437.744-.864s.676-.787.689-.8l.431-.502a9 9 0 0 1 .424-.478c.009 0 1.164 1.672 2.567 3.717l2.608 3.797.055.08h2.846c2.34.001 2.843-.004 2.834-.027-.01-.025-1.373-2.013-4.87-7.103-2.517-3.665-2.852-4.157-2.843-4.182.01-.024.353-.425 2.607-3.049l1.779-2.07c.062-.07.388-.45.724-.84l1.96-2.283c.027-.035-.02-.038-.814-.038h-.842l-.375.437a1174 1174 0 0 1-2.23 2.594c-.084.096-.506.586-.938 1.09a129 129 0 0 1-1.004 1.167c-.186.22-.374.44-1.239 1.442-.38.44-.399.459-.43.418-.02-.023-1.132-1.64-2.473-3.594L12.16 6.366H6.45zm2.228 1.165 1.186 1.7c1.196 1.71 5.895 8.436 8.917 12.763a421 421 0 0 0 1.783 2.54c.02.022.301.026 1.314.022l1.287-.005-3.37-4.823-5.963-8.534-2.593-3.712-1.3-.005-1.3-.006z"
    }
  ),
  (0, import_jsx_runtime.jsx)(
    "path",
    {
      fill: "#040404",
      d: "M0 16v16l16.005-.005 16.006-.006.005-15.994L32.022 0H0zm.01.01c0 8.8.003 12.4.006 8s.003-11.6 0-16-.005-.8-.005 8m6.556-9.477c.064.092 1.557 2.264 3.317 4.828l3.617 5.268c.23.334.418.614.418.622s-.086.114-.19.234-.399.462-.654.76l-2.014 2.34-1.244 1.447c-1.153 1.34-1.32 1.534-2.197 2.556-.469.546-.918 1.068-1 1.16-.08.092-.146.177-.146.189 0 .014.295.021.83.021h.83l.911-1.062c.502-.585.996-1.16 1.1-1.279a888 888 0 0 0 2.243-2.61c.043-.048.377-.437.744-.864l.689-.8.431-.502a9 9 0 0 1 .424-.478c.009 0 1.164 1.672 2.567 3.717l2.608 3.797.056.08h2.845c2.34.001 2.843-.004 2.834-.027-.01-.025-1.373-2.013-4.87-7.103-2.517-3.665-2.852-4.157-2.842-4.182.009-.024.352-.425 2.606-3.049l1.78-2.07.723-.84 1.96-2.283c.027-.035-.02-.038-.814-.038h-.842l-.375.437a1129 1129 0 0 1-2.23 2.594c-.084.096-.506.586-.938 1.09a129 129 0 0 1-1.004 1.167c-.186.22-.374.44-1.239 1.442-.38.44-.399.459-.43.418-.02-.023-1.132-1.64-2.473-3.594L12.16 6.366H6.45zm2.228 1.165 1.186 1.7 8.918 12.763a416 416 0 0 0 1.782 2.54c.02.022.301.026 1.314.022l1.287-.005-3.37-4.823-5.963-8.534-2.593-3.712-1.3-.005-1.3-.006z"
    }
  )
] }) });
var ForwardRef = (0, import_react.forwardRef)(SvgX);
var ForwardRef$1 = ForwardRef;

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useAPIErrorHandler.mjs
var React = __toESM(require_react(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/utils/getPrefixedId.mjs
function getPrefixedId(message, callback) {
  const prefixedMessage = `apiError.${message}`;
  if (typeof callback === "function") {
    return callback(prefixedMessage);
  }
  return prefixedMessage;
}

// node_modules/@strapi/admin/dist/admin/admin/src/utils/normalizeAPIError.mjs
function normalizeError(error, { name, intlMessagePrefixCallback }) {
  const { message } = error;
  const normalizedError = {
    id: getPrefixedId(message, intlMessagePrefixCallback),
    defaultMessage: message,
    name: error.name ?? name,
    values: {}
  };
  if ("path" in error) {
    normalizedError.values = {
      path: error.path.join(".")
    };
  }
  return normalizedError;
}
var validateErrorIsYupValidationError = (err) => typeof err.details === "object" && err.details !== null && "errors" in err.details;
function normalizeAPIError(apiError, intlMessagePrefixCallback) {
  var _a, _b;
  const error = (_b = (_a = apiError.response) == null ? void 0 : _a.data) == null ? void 0 : _b.error;
  if (error) {
    if (validateErrorIsYupValidationError(error)) {
      return {
        name: error.name,
        message: (error == null ? void 0 : error.message) || null,
        errors: error.details.errors.map((err) => normalizeError(err, {
          name: error.name,
          intlMessagePrefixCallback
        }))
      };
    }
    return normalizeError(error, {
      intlMessagePrefixCallback
    });
  }
  return null;
}

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useAPIErrorHandler.mjs
function useAPIErrorHandler(intlMessagePrefixCallback) {
  const { formatMessage } = useIntl();
  const formatError = React.useCallback((error) => {
    try {
      const formattedErr = formatAPIError(error, {
        intlMessagePrefixCallback,
        formatMessage
      });
      if (!formattedErr) {
        return formatFetchError(error, {
          intlMessagePrefixCallback,
          formatMessage
        });
      }
      return formattedErr;
    } catch (_) {
      throw new Error("formatAPIError: Unknown error:", error);
    }
  }, [
    formatMessage,
    intlMessagePrefixCallback
  ]);
  return {
    /**
    * @alpha
    * Convert ValidationErrors from the API into an object that can be used by forms.
    */
    _unstableFormatValidationErrors: React.useCallback((error) => {
      if (typeof error.details === "object" && error.details !== null) {
        if ("errors" in error.details && Array.isArray(error.details.errors)) {
          const validationErrors = error.details.errors;
          return validationErrors.reduce((acc, err) => {
            const { path, message } = err;
            return setIn(acc, path.join("."), message);
          }, {});
        } else {
          const details = error.details;
          return Object.keys(details).reduce((acc, key) => {
            const messages = details[key];
            return {
              ...acc,
              [key]: messages.join(", ")
            };
          }, {});
        }
      } else {
        return {};
      }
    }, []),
    /**
    * @alpha
    * This handles the errors given from `redux-toolkit`'s axios based baseQuery function.
    */
    _unstableFormatAPIError: React.useCallback((error) => {
      const err = {
        response: {
          data: {
            error
          }
        }
      };
      if (!error.message) {
        return "Unknown error occured.";
      }
      return formatError(err);
    }, [
      formatError
    ]),
    formatAPIError: formatError
  };
}
function formatFetchError(error, { intlMessagePrefixCallback, formatMessage }) {
  const { code, message } = error;
  return formatMessage({
    id: getPrefixedId(message, intlMessagePrefixCallback),
    defaultMessage: message
  }, {
    code
  });
}
function formatAPIError(error, { formatMessage, intlMessagePrefixCallback }) {
  if (!formatMessage) {
    throw new Error("The formatMessage callback is a mandatory argument.");
  }
  const normalizedError = normalizeAPIError(error, intlMessagePrefixCallback);
  if (!normalizedError) {
    return null;
  }
  if ("message" in normalizedError && normalizedError.message !== null) {
    return normalizedError.message;
  }
  if ("errors" in normalizedError) {
    return normalizedError.errors.map(({ id, defaultMessage, values }) => formatMessage({
      id,
      defaultMessage
    }, values)).join("\n");
  }
  return formatMessage(normalizedError);
}

// node_modules/@strapi/admin/dist/admin/admin/src/components/PageHelpers.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var React2 = __toESM(require_react(), 1);
var PageMain = ({ children, ...restProps }) => {
  return (0, import_jsx_runtime2.jsx)(Main, {
    ...restProps,
    children
  });
};
var Loading = ({ children = "Loading content." }) => {
  return (0, import_jsx_runtime2.jsx)(PageMain, {
    height: "100vh",
    "aria-busy": true,
    children: (0, import_jsx_runtime2.jsx)(Flex, {
      alignItems: "center",
      height: "100%",
      justifyContent: "center",
      children: (0, import_jsx_runtime2.jsx)(Loader, {
        children
      })
    })
  });
};
var Error2 = (props) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime2.jsx)(PageMain, {
    height: "100%",
    children: (0, import_jsx_runtime2.jsx)(Flex, {
      alignItems: "center",
      height: "100%",
      justifyContent: "center",
      children: (0, import_jsx_runtime2.jsx)(EmptyStateLayout, {
        icon: (0, import_jsx_runtime2.jsx)(ForwardRef$3, {
          width: "16rem"
        }),
        content: formatMessage({
          id: "anErrorOccurred",
          defaultMessage: "Whoops! Something went wrong. Please, try again."
        }),
        ...props
      })
    })
  });
};
var NoPermissions = (props) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime2.jsx)(PageMain, {
    height: "100%",
    children: (0, import_jsx_runtime2.jsx)(Flex, {
      alignItems: "center",
      height: "100%",
      justifyContent: "center",
      children: (0, import_jsx_runtime2.jsx)(Box, {
        minWidth: "50%",
        children: (0, import_jsx_runtime2.jsx)(EmptyStateLayout, {
          icon: (0, import_jsx_runtime2.jsx)(ForwardRef$H, {
            width: "16rem"
          }),
          content: formatMessage({
            id: "app.components.EmptyStateLayout.content-permissions",
            defaultMessage: "You don't have the permissions to access that content"
          }),
          ...props
        })
      })
    })
  });
};
var NoData = (props) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime2.jsx)(PageMain, {
    height: "100%",
    background: "neutral100",
    children: (0, import_jsx_runtime2.jsx)(Flex, {
      alignItems: "center",
      height: "100%",
      width: "100%",
      justifyContent: "center",
      children: (0, import_jsx_runtime2.jsx)(Box, {
        minWidth: "50%",
        children: (0, import_jsx_runtime2.jsx)(EmptyStateLayout, {
          icon: (0, import_jsx_runtime2.jsx)(ForwardRef$J, {
            width: "16rem"
          }),
          action: props.action,
          content: formatMessage({
            id: "app.components.EmptyStateLayout.content-document",
            defaultMessage: "No content found"
          }),
          ...props
        })
      })
    })
  });
};
var Protect = ({ permissions = [], children }) => {
  const userPermissions = useAuth("Protect", (state) => state.permissions);
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError2 } = useAPIErrorHandler();
  const matchingPermissions = userPermissions.filter((permission) => permissions.findIndex((perm) => perm.action === permission.action && perm.subject === permission.subject) >= 0);
  const shouldCheckConditions = matchingPermissions.some((perm) => Array.isArray(perm.conditions) && perm.conditions.length > 0);
  const { isLoading, error, data } = useCheckPermissionsQuery({
    permissions: matchingPermissions.map((perm) => ({
      action: perm.action,
      subject: perm.subject
    }))
  }, {
    skip: !shouldCheckConditions
  });
  React2.useEffect(() => {
    if (error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError2(error)
      });
    }
  }, [
    error,
    formatAPIError2,
    toggleNotification
  ]);
  if (isLoading) {
    return (0, import_jsx_runtime2.jsx)(Loading, {});
  }
  if (error) {
    return (0, import_jsx_runtime2.jsx)(Error2, {});
  }
  const { data: permissionsData } = data || {};
  const canAccess = shouldCheckConditions && permissionsData ? !permissionsData.includes(false) : matchingPermissions.length > 0;
  if (!canAccess) {
    return (0, import_jsx_runtime2.jsx)(NoPermissions, {});
  }
  return (0, import_jsx_runtime2.jsx)(import_jsx_runtime2.Fragment, {
    children: typeof children === "function" ? children({
      permissions: matchingPermissions
    }) : children
  });
};
var Title = ({ children: title }) => {
  React2.useEffect(() => {
    document.title = `${title} | Strapi`;
  }, [
    title
  ]);
  return null;
};
var Page = {
  Error: Error2,
  Loading,
  NoPermissions,
  Protect,
  NoData,
  Main: PageMain,
  Title
};

export {
  ForwardRef$13,
  ForwardRef$11,
  ForwardRef$Z,
  ForwardRef$X,
  ForwardRef$V,
  ForwardRef$P,
  ForwardRef$N,
  ForwardRef$J,
  ForwardRef$H,
  ForwardRef$F,
  ForwardRef$D,
  ForwardRef$x,
  ForwardRef$v,
  ForwardRef$r,
  ForwardRef$p,
  ForwardRef$n,
  ForwardRef$l,
  ForwardRef$j,
  ForwardRef$h,
  ForwardRef$b,
  ForwardRef$9,
  ForwardRef$5,
  ForwardRef$32 as ForwardRef$3,
  ForwardRef$1,
  useAPIErrorHandler,
  Page
};
//# sourceMappingURL=chunk-7LKLOY7A.js.map
