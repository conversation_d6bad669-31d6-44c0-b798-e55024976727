import {
  CSS,
  DndContext,
  DragOverlay,
  PointerSensor,
  SortableContext,
  closestCenter,
  useSensor,
  useSensors,
  useSortable,
  verticalListSortingStrategy
} from "./chunk-25ZR4HQ4.js";
import {
  AttributeIcon,
  COMPONENT_ICONS,
  StatusBadge,
  getTrad,
  useDataManager,
  useFormModalNavigation
} from "./chunk-WVL7LKFO.js";
import {
  getRelationType
} from "./chunk-H4GPAAVJ.js";
import "./chunk-EKXSMIUH.js";
import "./chunk-YV3ONWF5.js";
import "./chunk-UMW22TSS.js";
import "./chunk-4QC3H4VA.js";
import "./chunk-75D2ZJP5.js";
import "./chunk-VCTAT6B3.js";
import "./chunk-ROZIXYJG.js";
import "./chunk-C72RZIDJ.js";
import "./chunk-HZKRK7AR.js";
import "./chunk-LRN6A2UC.js";
import "./chunk-D2TGW5YS.js";
import "./chunk-M27D4U76.js";
import "./chunk-HX66WGOY.js";
import "./chunk-Y4UEUAII.js";
import "./chunk-BN2UQHMJ.js";
import "./chunk-NWWGC2Z2.js";
import "./chunk-MBK4V2X7.js";
import "./chunk-DY2RJG3P.js";
import "./chunk-K65KIEAL.js";
import "./chunk-BUDFB33L.js";
import "./chunk-7MILHJ3J.js";
import "./chunk-SGQJOZK5.js";
import "./chunk-AFM2NWPO.js";
import "./chunk-DUGZ4WIW.js";
import "./chunk-IFOFBKTA.js";
import {
  require_upperFirst
} from "./chunk-376QHLWZ.js";
import "./chunk-EGNP2T5O.js";
import {
  useTracking
} from "./chunk-XDCEA27D.js";
import "./chunk-EZSYDDUK.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-QIJGNK42.js";
import "./chunk-CJHUGFLE.js";
import "./chunk-IQGHPIIW.js";
import "./chunk-DWSGKQEK.js";
import "./chunk-W6ZGCRX6.js";
import "./chunk-PVCRV2LE.js";
import "./chunk-HWAQQGJJ.js";
import "./chunk-L5JCPKMP.js";
import {
  useRBAC
} from "./chunk-ZJEMJY2Q.js";
import "./chunk-6DRYEU2W.js";
import {
  Layouts
} from "./chunk-MTTHLNPH.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import {
  ForwardRef$J
} from "./chunk-7LKLOY7A.js";
import "./chunk-ODQFI753.js";
import "./chunk-ZOP4VV6J.js";
import {
  require_get
} from "./chunk-WH6VCVXU.js";
import "./chunk-IL5G2D22.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-76QM3EFM.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import "./chunk-UBCTZOSQ.js";
import {
  Box,
  Button,
  Divider,
  EmptyStateLayout,
  Flex,
  IconButton,
  Link as Link2,
  Typography,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import "./chunk-5ZC4PE57.js";
import {
  Link,
  Navigate,
  useNavigate,
  useParams
} from "./chunk-S65ZWNEO.js";
import {
  require_react_dom
} from "./chunk-FOD4ENRR.js";
import {
  ForwardRef$1h,
  ForwardRef$1v,
  ForwardRef$2D,
  ForwardRef$2h,
  ForwardRef$2n,
  ForwardRef$3T,
  ForwardRef$45,
  ForwardRef$4z,
  ForwardRef$j
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-type-builder/dist/admin/pages/ListView/ListView.mjs
var import_jsx_runtime11 = __toESM(require_jsx_runtime(), 1);
var import_upperFirst2 = __toESM(require_upperFirst(), 1);

// node_modules/@strapi/content-type-builder/dist/admin/components/List.mjs
var import_jsx_runtime9 = __toESM(require_jsx_runtime(), 1);
var import_react3 = __toESM(require_react(), 1);

// node_modules/@dnd-kit/modifiers/dist/modifiers.esm.js
var restrictToVerticalAxis = (_ref) => {
  let {
    transform
  } = _ref;
  return {
    ...transform,
    x: 0
  };
};

// node_modules/@strapi/content-type-builder/dist/admin/components/List.mjs
var import_react_dom = __toESM(require_react_dom(), 1);

// node_modules/@strapi/content-type-builder/dist/admin/components/AttributeRow.mjs
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_get3 = __toESM(require_get(), 1);
var import_upperFirst = __toESM(require_upperFirst(), 1);

// node_modules/@strapi/content-type-builder/dist/admin/icons/Curve.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var StyledBox = dt(Box)`
  position: absolute;
  left: -3.4rem;
  top: 0px;

  &:before {
    content: '';
    width: 0.4rem;
    height: 1.2rem;
    background: ${({ theme, color }) => theme.colors[color]};
    display: block;
  }
`;
var Svg = dt.svg`
  position: relative;
  flex-shrink: 0;
  transform: translate(-0.5px, -1px);

  * {
    fill: ${({ theme, color }) => theme.colors[color]};
  }
`;
var Curve = (props) => (0, import_jsx_runtime.jsx)(StyledBox, {
  children: (0, import_jsx_runtime.jsx)(Svg, {
    width: "20",
    height: "23",
    viewBox: "0 0 20 23",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ...props,
    children: (0, import_jsx_runtime.jsx)("path", {
      fillRule: "evenodd",
      clipRule: "evenodd",
      d: "M7.02477 14.7513C8.65865 17.0594 11.6046 18.6059 17.5596 18.8856C18.6836 18.9384 19.5976 19.8435 19.5976 20.9688V20.9688C19.5976 22.0941 18.6841 23.0125 17.5599 22.9643C10.9409 22.6805 6.454 20.9387 3.75496 17.1258C0.937988 13.1464 0.486328 7.39309 0.486328 0.593262H4.50974C4.50974 7.54693 5.06394 11.9813 7.02477 14.7513Z"
    })
  })
});

// node_modules/@strapi/content-type-builder/dist/admin/utils/getAttributeDisplayedType.mjs
var getAttributeDisplayedType = (type) => {
  let displayedType;
  switch (type) {
    case "date":
    case "datetime":
    case "time":
    case "timestamp":
      displayedType = "date";
      break;
    case "integer":
    case "biginteger":
    case "decimal":
    case "float":
      displayedType = "number";
      break;
    case "string":
    case "text":
      displayedType = "text";
      break;
    case "":
      displayedType = "relation";
      break;
    default:
      displayedType = type;
  }
  return displayedType;
};

// node_modules/@strapi/content-type-builder/dist/admin/components/ComponentList.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_get = __toESM(require_get(), 1);

// node_modules/@strapi/content-type-builder/dist/admin/components/ComponentRow.mjs
var ComponentRow = dt(Box)`
  &.component-row,
  &.dynamiczone-row {
    position: relative;

    > ul:first-of-type {
      padding: 0 0 0 104px;
      position: relative;

      &::before {
        content: '';
        width: 0.4rem;
        height: ${({ $isFromDynamicZone }) => $isFromDynamicZone ? "calc(100% - 65px)" : "calc(100%)"};
        position: absolute;
        left: 7rem;
        border-radius: 4px;

        ${({ $isFromDynamicZone, $isChildOfDynamicZone, theme }) => {
  if ($isChildOfDynamicZone) {
    return `background-color: ${theme.colors.primary200};`;
  }
  if ($isFromDynamicZone) {
    return `background-color: ${theme.colors.primary200};`;
  }
  return `background: ${theme.colors.neutral150};`;
}}
      }
    }
  }

  &.dynamiczone-row > ul:first-of-type {
    padding: 0;
  }
`;

// node_modules/@strapi/content-type-builder/dist/admin/components/ComponentList.mjs
var ComponentList = ({ component, isFromDynamicZone = false, firstLoopComponentUid }) => {
  const { components } = useDataManager();
  const type = (0, import_get.default)(components, component);
  return (0, import_jsx_runtime2.jsx)(ComponentRow, {
    $isChildOfDynamicZone: isFromDynamicZone,
    className: "component-row",
    children: (0, import_jsx_runtime2.jsx)(List, {
      type,
      firstLoopComponentUid: firstLoopComponentUid || component,
      isFromDynamicZone,
      isSub: true,
      secondLoopComponentUid: firstLoopComponentUid ? component : null
    })
  });
};

// node_modules/@strapi/content-type-builder/dist/admin/components/DisplayedType.mjs
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var DisplayedType = ({ type, customField = null, repeatable = false, multiple = false }) => {
  const { formatMessage } = useIntl();
  let readableType = type;
  if ([
    "integer",
    "biginteger",
    "float",
    "decimal"
  ].includes(type)) {
    readableType = "number";
  } else if ([
    "string"
  ].includes(type)) {
    readableType = "text";
  }
  if (customField) {
    return formatMessage({
      id: getTrad("attribute.customField"),
      defaultMessage: "Custom field"
    });
  }
  return (0, import_jsx_runtime3.jsxs)(import_jsx_runtime3.Fragment, {
    children: [
      repeatable && formatMessage({
        id: getTrad("component.repeatable"),
        defaultMessage: "Repeatable"
      }),
      multiple && formatMessage({
        id: getTrad("media.multiple"),
        defaultMessage: "Multiple"
      }),
      " ",
      formatMessage({
        id: getTrad(`attribute.${readableType}`),
        defaultMessage: type
      })
    ]
  });
};

// node_modules/@strapi/content-type-builder/dist/admin/components/DynamicZoneList.mjs
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);

// node_modules/@strapi/content-type-builder/dist/admin/components/ComponentCard/ComponentCard.mjs
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_get2 = __toESM(require_get(), 1);

// node_modules/@strapi/content-type-builder/dist/admin/components/ComponentCard/ComponentIcon/ComponentIcon.mjs
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var ComponentIcon = ({ isActive = false, icon = "dashboard" }) => {
  const Icon = COMPONENT_ICONS[icon] || COMPONENT_ICONS.dashboard;
  return (0, import_jsx_runtime4.jsx)(Flex, {
    alignItems: "center",
    background: isActive ? "primary200" : "neutral200",
    justifyContent: "center",
    height: 8,
    width: 8,
    borderRadius: "50%",
    children: (0, import_jsx_runtime4.jsx)(Icon, {
      height: "2rem",
      width: "2rem"
    })
  });
};

// node_modules/@strapi/content-type-builder/dist/admin/components/ComponentCard/ComponentCard.mjs
var CloseButton = dt(Box)`
  position: absolute;
  display: none;
  top: 5px;
  right: 0.8rem;

  svg {
    width: 1rem;
    height: 1rem;

    path {
      fill: ${({ theme }) => theme.colors.primary600};
    }
  }
`;
var ComponentBox = dt(Flex)`
  width: 14rem;
  height: 8rem;
  position: relative;
  border: 1px solid ${({ theme }) => theme.colors.neutral200};
  background: ${({ theme }) => theme.colors.neutral100};
  border-radius: ${({ theme }) => theme.borderRadius};
  max-width: 100%;

  &.active,
  &:focus,
  &:hover {
    border: 1px solid ${({ theme }) => theme.colors.primary200};
    background: ${({ theme }) => theme.colors.primary100};
    color: ${({ theme }) => theme.colors.primary600};

    ${CloseButton} {
      display: block;
    }

    /* > ComponentIcon */
    > div:first-child {
      background: ${({ theme }) => theme.colors.primary200};
      color: ${({ theme }) => theme.colors.primary600};

      svg {
        path {
          fill: ${({ theme }) => theme.colors.primary600};
        }
      }
    }
  }
`;
var ComponentCard = ({ component, dzName, index, isActive = false, isInDevelopmentMode = false, onClick, forTarget, targetUid, disabled }) => {
  const { components, removeComponentFromDynamicZone } = useDataManager();
  const type = (0, import_get2.default)(components, component);
  const { icon, displayName } = (type == null ? void 0 : type.info) || {};
  const onClose = (e) => {
    e.stopPropagation();
    removeComponentFromDynamicZone({
      forTarget,
      targetUid,
      dzName,
      componentToRemoveIndex: index
    });
  };
  return (0, import_jsx_runtime5.jsxs)(ComponentBox, {
    alignItems: "center",
    direction: "column",
    className: isActive ? "active" : "",
    borderRadius: "borderRadius",
    justifyContent: "center",
    paddingLeft: 4,
    paddingRight: 4,
    shrink: 0,
    onClick,
    role: "tab",
    tabIndex: isActive ? 0 : -1,
    cursor: "pointer",
    "aria-selected": isActive,
    "aria-controls": `dz-${dzName}-panel-${index}`,
    id: `dz-${dzName}-tab-${index}`,
    children: [
      (0, import_jsx_runtime5.jsx)(ComponentIcon, {
        icon,
        isActive
      }),
      (0, import_jsx_runtime5.jsx)(Box, {
        marginTop: 1,
        maxWidth: "100%",
        children: (0, import_jsx_runtime5.jsx)(Typography, {
          variant: "pi",
          fontWeight: "bold",
          ellipsis: true,
          children: displayName
        })
      }),
      isInDevelopmentMode && !disabled && (0, import_jsx_runtime5.jsx)(CloseButton, {
        cursor: "pointer",
        tag: "button",
        onClick: onClose,
        children: (0, import_jsx_runtime5.jsx)(ForwardRef$45, {})
      })
    ]
  });
};

// node_modules/@strapi/content-type-builder/dist/admin/components/DynamicZoneList.mjs
var StyledAddIcon = dt(ForwardRef$1h)`
  width: 3.2rem;
  height: 3.2rem;
  padding: 0.9rem;
  border-radius: 6.4rem;
  background: ${({ theme, disabled }) => disabled ? theme.colors.neutral100 : theme.colors.primary100};
  path {
    fill: ${({ theme, disabled }) => disabled ? theme.colors.neutral600 : theme.colors.primary600};
  }
`;
var ComponentStack = dt(Flex)`
  flex-shrink: 0;
  width: 14rem;
  height: 8rem;
  justify-content: center;
  align-items: center;
`;
var DynamicZoneList = ({ components = [], addComponent, name, forTarget, targetUid, disabled = false }) => {
  const { isInDevelopmentMode } = useDataManager();
  const [activeTab, setActiveTab] = (0, import_react.useState)(0);
  const { formatMessage } = useIntl();
  const toggle = (tab) => {
    if (activeTab !== tab) {
      setActiveTab(tab);
    }
  };
  const handleClickAdd = () => {
    addComponent(name);
  };
  return (0, import_jsx_runtime6.jsx)(ComponentRow, {
    className: "dynamiczone-row",
    $isFromDynamicZone: true,
    children: (0, import_jsx_runtime6.jsxs)(Box, {
      children: [
        (0, import_jsx_runtime6.jsx)(Box, {
          padding: 2,
          paddingLeft: "104px",
          children: (0, import_jsx_runtime6.jsxs)(Flex, {
            role: "tablist",
            gap: 2,
            wrap: "wrap",
            children: [
              isInDevelopmentMode && (0, import_jsx_runtime6.jsx)("button", {
                type: "button",
                onClick: handleClickAdd,
                disabled,
                style: {
                  cursor: disabled ? "not-allowed" : "pointer"
                },
                children: (0, import_jsx_runtime6.jsxs)(ComponentStack, {
                  direction: "column",
                  alignItems: "stretch",
                  gap: 1,
                  children: [
                    (0, import_jsx_runtime6.jsx)(StyledAddIcon, {
                      disabled
                    }),
                    (0, import_jsx_runtime6.jsx)(Typography, {
                      variant: "pi",
                      fontWeight: "bold",
                      textColor: disabled ? "neutral600" : "primary600",
                      children: formatMessage({
                        id: getTrad("button.component.add"),
                        defaultMessage: "Add a component"
                      })
                    })
                  ]
                })
              }),
              components.map((component, index) => {
                return (0, import_jsx_runtime6.jsx)(ComponentCard, {
                  dzName: name || "",
                  index,
                  component,
                  isActive: activeTab === index,
                  isInDevelopmentMode,
                  onClick: () => toggle(index),
                  forTarget,
                  targetUid,
                  disabled
                }, component);
              })
            ]
          })
        }),
        (0, import_jsx_runtime6.jsx)(Box, {
          children: components.map((component, index) => {
            return (0, import_jsx_runtime6.jsx)(Box, {
              id: `dz-${name}-panel-${index}`,
              role: "tabpanel",
              "aria-labelledby": `dz-${name}-tab-${index}`,
              style: {
                display: activeTab === index ? "block" : "none"
              },
              children: (0, import_jsx_runtime6.jsx)(ComponentList, {
                isFromDynamicZone: true,
                component
              }, component)
            }, component);
          })
        })
      ]
    })
  });
};

// node_modules/@strapi/content-type-builder/dist/admin/components/AttributeRow.mjs
var GridWrapper = dt(Flex)`
  justify-content: space-between;

  border-top: ${({ theme, $isOverlay }) => $isOverlay ? "none" : `1px solid ${theme.colors.neutral150}`};

  padding-top: ${({ theme }) => theme.spaces[4]};
  padding-bottom: ${({ theme }) => theme.spaces[4]};

  opacity: ${({ $isDragging }) => $isDragging ? 0 : 1};
  align-items: center;
`;
var StyledAttributeRow = dt(Box)`
  list-style: none;
  list-style-type: none;
`;
var AttributeRow = (0, import_react2.forwardRef)((props, ref) => {
  const { style, ...rest } = props;
  return (0, import_jsx_runtime7.jsx)(StyledAttributeRow, {
    tag: "li",
    ref,
    ...props.attributes,
    style,
    background: "neutral0",
    shadow: props.isOverlay ? "filterShadow" : "none",
    "aria-label": props.item.name,
    children: (0, import_jsx_runtime7.jsx)(MemoizedRow, {
      ...rest
    })
  });
});
var MemoizedRow = (0, import_react2.memo)((props) => {
  const { item, firstLoopComponentUid, isFromDynamicZone, addComponentToDZ, secondLoopComponentUid, type, isDragging, isOverlay, handleRef, listeners } = props;
  const shouldHideNestedInfos = isOverlay || isDragging;
  const [isOpen, setIsOpen] = (0, import_react2.useState)(true);
  const isTypeDeleted = type.status === "REMOVED";
  const { contentTypes, removeAttribute, isInDevelopmentMode } = useDataManager();
  const { onOpenModalEditField, onOpenModalEditCustomField } = useFormModalNavigation();
  const { formatMessage } = useIntl();
  const isDeleted = item.status === "REMOVED";
  const isMorph = item.type === "relation" && item.relation.includes("morph");
  const ico = [
    "integer",
    "biginteger",
    "float",
    "decimal"
  ].includes(item.type) ? "number" : item.type;
  const targetContentType = item.type === "relation" ? (0, import_get3.default)(contentTypes, item.target) : null;
  const isPluginContentType = (0, import_get3.default)(targetContentType, "plugin");
  const src = "target" in item && item.target ? "relation" : ico;
  const handleClick = () => {
    if (isMorph) {
      return;
    }
    if (item.configurable !== false) {
      const editTargetUid = secondLoopComponentUid || firstLoopComponentUid || type.uid;
      const attributeType = getAttributeDisplayedType(item.type);
      const step = item.type === "component" ? "2" : null;
      if (item.customField) {
        onOpenModalEditCustomField({
          forTarget: type.modelType,
          targetUid: editTargetUid,
          attributeName: item.name,
          attributeType,
          customFieldUid: item.customField
        });
      } else {
        onOpenModalEditField({
          forTarget: type.modelType,
          targetUid: editTargetUid,
          attributeName: item.name,
          attributeType,
          step
        });
      }
    }
  };
  let loopNumber;
  if (secondLoopComponentUid && firstLoopComponentUid) {
    loopNumber = 2;
  } else if (firstLoopComponentUid) {
    loopNumber = 1;
  } else {
    loopNumber = 0;
  }
  const canEdit = !isTypeDeleted && !isDeleted;
  const canDelete = !isTypeDeleted && !isDeleted;
  const cursor = isTypeDeleted || isDeleted ? "not-allowed" : "move";
  const canClick = isInDevelopmentMode && item.configurable !== false && !isMorph && canEdit;
  return (0, import_jsx_runtime7.jsxs)(import_jsx_runtime7.Fragment, {
    children: [
      (0, import_jsx_runtime7.jsxs)(GridWrapper, {
        $isOverlay: isOverlay,
        $isDragging: isDragging,
        onClick: canClick ? handleClick : void 0,
        paddingLeft: 4,
        paddingRight: 4,
        children: [
          (0, import_jsx_runtime7.jsxs)(Flex, {
            alignItems: "center",
            overflow: "hidden",
            gap: 2,
            children: [
              loopNumber !== 0 && !isOverlay && (0, import_jsx_runtime7.jsx)(Curve, {
                color: isFromDynamicZone ? "primary200" : "neutral150"
              }),
              isInDevelopmentMode && (0, import_jsx_runtime7.jsx)(IconButton, {
                cursor,
                role: "Handle",
                ref: handleRef,
                ...listeners,
                variant: "ghost",
                withTooltip: false,
                label: `${formatMessage({
                  id: "app.utils.drag",
                  defaultMessage: "Drag"
                })} ${item.name}`,
                disabled: isTypeDeleted || isDeleted,
                children: (0, import_jsx_runtime7.jsx)(ForwardRef$3T, {})
              }),
              (0, import_jsx_runtime7.jsxs)(Flex, {
                gap: 4,
                children: [
                  (0, import_jsx_runtime7.jsxs)(Flex, {
                    gap: 4,
                    alignItems: "center",
                    children: [
                      (0, import_jsx_runtime7.jsx)(AttributeIcon, {
                        type: src,
                        customField: item.customField
                      }),
                      (0, import_jsx_runtime7.jsxs)(Typography, {
                        textColor: "neutral800",
                        fontWeight: "bold",
                        textDecoration: isDeleted ? "line-through" : "none",
                        ellipsis: true,
                        overflow: "hidden",
                        children: [
                          item.name,
                          "required" in item && item.required && (0, import_jsx_runtime7.jsx)(Typography, {
                            textColor: "danger600",
                            children: "* "
                          })
                        ]
                      })
                    ]
                  }),
                  (0, import_jsx_runtime7.jsx)(Flex, {
                    children: (0, import_jsx_runtime7.jsxs)(Typography, {
                      textColor: "neutral600",
                      children: [
                        (0, import_jsx_runtime7.jsx)(DisplayedType, {
                          type: item.type,
                          customField: item.customField,
                          repeatable: "repeatable" in item && item.repeatable,
                          multiple: "multiple" in item && item.multiple
                        }),
                        item.type === "relation" && (0, import_jsx_runtime7.jsxs)(import_jsx_runtime7.Fragment, {
                          children: [
                            " (",
                            getRelationType(item.relation, item.targetAttribute),
                            ") ",
                            targetContentType && formatMessage({
                              id: getTrad("modelPage.attribute.with"),
                              defaultMessage: "with"
                            }),
                            " ",
                            targetContentType && (0, import_jsx_runtime7.jsx)(Link2, {
                              onClick: (e) => e.stopPropagation(),
                              tag: Link,
                              to: `/plugins/content-type-builder/content-types/${targetContentType.uid}`,
                              children: (0, import_upperFirst.default)(targetContentType.info.displayName)
                            }),
                            isPluginContentType && `(${formatMessage({
                              id: getTrad(`from`),
                              defaultMessage: "from"
                            })}: ${isPluginContentType})`
                          ]
                        }),
                        item.type === "component" && (0, import_jsx_runtime7.jsx)(ComponentLink, {
                          uid: item.component
                        })
                      ]
                    })
                  })
                ]
              })
            ]
          }),
          (0, import_jsx_runtime7.jsx)(Box, {
            children: (0, import_jsx_runtime7.jsx)(Flex, {
              justifyContent: "flex-end",
              gap: 1,
              onClick: (e) => e.stopPropagation(),
              children: (0, import_jsx_runtime7.jsxs)(import_jsx_runtime7.Fragment, {
                children: [
                  (0, import_jsx_runtime7.jsx)(Box, {
                    children: item.status && (0, import_jsx_runtime7.jsx)(StatusBadge, {
                      status: item.status
                    })
                  }),
                  [
                    "component",
                    "dynamiczone"
                  ].includes(item.type) && (0, import_jsx_runtime7.jsx)(IconButton, {
                    onClick: (e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      if (isOpen) {
                        setIsOpen(false);
                      } else {
                        setIsOpen(true);
                      }
                    },
                    "aria-expanded": isOpen,
                    label: formatMessage({
                      id: "app.utils.toggle",
                      defaultMessage: "Toggle"
                    }),
                    variant: "ghost",
                    withTooltip: false,
                    children: (0, import_jsx_runtime7.jsx)(ForwardRef$4z, {
                      "aria-hidden": true,
                      fill: "neutral500",
                      style: {
                        transform: `rotate(${isOpen ? "0deg" : "-90deg"})`,
                        transition: "transform 0.5s"
                      }
                    })
                  }),
                  isInDevelopmentMode && item.configurable !== false ? (0, import_jsx_runtime7.jsxs)(import_jsx_runtime7.Fragment, {
                    children: [
                      !isMorph && (0, import_jsx_runtime7.jsx)(IconButton, {
                        onClick: handleClick,
                        label: `${formatMessage({
                          id: "app.utils.edit",
                          defaultMessage: "Edit"
                        })} ${item.name}`,
                        variant: "ghost",
                        disabled: !canEdit,
                        children: (0, import_jsx_runtime7.jsx)(ForwardRef$1v, {})
                      }),
                      (0, import_jsx_runtime7.jsx)(IconButton, {
                        onClick: (e) => {
                          e.stopPropagation();
                          removeAttribute({
                            forTarget: type.modelType,
                            targetUid: type.uid,
                            attributeToRemoveName: item.name
                          });
                        },
                        label: `${formatMessage({
                          id: "global.delete",
                          defaultMessage: "Delete"
                        })} ${item.name}`,
                        variant: "ghost",
                        disabled: !canDelete,
                        children: (0, import_jsx_runtime7.jsx)(ForwardRef$j, {})
                      })
                    ]
                  }) : (0, import_jsx_runtime7.jsx)(Flex, {
                    padding: 2,
                    children: (0, import_jsx_runtime7.jsx)(ForwardRef$2h, {
                      fill: "neutral500"
                    })
                  })
                ]
              })
            })
          })
        ]
      }),
      (0, import_jsx_runtime7.jsxs)(SubRow, {
        $shouldHideNestedInfos: shouldHideNestedInfos,
        $isOpen: isOpen,
        children: [
          item.type === "component" && (0, import_jsx_runtime7.jsx)(ComponentList, {
            ...item,
            isFromDynamicZone,
            firstLoopComponentUid
          }),
          item.type === "dynamiczone" && (0, import_jsx_runtime7.jsx)(DynamicZoneList, {
            ...item,
            disabled: isTypeDeleted || item.status === "REMOVED",
            addComponent: addComponentToDZ,
            forTarget: type.modelType,
            targetUid: type.uid
          })
        ]
      })
    ]
  });
});
var SubRow = dt(Box)`
  display: ${({ $shouldHideNestedInfos }) => $shouldHideNestedInfos ? "none" : "block"};
  max-height: ${({ $isOpen }) => $isOpen ? "9999px" : "0px"};
  overflow: hidden;

  transition: ${({ $isOpen }) => $isOpen ? "max-height 1s ease-in-out" : "max-height 0.5s cubic-bezier(0, 1, 0, 1)"};
`;
var ComponentLink = ({ uid }) => {
  const { components } = useDataManager();
  const type = (0, import_get3.default)(components, uid);
  return (0, import_jsx_runtime7.jsxs)(import_jsx_runtime7.Fragment, {
    children: [
      " (",
      (0, import_jsx_runtime7.jsx)(Link2, {
        onClick: (e) => e.stopPropagation(),
        tag: Link,
        to: `/plugins/content-type-builder/component-categories/${type.category}/${type.uid}`,
        children: (0, import_upperFirst.default)(type.info.displayName)
      }),
      ")"
    ]
  });
};

// node_modules/@strapi/content-type-builder/dist/admin/components/Footers.mjs
var import_jsx_runtime8 = __toESM(require_jsx_runtime(), 1);
var IconBox = dt(Box)`
  height: 2.4rem;
  width: 2.4rem;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;

  svg {
    height: 1rem;
    width: 1rem;
  }

  svg path {
    fill: ${({ theme, color }) => theme.colors[`${color}600`]};
  }
`;
var ButtonBox = dt(Box)`
  border-radius: 0 0 ${({ theme }) => theme.borderRadius} ${({ theme }) => theme.borderRadius};
  display: block;
  width: 100%;
  border: none;
  position: relative;
`;
var NestedTFooter = ({ children, icon, color, ...props }) => {
  return (0, import_jsx_runtime8.jsx)(ButtonBox, {
    paddingBottom: 4,
    paddingTop: 4,
    paddingLeft: "6rem",
    tag: "button",
    type: "button",
    ...props,
    children: (0, import_jsx_runtime8.jsxs)(Flex, {
      children: [
        (0, import_jsx_runtime8.jsx)(IconBox, {
          color,
          "aria-hidden": true,
          background: `${color}200`,
          children: icon
        }),
        (0, import_jsx_runtime8.jsx)(Box, {
          paddingLeft: 3,
          children: (0, import_jsx_runtime8.jsx)(Typography, {
            variant: "pi",
            fontWeight: "bold",
            textColor: `${color}600`,
            children
          })
        })
      ]
    })
  });
};
var TFooter = ({ children, icon, color, ...props }) => {
  return (0, import_jsx_runtime8.jsxs)("div", {
    children: [
      (0, import_jsx_runtime8.jsx)(Divider, {}),
      (0, import_jsx_runtime8.jsx)(ButtonBox, {
        tag: "button",
        background: `${color}100`,
        padding: 5,
        ...props,
        children: (0, import_jsx_runtime8.jsxs)(Flex, {
          children: [
            (0, import_jsx_runtime8.jsx)(IconBox, {
              color,
              "aria-hidden": true,
              background: `${color}200`,
              children: icon
            }),
            (0, import_jsx_runtime8.jsx)(Box, {
              paddingLeft: 3,
              children: (0, import_jsx_runtime8.jsx)(Typography, {
                variant: "pi",
                fontWeight: "bold",
                textColor: `${color}600`,
                children
              })
            })
          ]
        })
      })
    ]
  });
};

// node_modules/@strapi/content-type-builder/dist/admin/components/List.mjs
var ListGrid = dt(Box)`
  white-space: nowrap;
  list-style: none;
  list-style-type: none;
`;
var SortableRow = (props) => {
  const { isInDevelopmentMode } = useDataManager();
  const { isDragging, attributes, listeners, setNodeRef, transform, transition, setActivatorNodeRef } = useSortable({
    disabled: !isInDevelopmentMode || props.item.status === "REMOVED" || props.type.status === "REMOVED",
    id: props.item.id,
    data: {
      index: props.item.index
    }
  });
  const style = {
    transform: CSS.Transform.toString({
      x: (transform == null ? void 0 : transform.x) ?? 0,
      y: (transform == null ? void 0 : transform.y) ?? 0,
      scaleX: 1,
      scaleY: 1
    }),
    transition
  };
  return (0, import_jsx_runtime9.jsx)(AttributeRow, {
    ref: setNodeRef,
    handleRef: setActivatorNodeRef,
    isDragging,
    attributes,
    listeners,
    style,
    ...props
  });
};
var List = ({ addComponentToDZ, firstLoopComponentUid, isFromDynamicZone = false, isMain = false, isSub = false, secondLoopComponentUid, type }) => {
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const { isInDevelopmentMode, moveAttribute } = useDataManager();
  const { onOpenModalAddField } = useFormModalNavigation();
  const items = type == null ? void 0 : type.attributes.map((item, index) => {
    return {
      id: `${type.uid}_${item.name}`,
      index,
      ...item
    };
  });
  const [activeId, setActiveId] = (0, import_react3.useState)(null);
  const isDeleted = (type == null ? void 0 : type.status) === "REMOVED";
  const sensors = useSensors(useSensor(PointerSensor));
  function handlerDragStart({ active }) {
    if (!active) {
      return;
    }
    setActiveId(active.id);
  }
  function handleDragEnd(event) {
    const { active, over } = event;
    setActiveId(null);
    if (over) {
      if (active.id !== over.id) {
        moveAttribute({
          forTarget: type.modelType,
          targetUid: type.uid,
          from: active.data.current.index,
          to: over.data.current.index
        });
      }
    }
  }
  const activeItem = items.find((item) => item.id === activeId);
  const onClickAddField = () => {
    if (isDeleted) {
      return;
    }
    trackUsage("hasClickedCTBAddFieldBanner");
    onOpenModalAddField({
      forTarget: type == null ? void 0 : type.modelType,
      targetUid: type.uid
    });
  };
  if ((type == null ? void 0 : type.attributes.length) === 0 && isMain) {
    return (0, import_jsx_runtime9.jsx)(EmptyStateLayout, {
      action: (0, import_jsx_runtime9.jsx)(Button, {
        onClick: onClickAddField,
        size: "L",
        startIcon: (0, import_jsx_runtime9.jsx)(ForwardRef$1h, {}),
        variant: "secondary",
        children: formatMessage({
          id: getTrad("table.button.no-fields"),
          defaultMessage: "Add new field"
        })
      }),
      content: formatMessage(type.modelType === "contentType" ? {
        id: getTrad("table.content.no-fields.collection-type"),
        defaultMessage: "Add your first field to this Collection-Type"
      } : {
        id: getTrad("table.content.no-fields.component"),
        defaultMessage: "Add your first field to this component"
      }),
      hasRadius: true,
      icon: (0, import_jsx_runtime9.jsx)(ForwardRef$J, {
        width: "16rem"
      })
    });
  }
  return (0, import_jsx_runtime9.jsxs)(DndContext, {
    sensors,
    collisionDetection: closestCenter,
    onDragEnd: handleDragEnd,
    onDragStart: handlerDragStart,
    onDragCancel: () => setActiveId(null),
    modifiers: [
      restrictToVerticalAxis
    ],
    children: [
      (0, import_jsx_runtime9.jsxs)(ListGrid, {
        tag: "ul",
        children: [
          (0, import_react_dom.createPortal)((0, import_jsx_runtime9.jsx)(DragOverlay, {
            zIndex: 10,
            children: activeItem && (0, import_jsx_runtime9.jsx)(AttributeRow, {
              isOverlay: true,
              item: activeItem,
              firstLoopComponentUid,
              isFromDynamicZone,
              secondLoopComponentUid,
              type,
              addComponentToDZ
            })
          }), document.body),
          (0, import_jsx_runtime9.jsx)(SortableContext, {
            items,
            strategy: verticalListSortingStrategy,
            children: items.map((item) => {
              return (0, import_jsx_runtime9.jsx)(SortableRow, {
                item,
                firstLoopComponentUid,
                isFromDynamicZone,
                secondLoopComponentUid,
                type,
                addComponentToDZ
              }, item.id);
            })
          })
        ]
      }),
      isMain && isInDevelopmentMode && (0, import_jsx_runtime9.jsx)(TFooter, {
        cursor: isDeleted ? "normal" : "pointer",
        icon: (0, import_jsx_runtime9.jsx)(ForwardRef$1h, {}),
        onClick: onClickAddField,
        color: isDeleted ? "neutral" : "primary",
        children: formatMessage({
          id: getTrad(`form.button.add.field.to.${type.modelType === "component" ? "component" : type.kind}`),
          defaultMessage: "Add another field"
        })
      }),
      isSub && isInDevelopmentMode && (0, import_jsx_runtime9.jsx)(NestedTFooter, {
        cursor: isDeleted ? "normal" : "pointer",
        icon: (0, import_jsx_runtime9.jsx)(ForwardRef$1h, {}),
        onClick: onClickAddField,
        color: isFromDynamicZone && !isDeleted ? "primary" : "neutral",
        children: formatMessage({
          id: getTrad(`form.button.add.field.to.component`),
          defaultMessage: "Add another field"
        })
      })
    ]
  });
};

// node_modules/@strapi/content-type-builder/dist/admin/pages/ListView/LinkToCMSettingsView.mjs
var import_jsx_runtime10 = __toESM(require_jsx_runtime(), 1);
var import_react4 = __toESM(require_react(), 1);
var cmPermissions = {
  collectionTypesConfigurations: [
    {
      action: "plugin::content-manager.collection-types.configure-view",
      subject: null
    }
  ],
  componentsConfigurations: [
    {
      action: "plugin::content-manager.components.configure-layout",
      subject: null
    }
  ],
  singleTypesConfigurations: [
    {
      action: "plugin::content-manager.single-types.configure-view",
      subject: null
    }
  ]
};
var getPermission = (type) => {
  if (type.modelType === "contentType") {
    if (type.kind === "singleType") {
      return cmPermissions.singleTypesConfigurations;
    }
    return cmPermissions.collectionTypesConfigurations;
  }
  return cmPermissions.componentsConfigurations;
};
var getLink = (type) => {
  switch (type.modelType) {
    case "contentType":
      switch (type.kind) {
        case "singleType":
          return `/content-manager/single-types/${type.uid}/configurations/edit`;
        case "collectionType":
          return `/content-manager/collection-types/${type.uid}/configurations/edit`;
      }
    case "component":
      return `/content-manager/components/${type.uid}/configurations/edit`;
  }
};
var StyledButton = dt(Button)`
  white-space: nowrap;
`;
var LinkToCMSettingsView = (0, import_react4.memo)(({ disabled, type }) => {
  const { formatMessage } = useIntl();
  const navigate = useNavigate();
  const permissionsToApply = getPermission(type);
  const label = formatMessage({
    id: "content-type-builder.form.button.configure-view",
    defaultMessage: "Configure the view"
  });
  const handleClick = () => {
    if (disabled) {
      return false;
    }
    const link = getLink(type);
    navigate(link);
    return false;
  };
  const { isLoading, allowedActions } = useRBAC(permissionsToApply);
  if (isLoading) {
    return null;
  }
  if (!allowedActions.canConfigureView && !allowedActions.canConfigureLayout) {
    return null;
  }
  return (0, import_jsx_runtime10.jsx)(StyledButton, {
    startIcon: (0, import_jsx_runtime10.jsx)(ForwardRef$2n, {}),
    variant: "tertiary",
    onClick: handleClick,
    disabled,
    children: label
  });
});

// node_modules/@strapi/content-type-builder/dist/admin/pages/ListView/ListView.mjs
var LayoutsHeaderCustom = dt(Layouts.Header)`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;
var ListView = () => {
  var _a;
  const { isInDevelopmentMode, contentTypes, components, isLoading } = useDataManager();
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const { contentTypeUid, componentUid } = useParams();
  const { onOpenModalAddComponentsToDZ, onOpenModalAddField, onOpenModalEditSchema } = useFormModalNavigation();
  const type = contentTypeUid ? contentTypes[contentTypeUid] : componentUid ? components[componentUid] : null;
  if (isLoading) {
    return null;
  }
  if (!type) {
    const allowedEndpoints = Object.values(contentTypes).filter((ct) => ct.visible === true && !ct.plugin).map((ct) => ct.uid).sort();
    if (allowedEndpoints.length > 0) {
      return (0, import_jsx_runtime11.jsx)(Navigate, {
        to: `/plugins/content-type-builder/content-types/${allowedEndpoints[0]}`
      });
    }
    return (0, import_jsx_runtime11.jsx)(Navigate, {
      to: "/plugins/content-type-builder/content-types/create-content-type"
    });
  }
  const isFromPlugin = "plugin" in type && (type == null ? void 0 : type.plugin) !== void 0;
  const forTarget = contentTypeUid ? "contentType" : "component";
  const label = ((_a = type == null ? void 0 : type.info) == null ? void 0 : _a.displayName) ?? "";
  const canEdit = isInDevelopmentMode && !isFromPlugin;
  const handleClickAddComponentToDZ = (dynamicZoneTarget) => {
    onOpenModalAddComponentsToDZ({
      dynamicZoneTarget,
      targetUid: type.uid
    });
  };
  const onEdit = () => {
    if ("kind" in type) {
      if ((type == null ? void 0 : type.kind) === "collectionType") {
        trackUsage("willEditNameOfContentType");
      }
      if ((type == null ? void 0 : type.kind) === "singleType") {
        trackUsage("willEditNameOfSingleType");
      }
      onOpenModalEditSchema({
        modalType: forTarget,
        forTarget,
        targetUid: type.uid,
        kind: type == null ? void 0 : type.kind
      });
      return;
    }
    onOpenModalEditSchema({
      modalType: forTarget,
      forTarget,
      targetUid: type.uid
    });
  };
  const addNewFieldLabel = formatMessage({
    id: getTrad("table.button.no-fields"),
    defaultMessage: "Add new field"
  });
  const addAnotherFieldLabel = formatMessage({
    id: getTrad("button.attributes.add.another"),
    defaultMessage: "Add another field"
  });
  const isDeleted = type.status === "REMOVED";
  const primaryAction = isInDevelopmentMode && (0, import_jsx_runtime11.jsxs)(Flex, {
    gap: 2,
    children: [
      (0, import_jsx_runtime11.jsx)(LinkToCMSettingsView, {
        type,
        disabled: type.status === "NEW" || isDeleted
      }, "link-to-cm-settings-view"),
      (0, import_jsx_runtime11.jsx)(Button, {
        startIcon: (0, import_jsx_runtime11.jsx)(ForwardRef$1v, {}),
        variant: "tertiary",
        onClick: onEdit,
        disabled: !canEdit || isDeleted,
        children: formatMessage({
          id: "app.utils.edit",
          defaultMessage: "Edit"
        })
      }),
      (0, import_jsx_runtime11.jsx)(Button, {
        startIcon: (0, import_jsx_runtime11.jsx)(ForwardRef$1h, {}),
        variant: "secondary",
        minWidth: "max-content",
        onClick: () => {
          onOpenModalAddField({
            forTarget,
            targetUid: type.uid
          });
        },
        disabled: isDeleted,
        children: type.attributes.length === 0 ? addNewFieldLabel : addAnotherFieldLabel
      })
    ]
  });
  return (0, import_jsx_runtime11.jsxs)(import_jsx_runtime11.Fragment, {
    children: [
      isDeleted && (0, import_jsx_runtime11.jsx)(Flex, {
        background: "danger100",
        justifyContent: "center",
        padding: 4,
        children: (0, import_jsx_runtime11.jsxs)(Flex, {
          gap: 2,
          children: [
            (0, import_jsx_runtime11.jsx)(ForwardRef$2D, {
              fill: "danger600",
              height: "2rem",
              width: "2rem"
            }),
            (0, import_jsx_runtime11.jsx)(Typography, {
              children: formatMessage({
                id: getTrad("table.warning.deleted"),
                defaultMessage: `This {kind} has been deleted`
              }, {
                kind: type.modelType === "contentType" ? "Content Type" : "Component"
              })
            })
          ]
        })
      }),
      (0, import_jsx_runtime11.jsx)(LayoutsHeaderCustom, {
        id: "title",
        primaryAction,
        title: (0, import_upperFirst2.default)(label)
      }),
      (0, import_jsx_runtime11.jsx)(Layouts.Content, {
        children: (0, import_jsx_runtime11.jsx)(Box, {
          background: "neutral0",
          shadow: "filterShadow",
          hasRadius: true,
          overflow: "auto",
          borderColor: "neutral150",
          children: (0, import_jsx_runtime11.jsx)(List, {
            type,
            addComponentToDZ: handleClickAddComponentToDZ,
            isMain: true
          })
        })
      })
    ]
  });
};
export {
  ListView as default
};
//# sourceMappingURL=ListView-S52QJX6F.js.map
