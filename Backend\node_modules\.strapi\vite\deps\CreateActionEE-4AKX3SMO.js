import {
  require_isNil
} from "./chunk-AOLNGJZC.js";
import {
  useLicenseLimits
} from "./chunk-53OG7EL5.js";
import "./chunk-EZSYDDUK.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-WH6VCVXU.js";
import "./chunk-IL5G2D22.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import {
  Button,
  Flex,
  TooltipImpl,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$2d,
  ForwardRef$3
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/ee/admin/src/pages/SettingsPage/pages/Users/<USER>/CreateActionEE.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_isNil = __toESM(require_isNil(), 1);
var CreateActionEE = React.forwardRef((props, ref) => {
  const { formatMessage } = useIntl();
  const { license, isError, isLoading } = useLicenseLimits();
  const { permittedSeats, shouldStopCreate } = license ?? {};
  if (isError || isLoading) {
    return null;
  }
  return (0, import_jsx_runtime.jsxs)(Flex, {
    gap: 2,
    children: [
      !(0, import_isNil.default)(permittedSeats) && shouldStopCreate && (0, import_jsx_runtime.jsx)(TooltipImpl, {
        label: formatMessage({
          id: "Settings.application.admin-seats.at-limit-tooltip",
          defaultMessage: "At limit: add seats to invite more users"
        }),
        side: "left",
        children: (0, import_jsx_runtime.jsx)(ForwardRef$3, {
          width: "1.4rem",
          height: "1.4rem",
          fill: "danger500"
        })
      }),
      (0, import_jsx_runtime.jsx)(Button, {
        ref,
        "data-testid": "create-user-button",
        startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$2d, {}),
        size: "S",
        disabled: shouldStopCreate,
        ...props,
        children: formatMessage({
          id: "Settings.permissions.users.create",
          defaultMessage: "Invite new user"
        })
      })
    ]
  });
});
export {
  CreateActionEE
};
//# sourceMappingURL=CreateActionEE-4AKX3SMO.js.map
