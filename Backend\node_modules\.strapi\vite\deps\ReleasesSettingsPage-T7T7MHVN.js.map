{"version": 3, "sources": ["../../../@strapi/content-releases/admin/src/pages/ReleasesSettingsPage.tsx"], "sourcesContent": ["import {\n  Form,\n  Layouts,\n  Page,\n  useAP<PERSON><PERSON>r<PERSON><PERSON><PERSON>,\n  isFetch<PERSON>rror,\n  <PERSON>radientBadge,\n  useNotification,\n  useField,\n  useRBAC,\n  FormHelpers,\n} from '@strapi/admin/strapi-admin';\nimport {\n  Button,\n  Combobox,\n  ComboboxOption,\n  Field,\n  Flex,\n  Grid,\n  Typography,\n} from '@strapi/design-system';\nimport { Check } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport { useTypedSelector } from '../modules/hooks';\nimport { useGetReleaseSettingsQuery, useUpdateReleaseSettingsMutation } from '../services/release';\nimport { getTimezones } from '../utils/time';\nimport { SETTINGS_SCHEMA } from '../validation/schemas';\n\nimport type { UpdateSettings } from '../../../shared/contracts/settings';\n\ninterface UpdateDefaultTimezone {\n  defaultTimezone: string;\n}\n\nconst ReleasesSettingsPage = () => {\n  const { formatMessage } = useIntl();\n  const { formatAPIError } = useAPIErrorHandler();\n  const { toggleNotification } = useNotification();\n  const { data, isLoading: isLoadingSettings } = useGetReleaseSettingsQuery();\n  const [updateReleaseSettings, { isLoading: isSubmittingForm }] =\n    useUpdateReleaseSettingsMutation();\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions['settings']?.['releases']\n  );\n  const {\n    allowedActions: { canUpdate },\n  } = useRBAC(permissions);\n\n  const { timezoneList } = getTimezones(new Date());\n\n  const handleSubmit = async (\n    body: UpdateSettings.Request['body'],\n    { setErrors }: FormHelpers<UpdateDefaultTimezone>\n  ) => {\n    const { defaultTimezone } = body;\n    const formattedDefaultTimezone = defaultTimezone;\n    const isBodyTimezoneValid = timezoneList.some(\n      (timezone) => timezone.value === formattedDefaultTimezone\n    );\n\n    if (!isBodyTimezoneValid && defaultTimezone) {\n      const errorMessage = formatMessage({\n        id: 'components.Input.error.validation.combobox.invalid',\n        defaultMessage: 'The value provided is not valid',\n      });\n      setErrors({\n        defaultTimezone: errorMessage,\n      });\n      toggleNotification({\n        type: 'danger',\n        message: errorMessage,\n      });\n      return;\n    }\n\n    const newBody =\n      !defaultTimezone || !isBodyTimezoneValid\n        ? { defaultTimezone: null }\n        : { defaultTimezone: formattedDefaultTimezone };\n\n    try {\n      const response = await updateReleaseSettings(newBody);\n\n      if ('data' in response) {\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({\n            id: 'content-releases.pages.Settings.releases.setting.default-timezone-notification-success',\n            defaultMessage: 'Default timezone updated.',\n          }),\n        });\n      } else if (isFetchError(response.error)) {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(response.error),\n        });\n      } else {\n        toggleNotification({\n          type: 'danger',\n          message: formatMessage({\n            id: 'notification.error',\n            defaultMessage: 'An error occurred',\n          }),\n        });\n      }\n    } catch (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'An error occurred',\n        }),\n      });\n    }\n  };\n\n  if (isLoadingSettings) {\n    return <Page.Loading />;\n  }\n\n  const releasePageTitle = formatMessage({\n    id: 'content-releases.pages.Releases.title',\n    defaultMessage: 'Releases',\n  });\n\n  return (\n    <Layouts.Root>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: releasePageTitle,\n          }\n        )}\n      </Page.Title>\n      <Page.Main aria-busy={isLoadingSettings} tabIndex={-1}>\n        <Form\n          method=\"PUT\"\n          initialValues={{\n            defaultTimezone: data?.data.defaultTimezone,\n          }}\n          onSubmit={handleSubmit}\n          validationSchema={SETTINGS_SCHEMA}\n        >\n          {({ modified, isSubmitting }: { modified: boolean; isSubmitting: boolean }) => {\n            return (\n              <>\n                <Layouts.Header\n                  primaryAction={\n                    canUpdate ? (\n                      <Button\n                        disabled={!modified || isSubmittingForm}\n                        loading={isSubmitting}\n                        startIcon={<Check />}\n                        type=\"submit\"\n                      >\n                        {formatMessage({\n                          id: 'global.save',\n                          defaultMessage: 'Save',\n                        })}\n                      </Button>\n                    ) : null\n                  }\n                  secondaryAction={\n                    <GradientBadge\n                      label={formatMessage({\n                        id: 'components.premiumFeature.title',\n                        defaultMessage: 'Premium feature',\n                      })}\n                    />\n                  }\n                  title={releasePageTitle}\n                  subtitle={formatMessage({\n                    id: 'content-releases.pages.Settings.releases.description',\n                    defaultMessage: 'Create and manage content updates',\n                  })}\n                />\n                <Layouts.Content>\n                  <Flex\n                    direction=\"column\"\n                    background=\"neutral0\"\n                    alignItems=\"stretch\"\n                    padding={6}\n                    gap={6}\n                    shadow=\"filterShadow\"\n                    hasRadius\n                  >\n                    <Typography variant=\"delta\" tag=\"h2\">\n                      {formatMessage({\n                        id: 'content-releases.pages.Settings.releases.preferences.title',\n                        defaultMessage: 'Preferences',\n                      })}\n                    </Typography>\n                    <Grid.Root>\n                      <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"stretch\">\n                        <TimezoneDropdown />\n                      </Grid.Item>\n                    </Grid.Root>\n                  </Flex>\n                </Layouts.Content>\n              </>\n            );\n          }}\n        </Form>\n      </Page.Main>\n    </Layouts.Root>\n  );\n};\n\nconst TimezoneDropdown = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions['settings']?.['releases']\n  );\n  const {\n    allowedActions: { canUpdate },\n  } = useRBAC(permissions);\n  const { formatMessage } = useIntl();\n  const { timezoneList } = getTimezones(new Date());\n  const field = useField('defaultTimezone');\n  return (\n    <Field.Root\n      name=\"defaultTimezone\"\n      hint={formatMessage({\n        id: 'content-releases.pages.Settings.releases.timezone.hint',\n        defaultMessage: 'The timezone of every release can still be changed individually.',\n      })}\n      error={field.error}\n    >\n      <Field.Label>\n        {formatMessage({\n          id: 'content-releases.pages.Settings.releases.timezone.label',\n          defaultMessage: 'Default timezone',\n        })}\n      </Field.Label>\n      <Combobox\n        autocomplete={{ type: 'list', filter: 'contains' }}\n        onTextValueChange={(value) => field.onChange('defaultTimezone', value)}\n        onChange={(value) => {\n          if ((field.value && value) || !field.value) {\n            field.onChange('defaultTimezone', value);\n          }\n        }}\n        onClear={() => field.onChange('defaultTimezone', '')}\n        value={field.value}\n        disabled={!canUpdate}\n      >\n        {timezoneList.map((timezone) => (\n          <ComboboxOption key={timezone.value} value={timezone.value}>\n            {timezone.value.replace(/&/, ' ')}\n          </ComboboxOption>\n        ))}\n      </Combobox>\n      <Field.Hint />\n      <Field.Error />\n    </Field.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ProtectedSettingsPage\n * -----------------------------------------------------------------------------------------------*/\n\nexport const ProtectedReleasesSettingsPage = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions['settings']?.['releases']?.read\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <ReleasesSettingsPage />\n    </Page.Protect>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,IAAMA,uBAAuB,MAAA;AAC3B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,eAAc,IAAKC,mBAAAA;AAC3B,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,MAAMC,WAAWC,kBAAiB,IAAKC,2BAAAA;AAC/C,QAAM,CAACC,uBAAuB,EAAEH,WAAWI,iBAAgB,CAAE,IAC3DC,iCAAAA;AACF,QAAMC,cAAcC,iBAClB,CAACC,UAAAA;;AAAUA,uBAAMC,UAAUH,YAAY,UAAA,MAA5BE,mBAA0C;GAAW;AAElE,QAAM,EACJE,gBAAgB,EAAEC,UAAS,EAAE,IAC3BC,QAAQN,WAAAA;AAEZ,QAAM,EAAEO,aAAY,IAAKC,aAAa,oBAAIC,KAAAA,CAAAA;AAE1C,QAAMC,eAAe,OACnBC,MACA,EAAEC,UAAS,MAAsC;AAEjD,UAAM,EAAEC,gBAAe,IAAKF;AAC5B,UAAMG,2BAA2BD;AACjC,UAAME,sBAAsBR,aAAaS,KACvC,CAACC,aAAaA,SAASC,UAAUJ,wBAAAA;AAGnC,QAAI,CAACC,uBAAuBF,iBAAiB;AAC3C,YAAMM,eAAehC,cAAc;QACjCiC,IAAI;QACJC,gBAAgB;MAClB,CAAA;AACAT,gBAAU;QACRC,iBAAiBM;MACnB,CAAA;AACA5B,yBAAmB;QACjB+B,MAAM;QACNC,SAASJ;MACX,CAAA;AACA;IACF;AAEA,UAAMK,UACJ,CAACX,mBAAmB,CAACE,sBACjB;MAAEF,iBAAiB;QACnB;MAAEA,iBAAiBC;IAAyB;AAElD,QAAI;AACF,YAAMW,WAAW,MAAM5B,sBAAsB2B,OAAAA;AAE7C,UAAI,UAAUC,UAAU;AACtBlC,2BAAmB;UACjB+B,MAAM;UACNC,SAASpC,cAAc;YACrBiC,IAAI;YACJC,gBAAgB;UAClB,CAAA;QACF,CAAA;MACF,WAAWK,aAAaD,SAASE,KAAK,GAAG;AACvCpC,2BAAmB;UACjB+B,MAAM;UACNC,SAASlC,eAAeoC,SAASE,KAAK;QACxC,CAAA;aACK;AACLpC,2BAAmB;UACjB+B,MAAM;UACNC,SAASpC,cAAc;YACrBiC,IAAI;YACJC,gBAAgB;UAClB,CAAA;QACF,CAAA;MACF;IACF,SAASM,OAAO;AACdpC,yBAAmB;QACjB+B,MAAM;QACNC,SAASpC,cAAc;UACrBiC,IAAI;UACJC,gBAAgB;QAClB,CAAA;MACF,CAAA;IACF;EACF;AAEA,MAAI1B,mBAAmB;AACrB,eAAOiC,wBAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,QAAMC,mBAAmB5C,cAAc;IACrCiC,IAAI;IACJC,gBAAgB;EAClB,CAAA;AAEA,aACEW,yBAACC,QAAQC,MAAI;;UACXN,wBAACC,KAAKM,OAAK;kBACRhD,cACC;UAAEiC,IAAI;UAAsBC,gBAAgB;WAC5C;UACEe,MAAML;QACR,CAAA;;UAGJH,wBAACC,KAAKQ,MAAI;QAACC,aAAW3C;QAAmB4C,UAAU;QACjD,cAAAX,wBAACY,MAAAA;UACCC,QAAO;UACPC,eAAe;YACb7B,iBAAiBpB,6BAAMA,KAAKoB;UAC9B;UACA8B,UAAUjC;UACVkC,kBAAkBC;UAEjB,UAAA,CAAC,EAAEC,UAAUC,aAAY,MAAgD;AACxE,uBACEf,yBAAAgB,6BAAA;;oBACEpB,wBAACK,QAAQgB,QAAM;kBACbC,eACE7C,gBACEuB,wBAACuB,QAAAA;oBACCC,UAAU,CAACN,YAAYhD;oBACvBuD,SAASN;oBACTO,eAAW1B,wBAAC2B,eAAAA,CAAAA,CAAAA;oBACZjC,MAAK;8BAEJnC,cAAc;sBACbiC,IAAI;sBACJC,gBAAgB;oBAClB,CAAA;kBAEA,CAAA,IAAA;kBAENmC,qBACE5B,wBAAC6B,uBAAAA;oBACCC,OAAOvE,cAAc;sBACnBiC,IAAI;sBACJC,gBAAgB;oBAClB,CAAA;;kBAGJsC,OAAO5B;kBACP6B,UAAUzE,cAAc;oBACtBiC,IAAI;oBACJC,gBAAgB;kBAClB,CAAA;;oBAEFO,wBAACK,QAAQ4B,SAAO;kBACd,cAAA7B,yBAAC8B,MAAAA;oBACCC,WAAU;oBACVC,YAAW;oBACXC,YAAW;oBACXC,SAAS;oBACTC,KAAK;oBACLC,QAAO;oBACPC,WAAS;;0BAETzC,wBAAC0C,YAAAA;wBAAWC,SAAQ;wBAAQC,KAAI;kCAC7BrF,cAAc;0BACbiC,IAAI;0BACJC,gBAAgB;wBAClB,CAAA;;0BAEFO,wBAAC6C,KAAKvC,MAAI;sCACRN,wBAAC6C,KAAKC,MAAI;0BAACC,KAAK;0BAAGC,GAAG;0BAAIb,WAAU;0BAASE,YAAW;0BACtD,cAAArC,wBAACiD,kBAAAA,CAAAA,CAAAA;;;;;;;;UAOf;;;;;AAKV;AAEA,IAAMA,mBAAmB,MAAA;AACvB,QAAM7E,cAAcC,iBAClB,CAACC,UAAAA;;AAAUA,uBAAMC,UAAUH,YAAY,UAAA,MAA5BE,mBAA0C;GAAW;AAElE,QAAM,EACJE,gBAAgB,EAAEC,UAAS,EAAE,IAC3BC,QAAQN,WAAAA;AACZ,QAAM,EAAEb,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEmB,aAAY,IAAKC,aAAa,oBAAIC,KAAAA,CAAAA;AAC1C,QAAMqE,QAAQC,SAAS,iBAAA;AACvB,aACE/C,yBAACgD,MAAM9C,MAAI;IACTE,MAAK;IACL6C,MAAM9F,cAAc;MAClBiC,IAAI;MACJC,gBAAgB;IAClB,CAAA;IACAM,OAAOmD,MAAMnD;;UAEbC,wBAACoD,MAAME,OAAK;kBACT/F,cAAc;UACbiC,IAAI;UACJC,gBAAgB;QAClB,CAAA;;UAEFO,wBAACuD,UAAAA;QACCC,cAAc;UAAE9D,MAAM;UAAQ+D,QAAQ;QAAW;QACjDC,mBAAmB,CAACpE,UAAU4D,MAAMS,SAAS,mBAAmBrE,KAAAA;QAChEqE,UAAU,CAACrE,UAAAA;AACT,cAAK4D,MAAM5D,SAASA,SAAU,CAAC4D,MAAM5D,OAAO;AAC1C4D,kBAAMS,SAAS,mBAAmBrE,KAAAA;UACpC;QACF;QACAsE,SAAS,MAAMV,MAAMS,SAAS,mBAAmB,EAAA;QACjDrE,OAAO4D,MAAM5D;QACbkC,UAAU,CAAC/C;QAEVE,UAAAA,aAAakF,IAAI,CAACxE,iBACjBW,wBAAC8D,QAAAA;UAAoCxE,OAAOD,SAASC;UAClDD,UAAAA,SAASC,MAAMyE,QAAQ,KAAK,GAAA;QADV1E,GAAAA,SAASC,KAAK,CAAA;;UAKvCU,wBAACoD,MAAMY,MAAI,CAAA,CAAA;UACXhE,wBAACoD,MAAMa,OAAK,CAAA,CAAA;;;AAGlB;IAMaC,gCAAgC,MAAA;AAC3C,QAAM9F,cAAcC,iBAClB,CAACC,UAAAA;;AAAUA,6BAAMC,UAAUH,YAAY,UAAA,MAA5BE,mBAA0C,gBAA1CA,mBAAuD6F;GAAAA;AAGpE,aACEnE,wBAACC,KAAKmE,SAAO;IAAChG;IACZ,cAAA4B,wBAAC1C,sBAAAA,CAAAA,CAAAA;;AAGP;", "names": ["ReleasesSettingsPage", "formatMessage", "useIntl", "formatAPIError", "useAPIErrorHandler", "toggleNotification", "useNotification", "data", "isLoading", "isLoadingSettings", "useGetReleaseSettingsQuery", "updateReleaseSettings", "isSubmittingForm", "useUpdateReleaseSettingsMutation", "permissions", "useTypedSelector", "state", "admin_app", "allowedActions", "canUpdate", "useRBAC", "timezoneList", "getTimezones", "Date", "handleSubmit", "body", "setErrors", "defaultTimezone", "formattedDefaultTimezone", "isBodyTimezoneValid", "some", "timezone", "value", "errorMessage", "id", "defaultMessage", "type", "message", "newBody", "response", "isFetchError", "error", "_jsx", "Page", "Loading", "releasePageTitle", "_jsxs", "Layouts", "Root", "Title", "name", "Main", "aria-busy", "tabIndex", "Form", "method", "initialValues", "onSubmit", "validationSchema", "SETTINGS_SCHEMA", "modified", "isSubmitting", "_Fragment", "Header", "primaryAction", "<PERSON><PERSON>", "disabled", "loading", "startIcon", "Check", "secondaryAction", "GradientBadge", "label", "title", "subtitle", "Content", "Flex", "direction", "background", "alignItems", "padding", "gap", "shadow", "hasRadius", "Typography", "variant", "tag", "Grid", "<PERSON><PERSON>", "col", "s", "TimezoneDropdown", "field", "useField", "Field", "hint", "Label", "Combobox", "autocomplete", "filter", "onTextValueChange", "onChange", "onClear", "map", "ComboboxOption", "replace", "Hint", "Error", "ProtectedReleasesSettingsPage", "read", "Protect"]}