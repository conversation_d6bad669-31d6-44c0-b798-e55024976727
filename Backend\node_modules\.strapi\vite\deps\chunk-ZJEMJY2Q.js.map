{"version": 3, "sources": ["../../../@strapi/admin/admin/src/utils/once.ts", "../../../@strapi/admin/admin/src/hooks/usePrev.ts", "../../../@strapi/admin/admin/src/hooks/useRBAC.ts"], "sourcesContent": ["export const once = <TFunc extends (...args: any) => any>(fn: TFunc) => {\n  const func = fn;\n  let called = false;\n\n  if (typeof func !== 'function') {\n    throw new TypeError(`once requires a function parameter`);\n  }\n\n  return (...args: any) => {\n    if (!called && process.env.NODE_ENV === 'development') {\n      func(...args);\n      called = true;\n    }\n  };\n};\n", "import { useEffect, useRef } from 'react';\n\nexport const usePrev = <T>(value: T): T | undefined => {\n  const ref = useRef<T>();\n\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n\n  return ref.current;\n};\n", "import * as React from 'react';\n\nimport isEqual from 'lodash/isEqual';\n\nimport { useAuth, Permission } from '../features/Auth';\nimport { once } from '../utils/once';\nimport { capitalise } from '../utils/strings';\n\nimport { usePrev } from './usePrev';\n\ntype AllowedActions = Record<string, boolean>;\n\n/**\n * @public\n * @description This hooks takes an object or array of permissions (the latter preferred) and\n * runs through them to match against the current user's permissions as well as the RBAC middleware\n * system checking any conditions that may be present. It returns the filtered permissions as the complete\n * object from the API and a set of actions that can be performed. An action is derived from the last part\n * of the permission action e.g. `admin::roles.create` would be `canCreate`. If there's a hyphen in the action\n * this is removed and capitalised e.g `admin::roles.create-draft` would be `canCreateDraft`.\n * @example\n * ```tsx\n * import { Page, useRBAC } from '@strapi/strapi/admin'\n *\n * const MyProtectedPage = () => {\n *  const { allowedActions, isLoading, error, permissions } = useRBAC([{ action: 'admin::roles.create' }])\n *\n *  if(isLoading) {\n *    return <Page.Loading />\n *  }\n *\n *  if(error){\n *    return <Page.Error />\n *  }\n *\n *  if(!allowedActions.canCreate) {\n *    return null\n *  }\n *\n *  return <MyPage permissions={permissions} />\n * }\n * ```\n */\nconst useRBAC = (\n  permissionsToCheck: Record<string, Permission[]> | Permission[] = [],\n  passedPermissions?: Permission[],\n  rawQueryContext?: string\n): {\n  allowedActions: AllowedActions;\n  isLoading: boolean;\n  error?: unknown;\n  permissions: Permission[];\n} => {\n  const isLoadingAuth = useAuth('useRBAC', (state) => state.isLoading);\n  const [isLoading, setIsLoading] = React.useState(true);\n  const [error, setError] = React.useState<unknown>();\n  const [data, setData] = React.useState<Record<string, boolean>>();\n\n  const warnOnce = React.useMemo(() => once(console.warn), []);\n\n  const actualPermissionsToCheck: Permission[] = React.useMemo(() => {\n    if (Array.isArray(permissionsToCheck)) {\n      return permissionsToCheck;\n    } else {\n      warnOnce(\n        'useRBAC: The first argument should be an array of permissions, not an object. This will be deprecated in the future.'\n      );\n\n      return Object.values(permissionsToCheck).flat();\n    }\n  }, [permissionsToCheck, warnOnce]);\n\n  /**\n   * This is the default value we return until the queryResults[i].data\n   * are all resolved with data. This preserves the original behaviour.\n   */\n  const defaultAllowedActions = React.useMemo(() => {\n    return actualPermissionsToCheck.reduce<Record<string, boolean>>((acc, permission) => {\n      return {\n        ...acc,\n        [getActionName(permission)]: false,\n      };\n    }, {});\n  }, [actualPermissionsToCheck]);\n\n  const checkUserHasPermissions = useAuth('useRBAC', (state) => state.checkUserHasPermissions);\n\n  const permssionsChecked = usePrev(actualPermissionsToCheck);\n  const contextChecked = usePrev(rawQueryContext);\n\n  React.useEffect(() => {\n    if (\n      !isEqual(permssionsChecked, actualPermissionsToCheck) ||\n      // TODO: also run this when the query context changes\n      contextChecked !== rawQueryContext\n    ) {\n      setIsLoading(true);\n      setData(undefined);\n      setError(undefined);\n\n      checkUserHasPermissions(actualPermissionsToCheck, passedPermissions, rawQueryContext)\n        .then((res) => {\n          if (res) {\n            setData(\n              res.reduce<Record<string, boolean>>((acc, permission) => {\n                return {\n                  ...acc,\n                  [getActionName(permission)]: true,\n                };\n              }, {})\n            );\n          }\n        })\n        .catch((err) => {\n          setError(err);\n        })\n        .finally(() => {\n          setIsLoading(false);\n        });\n    }\n  }, [\n    actualPermissionsToCheck,\n    checkUserHasPermissions,\n    passedPermissions,\n    permissionsToCheck,\n    permssionsChecked,\n    contextChecked,\n    rawQueryContext,\n  ]);\n\n  /**\n   * This hook originally would not return allowedActions\n   * until all the checks were complete.\n   */\n  const allowedActions = Object.entries({\n    ...defaultAllowedActions,\n    ...data,\n  }).reduce((acc, [name, allowed]) => {\n    acc[`can${capitalise(name)}`] = allowed;\n\n    return acc;\n  }, {} as AllowedActions);\n\n  return {\n    allowedActions,\n    permissions: actualPermissionsToCheck,\n    isLoading: isLoading || isLoadingAuth,\n    error,\n  };\n};\n\nconst getActionName = (permission: Permission): string => {\n  const [action = ''] = permission.action.split('.').slice(-1);\n  return action.split('-').map(capitalise).join('');\n};\n\nexport { useRBAC };\nexport type { AllowedActions };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAO,IAAMA,OAAO,CAAsCC,OAAAA;AACxD,QAAMC,OAAOD;AACb,MAAIE,SAAS;AAEb,MAAI,OAAOD,SAAS,YAAY;AAC9B,UAAM,IAAIE,UAAU,oCAAoC;EAC1D;AAEA,SAAO,IAAIC,SAAAA;AACT,QAAI,CAACF,UAAUG,MAAwC;AACrDJ,WAAQG,GAAAA,IAAAA;AACRF,eAAS;IACX;EACF;AACF;;;;ACZO,IAAMI,UAAU,CAAIC,UAAAA;AACzB,QAAMC,UAAMC,qBAAAA;AAEZC,8BAAU,MAAA;AACRF,QAAIG,UAAUJ;KACb;IAACA;EAAM,CAAA;AAEV,SAAOC,IAAIG;AACb;;;ACgCC,IACKC,UAAU,CACdC,qBAAkE,CAAA,GAClEC,mBACAC,oBAAAA;AAOA,QAAMC,gBAAgBC,QAAQ,WAAW,CAACC,UAAUA,MAAMC,SAAS;AACnE,QAAM,CAACA,WAAWC,YAAAA,IAAsBC,eAAS,IAAA;AACjD,QAAM,CAACC,OAAOC,QAAS,IAASF,eAAQ;AACxC,QAAM,CAACG,MAAMC,OAAQ,IAASJ,eAAQ;AAEtC,QAAMK,WAAiBC,cAAQ,MAAMC,KAAKC,QAAQC,IAAI,GAAG,CAAA,CAAE;AAE3D,QAAMC,2BAA+CJ,cAAQ,MAAA;AAC3D,QAAIK,MAAMC,QAAQpB,kBAAqB,GAAA;AACrC,aAAOA;WACF;AACLa,eACE,sHAAA;AAGF,aAAOQ,OAAOC,OAAOtB,kBAAAA,EAAoBuB,KAAI;IAC/C;KACC;IAACvB;IAAoBa;EAAS,CAAA;AAMjC,QAAMW,wBAA8BV,cAAQ,MAAA;AAC1C,WAAOI,yBAAyBO,OAAgC,CAACC,KAAKC,eAAAA;AACpE,aAAO;QACL,GAAGD;QACH,CAACE,cAAcD,UAAAA,CAAAA,GAAc;MAC/B;IACF,GAAG,CAAA,CAAC;KACH;IAACT;EAAyB,CAAA;AAE7B,QAAMW,0BAA0BzB,QAAQ,WAAW,CAACC,UAAUA,MAAMwB,uBAAuB;AAE3F,QAAMC,oBAAoBC,QAAQb,wBAAAA;AAClC,QAAMc,iBAAiBD,QAAQ7B,eAAAA;AAE/B+B,EAAMC,gBAAU,MAAA;AACd,QACE,KAACC,eAAAA,SAAQL,mBAAmBZ,wBAAAA;IAE5Bc,mBAAmB9B,iBACnB;AACAK,mBAAa,IAAA;AACbK,cAAQwB,MAAAA;AACR1B,eAAS0B,MAAAA;AAETP,8BAAwBX,0BAA0BjB,mBAAmBC,eAClEmC,EAAAA,KAAK,CAACC,QAAAA;AACL,YAAIA,KAAK;AACP1B,kBACE0B,IAAIb,OAAgC,CAACC,KAAKC,eAAAA;AACxC,mBAAO;cACL,GAAGD;cACH,CAACE,cAAcD,UAAAA,CAAAA,GAAc;YAC/B;UACF,GAAG,CAAA,CAAC,CAAA;QAER;OAEDY,EAAAA,MAAM,CAACC,QAAAA;AACN9B,iBAAS8B,GAAAA;MACX,CAAA,EACCC,QAAQ,MAAA;AACPlC,qBAAa,KAAA;MACf,CAAA;IACJ;KACC;IACDW;IACAW;IACA5B;IACAD;IACA8B;IACAE;IACA9B;EACD,CAAA;AAMD,QAAMwC,iBAAiBrB,OAAOsB,QAAQ;IACpC,GAAGnB;IACH,GAAGb;EACL,CAAA,EAAGc,OAAO,CAACC,KAAK,CAACkB,MAAMC,OAAQ,MAAA;AAC7BnB,QAAI,MAAMoB,WAAWF,IAAM,CAAA,EAAC,IAAIC;AAEhC,WAAOnB;EACT,GAAG,CAAA,CAAC;AAEJ,SAAO;IACLgB;IACAK,aAAa7B;IACbZ,WAAWA,aAAaH;IACxBM;EACF;AACF;AAEA,IAAMmB,gBAAgB,CAACD,eAAAA;AACrB,QAAM,CAACqB,SAAS,EAAE,IAAIrB,WAAWqB,OAAOC,MAAM,GAAKC,EAAAA,MAAM,EAAC;AAC1D,SAAOF,OAAOC,MAAM,GAAA,EAAKE,IAAIL,UAAAA,EAAYM,KAAK,EAAA;AAChD;", "names": ["once", "fn", "func", "called", "TypeError", "args", "process", "usePrev", "value", "ref", "useRef", "useEffect", "current", "useRBAC", "permissionsToCheck", "passedPermissions", "rawQueryContext", "isLoadingAuth", "useAuth", "state", "isLoading", "setIsLoading", "useState", "error", "setError", "data", "setData", "warnOnce", "useMemo", "once", "console", "warn", "actualPermissionsToCheck", "Array", "isArray", "Object", "values", "flat", "defaultAllowedActions", "reduce", "acc", "permission", "getActionName", "checkUserHasPermissions", "permssionsChecked", "usePrev", "contextChecked", "React", "useEffect", "isEqual", "undefined", "then", "res", "catch", "err", "finally", "allowedActions", "entries", "name", "allowed", "capitalise", "permissions", "action", "split", "slice", "map", "join"]}