{"version": 3, "sources": ["../../../@strapi/i18n/admin/src/pluginId.ts", "../../../@strapi/i18n/admin/src/utils/getTranslation.ts", "../../../@strapi/i18n/admin/src/services/api.ts", "../../../@strapi/i18n/admin/src/services/locales.ts", "../../../@strapi/i18n/admin/src/constants.ts"], "sourcesContent": ["export const pluginId = 'i18n';\n", "import { pluginId } from '../pluginId';\n\nconst getTranslation = (id: string) => `${pluginId}.${id}`;\n\nexport { getTranslation };\n", "import { adminApi } from '@strapi/admin/strapi-admin';\n\nconst i18nApi = adminApi.enhanceEndpoints({\n  addTagTypes: ['Locale'],\n});\n\nexport { i18nApi };\n", "import { i18nApi } from './api';\n\nimport type { GetISOLocales } from '../../../shared/contracts/iso-locales';\nimport type {\n  GetLocales,\n  CreateLocale,\n  DeleteLocale,\n  UpdateLocale,\n} from '../../../shared/contracts/locales';\n\nconst localesApi = i18nApi.injectEndpoints({\n  endpoints: (builder) => ({\n    createLocale: builder.mutation<CreateLocale.Response, CreateLocale.Request['body']>({\n      query: (data) => ({\n        url: '/i18n/locales',\n        method: 'POST',\n        data,\n      }),\n      invalidatesTags: [{ type: 'Locale', id: 'LIST' }],\n    }),\n    deleteLocale: builder.mutation<DeleteLocale.Response, DeleteLocale.Params['id']>({\n      query: (id) => ({\n        url: `/i18n/locales/${id}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: (result, error, id) => [{ type: 'Locale', id }],\n    }),\n    getLocales: builder.query<GetLocales.Response, void>({\n      query: () => '/i18n/locales',\n      providesTags: (res) => [\n        { type: 'Locale', id: 'LIST' },\n        ...(Array.isArray(res)\n          ? res.map((locale) => ({\n              type: 'Locale' as const,\n              id: locale.id,\n            }))\n          : []),\n      ],\n    }),\n    getDefaultLocales: builder.query<GetISOLocales.Response, void>({\n      query: () => '/i18n/iso-locales',\n    }),\n    updateLocale: builder.mutation<\n      UpdateLocale.Response,\n      UpdateLocale.Request['body'] & UpdateLocale.Params\n    >({\n      query: ({ id, ...data }) => ({\n        url: `/i18n/locales/${id}`,\n        method: 'PUT',\n        data,\n      }),\n      invalidatesTags: (result, error, { id }) => [{ type: 'Locale', id }],\n    }),\n  }),\n});\n\nconst {\n  useCreateLocaleMutation,\n  useDeleteLocaleMutation,\n  useGetLocalesQuery,\n  useGetDefaultLocalesQuery,\n  useUpdateLocaleMutation,\n} = localesApi;\n\nexport {\n  useCreateLocaleMutation,\n  useDeleteLocaleMutation,\n  useGetLocalesQuery,\n  useGetDefaultLocalesQuery,\n  useUpdateLocaleMutation,\n};\n", "export const PERMISSIONS = {\n  accessMain: [{ action: 'plugin::i18n.locale.read', subject: null }],\n  create: [{ action: 'plugin::i18n.locale.create', subject: null }],\n  delete: [{ action: 'plugin::i18n.locale.delete', subject: null }],\n  update: [{ action: 'plugin::i18n.locale.update', subject: null }],\n  read: [{ action: 'plugin::i18n.locale.read', subject: null }],\n};\n"], "mappings": ";;;;;AAAO,IAAMA,WAAW;;;ACElBC,IAAAA,iBAAiB,CAACC,OAAe,GAAGC,QAAS,IAAGD,EAAAA;;;ACAhDE,IAAAA,UAAUC,SAASC,iBAAiB;EACxCC,aAAa;IAAC;EAAS;AACzB,CAAA;;;ACMA,IAAMC,aAAaC,QAAQC,gBAAgB;EACzCC,WAAW,CAACC,aAAa;IACvBC,cAAcD,QAAQE,SAA8D;MAClFC,OAAO,CAACC,UAAU;QAChBC,KAAK;QACLC,QAAQ;QACRF;;MAEFG,iBAAiB;QAAC;UAAEC,MAAM;UAAUC,IAAI;QAAO;MAAE;IACnD,CAAA;IACAC,cAAcV,QAAQE,SAA2D;MAC/EC,OAAO,CAACM,QAAQ;QACdJ,KAAK,iBAAiBI,EAAAA;QACtBH,QAAQ;;MAEVC,iBAAiB,CAACI,QAAQC,OAAOH,OAAO;QAAC;UAAED,MAAM;UAAUC;QAAG;MAAE;IAClE,CAAA;IACAI,YAAYb,QAAQG,MAAiC;MACnDA,OAAO,MAAM;MACbW,cAAc,CAACC,QAAQ;QACrB;UAAEP,MAAM;UAAUC,IAAI;QAAO;WACzBO,MAAMC,QAAQF,GACdA,IAAAA,IAAIG,IAAI,CAACC,YAAY;UACnBX,MAAM;UACNC,IAAIU,OAAOV;QACb,EAAA,IACA,CAAA;MACL;IACH,CAAA;IACAW,mBAAmBpB,QAAQG,MAAoC;MAC7DA,OAAO,MAAM;IACf,CAAA;IACAkB,cAAcrB,QAAQE,SAGpB;MACAC,OAAO,CAAC,EAAEM,IAAI,GAAGL,KAAAA,OAAY;QAC3BC,KAAK,iBAAiBI,EAAAA;QACtBH,QAAQ;QACRF;;MAEFG,iBAAiB,CAACI,QAAQC,OAAO,EAAEH,GAAE,MAAO;QAAC;UAAED,MAAM;UAAUC;QAAG;MAAE;IACtE,CAAA;;AAEJ,CAAA;AAEM,IAAA,EACJa,yBACAC,yBACAC,oBACAC,2BACAC,wBAAuB,IACrB9B;;;IC9DS+B,cAAc;EACzBC,YAAY;IAAC;MAAEC,QAAQ;MAA4BC,SAAS;IAAK;EAAE;EACnEC,QAAQ;IAAC;MAAEF,QAAQ;MAA8BC,SAAS;IAAK;EAAE;EACjEE,QAAQ;IAAC;MAAEH,QAAQ;MAA8BC,SAAS;IAAK;EAAE;EACjEG,QAAQ;IAAC;MAAEJ,QAAQ;MAA8BC,SAAS;IAAK;EAAE;EACjEI,MAAM;IAAC;MAAEL,QAAQ;MAA4BC,SAAS;IAAK;EAAE;AAC/D;", "names": ["pluginId", "getTranslation", "id", "pluginId", "i18nApi", "adminApi", "enhanceEndpoints", "addTagTypes", "localesApi", "i18nApi", "injectEndpoints", "endpoints", "builder", "createLocale", "mutation", "query", "data", "url", "method", "invalidatesTags", "type", "id", "deleteLocale", "result", "error", "getLocales", "providesTags", "res", "Array", "isArray", "map", "locale", "getDefaultLocales", "updateLocale", "useCreateLocaleMutation", "useDeleteLocaleMutation", "useGetLocalesQuery", "useGetDefaultLocalesQuery", "useUpdateLocaleMutation", "PERMISSIONS", "accessMain", "action", "subject", "create", "delete", "update", "read"]}