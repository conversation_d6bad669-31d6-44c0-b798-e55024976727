import {
  require_map,
  require_tail
} from "./chunk-P3GECRPJ.js";
import {
  useCreateAPITokenMutation,
  useGetAPITokenQuery,
  useUpdateAPITokenMutation
} from "./chunk-CN7O7C2E.js";
import {
  FormHead,
  LifeSpanInput,
  TokenBox,
  TokenDescription,
  TokenName,
  TokenTypeSelect
} from "./chunk-F2MLD4QC.js";
import {
  API_TOKEN_TYPE
} from "./chunk-7GAX2FTH.js";
import {
  useGuidedTour
} from "./chunk-ROZIXYJG.js";
import {
  require_baseRest
} from "./chunk-BUDFB33L.js";
import {
  Form,
  Formik
} from "./chunk-7MILHJ3J.js";
import {
  errorsTrads
} from "./chunk-IFOFBKTA.js";
import {
  create4 as create,
  create5 as create2,
  create6 as create3,
  require_capitalize
} from "./chunk-376QHLWZ.js";
import {
  useTracking
} from "./chunk-XDCEA27D.js";
import {
  useRBAC
} from "./chunk-ZJEMJY2Q.js";
import {
  Layouts
} from "./chunk-MTTHLNPH.js";
import {
  Page,
  useAPIErrorHandler
} from "./chunk-7LKLOY7A.js";
import {
  useTypedSelector
} from "./chunk-ZOP4VV6J.js";
import {
  adminApi,
  isBaseQueryError,
  require_baseIndexOf
} from "./chunk-WH6VCVXU.js";
import {
  require_arrayMap,
  require_baseUnary,
  require_copyArray
} from "./chunk-CE4VABH2.js";
import {
  fn
} from "./chunk-QOUV5O5E.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  $c512c27ab02ef895$export$fd42f52fd3ae1109,
  Accordion,
  Box,
  CheckboxImpl,
  Flex,
  Grid,
  Typography,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  useLocation,
  useMatch,
  useNavigate
} from "./chunk-S65ZWNEO.js";
import {
  ForwardRef$4d
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt,
  lt
} from "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __commonJS,
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/_baseIndexOfWith.js
var require_baseIndexOfWith = __commonJS({
  "node_modules/lodash/_baseIndexOfWith.js"(exports, module) {
    function baseIndexOfWith(array, value, fromIndex, comparator) {
      var index = fromIndex - 1, length = array.length;
      while (++index < length) {
        if (comparator(array[index], value)) {
          return index;
        }
      }
      return -1;
    }
    module.exports = baseIndexOfWith;
  }
});

// node_modules/lodash/_basePullAll.js
var require_basePullAll = __commonJS({
  "node_modules/lodash/_basePullAll.js"(exports, module) {
    var arrayMap = require_arrayMap();
    var baseIndexOf = require_baseIndexOf();
    var baseIndexOfWith = require_baseIndexOfWith();
    var baseUnary = require_baseUnary();
    var copyArray = require_copyArray();
    var arrayProto = Array.prototype;
    var splice = arrayProto.splice;
    function basePullAll(array, values, iteratee, comparator) {
      var indexOf = comparator ? baseIndexOfWith : baseIndexOf, index = -1, length = values.length, seen = array;
      if (array === values) {
        values = copyArray(values);
      }
      if (iteratee) {
        seen = arrayMap(array, baseUnary(iteratee));
      }
      while (++index < length) {
        var fromIndex = 0, value = values[index], computed = iteratee ? iteratee(value) : value;
        while ((fromIndex = indexOf(seen, computed, fromIndex, comparator)) > -1) {
          if (seen !== array) {
            splice.call(seen, fromIndex, 1);
          }
          splice.call(array, fromIndex, 1);
        }
      }
      return array;
    }
    module.exports = basePullAll;
  }
});

// node_modules/lodash/pullAll.js
var require_pullAll = __commonJS({
  "node_modules/lodash/pullAll.js"(exports, module) {
    var basePullAll = require_basePullAll();
    function pullAll(array, values) {
      return array && array.length && values && values.length ? basePullAll(array, values) : array;
    }
    module.exports = pullAll;
  }
});

// node_modules/lodash/pull.js
var require_pull = __commonJS({
  "node_modules/lodash/pull.js"(exports, module) {
    var baseRest = require_baseRest();
    var pullAll = require_pullAll();
    var pull2 = baseRest(pullAll);
    module.exports = pull2;
  }
});

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/EditViewPage.mjs
var import_jsx_runtime8 = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/services/contentApi.mjs
var contentApiService = adminApi.injectEndpoints({
  endpoints: (builder) => ({
    getPermissions: builder.query({
      query: () => "/admin/content-api/permissions",
      transformResponse: (response) => response.data
    }),
    getRoutes: builder.query({
      query: () => "/admin/content-api/routes",
      transformResponse: (response) => response.data
    })
  }),
  overrideExisting: false
});
var { useGetPermissionsQuery, useGetRoutesQuery } = contentApiService;

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/apiTokenPermissions.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var [ApiTokenPermissionsContextProvider, useApiTokenPermissionsContext] = $c512c27ab02ef895$export$fd42f52fd3ae1109("ApiTokenPermissionsContext");
var ApiTokenPermissionsProvider = ({ children, ...rest }) => {
  return (0, import_jsx_runtime.jsx)(ApiTokenPermissionsContextProvider, {
    ...rest,
    children
  });
};
var useApiTokenPermissions = () => useApiTokenPermissionsContext("useApiTokenPermissions");

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/components/FormApiTokenContainer.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var FormApiTokenContainer = ({ errors = {}, onChange, canEditInputs, isCreating, values = {}, apiToken = {}, onDispatch, setHasChangedPermissions }) => {
  const { formatMessage } = useIntl();
  const handleChangeSelectApiTokenType = ({ target: { value } }) => {
    setHasChangedPermissions(false);
    if (value === "full-access") {
      onDispatch({
        type: "SELECT_ALL_ACTIONS"
      });
    }
    if (value === "read-only") {
      onDispatch({
        type: "ON_CHANGE_READ_ONLY"
      });
    }
  };
  const typeOptions = [
    {
      value: "read-only",
      label: {
        id: "Settings.tokens.types.read-only",
        defaultMessage: "Read-only"
      }
    },
    {
      value: "full-access",
      label: {
        id: "Settings.tokens.types.full-access",
        defaultMessage: "Full access"
      }
    },
    {
      value: "custom",
      label: {
        id: "Settings.tokens.types.custom",
        defaultMessage: "Custom"
      }
    }
  ];
  return (0, import_jsx_runtime2.jsx)(Box, {
    background: "neutral0",
    hasRadius: true,
    shadow: "filterShadow",
    paddingTop: 6,
    paddingBottom: 6,
    paddingLeft: 7,
    paddingRight: 7,
    children: (0, import_jsx_runtime2.jsxs)(Flex, {
      direction: "column",
      alignItems: "stretch",
      gap: 4,
      children: [
        (0, import_jsx_runtime2.jsx)(Typography, {
          variant: "delta",
          tag: "h2",
          children: formatMessage({
            id: "global.details",
            defaultMessage: "Details"
          })
        }),
        (0, import_jsx_runtime2.jsxs)(Grid.Root, {
          gap: 5,
          children: [
            (0, import_jsx_runtime2.jsx)(Grid.Item, {
              col: 6,
              xs: 12,
              direction: "column",
              alignItems: "stretch",
              children: (0, import_jsx_runtime2.jsx)(TokenName, {
                error: errors["name"],
                value: values["name"],
                canEditInputs,
                onChange
              })
            }, "name"),
            (0, import_jsx_runtime2.jsx)(Grid.Item, {
              col: 6,
              xs: 12,
              direction: "column",
              alignItems: "stretch",
              children: (0, import_jsx_runtime2.jsx)(TokenDescription, {
                error: errors["description"],
                value: values["description"],
                canEditInputs,
                onChange
              })
            }, "description"),
            (0, import_jsx_runtime2.jsx)(Grid.Item, {
              col: 6,
              xs: 12,
              direction: "column",
              alignItems: "stretch",
              children: (0, import_jsx_runtime2.jsx)(LifeSpanInput, {
                isCreating,
                error: errors["lifespan"],
                value: values["lifespan"],
                onChange,
                token: apiToken
              })
            }, "lifespan"),
            (0, import_jsx_runtime2.jsx)(Grid.Item, {
              col: 6,
              xs: 12,
              direction: "column",
              alignItems: "stretch",
              children: (0, import_jsx_runtime2.jsx)(TokenTypeSelect, {
                value: values["type"],
                error: errors["type"],
                label: {
                  id: "Settings.tokens.form.type",
                  defaultMessage: "Token type"
                },
                onChange: (value) => {
                  handleChangeSelectApiTokenType({
                    target: {
                      value
                    }
                  });
                  onChange({
                    target: {
                      name: "type",
                      value
                    }
                  });
                },
                options: typeOptions,
                canEditInputs
              })
            }, "type")
          ]
        })
      ]
    })
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/components/Permissions.mjs
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/components/ActionBoundRoutes.mjs
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/components/BoundRoute.mjs
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_map = __toESM(require_map(), 1);
var import_tail = __toESM(require_tail(), 1);
var getMethodColor = (verb) => {
  switch (verb) {
    case "POST": {
      return {
        text: "success600",
        border: "success200",
        background: "success100"
      };
    }
    case "GET": {
      return {
        text: "secondary600",
        border: "secondary200",
        background: "secondary100"
      };
    }
    case "PUT": {
      return {
        text: "warning600",
        border: "warning200",
        background: "warning100"
      };
    }
    case "DELETE": {
      return {
        text: "danger600",
        border: "danger200",
        background: "danger100"
      };
    }
    default: {
      return {
        text: "neutral600",
        border: "neutral200",
        background: "neutral100"
      };
    }
  }
};
var MethodBox = dt(Box)`
  margin: -1px;
  border-radius: ${({ theme }) => theme.spaces[1]} 0 0 ${({ theme }) => theme.spaces[1]};
`;
var BoundRoute = ({ route = {
  handler: "Nocontroller.error",
  method: "GET",
  path: "/there-is-no-path"
} }) => {
  const { formatMessage } = useIntl();
  const { method, handler: title, path } = route;
  const formattedRoute = path ? (0, import_tail.default)(path.split("/")) : [];
  const [controller = "", action = ""] = title ? title.split(".") : [];
  const colors = getMethodColor(route.method);
  return (0, import_jsx_runtime3.jsxs)(Flex, {
    direction: "column",
    alignItems: "stretch",
    gap: 2,
    children: [
      (0, import_jsx_runtime3.jsxs)(Typography, {
        variant: "delta",
        tag: "h3",
        children: [
          formatMessage({
            id: "Settings.apiTokens.createPage.BoundRoute.title",
            defaultMessage: "Bound route to"
          }),
          " ",
          (0, import_jsx_runtime3.jsx)("span", {
            children: controller
          }),
          (0, import_jsx_runtime3.jsxs)(Typography, {
            variant: "delta",
            textColor: "primary600",
            children: [
              ".",
              action
            ]
          })
        ]
      }),
      (0, import_jsx_runtime3.jsxs)(Flex, {
        hasRadius: true,
        background: "neutral0",
        borderColor: "neutral200",
        gap: 0,
        children: [
          (0, import_jsx_runtime3.jsx)(MethodBox, {
            background: colors.background,
            borderColor: colors.border,
            padding: 2,
            children: (0, import_jsx_runtime3.jsx)(Typography, {
              fontWeight: "bold",
              textColor: colors.text,
              children: method
            })
          }),
          (0, import_jsx_runtime3.jsx)(Box, {
            paddingLeft: 2,
            paddingRight: 2,
            children: (0, import_map.default)(formattedRoute, (value) => (0, import_jsx_runtime3.jsxs)(Typography, {
              textColor: value.includes(":") ? "neutral600" : "neutral900",
              children: [
                "/",
                value
              ]
            }, value))
          })
        ]
      })
    ]
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/components/ActionBoundRoutes.mjs
var ActionBoundRoutes = () => {
  const { value: { selectedAction, routes } } = useApiTokenPermissions();
  const { formatMessage } = useIntl();
  const actionSection = selectedAction == null ? void 0 : selectedAction.split(".")[0];
  return (0, import_jsx_runtime4.jsx)(Grid.Item, {
    col: 5,
    background: "neutral150",
    paddingTop: 6,
    paddingBottom: 6,
    paddingLeft: 7,
    paddingRight: 7,
    style: {
      minHeight: "100%"
    },
    direction: "column",
    alignItems: "stretch",
    children: selectedAction ? (0, import_jsx_runtime4.jsx)(Flex, {
      direction: "column",
      alignItems: "stretch",
      gap: 2,
      children: actionSection && actionSection in routes && routes[actionSection].map((route) => {
        var _a, _b;
        return ((_b = (_a = route.config.auth) == null ? void 0 : _a.scope) == null ? void 0 : _b.includes(selectedAction)) || route.handler === selectedAction ? (0, import_jsx_runtime4.jsx)(BoundRoute, {
          route
        }, route.handler) : null;
      })
    }) : (0, import_jsx_runtime4.jsxs)(Flex, {
      direction: "column",
      alignItems: "stretch",
      gap: 2,
      children: [
        (0, import_jsx_runtime4.jsx)(Typography, {
          variant: "delta",
          tag: "h3",
          children: formatMessage({
            id: "Settings.apiTokens.createPage.permissions.header.title",
            defaultMessage: "Advanced settings"
          })
        }),
        (0, import_jsx_runtime4.jsx)(Typography, {
          tag: "p",
          textColor: "neutral600",
          children: formatMessage({
            id: "Settings.apiTokens.createPage.permissions.header.hint",
            defaultMessage: "Select the application's actions or the plugin's actions and click on the cog icon to display the bound route"
          })
        })
      ]
    })
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/components/ContentTypesSection.mjs
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/components/CollapsableContentType.mjs
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_capitalize = __toESM(require_capitalize(), 1);
var activeCheckboxWrapperStyles = lt`
  background: ${(props) => props.theme.colors.primary100};

  #cog {
    opacity: 1;
  }
`;
var CheckboxWrapper = dt(Box)`
  display: flex;
  justify-content: space-between;
  align-items: center;

  #cog {
    opacity: 0;
    path {
      fill: ${(props) => props.theme.colors.primary600};
    }
  }

  /* Show active style both on hover and when the action is selected */
  ${(props) => props.$isActive && activeCheckboxWrapperStyles}
  &:hover {
    ${activeCheckboxWrapperStyles}
  }
`;
var Border = dt.div`
  flex: 1;
  align-self: center;
  border-top: 1px solid ${({ theme }) => theme.colors.neutral150};
`;
var CollapsableContentType = ({ controllers = [], label, orderNumber = 0, disabled = false }) => {
  const { value: { onChangeSelectAll, onChange, selectedActions, setSelectedAction, selectedAction } } = useApiTokenPermissions();
  const { formatMessage } = useIntl();
  const isActionSelected = (actionId) => actionId === selectedAction;
  return (0, import_jsx_runtime5.jsxs)(Accordion.Item, {
    value: `${label}-${orderNumber}`,
    children: [
      (0, import_jsx_runtime5.jsx)(Accordion.Header, {
        variant: orderNumber % 2 ? "primary" : "secondary",
        children: (0, import_jsx_runtime5.jsx)(Accordion.Trigger, {
          children: (0, import_capitalize.default)(label)
        })
      }),
      (0, import_jsx_runtime5.jsx)(Accordion.Content, {
        children: controllers == null ? void 0 : controllers.map((controller) => {
          const allActionsSelected = controller.actions.every((action) => selectedActions.includes(action.actionId));
          const someActionsSelected = controller.actions.some((action) => selectedActions.includes(action.actionId));
          return (0, import_jsx_runtime5.jsxs)(Box, {
            children: [
              (0, import_jsx_runtime5.jsxs)(Flex, {
                justifyContent: "space-between",
                alignItems: "center",
                padding: 4,
                children: [
                  (0, import_jsx_runtime5.jsx)(Box, {
                    paddingRight: 4,
                    children: (0, import_jsx_runtime5.jsx)(Typography, {
                      variant: "sigma",
                      textColor: "neutral600",
                      children: controller == null ? void 0 : controller.controller
                    })
                  }),
                  (0, import_jsx_runtime5.jsx)(Border, {}),
                  (0, import_jsx_runtime5.jsx)(Box, {
                    paddingLeft: 4,
                    children: (0, import_jsx_runtime5.jsx)(CheckboxImpl, {
                      checked: !allActionsSelected && someActionsSelected ? "indeterminate" : allActionsSelected,
                      onCheckedChange: () => {
                        onChangeSelectAll({
                          target: {
                            value: [
                              ...controller.actions
                            ]
                          }
                        });
                      },
                      disabled,
                      children: formatMessage({
                        id: "app.utils.select-all",
                        defaultMessage: "Select all"
                      })
                    })
                  })
                ]
              }),
              (0, import_jsx_runtime5.jsx)(Grid.Root, {
                gap: 4,
                padding: 4,
                children: (controller == null ? void 0 : controller.actions) && (controller == null ? void 0 : controller.actions.map((action) => {
                  return (0, import_jsx_runtime5.jsx)(Grid.Item, {
                    col: 6,
                    direction: "column",
                    alignItems: "stretch",
                    children: (0, import_jsx_runtime5.jsxs)(CheckboxWrapper, {
                      $isActive: isActionSelected(action.actionId),
                      padding: 2,
                      hasRadius: true,
                      children: [
                        (0, import_jsx_runtime5.jsx)(CheckboxImpl, {
                          checked: selectedActions.includes(action.actionId),
                          name: action.actionId,
                          onCheckedChange: () => {
                            onChange({
                              target: {
                                value: action.actionId
                              }
                            });
                          },
                          disabled,
                          children: action.action
                        }),
                        (0, import_jsx_runtime5.jsx)("button", {
                          type: "button",
                          "data-testid": "action-cog",
                          onClick: () => setSelectedAction({
                            target: {
                              value: action.actionId
                            }
                          }),
                          style: {
                            display: "inline-flex",
                            alignItems: "center"
                          },
                          children: (0, import_jsx_runtime5.jsx)(ForwardRef$4d, {
                            id: "cog"
                          })
                        })
                      ]
                    })
                  }, action.actionId);
                }))
              })
            ]
          }, `${label}.${controller == null ? void 0 : controller.controller}`);
        })
      })
    ]
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/components/ContentTypesSection.mjs
var ContentTypesSection = ({ section = null, ...props }) => {
  return (0, import_jsx_runtime6.jsx)(Box, {
    children: (0, import_jsx_runtime6.jsx)(Accordion.Root, {
      size: "M",
      children: section && section.map((api, index) => (0, import_jsx_runtime6.jsx)(CollapsableContentType, {
        label: api.label,
        controllers: api.controllers,
        orderNumber: index,
        ...props
      }, api.apiId))
    })
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/components/Permissions.mjs
var Permissions = ({ ...props }) => {
  const { value: { data } } = useApiTokenPermissions();
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime7.jsxs)(Grid.Root, {
    gap: 0,
    shadow: "filterShadow",
    hasRadius: true,
    background: "neutral0",
    children: [
      (0, import_jsx_runtime7.jsxs)(Grid.Item, {
        col: 7,
        paddingTop: 6,
        paddingBottom: 6,
        paddingLeft: 7,
        paddingRight: 7,
        direction: "column",
        alignItems: "stretch",
        gap: 6,
        children: [
          (0, import_jsx_runtime7.jsxs)(Flex, {
            direction: "column",
            alignItems: "stretch",
            gap: 2,
            children: [
              (0, import_jsx_runtime7.jsx)(Typography, {
                variant: "delta",
                tag: "h2",
                children: formatMessage({
                  id: "Settings.apiTokens.createPage.permissions.title",
                  defaultMessage: "Permissions"
                })
              }),
              (0, import_jsx_runtime7.jsx)(Typography, {
                tag: "p",
                textColor: "neutral600",
                children: formatMessage({
                  id: "Settings.apiTokens.createPage.permissions.description",
                  defaultMessage: "Only actions bound by a route are listed below."
                })
              })
            ]
          }),
          (data == null ? void 0 : data.permissions) && (0, import_jsx_runtime7.jsx)(ContentTypesSection, {
            section: data == null ? void 0 : data.permissions,
            ...props
          })
        ]
      }),
      (0, import_jsx_runtime7.jsx)(ActionBoundRoutes, {})
    ]
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/constants.mjs
var schema = create3().shape({
  name: create().max(100).required(errorsTrads.required.id),
  type: create().oneOf([
    "read-only",
    "full-access",
    "custom"
  ]).required(errorsTrads.required.id),
  description: create().nullable(),
  lifespan: create2().integer().min(0).nullable().defined(errorsTrads.required.id)
});

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/reducer.mjs
var import_pull = __toESM(require_pull(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/utils/transformPermissionsData.mjs
var transformPermissionsData = (data) => {
  const layout = {
    allActionsIds: [],
    permissions: []
  };
  layout.permissions = Object.entries(data).map(([apiId, permission]) => ({
    apiId,
    label: apiId.split("::")[1],
    controllers: Object.keys(permission.controllers).map((controller) => ({
      controller,
      actions: controller in permission.controllers ? permission.controllers[controller].map((action) => {
        const actionId = `${apiId}.${controller}.${action}`;
        if (apiId.includes("api::")) {
          layout.allActionsIds.push(actionId);
        }
        return {
          action,
          actionId
        };
      }).flat() : []
    })).flat()
  }));
  return layout;
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/reducer.mjs
var initialState = {
  data: {
    allActionsIds: [],
    permissions: []
  },
  routes: {},
  selectedAction: "",
  selectedActions: []
};
var reducer = (state, action) => fn(state, (draftState) => {
  switch (action.type) {
    case "ON_CHANGE": {
      if (draftState.selectedActions.includes(action.value)) {
        (0, import_pull.default)(draftState.selectedActions, action.value);
      } else {
        draftState.selectedActions.push(action.value);
      }
      break;
    }
    case "SELECT_ALL_IN_PERMISSION": {
      const areAllSelected = action.value.every((item) => draftState.selectedActions.includes(item.actionId));
      if (areAllSelected) {
        action.value.forEach((item) => {
          (0, import_pull.default)(draftState.selectedActions, item.actionId);
        });
      } else {
        action.value.forEach((item) => {
          draftState.selectedActions.push(item.actionId);
        });
      }
      break;
    }
    case "SELECT_ALL_ACTIONS": {
      draftState.selectedActions = [
        ...draftState.data.allActionsIds
      ];
      break;
    }
    case "ON_CHANGE_READ_ONLY": {
      const onlyReadOnlyActions = draftState.data.allActionsIds.filter((actionId) => actionId.includes("find") || actionId.includes("findOne"));
      draftState.selectedActions = [
        ...onlyReadOnlyActions
      ];
      break;
    }
    case "UPDATE_PERMISSIONS_LAYOUT": {
      draftState.data = transformPermissionsData(action.value);
      break;
    }
    case "UPDATE_ROUTES": {
      draftState.routes = {
        ...action.value
      };
      break;
    }
    case "UPDATE_PERMISSIONS": {
      draftState.selectedActions = [
        ...action.value
      ];
      break;
    }
    case "SET_SELECTED_ACTION": {
      draftState.selectedAction = action.value;
      break;
    }
    default:
      return draftState;
  }
});

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/EditViewPage.mjs
var EditView = () => {
  var _a, _b, _c, _d;
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { state: locationState } = useLocation();
  const permissions = useTypedSelector((state2) => state2.admin_app.permissions);
  const [apiToken, setApiToken] = React.useState(((_a = locationState == null ? void 0 : locationState.apiToken) == null ? void 0 : _a.accessKey) ? {
    ...locationState.apiToken
  } : null);
  const [showToken, setShowToken] = React.useState(Boolean((_b = locationState == null ? void 0 : locationState.apiToken) == null ? void 0 : _b.accessKey));
  const hideTimerRef = React.useRef(null);
  const { trackUsage } = useTracking();
  const setCurrentStep = useGuidedTour("EditView", (state2) => state2.setCurrentStep);
  const { allowedActions: { canCreate, canUpdate, canRegenerate } } = useRBAC((_c = permissions.settings) == null ? void 0 : _c["api-tokens"]);
  const [state, dispatch] = React.useReducer(reducer, initialState);
  const match = useMatch("/settings/api-tokens/:id");
  const id = (_d = match == null ? void 0 : match.params) == null ? void 0 : _d.id;
  const isCreating = id === "create";
  const { _unstableFormatAPIError: formatAPIError, _unstableFormatValidationErrors: formatValidtionErrors } = useAPIErrorHandler();
  const navigate = useNavigate();
  const contentAPIPermissionsQuery = useGetPermissionsQuery();
  const contentAPIRoutesQuery = useGetRoutesQuery();
  React.useEffect(() => {
    if (contentAPIPermissionsQuery.error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(contentAPIPermissionsQuery.error)
      });
    }
  }, [
    contentAPIPermissionsQuery.error,
    formatAPIError,
    toggleNotification
  ]);
  React.useEffect(() => {
    if (contentAPIRoutesQuery.error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(contentAPIRoutesQuery.error)
      });
    }
  }, [
    contentAPIRoutesQuery.error,
    formatAPIError,
    toggleNotification
  ]);
  React.useEffect(() => {
    if (contentAPIPermissionsQuery.data) {
      dispatch({
        type: "UPDATE_PERMISSIONS_LAYOUT",
        value: contentAPIPermissionsQuery.data
      });
    }
  }, [
    contentAPIPermissionsQuery.data
  ]);
  React.useEffect(() => {
    if (contentAPIRoutesQuery.data) {
      dispatch({
        type: "UPDATE_ROUTES",
        value: contentAPIRoutesQuery.data
      });
    }
  }, [
    contentAPIRoutesQuery.data
  ]);
  React.useEffect(() => {
    if (apiToken) {
      if (apiToken.type === "read-only") {
        dispatch({
          type: "ON_CHANGE_READ_ONLY"
        });
      }
      if (apiToken.type === "full-access") {
        dispatch({
          type: "SELECT_ALL_ACTIONS"
        });
      }
      if (apiToken.type === "custom") {
        dispatch({
          type: "UPDATE_PERMISSIONS",
          value: apiToken == null ? void 0 : apiToken.permissions
        });
      }
    }
  }, [
    apiToken
  ]);
  React.useEffect(() => {
    trackUsage(isCreating ? "didAddTokenFromList" : "didEditTokenFromList", {
      tokenType: API_TOKEN_TYPE
    });
  }, [
    isCreating,
    trackUsage
  ]);
  const { data, error, isLoading } = useGetAPITokenQuery(id, {
    skip: !id || isCreating || !!apiToken
  });
  React.useEffect(() => {
    if (error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    }
  }, [
    error,
    formatAPIError,
    toggleNotification
  ]);
  React.useEffect(() => {
    if (data) {
      setApiToken(data);
      if (data.type === "read-only") {
        dispatch({
          type: "ON_CHANGE_READ_ONLY"
        });
      }
      if (data.type === "full-access") {
        dispatch({
          type: "SELECT_ALL_ACTIONS"
        });
      }
      if (data.type === "custom") {
        dispatch({
          type: "UPDATE_PERMISSIONS",
          value: data == null ? void 0 : data.permissions
        });
      }
    }
  }, [
    data
  ]);
  React.useEffect(() => {
    if (showToken) {
      hideTimerRef.current = setTimeout(() => {
        setShowToken(false);
      }, 3e4);
      return () => {
        if (hideTimerRef.current) {
          clearTimeout(hideTimerRef.current);
          hideTimerRef.current = null;
        }
      };
    }
  }, [
    showToken
  ]);
  const [createToken] = useCreateAPITokenMutation();
  const [updateToken] = useUpdateAPITokenMutation();
  const handleSubmit = async (body, formik) => {
    trackUsage(isCreating ? "willCreateToken" : "willEditToken", {
      tokenType: API_TOKEN_TYPE
    });
    try {
      if (isCreating) {
        const res = await createToken({
          ...body,
          // lifespan must be "null" for unlimited (0 would mean instantly expired and isn't accepted)
          lifespan: (body == null ? void 0 : body.lifespan) && body.lifespan !== "0" ? parseInt(body.lifespan.toString(), 10) : null,
          permissions: body.type === "custom" ? state.selectedActions : null
        });
        if ("error" in res) {
          if (isBaseQueryError(res.error) && res.error.name === "ValidationError") {
            formik.setErrors(formatValidtionErrors(res.error));
          } else {
            toggleNotification({
              type: "danger",
              message: formatAPIError(res.error)
            });
          }
          return;
        }
        toggleNotification({
          type: "success",
          message: formatMessage({
            id: "notification.success.apitokencreated",
            defaultMessage: "API Token successfully created"
          })
        });
        trackUsage("didCreateToken", {
          type: res.data.type,
          tokenType: API_TOKEN_TYPE
        });
        navigate(`../api-tokens/${res.data.id.toString()}`, {
          state: {
            apiToken: res.data
          },
          replace: true
        });
        setCurrentStep("apiTokens.success");
      } else {
        const res = await updateToken({
          id,
          name: body.name,
          description: body.description,
          type: body.type,
          permissions: body.type === "custom" ? state.selectedActions : null
        });
        if ("error" in res) {
          if (isBaseQueryError(res.error) && res.error.name === "ValidationError") {
            formik.setErrors(formatValidtionErrors(res.error));
          } else {
            toggleNotification({
              type: "danger",
              message: formatAPIError(res.error)
            });
          }
          return;
        }
        toggleNotification({
          type: "success",
          message: formatMessage({
            id: "notification.success.apitokenedited",
            defaultMessage: "API Token successfully edited"
          })
        });
        trackUsage("didEditToken", {
          type: res.data.type,
          tokenType: API_TOKEN_TYPE
        });
      }
    } catch {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "Something went wrong"
        })
      });
    }
  };
  const [hasChangedPermissions, setHasChangedPermissions] = React.useState(false);
  const handleChangeCheckbox = ({ target: { value } }) => {
    setHasChangedPermissions(true);
    dispatch({
      type: "ON_CHANGE",
      value
    });
  };
  const handleChangeSelectAllCheckbox = ({ target: { value } }) => {
    setHasChangedPermissions(true);
    dispatch({
      type: "SELECT_ALL_IN_PERMISSION",
      value
    });
  };
  const setSelectedAction = ({ target: { value } }) => {
    dispatch({
      type: "SET_SELECTED_ACTION",
      value
    });
  };
  const toggleToken = () => {
    setShowToken((prev) => !prev);
    if (hideTimerRef.current) {
      clearTimeout(hideTimerRef.current);
      hideTimerRef.current = null;
    }
  };
  const providerValue = {
    ...state,
    onChange: handleChangeCheckbox,
    onChangeSelectAll: handleChangeSelectAllCheckbox,
    setSelectedAction
  };
  const canEditInputs = canUpdate && !isCreating || canCreate && isCreating;
  const canShowToken = !!(apiToken == null ? void 0 : apiToken.accessKey);
  if (isLoading) {
    return (0, import_jsx_runtime8.jsx)(Page.Loading, {});
  }
  return (0, import_jsx_runtime8.jsx)(ApiTokenPermissionsProvider, {
    value: providerValue,
    children: (0, import_jsx_runtime8.jsxs)(Page.Main, {
      children: [
        (0, import_jsx_runtime8.jsx)(Page.Title, {
          children: formatMessage({
            id: "Settings.PageTitle",
            defaultMessage: "Settings - {name}"
          }, {
            name: "API Tokens"
          })
        }),
        (0, import_jsx_runtime8.jsx)(Formik, {
          validationSchema: schema,
          validateOnChange: false,
          initialValues: {
            name: (apiToken == null ? void 0 : apiToken.name) || "",
            description: (apiToken == null ? void 0 : apiToken.description) || "",
            type: apiToken == null ? void 0 : apiToken.type,
            lifespan: apiToken == null ? void 0 : apiToken.lifespan
          },
          enableReinitialize: true,
          onSubmit: (body, actions) => handleSubmit(body, actions),
          children: ({ errors, handleChange, isSubmitting, values, setFieldValue }) => {
            if (hasChangedPermissions && (values == null ? void 0 : values.type) !== "custom") {
              setFieldValue("type", "custom");
            }
            return (0, import_jsx_runtime8.jsxs)(Form, {
              children: [
                (0, import_jsx_runtime8.jsx)(FormHead, {
                  title: {
                    id: "Settings.apiTokens.createPage.title",
                    defaultMessage: "Create API Token"
                  },
                  token: apiToken,
                  setToken: setApiToken,
                  toggleToken,
                  showToken,
                  canEditInputs,
                  canRegenerate,
                  canShowToken,
                  isSubmitting,
                  regenerateUrl: "/admin/api-tokens/"
                }),
                (0, import_jsx_runtime8.jsx)(Layouts.Content, {
                  children: (0, import_jsx_runtime8.jsxs)(Flex, {
                    direction: "column",
                    alignItems: "stretch",
                    gap: 6,
                    children: [
                      (apiToken == null ? void 0 : apiToken.accessKey) && showToken && (0, import_jsx_runtime8.jsx)(TokenBox, {
                        token: apiToken.accessKey,
                        tokenType: API_TOKEN_TYPE
                      }),
                      (0, import_jsx_runtime8.jsx)(FormApiTokenContainer, {
                        errors,
                        onChange: handleChange,
                        canEditInputs,
                        isCreating,
                        values,
                        apiToken,
                        onDispatch: dispatch,
                        setHasChangedPermissions
                      }),
                      (0, import_jsx_runtime8.jsx)(Permissions, {
                        disabled: !canEditInputs || (values == null ? void 0 : values.type) === "read-only" || (values == null ? void 0 : values.type) === "full-access"
                      })
                    ]
                  })
                })
              ]
            });
          }
        })
      ]
    })
  });
};
var ProtectedEditView = () => {
  const permissions = useTypedSelector((state) => {
    var _a;
    return (_a = state.admin_app.permissions.settings) == null ? void 0 : _a["api-tokens"].read;
  });
  return (0, import_jsx_runtime8.jsx)(Page.Protect, {
    permissions,
    children: (0, import_jsx_runtime8.jsx)(EditView, {})
  });
};

export {
  EditView,
  ProtectedEditView
};
//# sourceMappingURL=chunk-TKUH5TIS.js.map
