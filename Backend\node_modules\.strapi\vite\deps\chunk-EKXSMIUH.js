import {
  useNpsSurveySettings
} from "./chunk-YV3ONWF5.js";
import {
  QueryClient,
  QueryClientProvider,
  img
} from "./chunk-75D2ZJP5.js";
import {
  ADMIN_PERMISSIONS_CE,
  HOOKS
} from "./chunk-VCTAT6B3.js";
import {
  GuidedTourProvider,
  useGuidedTour
} from "./chunk-ROZIXYJG.js";
import {
  require_pick
} from "./chunk-C72RZIDJ.js";
import {
  useEnterprise
} from "./chunk-HZKRK7AR.js";
import {
  Login
} from "./chunk-D2TGW5YS.js";
import {
  Column,
  LayoutContent,
  Logo,
  UnauthenticatedLayout
} from "./chunk-M27D4U76.js";
import {
  ConfigurationProvider
} from "./chunk-HX66WGOY.js";
import {
  require_baseMerge,
  require_createAssigner,
  require_merge,
  require_omit
} from "./chunk-DY2RJG3P.js";
import {
  require_baseRest
} from "./chunk-BUDFB33L.js";
import {
  HistoryProvider
} from "./chunk-DUGZ4WIW.js";
import {
  errorsTrads
} from "./chunk-IFOFBKTA.js";
import {
  ValidationError,
  create,
  create4 as create2,
  create6 as create3
} from "./chunk-376QHLWZ.js";
import {
  TrackingProvider,
  useTracking
} from "./chunk-XDCEA27D.js";
import {
  createAbsoluteUrl,
  useInitQuery
} from "./chunk-EZSYDDUK.js";
import {
  getBasename
} from "./chunk-QIJGNK42.js";
import {
  useClipboard
} from "./chunk-CJHUGFLE.js";
import {
  ADMIN_PERMISSIONS_EE,
  getEERoutes
} from "./chunk-IQGHPIIW.js";
import {
  MemoizedInputRenderer
} from "./chunk-HWAQQGJJ.js";
import {
  Form
} from "./chunk-L5JCPKMP.js";
import {
  Layouts
} from "./chunk-MTTHLNPH.js";
import {
  getByteSize
} from "./chunk-PQINNV4N.js";
import {
  require_isEqual
} from "./chunk-VYSYYPOB.js";
import {
  ForwardRef$F,
  Page,
  useAPIErrorHandler
} from "./chunk-7LKLOY7A.js";
import {
  AuthProvider,
  LANGUAGE_LOCAL_STORAGE_KEY,
  StrapiAppProvider,
  THEME_LOCAL_STORAGE_KEY,
  getStoredToken,
  login,
  logout,
  reducer,
  setAvailableThemes,
  useAuth,
  useForgotPasswordMutation,
  useGetRegistrationInfoQuery,
  useRegisterAdminMutation,
  useRegisterUserMutation,
  useResetPasswordMutation
} from "./chunk-ODQFI753.js";
import {
  useTypedDispatch,
  useTypedSelector,
  useTypedStore
} from "./chunk-ZOP4VV6J.js";
import {
  adminApi,
  getFetchClient,
  isBaseQueryError,
  require_apply,
  require_toNumber
} from "./chunk-WH6VCVXU.js";
import {
  Provider_default,
  combineReducers,
  configureStore,
  isRejected,
  useDispatch
} from "./chunk-IL5G2D22.js";
import {
  require_isFunction,
  require_isObject,
  require_root
} from "./chunk-CE4VABH2.js";
import {
  L
} from "./chunk-QOUV5O5E.js";
import {
  NotificationsProvider,
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  Alert,
  Box,
  Button,
  DesignSystemProvider,
  EmptyStateLayout,
  Flex,
  Grid,
  Link as Link2,
  LinkButton,
  Main,
  Typography,
  darkTheme,
  lightTheme,
  provider_default,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  require_client
} from "./chunk-5ZC4PE57.js";
import {
  Link,
  NavLink,
  Navigate,
  Outlet,
  RouterProvider,
  createBrowserRouter,
  createMemoryRouter,
  useLocation,
  useMatch,
  useNavigate,
  useRouteError
} from "./chunk-S65ZWNEO.js";
import {
  ForwardRef$3,
  ForwardRef$3R,
  ForwardRef$5d
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt,
  ft
} from "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __commonJS,
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/invariant/browser.js
var require_browser = __commonJS({
  "node_modules/invariant/browser.js"(exports, module2) {
    "use strict";
    var invariant5 = function(condition, format, a, b, c, d, e, f) {
      if (true) {
        if (format === void 0) {
          throw new Error("invariant requires an error message argument");
        }
      }
      if (!condition) {
        var error;
        if (format === void 0) {
          error = new Error(
            "Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings."
          );
        } else {
          var args = [a, b, c, d, e, f];
          var argIndex = 0;
          error = new Error(
            format.replace(/%s/g, function() {
              return args[argIndex++];
            })
          );
          error.name = "Invariant Violation";
        }
        error.framesToPop = 1;
        throw error;
      }
    };
    module2.exports = invariant5;
  }
});

// node_modules/lodash/_customDefaultsMerge.js
var require_customDefaultsMerge = __commonJS({
  "node_modules/lodash/_customDefaultsMerge.js"(exports, module2) {
    var baseMerge = require_baseMerge();
    var isObject = require_isObject();
    function customDefaultsMerge(objValue, srcValue, key, object, source, stack) {
      if (isObject(objValue) && isObject(srcValue)) {
        stack.set(srcValue, objValue);
        baseMerge(objValue, srcValue, void 0, customDefaultsMerge, stack);
        stack["delete"](srcValue);
      }
      return objValue;
    }
    module2.exports = customDefaultsMerge;
  }
});

// node_modules/lodash/mergeWith.js
var require_mergeWith = __commonJS({
  "node_modules/lodash/mergeWith.js"(exports, module2) {
    var baseMerge = require_baseMerge();
    var createAssigner = require_createAssigner();
    var mergeWith = createAssigner(function(object, source, srcIndex, customizer) {
      baseMerge(object, source, srcIndex, customizer);
    });
    module2.exports = mergeWith;
  }
});

// node_modules/lodash/defaultsDeep.js
var require_defaultsDeep = __commonJS({
  "node_modules/lodash/defaultsDeep.js"(exports, module2) {
    var apply = require_apply();
    var baseRest = require_baseRest();
    var customDefaultsMerge = require_customDefaultsMerge();
    var mergeWith = require_mergeWith();
    var defaultsDeep2 = baseRest(function(args) {
      args.push(void 0, customDefaultsMerge);
      return apply(mergeWith, void 0, args);
    });
    module2.exports = defaultsDeep2;
  }
});

// node_modules/lodash/now.js
var require_now = __commonJS({
  "node_modules/lodash/now.js"(exports, module2) {
    var root = require_root();
    var now = function() {
      return root.Date.now();
    };
    module2.exports = now;
  }
});

// node_modules/lodash/debounce.js
var require_debounce = __commonJS({
  "node_modules/lodash/debounce.js"(exports, module2) {
    var isObject = require_isObject();
    var now = require_now();
    var toNumber = require_toNumber();
    var FUNC_ERROR_TEXT = "Expected a function";
    var nativeMax = Math.max;
    var nativeMin = Math.min;
    function debounce(func, wait, options) {
      var lastArgs, lastThis, maxWait, result, timerId, lastCallTime, lastInvokeTime = 0, leading = false, maxing = false, trailing = true;
      if (typeof func != "function") {
        throw new TypeError(FUNC_ERROR_TEXT);
      }
      wait = toNumber(wait) || 0;
      if (isObject(options)) {
        leading = !!options.leading;
        maxing = "maxWait" in options;
        maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;
        trailing = "trailing" in options ? !!options.trailing : trailing;
      }
      function invokeFunc(time) {
        var args = lastArgs, thisArg = lastThis;
        lastArgs = lastThis = void 0;
        lastInvokeTime = time;
        result = func.apply(thisArg, args);
        return result;
      }
      function leadingEdge(time) {
        lastInvokeTime = time;
        timerId = setTimeout(timerExpired, wait);
        return leading ? invokeFunc(time) : result;
      }
      function remainingWait(time) {
        var timeSinceLastCall = time - lastCallTime, timeSinceLastInvoke = time - lastInvokeTime, timeWaiting = wait - timeSinceLastCall;
        return maxing ? nativeMin(timeWaiting, maxWait - timeSinceLastInvoke) : timeWaiting;
      }
      function shouldInvoke(time) {
        var timeSinceLastCall = time - lastCallTime, timeSinceLastInvoke = time - lastInvokeTime;
        return lastCallTime === void 0 || timeSinceLastCall >= wait || timeSinceLastCall < 0 || maxing && timeSinceLastInvoke >= maxWait;
      }
      function timerExpired() {
        var time = now();
        if (shouldInvoke(time)) {
          return trailingEdge(time);
        }
        timerId = setTimeout(timerExpired, remainingWait(time));
      }
      function trailingEdge(time) {
        timerId = void 0;
        if (trailing && lastArgs) {
          return invokeFunc(time);
        }
        lastArgs = lastThis = void 0;
        return result;
      }
      function cancel() {
        if (timerId !== void 0) {
          clearTimeout(timerId);
        }
        lastInvokeTime = 0;
        lastArgs = lastCallTime = lastThis = timerId = void 0;
      }
      function flush() {
        return timerId === void 0 ? result : trailingEdge(now());
      }
      function debounced() {
        var time = now(), isInvoking = shouldInvoke(time);
        lastArgs = arguments;
        lastThis = this;
        lastCallTime = time;
        if (isInvoking) {
          if (timerId === void 0) {
            return leadingEdge(lastCallTime);
          }
          if (maxing) {
            clearTimeout(timerId);
            timerId = setTimeout(timerExpired, wait);
            return invokeFunc(lastCallTime);
          }
        }
        if (timerId === void 0) {
          timerId = setTimeout(timerExpired, wait);
        }
        return result;
      }
      debounced.cancel = cancel;
      debounced.flush = flush;
      return debounced;
    }
    module2.exports = debounce;
  }
});

// node_modules/lodash/throttle.js
var require_throttle = __commonJS({
  "node_modules/lodash/throttle.js"(exports, module2) {
    var debounce = require_debounce();
    var isObject = require_isObject();
    var FUNC_ERROR_TEXT = "Expected a function";
    function throttle2(func, wait, options) {
      var leading = true, trailing = true;
      if (typeof func != "function") {
        throw new TypeError(FUNC_ERROR_TEXT);
      }
      if (isObject(options)) {
        leading = "leading" in options ? !!options.leading : leading;
        trailing = "trailing" in options ? !!options.trailing : trailing;
      }
      return debounce(func, wait, {
        "leading": leading,
        "maxWait": wait,
        "trailing": trailing
      });
    }
    module2.exports = throttle2;
  }
});

// node_modules/@strapi/admin/dist/admin/admin/src/render.mjs
var import_client = __toESM(require_client(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/StrapiApp.mjs
var import_jsx_runtime15 = __toESM(require_jsx_runtime(), 1);
var import_react6 = __toESM(require_react(), 1);
var import_invariant4 = __toESM(require_browser(), 1);
var import_isFunction = __toESM(require_isFunction(), 1);
var import_merge = __toESM(require_merge(), 1);
var import_pick = __toESM(require_pick(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/core/apis/CustomFields.mjs
var import_invariant = __toESM(require_browser(), 1);
var ALLOWED_TYPES = [
  "biginteger",
  "boolean",
  "date",
  "datetime",
  "decimal",
  "email",
  "enumeration",
  "float",
  "integer",
  "json",
  "password",
  "richtext",
  "string",
  "text",
  "time",
  "uid"
];
var ALLOWED_ROOT_LEVEL_OPTIONS = [
  "min",
  "minLength",
  "max",
  "maxLength",
  "required",
  "regex",
  "enum",
  "unique",
  "private",
  "default"
];
var CustomFields = class {
  constructor() {
    this.register = (customFields) => {
      if (Array.isArray(customFields)) {
        customFields.forEach((customField) => {
          this.register(customField);
        });
      } else {
        const { name, pluginId, type, intlLabel, intlDescription, components, options } = customFields;
        (0, import_invariant.default)(name, "A name must be provided");
        (0, import_invariant.default)(type, "A type must be provided");
        (0, import_invariant.default)(intlLabel, "An intlLabel must be provided");
        (0, import_invariant.default)(intlDescription, "An intlDescription must be provided");
        (0, import_invariant.default)(components, "A components object must be provided");
        (0, import_invariant.default)(components.Input, "An Input component must be provided");
        (0, import_invariant.default)(ALLOWED_TYPES.includes(type), `Custom field type: '${type}' is not a valid Strapi type or it can't be used with a Custom Field`);
        const isValidObjectKey = /^(?![0-9])[a-zA-Z0-9$_-]+$/g;
        (0, import_invariant.default)(isValidObjectKey.test(name), `Custom field name: '${name}' is not a valid object key`);
        const allFormOptions = [
          ...(options == null ? void 0 : options.base) || [],
          ...(options == null ? void 0 : options.advanced) || []
        ];
        if (allFormOptions.length) {
          const optionPathValidations = allFormOptions.reduce(optionsValidationReducer, []);
          optionPathValidations.forEach(({ isValidOptionPath, errorMessage }) => {
            (0, import_invariant.default)(isValidOptionPath, errorMessage);
          });
        }
        const uid = pluginId ? `plugin::${pluginId}.${name}` : `global::${name}`;
        const uidAlreadyUsed = Object.prototype.hasOwnProperty.call(this.customFields, uid);
        (0, import_invariant.default)(!uidAlreadyUsed, `Custom field: '${uid}' has already been registered`);
        this.customFields[uid] = customFields;
      }
    };
    this.getAll = () => {
      return this.customFields;
    };
    this.get = (uid) => {
      return this.customFields[uid];
    };
    this.customFields = {};
  }
};
var optionsValidationReducer = (acc, option) => {
  if ("items" in option) {
    return option.items.reduce(optionsValidationReducer, acc);
  }
  if (!option.name) {
    acc.push({
      isValidOptionPath: false,
      errorMessage: "The 'name' property is required on an options object"
    });
  } else {
    acc.push({
      isValidOptionPath: option.name.startsWith("options") || ALLOWED_ROOT_LEVEL_OPTIONS.includes(option.name),
      errorMessage: `'${option.name}' must be prefixed with 'options.'`
    });
  }
  return acc;
};

// node_modules/@strapi/admin/dist/admin/admin/src/core/apis/Plugin.mjs
var import_react = __toESM(require_react(), 1);
var Plugin = class {
  getInjectedComponents(containerName, blockName) {
    try {
      return this.injectionZones[containerName][blockName] || [];
    } catch (err) {
      console.error("Cannot get injected component", err);
      return [];
    }
  }
  injectComponent(containerName, blockName, component) {
    try {
      this.injectionZones[containerName][blockName].push(component);
    } catch (err) {
      console.error("Cannot inject component", err);
    }
  }
  constructor(pluginConf) {
    this[L] = true;
    this.apis = pluginConf.apis || {};
    this.initializer = pluginConf.initializer || null;
    this.injectionZones = pluginConf.injectionZones || {};
    this.isReady = pluginConf.isReady !== void 0 ? pluginConf.isReady : true;
    this.name = pluginConf.name;
    this.pluginId = pluginConf.id;
  }
};

// node_modules/@strapi/admin/dist/admin/admin/src/core/apis/rbac.mjs
var RBAC = class {
  use(middleware) {
    if (Array.isArray(middleware)) {
      this.middlewares.push(...middleware);
    } else {
      this.middlewares.push(middleware);
    }
  }
  constructor() {
    this.middlewares = [];
    this.run = async (ctx, permissions) => {
      let index = 0;
      const middlewaresToRun = this.middlewares.map((middleware) => middleware(ctx));
      const next = async (permissions2) => {
        if (index < this.middlewares.length) {
          return middlewaresToRun[index++](next)(permissions2);
        }
        return permissions2;
      };
      return next(permissions);
    };
  }
};

// node_modules/@strapi/admin/dist/admin/admin/src/core/apis/router.mjs
var import_jsx_runtime14 = __toESM(require_jsx_runtime(), 1);
var import_react5 = __toESM(require_react(), 1);
var import_invariant2 = __toESM(require_browser(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/App.mjs
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_react4 = __toESM(require_react(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/components/Providers.mjs
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_react3 = __toESM(require_react(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/components/LanguageProvider.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_defaultsDeep = __toESM(require_defaultsDeep(), 1);
var LanguageProvider = ({ children, messages }) => {
  const locale = useTypedSelector((state) => state.admin_app.language.locale);
  const appMessages = (0, import_defaultsDeep.default)(messages[locale], messages.en);
  return (0, import_jsx_runtime.jsx)(provider_default, {
    locale,
    defaultLocale: "en",
    messages: appMessages,
    textComponent: "span",
    children
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/components/Theme.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var Theme = ({ children, themes }) => {
  const { currentTheme } = useTypedSelector((state) => state.admin_app.theme);
  const [systemTheme, setSystemTheme] = React.useState();
  const { locale } = useIntl();
  const dispatch = useDispatch();
  React.useEffect(() => {
    const themeWatcher = window.matchMedia("(prefers-color-scheme: dark)");
    setSystemTheme(themeWatcher.matches ? "dark" : "light");
    const listener = (event) => {
      setSystemTheme(event.matches ? "dark" : "light");
    };
    themeWatcher.addEventListener("change", listener);
    return () => {
      themeWatcher.removeEventListener("change", listener);
    };
  }, []);
  React.useEffect(() => {
    dispatch(setAvailableThemes(Object.keys(themes)));
  }, [
    dispatch,
    themes
  ]);
  const computedThemeName = currentTheme === "system" ? systemTheme : currentTheme;
  return (0, import_jsx_runtime2.jsxs)(DesignSystemProvider, {
    locale,
    /**
    * TODO: could we make this neater i.e. by setting up the context to throw
    * if it can't find it, that way the type is always fully defined and we're
    * not checking it all the time...
    */
    theme: themes == null ? void 0 : themes[computedThemeName || "light"],
    children: [
      children,
      (0, import_jsx_runtime2.jsx)(GlobalStyle, {})
    ]
  });
};
var GlobalStyle = ft`
  body {
    background: ${({ theme }) => theme.colors.neutral100};
  }
`;

// node_modules/@strapi/admin/dist/admin/admin/src/components/Providers.mjs
var queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false
    }
  }
});
var Providers = ({ children, strapi, store }) => {
  return (0, import_jsx_runtime3.jsx)(StrapiAppProvider, {
    components: strapi.library.components,
    customFields: strapi.customFields,
    widgets: strapi.widgets,
    fields: strapi.library.fields,
    menu: strapi.router.menu,
    getAdminInjectedComponents: strapi.getAdminInjectedComponents,
    getPlugin: strapi.getPlugin,
    plugins: strapi.plugins,
    rbac: strapi.rbac,
    runHookParallel: strapi.runHookParallel,
    runHookWaterfall: (name, initialValue) => strapi.runHookWaterfall(name, initialValue, store),
    runHookSeries: strapi.runHookSeries,
    settings: strapi.router.settings,
    children: (0, import_jsx_runtime3.jsx)(Provider_default, {
      store,
      children: (0, import_jsx_runtime3.jsx)(QueryClientProvider, {
        client: queryClient,
        children: (0, import_jsx_runtime3.jsx)(AuthProvider, {
          children: (0, import_jsx_runtime3.jsx)(HistoryProvider, {
            children: (0, import_jsx_runtime3.jsx)(LanguageProvider, {
              messages: strapi.configurations.translations,
              children: (0, import_jsx_runtime3.jsx)(Theme, {
                themes: strapi.configurations.themes,
                children: (0, import_jsx_runtime3.jsx)(NotificationsProvider, {
                  children: (0, import_jsx_runtime3.jsx)(TrackingProvider, {
                    children: (0, import_jsx_runtime3.jsx)(GuidedTourProvider, {
                      children: (0, import_jsx_runtime3.jsx)(ConfigurationProvider, {
                        defaultAuthLogo: strapi.configurations.authLogo,
                        defaultMenuLogo: strapi.configurations.menuLogo,
                        showReleaseNotification: strapi.configurations.notifications.releases,
                        children
                      })
                    })
                  })
                })
              })
            })
          })
        })
      })
    })
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/App.mjs
var App = ({ strapi, store }) => {
  (0, import_react4.useEffect)(() => {
    const language = localStorage.getItem(LANGUAGE_LOCAL_STORAGE_KEY) || "en";
    if (language) {
      document.documentElement.lang = language;
    }
  }, []);
  return (0, import_jsx_runtime4.jsx)(Providers, {
    strapi,
    store,
    children: (0, import_jsx_runtime4.jsx)(import_react4.Suspense, {
      fallback: (0, import_jsx_runtime4.jsx)(Page.Loading, {}),
      children: (0, import_jsx_runtime4.jsx)(Outlet, {})
    })
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/components/ErrorElement.mjs
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var ErrorElement = () => {
  const error = useRouteError();
  const { formatMessage } = useIntl();
  const { copy } = useClipboard();
  if (error instanceof Error) {
    console.error(error);
    const handleClick = async () => {
      await copy(`
\`\`\`
${error.stack}
\`\`\`
      `);
    };
    return (0, import_jsx_runtime5.jsx)(Main, {
      height: "100%",
      children: (0, import_jsx_runtime5.jsx)(Flex, {
        alignItems: "center",
        height: "100%",
        justifyContent: "center",
        children: (0, import_jsx_runtime5.jsxs)(Flex, {
          gap: 7,
          padding: 7,
          direction: "column",
          width: "35%",
          shadow: "tableShadow",
          borderColor: "neutral150",
          background: "neutral0",
          hasRadius: true,
          maxWidth: "512px",
          children: [
            (0, import_jsx_runtime5.jsxs)(Flex, {
              direction: "column",
              gap: 2,
              children: [
                (0, import_jsx_runtime5.jsx)(ForwardRef$3, {
                  width: "32px",
                  height: "32px",
                  fill: "danger600"
                }),
                (0, import_jsx_runtime5.jsx)(Typography, {
                  fontSize: 4,
                  fontWeight: "bold",
                  textAlign: "center",
                  children: formatMessage({
                    id: "app.error",
                    defaultMessage: "Something went wrong"
                  })
                }),
                (0, import_jsx_runtime5.jsx)(Typography, {
                  variant: "omega",
                  textAlign: "center",
                  children: formatMessage({
                    id: "app.error.message",
                    defaultMessage: `It seems like there is a bug in your instance, but we've got you covered. Please notify your technical team so they can investigate the source of the problem and report the issue to us by opening a bug report on {link}.`
                  }, {
                    link: (0, import_jsx_runtime5.jsx)(Link2, {
                      isExternal: true,
                      // hack to get rid of the current endIcon, which should be removable by using `null`.
                      endIcon: true,
                      href: "https://github.com/strapi/strapi/issues/new?assignees=&labels=&projects=&template=BUG_REPORT.md",
                      children: `Strapi's GitHub`
                    })
                  })
                })
              ]
            }),
            (0, import_jsx_runtime5.jsxs)(Flex, {
              gap: 4,
              direction: "column",
              width: "100%",
              children: [
                (0, import_jsx_runtime5.jsx)(StyledAlert, {
                  onClose: () => {
                  },
                  width: "100%",
                  closeLabel: "",
                  variant: "danger",
                  children: (0, import_jsx_runtime5.jsx)(ErrorType, {
                    children: error.message
                  })
                }),
                (0, import_jsx_runtime5.jsx)(Button, {
                  onClick: handleClick,
                  variant: "tertiary",
                  startIcon: (0, import_jsx_runtime5.jsx)(ForwardRef$3R, {}),
                  children: formatMessage({
                    id: "app.error.copy",
                    defaultMessage: "Copy to clipboard"
                  })
                })
              ]
            })
          ]
        })
      })
    });
  }
  throw error;
};
var StyledAlert = dt(Alert)`
  & > div:first-child {
    display: none;
  }

  & > button {
    display: none;
  }
`;
var ErrorType = dt(Typography)`
  word-break: break-all;
  color: ${({ theme }) => theme.colors.danger600};
`;

// node_modules/@strapi/admin/dist/admin/admin/src/pages/NotFoundPage.mjs
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var NotFoundPage = () => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime6.jsxs)(Page.Main, {
    labelledBy: "title",
    children: [
      (0, import_jsx_runtime6.jsx)(Layouts.Header, {
        id: "title",
        title: formatMessage({
          id: "content-manager.pageNotFound",
          defaultMessage: "Page not found"
        })
      }),
      (0, import_jsx_runtime6.jsx)(Layouts.Content, {
        children: (0, import_jsx_runtime6.jsx)(EmptyStateLayout, {
          action: (0, import_jsx_runtime6.jsx)(LinkButton, {
            tag: Link,
            variant: "secondary",
            endIcon: (0, import_jsx_runtime6.jsx)(ForwardRef$5d, {}),
            to: "/",
            children: formatMessage({
              id: "app.components.NotFoundPage.back",
              defaultMessage: "Back to homepage"
            })
          }),
          content: formatMessage({
            id: "app.page.not.found",
            defaultMessage: "Oops! We can't seem to find the page you're looging for..."
          }),
          hasRadius: true,
          icon: (0, import_jsx_runtime6.jsx)(ForwardRef$F, {
            width: "16rem"
          }),
          shadow: "tableShadow"
        })
      })
    ]
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/router.mjs
var import_jsx_runtime13 = __toESM(require_jsx_runtime(), 1);

// node_modules/@strapi/admin/dist/admin/ee/admin/src/pages/SettingsPage/constants.mjs
var getEERoutes2 = () => [
  ...window.strapi.features.isEnabled(window.strapi.features.AUDIT_LOGS) ? [
    {
      path: "audit-logs",
      lazy: async () => {
        const { ProtectedListPage } = await import("./ListPage-KNA6SPLN.js");
        return {
          Component: ProtectedListPage
        };
      }
    }
  ] : [],
  ...window.strapi.features.isEnabled(window.strapi.features.SSO) ? [
    {
      path: "single-sign-on",
      lazy: async () => {
        const { ProtectedSSO } = await import("./SingleSignOnPage-5DDK26GT.js");
        return {
          Component: ProtectedSSO
        };
      }
    }
  ] : []
];

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Auth/AuthPage.mjs
var import_jsx_runtime12 = __toESM(require_jsx_runtime(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Auth/components/ForgotPassword.mjs
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var ForgotPassword = () => {
  const navigate = useNavigate();
  const { formatMessage } = useIntl();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const [forgotPassword, { error }] = useForgotPasswordMutation();
  return (0, import_jsx_runtime7.jsx)(UnauthenticatedLayout, {
    children: (0, import_jsx_runtime7.jsxs)(Main, {
      children: [
        (0, import_jsx_runtime7.jsxs)(LayoutContent, {
          children: [
            (0, import_jsx_runtime7.jsxs)(Column, {
              children: [
                (0, import_jsx_runtime7.jsx)(Logo, {}),
                (0, import_jsx_runtime7.jsx)(Box, {
                  paddingTop: 6,
                  paddingBottom: 7,
                  children: (0, import_jsx_runtime7.jsx)(Typography, {
                    tag: "h1",
                    variant: "alpha",
                    children: formatMessage({
                      id: "Auth.form.button.password-recovery",
                      defaultMessage: "Password Recovery"
                    })
                  })
                }),
                error ? (0, import_jsx_runtime7.jsx)(Typography, {
                  id: "global-form-error",
                  role: "alert",
                  tabIndex: -1,
                  textColor: "danger600",
                  children: isBaseQueryError(error) ? formatAPIError(error) : formatMessage({
                    id: "notification.error",
                    defaultMessage: "An error occurred"
                  })
                }) : null
              ]
            }),
            (0, import_jsx_runtime7.jsx)(Form, {
              method: "POST",
              initialValues: {
                email: ""
              },
              onSubmit: async (body) => {
                const res = await forgotPassword(body);
                if (!("error" in res)) {
                  navigate("/auth/forgot-password-success");
                }
              },
              validationSchema: create3().shape({
                email: create2().email(errorsTrads.email).required({
                  id: errorsTrads.required.id,
                  defaultMessage: "This field is required."
                }).nullable()
              }),
              children: (0, import_jsx_runtime7.jsxs)(Flex, {
                direction: "column",
                alignItems: "stretch",
                gap: 6,
                children: [
                  [
                    {
                      label: formatMessage({
                        id: "Auth.form.email.label",
                        defaultMessage: "Email"
                      }),
                      name: "email",
                      placeholder: formatMessage({
                        id: "Auth.form.email.placeholder",
                        defaultMessage: "<EMAIL>"
                      }),
                      required: true,
                      type: "string"
                    }
                  ].map((field) => (0, import_jsx_runtime7.jsx)(MemoizedInputRenderer, {
                    ...field
                  }, field.name)),
                  (0, import_jsx_runtime7.jsx)(Button, {
                    type: "submit",
                    fullWidth: true,
                    children: formatMessage({
                      id: "Auth.form.button.forgot-password",
                      defaultMessage: "Send Email"
                    })
                  })
                ]
              })
            })
          ]
        }),
        (0, import_jsx_runtime7.jsx)(Flex, {
          justifyContent: "center",
          children: (0, import_jsx_runtime7.jsx)(Box, {
            paddingTop: 4,
            children: (0, import_jsx_runtime7.jsx)(Link2, {
              tag: NavLink,
              to: "/auth/login",
              children: formatMessage({
                id: "Auth.link.ready",
                defaultMessage: "Ready to sign in?"
              })
            })
          })
        })
      ]
    })
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Auth/components/ForgotPasswordSuccess.mjs
var import_jsx_runtime8 = __toESM(require_jsx_runtime(), 1);
var ForgotPasswordSuccess = () => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime8.jsx)(UnauthenticatedLayout, {
    children: (0, import_jsx_runtime8.jsxs)(Main, {
      children: [
        (0, import_jsx_runtime8.jsx)(LayoutContent, {
          children: (0, import_jsx_runtime8.jsxs)(Column, {
            children: [
              (0, import_jsx_runtime8.jsx)(Logo, {}),
              (0, import_jsx_runtime8.jsx)(Box, {
                paddingTop: 6,
                paddingBottom: 7,
                children: (0, import_jsx_runtime8.jsx)(Typography, {
                  tag: "h1",
                  variant: "alpha",
                  children: formatMessage({
                    id: "app.containers.AuthPage.ForgotPasswordSuccess.title",
                    defaultMessage: "Email sent"
                  })
                })
              }),
              (0, import_jsx_runtime8.jsx)(Typography, {
                children: formatMessage({
                  id: "app.containers.AuthPage.ForgotPasswordSuccess.text.email",
                  defaultMessage: "It can take a few minutes to receive your password recovery link."
                })
              }),
              (0, import_jsx_runtime8.jsx)(Box, {
                paddingTop: 4,
                children: (0, import_jsx_runtime8.jsx)(Typography, {
                  children: formatMessage({
                    id: "app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin",
                    defaultMessage: "If you do not receive this link, please contact your administrator."
                  })
                })
              })
            ]
          })
        }),
        (0, import_jsx_runtime8.jsx)(Flex, {
          justifyContent: "center",
          children: (0, import_jsx_runtime8.jsx)(Box, {
            paddingTop: 4,
            children: (0, import_jsx_runtime8.jsx)(Link2, {
              tag: NavLink,
              to: "/auth/login",
              children: formatMessage({
                id: "Auth.link.signin",
                defaultMessage: "Sign in"
              })
            })
          })
        })
      ]
    })
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Auth/components/Oops.mjs
var import_jsx_runtime9 = __toESM(require_jsx_runtime(), 1);
var React2 = __toESM(require_react(), 1);
var Oops = () => {
  const { formatMessage } = useIntl();
  const { search: searchString } = useLocation();
  const query = React2.useMemo(() => new URLSearchParams(searchString), [
    searchString
  ]);
  const message = query.get("info") || formatMessage({
    id: "Auth.components.Oops.text",
    defaultMessage: "Your account has been suspended."
  });
  return (0, import_jsx_runtime9.jsx)(UnauthenticatedLayout, {
    children: (0, import_jsx_runtime9.jsxs)(Main, {
      children: [
        (0, import_jsx_runtime9.jsx)(LayoutContent, {
          children: (0, import_jsx_runtime9.jsxs)(Column, {
            children: [
              (0, import_jsx_runtime9.jsx)(Logo, {}),
              (0, import_jsx_runtime9.jsx)(Box, {
                paddingTop: 6,
                paddingBottom: 7,
                children: (0, import_jsx_runtime9.jsx)(Typography, {
                  tag: "h1",
                  variant: "alpha",
                  children: formatMessage({
                    id: "Auth.components.Oops.title",
                    defaultMessage: "Oops..."
                  })
                })
              }),
              (0, import_jsx_runtime9.jsx)(Typography, {
                children: message
              }),
              (0, import_jsx_runtime9.jsx)(Box, {
                paddingTop: 4,
                children: (0, import_jsx_runtime9.jsx)(Typography, {
                  children: formatMessage({
                    id: "Auth.components.Oops.text.admin",
                    defaultMessage: "If this is a mistake, please contact your administrator."
                  })
                })
              })
            ]
          })
        }),
        (0, import_jsx_runtime9.jsx)(Flex, {
          justifyContent: "center",
          children: (0, import_jsx_runtime9.jsx)(Box, {
            paddingTop: 4,
            children: (0, import_jsx_runtime9.jsx)(Link2, {
              tag: NavLink,
              to: "/auth/login",
              children: formatMessage({
                id: "Auth.link.signin",
                defaultMessage: "Sign in"
              })
            })
          })
        })
      ]
    })
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Auth/components/Register.mjs
var import_jsx_runtime10 = __toESM(require_jsx_runtime(), 1);
var React3 = __toESM(require_react(), 1);
var import_omit = __toESM(require_omit(), 1);
var REGISTER_USER_SCHEMA = create3().shape({
  firstname: create2().trim().required(errorsTrads.required).nullable(),
  lastname: create2().nullable(),
  password: create2().min(8, {
    id: errorsTrads.minLength.id,
    defaultMessage: "Password must be at least 8 characters",
    values: {
      min: 8
    }
  }).test("max-bytes", {
    id: "components.Input.error.contain.maxBytes",
    defaultMessage: "Password must be less than 73 bytes"
  }, function(value) {
    if (!value || typeof value !== "string") return true;
    const byteSize = getByteSize(value);
    return byteSize <= 72;
  }).matches(/[a-z]/, {
    message: {
      id: "components.Input.error.contain.lowercase",
      defaultMessage: "Password must contain at least 1 lowercase letter"
    }
  }).matches(/[A-Z]/, {
    message: {
      id: "components.Input.error.contain.uppercase",
      defaultMessage: "Password must contain at least 1 uppercase letter"
    }
  }).matches(/\d/, {
    message: {
      id: "components.Input.error.contain.number",
      defaultMessage: "Password must contain at least 1 number"
    }
  }).required({
    id: errorsTrads.required.id,
    defaultMessage: "Password is required"
  }).nullable(),
  confirmPassword: create2().required({
    id: errorsTrads.required.id,
    defaultMessage: "Confirm password is required"
  }).oneOf([
    create("password"),
    null
  ], {
    id: "components.Input.error.password.noMatch",
    defaultMessage: "Passwords must match"
  }).nullable(),
  registrationToken: create2().required({
    id: errorsTrads.required.id,
    defaultMessage: "Registration token is required"
  })
});
var REGISTER_ADMIN_SCHEMA = create3().shape({
  firstname: create2().trim().required({
    id: errorsTrads.required.id,
    defaultMessage: "Firstname is required"
  }).nullable(),
  lastname: create2().nullable(),
  password: create2().min(8, {
    id: errorsTrads.minLength.id,
    defaultMessage: "Password must be at least 8 characters",
    values: {
      min: 8
    }
  }).test("max-bytes", {
    id: "components.Input.error.contain.maxBytes",
    defaultMessage: "Password must be less than 73 bytes"
  }, function(value) {
    if (!value) return true;
    return new TextEncoder().encode(value).length <= 72;
  }).matches(/[a-z]/, {
    message: {
      id: "components.Input.error.contain.lowercase",
      defaultMessage: "Password must contain at least 1 lowercase letter"
    }
  }).matches(/[A-Z]/, {
    message: {
      id: "components.Input.error.contain.uppercase",
      defaultMessage: "Password must contain at least 1 uppercase letter"
    }
  }).matches(/\d/, {
    message: {
      id: "components.Input.error.contain.number",
      defaultMessage: "Password must contain at least 1 number"
    }
  }).required({
    id: errorsTrads.required.id,
    defaultMessage: "Password is required"
  }).nullable(),
  confirmPassword: create2().required({
    id: errorsTrads.required.id,
    defaultMessage: "Confirm password is required"
  }).nullable().oneOf([
    create("password"),
    null
  ], {
    id: "components.Input.error.password.noMatch",
    defaultMessage: "Passwords must match"
  }),
  email: create2().email({
    id: errorsTrads.email.id,
    defaultMessage: "Not a valid email"
  }).strict().lowercase({
    id: errorsTrads.lowercase.id,
    defaultMessage: "Email must be lowercase"
  }).required({
    id: errorsTrads.required.id,
    defaultMessage: "Email is required"
  }).nullable()
});
var Register = ({ hasAdmin }) => {
  const { toggleNotification } = useNotification();
  const navigate = useNavigate();
  const [submitCount, setSubmitCount] = React3.useState(0);
  const [apiError, setApiError] = React3.useState();
  const { trackUsage } = useTracking();
  const { formatMessage } = useIntl();
  const setSkipped = useGuidedTour("Register", (state) => state.setSkipped);
  const { search: searchString } = useLocation();
  const query = React3.useMemo(() => new URLSearchParams(searchString), [
    searchString
  ]);
  const match = useMatch("/auth/:authType");
  const { _unstableFormatAPIError: formatAPIError, _unstableFormatValidationErrors: formatValidationErrors } = useAPIErrorHandler();
  const { setNpsSurveySettings } = useNpsSurveySettings();
  const registrationToken = query.get("registrationToken");
  const { data: userInfo, error } = useGetRegistrationInfoQuery(registrationToken, {
    skip: !registrationToken
  });
  React3.useEffect(() => {
    if (error) {
      const message = isBaseQueryError(error) ? formatAPIError(error) : error.message ?? "";
      toggleNotification({
        type: "danger",
        message
      });
      navigate(`/auth/oops?info=${encodeURIComponent(message)}`);
    }
  }, [
    error,
    formatAPIError,
    navigate,
    toggleNotification
  ]);
  const [registerAdmin] = useRegisterAdminMutation();
  const [registerUser] = useRegisterUserMutation();
  const dispatch = useTypedDispatch();
  const handleRegisterAdmin = async ({ news, ...body }, setFormErrors) => {
    const res = await registerAdmin(body);
    if ("data" in res) {
      dispatch(login({
        token: res.data.token
      }));
      const { roles } = res.data.user;
      if (roles) {
        const isUserSuperAdmin = roles.find(({ code }) => code === "strapi-super-admin");
        if (isUserSuperAdmin) {
          localStorage.setItem("GUIDED_TOUR_SKIPPED", JSON.stringify(false));
          setSkipped(false);
          trackUsage("didLaunchGuidedtour");
        }
      }
      if (news) {
        setNpsSurveySettings((s) => ({
          ...s,
          enabled: true
        }));
        navigate({
          pathname: "/usecase",
          search: `?hasAdmin=${true}`
        });
      } else {
        navigate("/");
      }
    } else {
      if (isBaseQueryError(res.error)) {
        trackUsage("didNotCreateFirstAdmin");
        if (res.error.name === "ValidationError") {
          setFormErrors(formatValidationErrors(res.error));
          return;
        }
        setApiError(formatAPIError(res.error));
      }
    }
  };
  const handleRegisterUser = async ({ news, ...body }, setFormErrors) => {
    const res = await registerUser(body);
    if ("data" in res) {
      dispatch(login({
        token: res.data.token
      }));
      if (news) {
        setNpsSurveySettings((s) => ({
          ...s,
          enabled: true
        }));
        navigate({
          pathname: "/usecase",
          search: `?hasAdmin=${hasAdmin}`
        });
      } else {
        navigate("/");
      }
    } else {
      if (isBaseQueryError(res.error)) {
        trackUsage("didNotCreateFirstAdmin");
        if (res.error.name === "ValidationError") {
          setFormErrors(formatValidationErrors(res.error));
          return;
        }
        setApiError(formatAPIError(res.error));
      }
    }
  };
  if (!match || match.params.authType !== "register" && match.params.authType !== "register-admin") {
    return (0, import_jsx_runtime10.jsx)(Navigate, {
      to: "/"
    });
  }
  const isAdminRegistration = match.params.authType === "register-admin";
  const schema = isAdminRegistration ? REGISTER_ADMIN_SCHEMA : REGISTER_USER_SCHEMA;
  return (0, import_jsx_runtime10.jsx)(UnauthenticatedLayout, {
    children: (0, import_jsx_runtime10.jsxs)(LayoutContent, {
      children: [
        (0, import_jsx_runtime10.jsxs)(Flex, {
          direction: "column",
          alignItems: "center",
          gap: 3,
          children: [
            (0, import_jsx_runtime10.jsx)(Logo, {}),
            (0, import_jsx_runtime10.jsx)(Typography, {
              tag: "h1",
              variant: "alpha",
              textAlign: "center",
              children: formatMessage({
                id: "Auth.form.welcome.title",
                defaultMessage: "Welcome to Strapi!"
              })
            }),
            (0, import_jsx_runtime10.jsx)(Typography, {
              variant: "epsilon",
              textColor: "neutral600",
              textAlign: "center",
              children: formatMessage({
                id: "Auth.form.register.subtitle",
                defaultMessage: "Credentials are only used to authenticate in Strapi. All saved data will be stored in your database."
              })
            }),
            apiError ? (0, import_jsx_runtime10.jsx)(Typography, {
              id: "global-form-error",
              role: "alert",
              tabIndex: -1,
              textColor: "danger600",
              children: apiError
            }) : null
          ]
        }),
        (0, import_jsx_runtime10.jsx)(Form, {
          method: "POST",
          initialValues: {
            firstname: (userInfo == null ? void 0 : userInfo.firstname) || "",
            lastname: (userInfo == null ? void 0 : userInfo.lastname) || "",
            email: (userInfo == null ? void 0 : userInfo.email) || "",
            password: "",
            confirmPassword: "",
            registrationToken: registrationToken || void 0,
            news: false
          },
          onSubmit: async (data, helpers) => {
            const normalizedData = normalizeData(data);
            try {
              await schema.validate(normalizedData, {
                abortEarly: false
              });
              if (submitCount > 0 && isAdminRegistration) {
                trackUsage("didSubmitWithErrorsFirstAdmin", {
                  count: submitCount.toString()
                });
              }
              if (normalizedData.registrationToken) {
                handleRegisterUser({
                  userInfo: (0, import_omit.default)(normalizedData, [
                    "registrationToken",
                    "confirmPassword",
                    "email",
                    "news"
                  ]),
                  registrationToken: normalizedData.registrationToken,
                  news: normalizedData.news
                }, helpers.setErrors);
              } else {
                await handleRegisterAdmin((0, import_omit.default)(normalizedData, [
                  "registrationToken",
                  "confirmPassword"
                ]), helpers.setErrors);
              }
            } catch (err) {
              if (err instanceof ValidationError) {
                helpers.setErrors(err.inner.reduce((acc, { message, path }) => {
                  if (path && typeof message === "object") {
                    acc[path] = formatMessage(message);
                  }
                  return acc;
                }, {}));
              }
              setSubmitCount(submitCount + 1);
            }
          },
          children: (0, import_jsx_runtime10.jsxs)(Flex, {
            direction: "column",
            alignItems: "stretch",
            gap: 6,
            marginTop: 7,
            children: [
              (0, import_jsx_runtime10.jsx)(Grid.Root, {
                gap: 4,
                children: [
                  {
                    label: formatMessage({
                      id: "Auth.form.firstname.label",
                      defaultMessage: "Firstname"
                    }),
                    name: "firstname",
                    required: true,
                    size: 6,
                    type: "string"
                  },
                  {
                    label: formatMessage({
                      id: "Auth.form.lastname.label",
                      defaultMessage: "Lastname"
                    }),
                    name: "lastname",
                    size: 6,
                    type: "string"
                  },
                  {
                    disabled: !isAdminRegistration,
                    label: formatMessage({
                      id: "Auth.form.email.label",
                      defaultMessage: "Email"
                    }),
                    name: "email",
                    required: true,
                    size: 12,
                    type: "email"
                  },
                  {
                    hint: formatMessage({
                      id: "Auth.form.password.hint",
                      defaultMessage: "Must be at least 8 characters, 1 uppercase, 1 lowercase & 1 number"
                    }),
                    label: formatMessage({
                      id: "global.password",
                      defaultMessage: "Password"
                    }),
                    name: "password",
                    required: true,
                    size: 12,
                    type: "password"
                  },
                  {
                    label: formatMessage({
                      id: "Auth.form.confirmPassword.label",
                      defaultMessage: "Confirm Password"
                    }),
                    name: "confirmPassword",
                    required: true,
                    size: 12,
                    type: "password"
                  },
                  {
                    label: formatMessage({
                      id: "Auth.form.register.news.label",
                      defaultMessage: "Keep me updated about new features & upcoming improvements (by doing this you accept the {terms} and the {policy})."
                    }, {
                      terms: (0, import_jsx_runtime10.jsx)(A, {
                        target: "_blank",
                        href: "https://strapi.io/terms",
                        rel: "noreferrer",
                        children: formatMessage({
                          id: "Auth.privacy-policy-agreement.terms",
                          defaultMessage: "terms"
                        })
                      }),
                      policy: (0, import_jsx_runtime10.jsx)(A, {
                        target: "_blank",
                        href: "https://strapi.io/privacy",
                        rel: "noreferrer",
                        children: formatMessage({
                          id: "Auth.privacy-policy-agreement.policy",
                          defaultMessage: "policy"
                        })
                      })
                    }),
                    name: "news",
                    size: 12,
                    type: "checkbox"
                  }
                ].map(({ size, ...field }) => (0, import_jsx_runtime10.jsx)(Grid.Item, {
                  col: size,
                  direction: "column",
                  alignItems: "stretch",
                  children: (0, import_jsx_runtime10.jsx)(MemoizedInputRenderer, {
                    ...field
                  })
                }, field.name))
              }),
              (0, import_jsx_runtime10.jsx)(Button, {
                fullWidth: true,
                size: "L",
                type: "submit",
                children: formatMessage({
                  id: "Auth.form.button.register",
                  defaultMessage: "Let's start"
                })
              })
            ]
          })
        }),
        (match == null ? void 0 : match.params.authType) === "register" && (0, import_jsx_runtime10.jsx)(Box, {
          paddingTop: 4,
          children: (0, import_jsx_runtime10.jsx)(Flex, {
            justifyContent: "center",
            children: (0, import_jsx_runtime10.jsx)(Link2, {
              tag: NavLink,
              to: "/auth/login",
              children: formatMessage({
                id: "Auth.link.signin.account",
                defaultMessage: "Already have an account?"
              })
            })
          })
        })
      ]
    })
  });
};
function normalizeData(data) {
  return Object.entries(data).reduce((acc, [key, value]) => {
    if (![
      "password",
      "confirmPassword"
    ].includes(key) && typeof value === "string") {
      acc[key] = value.trim();
      if (key === "lastname") {
        acc[key] = value || void 0;
      }
    } else {
      acc[key] = value;
    }
    return acc;
  }, {});
}
var A = dt.a`
  color: ${({ theme }) => theme.colors.primary600};
`;

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Auth/components/ResetPassword.mjs
var import_jsx_runtime11 = __toESM(require_jsx_runtime(), 1);
var React4 = __toESM(require_react(), 1);
var RESET_PASSWORD_SCHEMA = create3().shape({
  password: create2().min(8, {
    id: errorsTrads.minLength.id,
    defaultMessage: "Password must be at least 8 characters",
    values: {
      min: 8
    }
  }).test("required-byte-size", {
    id: "components.Input.error.contain.maxBytes",
    defaultMessage: "Password must be less than 73 bytes"
  }, function(value) {
    if (!value || typeof value !== "string") return true;
    const byteSize = getByteSize(value);
    return byteSize <= 72;
  }).matches(/[a-z]/, {
    message: {
      id: "components.Input.error.contain.lowercase",
      defaultMessage: "Password must contain at least 1 lowercase letter"
    }
  }).matches(/[A-Z]/, {
    message: {
      id: "components.Input.error.contain.uppercase",
      defaultMessage: "Password must contain at least 1 uppercase letter"
    }
  }).matches(/\d/, {
    message: {
      id: "components.Input.error.contain.number",
      defaultMessage: "Password must contain at least 1 number"
    }
  }).required({
    id: errorsTrads.required.id,
    defaultMessage: "Password is required"
  }).nullable(),
  confirmPassword: create2().required({
    id: errorsTrads.required.id,
    defaultMessage: "Confirm password is required"
  }).oneOf([
    create("password"),
    null
  ], {
    id: "components.Input.error.password.noMatch",
    defaultMessage: "Passwords must match"
  }).nullable()
});
var ResetPassword = () => {
  const { formatMessage } = useIntl();
  const dispatch = useTypedDispatch();
  const navigate = useNavigate();
  const { search: searchString } = useLocation();
  const query = React4.useMemo(() => new URLSearchParams(searchString), [
    searchString
  ]);
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const [resetPassword, { error }] = useResetPasswordMutation();
  const handleSubmit = async (body) => {
    const res = await resetPassword(body);
    if ("data" in res) {
      dispatch(login({
        token: res.data.token
      }));
      navigate("/");
    }
  };
  if (!query.get("code")) {
    return (0, import_jsx_runtime11.jsx)(Navigate, {
      to: "/auth/login"
    });
  }
  return (0, import_jsx_runtime11.jsx)(UnauthenticatedLayout, {
    children: (0, import_jsx_runtime11.jsxs)(Main, {
      children: [
        (0, import_jsx_runtime11.jsxs)(LayoutContent, {
          children: [
            (0, import_jsx_runtime11.jsxs)(Column, {
              children: [
                (0, import_jsx_runtime11.jsx)(Logo, {}),
                (0, import_jsx_runtime11.jsx)(Box, {
                  paddingTop: 6,
                  paddingBottom: 7,
                  children: (0, import_jsx_runtime11.jsx)(Typography, {
                    tag: "h1",
                    variant: "alpha",
                    children: formatMessage({
                      id: "global.reset-password",
                      defaultMessage: "Reset password"
                    })
                  })
                }),
                error ? (0, import_jsx_runtime11.jsx)(Typography, {
                  id: "global-form-error",
                  role: "alert",
                  tabIndex: -1,
                  textColor: "danger600",
                  children: isBaseQueryError(error) ? formatAPIError(error) : formatMessage({
                    id: "notification.error",
                    defaultMessage: "An error occurred"
                  })
                }) : null
              ]
            }),
            (0, import_jsx_runtime11.jsx)(Form, {
              method: "POST",
              initialValues: {
                password: "",
                confirmPassword: ""
              },
              onSubmit: (values) => {
                handleSubmit({
                  password: values.password,
                  resetPasswordToken: query.get("code")
                });
              },
              validationSchema: RESET_PASSWORD_SCHEMA,
              children: (0, import_jsx_runtime11.jsxs)(Flex, {
                direction: "column",
                alignItems: "stretch",
                gap: 6,
                children: [
                  [
                    {
                      hint: formatMessage({
                        id: "Auth.form.password.hint",
                        defaultMessage: "Password must contain at least 8 characters, 1 uppercase, 1 lowercase and 1 number"
                      }),
                      label: formatMessage({
                        id: "global.password",
                        defaultMessage: "Password"
                      }),
                      name: "password",
                      required: true,
                      type: "password"
                    },
                    {
                      label: formatMessage({
                        id: "Auth.form.confirmPassword.label",
                        defaultMessage: "Confirm Password"
                      }),
                      name: "confirmPassword",
                      required: true,
                      type: "password"
                    }
                  ].map((field) => (0, import_jsx_runtime11.jsx)(MemoizedInputRenderer, {
                    ...field
                  }, field.name)),
                  (0, import_jsx_runtime11.jsx)(Button, {
                    fullWidth: true,
                    type: "submit",
                    children: formatMessage({
                      id: "global.change-password",
                      defaultMessage: "Change password"
                    })
                  })
                ]
              })
            })
          ]
        }),
        (0, import_jsx_runtime11.jsx)(Flex, {
          justifyContent: "center",
          children: (0, import_jsx_runtime11.jsx)(Box, {
            paddingTop: 4,
            children: (0, import_jsx_runtime11.jsx)(Link2, {
              tag: NavLink,
              to: "/auth/login",
              children: formatMessage({
                id: "Auth.link.ready",
                defaultMessage: "Ready to sign in?"
              })
            })
          })
        })
      ]
    })
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Auth/constants.mjs
var FORMS = {
  "forgot-password": ForgotPassword,
  "forgot-password-success": ForgotPasswordSuccess,
  // the `Component` attribute is set after all forms and CE/EE components are loaded, but since we
  // are here outside of a React component we can not use the hook directly
  login: () => null,
  oops: Oops,
  register: Register,
  "register-admin": Register,
  "reset-password": ResetPassword,
  providers: () => null
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Auth/AuthPage.mjs
var AuthPage = () => {
  const { search } = useLocation();
  const match = useMatch("/auth/:authType");
  const authType = match == null ? void 0 : match.params.authType;
  const { data } = useInitQuery();
  const { hasAdmin } = data ?? {};
  const Login$1 = useEnterprise(Login, async () => (await import("./Login-UUOGBDZO.js")).LoginEE);
  const forms = useEnterprise(FORMS, async () => (await import("./constants-AXLEZQHJ.js")).FORMS, {
    combine(ceForms, eeForms) {
      return {
        ...ceForms,
        ...eeForms
      };
    },
    defaultValue: FORMS
  });
  const { token } = useAuth("AuthPage", (auth) => auth);
  if (!authType || !forms) {
    return (0, import_jsx_runtime12.jsx)(Navigate, {
      to: "/"
    });
  }
  const Component = forms[authType];
  if (!Component) {
    return (0, import_jsx_runtime12.jsx)(Navigate, {
      to: "/"
    });
  }
  if (authType !== "register-admin" && authType !== "register" && token) {
    return (0, import_jsx_runtime12.jsx)(Navigate, {
      to: "/"
    });
  }
  if (hasAdmin && authType === "register-admin" && token) {
    return (0, import_jsx_runtime12.jsx)(Navigate, {
      to: "/"
    });
  }
  if (!hasAdmin && authType !== "register-admin") {
    return (0, import_jsx_runtime12.jsx)(Navigate, {
      to: {
        pathname: "/auth/register-admin",
        // Forward the `?redirectTo` from /auth/login
        // /abc => /auth/login?redirectTo=%2Fabc => /auth/register-admin?redirectTo=%2Fabc
        search
      }
    });
  }
  if (Login$1 && authType === "login") {
    return (0, import_jsx_runtime12.jsx)(Login$1, {});
  } else if (authType === "login" && !Login$1) {
    return null;
  }
  return (0, import_jsx_runtime12.jsx)(Component, {
    hasAdmin
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/constants.mjs
var ROUTES_CE = [
  {
    lazy: async () => {
      const { ProtectedListPage } = await import("./ListPage-ILG2CSP3.js");
      return {
        Component: ProtectedListPage
      };
    },
    path: "roles"
  },
  {
    lazy: async () => {
      const { ProtectedCreatePage } = await import("./CreatePage-BRWBUNUH.js");
      return {
        Component: ProtectedCreatePage
      };
    },
    path: "roles/duplicate/:id"
  },
  {
    lazy: async () => {
      const { ProtectedCreatePage } = await import("./CreatePage-BRWBUNUH.js");
      return {
        Component: ProtectedCreatePage
      };
    },
    path: "roles/new"
  },
  {
    lazy: async () => {
      const { ProtectedEditPage } = await import("./EditPage-B3K6FUQO.js");
      return {
        Component: ProtectedEditPage
      };
    },
    path: "roles/:id"
  },
  {
    lazy: async () => {
      const { ProtectedListPage } = await import("./ListPage-JGQCS7AV.js");
      return {
        Component: ProtectedListPage
      };
    },
    path: "users"
  },
  {
    lazy: async () => {
      const { ProtectedEditPage } = await import("./EditPage-ISNAO326.js");
      return {
        Component: ProtectedEditPage
      };
    },
    path: "users/:id"
  },
  {
    lazy: async () => {
      const { ProtectedCreatePage } = await import("./CreatePage-FHQP53ZN.js");
      return {
        Component: ProtectedCreatePage
      };
    },
    path: "webhooks/create"
  },
  {
    lazy: async () => {
      const editWebhook = await import("./EditPage-HD3ZL5IH.js");
      return {
        Component: editWebhook.ProtectedEditPage
      };
    },
    path: "webhooks/:id"
  },
  {
    lazy: async () => {
      const { ProtectedListPage } = await import("./ListPage-2XGO36HR.js");
      return {
        Component: ProtectedListPage
      };
    },
    path: "webhooks"
  },
  {
    lazy: async () => {
      const { ProtectedListView } = await import("./ListView-P646PDDC.js");
      return {
        Component: ProtectedListView
      };
    },
    path: "api-tokens"
  },
  {
    lazy: async () => {
      const { ProtectedCreateView } = await import("./CreateView-TTKNTOQK.js");
      return {
        Component: ProtectedCreateView
      };
    },
    path: "api-tokens/create"
  },
  {
    lazy: async () => {
      const { ProtectedEditView } = await import("./EditViewPage-A2SMPKAL.js");
      return {
        Component: ProtectedEditView
      };
    },
    path: "api-tokens/:id"
  },
  {
    lazy: async () => {
      const { ProtectedCreateView } = await import("./CreateView-G5SRGWZN.js");
      return {
        Component: ProtectedCreateView
      };
    },
    path: "transfer-tokens/create"
  },
  {
    lazy: async () => {
      const { ProtectedListView } = await import("./ListView-SQPO4KRW.js");
      return {
        Component: ProtectedListView
      };
    },
    path: "transfer-tokens"
  },
  {
    lazy: async () => {
      const { ProtectedEditView } = await import("./EditView-6LWAYC7U.js");
      return {
        Component: ProtectedEditView
      };
    },
    path: "transfer-tokens/:id"
  },
  {
    lazy: async () => {
      const { ProtectedInstalledPlugins } = await import("./InstalledPlugins-HRNOU7OH.js");
      return {
        Component: ProtectedInstalledPlugins
      };
    },
    path: "list-plugins"
  },
  {
    lazy: async () => {
      const { PurchaseAuditLogs } = await import("./PurchaseAuditLogs-K2RS2MHY.js");
      return {
        Component: PurchaseAuditLogs
      };
    },
    path: "purchase-audit-logs"
  },
  {
    lazy: async () => {
      const { PurchaseSingleSignOn } = await import("./PurchaseSingleSignOn-6VLKTOGY.js");
      return {
        Component: PurchaseSingleSignOn
      };
    },
    path: "purchase-single-sign-on"
  },
  {
    lazy: async () => {
      const { PurchaseContentHistory } = await import("./PurchaseContentHistory-OUSH2COX.js");
      return {
        Component: PurchaseContentHistory
      };
    },
    path: "purchase-content-history"
  }
];

// node_modules/@strapi/admin/dist/admin/admin/src/router.mjs
var getImmutableRoutes = () => [
  {
    path: "usecase",
    lazy: async () => {
      const { PrivateUseCasePage } = await import("./UseCasePage-WXHQSBTX.js");
      return {
        Component: PrivateUseCasePage
      };
    }
  },
  // this needs to go before auth/:authType because otherwise it won't match the route
  ...getEERoutes(),
  {
    path: "auth/:authType",
    element: (0, import_jsx_runtime13.jsx)(AuthPage, {})
  }
];
var getInitialRoutes = () => [
  {
    index: true,
    lazy: async () => {
      const { HomePage } = await import("./HomePage-YPEBBIYI.js");
      return {
        Component: HomePage
      };
    }
  },
  {
    path: "me",
    lazy: async () => {
      const { ProfilePage } = await import("./ProfilePage-7UUXF36O.js");
      return {
        Component: ProfilePage
      };
    }
  },
  {
    path: "marketplace",
    lazy: async () => {
      const { ProtectedMarketplacePage } = await import("./MarketplacePage-4ZUSMWUQ.js");
      return {
        Component: ProtectedMarketplacePage
      };
    }
  },
  {
    path: "settings/*",
    lazy: async () => {
      const { Layout } = await import("./Layout-65QL3ALE.js");
      return {
        Component: Layout
      };
    },
    children: [
      {
        path: "application-infos",
        lazy: async () => {
          const { ApplicationInfoPage } = await import("./ApplicationInfoPage-TRSYQQ7C.js");
          return {
            Component: ApplicationInfoPage
          };
        }
      },
      // ...Object.values(this.settings).flatMap(({ links }) =>
      //   links.map(({ to, Component }) => ({
      //     path: `${to}/*`,
      //     element: (
      //       <React.Suspense fallback={<Page.Loading />}>
      //         <Component />
      //       </React.Suspense>
      //     ),
      //   }))
      // ),
      ...[
        ...getEERoutes2(),
        ...ROUTES_CE
      ].filter((route, index, refArray) => refArray.findIndex((obj) => obj.path === route.path) === index)
    ]
  }
];

// node_modules/@strapi/admin/dist/admin/admin/src/core/apis/router.mjs
var Router = class {
  get routes() {
    return this._routes;
  }
  get menu() {
    return this._menu;
  }
  get settings() {
    return this._settings;
  }
  /**
  * @internal This method is used internally by Strapi to create the router.
  * It should not be used by plugins, doing so will likely break the application.
  */
  createRouter(strapi, { memory, ...opts } = {}) {
    const routes = [
      {
        path: "/*",
        errorElement: (0, import_jsx_runtime14.jsx)(Provider_default, {
          store: strapi.store,
          children: (0, import_jsx_runtime14.jsx)(LanguageProvider, {
            messages: strapi.configurations.translations,
            children: (0, import_jsx_runtime14.jsx)(Theme, {
              themes: strapi.configurations.themes,
              children: (0, import_jsx_runtime14.jsx)(ErrorElement, {})
            })
          })
        }),
        element: (0, import_jsx_runtime14.jsx)(App, {
          strapi,
          store: strapi.store
        }),
        children: [
          ...getImmutableRoutes(),
          {
            path: "/*",
            lazy: async () => {
              const { PrivateAdminLayout } = await import("./AuthenticatedLayout-I7PO4XWS.js");
              return {
                Component: PrivateAdminLayout
              };
            },
            children: [
              ...this.routes,
              {
                path: "*",
                element: (0, import_jsx_runtime14.jsx)(NotFoundPage, {})
              }
            ]
          }
        ]
      }
    ];
    if (memory) {
      this.router = createMemoryRouter(routes, opts);
    } else {
      this.router = createBrowserRouter(routes, opts);
    }
    return this.router;
  }
  addSettingsLink(section, link) {
    var _a, _b, _c, _d;
    if (typeof section === "object" && "links" in section) {
      (0, import_invariant2.default)(section.id, "section.id should be defined");
      (0, import_invariant2.default)(((_a = section.intlLabel) == null ? void 0 : _a.id) && ((_b = section.intlLabel) == null ? void 0 : _b.defaultMessage), "section.intlLabel should be defined");
      (0, import_invariant2.default)(this.settings[section.id] === void 0, "A similar section already exists");
      (0, import_invariant2.default)(Array.isArray(section.links), "TypeError expected links to be an array");
      this.settings[section.id] = {
        ...section,
        links: []
      };
      section.links.forEach((link2) => {
        this.createSettingsLink(section.id, link2);
      });
    } else if (typeof section === "object" && link) {
      (0, import_invariant2.default)(section.id, "section.id should be defined");
      (0, import_invariant2.default)(((_c = section.intlLabel) == null ? void 0 : _c.id) && ((_d = section.intlLabel) == null ? void 0 : _d.defaultMessage), "section.intlLabel should be defined");
      (0, import_invariant2.default)(this.settings[section.id] === void 0, "A similar section already exists");
      this.settings[section.id] = {
        ...section,
        links: []
      };
      if (Array.isArray(link)) {
        link.forEach((l) => this.createSettingsLink(section.id, l));
      } else {
        this.createSettingsLink(section.id, link);
      }
    } else if (typeof section === "string" && link) {
      if (Array.isArray(link)) {
        link.forEach((l) => this.createSettingsLink(section, l));
      } else {
        this.createSettingsLink(section, link);
      }
    } else {
      throw new Error("Invalid arguments provided to addSettingsLink, at minimum a sectionId and link are required.");
    }
  }
  /**
  * @alpha
  * @description Adds a route or an array of routes to the router.
  * Otherwise, pass a function that receives the current routes and
  * returns the new routes in a reducer like fashion.
  */
  addRoute(route) {
    if (Array.isArray(route)) {
      this._routes = [
        ...this._routes,
        ...route
      ];
    } else if (typeof route === "object" && route !== null) {
      this._routes.push(route);
    } else if (typeof route === "function") {
      this._routes = route(this._routes);
    } else {
      throw new Error(`Expected the \`route\` passed to \`addRoute\` to be an array or a function, but received ${getPrintableType(route)}`);
    }
  }
  constructor(initialRoutes) {
    this._routes = [];
    this.router = null;
    this._menu = [];
    this._settings = {
      global: {
        id: "global",
        intlLabel: {
          id: "Settings.global",
          defaultMessage: "Global Settings"
        },
        links: []
      }
    };
    this.addMenuLink = (link) => {
      var _a, _b;
      (0, import_invariant2.default)(link.to, `[${link.intlLabel.defaultMessage}]: link.to should be defined`);
      (0, import_invariant2.default)(typeof link.to === "string", `[${link.intlLabel.defaultMessage}]: Expected link.to to be a string instead received ${typeof link.to}`);
      (0, import_invariant2.default)(((_a = link.intlLabel) == null ? void 0 : _a.id) && ((_b = link.intlLabel) == null ? void 0 : _b.defaultMessage), `[${link.intlLabel.defaultMessage}]: link.intlLabel.id & link.intlLabel.defaultMessage should be defined`);
      (0, import_invariant2.default)(!link.Component || link.Component && typeof link.Component === "function", `[${link.intlLabel.defaultMessage}]: link.Component must be a function returning a Promise that returns a default component. Please use: \`Component: () => import(path)\` instead.`);
      if (!link.Component || link.Component && typeof link.Component === "function" && // @ts-expect-error – shh
      link.Component[Symbol.toStringTag] === "AsyncFunction") {
        console.warn(`
      [${link.intlLabel.defaultMessage}]: [deprecated] addMenuLink() was called with an async Component from the plugin "${link.intlLabel.defaultMessage}". This will be removed in the future. Please use: \`Component: () => import(path)\` ensuring you return a default export instead.
      `.trim());
      }
      if (link.to.startsWith("/")) {
        console.warn(`[${link.intlLabel.defaultMessage}]: the \`to\` property of your menu link is an absolute path, it should be relative to the root of the application. This has been corrected for you but will be removed in a future version of Strapi.`);
        link.to = link.to.slice(1);
      }
      const { Component, ...restLink } = link;
      if (Component) {
        this._routes.push({
          path: `${link.to}/*`,
          lazy: async () => {
            const mod = await Component();
            if ("default" in mod) {
              return {
                Component: mod.default
              };
            } else {
              return {
                Component: mod
              };
            }
          }
        });
      }
      this.menu.push(restLink);
    };
    this.createSettingsLink = (sectionId, link) => {
      var _a, _b;
      (0, import_invariant2.default)(this._settings[sectionId], "The section does not exist");
      (0, import_invariant2.default)(link.id, `[${link.intlLabel.defaultMessage}]: link.id should be defined`);
      (0, import_invariant2.default)(((_a = link.intlLabel) == null ? void 0 : _a.id) && ((_b = link.intlLabel) == null ? void 0 : _b.defaultMessage), `[${link.intlLabel.defaultMessage}]: link.intlLabel.id & link.intlLabel.defaultMessage`);
      (0, import_invariant2.default)(link.to, `[${link.intlLabel.defaultMessage}]: link.to should be defined`);
      (0, import_invariant2.default)(!link.Component || link.Component && typeof link.Component === "function", `[${link.intlLabel.defaultMessage}]: link.Component must be a function returning a Promise. Please use: \`Component: () => import(path)\` instead.`);
      if (!link.Component || link.Component && typeof link.Component === "function" && // @ts-expect-error – shh
      link.Component[Symbol.toStringTag] === "AsyncFunction") {
        console.warn(`
      [${link.intlLabel.defaultMessage}]: [deprecated] addSettingsLink() was called with an async Component from the plugin "${link.intlLabel.defaultMessage}". This will be removed in the future. Please use: \`Component: () => import(path)\` ensuring you return a default export instead.
      `.trim());
      }
      if (link.to.startsWith("/")) {
        console.warn(`[${link.intlLabel.defaultMessage}]: the \`to\` property of your settings link is an absolute path. It should be relative to \`/settings\`. This has been corrected for you but will be removed in a future version of Strapi.`);
        link.to = link.to.slice(1);
      }
      if (link.to.split("/")[0] === "settings") {
        console.warn(`[${link.intlLabel.defaultMessage}]: the \`to\` property of your settings link has \`settings\` as the first part of it's path. It should be relative to \`settings\` and therefore, not include it. This has been corrected for you but will be removed in a future version of Strapi.`);
        link.to = link.to.split("/").slice(1).join("/");
      }
      const { Component, ...restLink } = link;
      const settingsIndex = this._routes.findIndex((route) => route.path === "settings/*");
      if (!settingsIndex) {
        console.warn("A third party plugin has removed the settings section, the settings link cannot be added.");
        return;
      } else if (!this._routes[settingsIndex].children) {
        this._routes[settingsIndex].children = [];
      }
      if (Component) {
        this._routes[settingsIndex].children.push({
          path: `${link.to}/*`,
          lazy: async () => {
            const mod = await Component();
            if ("default" in mod) {
              return {
                Component: mod.default
              };
            } else {
              return {
                Component: mod
              };
            }
          }
        });
      }
      this._settings[sectionId].links.push(restLink);
    };
    this._routes = initialRoutes;
  }
};
var getPrintableType = (value) => {
  const nativeType = typeof value;
  if (nativeType === "object") {
    if (value === null) return "null";
    if (Array.isArray(value)) return "array";
    if (value instanceof Object && value.constructor.name !== "Object") {
      return value.constructor.name;
    }
  }
  return nativeType;
};

// node_modules/@strapi/admin/dist/admin/admin/src/core/apis/Widgets.mjs
var import_invariant3 = __toESM(require_browser(), 1);
var Widgets = class {
  constructor() {
    this.register = (widget) => {
      if (Array.isArray(widget)) {
        widget.forEach((newWidget) => {
          this.register(newWidget);
        });
      } else {
        (0, import_invariant3.default)(widget.id, "An id must be provided");
        (0, import_invariant3.default)(widget.component, "A component must be provided");
        (0, import_invariant3.default)(widget.title, "A title must be provided");
        (0, import_invariant3.default)(widget.icon, "An icon must be provided");
        const { id, pluginId, ...widgetToStore } = widget;
        const uid = pluginId ? `plugin::${pluginId}.${id}` : `global::${id}`;
        this.widgets[uid] = {
          ...widgetToStore,
          uid
        };
      }
    };
    this.getAll = () => {
      return Object.values(this.widgets);
    };
    this.widgets = {};
  }
};

// node_modules/@strapi/admin/dist/admin/admin/src/core/store/configure.mjs
var staticReducers = {
  [adminApi.reducerPath]: adminApi.reducer,
  admin_app: reducer
};
var injectReducerStoreEnhancer = (appReducers) => (next) => (...args) => {
  const store = next(...args);
  const asyncReducers = {};
  return {
    ...store,
    asyncReducers,
    injectReducer: (key, asyncReducer) => {
      asyncReducers[key] = asyncReducer;
      store.replaceReducer(
        // @ts-expect-error we dynamically add reducers which makes the types uncomfortable.
        combineReducers({
          ...appReducers,
          ...asyncReducers
        })
      );
    }
  };
};
var configureStoreImpl = (preloadedState = {}, appMiddlewares = [], injectedReducers = {}) => {
  const coreReducers = {
    ...staticReducers,
    ...injectedReducers
  };
  const defaultMiddlewareOptions = {};
  if (false) {
    defaultMiddlewareOptions.serializableCheck = false;
    defaultMiddlewareOptions.immutableCheck = false;
  }
  const store = configureStore({
    preloadedState: {
      admin_app: preloadedState.admin_app
    },
    reducer: coreReducers,
    devTools: true,
    middleware: (getDefaultMiddleware) => [
      ...getDefaultMiddleware(defaultMiddlewareOptions),
      rtkQueryUnauthorizedMiddleware,
      adminApi.middleware,
      ...appMiddlewares.map((m) => m())
    ],
    enhancers: [
      injectReducerStoreEnhancer(coreReducers)
    ]
  });
  return store;
};
var rtkQueryUnauthorizedMiddleware = ({ dispatch }) => (next) => (action) => {
  var _a;
  if (isRejected(action) && ((_a = action.payload) == null ? void 0 : _a.status) === 401) {
    dispatch(logout());
    window.location.href = "/admin/auth/login";
    return;
  }
  return next(action);
};

// node_modules/@strapi/admin/dist/admin/admin/src/core/utils/createHook.mjs
var createHook = () => {
  const _handlers = [];
  return {
    register(fn) {
      _handlers.push(fn);
    },
    delete(handler) {
      _handlers.splice(_handlers.indexOf(handler), 1);
    },
    runWaterfall(args, store) {
      return _handlers.reduce((acc, fn) => fn(acc, store), args);
    },
    async runWaterfallAsync(args, store) {
      let result = args;
      for (const fn of _handlers) {
        result = await fn(result, store);
      }
      return result;
    },
    runSeries(...args) {
      return _handlers.map((fn) => fn(...args));
    },
    async runSeriesAsync(...args) {
      const result = [];
      for (const fn of _handlers) {
        result.push(await fn(...args));
      }
      return result;
    },
    runParallel(...args) {
      return Promise.all(_handlers.map((fn) => {
        return fn(...args);
      }));
    }
  };
};

// node_modules/@strapi/admin/dist/admin/admin/src/translations/languageNativeNames.mjs
var languageNativeNames = {
  ar: "العربية",
  ca: "Català",
  cs: "Čeština",
  de: "Deutsch",
  dk: "Dansk",
  en: "English",
  "en-GB": "English (United Kingdom)",
  es: "Español",
  eu: "Euskara",
  uz: "O`zbekcha",
  ro: "Română",
  fr: "Français",
  gu: "Gujarati",
  he: "עברית",
  hu: "Magyar",
  id: "Indonesian",
  it: "Italiano",
  ja: "日本語",
  ko: "한국어",
  ml: "Malayalam",
  ms: "Melayu",
  nl: "Nederlands",
  no: "Norwegian",
  pl: "Polski",
  "pt-BR": "Português (Brasil)",
  pt: "Português (Portugal)",
  ru: "Русский",
  sk: "Slovenčina",
  sv: "Swedish",
  th: "ไทย",
  tr: "Türkçe",
  uk: "Українська",
  vi: "Tiếng Việt",
  "zh-Hans": "中文 (简体)",
  zh: "中文 (繁體)",
  sa: "संस्कृत",
  hi: "हिन्दी"
};

// node_modules/@strapi/admin/dist/admin/admin/src/StrapiApp.mjs
function __variableDynamicImportRuntime1__(path) {
  switch (path) {
    case "./translations/ar.json":
      return import("./ar.json-2GIGG7UX.js");
    case "./translations/ca.json":
      return import("./ca.json-MOLPDGE7.js");
    case "./translations/cs.json":
      return import("./cs.json-NLYIZIFN.js");
    case "./translations/de.json":
      return import("./de.json-3UNT74EP.js");
    case "./translations/dk.json":
      return import("./dk.json-6OBWELMR.js");
    case "./translations/en.json":
      return import("./en.json-EDF56CPT.js");
    case "./translations/es.json":
      return import("./es.json-RYQMXOUR.js");
    case "./translations/eu.json":
      return import("./eu.json-SAKEWMWA.js");
    case "./translations/fr.json":
      return import("./fr.json-CG24YLCD.js");
    case "./translations/gu.json":
      return import("./gu.json-EO2GIQID.js");
    case "./translations/he.json":
      return import("./he.json-VZB4I3FT.js");
    case "./translations/hi.json":
      return import("./hi.json-UERK2VXE.js");
    case "./translations/hu.json":
      return import("./hu.json-5OKKJ7RC.js");
    case "./translations/id.json":
      return import("./id.json-GVRRKDG7.js");
    case "./translations/it.json":
      return import("./it.json-DH2UR2AM.js");
    case "./translations/ja.json":
      return import("./ja.json-W3KU6X2O.js");
    case "./translations/ko.json":
      return import("./ko.json-G2XUQFSS.js");
    case "./translations/ml.json":
      return import("./ml.json-KWMY42MA.js");
    case "./translations/ms.json":
      return import("./ms.json-3Z4426VX.js");
    case "./translations/nl.json":
      return import("./nl.json-EKKOHRM2.js");
    case "./translations/no.json":
      return import("./no.json-AGXVR7HE.js");
    case "./translations/pl.json":
      return import("./pl.json-TPY472UD.js");
    case "./translations/pt-BR.json":
      return import("./pt-BR.json-PE2O2NB5.js");
    case "./translations/pt.json":
      return import("./pt.json-FWKE762M.js");
    case "./translations/ru.json":
      return import("./ru.json-V3LIQVZ4.js");
    case "./translations/sa.json":
      return import("./sa.json-XETW5ZUF.js");
    case "./translations/sk.json":
      return import("./sk.json-IC3GPYUO.js");
    case "./translations/sv.json":
      return import("./sv.json-FOM4PBBI.js");
    case "./translations/th.json":
      return import("./th.json-FY5UDF3C.js");
    case "./translations/tr.json":
      return import("./tr.json-NLVL37ZT.js");
    case "./translations/uk.json":
      return import("./uk.json-SXBWOQYX.js");
    case "./translations/vi.json":
      return import("./vi.json-KMBMC32Z.js");
    case "./translations/zh-Hans.json":
      return import("./zh-Hans.json-WM6UZJRM.js");
    case "./translations/zh.json":
      return import("./zh.json-AOAZL3X5.js");
    default:
      return new Promise(function(resolve, reject) {
        (typeof queueMicrotask === "function" ? queueMicrotask : setTimeout)(
          reject.bind(null, new Error("Unknown variable dynamic import: " + path))
        );
      });
  }
}
function __variableDynamicImportRuntime0__(path) {
  switch (path) {
    case "./translations/en-GB.js":
      return import("./en-GB-YRN4GBLM.js");
    default:
      return new Promise(function(resolve, reject) {
        (typeof queueMicrotask === "function" ? queueMicrotask : setTimeout)(
          reject.bind(null, new Error("Unknown variable dynamic import: " + path))
        );
      });
  }
}
var { INJECT_COLUMN_IN_TABLE, MUTATE_COLLECTION_TYPES_LINKS, MUTATE_EDIT_VIEW_LAYOUT, MUTATE_SINGLE_TYPES_LINKS } = HOOKS;
var StrapiApp = class {
  async bootstrap(customBootstrap) {
    Object.keys(this.appPlugins).forEach((plugin) => {
      const bootstrap = this.appPlugins[plugin].bootstrap;
      if (bootstrap) {
        bootstrap({
          addSettingsLink: this.addSettingsLink,
          addSettingsLinks: this.addSettingsLinks,
          getPlugin: this.getPlugin,
          registerHook: this.registerHook
        });
      }
    });
    if ((0, import_isFunction.default)(customBootstrap)) {
      customBootstrap({
        addComponents: this.addComponents,
        addFields: this.addFields,
        addMenuLink: this.addMenuLink,
        addReducers: this.addReducers,
        addSettingsLink: this.addSettingsLink,
        addSettingsLinks: this.addSettingsLinks,
        getPlugin: this.getPlugin,
        registerHook: this.registerHook
      });
    }
  }
  async register(customRegister) {
    Object.keys(this.appPlugins).forEach((plugin) => {
      this.appPlugins[plugin].register(this);
    });
    if ((0, import_isFunction.default)(customRegister)) {
      customRegister(this);
    }
  }
  async loadAdminTrads() {
    const adminLocales = await Promise.all(this.configurations.locales.map(async (locale) => {
      try {
        const { default: data } = await __variableDynamicImportRuntime0__(`./translations/${locale}.js`);
        return {
          data,
          locale
        };
      } catch {
        try {
          const { default: data } = await __variableDynamicImportRuntime1__(`./translations/${locale}.json`);
          return {
            data,
            locale
          };
        } catch {
          return {
            data: null,
            locale
          };
        }
      }
    }));
    return adminLocales.reduce((acc, current) => {
      if (current.data) {
        acc[current.locale] = current.data;
      }
      return acc;
    }, {});
  }
  /**
  * Load the application's translations and merged the custom translations
  * with the default ones.
  */
  async loadTrads(customTranslations = {}) {
    const adminTranslations = await this.loadAdminTrads();
    const arrayOfPromises = Object.keys(this.appPlugins).map((plugin) => {
      const registerTrads = this.appPlugins[plugin].registerTrads;
      if (registerTrads) {
        return registerTrads({
          locales: this.configurations.locales
        });
      }
      return null;
    }).filter((a) => a);
    const pluginsTrads = await Promise.all(arrayOfPromises);
    const mergedTrads = pluginsTrads.reduce((acc, currentPluginTrads) => {
      const pluginTrads = currentPluginTrads.reduce((acc1, current) => {
        acc1[current.locale] = current.data;
        return acc1;
      }, {});
      Object.keys(pluginTrads).forEach((locale) => {
        acc[locale] = {
          ...acc[locale],
          ...pluginTrads[locale]
        };
      });
      return acc;
    }, {});
    const translations = this.configurations.locales.reduce((acc, current) => {
      acc[current] = {
        ...adminTranslations[current],
        ...mergedTrads[current] || {},
        ...customTranslations[current] ?? {}
      };
      return acc;
    }, {});
    this.configurations.translations = translations;
    return Promise.resolve();
  }
  render() {
    const localeNames = (0, import_pick.default)(languageNativeNames, this.configurations.locales || []);
    const locale = localStorage.getItem(LANGUAGE_LOCAL_STORAGE_KEY) || "en";
    this.store = configureStoreImpl({
      admin_app: {
        permissions: (0, import_merge.default)({}, ADMIN_PERMISSIONS_CE, ADMIN_PERMISSIONS_EE),
        theme: {
          availableThemes: [],
          currentTheme: localStorage.getItem(THEME_LOCAL_STORAGE_KEY) || "system"
        },
        language: {
          locale: localeNames[locale] ? locale : "en",
          localeNames
        },
        token: getStoredToken()
      }
    }, this.middlewares, this.reducers);
    const router = this.router.createRouter(this, {
      basename: getBasename()
    });
    return (0, import_jsx_runtime15.jsx)(RouterProvider, {
      router
    });
  }
  constructor({ config, appPlugins } = {}) {
    this.plugins = {};
    this.hooksDict = {};
    this.admin = {
      injectionZones: {}
    };
    this.translations = {};
    this.configurations = {
      authLogo: img,
      head: {
        favicon: ""
      },
      locales: [
        "en"
      ],
      menuLogo: img,
      notifications: {
        releases: true
      },
      themes: {
        light: lightTheme,
        dark: darkTheme
      },
      translations: {},
      tutorials: true
    };
    this.rbac = new RBAC();
    this.library = {
      components: {},
      fields: {}
    };
    this.middlewares = [];
    this.reducers = {};
    this.store = null;
    this.customFields = new CustomFields();
    this.widgets = new Widgets();
    this.addComponents = (components) => {
      if (Array.isArray(components)) {
        components.map((comp) => {
          (0, import_invariant4.default)(comp.Component, "A Component must be provided");
          (0, import_invariant4.default)(comp.name, "A type must be provided");
          this.library.components[comp.name] = comp.Component;
        });
      } else {
        (0, import_invariant4.default)(components.Component, "A Component must be provided");
        (0, import_invariant4.default)(components.name, "A type must be provided");
        this.library.components[components.name] = components.Component;
      }
    };
    this.addFields = (fields) => {
      if (Array.isArray(fields)) {
        fields.map((field) => {
          (0, import_invariant4.default)(field.Component, "A Component must be provided");
          (0, import_invariant4.default)(field.type, "A type must be provided");
          this.library.fields[field.type] = field.Component;
        });
      } else {
        (0, import_invariant4.default)(fields.Component, "A Component must be provided");
        (0, import_invariant4.default)(fields.type, "A type must be provided");
        this.library.fields[fields.type] = fields.Component;
      }
    };
    this.addMiddlewares = (middlewares) => {
      middlewares.forEach((middleware) => {
        this.middlewares.push(middleware);
      });
    };
    this.addRBACMiddleware = (m) => {
      if (Array.isArray(m)) {
        this.rbac.use(m);
      } else {
        this.rbac.use(m);
      }
    };
    this.addReducers = (reducers) => {
      Object.entries(reducers).forEach(([name, reducer2]) => {
        this.reducers[name] = reducer2;
      });
    };
    this.addMenuLink = (link) => this.router.addMenuLink(link);
    this.addSettingsLinks = (sectionId, links) => {
      (0, import_invariant4.default)(Array.isArray(links), "TypeError expected links to be an array");
      this.router.addSettingsLink(sectionId, links);
    };
    this.createSettingSection = (section, links) => this.router.addSettingsLink(section, links);
    this.addSettingsLink = (sectionId, link) => {
      this.router.addSettingsLink(sectionId, link);
    };
    this.createCustomConfigurations = (customConfig) => {
      var _a, _b, _c, _d, _e;
      if (customConfig.locales) {
        this.configurations.locales = [
          "en",
          ...((_a = customConfig.locales) == null ? void 0 : _a.filter((loc) => loc !== "en")) || []
        ];
      }
      if ((_b = customConfig.auth) == null ? void 0 : _b.logo) {
        this.configurations.authLogo = customConfig.auth.logo;
      }
      if ((_c = customConfig.menu) == null ? void 0 : _c.logo) {
        this.configurations.menuLogo = customConfig.menu.logo;
      }
      if ((_d = customConfig.head) == null ? void 0 : _d.favicon) {
        this.configurations.head.favicon = customConfig.head.favicon;
      }
      if (customConfig.theme) {
        const darkTheme2 = customConfig.theme.dark;
        const lightTheme2 = customConfig.theme.light;
        if (!darkTheme2 && !lightTheme2) {
          console.warn(`[deprecated] In future versions, Strapi will stop supporting this theme customization syntax. The theme configuration accepts a light and a dark key to customize each theme separately. See https://docs.strapi.io/developer-docs/latest/development/admin-customization.html#theme-extension.`.trim());
          (0, import_merge.default)(this.configurations.themes.light, customConfig.theme);
        }
        if (lightTheme2) (0, import_merge.default)(this.configurations.themes.light, lightTheme2);
        if (darkTheme2) (0, import_merge.default)(this.configurations.themes.dark, darkTheme2);
      }
      if (((_e = customConfig.notifications) == null ? void 0 : _e.releases) !== void 0) {
        this.configurations.notifications.releases = customConfig.notifications.releases;
      }
      if (customConfig.tutorials !== void 0) {
        this.configurations.tutorials = customConfig.tutorials;
      }
    };
    this.createHook = (name) => {
      this.hooksDict[name] = createHook();
    };
    this.getAdminInjectedComponents = (moduleName, containerName, blockName) => {
      try {
        return this.admin.injectionZones[moduleName][containerName][blockName] || [];
      } catch (err) {
        console.error("Cannot get injected component", err);
        return [];
      }
    };
    this.getPlugin = (pluginId) => this.plugins[pluginId];
    this.registerHook = (name, fn) => {
      (0, import_invariant4.default)(this.hooksDict[name], `The hook ${name} is not defined. You are trying to register a hook that does not exist in the application.`);
      this.hooksDict[name].register(fn);
    };
    this.registerPlugin = (pluginConf) => {
      const plugin = new Plugin(pluginConf);
      this.plugins[plugin.pluginId] = plugin;
    };
    this.runHookSeries = (name, asynchronous = false) => asynchronous ? this.hooksDict[name].runSeriesAsync() : this.hooksDict[name].runSeries();
    this.runHookWaterfall = (name, initialValue, store) => {
      return this.hooksDict[name].runWaterfall(initialValue, store);
    };
    this.runHookParallel = (name) => this.hooksDict[name].runParallel();
    this.appPlugins = appPlugins || {};
    this.createCustomConfigurations(config ?? {});
    this.createHook(INJECT_COLUMN_IN_TABLE);
    this.createHook(MUTATE_COLLECTION_TYPES_LINKS);
    this.createHook(MUTATE_SINGLE_TYPES_LINKS);
    this.createHook(MUTATE_EDIT_VIEW_LAYOUT);
    this.router = new Router(getInitialRoutes());
  }
};

// node_modules/@strapi/admin/dist/admin/admin/src/render.mjs
var renderAdmin = async (mountNode, { plugins, customisations, features }) => {
  var _a, _b;
  if (!mountNode) {
    throw new Error("[@strapi/admin]: Could not find the root element to mount the admin app");
  }
  window.strapi = {
    /**
    * This ENV variable is passed from the strapi instance, by default no url is set
    * in the config and therefore the instance returns you an empty string so URLs are relative.
    *
    * To ensure that the backendURL is always set, we use the window.location.origin as a fallback.
    */
    backendURL: createAbsoluteUrl(process.env.STRAPI_ADMIN_BACKEND_URL),
    isEE: false,
    isTrial: false,
    telemetryDisabled: process.env.STRAPI_TELEMETRY_DISABLED === "true",
    future: {
      isEnabled: (name) => {
        var _a2;
        return ((_a2 = features == null ? void 0 : features.future) == null ? void 0 : _a2[name]) === true;
      }
    },
    // @ts-expect-error – there's pollution from the global scope of Node.
    features: {
      SSO: "sso",
      AUDIT_LOGS: "audit-logs",
      REVIEW_WORKFLOWS: "review-workflows",
      /**
      * If we don't get the license then we know it's not EE
      * so no feature is enabled.
      */
      isEnabled: () => false
    },
    projectType: "Community",
    flags: {
      nps: false,
      promoteEE: true
    }
  };
  const { get } = getFetchClient();
  try {
    const { data: { data: { isEE, isTrial, features: features2, flags } } } = await get("/admin/project-type");
    window.strapi.isEE = isEE;
    window.strapi.isTrialLicense = isTrial;
    window.strapi.flags = flags;
    window.strapi.features = {
      ...window.strapi.features,
      isEnabled: (featureName) => features2.some((feature) => feature.name === featureName)
    };
    window.strapi.projectType = isEE ? "Enterprise" : "Community";
  } catch (err) {
    console.error(err);
  }
  const app = new StrapiApp({
    config: customisations == null ? void 0 : customisations.config,
    appPlugins: plugins
  });
  await app.register(customisations == null ? void 0 : customisations.register);
  await app.bootstrap(customisations == null ? void 0 : customisations.bootstrap);
  await app.loadTrads((_a = customisations == null ? void 0 : customisations.config) == null ? void 0 : _a.translations);
  (0, import_client.createRoot)(mountNode).render(app.render());
  if (typeof module !== "undefined" && module && "hot" in module && typeof module.hot === "object" && module.hot !== null && "accept" in module.hot && typeof module.hot.accept === "function") {
    module.hot.accept();
  }
  if (typeof ((_b = import.meta.hot) == null ? void 0 : _b.accept) === "function") {
    import.meta.hot.accept();
  }
};

// node_modules/@strapi/admin/dist/admin/admin/src/components/DescriptionComponentRenderer.mjs
var import_jsx_runtime16 = __toESM(require_jsx_runtime(), 1);
var React7 = __toESM(require_react(), 1);
var import_isEqual = __toESM(require_isEqual(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useForceUpdate.mjs
var React6 = __toESM(require_react(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useIsMounted.mjs
var React5 = __toESM(require_react(), 1);
var useIsMounted = () => {
  const isMounted = React5.useRef(false);
  React5.useLayoutEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);
  return isMounted;
};

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useForceUpdate.mjs
var useForceUpdate = () => {
  const [tick, update] = React6.useState();
  const isMounted = useIsMounted();
  const forceUpdate = React6.useCallback(() => {
    if (isMounted.current) {
      update(Math.random());
    }
  }, [
    isMounted,
    update
  ]);
  return [
    tick,
    forceUpdate
  ];
};

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useThrottledCallback.mjs
var import_react7 = __toESM(require_react(), 1);
var import_throttle = __toESM(require_throttle(), 1);
var useThrottledCallback = (callback, wait, options) => {
  const throttledCallback = (0, import_react7.useMemo)(() => (0, import_throttle.default)(callback, wait, options), [
    callback,
    options,
    wait
  ]);
  return throttledCallback;
};

// node_modules/@strapi/admin/dist/admin/admin/src/utils/shims.mjs
var requestIdleCallbackShim = (callback) => {
  const start = Date.now();
  return setTimeout(() => {
    callback({
      didTimeout: false,
      timeRemaining() {
        return Math.max(0, Date.now() - start);
      }
    });
  }, 1);
};
var _requestIdleCallback = typeof requestIdleCallback === "undefined" ? requestIdleCallbackShim : requestIdleCallback;
var cancelIdleCallbackShim = (handle) => {
  return clearTimeout(handle);
};
var _cancelIdleCallback = typeof cancelIdleCallback === "undefined" ? cancelIdleCallbackShim : cancelIdleCallback;

// node_modules/@strapi/admin/dist/admin/admin/src/components/DescriptionComponentRenderer.mjs
var DescriptionComponentRenderer = ({ children, props, descriptions }) => {
  const statesRef = React7.useRef({});
  const [tick, forceUpdate] = useForceUpdate();
  const requestHandle = React7.useRef(null);
  const requestUpdate = React7.useCallback(() => {
    if (requestHandle.current) {
      _cancelIdleCallback(requestHandle.current);
    }
    requestHandle.current = _requestIdleCallback(() => {
      requestHandle.current = null;
      forceUpdate();
    });
  }, [
    forceUpdate
  ]);
  const throttledRequestUpdate = useThrottledCallback(requestUpdate, 60, {
    trailing: true
  });
  const update = React7.useCallback((id, description) => {
    if (description === null) {
      delete statesRef.current[id];
    } else {
      const current = statesRef.current[id];
      statesRef.current[id] = {
        ...current,
        value: {
          ...description,
          id
        }
      };
    }
    throttledRequestUpdate();
  }, [
    throttledRequestUpdate
  ]);
  const ids2 = React7.useMemo(() => descriptions.map((description) => getCompId(description)), [
    descriptions
  ]);
  const states = React7.useMemo(
    () => ids2.map((id) => {
      var _a;
      return (_a = statesRef.current[id]) == null ? void 0 : _a.value;
    }).filter((state) => state !== null && state !== void 0),
    /**
    * we leave tick in the deps to ensure the memo is recalculated when the `update` function  is called.
    * the `ids` will most likely be stable unless we get new actions, but we can't respond to the Description
    * Component changing the ref data in any other way.
    */
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      ids2,
      tick
    ]
  );
  return (0, import_jsx_runtime16.jsxs)(import_jsx_runtime16.Fragment, {
    children: [
      descriptions.map((description) => {
        const key = getCompId(description);
        return (0, import_jsx_runtime16.jsx)(Description, {
          id: key,
          description,
          props,
          update
        }, key);
      }),
      children(states)
    ]
  });
};
var Description = React7.memo(({ description, id, props, update }) => {
  const comp = description(props);
  useShallowCompareEffect(() => {
    update(id, comp);
    return () => {
      update(id, null);
    };
  }, comp);
  return null;
}, (prev, next) => (0, import_isEqual.default)(prev.props, next.props));
var ids = /* @__PURE__ */ new WeakMap();
var counter = 0;
function getCompId(comp) {
  const cachedId = ids.get(comp);
  if (cachedId) return cachedId;
  const id = `${comp.name || comp.displayName || "<anonymous>"}-${counter++}`;
  ids.set(comp, id);
  return id;
}
var useShallowCompareMemoize = (value) => {
  const ref = React7.useRef(void 0);
  if (!(0, import_isEqual.default)(value, ref.current)) {
    ref.current = value;
  }
  return [
    ref.current
  ];
};
var useShallowCompareEffect = (callback, dependencies) => {
  React7.useEffect(callback, useShallowCompareMemoize(dependencies));
};

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useInjectReducer.mjs
var import_react8 = __toESM(require_react(), 1);
function useInjectReducer(namespace, reducer2) {
  const store = useTypedStore();
  (0, import_react8.useEffect)(() => {
    store.injectReducer(namespace, reducer2);
  }, [
    store,
    namespace,
    reducer2
  ]);
}

export {
  renderAdmin,
  require_debounce,
  require_throttle,
  DescriptionComponentRenderer,
  useInjectReducer
};
//# sourceMappingURL=chunk-EKXSMIUH.js.map
