{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/services/components.ts", "../../../@strapi/content-manager/admin/src/pages/ComponentConfigurationPage.tsx"], "sourcesContent": ["import { contentManagerApi } from './api';\n\nimport type {\n  FindComponentConfiguration,\n  UpdateComponentConfiguration,\n} from '../../../shared/contracts/components';\n\nconst componentsApi = contentManagerApi.injectEndpoints({\n  endpoints: (builder) => ({\n    getComponentConfiguration: builder.query<\n      FindComponentConfiguration.Response['data'],\n      FindComponentConfiguration.Params['uid']\n    >({\n      query: (uid) => `/content-manager/components/${uid}/configuration`,\n      transformResponse: (response: FindComponentConfiguration.Response) => response.data,\n      providesTags: (_result, _error, uid) => [{ type: 'ComponentConfiguration', id: uid }],\n    }),\n    updateComponentConfiguration: builder.mutation({\n      query: ({ uid, ...body }) => ({\n        url: `/content-manager/components/${uid}/configuration`,\n        method: 'PUT',\n        data: body,\n      }),\n      transformResponse: (response: UpdateComponentConfiguration.Response) => response.data,\n      invalidatesTags: (_result, _error, { uid }) => [\n        { type: 'ComponentConfiguration', id: uid },\n        // otherwise layouts already fetched will have stale component configuration data.\n        { type: 'ContentTypeSettings', id: 'LIST' },\n      ],\n    }),\n  }),\n});\n\nconst { useGetComponentConfigurationQuery, useUpdateComponentConfigurationMutation } =\n  componentsApi;\n\nexport { useGetComponentConfigurationQuery, useUpdateComponentConfigurationMutation };\n", "import * as React from 'react';\n\nimport { Page, useNotification, useAP<PERSON><PERSON>r<PERSON>and<PERSON> } from '@strapi/admin/strapi-admin';\nimport { useIntl } from 'react-intl';\nimport { useParams } from 'react-router-dom';\n\nimport { TEMP_FIELD_NAME } from '../components/ConfigurationForm/Fields';\nimport { ConfigurationForm, ConfigurationFormProps } from '../components/ConfigurationForm/Form';\nimport { ComponentsDictionary, extractContentTypeComponents } from '../hooks/useContentTypeSchema';\nimport {\n  DEFAULT_SETTINGS,\n  EditLayout,\n  convertEditLayoutToFieldLayouts,\n} from '../hooks/useDocumentLayout';\nimport { useTypedSelector } from '../modules/hooks';\nimport {\n  useGetComponentConfigurationQuery,\n  useUpdateComponentConfigurationMutation,\n} from '../services/components';\nimport { useGetInitialDataQuery } from '../services/init';\nimport { setIn } from '../utils/objects';\n\nimport type { Component, FindComponentConfiguration } from '../../../shared/contracts/components';\nimport type { Metadatas } from '../../../shared/contracts/content-types';\n\n/* -------------------------------------------------------------------------------------------------\n * ComponentConfigurationPage\n * -----------------------------------------------------------------------------------------------*/\n\nconst ComponentConfigurationPage = () => {\n  /**\n   * useDocumentLayout only works for documents, not components,\n   * it feels weird to make that hook work for both when this is SUCH\n   * a unique use case and we only do it here, so in short, we essentially\n   * just extracted the logic to make an edit view layout and reproduced it here.\n   */\n  const { slug: model } = useParams<{ slug: string }>();\n  const { toggleNotification } = useNotification();\n  const { formatMessage } = useIntl();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n\n  const {\n    components,\n    fieldSizes,\n    schema,\n    error: errorSchema,\n    isLoading: isLoadingSchema,\n    isFetching: isFetchingSchema,\n  } = useGetInitialDataQuery(undefined, {\n    selectFromResult: (res) => {\n      const schema = res.data?.components.find((ct) => ct.uid === model);\n\n      const componentsByKey = res.data?.components.reduce<ComponentsDictionary>(\n        (acc, component) => {\n          acc[component.uid] = component;\n\n          return acc;\n        },\n        {}\n      );\n\n      const components = extractContentTypeComponents(schema?.attributes, componentsByKey);\n\n      const fieldSizes = Object.entries(res.data?.fieldSizes ?? {}).reduce<\n        ConfigurationFormProps['fieldSizes']\n      >((acc, [attributeName, { default: size }]) => {\n        acc[attributeName] = size;\n\n        return acc;\n      }, {});\n\n      return {\n        isFetching: res.isFetching,\n        isLoading: res.isLoading,\n        error: res.error,\n        components,\n        schema,\n        fieldSizes,\n      };\n    },\n  });\n\n  React.useEffect(() => {\n    if (errorSchema) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(errorSchema),\n      });\n    }\n  }, [errorSchema, formatAPIError, toggleNotification]);\n\n  const {\n    data,\n    isLoading: isLoadingConfig,\n    isFetching: isFetchingConfig,\n    error,\n  } = useGetComponentConfigurationQuery(model ?? '');\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    }\n  }, [error, formatAPIError, toggleNotification]);\n\n  /**\n   * you **must** check if we're loading or fetching in case the component gets new props\n   * but nothing was unmounted, it then becomes a fetch, not a load.\n   */\n  const isLoading = isLoadingConfig || isLoadingSchema || isFetchingConfig || isFetchingSchema;\n\n  const editLayout = React.useMemo(\n    () =>\n      data && !isLoading\n        ? formatEditLayout(data, { schema, components })\n        : ({\n            layout: [],\n            components: {},\n            metadatas: {},\n            options: {},\n            settings: DEFAULT_SETTINGS,\n          } as EditLayout),\n    [data, isLoading, schema, components]\n  );\n\n  const [updateConfiguration] = useUpdateComponentConfigurationMutation();\n  const handleSubmit: ConfigurationFormProps['onSubmit'] = async (formData) => {\n    try {\n      /**\n       * We reconstruct the metadatas object by taking the existing list metadatas\n       * and re-merging that by attribute name with the current list metadatas, whilst overwriting\n       * the data from the form we've built.\n       */\n      const meta = Object.entries(data?.component.metadatas ?? {}).reduce<Metadatas>(\n        (acc, [name, { edit, list }]) => {\n          const {\n            __temp_key__,\n            size: _size,\n            name: _name,\n            ...editedMetadata\n          } = formData.layout.flatMap((row) => row.children).find((field) => field.name === name) ??\n          {};\n\n          acc[name] = {\n            edit: {\n              ...edit,\n              ...editedMetadata,\n            },\n            list,\n          };\n\n          return acc;\n        },\n        {}\n      );\n\n      const res = await updateConfiguration({\n        layouts: {\n          edit: formData.layout.map((row) =>\n            row.children.reduce<Array<{ name: string; size: number }>>((acc, { name, size }) => {\n              if (name !== TEMP_FIELD_NAME) {\n                return [...acc, { name, size }];\n              }\n\n              return acc;\n            }, [])\n          ),\n          list: data?.component.layouts.list,\n        },\n        settings: setIn(formData.settings, 'displayName', undefined),\n        metadatas: meta,\n        uid: model,\n      });\n\n      if ('data' in res) {\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({ id: 'notification.success.saved', defaultMessage: 'Saved' }),\n        });\n      } else {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(res.error),\n        });\n      }\n    } catch {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occurred' }),\n      });\n    }\n  };\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  if (error || errorSchema || !schema) {\n    return <Page.Error />;\n  }\n\n  return (\n    <>\n      <Page.Title>{`Configure ${editLayout.settings.displayName} Edit View`}</Page.Title>\n      <ConfigurationForm\n        onSubmit={handleSubmit}\n        attributes={schema.attributes}\n        fieldSizes={fieldSizes}\n        layout={editLayout}\n      />\n    </>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Header\n * -----------------------------------------------------------------------------------------------*/\n\nconst formatEditLayout = (\n  data: FindComponentConfiguration.Response['data'],\n  { schema, components }: { schema?: Component; components: ComponentsDictionary }\n) => {\n  const editAttributes = convertEditLayoutToFieldLayouts(\n    data.component.layouts.edit,\n    schema?.attributes,\n    data.component.metadatas,\n    { configurations: data.components, schemas: components }\n  );\n\n  const componentEditAttributes = Object.entries(data.components).reduce<EditLayout['components']>(\n    (acc, [uid, configuration]) => {\n      acc[uid] = {\n        layout: convertEditLayoutToFieldLayouts(\n          configuration.layouts.edit,\n          components[uid].attributes,\n          configuration.metadatas\n        ),\n        settings: {\n          ...configuration.settings,\n          icon: components[uid].info.icon,\n          displayName: components[uid].info.displayName,\n        },\n      };\n      return acc;\n    },\n    {}\n  );\n\n  const editMetadatas = Object.entries(data.component.metadatas).reduce<EditLayout['metadatas']>(\n    (acc, [attribute, metadata]) => {\n      return {\n        ...acc,\n        [attribute]: metadata.edit,\n      };\n    },\n    {}\n  );\n\n  return {\n    layout: [editAttributes],\n    components: componentEditAttributes,\n    metadatas: editMetadatas,\n    options: {\n      ...schema?.options,\n      ...schema?.pluginOptions,\n    },\n    settings: {\n      ...data.component.settings,\n      displayName: schema?.info.displayName,\n    },\n  };\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Header\n * -----------------------------------------------------------------------------------------------*/\n\nconst ProtectedComponentConfigurationPage = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.contentManager?.componentsConfigurations\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <ComponentConfigurationPage />\n    </Page.Protect>\n  );\n};\n\nexport { ComponentConfigurationPage, ProtectedComponentConfigurationPage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAMA,gBAAgBC,kBAAkBC,gBAAgB;EACtDC,WAAW,CAACC,aAAa;IACvBC,2BAA2BD,QAAQE,MAGjC;MACAA,OAAO,CAACC,QAAQ,+BAA+BA,GAAAA;MAC/CC,mBAAmB,CAACC,aAAkDA,SAASC;MAC/EC,cAAc,CAACC,SAASC,QAAQN,QAAQ;QAAC;UAAEO,MAAM;UAA0BC,IAAIR;QAAI;MAAE;IACvF,CAAA;IACAS,8BAA8BZ,QAAQa,SAAS;MAC7CX,OAAO,CAAC,EAAEC,KAAK,GAAGW,KAAAA,OAAY;QAC5BC,KAAK,+BAA+BZ,GAAAA;QACpCa,QAAQ;QACRV,MAAMQ;;MAERV,mBAAmB,CAACC,aAAoDA,SAASC;MACjFW,iBAAiB,CAACT,SAASC,QAAQ,EAAEN,IAAG,MAAO;QAC7C;UAAEO,MAAM;UAA0BC,IAAIR;QAAI;;QAE1C;UAAEO,MAAM;UAAuBC,IAAI;QAAO;MAC3C;IACH,CAAA;;AAEJ,CAAA;AAEA,IAAM,EAAEO,mCAAmCC,wCAAuC,IAChFvB;;;ACPgG,IAE5FwB,6BAA6B,MAAA;AAOjC,QAAM,EAAEC,MAAMC,MAAK,IAAKC,UAAAA;AACxB,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,yBAAyBC,eAAc,IAAKC,mBAAAA;AAEpD,QAAM,EACJC,YACAC,YACAC,QACAC,OAAOC,aACPC,WAAWC,iBACXC,YAAYC,iBAAgB,IAC1BC,uBAAuBC,QAAW;IACpCC,kBAAkB,CAACC,QAAAA;;AACjB,YAAMV,WAASU,SAAIC,SAAJD,mBAAUZ,WAAWc,KAAK,CAACC,OAAOA,GAAGC,QAAQzB;AAE5D,YAAM0B,mBAAkBL,SAAIC,SAAJD,mBAAUZ,WAAWkB,OAC3C,CAACC,KAAKC,cAAAA;AACJD,YAAIC,UAAUJ,GAAG,IAAII;AAErB,eAAOD;MACT,GACA,CAAA;AAGF,YAAMnB,cAAaqB,6BAA6BnB,WAAAA,gBAAAA,QAAQoB,YAAYL,eAAAA;AAEpE,YAAMhB,cAAasB,OAAOC,UAAQZ,SAAIC,SAAJD,mBAAUX,eAAc,CAAA,CAAC,EAAGiB,OAE5D,CAACC,KAAK,CAACM,eAAe,EAAEC,SAASC,KAAI,CAAE,MAAC;AACxCR,YAAIM,aAAAA,IAAiBE;AAErB,eAAOR;MACT,GAAG,CAAA,CAAC;AAEJ,aAAO;QACLZ,YAAYK,IAAIL;QAChBF,WAAWO,IAAIP;QACfF,OAAOS,IAAIT;QACXH,YAAAA;QACAE,QAAAA;QACAD,YAAAA;MACF;IACF;EACF,CAAA;AAEA2B,EAAMC,gBAAU,MAAA;AACd,QAAIzB,aAAa;AACfX,yBAAmB;QACjBqC,MAAM;QACNC,SAASjC,eAAeM,WAAAA;MAC1B,CAAA;IACF;KACC;IAACA;IAAaN;IAAgBL;EAAmB,CAAA;AAEpD,QAAM,EACJoB,MACAR,WAAW2B,iBACXzB,YAAY0B,kBACZ9B,MAAK,IACH+B,kCAAkC3C,SAAS,EAAA;AAE/CqC,EAAMC,gBAAU,MAAA;AACd,QAAI1B,OAAO;AACTV,yBAAmB;QACjBqC,MAAM;QACNC,SAASjC,eAAeK,KAAAA;MAC1B,CAAA;IACF;KACC;IAACA;IAAOL;IAAgBL;EAAmB,CAAA;AAM9C,QAAMY,YAAY2B,mBAAmB1B,mBAAmB2B,oBAAoBzB;AAE5E,QAAM2B,aAAmBC,cACvB,MACEvB,QAAQ,CAACR,YACLgC,iBAAiBxB,MAAM;IAAEX;IAAQF;GAChC,IAAA;IACCsC,QAAQ,CAAA;IACRtC,YAAY,CAAA;IACZuC,WAAW,CAAA;IACXC,SAAS,CAAA;IACTC,UAAUC;KAElB;IAAC7B;IAAMR;IAAWH;IAAQF;EAAW,CAAA;AAGvC,QAAM,CAAC2C,mBAAAA,IAAuBC,wCAAAA;AAC9B,QAAMC,eAAmD,OAAOC,aAAAA;AAC9D,QAAI;AAMF,YAAMC,OAAOxB,OAAOC,SAAQX,6BAAMO,UAAUmB,cAAa,CAAA,CAAC,EAAGrB,OAC3D,CAACC,KAAK,CAAC6B,MAAM,EAAEC,MAAMC,KAAI,CAAE,MAAC;AAC1B,cAAM,EACJC,cACAxB,MAAMyB,OACNJ,MAAMK,OACN,GAAGC,eACJ,IAAGR,SAASR,OAAOiB,QAAQ,CAACC,QAAQA,IAAIC,QAAQ,EAAE3C,KAAK,CAAC4C,UAAUA,MAAMV,SAASA,IAAAA,KAClF,CAAA;AAEA7B,YAAI6B,IAAAA,IAAQ;UACVC,MAAM;YACJ,GAAGA;YACH,GAAGK;UACL;UACAJ;QACF;AAEA,eAAO/B;MACT,GACA,CAAA,CAAC;AAGH,YAAMP,MAAM,MAAM+B,oBAAoB;QACpCgB,SAAS;UACPV,MAAMH,SAASR,OAAOsB,IAAI,CAACJ,QACzBA,IAAIC,SAASvC,OAA8C,CAACC,KAAK,EAAE6B,MAAMrB,KAAI,MAAE;AAC7E,gBAAIqB,SAASa,iBAAiB;AAC5B,qBAAO;gBAAI1C,GAAAA;gBAAK;kBAAE6B;kBAAMrB;gBAAK;cAAE;YACjC;AAEA,mBAAOR;UACT,GAAG,CAAA,CAAE,CAAA;UAEP+B,MAAMrC,6BAAMO,UAAUuC,QAAQT;QAChC;QACAT,UAAUqB,MAAMhB,SAASL,UAAU,eAAe/B,MAAAA;QAClD6B,WAAWQ;QACX/B,KAAKzB;MACP,CAAA;AAEA,UAAI,UAAUqB,KAAK;AACjBnB,2BAAmB;UACjBqC,MAAM;UACNC,SAASpC,cAAc;YAAEoE,IAAI;YAA8BC,gBAAgB;UAAQ,CAAA;QACrF,CAAA;aACK;AACLvE,2BAAmB;UACjBqC,MAAM;UACNC,SAASjC,eAAec,IAAIT,KAAK;QACnC,CAAA;MACF;IACF,QAAQ;AACNV,yBAAmB;QACjBqC,MAAM;QACNC,SAASpC,cAAc;UAAEoE,IAAI;UAAsBC,gBAAgB;QAAoB,CAAA;MACzF,CAAA;IACF;EACF;AAEA,MAAI3D,WAAW;AACb,eAAO4D,wBAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,MAAIhE,SAASC,eAAe,CAACF,QAAQ;AACnC,eAAO+D,wBAACC,KAAKE,OAAK,CAAA,CAAA;EACpB;AAEA,aACEC,yBAAAC,6BAAA;;UACEL,wBAACC,KAAKK,OAAK;kBAAE,aAAapC,WAAWM,SAAS+B,WAAW;;UACzDP,wBAACQ,mBAAAA;QACCC,UAAU7B;QACVvB,YAAYpB,OAAOoB;QACnBrB;QACAqC,QAAQH;;;;AAIhB;AAMA,IAAME,mBAAmB,CACvBxB,MACA,EAAEX,QAAQF,WAAU,MAA4D;AAEhF,QAAM2E,iBAAiBC,gCACrB/D,KAAKO,UAAUuC,QAAQV,MACvB/C,iCAAQoB,YACRT,KAAKO,UAAUmB,WACf;IAAEsC,gBAAgBhE,KAAKb;IAAY8E,SAAS9E;EAAW,CAAA;AAGzD,QAAM+E,0BAA0BxD,OAAOC,QAAQX,KAAKb,UAAU,EAAEkB,OAC9D,CAACC,KAAK,CAACH,KAAKgE,aAAc,MAAA;AACxB7D,QAAIH,GAAAA,IAAO;MACTsB,QAAQsC,gCACNI,cAAcrB,QAAQV,MACtBjD,WAAWgB,GAAI,EAACM,YAChB0D,cAAczC,SAAS;MAEzBE,UAAU;QACR,GAAGuC,cAAcvC;QACjBwC,MAAMjF,WAAWgB,GAAAA,EAAKkE,KAAKD;QAC3BT,aAAaxE,WAAWgB,GAAAA,EAAKkE,KAAKV;MACpC;IACF;AACA,WAAOrD;EACT,GACA,CAAA,CAAC;AAGH,QAAMgE,gBAAgB5D,OAAOC,QAAQX,KAAKO,UAAUmB,SAAS,EAAErB,OAC7D,CAACC,KAAK,CAACiE,WAAWC,QAAS,MAAA;AACzB,WAAO;MACL,GAAGlE;MACH,CAACiE,SAAAA,GAAYC,SAASpC;IACxB;EACF,GACA,CAAA,CAAC;AAGH,SAAO;IACLX,QAAQ;MAACqC;IAAe;IACxB3E,YAAY+E;IACZxC,WAAW4C;IACX3C,SAAS;MACP,GAAGtC,iCAAQsC;MACX,GAAGtC,iCAAQoF;IACb;IACA7C,UAAU;MACR,GAAG5B,KAAKO,UAAUqB;MAClB+B,aAAatE,iCAAQgF,KAAKV;IAC5B;EACF;AACF;AAIkG,IAE5Fe,sCAAsC,MAAA;AAC1C,QAAMC,cAAcC,iBAClB,CAACC,UAAUA;;AAAAA,uBAAMC,UAAUH,YAAYI,mBAA5BF,mBAA4CG;GAAAA;AAGzD,aACE5B,wBAACC,KAAK4B,SAAO;IAACN;IACZ,cAAAvB,wBAAC5E,4BAAAA,CAAAA,CAAAA;;AAGP;", "names": ["componentsApi", "contentManagerApi", "injectEndpoints", "endpoints", "builder", "getComponentConfiguration", "query", "uid", "transformResponse", "response", "data", "providesTags", "_result", "_error", "type", "id", "updateComponentConfiguration", "mutation", "body", "url", "method", "invalidatesTags", "useGetComponentConfigurationQuery", "useUpdateComponentConfigurationMutation", "ComponentConfigurationPage", "slug", "model", "useParams", "toggleNotification", "useNotification", "formatMessage", "useIntl", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "components", "fieldSizes", "schema", "error", "errorSchema", "isLoading", "isLoadingSchema", "isFetching", "isFetchingSchema", "useGetInitialDataQuery", "undefined", "selectFromResult", "res", "data", "find", "ct", "uid", "componentsByKey", "reduce", "acc", "component", "extractContentTypeComponents", "attributes", "Object", "entries", "attributeName", "default", "size", "React", "useEffect", "type", "message", "isLoadingConfig", "isFetchingConfig", "useGetComponentConfigurationQuery", "editLayout", "useMemo", "formatEditLayout", "layout", "metadatas", "options", "settings", "DEFAULT_SETTINGS", "updateConfiguration", "useUpdateComponentConfigurationMutation", "handleSubmit", "formData", "meta", "name", "edit", "list", "__temp_key__", "_size", "_name", "editedMetadata", "flatMap", "row", "children", "field", "layouts", "map", "TEMP_FIELD_NAME", "setIn", "id", "defaultMessage", "_jsx", "Page", "Loading", "Error", "_jsxs", "_Fragment", "Title", "displayName", "ConfigurationForm", "onSubmit", "editAttributes", "convertEditLayoutToFieldLayouts", "configurations", "schemas", "componentEditAttributes", "configuration", "icon", "info", "editMetadatas", "attribute", "metadata", "pluginOptions", "ProtectedComponentConfigurationPage", "permissions", "useTypedSelector", "state", "admin_app", "contentManager", "componentsConfigurations", "Protect"]}