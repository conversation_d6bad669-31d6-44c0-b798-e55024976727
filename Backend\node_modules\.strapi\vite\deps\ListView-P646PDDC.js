import {
  useDeleteAPITokenMutation,
  useGetAPITokensQuery
} from "./chunk-CN7O7C2E.js";
import {
  Table
} from "./chunk-XMDBZNV3.js";
import {
  useOnce
} from "./chunk-AF2Z2UHO.js";
import {
  API_TOKEN_TYPE
} from "./chunk-7GAX2FTH.js";
import {
  useGuidedTour
} from "./chunk-ROZIXYJG.js";
import "./chunk-NWWGC2Z2.js";
import "./chunk-DD3MYA7D.js";
import "./chunk-O5ZNSDDU.js";
import "./chunk-MBK4V2X7.js";
import "./chunk-SGQJOZK5.js";
import "./chunk-AFM2NWPO.js";
import {
  useTracking
} from "./chunk-XDCEA27D.js";
import "./chunk-EZSYDDUK.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-PVCRV2LE.js";
import {
  useRBAC
} from "./chunk-ZJEMJY2Q.js";
import "./chunk-D4WYVNVM.js";
import "./chunk-MMOBCIZG.js";
import {
  Layouts
} from "./chunk-MTTHLNPH.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import {
  ForwardRef$J,
  Page,
  useAPIErrorHandler
} from "./chunk-7LKLOY7A.js";
import "./chunk-ODQFI753.js";
import {
  useTypedSelector
} from "./chunk-ZOP4VV6J.js";
import {
  require_lib
} from "./chunk-WH6VCVXU.js";
import "./chunk-IL5G2D22.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-76QM3EFM.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  EmptyStateLayout,
  LinkButton,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  Link,
  useNavigate
} from "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$1h
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/ApiTokens/ListView.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var qs = __toESM(require_lib(), 1);
var TABLE_HEADERS = [
  {
    name: "name",
    label: {
      id: "Settings.apiTokens.ListView.headers.name",
      defaultMessage: "Name"
    },
    sortable: true
  },
  {
    name: "description",
    label: {
      id: "Settings.apiTokens.ListView.headers.description",
      defaultMessage: "Description"
    },
    sortable: false
  },
  {
    name: "createdAt",
    label: {
      id: "Settings.apiTokens.ListView.headers.createdAt",
      defaultMessage: "Created at"
    },
    sortable: false
  },
  {
    name: "lastUsedAt",
    label: {
      id: "Settings.apiTokens.ListView.headers.lastUsedAt",
      defaultMessage: "Last used"
    },
    sortable: false
  }
];
var ListView = () => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const permissions = useTypedSelector((state) => {
    var _a;
    return (_a = state.admin_app.permissions.settings) == null ? void 0 : _a["api-tokens"];
  });
  const { allowedActions: { canRead, canCreate, canDelete, canUpdate } } = useRBAC(permissions);
  const navigate = useNavigate();
  const { trackUsage } = useTracking();
  const startSection = useGuidedTour("ListView", (state) => state.startSection);
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  React.useEffect(() => {
    startSection("apiTokens");
  }, [
    startSection
  ]);
  React.useEffect(() => {
    navigate({
      search: qs.stringify({
        sort: "name:ASC"
      }, {
        encode: false
      })
    });
  }, [
    navigate
  ]);
  const headers = TABLE_HEADERS.map((header) => ({
    ...header,
    label: formatMessage(header.label)
  }));
  useOnce(() => {
    trackUsage("willAccessTokenList", {
      tokenType: API_TOKEN_TYPE
    });
  });
  const { data: apiTokens = [], isLoading, error } = useGetAPITokensQuery();
  React.useEffect(() => {
    if (error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    }
  }, [
    error,
    formatAPIError,
    toggleNotification
  ]);
  React.useEffect(() => {
    trackUsage("didAccessTokenList", {
      number: apiTokens.length,
      tokenType: API_TOKEN_TYPE
    });
  }, [
    apiTokens,
    trackUsage
  ]);
  const [deleteToken] = useDeleteAPITokenMutation();
  const handleDelete = async (id) => {
    try {
      const res = await deleteToken(id);
      if ("error" in res) {
        toggleNotification({
          type: "danger",
          message: formatAPIError(res.error)
        });
        return;
      }
      trackUsage("didDeleteToken");
    } catch {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "Something went wrong"
        })
      });
    }
  };
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, {
    children: [
      (0, import_jsx_runtime.jsx)(Page.Title, {
        children: formatMessage({
          id: "Settings.PageTitle",
          defaultMessage: "Settings - {name}"
        }, {
          name: "API Tokens"
        })
      }),
      (0, import_jsx_runtime.jsx)(Layouts.Header, {
        title: formatMessage({
          id: "Settings.apiTokens.title",
          defaultMessage: "API Tokens"
        }),
        subtitle: formatMessage({
          id: "Settings.apiTokens.description",
          defaultMessage: "List of generated tokens to consume the API"
        }),
        primaryAction: canCreate && (0, import_jsx_runtime.jsx)(LinkButton, {
          tag: Link,
          "data-testid": "create-api-token-button",
          startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1h, {}),
          size: "S",
          onClick: () => trackUsage("willAddTokenFromList", {
            tokenType: API_TOKEN_TYPE
          }),
          to: "/settings/api-tokens/create",
          children: formatMessage({
            id: "Settings.apiTokens.create",
            defaultMessage: "Create new API Token"
          })
        })
      }),
      !canRead ? (0, import_jsx_runtime.jsx)(Page.NoPermissions, {}) : (0, import_jsx_runtime.jsx)(Page.Main, {
        "aria-busy": isLoading,
        children: (0, import_jsx_runtime.jsxs)(Layouts.Content, {
          children: [
            apiTokens.length > 0 && (0, import_jsx_runtime.jsx)(Table, {
              permissions: {
                canRead,
                canDelete,
                canUpdate
              },
              headers,
              isLoading,
              onConfirmDelete: handleDelete,
              tokens: apiTokens,
              tokenType: API_TOKEN_TYPE
            }),
            canCreate && apiTokens.length === 0 ? (0, import_jsx_runtime.jsx)(EmptyStateLayout, {
              icon: (0, import_jsx_runtime.jsx)(ForwardRef$J, {
                width: "16rem"
              }),
              content: formatMessage({
                id: "Settings.apiTokens.addFirstToken",
                defaultMessage: "Add your first API Token"
              }),
              action: (0, import_jsx_runtime.jsx)(LinkButton, {
                tag: Link,
                variant: "secondary",
                startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1h, {}),
                to: "/settings/api-tokens/create",
                children: formatMessage({
                  id: "Settings.apiTokens.addNewToken",
                  defaultMessage: "Add new API Token"
                })
              })
            }) : null,
            !canCreate && apiTokens.length === 0 ? (0, import_jsx_runtime.jsx)(EmptyStateLayout, {
              icon: (0, import_jsx_runtime.jsx)(ForwardRef$J, {
                width: "16rem"
              }),
              content: formatMessage({
                id: "Settings.apiTokens.emptyStateLayout",
                defaultMessage: "You don’t have any content yet..."
              })
            }) : null
          ]
        })
      })
    ]
  });
};
var ProtectedListView = () => {
  const permissions = useTypedSelector((state) => {
    var _a;
    return (_a = state.admin_app.permissions.settings) == null ? void 0 : _a["api-tokens"].main;
  });
  return (0, import_jsx_runtime.jsx)(Page.Protect, {
    permissions,
    children: (0, import_jsx_runtime.jsx)(ListView, {})
  });
};
export {
  ListView,
  ProtectedListView
};
//# sourceMappingURL=ListView-P646PDDC.js.map
