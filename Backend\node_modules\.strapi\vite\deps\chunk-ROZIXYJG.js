import {
  require_set
} from "./chunk-SGQJOZK5.js";
import {
  require_get
} from "./chunk-WH6VCVXU.js";
import {
  createContext
} from "./chunk-76QM3EFM.js";
import {
  fn
} from "./chunk-QOUV5O5E.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/components/GuidedTour/Provider.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_get = __toESM(require_get(), 1);
var import_set = __toESM(require_set(), 1);
var GUIDED_TOUR_COMPLETED_STEPS = "GUIDED_TOUR_COMPLETED_STEPS";
var GUIDED_TOUR_CURRENT_STEP = "GUIDED_TOUR_CURRENT_STEP";
var GUIDED_TOUR_SKIPPED = "GUIDED_TOUR_SKIPPED";
var [GuidedTourProviderImpl, useGuidedTour] = createContext("GuidedTour");
var GuidedTourProvider = ({ children }) => {
  const [{ currentStep, guidedTourState, isGuidedTourVisible, isSkipped }, dispatch] = React.useReducer(reducer, initialState, initialiseState);
  const setCurrentStep = (step) => {
    if (step !== null) {
      const isStepAlreadyDone = (0, import_get.default)(guidedTourState, step);
      const [sectionName, stepName] = step.split(".");
      const sectionArray = Object.entries(guidedTourState[sectionName]);
      const currentStepIndex = sectionArray.findIndex(([key]) => key === stepName);
      const previousSteps = sectionArray.slice(0, currentStepIndex);
      const isStepToShow = previousSteps.every(([, sectionValue]) => sectionValue);
      if (isStepAlreadyDone || isSkipped || !isStepToShow) {
        return null;
      }
    }
    window.localStorage.setItem(GUIDED_TOUR_CURRENT_STEP, JSON.stringify(null));
    return dispatch({
      type: "SET_CURRENT_STEP",
      step
    });
  };
  const setGuidedTourVisibility = (value) => {
    dispatch({
      type: "SET_GUIDED_TOUR_VISIBILITY",
      value
    });
  };
  const setStepState = (currentStep2, value) => {
    addCompletedStep(currentStep2);
    dispatch({
      type: "SET_STEP_STATE",
      currentStep: currentStep2,
      value
    });
  };
  const startSection = (sectionName) => {
    const sectionSteps = guidedTourState[sectionName];
    if (sectionSteps) {
      const guidedTourArray = Object.entries(guidedTourState);
      const currentSectionIndex = guidedTourArray.findIndex(([key]) => key === sectionName);
      const previousSections = guidedTourArray.slice(0, currentSectionIndex);
      const isSectionToShow = previousSections.every(([, sectionValue]) => Object.values(sectionValue).every(Boolean));
      const [firstStep] = Object.keys(sectionSteps);
      const isFirstStepDone = sectionSteps[firstStep];
      if (isSectionToShow && !currentStep && !isFirstStepDone) {
        setCurrentStep(`${sectionName}.${firstStep}`);
      }
    }
  };
  const setSkipped = (value) => {
    window.localStorage.setItem(GUIDED_TOUR_SKIPPED, JSON.stringify(value));
    dispatch({
      type: "SET_SKIPPED",
      value
    });
  };
  return (0, import_jsx_runtime.jsx)(GuidedTourProviderImpl, {
    guidedTourState,
    currentStep,
    setCurrentStep,
    setGuidedTourVisibility,
    setSkipped,
    setStepState,
    startSection,
    isGuidedTourVisible,
    isSkipped,
    children
  });
};
var initialState = {
  currentStep: null,
  guidedTourState: {
    contentTypeBuilder: {
      create: false,
      success: false
    },
    contentManager: {
      create: false,
      success: false
    },
    apiTokens: {
      create: false,
      success: false
    }
  },
  isGuidedTourVisible: false,
  isSkipped: false
};
var reducer = (state = initialState, action) => fn(state, (draftState) => {
  switch (action.type) {
    case "SET_CURRENT_STEP": {
      draftState.currentStep = action.step;
      break;
    }
    case "SET_STEP_STATE": {
      const [section, step] = action.currentStep.split(".");
      draftState.guidedTourState[section][step] = action.value;
      break;
    }
    case "SET_SKIPPED": {
      draftState.isSkipped = action.value;
      break;
    }
    case "SET_GUIDED_TOUR_VISIBILITY": {
      draftState.isGuidedTourVisible = action.value;
      break;
    }
    default: {
      return draftState;
    }
  }
});
var initialiseState = (initialState2) => {
  const copyInitialState = {
    ...initialState2
  };
  const guidedTourLocaleStorage = JSON.parse(window.localStorage.getItem(GUIDED_TOUR_COMPLETED_STEPS) ?? "[]");
  const currentStepLocaleStorage = JSON.parse(window.localStorage.getItem(GUIDED_TOUR_CURRENT_STEP) ?? "null");
  const skippedLocaleStorage = JSON.parse(window.localStorage.getItem(GUIDED_TOUR_SKIPPED) ?? "null");
  if (Array.isArray(guidedTourLocaleStorage)) {
    guidedTourLocaleStorage.forEach((step) => {
      const [sectionName, stepName] = step.split(".");
      (0, import_set.default)(copyInitialState, [
        "guidedTourState",
        sectionName,
        stepName
      ], true);
    });
  }
  if (currentStepLocaleStorage) {
    const [sectionName, stepName] = currentStepLocaleStorage.split(".");
    (0, import_set.default)(copyInitialState, [
      "guidedTourState",
      sectionName,
      stepName
    ], true);
    addCompletedStep(currentStepLocaleStorage);
    window.localStorage.setItem(GUIDED_TOUR_CURRENT_STEP, JSON.stringify(null));
  }
  if (skippedLocaleStorage !== null) {
    (0, import_set.default)(copyInitialState, "isSkipped", skippedLocaleStorage);
  }
  return copyInitialState;
};
var addCompletedStep = (completedStep) => {
  const currentSteps = JSON.parse(window.localStorage.getItem(GUIDED_TOUR_COMPLETED_STEPS) ?? "[]");
  if (!Array.isArray(currentSteps)) {
    return;
  }
  const isAlreadyStored = currentSteps.includes(completedStep);
  if (isAlreadyStored) {
    return;
  }
  window.localStorage.setItem(GUIDED_TOUR_COMPLETED_STEPS, JSON.stringify([
    ...currentSteps,
    completedStep
  ]));
};

export {
  useGuidedTour,
  GuidedTourProvider
};
//# sourceMappingURL=chunk-ROZIXYJG.js.map
