import {
  COMMON_USER_SCHEMA
} from "./chunk-AHWGSUAE.js";
import {
  MagicLinkCE,
  SelectRoles
} from "./chunk-ZJEDGGGS.js";
import {
  selectAdminPermissions
} from "./chunk-2TUVRMRA.js";
import "./chunk-LDJANAVJ.js";
import "./chunk-BGSCGOFP.js";
import {
  require_pick
} from "./chunk-C72RZIDJ.js";
import {
  useEnterprise
} from "./chunk-HZKRK7AR.js";
import "./chunk-AFM2NWPO.js";
import {
  BackButton
} from "./chunk-DUGZ4WIW.js";
import {
  errorsTrads
} from "./chunk-IFOFBKTA.js";
import {
  create3 as create,
  create6 as create2,
  create7 as create3
} from "./chunk-376QHLWZ.js";
import "./chunk-EGNP2T5O.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-QIJGNK42.js";
import "./chunk-CJHUGFLE.js";
import {
  getDisplayName
} from "./chunk-PVNXTKO6.js";
import {
  MemoizedInputRenderer
} from "./chunk-HWAQQGJJ.js";
import {
  Form
} from "./chunk-L5JCPKMP.js";
import {
  useRBAC
} from "./chunk-ZJEMJY2Q.js";
import {
  useAdminUsers,
  useUpdateUserMutation
} from "./chunk-6DRYEU2W.js";
import {
  Layouts
} from "./chunk-MTTHLNPH.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import {
  Page,
  useAPIErrorHandler
} from "./chunk-7LKLOY7A.js";
import "./chunk-ODQFI753.js";
import {
  useTypedSelector
} from "./chunk-ZOP4VV6J.js";
import {
  isBaseQueryError
} from "./chunk-WH6VCVXU.js";
import "./chunk-IL5G2D22.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-76QM3EFM.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  Box,
  Button,
  Flex,
  Grid,
  Typography,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  useMatch,
  useNavigate
} from "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$4F
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Users/<USER>
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_pick = __toESM(require_pick(), 1);
var EDIT_VALIDATION_SCHEMA = create2().shape({
  ...COMMON_USER_SCHEMA,
  isActive: create(),
  roles: create3().min(1, {
    id: errorsTrads.required.id,
    defaultMessage: "This field is required"
  }).required({
    id: errorsTrads.required.id,
    defaultMessage: "This field is required"
  })
});
var fieldsToPick = [
  "email",
  "firstname",
  "lastname",
  "username",
  "isActive",
  "roles"
];
var EditPage = () => {
  var _a, _b, _c;
  const { formatMessage } = useIntl();
  const match = useMatch("/settings/users/:id");
  const id = ((_a = match == null ? void 0 : match.params) == null ? void 0 : _a.id) ?? "";
  const navigate = useNavigate();
  const { toggleNotification } = useNotification();
  const MagicLink = useEnterprise(MagicLinkCE, async () => (await import("./MagicLinkEE-L6L3227A.js")).MagicLinkEE);
  const { _unstableFormatAPIError: formatAPIError, _unstableFormatValidationErrors: formatValidationErrors } = useAPIErrorHandler();
  const permissions = useTypedSelector(selectAdminPermissions);
  const { isLoading: isLoadingRBAC, allowedActions: { canUpdate } } = useRBAC({
    read: ((_b = permissions.settings) == null ? void 0 : _b.users.read) ?? [],
    update: ((_c = permissions.settings) == null ? void 0 : _c.users.update) ?? []
  });
  const [updateUser] = useUpdateUserMutation();
  const { data, error, isLoading: isLoadingAdminUsers } = useAdminUsers({
    id
  }, {
    refetchOnMountOrArgChange: true
  });
  const [user] = (data == null ? void 0 : data.users) ?? [];
  React.useEffect(() => {
    if (error) {
      if (error.name === "UnauthorizedError") {
        toggleNotification({
          type: "info",
          message: formatMessage({
            id: "notification.permission.not-allowed-read",
            defaultMessage: "You are not allowed to see this document"
          })
        });
        navigate("/");
      } else {
        toggleNotification({
          type: "danger",
          message: formatAPIError(error)
        });
      }
    }
  }, [
    error,
    formatAPIError,
    formatMessage,
    navigate,
    toggleNotification
  ]);
  const isLoading = isLoadingAdminUsers || !MagicLink || isLoadingRBAC;
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  const initialData = {
    ...(0, import_pick.default)(user, fieldsToPick),
    roles: user.roles.map(({ id: id2 }) => id2),
    password: "",
    confirmPassword: ""
  };
  const handleSubmit = async (body, actions) => {
    const { confirmPassword: _confirmPassword, ...bodyRest } = body;
    const res = await updateUser({
      id,
      ...bodyRest
    });
    if ("error" in res && isBaseQueryError(res.error)) {
      if (res.error.name === "ValidationError") {
        actions.setErrors(formatValidationErrors(res.error));
      }
      toggleNotification({
        type: "danger",
        message: formatAPIError(res.error)
      });
    } else {
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: "notification.success.saved",
          defaultMessage: "Saved"
        })
      });
      actions.setValues({
        ...(0, import_pick.default)(body, fieldsToPick),
        password: "",
        confirmPassword: ""
      });
    }
  };
  return (0, import_jsx_runtime.jsxs)(Page.Main, {
    children: [
      (0, import_jsx_runtime.jsx)(Page.Title, {
        children: formatMessage({
          id: "Settings.PageTitle",
          defaultMessage: "Settings - {name}"
        }, {
          name: "Users"
        })
      }),
      (0, import_jsx_runtime.jsx)(Form, {
        method: "PUT",
        onSubmit: handleSubmit,
        initialValues: initialData,
        validationSchema: EDIT_VALIDATION_SCHEMA,
        children: ({ isSubmitting, modified }) => {
          return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, {
            children: [
              (0, import_jsx_runtime.jsx)(Layouts.Header, {
                primaryAction: (0, import_jsx_runtime.jsx)(Button, {
                  disabled: isSubmitting || !canUpdate || !modified,
                  startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4F, {}),
                  loading: isSubmitting,
                  type: "submit",
                  children: formatMessage({
                    id: "global.save",
                    defaultMessage: "Save"
                  })
                }),
                title: formatMessage({
                  id: "app.containers.Users.EditPage.header.label",
                  defaultMessage: "Edit {name}"
                }, {
                  // @ts-expect-error – issues with the Entity ID type, still.
                  name: getDisplayName(initialData)
                }),
                navigationAction: (0, import_jsx_runtime.jsx)(BackButton, {
                  fallback: "../users"
                })
              }),
              (0, import_jsx_runtime.jsxs)(Layouts.Content, {
                children: [
                  (user == null ? void 0 : user.registrationToken) && (0, import_jsx_runtime.jsx)(Box, {
                    paddingBottom: 6,
                    children: (0, import_jsx_runtime.jsx)(MagicLink, {
                      registrationToken: user.registrationToken
                    })
                  }),
                  (0, import_jsx_runtime.jsxs)(Flex, {
                    direction: "column",
                    alignItems: "stretch",
                    gap: 7,
                    children: [
                      (0, import_jsx_runtime.jsx)(Box, {
                        background: "neutral0",
                        hasRadius: true,
                        shadow: "filterShadow",
                        paddingTop: 6,
                        paddingBottom: 6,
                        paddingLeft: 7,
                        paddingRight: 7,
                        children: (0, import_jsx_runtime.jsxs)(Flex, {
                          direction: "column",
                          alignItems: "stretch",
                          gap: 4,
                          children: [
                            (0, import_jsx_runtime.jsx)(Typography, {
                              variant: "delta",
                              tag: "h2",
                              children: formatMessage({
                                id: "app.components.Users.ModalCreateBody.block-title.details",
                                defaultMessage: "Details"
                              })
                            }),
                            (0, import_jsx_runtime.jsx)(Grid.Root, {
                              gap: 5,
                              children: LAYOUT.map((row) => row.map(({ size, label, ...field }) => {
                                return (0, import_jsx_runtime.jsx)(Grid.Item, {
                                  col: size,
                                  direction: "column",
                                  alignItems: "stretch",
                                  children: (0, import_jsx_runtime.jsx)(MemoizedInputRenderer, {
                                    ...field,
                                    disabled: !canUpdate,
                                    label: formatMessage(label),
                                    placeholder: "placeholder" in field ? formatMessage(field.placeholder) : void 0
                                  })
                                }, field.name);
                              }))
                            })
                          ]
                        })
                      }),
                      (0, import_jsx_runtime.jsx)(Box, {
                        background: "neutral0",
                        hasRadius: true,
                        shadow: "filterShadow",
                        paddingTop: 6,
                        paddingBottom: 6,
                        paddingLeft: 7,
                        paddingRight: 7,
                        children: (0, import_jsx_runtime.jsxs)(Flex, {
                          direction: "column",
                          alignItems: "stretch",
                          gap: 4,
                          children: [
                            (0, import_jsx_runtime.jsx)(Typography, {
                              variant: "delta",
                              tag: "h2",
                              children: formatMessage({
                                id: "global.roles",
                                defaultMessage: "User's role"
                              })
                            }),
                            (0, import_jsx_runtime.jsx)(Grid.Root, {
                              gap: 5,
                              children: (0, import_jsx_runtime.jsx)(Grid.Item, {
                                col: 6,
                                xs: 12,
                                direction: "column",
                                alignItems: "stretch",
                                children: (0, import_jsx_runtime.jsx)(SelectRoles, {
                                  disabled: !canUpdate
                                })
                              })
                            })
                          ]
                        })
                      })
                    ]
                  })
                ]
              })
            ]
          });
        }
      })
    ]
  });
};
var LAYOUT = [
  [
    {
      label: {
        id: "Auth.form.firstname.label",
        defaultMessage: "First name"
      },
      name: "firstname",
      placeholder: {
        id: "Auth.form.firstname.placeholder",
        defaultMessage: "e.g. Kai"
      },
      type: "string",
      size: 6,
      required: true
    },
    {
      label: {
        id: "Auth.form.lastname.label",
        defaultMessage: "Last name"
      },
      name: "lastname",
      placeholder: {
        id: "Auth.form.lastname.placeholder",
        defaultMessage: "e.g. Doe"
      },
      type: "string",
      size: 6
    }
  ],
  [
    {
      label: {
        id: "Auth.form.email.label",
        defaultMessage: "Email"
      },
      name: "email",
      placeholder: {
        id: "Auth.form.email.placeholder",
        defaultMessage: "e.g. <EMAIL>"
      },
      type: "email",
      size: 6,
      required: true
    },
    {
      label: {
        id: "Auth.form.username.label",
        defaultMessage: "Username"
      },
      name: "username",
      placeholder: {
        id: "Auth.form.username.placeholder",
        defaultMessage: "e.g. Kai_Doe"
      },
      type: "string",
      size: 6
    }
  ],
  [
    {
      autoComplete: "new-password",
      label: {
        id: "global.password",
        defaultMessage: "Password"
      },
      name: "password",
      type: "password",
      size: 6
    },
    {
      autoComplete: "new-password",
      label: {
        id: "Auth.form.confirmPassword.label",
        defaultMessage: "Password confirmation"
      },
      name: "confirmPassword",
      type: "password",
      size: 6
    }
  ],
  [
    {
      label: {
        id: "Auth.form.active.label",
        defaultMessage: "Active"
      },
      name: "isActive",
      type: "boolean",
      size: 6
    }
  ]
];
var ProtectedEditPage = () => {
  const permissions = useTypedSelector((state) => {
    var _a;
    return (_a = state.admin_app.permissions.settings) == null ? void 0 : _a.users.read;
  });
  return (0, import_jsx_runtime.jsx)(Page.Protect, {
    permissions,
    children: (0, import_jsx_runtime.jsx)(EditPage, {})
  });
};
export {
  EditPage,
  ProtectedEditPage
};
//# sourceMappingURL=EditPage-ISNAO326.js.map
