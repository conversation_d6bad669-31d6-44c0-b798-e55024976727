import {
  SETTINGS_SCHEMA
} from "./chunk-5DNP3MPU.js";
import {
  useTypedSelector
} from "./chunk-3S5POVAJ.js";
import {
  getTimezones,
  useGetReleaseSettingsQuery,
  useUpdateReleaseSettingsMutation
} from "./chunk-ZHZ667KP.js";
import "./chunk-EKXSMIUH.js";
import "./chunk-YV3ONWF5.js";
import "./chunk-UMW22TSS.js";
import "./chunk-4QC3H4VA.js";
import "./chunk-75D2ZJP5.js";
import "./chunk-VCTAT6B3.js";
import "./chunk-ROZIXYJG.js";
import "./chunk-C72RZIDJ.js";
import "./chunk-HZKRK7AR.js";
import {
  GradientBadgeWithIcon
} from "./chunk-LRN6A2UC.js";
import "./chunk-D2TGW5YS.js";
import "./chunk-M27D4U76.js";
import "./chunk-HX66WGOY.js";
import "./chunk-Y4UEUAII.js";
import "./chunk-BN2UQHMJ.js";
import "./chunk-NWWGC2Z2.js";
import "./chunk-MBK4V2X7.js";
import "./chunk-DY2RJG3P.js";
import "./chunk-K65KIEAL.js";
import "./chunk-BUDFB33L.js";
import "./chunk-7MILHJ3J.js";
import "./chunk-SGQJOZK5.js";
import "./chunk-AFM2NWPO.js";
import "./chunk-DUGZ4WIW.js";
import "./chunk-IFOFBKTA.js";
import "./chunk-376QHLWZ.js";
import "./chunk-EGNP2T5O.js";
import "./chunk-XDCEA27D.js";
import "./chunk-EZSYDDUK.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-QIJGNK42.js";
import "./chunk-CJHUGFLE.js";
import "./chunk-IQGHPIIW.js";
import "./chunk-DWSGKQEK.js";
import "./chunk-W6ZGCRX6.js";
import "./chunk-PVCRV2LE.js";
import "./chunk-HWAQQGJJ.js";
import {
  Form,
  useField
} from "./chunk-L5JCPKMP.js";
import {
  useRBAC
} from "./chunk-ZJEMJY2Q.js";
import "./chunk-6DRYEU2W.js";
import {
  Layouts
} from "./chunk-MTTHLNPH.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import {
  Page,
  useAPIErrorHandler
} from "./chunk-7LKLOY7A.js";
import "./chunk-ODQFI753.js";
import "./chunk-ZOP4VV6J.js";
import {
  isFetchError
} from "./chunk-WH6VCVXU.js";
import "./chunk-IL5G2D22.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-76QM3EFM.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  Button,
  Combobox,
  Field,
  Flex,
  Grid,
  Option,
  Typography,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import "./chunk-5ZC4PE57.js";
import "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$4F
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import "./chunk-ACIMPXWY.js";
import "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-releases/dist/admin/pages/ReleasesSettingsPage.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var ReleasesSettingsPage = () => {
  const { formatMessage } = useIntl();
  const { formatAPIError } = useAPIErrorHandler();
  const { toggleNotification } = useNotification();
  const { data, isLoading: isLoadingSettings } = useGetReleaseSettingsQuery();
  const [updateReleaseSettings, { isLoading: isSubmittingForm }] = useUpdateReleaseSettingsMutation();
  const permissions = useTypedSelector((state) => {
    var _a;
    return (_a = state.admin_app.permissions["settings"]) == null ? void 0 : _a["releases"];
  });
  const { allowedActions: { canUpdate } } = useRBAC(permissions);
  const { timezoneList } = getTimezones(/* @__PURE__ */ new Date());
  const handleSubmit = async (body, { setErrors }) => {
    const { defaultTimezone } = body;
    const formattedDefaultTimezone = defaultTimezone;
    const isBodyTimezoneValid = timezoneList.some((timezone) => timezone.value === formattedDefaultTimezone);
    if (!isBodyTimezoneValid && defaultTimezone) {
      const errorMessage = formatMessage({
        id: "components.Input.error.validation.combobox.invalid",
        defaultMessage: "The value provided is not valid"
      });
      setErrors({
        defaultTimezone: errorMessage
      });
      toggleNotification({
        type: "danger",
        message: errorMessage
      });
      return;
    }
    const newBody = !defaultTimezone || !isBodyTimezoneValid ? {
      defaultTimezone: null
    } : {
      defaultTimezone: formattedDefaultTimezone
    };
    try {
      const response = await updateReleaseSettings(newBody);
      if ("data" in response) {
        toggleNotification({
          type: "success",
          message: formatMessage({
            id: "content-releases.pages.Settings.releases.setting.default-timezone-notification-success",
            defaultMessage: "Default timezone updated."
          })
        });
      } else if (isFetchError(response.error)) {
        toggleNotification({
          type: "danger",
          message: formatAPIError(response.error)
        });
      } else {
        toggleNotification({
          type: "danger",
          message: formatMessage({
            id: "notification.error",
            defaultMessage: "An error occurred"
          })
        });
      }
    } catch (error) {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "An error occurred"
        })
      });
    }
  };
  if (isLoadingSettings) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  const releasePageTitle = formatMessage({
    id: "content-releases.pages.Releases.title",
    defaultMessage: "Releases"
  });
  return (0, import_jsx_runtime.jsxs)(Layouts.Root, {
    children: [
      (0, import_jsx_runtime.jsx)(Page.Title, {
        children: formatMessage({
          id: "Settings.PageTitle",
          defaultMessage: "Settings - {name}"
        }, {
          name: releasePageTitle
        })
      }),
      (0, import_jsx_runtime.jsx)(Page.Main, {
        "aria-busy": isLoadingSettings,
        tabIndex: -1,
        children: (0, import_jsx_runtime.jsx)(Form, {
          method: "PUT",
          initialValues: {
            defaultTimezone: data == null ? void 0 : data.data.defaultTimezone
          },
          onSubmit: handleSubmit,
          validationSchema: SETTINGS_SCHEMA,
          children: ({ modified, isSubmitting }) => {
            return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, {
              children: [
                (0, import_jsx_runtime.jsx)(Layouts.Header, {
                  primaryAction: canUpdate ? (0, import_jsx_runtime.jsx)(Button, {
                    disabled: !modified || isSubmittingForm,
                    loading: isSubmitting,
                    startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4F, {}),
                    type: "submit",
                    children: formatMessage({
                      id: "global.save",
                      defaultMessage: "Save"
                    })
                  }) : null,
                  secondaryAction: (0, import_jsx_runtime.jsx)(GradientBadgeWithIcon, {
                    label: formatMessage({
                      id: "components.premiumFeature.title",
                      defaultMessage: "Premium feature"
                    })
                  }),
                  title: releasePageTitle,
                  subtitle: formatMessage({
                    id: "content-releases.pages.Settings.releases.description",
                    defaultMessage: "Create and manage content updates"
                  })
                }),
                (0, import_jsx_runtime.jsx)(Layouts.Content, {
                  children: (0, import_jsx_runtime.jsxs)(Flex, {
                    direction: "column",
                    background: "neutral0",
                    alignItems: "stretch",
                    padding: 6,
                    gap: 6,
                    shadow: "filterShadow",
                    hasRadius: true,
                    children: [
                      (0, import_jsx_runtime.jsx)(Typography, {
                        variant: "delta",
                        tag: "h2",
                        children: formatMessage({
                          id: "content-releases.pages.Settings.releases.preferences.title",
                          defaultMessage: "Preferences"
                        })
                      }),
                      (0, import_jsx_runtime.jsx)(Grid.Root, {
                        children: (0, import_jsx_runtime.jsx)(Grid.Item, {
                          col: 6,
                          s: 12,
                          direction: "column",
                          alignItems: "stretch",
                          children: (0, import_jsx_runtime.jsx)(TimezoneDropdown, {})
                        })
                      })
                    ]
                  })
                })
              ]
            });
          }
        })
      })
    ]
  });
};
var TimezoneDropdown = () => {
  const permissions = useTypedSelector((state) => {
    var _a;
    return (_a = state.admin_app.permissions["settings"]) == null ? void 0 : _a["releases"];
  });
  const { allowedActions: { canUpdate } } = useRBAC(permissions);
  const { formatMessage } = useIntl();
  const { timezoneList } = getTimezones(/* @__PURE__ */ new Date());
  const field = useField("defaultTimezone");
  return (0, import_jsx_runtime.jsxs)(Field.Root, {
    name: "defaultTimezone",
    hint: formatMessage({
      id: "content-releases.pages.Settings.releases.timezone.hint",
      defaultMessage: "The timezone of every release can still be changed individually."
    }),
    error: field.error,
    children: [
      (0, import_jsx_runtime.jsx)(Field.Label, {
        children: formatMessage({
          id: "content-releases.pages.Settings.releases.timezone.label",
          defaultMessage: "Default timezone"
        })
      }),
      (0, import_jsx_runtime.jsx)(Combobox, {
        autocomplete: {
          type: "list",
          filter: "contains"
        },
        onTextValueChange: (value) => field.onChange("defaultTimezone", value),
        onChange: (value) => {
          if (field.value && value || !field.value) {
            field.onChange("defaultTimezone", value);
          }
        },
        onClear: () => field.onChange("defaultTimezone", ""),
        value: field.value,
        disabled: !canUpdate,
        children: timezoneList.map((timezone) => (0, import_jsx_runtime.jsx)(Option, {
          value: timezone.value,
          children: timezone.value.replace(/&/, " ")
        }, timezone.value))
      }),
      (0, import_jsx_runtime.jsx)(Field.Hint, {}),
      (0, import_jsx_runtime.jsx)(Field.Error, {})
    ]
  });
};
var ProtectedReleasesSettingsPage = () => {
  const permissions = useTypedSelector((state) => {
    var _a, _b;
    return (_b = (_a = state.admin_app.permissions["settings"]) == null ? void 0 : _a["releases"]) == null ? void 0 : _b.read;
  });
  return (0, import_jsx_runtime.jsx)(Page.Protect, {
    permissions,
    children: (0, import_jsx_runtime.jsx)(ReleasesSettingsPage, {})
  });
};
export {
  ProtectedReleasesSettingsPage
};
//# sourceMappingURL=ReleasesSettingsPage-T7T7MHVN.js.map
