import {
  SSOProviders
} from "./chunk-OT6263S7.js";
import {
  Column,
  LayoutContent,
  Logo,
  UnauthenticatedLayout
} from "./chunk-M27D4U76.js";
import "./chunk-HX66WGOY.js";
import "./chunk-XDCEA27D.js";
import "./chunk-EZSYDDUK.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-ZJEMJY2Q.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import "./chunk-7LKLOY7A.js";
import {
  useGetProvidersQuery
} from "./chunk-ODQFI753.js";
import "./chunk-ZOP4VV6J.js";
import "./chunk-WH6VCVXU.js";
import "./chunk-IL5G2D22.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-76QM3EFM.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import "./chunk-UBCTZOSQ.js";
import {
  Box,
  Button,
  Divider,
  Flex,
  Link,
  Loader,
  Main,
  Typography,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  NavLink,
  Navigate,
  useNavigate
} from "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-ACIMPXWY.js";
import "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/ee/admin/src/pages/AuthPage/components/Providers.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var Providers = () => {
  const navigate = useNavigate();
  const { formatMessage } = useIntl();
  const { isLoading, data: providers = [] } = useGetProvidersQuery(void 0, {
    skip: !window.strapi.features.isEnabled(window.strapi.features.SSO)
  });
  const handleClick = () => {
    navigate("/auth/login");
  };
  if (!window.strapi.features.isEnabled(window.strapi.features.SSO) || !isLoading && providers.length === 0) {
    return (0, import_jsx_runtime.jsx)(Navigate, {
      to: "/auth/login"
    });
  }
  return (0, import_jsx_runtime.jsx)(UnauthenticatedLayout, {
    children: (0, import_jsx_runtime.jsxs)(Main, {
      children: [
        (0, import_jsx_runtime.jsxs)(LayoutContent, {
          children: [
            (0, import_jsx_runtime.jsxs)(Column, {
              children: [
                (0, import_jsx_runtime.jsx)(Logo, {}),
                (0, import_jsx_runtime.jsx)(Box, {
                  paddingTop: 6,
                  paddingBottom: 1,
                  children: (0, import_jsx_runtime.jsx)(Typography, {
                    tag: "h1",
                    variant: "alpha",
                    children: formatMessage({
                      id: "Auth.form.welcome.title"
                    })
                  })
                }),
                (0, import_jsx_runtime.jsx)(Box, {
                  paddingBottom: 7,
                  children: (0, import_jsx_runtime.jsx)(Typography, {
                    variant: "epsilon",
                    textColor: "neutral600",
                    children: formatMessage({
                      id: "Auth.login.sso.subtitle"
                    })
                  })
                })
              ]
            }),
            (0, import_jsx_runtime.jsxs)(Flex, {
              direction: "column",
              alignItems: "stretch",
              gap: 7,
              children: [
                isLoading ? (0, import_jsx_runtime.jsx)(Flex, {
                  justifyContent: "center",
                  children: (0, import_jsx_runtime.jsx)(Loader, {
                    children: formatMessage({
                      id: "Auth.login.sso.loading"
                    })
                  })
                }) : (0, import_jsx_runtime.jsx)(SSOProviders, {
                  providers
                }),
                (0, import_jsx_runtime.jsxs)(Flex, {
                  children: [
                    (0, import_jsx_runtime.jsx)(DividerFull, {}),
                    (0, import_jsx_runtime.jsx)(Box, {
                      paddingLeft: 3,
                      paddingRight: 3,
                      children: (0, import_jsx_runtime.jsx)(Typography, {
                        variant: "sigma",
                        textColor: "neutral600",
                        children: formatMessage({
                          id: "or"
                        })
                      })
                    }),
                    (0, import_jsx_runtime.jsx)(DividerFull, {})
                  ]
                }),
                (0, import_jsx_runtime.jsx)(Button, {
                  fullWidth: true,
                  size: "L",
                  onClick: handleClick,
                  children: formatMessage({
                    id: "Auth.form.button.login.strapi"
                  })
                })
              ]
            })
          ]
        }),
        (0, import_jsx_runtime.jsx)(Flex, {
          justifyContent: "center",
          children: (0, import_jsx_runtime.jsx)(Box, {
            paddingTop: 4,
            children: (0, import_jsx_runtime.jsx)(Link, {
              tag: NavLink,
              to: "/auth/forgot-password",
              children: (0, import_jsx_runtime.jsx)(Typography, {
                variant: "pi",
                children: formatMessage({
                  id: "Auth.link.forgot-password"
                })
              })
            })
          })
        })
      ]
    })
  });
};
var DividerFull = dt(Divider)`
  flex: 1;
`;

// node_modules/@strapi/admin/dist/admin/ee/admin/src/pages/AuthPage/constants.mjs
var FORMS = {
  providers: Providers
};
export {
  FORMS
};
//# sourceMappingURL=constants-AXLEZQHJ.js.map
