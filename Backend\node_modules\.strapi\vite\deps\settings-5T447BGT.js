import {
  <PERSON><PERSON>,
  <PERSON>,
  useReviewWorkflows
} from "./chunk-O667HMYI.js";
import {
  CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME,
  LimitsModal,
  useGetContentTypesQuery
} from "./chunk-K2IFNJMI.js";
import {
  useTypedSelector
} from "./chunk-HPPKX7ND.js";
import "./chunk-EKXSMIUH.js";
import "./chunk-C7H2BX76.js";
import "./chunk-YL7CEHOK.js";
import "./chunk-YV3ONWF5.js";
import "./chunk-UMW22TSS.js";
import "./chunk-4QC3H4VA.js";
import "./chunk-75D2ZJP5.js";
import "./chunk-VCTAT6B3.js";
import "./chunk-ROZIXYJG.js";
import {
  useLicenseLimits
} from "./chunk-53OG7EL5.js";
import "./chunk-C72RZIDJ.js";
import "./chunk-HZKRK7AR.js";
import "./chunk-LRN6A2UC.js";
import "./chunk-D2TGW5YS.js";
import "./chunk-M27D4U76.js";
import "./chunk-HX66WGOY.js";
import "./chunk-Y4UEUAII.js";
import "./chunk-BN2UQHMJ.js";
import {
  ConfirmDialog
} from "./chunk-NWWGC2Z2.js";
import "./chunk-MBK4V2X7.js";
import "./chunk-DY2RJG3P.js";
import "./chunk-K65KIEAL.js";
import "./chunk-BUDFB33L.js";
import "./chunk-7MILHJ3J.js";
import "./chunk-SGQJOZK5.js";
import "./chunk-AFM2NWPO.js";
import "./chunk-DUGZ4WIW.js";
import "./chunk-IFOFBKTA.js";
import "./chunk-376QHLWZ.js";
import "./chunk-EGNP2T5O.js";
import {
  useTracking
} from "./chunk-XDCEA27D.js";
import "./chunk-EZSYDDUK.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-QIJGNK42.js";
import "./chunk-CJHUGFLE.js";
import "./chunk-IQGHPIIW.js";
import "./chunk-DWSGKQEK.js";
import "./chunk-W6ZGCRX6.js";
import {
  Table
} from "./chunk-PVCRV2LE.js";
import "./chunk-HWAQQGJJ.js";
import "./chunk-L5JCPKMP.js";
import {
  useRBAC
} from "./chunk-ZJEMJY2Q.js";
import "./chunk-6DRYEU2W.js";
import "./chunk-MTTHLNPH.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import {
  Page
} from "./chunk-7LKLOY7A.js";
import "./chunk-ODQFI753.js";
import "./chunk-ZOP4VV6J.js";
import "./chunk-WH6VCVXU.js";
import "./chunk-IL5G2D22.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-76QM3EFM.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import "./chunk-UBCTZOSQ.js";
import {
  Dialog,
  Flex,
  IconButton,
  LinkButton,
  TFooter,
  Typography,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import "./chunk-5ZC4PE57.js";
import {
  Link,
  NavLink,
  useNavigate
} from "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$1h,
  ForwardRef$1v,
  ForwardRef$j
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/review-workflows/dist/admin/routes/settings/index.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var ReviewWorkflowsListView = () => {
  const { formatMessage } = useIntl();
  const navigate = useNavigate();
  const { trackUsage } = useTracking();
  const [workflowToDelete, setWorkflowToDelete] = React.useState(null);
  const [showLimitModal, setShowLimitModal] = React.useState(false);
  const { data, isLoading: isLoadingModels } = useGetContentTypesQuery();
  const { meta, workflows, isLoading, delete: deleteAction } = useReviewWorkflows();
  const { getFeature, isLoading: isLicenseLoading } = useLicenseLimits();
  const permissions = useTypedSelector((state) => {
    var _a;
    return (_a = state.admin_app.permissions.settings) == null ? void 0 : _a["review-workflows"];
  });
  const { allowedActions: { canCreate, canRead, canUpdate, canDelete } } = useRBAC(permissions);
  const limits = getFeature("review-workflows");
  const numberOfWorkflows = limits == null ? void 0 : limits[CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME];
  const handleDeleteWorkflow = (workflowId) => {
    setWorkflowToDelete(workflowId);
  };
  const toggleConfirmDeleteDialog = () => {
    setWorkflowToDelete(null);
  };
  const handleConfirmDeleteDialog = async () => {
    if (!workflowToDelete) return;
    await deleteAction(workflowToDelete);
    setWorkflowToDelete(null);
  };
  const handleCreateClick = (event) => {
    event.preventDefault();
    if (numberOfWorkflows && meta && (meta == null ? void 0 : meta.workflowCount) >= parseInt(numberOfWorkflows, 10)) {
      event.preventDefault();
      setShowLimitModal(true);
    } else {
      navigate("create");
      trackUsage("willCreateWorkflow");
    }
  };
  React.useEffect(() => {
    if (!isLoading && !isLicenseLoading) {
      if (numberOfWorkflows && meta && (meta == null ? void 0 : meta.workflowCount) > parseInt(numberOfWorkflows, 10)) {
        setShowLimitModal(true);
      }
    }
  }, [
    isLicenseLoading,
    isLoading,
    meta,
    meta == null ? void 0 : meta.workflowCount,
    numberOfWorkflows
  ]);
  const headers = [
    {
      label: formatMessage({
        id: "Settings.review-workflows.list.page.list.column.name.title",
        defaultMessage: "Name"
      }),
      name: "name"
    },
    {
      label: formatMessage({
        id: "Settings.review-workflows.list.page.list.column.stages.title",
        defaultMessage: "Stages"
      }),
      name: "stages"
    },
    {
      label: formatMessage({
        id: "Settings.review-workflows.list.page.list.column.contentTypes.title",
        defaultMessage: "Content Types"
      }),
      name: "content-types"
    }
  ];
  if (isLoading || isLoadingModels) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  const contentTypes = Object.values(data ?? {}).reduce((acc, curr) => {
    acc.push(...curr);
    return acc;
  }, []);
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, {
    children: [
      (0, import_jsx_runtime.jsx)(Header, {
        primaryAction: canCreate ? (0, import_jsx_runtime.jsx)(LinkButton, {
          startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1h, {}),
          size: "S",
          tag: NavLink,
          to: "create",
          onClick: handleCreateClick,
          children: formatMessage({
            id: "Settings.review-workflows.list.page.create",
            defaultMessage: "Create new workflow"
          })
        }) : null,
        subtitle: formatMessage({
          id: "Settings.review-workflows.list.page.subtitle",
          defaultMessage: "Manage your content review process"
        }),
        title: formatMessage({
          id: "Settings.review-workflows.list.page.title",
          defaultMessage: "Review Workflows"
        })
      }),
      (0, import_jsx_runtime.jsxs)(Root, {
        children: [
          (0, import_jsx_runtime.jsx)(Table.Root, {
            isLoading,
            rows: workflows,
            footer: canCreate ? (0, import_jsx_runtime.jsx)(TFooter, {
              cursor: "pointer",
              icon: (0, import_jsx_runtime.jsx)(ForwardRef$1h, {}),
              onClick: handleCreateClick,
              children: formatMessage({
                id: "Settings.review-workflows.list.page.create",
                defaultMessage: "Create new workflow"
              })
            }) : null,
            headers,
            children: (0, import_jsx_runtime.jsxs)(Table.Content, {
              children: [
                (0, import_jsx_runtime.jsx)(Table.Head, {
                  children: headers.map((head) => (0, import_jsx_runtime.jsx)(Table.HeaderCell, {
                    ...head
                  }, head.name))
                }),
                (0, import_jsx_runtime.jsx)(Table.Body, {
                  children: workflows.map((workflow) => (0, import_jsx_runtime.jsxs)(Table.Row, {
                    onClick: () => {
                      navigate(`${workflow.id}`);
                    },
                    children: [
                      (0, import_jsx_runtime.jsx)(Table.Cell, {
                        width: "25rem",
                        children: (0, import_jsx_runtime.jsx)(Typography, {
                          textColor: "neutral800",
                          fontWeight: "bold",
                          ellipsis: true,
                          children: workflow.name
                        })
                      }),
                      (0, import_jsx_runtime.jsx)(Table.Cell, {
                        children: (0, import_jsx_runtime.jsx)(Typography, {
                          textColor: "neutral800",
                          children: workflow.stages.length
                        })
                      }),
                      (0, import_jsx_runtime.jsx)(Table.Cell, {
                        children: (0, import_jsx_runtime.jsx)(Typography, {
                          textColor: "neutral800",
                          children: workflow.contentTypes.map((uid) => {
                            const contentType = contentTypes.find((contentType2) => contentType2.uid === uid);
                            return (contentType == null ? void 0 : contentType.info.displayName) ?? "";
                          }).join(", ")
                        })
                      }),
                      (0, import_jsx_runtime.jsx)(Table.Cell, {
                        children: (0, import_jsx_runtime.jsxs)(Flex, {
                          alignItems: "center",
                          justifyContent: "end",
                          children: [
                            canRead || canUpdate ? (0, import_jsx_runtime.jsx)(IconButton, {
                              tag: Link,
                              to: workflow.id.toString(),
                              label: formatMessage({
                                id: "Settings.review-workflows.list.page.list.column.actions.edit.label",
                                defaultMessage: "Edit {name}"
                              }, {
                                name: workflow.name
                              }),
                              variant: "ghost",
                              children: (0, import_jsx_runtime.jsx)(ForwardRef$1v, {})
                            }) : null,
                            workflows.length > 1 && canDelete ? (0, import_jsx_runtime.jsx)(IconButton, {
                              withTooltip: false,
                              label: formatMessage({
                                id: "Settings.review-workflows.list.page.list.column.actions.delete.label",
                                defaultMessage: "Delete {name}"
                              }, {
                                name: "Default workflow"
                              }),
                              variant: "ghost",
                              onClick: (e) => {
                                e.stopPropagation();
                                handleDeleteWorkflow(String(workflow.id));
                              },
                              children: (0, import_jsx_runtime.jsx)(ForwardRef$j, {})
                            }) : null
                          ]
                        })
                      })
                    ]
                  }, workflow.id))
                })
              ]
            })
          }),
          (0, import_jsx_runtime.jsx)(Dialog.Root, {
            open: !!workflowToDelete,
            onOpenChange: toggleConfirmDeleteDialog,
            children: (0, import_jsx_runtime.jsx)(ConfirmDialog, {
              onConfirm: handleConfirmDeleteDialog,
              children: formatMessage({
                id: "Settings.review-workflows.list.page.delete.confirm.body",
                defaultMessage: "If you remove this worfklow, all stage-related information will be removed for this content-type. Are you sure you want to remove it?"
              })
            })
          }),
          (0, import_jsx_runtime.jsxs)(LimitsModal.Root, {
            open: showLimitModal,
            onOpenChange: () => setShowLimitModal(false),
            children: [
              (0, import_jsx_runtime.jsx)(LimitsModal.Title, {
                children: formatMessage({
                  id: "Settings.review-workflows.list.page.workflows.limit.title",
                  defaultMessage: "You’ve reached the limit of workflows in your plan"
                })
              }),
              (0, import_jsx_runtime.jsx)(LimitsModal.Body, {
                children: formatMessage({
                  id: "Settings.review-workflows.list.page.workflows.limit.body",
                  defaultMessage: "Delete a workflow or contact Sales to enable more workflows."
                })
              })
            ]
          })
        ]
      })
    ]
  });
};
var ProtectedListPage = () => {
  const permissions = useTypedSelector((state) => {
    var _a, _b;
    return (_b = (_a = state.admin_app.permissions.settings) == null ? void 0 : _a["review-workflows"]) == null ? void 0 : _b.main;
  });
  return (0, import_jsx_runtime.jsx)(Page.Protect, {
    permissions,
    children: (0, import_jsx_runtime.jsx)(ReviewWorkflowsListView, {})
  });
};
export {
  ProtectedListPage,
  ReviewWorkflowsListView
};
//# sourceMappingURL=settings-5T447BGT.js.map
