{"version": 3, "sources": ["../../../@strapi/admin/admin/src/components/NpsSurvey.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {\n  Box,\n  Flex,\n  IconButton,\n  Button,\n  Typography,\n  Textarea,\n  Portal,\n  Field,\n  VisuallyHidden,\n} from '@strapi/design-system';\nimport { Cross } from '@strapi/icons';\nimport { Formik, Form } from 'formik';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\nimport * as yup from 'yup';\n\nimport { useAppInfo } from '../features/AppInfo';\nimport { useAuth } from '../features/Auth';\nimport { useNotification } from '../features/Notifications';\nimport { usePersistentState } from '../hooks/usePersistentState';\n\nconst FieldWrapper = styled(Field.Root)`\n  height: 3.2rem;\n  width: 3.2rem;\n\n  > label,\n  ~ input {\n    display: block;\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n  }\n\n  > label {\n    color: inherit;\n    cursor: pointer;\n    padding: ${({ theme }) => theme.spaces[2]};\n    text-align: center;\n    vertical-align: middle;\n  }\n\n  &:hover,\n  &:focus-within {\n    background-color: ${({ theme }) => theme.colors.neutral0};\n  }\n\n  &:active,\n  &.selected {\n    color: ${({ theme }) => theme.colors.primary700};\n    background-color: ${({ theme }) => theme.colors.neutral0};\n    border-color: ${({ theme }) => theme.colors.primary700};\n  }\n`;\n\nconst delays = {\n  postResponse: 90 * 24 * 60 * 60 * 1000, // 90 days in ms\n  postFirstDismissal: 14 * 24 * 60 * 60 * 1000, // 14 days in ms\n  postSubsequentDismissal: 90 * 24 * 60 * 60 * 1000, // 90 days in ms\n  display: 30 * 60 * 1000, // 30 minutes in ms\n};\n\nconst ratingArray = [...Array(11).keys()];\n\nconst checkIfShouldShowSurvey = (settings: NpsSurveySettings) => {\n  const { enabled, lastResponseDate, firstDismissalDate, lastDismissalDate } = settings;\n\n  // This function goes through all the cases where we'd want to not show the survey:\n  // 1. If the survey is disabled by strapi, abort mission, don't bother checking the other settings.\n  // 2. If the survey is disabled by user, abort mission, don't bother checking the other settings.\n  // 3. If the user has already responded to the survey, check if enough time has passed since the last response.\n  // 4. If the user has dismissed the survey twice or more before, check if enough time has passed since the last dismissal.\n  // 5. If the user has only dismissed the survey once before, check if enough time has passed since the first dismissal.\n  // If none of these cases check out, then we show the survey.\n  // Note that submitting a response resets the dismissal counts.\n  // Checks 4 and 5 should not be reversed, since the first dismissal will also exist if the user has dismissed the survey twice or more before.\n\n  // For users who had created an account before the NPS feature was introduced,\n  // we assume that they would have enabled the NPS feature if they had the chance.\n\n  // Global strapi disable for NSP.\n  if (window.strapi.flags.nps === false) {\n    return false;\n  }\n\n  // User chose not to enable the NPS feature when signing up\n  if (enabled === false) {\n    return false;\n  }\n\n  // The user has already responded to the survey\n  if (lastResponseDate) {\n    const timeSinceLastResponse = Date.now() - new Date(lastResponseDate).getTime();\n\n    if (timeSinceLastResponse >= delays.postResponse) {\n      return true;\n    }\n\n    return false;\n  }\n\n  // The user has dismissed the survey twice or more before\n  if (lastDismissalDate) {\n    const timeSinceLastDismissal = Date.now() - new Date(lastDismissalDate).getTime();\n\n    if (timeSinceLastDismissal >= delays.postSubsequentDismissal) {\n      return true;\n    }\n\n    return false;\n  }\n\n  // The user has only dismissed the survey once before\n  if (firstDismissalDate) {\n    const timeSinceFirstDismissal = Date.now() - new Date(firstDismissalDate).getTime();\n\n    if (timeSinceFirstDismissal >= delays.postFirstDismissal) {\n      return true;\n    }\n\n    return false;\n  }\n\n  // The user has not interacted with the survey before\n  return true;\n};\n\nconst NpsSurvey = () => {\n  const { formatMessage } = useIntl();\n  const { npsSurveySettings, setNpsSurveySettings } = useNpsSurveySettings();\n  const [isFeedbackResponse, setIsFeedbackResponse] = React.useState(false);\n  const { toggleNotification } = useNotification();\n  const currentEnvironment = useAppInfo('NpsSurvey', (state) => state.currentEnvironment);\n  const strapiVersion = useAppInfo('NpsSurvey', (state) => state.strapiVersion);\n\n  interface NpsSurveyMutationBody {\n    email: string;\n    rating: number | null;\n    comment: string;\n    environment?: string;\n    version?: string;\n    license: 'Enterprise' | 'Community';\n  }\n\n  // Only check on first render if the survey should be shown\n  const [surveyIsShown, setSurveyIsShown] = React.useState(\n    checkIfShouldShowSurvey(npsSurveySettings)\n  );\n\n  // Set a cooldown to show the survey when session begins\n  const [displaySurvey, setDisplaySurvey] = React.useState(false);\n\n  React.useEffect(() => {\n    const displayTime = setTimeout(() => {\n      setDisplaySurvey(true);\n    }, delays.display);\n\n    return () => {\n      clearTimeout(displayTime);\n    };\n  }, []);\n\n  const { user } = useAuth('NpsSurvey', (auth) => auth);\n\n  if (!displaySurvey) {\n    return null;\n  }\n\n  if (!surveyIsShown) {\n    return null;\n  }\n\n  const handleSubmitResponse = async ({\n    npsSurveyRating,\n    npsSurveyFeedback,\n  }: {\n    npsSurveyRating: NpsSurveyMutationBody['rating'];\n    npsSurveyFeedback: NpsSurveyMutationBody['comment'];\n  }) => {\n    try {\n      const body = {\n        email: typeof user === 'object' && user.email ? user.email : '',\n        rating: npsSurveyRating,\n        comment: npsSurveyFeedback,\n        environment: currentEnvironment,\n        version: strapiVersion ?? undefined,\n        license: window.strapi.projectType,\n        isHostedOnStrapiCloud: process.env.STRAPI_HOSTING === 'strapi.cloud',\n      };\n      const res = await fetch('https://analytics.strapi.io/submit-nps', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(body),\n      });\n\n      if (!res.ok) {\n        throw new Error('Failed to submit NPS survey');\n      }\n\n      setNpsSurveySettings((settings) => ({\n        ...settings,\n        lastResponseDate: new Date().toString(),\n        firstDismissalDate: null,\n        lastDismissalDate: null,\n      }));\n      setIsFeedbackResponse(true);\n      // Thank you message displayed in the banner should disappear after few seconds.\n      setTimeout(() => {\n        setSurveyIsShown(false);\n      }, 3000);\n    } catch (err) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occurred' }),\n      });\n    }\n  };\n\n  const handleDismiss = () => {\n    setNpsSurveySettings((settings) => {\n      const nextSettings = {\n        ...settings,\n        lastResponseDate: null,\n      };\n\n      if (settings.firstDismissalDate) {\n        // If the user dismisses the survey for the second time\n        nextSettings.lastDismissalDate = new Date().toString();\n      } else {\n        // If the user dismisses the survey for the first time\n        nextSettings.firstDismissalDate = new Date().toString();\n      }\n\n      return nextSettings;\n    });\n\n    setSurveyIsShown(false);\n  };\n\n  return (\n    <Portal>\n      <Formik\n        initialValues={{ npsSurveyFeedback: '', npsSurveyRating: null }}\n        onSubmit={handleSubmitResponse}\n        validationSchema={yup.object({\n          npsSurveyFeedback: yup.string(),\n          npsSurveyRating: yup.number().required(),\n        })}\n      >\n        {({ values, handleChange, setFieldValue, isSubmitting }) => (\n          <Form name=\"npsSurveyForm\">\n            <Flex\n              hasRadius\n              direction=\"column\"\n              padding={4}\n              borderColor=\"primary200\"\n              background=\"neutral0\"\n              shadow=\"popupShadow\"\n              position=\"fixed\"\n              bottom={0}\n              left=\"50%\"\n              transform=\"translateX(-50%)\"\n              zIndex=\"200\"\n              width=\"50%\"\n            >\n              {isFeedbackResponse ? (\n                <Typography fontWeight=\"semiBold\">\n                  {formatMessage({\n                    id: 'app.components.NpsSurvey.feedback-response',\n                    defaultMessage: 'Thank you very much for your feedback!',\n                  })}\n                </Typography>\n              ) : (\n                <Box tag=\"fieldset\" width=\"100%\" borderWidth={0}>\n                  <Flex justifyContent=\"space-between\" width=\"100%\">\n                    <Box marginLeft=\"auto\" marginRight=\"auto\">\n                      <Typography fontWeight=\"semiBold\" tag=\"legend\">\n                        {formatMessage({\n                          id: 'app.components.NpsSurvey.banner-title',\n                          defaultMessage:\n                            'How likely are you to recommend Strapi to a friend or colleague?',\n                        })}\n                      </Typography>\n                    </Box>\n                    <IconButton\n                      onClick={handleDismiss}\n                      withTooltip={false}\n                      label={formatMessage({\n                        id: 'app.components.NpsSurvey.dismiss-survey-label',\n                        defaultMessage: 'Dismiss survey',\n                      })}\n                    >\n                      <Cross />\n                    </IconButton>\n                  </Flex>\n                  <Flex gap={2} marginTop={2} marginBottom={2} justifyContent=\"center\">\n                    <Typography variant=\"pi\" textColor=\"neutral600\">\n                      {formatMessage({\n                        id: 'app.components.NpsSurvey.no-recommendation',\n                        defaultMessage: 'Not at all likely',\n                      })}\n                    </Typography>\n                    {ratingArray.map((number) => {\n                      return (\n                        <FieldWrapper\n                          key={number}\n                          name=\"npsSurveyRating\"\n                          className={values.npsSurveyRating === number ? 'selected' : undefined} // \"selected\" class added when child radio button is checked\n                          hasRadius\n                          background=\"primary100\"\n                          borderColor=\"primary200\"\n                          color=\"primary600\"\n                          position=\"relative\"\n                          cursor=\"pointer\"\n                        >\n                          <Field.Label>\n                            <VisuallyHidden>\n                              <Field.Input\n                                type=\"radio\"\n                                checked={values.npsSurveyRating === number}\n                                onChange={(e) =>\n                                  setFieldValue('npsSurveyRating', parseInt(e.target.value, 10))\n                                }\n                                value={number}\n                              />\n                            </VisuallyHidden>\n                            {number}\n                          </Field.Label>\n                        </FieldWrapper>\n                      );\n                    })}\n                    <Typography variant=\"pi\" textColor=\"neutral600\">\n                      {formatMessage({\n                        id: 'app.components.NpsSurvey.happy-to-recommend',\n                        defaultMessage: 'Extremely likely',\n                      })}\n                    </Typography>\n                  </Flex>\n                  {values.npsSurveyRating !== null && (\n                    <Flex direction=\"column\">\n                      <Box marginTop={2}>\n                        <Field.Label fontWeight=\"semiBold\" fontSize={2}>\n                          {formatMessage({\n                            id: 'app.components.NpsSurvey.feedback-question',\n                            defaultMessage: 'Do you have any suggestion for improvements?',\n                          })}\n                        </Field.Label>\n                      </Box>\n                      <Box width=\"62%\" marginTop={3} marginBottom={4}>\n                        <Textarea\n                          id=\"npsSurveyFeedback\" // formik element attribute \"id\" should be same as the values key to work\n                          width=\"100%\"\n                          onChange={handleChange}\n                          value={values.npsSurveyFeedback}\n                        />\n                      </Box>\n                      <Button marginBottom={2} type=\"submit\" loading={isSubmitting}>\n                        {formatMessage({\n                          id: 'app.components.NpsSurvey.submit-feedback',\n                          defaultMessage: 'Submit Feedback',\n                        })}\n                      </Button>\n                    </Flex>\n                  )}\n                </Box>\n              )}\n            </Flex>\n          </Form>\n        )}\n      </Formik>\n    </Portal>\n  );\n};\n\ninterface NpsSurveySettings {\n  enabled: boolean;\n  lastResponseDate: string | null;\n  firstDismissalDate: string | null;\n  lastDismissalDate: string | null;\n}\n\n/**\n * We exported to make it available during admin user registration.\n * Because we only enable the NPS for users who subscribe to the newsletter when signing up\n */\nfunction useNpsSurveySettings() {\n  const [npsSurveySettings, setNpsSurveySettings] = usePersistentState<NpsSurveySettings>(\n    'STRAPI_NPS_SURVEY_SETTINGS',\n    {\n      enabled: true,\n      lastResponseDate: null,\n      firstDismissalDate: null,\n      lastDismissalDate: null,\n    }\n  );\n\n  /**\n   * TODO: should this just be an array so we can alias the `usePersistentState` hook?\n   */\n  return { npsSurveySettings, setNpsSurveySettings };\n}\n\nexport { NpsSurvey, useNpsSurveySettings };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,IAAMA,eAAeC,GAAOC,MAAMC,IAAI;;;;;;;;;;;;;;;;;eAiBvB,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAO,CAAA,CAAE;;;;;;;wBAOrB,CAAC,EAAED,MAAK,MAAOA,MAAME,OAAOC,QAAQ;;;;;aAK/C,CAAC,EAAEH,MAAK,MAAOA,MAAME,OAAOE,UAAU;wBAC3B,CAAC,EAAEJ,MAAK,MAAOA,MAAME,OAAOC,QAAQ;oBACxC,CAAC,EAAEH,MAAK,MAAOA,MAAME,OAAOE,UAAU;;;AAI1D,IAAMC,SAAS;EACbC,cAAc,KAAK,KAAK,KAAK,KAAK;EAClCC,oBAAoB,KAAK,KAAK,KAAK,KAAK;EACxCC,yBAAyB,KAAK,KAAK,KAAK,KAAK;EAC7CC,SAAS,KAAK,KAAK;AACrB;AAEA,IAAMC,cAAc;EAAIC,GAAAA,MAAM,EAAA,EAAIC,KAAI;AAAG;AAEzC,IAAMC,0BAA0B,CAACC,aAAAA;AAC/B,QAAM,EAAEC,SAASC,kBAAkBC,oBAAoBC,kBAAiB,IAAKJ;AAgB7E,MAAIK,OAAOC,OAAOC,MAAMC,QAAQ,OAAO;AACrC,WAAO;EACT;AAGA,MAAIP,YAAY,OAAO;AACrB,WAAO;EACT;AAGA,MAAIC,kBAAkB;AACpB,UAAMO,wBAAwBC,KAAKC,IAAG,IAAK,IAAID,KAAKR,gBAAAA,EAAkBU,QAAO;AAE7E,QAAIH,yBAAyBlB,OAAOC,cAAc;AAChD,aAAO;IACT;AAEA,WAAO;EACT;AAGA,MAAIY,mBAAmB;AACrB,UAAMS,yBAAyBH,KAAKC,IAAG,IAAK,IAAID,KAAKN,iBAAAA,EAAmBQ,QAAO;AAE/E,QAAIC,0BAA0BtB,OAAOG,yBAAyB;AAC5D,aAAO;IACT;AAEA,WAAO;EACT;AAGA,MAAIS,oBAAoB;AACtB,UAAMW,0BAA0BJ,KAAKC,IAAG,IAAK,IAAID,KAAKP,kBAAAA,EAAoBS,QAAO;AAEjF,QAAIE,2BAA2BvB,OAAOE,oBAAoB;AACxD,aAAO;IACT;AAEA,WAAO;EACT;AAGA,SAAO;AACT;AAEA,IAAMsB,YAAY,MAAA;AAChB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,mBAAmBC,qBAAoB,IAAKC,qBAAAA;AACpD,QAAM,CAACC,oBAAoBC,qBAAAA,IAA+BC,eAAS,KAAA;AACnE,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAMC,qBAAqBC,WAAW,aAAa,CAACC,UAAUA,MAAMF,kBAAkB;AACtF,QAAMG,gBAAgBF,WAAW,aAAa,CAACC,UAAUA,MAAMC,aAAa;AAY5E,QAAM,CAACC,eAAeC,gBAAAA,IAA0BR,eAC9CxB,wBAAwBmB,iBAAAA,CAAAA;AAI1B,QAAM,CAACc,eAAeC,gBAAAA,IAA0BV,eAAS,KAAA;AAEzDW,EAAMC,gBAAU,MAAA;AACd,UAAMC,cAAcC,WAAW,MAAA;AAC7BJ,uBAAiB,IAAA;IACnB,GAAG1C,OAAOI,OAAO;AAEjB,WAAO,MAAA;AACL2C,mBAAaF,WAAAA;IACf;EACF,GAAG,CAAA,CAAE;AAEL,QAAM,EAAEG,KAAI,IAAKC,QAAQ,aAAa,CAACC,SAASA,IAAAA;AAEhD,MAAI,CAACT,eAAe;AAClB,WAAO;EACT;AAEA,MAAI,CAACF,eAAe;AAClB,WAAO;EACT;AAEA,QAAMY,uBAAuB,OAAO,EAClCC,iBACAC,kBAAiB,MAIlB;AACC,QAAI;AACF,YAAMC,OAAO;QACXC,OAAO,OAAOP,SAAS,YAAYA,KAAKO,QAAQP,KAAKO,QAAQ;QAC7DC,QAAQJ;QACRK,SAASJ;QACTK,aAAavB;QACbwB,SAASrB,iBAAiBsB;QAC1BC,SAAS/C,OAAOC,OAAO+C;QACvBC,uBAAuBC,QAAQC,IAAIC,mBAAmB;MACxD;AACA,YAAMC,MAAM,MAAMC,MAAM,0CAA0C;QAChEC,QAAQ;QACRC,SAAS;UACP,gBAAgB;QAClB;QACAhB,MAAMiB,KAAKC,UAAUlB,IAAAA;MACvB,CAAA;AAEA,UAAI,CAACa,IAAIM,IAAI;AACX,cAAM,IAAIC,MAAM,6BAAA;MAClB;AAEA9C,2BAAqB,CAACnB,cAAc;QAClC,GAAGA;QACHE,mBAAkB,oBAAIQ,KAAAA,GAAOwD,SAAQ;QACrC/D,oBAAoB;QACpBC,mBAAmB;QACrB;AACAkB,4BAAsB,IAAA;AAEtBe,iBAAW,MAAA;AACTN,yBAAiB,KAAA;SAChB,GAAA;IACL,SAASoC,KAAK;AACZ3C,yBAAmB;QACjB4C,MAAM;QACNC,SAASrD,cAAc;UAAEsD,IAAI;UAAsBC,gBAAgB;QAAoB,CAAA;MACzF,CAAA;IACF;EACF;AAEA,QAAMC,gBAAgB,MAAA;AACpBrD,yBAAqB,CAACnB,aAAAA;AACpB,YAAMyE,eAAe;QACnB,GAAGzE;QACHE,kBAAkB;MACpB;AAEA,UAAIF,SAASG,oBAAoB;AAE/BsE,qBAAarE,qBAAoB,oBAAIM,KAAAA,GAAOwD,SAAQ;aAC/C;AAELO,qBAAatE,sBAAqB,oBAAIO,KAAAA,GAAOwD,SAAQ;MACvD;AAEA,aAAOO;IACT,CAAA;AAEA1C,qBAAiB,KAAA;EACnB;AAEA,aACE2C,wBAACC,UAAAA;IACC,cAAAD,wBAACE,QAAAA;MACCC,eAAe;QAAEjC,mBAAmB;QAAID,iBAAiB;MAAK;MAC9DmC,UAAUpC;MACVqC,kBAAsBC,QAAO;QAC3BpC,mBAAuBqC,OAAM;QAC7BtC,iBAAqBuC,QAAM,EAAGC,SAAQ;MACxC,CAAA;gBAEC,CAAC,EAAEC,QAAQC,cAAcC,eAAeC,aAAY,UACnDb,wBAACc,MAAAA;QAAKC,MAAK;QACT,cAAAf,wBAACgB,MAAAA;UACCC,WAAS;UACTC,WAAU;UACVC,SAAS;UACTC,aAAY;UACZC,YAAW;UACXC,QAAO;UACPC,UAAS;UACTC,QAAQ;UACRC,MAAK;UACLC,WAAU;UACVC,QAAO;UACPC,OAAM;UAELjF,UAAAA,yBACCqD,wBAAC6B,YAAAA;YAAWC,YAAW;sBACpBxF,cAAc;cACbsD,IAAI;cACJC,gBAAgB;YAClB,CAAA;mBAGFkC,yBAACC,KAAAA;YAAIC,KAAI;YAAWL,OAAM;YAAOM,aAAa;;kBAC5CH,yBAACf,MAAAA;gBAAKmB,gBAAe;gBAAgBP,OAAM;;sBACzC5B,wBAACgC,KAAAA;oBAAII,YAAW;oBAAOC,aAAY;oBACjC,cAAArC,wBAAC6B,YAAAA;sBAAWC,YAAW;sBAAWG,KAAI;gCACnC3F,cAAc;wBACbsD,IAAI;wBACJC,gBACE;sBACJ,CAAA;;;sBAGJG,wBAACsC,YAAAA;oBACCC,SAASzC;oBACT0C,aAAa;oBACbC,OAAOnG,cAAc;sBACnBsD,IAAI;sBACJC,gBAAgB;oBAClB,CAAA;oBAEA,cAAAG,wBAAC0C,eAAAA,CAAAA,CAAAA;;;;kBAGLX,yBAACf,MAAAA;gBAAK2B,KAAK;gBAAGC,WAAW;gBAAGC,cAAc;gBAAGV,gBAAe;;sBAC1DnC,wBAAC6B,YAAAA;oBAAWiB,SAAQ;oBAAKC,WAAU;8BAChCzG,cAAc;sBACbsD,IAAI;sBACJC,gBAAgB;oBAClB,CAAA;;kBAED3E,YAAY8H,IAAI,CAACxC,WAAAA;AAChB,+BACER,wBAAC5F,cAAAA;sBAEC2G,MAAK;sBACLkC,WAAWvC,OAAOzC,oBAAoBuC,SAAS,aAAa/B;sBAC5DwC,WAAS;sBACTI,YAAW;sBACXD,aAAY;sBACZ8B,OAAM;sBACN3B,UAAS;sBACT4B,QAAO;oCAEPpB,yBAACzH,MAAM8I,OAAK;;8BACVpD,wBAACqD,gBAAAA;0CACCrD,wBAAC1F,MAAMgJ,OAAK;8BACV5D,MAAK;8BACL6D,SAAS7C,OAAOzC,oBAAoBuC;8BACpCgD,UAAU,CAACC,MACT7C,cAAc,mBAAmB8C,SAASD,EAAEE,OAAOC,OAAO,EAAA,CAAA;8BAE5DA,OAAOpD;;;0BAGVA;;;oBArBEA,GAAAA,MAAAA;kBAyBX,CAAA;sBACAR,wBAAC6B,YAAAA;oBAAWiB,SAAQ;oBAAKC,WAAU;8BAChCzG,cAAc;sBACbsD,IAAI;sBACJC,gBAAgB;oBAClB,CAAA;;;;cAGHa,OAAOzC,oBAAoB,YAC1B8D,yBAACf,MAAAA;gBAAKE,WAAU;;sBACdlB,wBAACgC,KAAAA;oBAAIY,WAAW;kCACd5C,wBAAC1F,MAAM8I,OAAK;sBAACtB,YAAW;sBAAW+B,UAAU;gCAC1CvH,cAAc;wBACbsD,IAAI;wBACJC,gBAAgB;sBAClB,CAAA;;;sBAGJG,wBAACgC,KAAAA;oBAAIJ,OAAM;oBAAMgB,WAAW;oBAAGC,cAAc;oBAC3C,cAAA7C,wBAAC8D,UAAAA;sBACClE,IAAG;sBACHgC,OAAM;sBACN4B,UAAU7C;sBACViD,OAAOlD,OAAOxC;;;sBAGlB8B,wBAAC+D,QAAAA;oBAAOlB,cAAc;oBAAGnD,MAAK;oBAASsE,SAASnD;8BAC7CvE,cAAc;sBACbsD,IAAI;sBACJC,gBAAgB;oBAClB,CAAA;;;;;;;;;;AAYxB;AAaA,SAASnD,uBAAAA;AACP,QAAM,CAACF,mBAAmBC,oBAAqB,IAAGwH,mBAChD,8BACA;IACE1I,SAAS;IACTC,kBAAkB;IAClBC,oBAAoB;IACpBC,mBAAmB;EACrB,CAAA;AAMF,SAAO;IAAEc;IAAmBC;EAAqB;AACnD;", "names": ["FieldWrapper", "styled", "Field", "Root", "theme", "spaces", "colors", "neutral0", "primary700", "delays", "postResponse", "postFirstDismissal", "postSubsequentDismissal", "display", "ratingArray", "Array", "keys", "checkIfShouldShowSurvey", "settings", "enabled", "lastResponseDate", "firstDismissalDate", "lastDismissalDate", "window", "strapi", "flags", "nps", "timeSinceLastResponse", "Date", "now", "getTime", "timeSinceLastDismissal", "timeSinceFirstDismissal", "NpsSurvey", "formatMessage", "useIntl", "npsSurveySettings", "setNpsSurveySettings", "useNpsSurveySettings", "isFeedbackResponse", "setIsFeedbackResponse", "useState", "toggleNotification", "useNotification", "currentEnvironment", "useAppInfo", "state", "strapiVersion", "surveyIsShown", "setSurveyIsShown", "displaySurvey", "setDisplaySurvey", "React", "useEffect", "displayTime", "setTimeout", "clearTimeout", "user", "useAuth", "auth", "handleSubmitResponse", "npsSurveyRating", "npsSurveyFeedback", "body", "email", "rating", "comment", "environment", "version", "undefined", "license", "projectType", "isHostedOnStrapiCloud", "process", "env", "STRAPI_HOSTING", "res", "fetch", "method", "headers", "JSON", "stringify", "ok", "Error", "toString", "err", "type", "message", "id", "defaultMessage", "handle<PERSON><PERSON><PERSON>", "nextSettings", "_jsx", "Portal", "<PERSON><PERSON>", "initialValues", "onSubmit", "validationSchema", "object", "string", "number", "required", "values", "handleChange", "setFieldValue", "isSubmitting", "Form", "name", "Flex", "hasRadius", "direction", "padding", "borderColor", "background", "shadow", "position", "bottom", "left", "transform", "zIndex", "width", "Typography", "fontWeight", "_jsxs", "Box", "tag", "borderWidth", "justifyContent", "marginLeft", "marginRight", "IconButton", "onClick", "withTooltip", "label", "Cross", "gap", "marginTop", "marginBottom", "variant", "textColor", "map", "className", "color", "cursor", "Label", "VisuallyHidden", "Input", "checked", "onChange", "e", "parseInt", "target", "value", "fontSize", "Textarea", "<PERSON><PERSON>", "loading", "usePersistentState"]}