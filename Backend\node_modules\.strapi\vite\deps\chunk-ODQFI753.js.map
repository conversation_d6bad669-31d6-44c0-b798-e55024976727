{"version": 3, "sources": ["../../../@strapi/admin/admin/src/features/StrapiApp.tsx", "../../../@strapi/admin/admin/src/hooks/useQueryParams.ts", "../../../@strapi/admin/admin/src/reducer.ts", "../../../@strapi/admin/admin/src/services/auth.ts", "../../../@strapi/admin/admin/src/features/Auth.tsx"], "sourcesContent": ["import { createContext } from '../components/Context';\nimport { RBAC } from '../core/apis/rbac';\nimport { Router } from '../core/apis/router';\n\nimport type { StrapiApp } from '../StrapiApp';\n\n/* -------------------------------------------------------------------------------------------------\n * StrapiApp\n * -----------------------------------------------------------------------------------------------*/\ninterface StrapiAppContextValue\n  extends Pick<\n      StrapiApp,\n      | 'customFields'\n      | 'getPlugin'\n      | 'getAdminInjectedComponents'\n      | 'plugins'\n      | 'runHookParallel'\n      | 'runHookSeries'\n      | 'widgets'\n    >,\n    Pick<Router, 'menu' | 'settings'> {\n  components: StrapiApp['library']['components'];\n  fields: StrapiApp['library']['fields'];\n  rbac: RBAC;\n  runHookWaterfall: <TData>(\n    name: Parameters<StrapiApp['runHookWaterfall']>[0],\n    initialValue: TData\n  ) => TData;\n}\n\nconst [StrapiAppProvider, useStrapiApp] = createContext<StrapiAppContextValue>('StrapiApp');\n\nexport { StrapiAppProvider, useStrapiApp };\nexport type { StrapiAppContextValue };\n", "import { useCallback, useMemo } from 'react';\n\nimport { parse, stringify } from 'qs';\nimport { useNavigate, useLocation } from 'react-router-dom';\n\nconst useSearch = () => {\n  const { search } = useLocation();\n\n  return useMemo(() => search, [search]);\n};\n\nconst useQueryParams = <TQuery extends object>(initialParams?: TQuery) => {\n  const search = useSearch();\n  const navigate = useNavigate();\n\n  const query = useMemo(() => {\n    // TODO: investigate why sometimes we're getting the search with a leading `?` and sometimes not.\n    const searchQuery = search.startsWith('?') ? search.slice(1) : search;\n    if (!search && initialParams) {\n      return initialParams;\n    }\n\n    return { ...initialParams, ...parse(searchQuery) } as TQuery;\n  }, [search, initialParams]);\n\n  const setQuery = useCallback(\n    (nextParams: TQuery, method: 'push' | 'remove' = 'push', replace = false) => {\n      let nextQuery = { ...query };\n\n      if (method === 'remove') {\n        Object.keys(nextParams).forEach((key) => {\n          if (Object.prototype.hasOwnProperty.call(nextQuery, key)) {\n            // @ts-expect-error – this is fine, if you want to fix it, please do.\n            delete nextQuery[key];\n          }\n        });\n      } else {\n        nextQuery = { ...query, ...nextParams };\n      }\n\n      navigate({ search: stringify(nextQuery, { encode: false }) }, { replace });\n    },\n    [navigate, query]\n  );\n\n  return [{ query, rawQuery: search }, setQuery] as const;\n};\n\nexport { useQueryParams };\n", "import { createSlice } from '@reduxjs/toolkit';\n\nimport { PermissionMap } from './types/permissions';\nimport { getCookieValue, setCookie, deleteCookie } from './utils/cookies';\n\nimport type { PayloadAction } from '@reduxjs/toolkit';\n\ntype ThemeName = 'light' | 'dark' | 'system';\n\ninterface AppState {\n  language: {\n    locale: string;\n    localeNames: Record<string, string>;\n  };\n  permissions: Partial<PermissionMap>;\n  theme: {\n    currentTheme: ThemeName;\n    availableThemes: string[];\n  };\n  token?: string | null;\n}\n\nconst STORAGE_KEYS = {\n  TOKEN: 'jwtToken',\n  STATUS: 'isLoggedIn',\n};\n\nconst THEME_LOCAL_STORAGE_KEY = 'STRAPI_THEME';\nconst LANGUAGE_LOCAL_STORAGE_KEY = 'strapi-admin-language';\n\nexport const getStoredToken = (): string | null => {\n  const fromLocalStorage = localStorage.getItem(STORAGE_KEYS.TOKEN);\n  if (fromLocalStorage) {\n    return JSON.parse(fromLocalStorage);\n  }\n\n  const fromCookie = getCookieValue(STORAGE_KEYS.TOKEN);\n  return fromCookie ?? null;\n};\n\nconst adminSlice = createSlice({\n  name: 'admin',\n  initialState: () => {\n    return {\n      language: {\n        locale: 'en',\n        localeNames: { en: 'English' },\n      },\n      permissions: {},\n      theme: {\n        availableThemes: [],\n        currentTheme: localStorage.getItem(THEME_LOCAL_STORAGE_KEY) || 'system',\n      },\n      token: null,\n    } as AppState;\n  },\n  reducers: {\n    setAppTheme(state, action: PayloadAction<ThemeName>) {\n      state.theme.currentTheme = action.payload;\n      window.localStorage.setItem(THEME_LOCAL_STORAGE_KEY, action.payload);\n    },\n    setAvailableThemes(state, action: PayloadAction<AppState['theme']['availableThemes']>) {\n      state.theme.availableThemes = action.payload;\n    },\n    setLocale(state, action: PayloadAction<string>) {\n      state.language.locale = action.payload;\n\n      window.localStorage.setItem(LANGUAGE_LOCAL_STORAGE_KEY, action.payload);\n      document.documentElement.setAttribute('lang', action.payload);\n    },\n    setToken(state, action: PayloadAction<string | null>) {\n      state.token = action.payload;\n    },\n    login(state, action: PayloadAction<{ token: string; persist?: boolean }>) {\n      const { token, persist } = action.payload;\n\n      if (!persist) {\n        setCookie(STORAGE_KEYS.TOKEN, token);\n      } else {\n        window.localStorage.setItem(STORAGE_KEYS.TOKEN, JSON.stringify(token));\n      }\n      window.localStorage.setItem(STORAGE_KEYS.STATUS, 'true');\n      state.token = token;\n    },\n    logout(state) {\n      state.token = null;\n      deleteCookie(STORAGE_KEYS.TOKEN);\n      window.localStorage.removeItem(STORAGE_KEYS.TOKEN);\n      window.localStorage.removeItem(STORAGE_KEYS.STATUS);\n    },\n  },\n});\n\nconst reducer = adminSlice.reducer;\n\nexport const { setAppTheme, setAvailableThemes, setLocale, setToken, logout, login } =\n  adminSlice.actions;\n\nexport { reducer, THEME_LOCAL_STORAGE_KEY, LANGUAGE_LOCAL_STORAGE_KEY };\nexport type { AppState, ThemeName };\n", "import { ProvidersOptions } from '../../../shared/contracts/admin';\nimport {\n  type RenewToken,\n  type Login,\n  type ResetPassword,\n  type RegisterAdmin,\n  type Register,\n  type RegistrationInfo,\n  ForgotPassword,\n} from '../../../shared/contracts/authentication';\nimport { Check } from '../../../shared/contracts/permissions';\nimport { GetProviders, IsSSOLocked } from '../../../shared/contracts/providers';\nimport { type GetOwnPermissions, type GetMe, type UpdateMe } from '../../../shared/contracts/users';\n\nimport { adminApi } from './api';\n\nconst authService = adminApi\n  .enhanceEndpoints({\n    addTagTypes: ['User', 'Me', 'ProvidersOptions'],\n  })\n  .injectEndpoints({\n    endpoints: (builder) => ({\n      /**\n       * ME\n       */\n      getMe: builder.query<GetMe.Response['data'], void>({\n        query: () => ({\n          method: 'GET',\n          url: '/admin/users/me',\n        }),\n        transformResponse(res: GetMe.Response) {\n          return res.data;\n        },\n        providesTags: (res) => (res ? ['Me', { type: 'User', id: res.id }] : ['Me']),\n      }),\n      getMyPermissions: builder.query<GetOwnPermissions.Response['data'], void>({\n        query: () => ({\n          method: 'GET',\n          url: '/admin/users/me/permissions',\n        }),\n        transformResponse(res: GetOwnPermissions.Response) {\n          return res.data;\n        },\n      }),\n      updateMe: builder.mutation<UpdateMe.Response['data'], UpdateMe.Request['body']>({\n        query: (body) => ({\n          method: 'PUT',\n          url: '/admin/users/me',\n          data: body,\n        }),\n        transformResponse(res: UpdateMe.Response) {\n          return res.data;\n        },\n        invalidatesTags: ['Me'],\n      }),\n      /**\n       * Permissions\n       */\n      checkPermissions: builder.query<Check.Response, Check.Request['body']>({\n        query: (permissions) => ({\n          method: 'POST',\n          url: '/admin/permissions/check',\n          data: permissions,\n        }),\n      }),\n      /**\n       * Auth methods\n       */\n      login: builder.mutation<Login.Response['data'], Login.Request['body']>({\n        query: (body) => ({\n          method: 'POST',\n          url: '/admin/login',\n          data: body,\n        }),\n        transformResponse(res: Login.Response) {\n          return res.data;\n        },\n        invalidatesTags: ['Me'],\n      }),\n      logout: builder.mutation<void, void>({\n        query: () => ({\n          method: 'POST',\n          url: '/admin/logout',\n        }),\n      }),\n      resetPassword: builder.mutation<\n        ResetPassword.Response['data'],\n        ResetPassword.Request['body']\n      >({\n        query: (body) => ({\n          method: 'POST',\n          url: '/admin/reset-password',\n          data: body,\n        }),\n        transformResponse(res: ResetPassword.Response) {\n          return res.data;\n        },\n      }),\n      renewToken: builder.mutation<RenewToken.Response['data'], RenewToken.Request['body']>({\n        query: (body) => ({\n          method: 'POST',\n          url: '/admin/renew-token',\n          data: body,\n        }),\n        transformResponse(res: RenewToken.Response) {\n          return res.data;\n        },\n      }),\n      getRegistrationInfo: builder.query<\n        RegistrationInfo.Response['data'],\n        RegistrationInfo.Request['query']['registrationToken']\n      >({\n        query: (registrationToken) => ({\n          url: '/admin/registration-info',\n          method: 'GET',\n          config: {\n            params: {\n              registrationToken,\n            },\n          },\n        }),\n        transformResponse(res: RegistrationInfo.Response) {\n          return res.data;\n        },\n      }),\n      registerAdmin: builder.mutation<\n        RegisterAdmin.Response['data'],\n        RegisterAdmin.Request['body']\n      >({\n        query: (body) => ({\n          method: 'POST',\n          url: '/admin/register-admin',\n          data: body,\n        }),\n        transformResponse(res: RegisterAdmin.Response) {\n          return res.data;\n        },\n      }),\n      registerUser: builder.mutation<Register.Response['data'], Register.Request['body']>({\n        query: (body) => ({\n          method: 'POST',\n          url: '/admin/register',\n          data: body,\n        }),\n        transformResponse(res: Register.Response) {\n          return res.data;\n        },\n      }),\n      forgotPassword: builder.mutation<ForgotPassword.Response, ForgotPassword.Request['body']>({\n        query: (body) => ({\n          url: '/admin/forgot-password',\n          method: 'POST',\n          data: body,\n        }),\n      }),\n      isSSOLocked: builder.query<IsSSOLocked.Response['data'], void>({\n        query: () => ({\n          url: '/admin/providers/isSSOLocked',\n          method: 'GET',\n        }),\n        transformResponse(res: IsSSOLocked.Response) {\n          return res.data;\n        },\n      }),\n      getProviders: builder.query<GetProviders.Response, void>({\n        query: () => ({\n          url: '/admin/providers',\n          method: 'GET',\n        }),\n      }),\n      getProviderOptions: builder.query<ProvidersOptions.Response['data'], void>({\n        query: () => ({\n          url: '/admin/providers/options',\n          method: 'GET',\n        }),\n        transformResponse(res: ProvidersOptions.Response) {\n          return res.data;\n        },\n        providesTags: ['ProvidersOptions'],\n      }),\n      updateProviderOptions: builder.mutation<\n        ProvidersOptions.Response['data'],\n        ProvidersOptions.Request['body']\n      >({\n        query: (body) => ({\n          url: '/admin/providers/options',\n          method: 'PUT',\n          data: body,\n        }),\n        transformResponse(res: ProvidersOptions.Response) {\n          return res.data;\n        },\n        invalidatesTags: ['ProvidersOptions'],\n      }),\n    }),\n    overrideExisting: false,\n  });\n\nconst {\n  useCheckPermissionsQuery,\n  useLazyCheckPermissionsQuery,\n  useGetMeQuery,\n  useLoginMutation,\n  useRenewTokenMutation,\n  useLogoutMutation,\n  useUpdateMeMutation,\n  useResetPasswordMutation,\n  useRegisterAdminMutation,\n  useRegisterUserMutation,\n  useGetRegistrationInfoQuery,\n  useForgotPasswordMutation,\n  useGetMyPermissionsQuery,\n  useIsSSOLockedQuery,\n  useGetProvidersQuery,\n  useGetProviderOptionsQuery,\n  useUpdateProviderOptionsMutation,\n} = authService;\n\nexport {\n  useCheckPermissionsQuery,\n  useLazyCheckPermissionsQuery,\n  useGetMeQuery,\n  useLoginMutation,\n  useRenewTokenMutation,\n  useLogoutMutation,\n  useUpdateMeMutation,\n  useResetPasswordMutation,\n  useRegisterAdminMutation,\n  useRegisterUserMutation,\n  useGetRegistrationInfoQuery,\n  useForgotPasswordMutation,\n  useGetMyPermissionsQuery,\n  useIsSSOLockedQuery,\n  useGetProvidersQuery,\n  useGetProviderOptionsQuery,\n  useUpdateProviderOptionsMutation,\n};\n", "import * as React from 'react';\n\nimport { useLocation, useNavigate } from 'react-router-dom';\n\nimport { Login } from '../../../shared/contracts/authentication';\nimport { createContext } from '../components/Context';\nimport { useTypedDispatch, useTypedSelector } from '../core/store/hooks';\nimport { useStrapiApp } from '../features/StrapiApp';\nimport { useQueryParams } from '../hooks/useQueryParams';\nimport { login as loginAction, logout as logoutAction, setLocale } from '../reducer';\nimport { adminApi } from '../services/api';\nimport {\n  useGetMeQuery,\n  useGetMyPermissionsQuery,\n  useLazyCheckPermissionsQuery,\n  useLoginMutation,\n  useLogoutMutation,\n  useRenewTokenMutation,\n} from '../services/auth';\n\nimport type {\n  Permission as PermissionContract,\n  SanitizedAdminUser,\n} from '../../../shared/contracts/shared';\n\ninterface Permission\n  extends Pick<PermissionContract, 'action' | 'subject'>,\n    Partial<Omit<PermissionContract, 'action' | 'subject'>> {}\n\ninterface User\n  extends Pick<SanitizedAdminUser, 'email' | 'firstname' | 'lastname' | 'username' | 'roles'>,\n    Partial<Omit<SanitizedAdminUser, 'email' | 'firstname' | 'lastname' | 'username' | 'roles'>> {}\n\ninterface AuthContextValue {\n  login: (\n    body: Login.Request['body'] & { rememberMe: boolean }\n  ) => Promise<Awaited<ReturnType<ReturnType<typeof useLoginMutation>[0]>>>;\n  logout: () => Promise<void>;\n  /**\n   * @alpha\n   * @description given a list of permissions, this function checks\n   * those against the current user's permissions or those passed as\n   * the second argument, if the user has those permissions the complete\n   * permission object form the API is returned. Therefore, if the list is\n   * empty, the user does not have any of those permissions.\n   */\n  checkUserHasPermissions: (\n    permissions?: Permission[],\n    passedPermissions?: Permission[],\n    rawQueryContext?: string\n  ) => Promise<Permission[]>;\n  isLoading: boolean;\n  permissions: Permission[];\n  refetchPermissions: () => Promise<void>;\n  token: string | null;\n  user?: User;\n}\n\nconst [Provider, useAuth] = createContext<AuthContextValue>('Auth');\n\ninterface AuthProviderProps {\n  children: React.ReactNode;\n  /**\n   * @internal could be removed at any time.\n   */\n  _defaultPermissions?: Permission[];\n\n  // NOTE: this is used for testing purposed only\n  _disableRenewToken?: boolean;\n}\n\nconst STORAGE_KEYS = {\n  TOKEN: 'jwtToken',\n  STATUS: 'isLoggedIn',\n};\n\nconst AuthProvider = ({\n  children,\n  _defaultPermissions = [],\n  _disableRenewToken = false,\n}: AuthProviderProps) => {\n  const dispatch = useTypedDispatch();\n  const runRbacMiddleware = useStrapiApp('AuthProvider', (state) => state.rbac.run);\n  const location = useLocation();\n  const [{ rawQuery }] = useQueryParams();\n\n  const locationRef = React.useRef(location);\n\n  // Update ref without causing re-render\n  React.useEffect(() => {\n    locationRef.current = location;\n  }, [location]);\n\n  const token = useTypedSelector((state) => state.admin_app.token ?? null);\n\n  const { data: user, isLoading: isLoadingUser } = useGetMeQuery(undefined, {\n    /**\n     * If there's no token, we don't try to fetch\n     * the user data because it will fail.\n     */\n    skip: !token,\n  });\n\n  const {\n    data: userPermissions = _defaultPermissions,\n    refetch,\n    isUninitialized,\n    isLoading: isLoadingPermissions,\n  } = useGetMyPermissionsQuery(undefined, {\n    skip: !token,\n  });\n\n  const navigate = useNavigate();\n\n  const [loginMutation] = useLoginMutation();\n  const [renewTokenMutation] = useRenewTokenMutation();\n  const [logoutMutation] = useLogoutMutation();\n\n  const clearStateAndLogout = React.useCallback(() => {\n    dispatch(adminApi.util.resetApiState());\n    dispatch(logoutAction());\n    navigate('/auth/login');\n  }, [dispatch, navigate]);\n\n  /**\n   * Fetch data from storages on mount and store it in our state.\n   * It's not normally stored in session storage unless the user\n   * does click \"remember me\" when they login. We also need to renew the token.\n   */\n  React.useEffect(() => {\n    if (token && !_disableRenewToken) {\n      renewTokenMutation({ token }).then((res) => {\n        if ('data' in res) {\n          dispatch(\n            loginAction({\n              token: res.data.token,\n            })\n          );\n        } else {\n          clearStateAndLogout();\n        }\n      });\n    }\n  }, [token, dispatch, renewTokenMutation, clearStateAndLogout, _disableRenewToken]);\n\n  React.useEffect(() => {\n    if (user) {\n      if (user.preferedLanguage) {\n        dispatch(setLocale(user.preferedLanguage));\n      }\n    }\n  }, [dispatch, user]);\n\n  React.useEffect(() => {\n    /**\n     * This will log a user out of all tabs if they log out in one tab.\n     */\n    const handleUserStorageChange = (event: StorageEvent) => {\n      if (event.key === STORAGE_KEYS.STATUS && event.newValue === null) {\n        clearStateAndLogout();\n      }\n    };\n\n    window.addEventListener('storage', handleUserStorageChange);\n\n    return () => {\n      window.removeEventListener('storage', handleUserStorageChange);\n    };\n  });\n\n  const login = React.useCallback<AuthContextValue['login']>(\n    async ({ rememberMe, ...body }) => {\n      const res = await loginMutation(body);\n\n      /**\n       * There will always be a `data` key in the response\n       * because if something fails, it will throw an error.\n       */\n      if ('data' in res) {\n        const { token } = res.data;\n\n        dispatch(\n          loginAction({\n            token,\n            persist: rememberMe,\n          })\n        );\n      }\n\n      return res;\n    },\n    [dispatch, loginMutation]\n  );\n\n  const logout = React.useCallback(async () => {\n    await logoutMutation();\n    clearStateAndLogout();\n  }, [clearStateAndLogout, logoutMutation]);\n\n  const refetchPermissions = React.useCallback(async () => {\n    if (!isUninitialized) {\n      await refetch();\n    }\n  }, [isUninitialized, refetch]);\n\n  const [checkPermissions] = useLazyCheckPermissionsQuery();\n  const checkUserHasPermissions: AuthContextValue['checkUserHasPermissions'] = React.useCallback(\n    async (\n      permissions,\n      passedPermissions,\n      // TODO:\n      // Here we have parameterised checkUserHasPermissions in order to pass\n      // query context from elsewhere in the application.\n      // See packages/core/content-manager/admin/src/features/DocumentRBAC.tsx\n\n      // This is in order to calculate permissions on accurate query params.\n      // We should be able to rely on the query params in this provider\n      // If we need to pass additional context to the RBAC middleware\n      // we should define a better context type.\n      rawQueryContext\n    ) => {\n      /**\n       * If there's no permissions to check, then we allow it to\n       * pass to preserve existing behaviours.\n       *\n       * TODO: should we review this? it feels more dangerous than useful.\n       */\n      if (!permissions || permissions.length === 0) {\n        return [{ action: '', subject: '' }];\n      }\n\n      /**\n       * Given the provided permissions, return the permissions from either passedPermissions\n       * or userPermissions as this is expected to be the full permission entity.\n       */\n      const actualUserPermissions = passedPermissions ?? userPermissions;\n\n      const matchingPermissions = actualUserPermissions.filter(\n        (permission) =>\n          permissions.findIndex(\n            (perm) =>\n              perm.action === permission.action &&\n              // Only check the subject if it's provided\n              (perm.subject == undefined || perm.subject === permission.subject)\n          ) >= 0\n      );\n\n      const middlewaredPermissions = await runRbacMiddleware(\n        {\n          user,\n          permissions: userPermissions,\n          pathname: locationRef.current.pathname,\n          search: (rawQueryContext || rawQuery).split('?')[1] ?? '',\n        },\n        matchingPermissions\n      );\n\n      const shouldCheckConditions = middlewaredPermissions.some(\n        (perm) => Array.isArray(perm.conditions) && perm.conditions.length > 0\n      );\n\n      if (!shouldCheckConditions) {\n        return middlewaredPermissions;\n      }\n\n      const { data, error } = await checkPermissions({\n        permissions: middlewaredPermissions.map((perm) => ({\n          action: perm.action,\n          subject: perm.subject,\n        })),\n      });\n\n      if (error) {\n        throw error;\n      } else {\n        return middlewaredPermissions.filter((_, index) => data?.data[index] === true);\n      }\n    },\n    [checkPermissions, rawQuery, runRbacMiddleware, user, userPermissions]\n  );\n\n  const isLoading = isLoadingUser || isLoadingPermissions;\n\n  return (\n    <Provider\n      token={token}\n      user={user}\n      login={login}\n      logout={logout}\n      permissions={userPermissions}\n      checkUserHasPermissions={checkUserHasPermissions}\n      refetchPermissions={refetchPermissions}\n      isLoading={isLoading}\n    >\n      {children}\n    </Provider>\n  );\n};\n\nexport { AuthProvider, useAuth, STORAGE_KEYS };\nexport type { AuthContextValue, Permission, User };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,IAAM,CAACA,mBAAmBC,YAAa,IAAGC,cAAqC,WAAA;;;;;ACzB/E,IAAMC,YAAY,MAAA;AAChB,QAAM,EAAEC,OAAM,IAAKC,YAAAA;AAEnB,aAAOC,sBAAQ,MAAMF,QAAQ;IAACA;EAAO,CAAA;AACvC;AAEA,IAAMG,iBAAiB,CAAwBC,kBAAAA;AAC7C,QAAMJ,SAASD,UAAAA;AACf,QAAMM,WAAWC,YAAAA;AAEjB,QAAMC,YAAQL,sBAAQ,MAAA;AAEpB,UAAMM,cAAcR,OAAOS,WAAW,GAAA,IAAOT,OAAOU,MAAM,CAAKV,IAAAA;AAC/D,QAAI,CAACA,UAAUI,eAAe;AAC5B,aAAOA;IACT;AAEA,WAAO;MAAE,GAAGA;MAAe,OAAGO,iBAAMH,WAAY;IAAC;KAChD;IAACR;IAAQI;EAAc,CAAA;AAE1B,QAAMQ,eAAWC,0BACf,CAACC,YAAoBC,SAA4B,QAAQC,UAAU,UAAK;AACtE,QAAIC,YAAY;MAAE,GAAGV;IAAM;AAE3B,QAAIQ,WAAW,UAAU;AACvBG,aAAOC,KAAKL,UAAYM,EAAAA,QAAQ,CAACC,QAAAA;AAC/B,YAAIH,OAAOI,UAAUC,eAAeC,KAAKP,WAAWI,GAAM,GAAA;AAExD,iBAAOJ,UAAUI,GAAI;QACvB;MACF,CAAA;WACK;AACLJ,kBAAY;QAAE,GAAGV;QAAO,GAAGO;MAAW;IACxC;AAEAT,aAAS;MAAEL,YAAQyB,qBAAUR,WAAW;QAAES,QAAQ;MAAM,CAAA;OAAM;MAAEV;IAAQ,CAAA;KAE1E;IAACX;IAAUE;EAAM,CAAA;AAGnB,SAAO;IAAC;MAAEA;MAAOoB,UAAU3B;IAAO;IAAGY;EAAS;AAChD;;;ACxBA,IAAMgB,eAAe;EACnBC,OAAO;EACPC,QAAQ;AACV;AAEA,IAAMC,0BAA0B;AAChC,IAAMC,6BAA6B;IAEtBC,iBAAiB,MAAA;AAC5B,QAAMC,mBAAmBC,aAAaC,QAAQR,aAAaC,KAAK;AAChE,MAAIK,kBAAkB;AACpB,WAAOG,KAAKC,MAAMJ,gBAAAA;EACpB;AAEA,QAAMK,aAAaC,eAAeZ,aAAaC,KAAK;AACpD,SAAOU,cAAc;AACvB;AAEA,IAAME,aAAaC,YAAY;EAC7BC,MAAM;EACNC,cAAc,MAAA;AACZ,WAAO;MACLC,UAAU;QACRC,QAAQ;QACRC,aAAa;UAAEC,IAAI;QAAU;MAC/B;MACAC,aAAa,CAAA;MACbC,OAAO;QACLC,iBAAiB,CAAA;QACjBC,cAAcjB,aAAaC,QAAQL,uBAA4B,KAAA;MACjE;MACAsB,OAAO;IACT;EACF;EACAC,UAAU;IACRC,YAAYC,OAAOC,QAAgC;AACjDD,YAAMN,MAAME,eAAeK,OAAOC;AAClCC,aAAOxB,aAAayB,QAAQ7B,yBAAyB0B,OAAOC,OAAO;IACrE;IACAG,mBAAmBL,OAAOC,QAA2D;AACnFD,YAAMN,MAAMC,kBAAkBM,OAAOC;IACvC;IACAI,UAAUN,OAAOC,QAA6B;AAC5CD,YAAMX,SAASC,SAASW,OAAOC;AAE/BC,aAAOxB,aAAayB,QAAQ5B,4BAA4ByB,OAAOC,OAAO;AACtEK,eAASC,gBAAgBC,aAAa,QAAQR,OAAOC,OAAO;IAC9D;IACAQ,SAASV,OAAOC,QAAoC;AAClDD,YAAMH,QAAQI,OAAOC;IACvB;IACAS,MAAMX,OAAOC,QAA2D;AACtE,YAAM,EAAEJ,OAAOe,QAAO,IAAKX,OAAOC;AAElC,UAAI,CAACU,SAAS;AACZC,kBAAUzC,aAAaC,OAAOwB,KAAAA;aACzB;AACLM,eAAOxB,aAAayB,QAAQhC,aAAaC,OAAOQ,KAAKiC,UAAUjB,KAAAA,CAAAA;MACjE;AACAM,aAAOxB,aAAayB,QAAQhC,aAAaE,QAAQ,MAAA;AACjD0B,YAAMH,QAAQA;IAChB;IACAkB,OAAOf,OAAK;AACVA,YAAMH,QAAQ;AACdmB,mBAAa5C,aAAaC,KAAK;AAC/B8B,aAAOxB,aAAasC,WAAW7C,aAAaC,KAAK;AACjD8B,aAAOxB,aAAasC,WAAW7C,aAAaE,MAAM;IACpD;EACF;AACF,CAAA;AAEM4C,IAAAA,UAAUjC,WAAWiC;IAEd,EAAEnB,aAAaM,oBAAoBC,WAAWI,UAAUK,QAAQJ,MAAK,IAChF1B,WAAWkC;;;AChFb,IAAMC,cAAcC,SACjBC,iBAAiB;EAChBC,aAAa;IAAC;IAAQ;IAAM;EAAmB;AACjD,CAAA,EACCC,gBAAgB;EACfC,WAAW,CAACC,aAAa;;;;IAIvBC,OAAOD,QAAQE,MAAoC;MACjDA,OAAO,OAAO;QACZC,QAAQ;QACRC,KAAK;;MAEPC,kBAAkBC,KAAmB;AACnC,eAAOA,IAAIC;MACb;MACAC,cAAc,CAACF,QAASA,MAAM;QAAC;QAAM;UAAEG,MAAM;UAAQC,IAAIJ,IAAII;QAAG;UAAK;QAAC;MAAK;IAC7E,CAAA;IACAC,kBAAkBX,QAAQE,MAAgD;MACxEA,OAAO,OAAO;QACZC,QAAQ;QACRC,KAAK;;MAEPC,kBAAkBC,KAA+B;AAC/C,eAAOA,IAAIC;MACb;IACF,CAAA;IACAK,UAAUZ,QAAQa,SAA8D;MAC9EX,OAAO,CAACY,UAAU;QAChBX,QAAQ;QACRC,KAAK;QACLG,MAAMO;;MAERT,kBAAkBC,KAAsB;AACtC,eAAOA,IAAIC;MACb;MACAQ,iBAAiB;QAAC;MAAK;IACzB,CAAA;;;;IAIAC,kBAAkBhB,QAAQE,MAA6C;MACrEA,OAAO,CAACe,iBAAiB;QACvBd,QAAQ;QACRC,KAAK;QACLG,MAAMU;;IAEV,CAAA;;;;IAIAC,OAAOlB,QAAQa,SAAwD;MACrEX,OAAO,CAACY,UAAU;QAChBX,QAAQ;QACRC,KAAK;QACLG,MAAMO;;MAERT,kBAAkBC,KAAmB;AACnC,eAAOA,IAAIC;MACb;MACAQ,iBAAiB;QAAC;MAAK;IACzB,CAAA;IACAI,QAAQnB,QAAQa,SAAqB;MACnCX,OAAO,OAAO;QACZC,QAAQ;QACRC,KAAK;;IAET,CAAA;IACAgB,eAAepB,QAAQa,SAGrB;MACAX,OAAO,CAACY,UAAU;QAChBX,QAAQ;QACRC,KAAK;QACLG,MAAMO;;MAERT,kBAAkBC,KAA2B;AAC3C,eAAOA,IAAIC;MACb;IACF,CAAA;IACAc,YAAYrB,QAAQa,SAAkE;MACpFX,OAAO,CAACY,UAAU;QAChBX,QAAQ;QACRC,KAAK;QACLG,MAAMO;;MAERT,kBAAkBC,KAAwB;AACxC,eAAOA,IAAIC;MACb;IACF,CAAA;IACAe,qBAAqBtB,QAAQE,MAG3B;MACAA,OAAO,CAACqB,uBAAuB;QAC7BnB,KAAK;QACLD,QAAQ;QACRqB,QAAQ;UACNC,QAAQ;YACNF;UACF;QACF;;MAEFlB,kBAAkBC,KAA8B;AAC9C,eAAOA,IAAIC;MACb;IACF,CAAA;IACAmB,eAAe1B,QAAQa,SAGrB;MACAX,OAAO,CAACY,UAAU;QAChBX,QAAQ;QACRC,KAAK;QACLG,MAAMO;;MAERT,kBAAkBC,KAA2B;AAC3C,eAAOA,IAAIC;MACb;IACF,CAAA;IACAoB,cAAc3B,QAAQa,SAA8D;MAClFX,OAAO,CAACY,UAAU;QAChBX,QAAQ;QACRC,KAAK;QACLG,MAAMO;;MAERT,kBAAkBC,KAAsB;AACtC,eAAOA,IAAIC;MACb;IACF,CAAA;IACAqB,gBAAgB5B,QAAQa,SAAkE;MACxFX,OAAO,CAACY,UAAU;QAChBV,KAAK;QACLD,QAAQ;QACRI,MAAMO;;IAEV,CAAA;IACAe,aAAa7B,QAAQE,MAA0C;MAC7DA,OAAO,OAAO;QACZE,KAAK;QACLD,QAAQ;;MAEVE,kBAAkBC,KAAyB;AACzC,eAAOA,IAAIC;MACb;IACF,CAAA;IACAuB,cAAc9B,QAAQE,MAAmC;MACvDA,OAAO,OAAO;QACZE,KAAK;QACLD,QAAQ;;IAEZ,CAAA;IACA4B,oBAAoB/B,QAAQE,MAA+C;MACzEA,OAAO,OAAO;QACZE,KAAK;QACLD,QAAQ;;MAEVE,kBAAkBC,KAA8B;AAC9C,eAAOA,IAAIC;MACb;MACAC,cAAc;QAAC;MAAmB;IACpC,CAAA;IACAwB,uBAAuBhC,QAAQa,SAG7B;MACAX,OAAO,CAACY,UAAU;QAChBV,KAAK;QACLD,QAAQ;QACRI,MAAMO;;MAERT,kBAAkBC,KAA8B;AAC9C,eAAOA,IAAIC;MACb;MACAQ,iBAAiB;QAAC;MAAmB;IACvC,CAAA;;EAEFkB,kBAAkB;AACpB,CAAA;AAEF,IAAM,EACJC,0BACAC,8BACAC,eACAC,kBACAC,uBACAC,mBACAC,qBACAC,0BACAC,0BACAC,yBACAC,6BACAC,2BACAC,0BACAC,qBACAC,sBACAC,4BACAC,iCAAgC,IAC9BxD;;;;;AC9JJ,IAAM,CAACyD,UAAUC,OAAQ,IAAGC,cAAgC,MAAA;AAa5D,IAAMC,gBAAe;EACnBC,OAAO;EACPC,QAAQ;AACV;AAEMC,IAAAA,eAAe,CAAC,EACpBC,UACAC,sBAAsB,CAAA,GACtBC,qBAAqB,MAAK,MACR;AAClB,QAAMC,WAAWC,iBAAAA;AACjB,QAAMC,oBAAoBC,aAAa,gBAAgB,CAACC,UAAUA,MAAMC,KAAKC,GAAG;AAChF,QAAMC,WAAWC,YAAAA;AACjB,QAAM,CAAC,EAAEC,SAAQ,CAAE,IAAIC,eAAAA;AAEvB,QAAMC,cAAoBC,aAAOL,QAAAA;AAGjCM,EAAMC,gBAAU,MAAA;AACdH,gBAAYI,UAAUR;KACrB;IAACA;EAAS,CAAA;AAEb,QAAMS,QAAQC,iBAAiB,CAACb,UAAUA,MAAMc,UAAUF,SAAS,IAAA;AAEnE,QAAM,EAAEG,MAAMC,MAAMC,WAAWC,cAAa,IAAKC,cAAcC,QAAW;;;;;IAKxEC,MAAM,CAACT;EACT,CAAA;AAEA,QAAM,EACJG,MAAMO,kBAAkB5B,qBACxB6B,SACAC,iBACAP,WAAWQ,qBAAoB,IAC7BC,yBAAyBN,QAAW;IACtCC,MAAM,CAACT;EACT,CAAA;AAEA,QAAMe,WAAWC,YAAAA;AAEjB,QAAM,CAACC,aAAAA,IAAiBC,iBAAAA;AACxB,QAAM,CAACC,kBAAAA,IAAsBC,sBAAAA;AAC7B,QAAM,CAACC,cAAAA,IAAkBC,kBAAAA;AAEzB,QAAMC,sBAA4BC,kBAAY,MAAA;AAC5CxC,aAASyC,SAASC,KAAKC,cAAa,CAAA;AACpC3C,aAAS4C,OAAAA,CAAAA;AACTb,aAAS,aAAA;KACR;IAAC/B;IAAU+B;EAAS,CAAA;AAOvBlB,EAAMC,gBAAU,MAAA;AACd,QAAIE,SAAS,CAACjB,oBAAoB;AAChCoC,yBAAmB;QAAEnB;OAAS6B,EAAAA,KAAK,CAACC,QAAAA;AAClC,YAAI,UAAUA,KAAK;AACjB9C,mBACE+C,MAAY;YACV/B,OAAO8B,IAAI3B,KAAKH;UAClB,CAAA,CAAA;eAEG;AACLuB,8BAAAA;QACF;MACF,CAAA;IACF;KACC;IAACvB;IAAOhB;IAAUmC;IAAoBI;IAAqBxC;EAAmB,CAAA;AAEjFc,EAAMC,gBAAU,MAAA;AACd,QAAIM,MAAM;AACR,UAAIA,KAAK4B,kBAAkB;AACzBhD,iBAASiD,UAAU7B,KAAK4B,gBAAgB,CAAA;MAC1C;IACF;KACC;IAAChD;IAAUoB;EAAK,CAAA;AAEnBP,EAAMC,gBAAU,MAAA;AAId,UAAMoC,0BAA0B,CAACC,UAAAA;AAC/B,UAAIA,MAAMC,QAAQ3D,cAAaE,UAAUwD,MAAME,aAAa,MAAM;AAChEd,4BAAAA;MACF;IACF;AAEAe,WAAOC,iBAAiB,WAAWL,uBAAAA;AAEnC,WAAO,MAAA;AACLI,aAAOE,oBAAoB,WAAWN,uBAAAA;IACxC;EACF,CAAA;AAEA,QAAMO,UAAcjB,kBAClB,OAAO,EAAEkB,YAAY,GAAGC,KAAM,MAAA;AAC5B,UAAMb,MAAM,MAAMb,cAAc0B,IAAAA;AAMhC,QAAI,UAAUb,KAAK;AACjB,YAAM,EAAE9B,OAAAA,OAAK,IAAK8B,IAAI3B;AAEtBnB,eACE+C,MAAY;QACV/B,OAAAA;QACA4C,SAASF;MACX,CAAA,CAAA;IAEJ;AAEA,WAAOZ;KAET;IAAC9C;IAAUiC;EAAc,CAAA;AAG3B,QAAM4B,WAAerB,kBAAY,YAAA;AAC/B,UAAMH,eAAAA;AACNE,wBAAAA;KACC;IAACA;IAAqBF;EAAe,CAAA;AAExC,QAAMyB,qBAA2BtB,kBAAY,YAAA;AAC3C,QAAI,CAACZ,iBAAiB;AACpB,YAAMD,QAAAA;IACR;KACC;IAACC;IAAiBD;EAAQ,CAAA;AAE7B,QAAM,CAACoC,gBAAAA,IAAoBC,6BAAAA;AAC3B,QAAMC,0BAA6EzB,kBACjF,OACE0B,aACAC,mBAUAC,oBAAAA;AAQA,QAAI,CAACF,eAAeA,YAAYG,WAAW,GAAG;AAC5C,aAAO;QAAC;UAAEC,QAAQ;UAAIC,SAAS;QAAG;MAAE;IACtC;AAMA,UAAMC,wBAAwBL,qBAAqBzC;AAEnD,UAAM+C,sBAAsBD,sBAAsBE,OAChD,CAACC,eACCT,YAAYU,UACV,CAACC,SACCA,KAAKP,WAAWK,WAAWL;KAE1BO,KAAKN,WAAW/C,UAAaqD,KAAKN,YAAYI,WAAWJ,QACzD,KAAA,CAAA;AAGT,UAAMO,yBAAyB,MAAM5E,kBACnC;MACEkB;MACA8C,aAAaxC;MACbqD,UAAUpE,YAAYI,QAAQgE;MAC9BC,SAASZ,mBAAmB3D,UAAUwE,MAAM,GAAA,EAAK,CAAA,KAAM;OAEzDR,mBAAAA;AAGF,UAAMS,wBAAwBJ,uBAAuBK,KACnD,CAACN,SAASO,MAAMC,QAAQR,KAAKS,UAAU,KAAKT,KAAKS,WAAWjB,SAAS,CAAA;AAGvE,QAAI,CAACa,uBAAuB;AAC1B,aAAOJ;IACT;AAEA,UAAM,EAAE3D,MAAMoE,MAAK,IAAK,MAAMxB,iBAAiB;MAC7CG,aAAaY,uBAAuBU,IAAI,CAACX,UAAU;QACjDP,QAAQO,KAAKP;QACbC,SAASM,KAAKN;QAChB;IACF,CAAA;AAEA,QAAIgB,OAAO;AACT,YAAMA;WACD;AACL,aAAOT,uBAAuBJ,OAAO,CAACe,GAAGC,WAAUvE,6BAAMA,KAAKuE,YAAW,IAAA;IAC3E;KAEF;IAAC3B;IAAkBtD;IAAUP;IAAmBkB;IAAMM;EAAgB,CAAA;AAGxE,QAAML,YAAYC,iBAAiBO;AAEnC,aACE8D,wBAACrG,UAAAA;IACC0B;IACAI;IACAqC,OAAOA;IACPI,QAAQA;IACRK,aAAaxC;IACbuC;IACAH;IACAzC;IAECxB;;AAGP;", "names": ["StrapiAppProvider", "useStrapiApp", "createContext", "useSearch", "search", "useLocation", "useMemo", "useQueryParams", "initialParams", "navigate", "useNavigate", "query", "searchQuery", "startsWith", "slice", "parse", "<PERSON><PERSON><PERSON><PERSON>", "useCallback", "nextParams", "method", "replace", "<PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call", "stringify", "encode", "<PERSON><PERSON><PERSON><PERSON>", "STORAGE_KEYS", "TOKEN", "STATUS", "THEME_LOCAL_STORAGE_KEY", "LANGUAGE_LOCAL_STORAGE_KEY", "getStoredToken", "fromLocalStorage", "localStorage", "getItem", "JSON", "parse", "fromCookie", "getCookieValue", "adminSlice", "createSlice", "name", "initialState", "language", "locale", "localeNames", "en", "permissions", "theme", "availableThemes", "currentTheme", "token", "reducers", "setAppTheme", "state", "action", "payload", "window", "setItem", "setAvailableThemes", "setLocale", "document", "documentElement", "setAttribute", "setToken", "login", "persist", "<PERSON><PERSON><PERSON><PERSON>", "stringify", "logout", "deleteC<PERSON>ie", "removeItem", "reducer", "actions", "authService", "adminApi", "enhanceEndpoints", "addTagTypes", "injectEndpoints", "endpoints", "builder", "getMe", "query", "method", "url", "transformResponse", "res", "data", "providesTags", "type", "id", "getMyPermissions", "updateMe", "mutation", "body", "invalidatesTags", "checkPermissions", "permissions", "login", "logout", "resetPassword", "renewToken", "getRegistrationInfo", "registrationToken", "config", "params", "registerAdmin", "registerUser", "forgotPassword", "isSSOLocked", "getProviders", "getProviderOptions", "updateProviderOptions", "overrideExisting", "useCheckPermissionsQuery", "useLazyCheckPermissionsQuery", "useGetMeQuery", "useLoginMutation", "useRenewTokenMutation", "useLogoutMutation", "useUpdateMeMutation", "useResetPasswordMutation", "useRegisterAdminMutation", "useRegisterUserMutation", "useGetRegistrationInfoQuery", "useForgotPasswordMutation", "useGetMyPermissionsQuery", "useIsSSOLockedQuery", "useGetProvidersQuery", "useGetProviderOptionsQuery", "useUpdateProviderOptionsMutation", "Provider", "useAuth", "createContext", "STORAGE_KEYS", "TOKEN", "STATUS", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_defaultPermissions", "_disableRenewToken", "dispatch", "useTypedDispatch", "runRbacMiddleware", "useStrapiApp", "state", "rbac", "run", "location", "useLocation", "<PERSON><PERSON><PERSON><PERSON>", "useQueryParams", "locationRef", "useRef", "React", "useEffect", "current", "token", "useTypedSelector", "admin_app", "data", "user", "isLoading", "isLoadingUser", "useGetMeQuery", "undefined", "skip", "userPermissions", "refetch", "isUninitialized", "isLoadingPermissions", "useGetMyPermissionsQuery", "navigate", "useNavigate", "loginMutation", "useLoginMutation", "renewTokenMutation", "useRenewTokenMutation", "logoutMutation", "useLogoutMutation", "clearStateAndLogout", "useCallback", "adminApi", "util", "resetApiState", "logoutAction", "then", "res", "loginAction", "preferedLanguage", "setLocale", "handleUserStorageChange", "event", "key", "newValue", "window", "addEventListener", "removeEventListener", "login", "rememberMe", "body", "persist", "logout", "refetchPermissions", "checkPermissions", "useLazyCheckPermissionsQuery", "checkUserHasPermissions", "permissions", "passedPermissions", "rawQueryContext", "length", "action", "subject", "actualUserPermissions", "matchingPermissions", "filter", "permission", "findIndex", "perm", "middlewaredPermissions", "pathname", "search", "split", "shouldCheckConditions", "some", "Array", "isArray", "conditions", "error", "map", "_", "index", "_jsx"]}