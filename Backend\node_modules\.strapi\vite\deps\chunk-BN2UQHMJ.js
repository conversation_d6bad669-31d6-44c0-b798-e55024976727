import {
  useTracking
} from "./chunk-XDCEA27D.js";
import {
  useQueryParams
} from "./chunk-ODQFI753.js";
import {
  IconButton,
  SearchForm,
  Searchbar,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  ForwardRef$X
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/components/SearchInput.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var SearchInput = ({ disabled, label, placeholder, trackedEvent, trackedEventDetails }) => {
  const inputRef = React.useRef(null);
  const iconButtonRef = React.useRef(null);
  const [{ query }, setQuery] = useQueryParams();
  const [value, setValue] = React.useState((query == null ? void 0 : query._q) || "");
  const [isOpen, setIsOpen] = React.useState(!!value);
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const handleToggle = () => setIsOpen((prev) => !prev);
  React.useLayoutEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [
    isOpen
  ]);
  const handleClear = () => {
    setValue("");
    setQuery({
      _q: ""
    }, "remove");
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    if (value) {
      if (trackedEvent) {
        trackUsage(trackedEvent, trackedEventDetails);
      }
      setQuery({
        _q: encodeURIComponent(value),
        page: 1
      });
    } else {
      handleToggle();
      setQuery({
        _q: ""
      }, "remove");
    }
  };
  if (isOpen) {
    return (0, import_jsx_runtime.jsx)(SearchForm, {
      onSubmit: handleSubmit,
      children: (0, import_jsx_runtime.jsx)(Searchbar, {
        ref: inputRef,
        name: "search",
        onChange: (e) => setValue(e.target.value),
        value,
        clearLabel: formatMessage({
          id: "clearLabel",
          defaultMessage: "Clear"
        }),
        onClear: handleClear,
        placeholder,
        children: label
      })
    });
  }
  return (0, import_jsx_runtime.jsx)(IconButton, {
    ref: iconButtonRef,
    disabled,
    label: formatMessage({
      id: "global.search",
      defaultMessage: "Search"
    }),
    onClick: handleToggle,
    children: (0, import_jsx_runtime.jsx)(ForwardRef$X, {})
  });
};

export {
  SearchInput
};
//# sourceMappingURL=chunk-BN2UQHMJ.js.map
