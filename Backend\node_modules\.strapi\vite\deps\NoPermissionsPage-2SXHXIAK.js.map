{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/pages/NoPermissionsPage.tsx"], "sourcesContent": ["import { Page, Layouts } from '@strapi/admin/strapi-admin';\nimport { useIntl } from 'react-intl';\n\nimport { getTranslation } from '../utils/translations';\n\nconst NoPermissions = () => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <>\n      <Layouts.Header\n        title={formatMessage({\n          id: getTranslation('header.name'),\n          defaultMessage: 'Content',\n        })}\n      />\n      <Layouts.Content>\n        <Page.NoPermissions />\n      </Layouts.Content>\n    </>\n  );\n};\n\nexport { NoPermissions };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,IAAMA,gBAAgB,MAAA;AACpB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEC,yBAAAC,6BAAA;;UACEC,wBAACC,QAAQC,QAAM;QACbC,OAAOP,cAAc;UACnBQ,IAAIC,eAAe,aAAA;UACnBC,gBAAgB;QAClB,CAAA;;UAEFN,wBAACC,QAAQM,SAAO;sBACdP,wBAACQ,KAAKb,eAAa,CAAA,CAAA;;;;AAI3B;", "names": ["NoPermissions", "formatMessage", "useIntl", "_jsxs", "_Fragment", "_jsx", "Layouts", "Header", "title", "id", "getTranslation", "defaultMessage", "Content", "Page"]}