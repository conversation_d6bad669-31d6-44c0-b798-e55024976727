import {
  useTypedSelector
} from "./chunk-SEUNPHZA.js";
import {
  BulkActionsRenderer,
  TableActions
} from "./chunk-6NZ2ITH7.js";
import {
  DocumentRBAC,
  InjectionZone,
  PERMISSIONS,
  getDisplayName,
  getRelationLabel,
  prefixFileUrlWithBackendUrl,
  useDebounce,
  useDocumentRBAC,
  useGetRelationsQuery,
  usePrev
} from "./chunk-6OM5ZNLL.js";
import {
  DocumentStatus
} from "./chunk-4LPIPK4X.js";
import "./chunk-2D65YUEQ.js";
import "./chunk-QBVVXK5Q.js";
import {
  CREATOR_FIELDS,
  HOOKS,
  buildValidParams,
  checkIfAttributeIsDisplayable,
  convertListLayoutToFieldLayouts,
  getMainField,
  useContentTypeSchema,
  useDoc,
  useDocumentLayout,
  useGetAllDocumentsQuery,
  useGetContentTypeConfigurationQuery
} from "./chunk-GZO3GFLN.js";
import "./chunk-A46TUCLT.js";
import {
  getTranslation
} from "./chunk-HIZVCZYI.js";
import "./chunk-6LY4MOO2.js";
import "./chunk-EKXSMIUH.js";
import "./chunk-SYWYLB7I.js";
import "./chunk-YL7CEHOK.js";
import "./chunk-YV3ONWF5.js";
import "./chunk-UMW22TSS.js";
import "./chunk-4QC3H4VA.js";
import "./chunk-75D2ZJP5.js";
import "./chunk-VCTAT6B3.js";
import "./chunk-ROZIXYJG.js";
import "./chunk-C72RZIDJ.js";
import "./chunk-HZKRK7AR.js";
import "./chunk-LRN6A2UC.js";
import "./chunk-D2TGW5YS.js";
import "./chunk-M27D4U76.js";
import "./chunk-HX66WGOY.js";
import "./chunk-Y4UEUAII.js";
import {
  SearchInput
} from "./chunk-BN2UQHMJ.js";
import "./chunk-NWWGC2Z2.js";
import "./chunk-DD3MYA7D.js";
import "./chunk-O5ZNSDDU.js";
import "./chunk-MBK4V2X7.js";
import {
  require_isEmpty
} from "./chunk-YJEURQPS.js";
import "./chunk-DY2RJG3P.js";
import "./chunk-K65KIEAL.js";
import "./chunk-BUDFB33L.js";
import "./chunk-7MILHJ3J.js";
import "./chunk-SGQJOZK5.js";
import "./chunk-AFM2NWPO.js";
import {
  BackButton
} from "./chunk-DUGZ4WIW.js";
import "./chunk-IFOFBKTA.js";
import "./chunk-376QHLWZ.js";
import "./chunk-EGNP2T5O.js";
import {
  useTracking
} from "./chunk-XDCEA27D.js";
import "./chunk-EZSYDDUK.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-QIJGNK42.js";
import "./chunk-CJHUGFLE.js";
import "./chunk-IQGHPIIW.js";
import {
  Filters
} from "./chunk-DWSGKQEK.js";
import {
  Pagination
} from "./chunk-W6ZGCRX6.js";
import {
  Table,
  useTable
} from "./chunk-PVCRV2LE.js";
import "./chunk-HWAQQGJJ.js";
import {
  useField
} from "./chunk-L5JCPKMP.js";
import {
  useRBAC
} from "./chunk-ZJEMJY2Q.js";
import {
  parseISO
} from "./chunk-D4WYVNVM.js";
import "./chunk-MMOBCIZG.js";
import {
  useAdminUsers
} from "./chunk-6DRYEU2W.js";
import {
  Layouts
} from "./chunk-MTTHLNPH.js";
import "./chunk-PQINNV4N.js";
import {
  require_isEqual
} from "./chunk-VYSYYPOB.js";
import {
  ForwardRef$J,
  Page,
  useAPIErrorHandler
} from "./chunk-7LKLOY7A.js";
import {
  useAuth,
  useQueryParams,
  useStrapiApp
} from "./chunk-ODQFI753.js";
import "./chunk-ZOP4VV6J.js";
import {
  require_lib
} from "./chunk-WH6VCVXU.js";
import "./chunk-IL5G2D22.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-76QM3EFM.js";
import {
  require_toString
} from "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  Avatar,
  Badge,
  Box,
  Button,
  CheckboxImpl,
  Combobox,
  EmptyStateLayout,
  Flex,
  IconButton,
  LinkButton,
  Loader,
  Menu,
  Option,
  Popover,
  TextButton,
  TooltipImpl,
  Typography,
  useCollator,
  useIntl,
  useNotifyAT
} from "./chunk-7GC3Y62Q.js";
import "./chunk-5ZC4PE57.js";
import {
  Link,
  NavLink,
  useNavigate,
  useParams
} from "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$1h,
  ForwardRef$2n,
  ForwardRef$4d
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/dist/admin/pages/ListView/ListViewPage.mjs
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var React3 = __toESM(require_react(), 1);
var import_isEqual = __toESM(require_isEqual(), 1);
var import_qs2 = __toESM(require_lib(), 1);

// node_modules/@strapi/content-manager/dist/admin/pages/ListView/components/Filters.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var NOT_ALLOWED_FILTERS = [
  "json",
  "component",
  "media",
  "richtext",
  "dynamiczone",
  "password",
  "blocks"
];
var DEFAULT_ALLOWED_FILTERS = [
  "createdAt",
  "updatedAt"
];
var USER_FILTER_ATTRIBUTES = [
  ...CREATOR_FIELDS,
  "strapi_assignee"
];
var FiltersImpl = ({ disabled, schema }) => {
  var _a;
  const { attributes, uid: model, options } = schema;
  const { formatMessage, locale } = useIntl();
  const { trackUsage } = useTracking();
  const allPermissions = useAuth("FiltersImpl", (state) => state.permissions);
  const [{ query }] = useQueryParams();
  const { schemas } = useContentTypeSchema();
  const canReadAdminUsers = React.useMemo(() => allPermissions.filter((permission) => permission.action === "admin::users.read" && permission.subject === null).length > 0, [
    allPermissions
  ]);
  const selectedUserIds = (((_a = query == null ? void 0 : query.filters) == null ? void 0 : _a.$and) ?? []).reduce((acc, filter) => {
    const [key, value] = Object.entries(filter)[0];
    if (typeof value.id !== "object") {
      return acc;
    }
    const id = value.id.$eq || value.id.$ne;
    if (id && USER_FILTER_ATTRIBUTES.includes(key) && !acc.includes(id)) {
      acc.push(id);
    }
    return acc;
  }, []);
  const { data: userData, isLoading: isLoadingAdminUsers } = useAdminUsers({
    filters: {
      id: {
        $in: selectedUserIds
      }
    }
  }, {
    // fetch the list of admin users only if the filter contains users and the
    // current user has permissions to display users
    skip: selectedUserIds.length === 0 || !canReadAdminUsers
  });
  const { users = [] } = userData ?? {};
  const { metadata } = useGetContentTypeConfigurationQuery(model, {
    selectFromResult: ({ data }) => ({
      metadata: (data == null ? void 0 : data.contentType.metadatas) ?? {}
    })
  });
  const formatter = useCollator(locale, {
    sensitivity: "base"
  });
  const displayedFilters = React.useMemo(() => {
    const [{ properties: { fields = [] } = {
      fields: []
    } }] = allPermissions.filter((permission) => permission.action === "plugin::content-manager.explorer.read" && permission.subject === model);
    const allowedFields = fields.filter((field) => {
      const attribute = attributes[field] ?? {};
      return attribute.type && !NOT_ALLOWED_FILTERS.includes(attribute.type);
    });
    return [
      "id",
      "documentId",
      ...allowedFields,
      ...DEFAULT_ALLOWED_FILTERS,
      ...canReadAdminUsers ? CREATOR_FIELDS : []
    ].map((name) => {
      const attribute = attributes[name];
      if (NOT_ALLOWED_FILTERS.includes(attribute.type)) {
        return null;
      }
      const { mainField: mainFieldName = "", label } = metadata[name].list;
      let filter = {
        name,
        label: label ?? "",
        mainField: getMainField(attribute, mainFieldName, {
          schemas,
          components: {}
        }),
        // @ts-expect-error – TODO: this is filtered out above in the `allowedFields` call but TS complains, is there a better way to solve this?
        type: attribute.type
      };
      if (attribute.type === "relation" && "target" in attribute && attribute.target === "admin::user") {
        filter = {
          ...filter,
          input: AdminUsersFilter,
          options: users.map((user) => ({
            label: getDisplayName(user),
            value: user.id.toString()
          })),
          operators: [
            {
              label: formatMessage({
                id: "components.FilterOptions.FILTER_TYPES.$eq",
                defaultMessage: "is"
              }),
              value: "$eq"
            },
            {
              label: formatMessage({
                id: "components.FilterOptions.FILTER_TYPES.$ne",
                defaultMessage: "is not"
              }),
              value: "$ne"
            }
          ],
          mainField: {
            name: "id",
            type: "integer"
          }
        };
      }
      if (attribute.type === "enumeration") {
        filter = {
          ...filter,
          options: attribute.enum.map((value) => ({
            label: value,
            value
          }))
        };
      }
      return filter;
    }).filter(Boolean).toSorted((a, b) => formatter.compare(a.label, b.label));
  }, [
    allPermissions,
    canReadAdminUsers,
    model,
    attributes,
    metadata,
    schemas,
    users,
    formatMessage,
    formatter
  ]);
  const onOpenChange = (isOpen) => {
    if (isOpen) {
      trackUsage("willFilterEntries");
    }
  };
  const handleFilterChange = (data) => {
    const attribute = attributes[data.name];
    if (attribute) {
      trackUsage("didFilterEntries", {
        useRelation: attribute.type === "relation"
      });
    }
  };
  return (0, import_jsx_runtime.jsxs)(Filters.Root, {
    disabled,
    options: displayedFilters,
    onOpenChange,
    onChange: handleFilterChange,
    children: [
      (0, import_jsx_runtime.jsx)(Filters.Trigger, {}),
      (0, import_jsx_runtime.jsx)(Filters.Popover, {}),
      (0, import_jsx_runtime.jsx)(Filters.List, {})
    ]
  });
};
var AdminUsersFilter = ({ name }) => {
  const [pageSize, setPageSize] = React.useState(10);
  const [search, setSearch] = React.useState("");
  const { formatMessage } = useIntl();
  const debouncedSearch = useDebounce(search, 300);
  const { data, isLoading } = useAdminUsers({
    pageSize,
    _q: debouncedSearch
  });
  const field = useField(name);
  const handleOpenChange = (isOpen) => {
    if (!isOpen) {
      setPageSize(10);
    }
  };
  const { users = [], pagination } = data ?? {};
  const { pageCount = 1, page = 1 } = pagination ?? {};
  return (0, import_jsx_runtime.jsx)(Combobox, {
    value: field.value,
    "aria-label": formatMessage({
      id: "content-manager.components.Filters.usersSelect.label",
      defaultMessage: "Search and select a user to filter"
    }),
    onOpenChange: handleOpenChange,
    onChange: (value) => field.onChange(name, value),
    loading: isLoading,
    onLoadMore: () => setPageSize(pageSize + 10),
    hasMoreItems: page < pageCount,
    onInputChange: (e) => {
      setSearch(e.currentTarget.value);
    },
    children: users.map((user) => {
      return (0, import_jsx_runtime.jsx)(Option, {
        value: user.id.toString(),
        children: getDisplayName(user)
      }, user.id);
    })
  });
};

// node_modules/@strapi/content-manager/dist/admin/pages/ListView/components/TableCells/CellContent.mjs
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_isEmpty = __toESM(require_isEmpty(), 1);

// node_modules/@strapi/content-manager/dist/admin/pages/ListView/components/TableCells/CellValue.mjs
var import_toString = __toESM(require_toString(), 1);
var CellValue = ({ type, value }) => {
  const { formatDate, formatTime, formatNumber } = useIntl();
  let formattedValue = value;
  if (type === "date") {
    formattedValue = formatDate(parseISO(value), {
      dateStyle: "full"
    });
  }
  if (type === "datetime") {
    formattedValue = formatDate(value, {
      dateStyle: "full",
      timeStyle: "short"
    });
  }
  if (type === "time") {
    const [hour, minute, second] = value.split(":");
    const date = /* @__PURE__ */ new Date();
    date.setHours(hour);
    date.setMinutes(minute);
    date.setSeconds(second);
    formattedValue = formatTime(date, {
      timeStyle: "short"
    });
  }
  if ([
    "float",
    "decimal"
  ].includes(type)) {
    formattedValue = formatNumber(value, {
      // Should be kept in sync with the corresponding value
      // in the design-system/NumberInput: https://github.com/strapi/design-system/blob/main/packages/strapi-design-system/src/NumberInput/NumberInput.js#L53
      maximumFractionDigits: 20
    });
  }
  if ([
    "integer",
    "biginteger"
  ].includes(type)) {
    formattedValue = formatNumber(value, {
      maximumFractionDigits: 0
    });
  }
  return (0, import_toString.default)(formattedValue);
};

// node_modules/@strapi/content-manager/dist/admin/pages/ListView/components/TableCells/Components.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var SingleComponent = ({ content, mainField }) => {
  if (!mainField) {
    return null;
  }
  return (0, import_jsx_runtime2.jsx)(TooltipImpl, {
    label: content[mainField.name],
    children: (0, import_jsx_runtime2.jsx)(Typography, {
      maxWidth: "25rem",
      textColor: "neutral800",
      ellipsis: true,
      children: (0, import_jsx_runtime2.jsx)(CellValue, {
        type: mainField.type,
        value: content[mainField.name]
      })
    })
  });
};
var RepeatableComponent = ({ content, mainField }) => {
  const { formatMessage } = useIntl();
  if (!mainField) {
    return null;
  }
  return (0, import_jsx_runtime2.jsxs)(Menu.Root, {
    children: [
      (0, import_jsx_runtime2.jsxs)(Menu.Trigger, {
        onClick: (e) => e.stopPropagation(),
        children: [
          (0, import_jsx_runtime2.jsx)(Badge, {
            children: content.length
          }),
          formatMessage({
            id: "content-manager.containers.list.items",
            defaultMessage: "{number, plural, =0 {items} one {item} other {items}}"
          }, {
            number: content.length
          })
        ]
      }),
      (0, import_jsx_runtime2.jsx)(Menu.Content, {
        children: content.map((item) => (0, import_jsx_runtime2.jsx)(Menu.Item, {
          disabled: true,
          children: (0, import_jsx_runtime2.jsx)(Typography, {
            maxWidth: "50rem",
            ellipsis: true,
            children: (0, import_jsx_runtime2.jsx)(CellValue, {
              type: mainField.type,
              value: item[mainField.name]
            })
          })
        }, item.id))
      })
    ]
  });
};

// node_modules/@strapi/content-manager/dist/admin/pages/ListView/components/TableCells/Media.mjs
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var getFileExtension = (ext) => ext && ext[0] === "." ? ext.substring(1) : ext;
var MediaSingle = ({ url, mime, alternativeText, name, ext, formats }) => {
  var _a;
  const fileURL = prefixFileUrlWithBackendUrl(url);
  if (mime.includes("image")) {
    const thumbnail = (_a = formats == null ? void 0 : formats.thumbnail) == null ? void 0 : _a.url;
    const mediaURL = prefixFileUrlWithBackendUrl(thumbnail) || fileURL;
    return (0, import_jsx_runtime3.jsx)(Avatar.Item, {
      src: mediaURL,
      alt: alternativeText || name,
      fallback: alternativeText || name,
      preview: true
    });
  }
  const fileExtension = getFileExtension(ext);
  const fileName = name.length > 100 ? `${name.substring(0, 100)}...` : name;
  return (0, import_jsx_runtime3.jsx)(TooltipImpl, {
    description: fileName,
    children: (0, import_jsx_runtime3.jsx)(FileWrapper, {
      children: fileExtension
    })
  });
};
var FileWrapper = ({ children }) => {
  return (0, import_jsx_runtime3.jsx)(Flex, {
    tag: "span",
    position: "relative",
    borderRadius: "50%",
    width: "26px",
    height: "26px",
    borderColor: "neutral200",
    background: "neutral150",
    paddingLeft: "1px",
    justifyContent: "center",
    alignItems: "center",
    children: (0, import_jsx_runtime3.jsx)(FileTypography, {
      variant: "sigma",
      textColor: "neutral600",
      children
    })
  });
};
var FileTypography = dt(Typography)`
  font-size: 0.9rem;
  line-height: 0.9rem;
`;
var MediaMultiple = ({ content }) => {
  return (0, import_jsx_runtime3.jsx)(Avatar.Group, {
    children: content.map((file, index) => {
      const key = `${file.id}${index}`;
      if (index === 3) {
        const remainingFiles = `+${content.length - 3}`;
        return (0, import_jsx_runtime3.jsx)(FileWrapper, {
          children: remainingFiles
        }, key);
      }
      if (index > 3) {
        return null;
      }
      return (0, import_jsx_runtime3.jsx)(MediaSingle, {
        ...file
      }, key);
    })
  });
};

// node_modules/@strapi/content-manager/dist/admin/pages/ListView/components/TableCells/Relations.mjs
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var React2 = __toESM(require_react(), 1);
var RelationSingle = ({ mainField, content }) => {
  return (0, import_jsx_runtime4.jsx)(Typography, {
    maxWidth: "50rem",
    textColor: "neutral800",
    ellipsis: true,
    children: getRelationLabel(content, mainField)
  });
};
var RelationMultiple = ({ mainField, content, rowId, name }) => {
  const { model } = useDoc();
  const { formatMessage } = useIntl();
  const { notifyStatus } = useNotifyAT();
  const [isOpen, setIsOpen] = React2.useState(false);
  const [targetField] = name.split(".");
  const { data, isLoading } = useGetRelationsQuery({
    model,
    id: rowId,
    targetField
  }, {
    skip: !isOpen,
    refetchOnMountOrArgChange: true
  });
  const contentCount = Array.isArray(content) ? content.length : content.count;
  React2.useEffect(() => {
    if (data) {
      notifyStatus(formatMessage({
        id: getTranslation("DynamicTable.relation-loaded"),
        defaultMessage: "Relations have been loaded"
      }));
    }
  }, [
    data,
    formatMessage,
    notifyStatus
  ]);
  return (0, import_jsx_runtime4.jsxs)(Menu.Root, {
    onOpenChange: (isOpen2) => setIsOpen(isOpen2),
    children: [
      (0, import_jsx_runtime4.jsx)(Menu.Trigger, {
        onClick: (e) => e.stopPropagation(),
        children: (0, import_jsx_runtime4.jsx)(Typography, {
          style: {
            cursor: "pointer"
          },
          textColor: "neutral800",
          fontWeight: "regular",
          children: contentCount > 0 ? formatMessage({
            id: "content-manager.containers.list.items",
            defaultMessage: "{number} {number, plural, =0 {items} one {item} other {items}}"
          }, {
            number: contentCount
          }) : "-"
        })
      }),
      (0, import_jsx_runtime4.jsxs)(Menu.Content, {
        children: [
          isLoading && (0, import_jsx_runtime4.jsx)(Menu.Item, {
            disabled: true,
            children: (0, import_jsx_runtime4.jsx)(Loader, {
              small: true,
              children: formatMessage({
                id: getTranslation("ListViewTable.relation-loading"),
                defaultMessage: "Relations are loading"
              })
            })
          }),
          (data == null ? void 0 : data.results) && (0, import_jsx_runtime4.jsxs)(import_jsx_runtime4.Fragment, {
            children: [
              data.results.map((entry) => (0, import_jsx_runtime4.jsx)(Menu.Item, {
                children: (0, import_jsx_runtime4.jsx)(Typography, {
                  maxWidth: "50rem",
                  ellipsis: true,
                  children: getRelationLabel(entry, mainField)
                })
              }, entry.documentId)),
              (data == null ? void 0 : data.pagination) && (data == null ? void 0 : data.pagination.total) > 10 && (0, import_jsx_runtime4.jsx)(Menu.Item, {
                "aria-disabled": true,
                "aria-label": formatMessage({
                  id: getTranslation("ListViewTable.relation-more"),
                  defaultMessage: "This relation contains more entities than displayed"
                }),
                children: (0, import_jsx_runtime4.jsx)(Typography, {
                  children: "…"
                })
              })
            ]
          })
        ]
      })
    ]
  });
};

// node_modules/@strapi/content-manager/dist/admin/pages/ListView/components/TableCells/CellContent.mjs
var CellContent = ({ content, mainField, attribute, rowId, name }) => {
  if (!hasContent(content, mainField, attribute)) {
    return (0, import_jsx_runtime5.jsx)(Typography, {
      textColor: "neutral800",
      paddingLeft: attribute.type === "relation" ? "1.6rem" : 0,
      paddingRight: attribute.type === "relation" ? "1.6rem" : 0,
      children: "-"
    });
  }
  switch (attribute.type) {
    case "media":
      if (!attribute.multiple) {
        return (0, import_jsx_runtime5.jsx)(MediaSingle, {
          ...content
        });
      }
      return (0, import_jsx_runtime5.jsx)(MediaMultiple, {
        content
      });
    case "relation": {
      if (isSingleRelation(attribute.relation)) {
        return (0, import_jsx_runtime5.jsx)(RelationSingle, {
          mainField,
          content
        });
      }
      return (0, import_jsx_runtime5.jsx)(RelationMultiple, {
        rowId,
        mainField,
        content,
        name
      });
    }
    case "component":
      if (attribute.repeatable) {
        return (0, import_jsx_runtime5.jsx)(RepeatableComponent, {
          mainField,
          content
        });
      }
      return (0, import_jsx_runtime5.jsx)(SingleComponent, {
        mainField,
        content
      });
    case "string":
      return (0, import_jsx_runtime5.jsx)(TooltipImpl, {
        description: content,
        children: (0, import_jsx_runtime5.jsx)(Typography, {
          maxWidth: "30rem",
          ellipsis: true,
          textColor: "neutral800",
          children: (0, import_jsx_runtime5.jsx)(CellValue, {
            type: attribute.type,
            value: content
          })
        })
      });
    default:
      return (0, import_jsx_runtime5.jsx)(Typography, {
        maxWidth: "30rem",
        ellipsis: true,
        textColor: "neutral800",
        children: (0, import_jsx_runtime5.jsx)(CellValue, {
          type: attribute.type,
          value: content
        })
      });
  }
};
var hasContent = (content, mainField, attribute) => {
  if (attribute.type === "component") {
    if (attribute.repeatable || !mainField) {
      return (content == null ? void 0 : content.length) > 0;
    }
    const value = content == null ? void 0 : content[mainField.name];
    if (mainField.name === "id" && ![
      void 0,
      null
    ].includes(value)) {
      return true;
    }
    return !(0, import_isEmpty.default)(value);
  }
  if (attribute.type === "relation") {
    if (isSingleRelation(attribute.relation)) {
      return !(0, import_isEmpty.default)(content);
    }
    if (Array.isArray(content)) {
      return content.length > 0;
    }
    return (content == null ? void 0 : content.count) > 0;
  }
  if ([
    "integer",
    "decimal",
    "float",
    "number"
  ].includes(attribute.type)) {
    return typeof content === "number";
  }
  if (attribute.type === "boolean") {
    return content !== null;
  }
  return !(0, import_isEmpty.default)(content);
};
var isSingleRelation = (type) => [
  "oneToOne",
  "manyToOne",
  "oneToOneMorph"
].includes(type);

// node_modules/@strapi/content-manager/dist/admin/pages/ListView/components/ViewSettingsMenu.mjs
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_qs = __toESM(require_lib(), 1);
var ViewSettingsMenu = (props) => {
  const permissions = useTypedSelector((state) => {
    var _a;
    return ((_a = state.admin_app.permissions.contentManager) == null ? void 0 : _a.collectionTypesConfigurations) ?? [];
  });
  const [{ query }] = useQueryParams();
  const { formatMessage } = useIntl();
  const { allowedActions: { canConfigureView } } = useRBAC(permissions);
  return (0, import_jsx_runtime6.jsxs)(Popover.Root, {
    children: [
      (0, import_jsx_runtime6.jsx)(Popover.Trigger, {
        children: (0, import_jsx_runtime6.jsx)(IconButton, {
          label: formatMessage({
            id: "components.ViewSettings.tooltip",
            defaultMessage: "View Settings"
          }),
          children: (0, import_jsx_runtime6.jsx)(ForwardRef$4d, {})
        })
      }),
      (0, import_jsx_runtime6.jsx)(Popover.Content, {
        side: "bottom",
        align: "end",
        sideOffset: 4,
        children: (0, import_jsx_runtime6.jsxs)(Flex, {
          alignItems: "stretch",
          direction: "column",
          padding: 3,
          gap: 3,
          children: [
            canConfigureView ? (0, import_jsx_runtime6.jsx)(LinkButton, {
              size: "S",
              startIcon: (0, import_jsx_runtime6.jsx)(ForwardRef$2n, {}),
              variant: "secondary",
              tag: NavLink,
              to: {
                pathname: "configurations/list",
                search: query.plugins ? (0, import_qs.stringify)({
                  plugins: query.plugins
                }, {
                  encode: false
                }) : ""
              },
              children: formatMessage({
                id: "app.links.configure-view",
                defaultMessage: "Configure the view"
              })
            }) : null,
            (0, import_jsx_runtime6.jsx)(FieldPicker, {
              ...props
            })
          ]
        })
      })
    ]
  });
};
var FieldPicker = ({ headers = [], resetHeaders, setHeaders }) => {
  const { trackUsage } = useTracking();
  const { formatMessage, locale } = useIntl();
  const { schema, model } = useDoc();
  const { list } = useDocumentLayout(model);
  const formatter = useCollator(locale, {
    sensitivity: "base"
  });
  const attributes = (schema == null ? void 0 : schema.attributes) ?? {};
  const columns = Object.keys(attributes).filter((name) => checkIfAttributeIsDisplayable(attributes[name])).map((name) => {
    var _a;
    return {
      name,
      label: ((_a = list.metadatas[name]) == null ? void 0 : _a.label) ?? ""
    };
  }).sort((a, b) => formatter.compare(a.label, b.label));
  const handleChange = (name) => {
    trackUsage("didChangeDisplayedFields");
    const newHeaders = headers.includes(name) ? headers.filter((header) => header !== name) : [
      ...headers,
      name
    ];
    setHeaders(newHeaders);
  };
  const handleReset = () => {
    resetHeaders();
  };
  return (0, import_jsx_runtime6.jsxs)(Flex, {
    tag: "fieldset",
    direction: "column",
    alignItems: "stretch",
    gap: 3,
    borderWidth: 0,
    maxHeight: "240px",
    overflow: "scroll",
    children: [
      (0, import_jsx_runtime6.jsxs)(Flex, {
        justifyContent: "space-between",
        children: [
          (0, import_jsx_runtime6.jsx)(Typography, {
            tag: "legend",
            variant: "pi",
            fontWeight: "bold",
            children: formatMessage({
              id: "containers.list.displayedFields",
              defaultMessage: "Displayed fields"
            })
          }),
          (0, import_jsx_runtime6.jsx)(TextButton, {
            onClick: handleReset,
            children: formatMessage({
              id: "app.components.Button.reset",
              defaultMessage: "Reset"
            })
          })
        ]
      }),
      (0, import_jsx_runtime6.jsx)(Flex, {
        direction: "column",
        alignItems: "stretch",
        children: columns.map((header) => {
          const isActive = headers.includes(header.name);
          return (0, import_jsx_runtime6.jsx)(Flex, {
            wrap: "wrap",
            gap: 2,
            background: isActive ? "primary100" : "transparent",
            hasRadius: true,
            padding: 2,
            children: (0, import_jsx_runtime6.jsx)(CheckboxImpl, {
              onCheckedChange: () => handleChange(header.name),
              checked: isActive,
              name: header.name,
              children: (0, import_jsx_runtime6.jsx)(Typography, {
                fontSize: 1,
                children: header.label
              })
            })
          }, header.name);
        })
      })
    ]
  });
};

// node_modules/@strapi/content-manager/dist/admin/pages/ListView/ListViewPage.mjs
var { INJECT_COLUMN_IN_TABLE } = HOOKS;
var LayoutsHeaderCustom = dt(Layouts.Header)`
  overflow-wrap: anywhere;
`;
var ListViewPage = () => {
  var _a;
  const { trackUsage } = useTracking();
  const navigate = useNavigate();
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler(getTranslation);
  const { collectionType, model, schema } = useDoc();
  const { list } = useDocumentLayout(model);
  const [displayedHeaders, setDisplayedHeaders] = React3.useState([]);
  const listLayout = usePrev(list.layout);
  React3.useEffect(() => {
    if (!(0, import_isEqual.default)(listLayout, list.layout)) {
      setDisplayedHeaders(list.layout);
    }
  }, [
    list.layout,
    listLayout
  ]);
  const handleSetHeaders = (headers) => {
    setDisplayedHeaders(convertListLayoutToFieldLayouts(headers, schema.attributes, list.metadatas));
  };
  const [{ query }] = useQueryParams({
    page: "1",
    pageSize: list.settings.pageSize.toString(),
    sort: list.settings.defaultSortBy ? `${list.settings.defaultSortBy}:${list.settings.defaultSortOrder}` : ""
  });
  const params = React3.useMemo(() => buildValidParams(query), [
    query
  ]);
  const { data, error, isFetching } = useGetAllDocumentsQuery({
    model,
    params
  });
  React3.useEffect(() => {
    if (error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    }
  }, [
    error,
    formatAPIError,
    toggleNotification
  ]);
  const { results = [], pagination } = data ?? {};
  React3.useEffect(() => {
    if (pagination && pagination.pageCount > 0 && pagination.page > pagination.pageCount) {
      navigate({
        search: (0, import_qs2.stringify)({
          ...query,
          page: pagination.pageCount
        })
      }, {
        replace: true
      });
    }
  }, [
    pagination,
    formatMessage,
    query,
    navigate
  ]);
  const { canCreate } = useDocumentRBAC("ListViewPage", ({ canCreate: canCreate2 }) => ({
    canCreate: canCreate2
  }));
  const runHookWaterfall = useStrapiApp("ListViewPage", ({ runHookWaterfall: runHookWaterfall2 }) => runHookWaterfall2);
  const tableHeaders = React3.useMemo(() => {
    var _a2;
    const headers = runHookWaterfall(INJECT_COLUMN_IN_TABLE, {
      displayedHeaders,
      layout: list
    });
    const formattedHeaders = headers.displayedHeaders.map((header) => {
      var _a3;
      const translation = typeof header.label === "string" ? {
        id: `content-manager.content-types.${model}.${header.name}`,
        defaultMessage: header.label
      } : header.label;
      return {
        ...header,
        label: formatMessage(translation),
        name: `${header.name}${((_a3 = header.mainField) == null ? void 0 : _a3.name) ? `.${header.mainField.name}` : ""}`
      };
    });
    if ((_a2 = schema == null ? void 0 : schema.options) == null ? void 0 : _a2.draftAndPublish) {
      formattedHeaders.push({
        attribute: {
          type: "custom"
        },
        name: "status",
        label: formatMessage({
          id: getTranslation(`containers.list.table-headers.status`),
          defaultMessage: "status"
        }),
        searchable: false,
        sortable: false
      });
    }
    return formattedHeaders;
  }, [
    displayedHeaders,
    formatMessage,
    list,
    runHookWaterfall,
    (_a = schema == null ? void 0 : schema.options) == null ? void 0 : _a.draftAndPublish,
    model
  ]);
  if (isFetching) {
    return (0, import_jsx_runtime7.jsx)(Page.Loading, {});
  }
  if (error) {
    return (0, import_jsx_runtime7.jsx)(Page.Error, {});
  }
  const contentTypeTitle = (schema == null ? void 0 : schema.info.displayName) ? formatMessage({
    id: schema.info.displayName,
    defaultMessage: schema.info.displayName
  }) : formatMessage({
    id: "content-manager.containers.untitled",
    defaultMessage: "Untitled"
  });
  const handleRowClick = (id) => () => {
    trackUsage("willEditEntryFromList");
    navigate({
      pathname: id.toString(),
      search: (0, import_qs2.stringify)({
        plugins: query.plugins
      })
    });
  };
  if (!isFetching && results.length === 0) {
    return (0, import_jsx_runtime7.jsxs)(Page.Main, {
      children: [
        (0, import_jsx_runtime7.jsx)(Page.Title, {
          children: `${contentTypeTitle}`
        }),
        (0, import_jsx_runtime7.jsx)(LayoutsHeaderCustom, {
          primaryAction: canCreate ? (0, import_jsx_runtime7.jsx)(CreateButton, {}) : null,
          subtitle: formatMessage({
            id: getTranslation("pages.ListView.header-subtitle"),
            defaultMessage: "{number, plural, =0 {# entries} one {# entry} other {# entries}} found"
          }, {
            number: pagination == null ? void 0 : pagination.total
          }),
          title: contentTypeTitle,
          navigationAction: (0, import_jsx_runtime7.jsx)(BackButton, {})
        }),
        (0, import_jsx_runtime7.jsx)(Layouts.Action, {
          endActions: (0, import_jsx_runtime7.jsxs)(import_jsx_runtime7.Fragment, {
            children: [
              (0, import_jsx_runtime7.jsx)(InjectionZone, {
                area: "listView.actions"
              }),
              (0, import_jsx_runtime7.jsx)(ViewSettingsMenu, {
                setHeaders: handleSetHeaders,
                resetHeaders: () => setDisplayedHeaders(list.layout),
                headers: displayedHeaders.map((header) => header.name)
              })
            ]
          }),
          startActions: (0, import_jsx_runtime7.jsxs)(import_jsx_runtime7.Fragment, {
            children: [
              list.settings.searchable && (0, import_jsx_runtime7.jsx)(SearchInput, {
                disabled: results.length === 0,
                label: formatMessage({
                  id: "app.component.search.label",
                  defaultMessage: "Search for {target}"
                }, {
                  target: contentTypeTitle
                }),
                placeholder: formatMessage({
                  id: "global.search",
                  defaultMessage: "Search"
                }),
                trackedEvent: "didSearch"
              }),
              list.settings.filterable && schema ? (0, import_jsx_runtime7.jsx)(FiltersImpl, {
                disabled: results.length === 0,
                schema
              }) : null
            ]
          })
        }),
        (0, import_jsx_runtime7.jsx)(Layouts.Content, {
          children: (0, import_jsx_runtime7.jsx)(Box, {
            background: "neutral0",
            shadow: "filterShadow",
            hasRadius: true,
            children: (0, import_jsx_runtime7.jsx)(EmptyStateLayout, {
              action: canCreate ? (0, import_jsx_runtime7.jsx)(CreateButton, {
                variant: "secondary"
              }) : null,
              content: formatMessage({
                id: "app.components.EmptyStateLayout.content-document",
                defaultMessage: "No content found"
              }),
              hasRadius: true,
              icon: (0, import_jsx_runtime7.jsx)(ForwardRef$J, {
                width: "16rem"
              })
            })
          })
        })
      ]
    });
  }
  return (0, import_jsx_runtime7.jsxs)(Page.Main, {
    children: [
      (0, import_jsx_runtime7.jsx)(Page.Title, {
        children: `${contentTypeTitle}`
      }),
      (0, import_jsx_runtime7.jsx)(LayoutsHeaderCustom, {
        primaryAction: canCreate ? (0, import_jsx_runtime7.jsx)(CreateButton, {}) : null,
        subtitle: formatMessage({
          id: getTranslation("pages.ListView.header-subtitle"),
          defaultMessage: "{number, plural, =0 {# entries} one {# entry} other {# entries}} found"
        }, {
          number: pagination == null ? void 0 : pagination.total
        }),
        title: contentTypeTitle,
        navigationAction: (0, import_jsx_runtime7.jsx)(BackButton, {})
      }),
      (0, import_jsx_runtime7.jsx)(Layouts.Action, {
        endActions: (0, import_jsx_runtime7.jsxs)(import_jsx_runtime7.Fragment, {
          children: [
            (0, import_jsx_runtime7.jsx)(InjectionZone, {
              area: "listView.actions"
            }),
            (0, import_jsx_runtime7.jsx)(ViewSettingsMenu, {
              setHeaders: handleSetHeaders,
              resetHeaders: () => setDisplayedHeaders(list.layout),
              headers: displayedHeaders.map((header) => header.name)
            })
          ]
        }),
        startActions: (0, import_jsx_runtime7.jsxs)(import_jsx_runtime7.Fragment, {
          children: [
            list.settings.searchable && (0, import_jsx_runtime7.jsx)(SearchInput, {
              disabled: results.length === 0,
              label: formatMessage({
                id: "app.component.search.label",
                defaultMessage: "Search for {target}"
              }, {
                target: contentTypeTitle
              }),
              placeholder: formatMessage({
                id: "global.search",
                defaultMessage: "Search"
              }),
              trackedEvent: "didSearch"
            }),
            list.settings.filterable && schema ? (0, import_jsx_runtime7.jsx)(FiltersImpl, {
              disabled: results.length === 0,
              schema
            }) : null
          ]
        })
      }),
      (0, import_jsx_runtime7.jsx)(Layouts.Content, {
        children: (0, import_jsx_runtime7.jsxs)(Flex, {
          gap: 4,
          direction: "column",
          alignItems: "stretch",
          children: [
            (0, import_jsx_runtime7.jsxs)(Table.Root, {
              rows: results,
              headers: tableHeaders,
              isLoading: isFetching,
              children: [
                (0, import_jsx_runtime7.jsx)(TableActionsBar, {}),
                (0, import_jsx_runtime7.jsxs)(Table.Content, {
                  children: [
                    (0, import_jsx_runtime7.jsxs)(Table.Head, {
                      children: [
                        (0, import_jsx_runtime7.jsx)(Table.HeaderCheckboxCell, {}),
                        tableHeaders.map((header) => (0, import_jsx_runtime7.jsx)(Table.HeaderCell, {
                          ...header
                        }, header.name))
                      ]
                    }),
                    (0, import_jsx_runtime7.jsx)(Table.Loading, {}),
                    (0, import_jsx_runtime7.jsx)(Table.Empty, {
                      action: canCreate ? (0, import_jsx_runtime7.jsx)(CreateButton, {
                        variant: "secondary"
                      }) : null
                    }),
                    (0, import_jsx_runtime7.jsx)(Table.Body, {
                      children: results.map((row) => {
                        return (0, import_jsx_runtime7.jsxs)(Table.Row, {
                          cursor: "pointer",
                          onClick: handleRowClick(row.documentId),
                          children: [
                            (0, import_jsx_runtime7.jsx)(Table.CheckboxCell, {
                              id: row.id
                            }),
                            tableHeaders.map(({ cellFormatter, ...header }) => {
                              if (header.name === "status") {
                                const { status } = row;
                                return (0, import_jsx_runtime7.jsx)(Table.Cell, {
                                  children: (0, import_jsx_runtime7.jsx)(DocumentStatus, {
                                    status,
                                    maxWidth: "min-content"
                                  })
                                }, header.name);
                              }
                              if ([
                                "createdBy",
                                "updatedBy"
                              ].includes(header.name.split(".")[0])) {
                                return (0, import_jsx_runtime7.jsx)(Table.Cell, {
                                  children: (0, import_jsx_runtime7.jsx)(Typography, {
                                    textColor: "neutral800",
                                    children: row[header.name.split(".")[0]] ? getDisplayName(row[header.name.split(".")[0]]) : "-"
                                  })
                                }, header.name);
                              }
                              if (typeof cellFormatter === "function") {
                                return (0, import_jsx_runtime7.jsx)(Table.Cell, {
                                  children: cellFormatter(row, header, {
                                    collectionType,
                                    model
                                  })
                                }, header.name);
                              }
                              return (0, import_jsx_runtime7.jsx)(Table.Cell, {
                                children: (0, import_jsx_runtime7.jsx)(CellContent, {
                                  content: row[header.name.split(".")[0]],
                                  rowId: row.documentId,
                                  ...header
                                })
                              }, header.name);
                            }),
                            (0, import_jsx_runtime7.jsx)(ActionsCell, {
                              onClick: (e) => e.stopPropagation(),
                              children: (0, import_jsx_runtime7.jsx)(TableActions, {
                                document: row
                              })
                            })
                          ]
                        }, row.id);
                      })
                    })
                  ]
                })
              ]
            }),
            (0, import_jsx_runtime7.jsxs)(Pagination.Root, {
              ...pagination,
              onPageSizeChange: () => trackUsage("willChangeNumberOfEntriesPerPage"),
              children: [
                (0, import_jsx_runtime7.jsx)(Pagination.PageSize, {}),
                (0, import_jsx_runtime7.jsx)(Pagination.Links, {})
              ]
            })
          ]
        })
      })
    ]
  });
};
var ActionsCell = dt(Table.Cell)`
  display: flex;
  justify-content: flex-end;
`;
var TableActionsBar = () => {
  var _a, _b;
  const selectRow = useTable("TableActionsBar", (state) => state.selectRow);
  const [{ query }] = useQueryParams();
  const locale = (_b = (_a = query == null ? void 0 : query.plugins) == null ? void 0 : _a.i18n) == null ? void 0 : _b.locale;
  const prevLocale = usePrev(locale);
  React3.useEffect(() => {
    if (prevLocale !== locale) {
      selectRow([]);
    }
  }, [
    selectRow,
    prevLocale,
    locale
  ]);
  return (0, import_jsx_runtime7.jsx)(Table.ActionBar, {
    children: (0, import_jsx_runtime7.jsx)(BulkActionsRenderer, {})
  });
};
var CreateButton = ({ variant }) => {
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const [{ query }] = useQueryParams();
  return (0, import_jsx_runtime7.jsx)(Button, {
    variant,
    tag: Link,
    onClick: () => {
      trackUsage("willCreateEntry", {
        status: "draft"
      });
    },
    startIcon: (0, import_jsx_runtime7.jsx)(ForwardRef$1h, {}),
    style: {
      textDecoration: "none"
    },
    to: {
      pathname: "create",
      search: (0, import_qs2.stringify)({
        plugins: query.plugins
      })
    },
    minWidth: "max-content",
    marginLeft: 2,
    children: formatMessage({
      id: getTranslation("HeaderLayout.button.label-add-entry"),
      defaultMessage: "Create new entry"
    })
  });
};
var ProtectedListViewPage = () => {
  const { slug = "" } = useParams();
  const { permissions = [], isLoading, error } = useRBAC(PERMISSIONS.map((action) => ({
    action,
    subject: slug
  })));
  if (isLoading) {
    return (0, import_jsx_runtime7.jsx)(Page.Loading, {});
  }
  if (error || !slug) {
    return (0, import_jsx_runtime7.jsx)(Page.Error, {});
  }
  return (0, import_jsx_runtime7.jsx)(Page.Protect, {
    permissions,
    children: ({ permissions: permissions2 }) => (0, import_jsx_runtime7.jsx)(DocumentRBAC, {
      permissions: permissions2,
      children: (0, import_jsx_runtime7.jsx)(ListViewPage, {})
    })
  });
};
export {
  ListViewPage,
  ProtectedListViewPage
};
//# sourceMappingURL=ListViewPage-BG6RJRKB.js.map
