{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/components/ComponentIcon.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { Flex, FlexProps } from '@strapi/design-system';\nimport * as Icons from '@strapi/icons';\nimport * as Symbols from '@strapi/icons/symbols';\n\nimport type { Struct } from '@strapi/types';\n\ninterface ComponentIconProps extends FlexProps {\n  showBackground?: boolean;\n  icon?: Struct.ContentTypeSchemaInfo['icon'];\n}\n\nconst ComponentIcon = ({\n  showBackground = true,\n  icon = 'dashboard',\n  ...props\n}: ComponentIconProps) => {\n  const Icon = COMPONENT_ICONS[icon as keyof typeof COMPONENT_ICONS] || COMPONENT_ICONS.dashboard;\n\n  return (\n    <Flex\n      alignItems=\"center\"\n      background={showBackground ? 'neutral200' : undefined}\n      justifyContent=\"center\"\n      height={8}\n      width={8}\n      color=\"neutral600\"\n      borderRadius={showBackground ? '50%' : 0}\n      {...props}\n    >\n      <Icon height=\"2rem\" width=\"2rem\" />\n    </Flex>\n  );\n};\n\nconst COMPONENT_ICONS: Record<string, React.ComponentType<any>> = {\n  alien: Icons.Alien,\n  apps: Icons.GridNine,\n  archive: Icons.Archive,\n  arrowDown: Icons.ArrowDown,\n  arrowLeft: Icons.ArrowLeft,\n  arrowRight: Icons.ArrowRight,\n  arrowUp: Icons.ArrowUp,\n  attachment: Icons.Paperclip,\n  bell: Icons.Bell,\n  bold: Icons.Bold,\n  book: Icons.Book,\n  briefcase: Icons.Briefcase,\n  brush: Icons.PaintBrush,\n  bulletList: Icons.BulletList,\n  calendar: Icons.Calendar,\n  car: Icons.Car,\n  cast: Icons.Cast,\n  chartBubble: Icons.ChartBubble,\n  chartCircle: Icons.ChartCircle,\n  chartPie: Icons.ChartPie,\n  check: Icons.Check,\n  clock: Icons.Clock,\n  cloud: Icons.Cloud,\n  code: Icons.Code,\n  cog: Icons.Cog,\n  collapse: Icons.Collapse,\n  command: Icons.Command,\n  connector: Icons.Faders,\n  crop: Icons.Crop,\n  crown: Icons.Crown,\n  cup: Icons.Coffee,\n  cursor: Icons.Cursor,\n  dashboard: Icons.SquaresFour,\n  database: Icons.Database,\n  discuss: Icons.Discuss,\n  doctor: Icons.Stethoscope,\n  earth: Icons.Earth,\n  emotionHappy: Icons.EmotionHappy,\n  emotionUnhappy: Icons.EmotionUnhappy,\n  envelop: Icons.Mail,\n  exit: Icons.SignOut,\n  expand: Icons.Expand,\n  eye: Icons.Eye,\n  feather: Icons.Feather,\n  file: Icons.File,\n  fileError: Icons.FileError,\n  filePdf: Icons.FilePdf,\n  filter: Icons.Filter,\n  folder: Icons.Folder,\n  gate: Icons.CastleTurret,\n  gift: Icons.Gift,\n  globe: Icons.Globe,\n  grid: Icons.GridFour,\n  handHeart: Icons.HandHeart,\n  hashtag: Icons.Hashtag,\n  headphone: Icons.Headphones,\n  heart: Icons.Heart,\n  house: Icons.House,\n  information: Icons.Information,\n  italic: Icons.Italic,\n  key: Icons.Key,\n  landscape: Icons.Images,\n  layer: Icons.ListPlus,\n  layout: Icons.Layout,\n  lightbulb: Icons.Lightbulb,\n  link: Icons.Link,\n  lock: Icons.Lock,\n  magic: Icons.Magic,\n  manyToMany: Icons.ManyToMany,\n  manyToOne: Icons.ManyToOne,\n  manyWays: Icons.ManyWays,\n  medium: Symbols.Medium,\n  message: Icons.Message,\n  microphone: Icons.Microphone,\n  monitor: Icons.Monitor,\n  moon: Icons.Moon,\n  music: Icons.MusicNotes,\n  oneToMany: Icons.OneToMany,\n  oneToOne: Icons.OneToOne,\n  oneWay: Icons.OneWay,\n  paint: Icons.PaintBrush,\n  paintBrush: Icons.PaintBrush,\n  paperPlane: Icons.PaperPlane,\n  pencil: Icons.Pencil,\n  phone: Icons.Phone,\n  picture: Icons.Image,\n  pin: Icons.Pin,\n  pinMap: Icons.PinMap,\n  plane: Icons.Plane,\n  play: Icons.Play,\n  plus: Icons.Plus,\n  priceTag: Icons.PriceTag,\n  puzzle: Icons.PuzzlePiece,\n  question: Icons.Question,\n  quote: Icons.Quotes,\n  refresh: Icons.ArrowClockwise,\n  restaurant: Icons.Restaurant,\n  rocket: Icons.Rocket,\n  rotate: Icons.ArrowsCounterClockwise,\n  scissors: Icons.Scissors,\n  search: Icons.Search,\n  seed: Icons.Plant,\n  server: Icons.Server,\n  shield: Icons.Shield,\n  shirt: Icons.Shirt,\n  shoppingCart: Icons.ShoppingCart,\n  slideshow: Icons.PresentationChart,\n  stack: Icons.Stack,\n  star: Icons.Star,\n  store: Icons.Store,\n  strikeThrough: Icons.StrikeThrough,\n  sun: Icons.Sun,\n  television: Icons.Television,\n  thumbDown: Icons.ThumbDown,\n  thumbUp: Icons.ThumbUp,\n  train: Icons.Train,\n  twitter: Symbols.X,\n  typhoon: Icons.Typhoon,\n  underline: Icons.Underline,\n  user: Icons.User,\n  volumeMute: Icons.VolumeMute,\n  volumeUp: Icons.VolumeUp,\n  walk: Icons.Walk,\n  wheelchair: Icons.Wheelchair,\n  write: Icons.Feather,\n};\n\nexport { ComponentIcon, COMPONENT_ICONS };\nexport type { ComponentIconProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaMA,IAAAA,gBAAgB,CAAC,EACrBC,iBAAiB,MACjBC,OAAO,aACP,GAAGC,MACgB,MAAA;AACnB,QAAMC,OAAOC,gBAAgBH,IAAqC,KAAIG,gBAAgBC;AAEtF,aACEC,wBAACC,MAAAA;IACCC,YAAW;IACXC,YAAYT,iBAAiB,eAAeU;IAC5CC,gBAAe;IACfC,QAAQ;IACRC,OAAO;IACPC,OAAM;IACNC,cAAcf,iBAAiB,QAAQ;IACtC,GAAGE;IAEJ,cAAAI,wBAACH,MAAAA;MAAKS,QAAO;MAAOC,OAAM;;;AAGhC;AAEA,IAAMT,kBAA4D;EAChEY,OAAaC;EACbC,MAAYC;EACZC,SAAeC;EACfC,WAAiBC;EACjBC,WAAiBC;EACjBC,YAAkBC;EAClBC,SAAeC;EACfC,YAAkBC;EAClBC,MAAYC;EACZC,MAAYC;EACZC,MAAYC;EACZC,WAAiBC;EACjBC,OAAaC;EACbC,YAAkBC;EAClBC,UAAgBC;EAChBC,KAAWC;EACXC,MAAYC;EACZC,aAAmBC;EACnBC,aAAmBC;EACnBC,UAAgBC;EAChBC,OAAaC;EACbC,OAAaC;EACbC,OAAaC;EACbC,MAAYC;EACZC,KAAWC;EACXC,UAAgBC;EAChBC,SAAeC;EACfC,WAAiBC;EACjBC,MAAYC;EACZC,OAAaC;EACbC,KAAWC;EACXC,QAAcC;EACd1E,WAAiB2E;EACjBC,UAAgBC;EAChBC,SAAeC;EACfC,QAAcC;EACdC,OAAaC;EACbC,cAAoBC;EACpBC,gBAAsBC;EACtBC,SAAeC;EACfC,MAAYC;EACZC,QAAcC;EACdC,KAAWC;EACXC,SAAeC;EACfC,MAAYC;EACZC,WAAiBC;EACjBC,SAAeC;EACfC,QAAcC;EACdC,QAAcC;EACdC,MAAYC;EACZC,MAAYC;EACZC,OAAaC;EACbC,MAAYC;EACZC,WAAiBC;EACjBC,SAAeC;EACfC,WAAiBC;EACjBC,OAAaC;EACbC,OAAaC;EACbC,aAAmBC;EACnBC,QAAcC;EACdC,KAAWC;EACXC,WAAiBC;EACjBC,OAAaC;EACbC,QAAcC;EACdC,WAAiBC;EACjBC,MAAYC;EACZC,MAAYC;EACZC,OAAaC;EACbC,YAAkBC;EAClBC,WAAiBC;EACjBC,UAAgBC;EAChBC,QAAgBC;EAChBC,SAAeC;EACfC,YAAkBC;EAClBC,SAAeC;EACfC,MAAYC;EACZC,OAAaC;EACbC,WAAiBC;EACjBC,UAAgBC;EAChBC,QAAcC;EACdC,OAAatI;EACbuI,YAAkBvI;EAClBwI,YAAkBC;EAClBC,QAAcC;EACdC,OAAaC;EACbC,SAAeC;EACfC,KAAWC;EACXC,QAAcC;EACdC,OAAaC;EACbC,MAAYC;EACZC,MAAYC;EACZC,UAAgBC;EAChBC,QAAcC;EACdC,UAAgBC;EAChBC,OAAaC;EACbC,SAAeC;EACfC,YAAkBC;EAClBC,QAAcC;EACdC,QAAcC;EACdC,UAAgBC;EAChBC,QAAcC;EACdC,MAAYC;EACZC,QAAcC;EACdC,QAAcC;EACdC,OAAaC;EACbC,cAAoBC;EACpBC,WAAiBC;EACjBC,OAAaC;EACbC,MAAYC;EACZC,OAAaC;EACbC,eAAqBC;EACrBC,KAAWC;EACXC,YAAkBC;EAClBC,WAAiBC;EACjBC,SAAeC;EACfC,OAAaC;EACbC,SAAiBC;EACjBC,SAAeC;EACfC,WAAiBC;EACjBC,MAAYC;EACZC,YAAkBC;EAClBC,UAAgBC;EAChBC,MAAYC;EACZC,YAAkBC;EAClBC,OAAa/J;AACf;", "names": ["ComponentIcon", "showBackground", "icon", "props", "Icon", "COMPONENT_ICONS", "dashboard", "_jsx", "Flex", "alignItems", "background", "undefined", "justifyContent", "height", "width", "color", "borderRadius", "alien", "Alien", "apps", "GridNine", "archive", "Archive", "arrowDown", "ArrowDown", "arrowLeft", "ArrowLeft", "arrowRight", "ArrowRight", "arrowUp", "ArrowUp", "attachment", "Paperclip", "bell", "Bell", "bold", "Bold", "book", "Book", "briefcase", "Briefcase", "brush", "PaintBrush", "bulletList", "BulletList", "calendar", "Calendar", "car", "Car", "cast", "Cast", "chartBubble", "ChartBubble", "chartCircle", "ChartCircle", "chartPie", "ChartPie", "check", "Check", "clock", "Clock", "cloud", "Cloud", "code", "Code", "cog", "Cog", "collapse", "Collapse", "command", "Command", "connector", "Faders", "crop", "Crop", "crown", "Crown", "cup", "Coffee", "cursor", "<PERSON><PERSON><PERSON>", "SquaresFour", "database", "Database", "discuss", "Discuss", "doctor", "Stethoscope", "earth", "Earth", "emotionHappy", "EmotionHappy", "emotion<PERSON><PERSON><PERSON><PERSON>", "EmotionUnhappy", "envelop", "Mail", "exit", "SignOut", "expand", "Expand", "eye", "Eye", "feather", "<PERSON><PERSON>", "file", "File", "fileError", "FileError", "filePdf", "FilePdf", "filter", "Filter", "folder", "Folder", "gate", "<PERSON><PERSON><PERSON><PERSON>", "gift", "Gift", "globe", "Globe", "grid", "GridFour", "<PERSON><PERSON><PERSON><PERSON>", "HandHeart", "hashtag", "Hashtag", "headphone", "Headphones", "heart", "Heart", "house", "House", "information", "Information", "italic", "Italic", "key", "Key", "landscape", "Images", "layer", "ListPlus", "layout", "Layout", "lightbulb", "Lightbulb", "link", "Link", "lock", "Lock", "magic", "Magic", "manyToMany", "ManyToMany", "manyToOne", "ManyToOne", "manyWays", "ManyWays", "medium", "Medium", "message", "Message", "microphone", "Microphone", "monitor", "Monitor", "moon", "Moon", "music", "MusicNotes", "oneToMany", "OneToMany", "oneToOne", "OneToOne", "oneWay", "OneWay", "paint", "paintBrush", "paperPlane", "PaperPlane", "pencil", "Pencil", "phone", "Phone", "picture", "Image", "pin", "<PERSON>n", "pinMap", "PinMap", "plane", "Plane", "play", "Play", "plus", "Plus", "priceTag", "PriceTag", "puzzle", "PuzzleP<PERSON>ce", "question", "Question", "quote", "Quotes", "refresh", "ArrowClockwise", "restaurant", "Restaurant", "rocket", "Rocket", "rotate", "ArrowsCounterClockwise", "scissors", "Scissors", "search", "Search", "seed", "Plant", "server", "Server", "shield", "Shield", "shirt", "Shirt", "shoppingCart", "ShoppingCart", "slideshow", "Presentation<PERSON>hart", "stack", "<PERSON><PERSON>", "star", "Star", "store", "Store", "strikeThrough", "StrikeThrough", "sun", "Sun", "television", "Television", "thumbDown", "ThumbDown", "thumbUp", "ThumbUp", "train", "Train", "twitter", "X", "typhoon", "Typhoon", "underline", "Underline", "user", "User", "volumeMute", "VolumeMute", "volumeUp", "VolumeUp", "walk", "Walk", "wheelchair", "Wheelchair", "write"]}