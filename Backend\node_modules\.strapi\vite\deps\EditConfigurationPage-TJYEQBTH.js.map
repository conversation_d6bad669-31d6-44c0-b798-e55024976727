{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/pages/EditConfigurationPage.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { Page, useNotification, useTracking, useAPIErrorHandler } from '@strapi/admin/strapi-admin';\nimport { useIntl } from 'react-intl';\n\nimport { TEMP_FIELD_NAME } from '../components/ConfigurationForm/Fields';\nimport { ConfigurationForm, ConfigurationFormProps } from '../components/ConfigurationForm/Form';\nimport { useDoc } from '../hooks/useDocument';\nimport { useDocLayout } from '../hooks/useDocumentLayout';\nimport { useTypedSelector } from '../modules/hooks';\nimport { useUpdateContentTypeConfigurationMutation } from '../services/contentTypes';\nimport { useGetInitialDataQuery } from '../services/init';\nimport { setIn } from '../utils/objects';\n\nimport type { Metadatas } from '../../../shared/contracts/content-types';\n\nconst EditConfigurationPage = () => {\n  const { trackUsage } = useTracking();\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n  const { isLoading: isLoadingSchema, schema, model } = useDoc();\n  const { isLoading: isLoadingLayout, error, list, edit } = useDocLayout();\n\n  const {\n    fieldSizes,\n    error: errorFieldSizes,\n    isLoading: isLoadingFieldSizes,\n    isFetching: isFetchingFieldSizes,\n  } = useGetInitialDataQuery(undefined, {\n    selectFromResult: (res) => {\n      const fieldSizes = Object.entries(res.data?.fieldSizes ?? {}).reduce<\n        ConfigurationFormProps['fieldSizes']\n      >((acc, [attributeName, { default: size }]) => {\n        acc[attributeName] = size;\n\n        return acc;\n      }, {});\n\n      return {\n        isFetching: res.isFetching,\n        isLoading: res.isLoading,\n        error: res.error,\n        fieldSizes,\n      };\n    },\n  });\n\n  React.useEffect(() => {\n    if (errorFieldSizes) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(errorFieldSizes),\n      });\n    }\n  }, [errorFieldSizes, formatAPIError, toggleNotification]);\n\n  const isLoading =\n    isLoadingSchema || isLoadingLayout || isLoadingFieldSizes || isFetchingFieldSizes;\n\n  const [updateConfiguration] = useUpdateContentTypeConfigurationMutation();\n  const handleSubmit: ConfigurationFormProps['onSubmit'] = async (data) => {\n    try {\n      trackUsage('willSaveContentTypeLayout');\n\n      /**\n       * We reconstruct the metadatas object by taking the existing list metadatas\n       * and re-merging that by attribute name with the current list metadatas, whilst overwriting\n       * the data from the form we've built.\n       */\n      const meta = Object.entries(list.metadatas).reduce<Metadatas>(\n        (acc, [name, { mainField: _mainField, ...listMeta }]) => {\n          const existingEditMeta = edit.metadatas[name];\n\n          const {\n            __temp_key__,\n            size: _size,\n            name: _name,\n            ...editedMetadata\n          } = data.layout.flatMap((row) => row.children).find((field) => field.name === name) ?? {};\n\n          acc[name] = {\n            edit: {\n              ...existingEditMeta,\n              ...editedMetadata,\n            },\n            list: listMeta,\n          };\n\n          return acc;\n        },\n        {}\n      );\n\n      const res = await updateConfiguration({\n        layouts: {\n          edit: data.layout.map((row) =>\n            row.children.reduce<Array<{ name: string; size: number }>>((acc, { name, size }) => {\n              if (name !== TEMP_FIELD_NAME) {\n                return [...acc, { name, size }];\n              }\n\n              return acc;\n            }, [])\n          ),\n          list: list.layout.map((field) => field.name),\n        },\n        settings: setIn(data.settings, 'displayName', undefined),\n        metadatas: meta,\n        uid: model,\n      });\n\n      if ('data' in res) {\n        trackUsage('didEditEditSettings');\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({ id: 'notification.success.saved', defaultMessage: 'Saved' }),\n        });\n      } else {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(res.error),\n        });\n      }\n    } catch {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occurred' }),\n      });\n    }\n  };\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  if (errorFieldSizes || error || !schema) {\n    return <Page.Error />;\n  }\n\n  return (\n    <>\n      <Page.Title>{`Configure ${edit.settings.displayName} Edit View`}</Page.Title>\n      <ConfigurationForm\n        onSubmit={handleSubmit}\n        attributes={schema.attributes}\n        fieldSizes={fieldSizes}\n        layout={edit}\n      />\n    </>\n  );\n};\n\nconst ProtectedEditConfigurationPage = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.contentManager?.collectionTypesConfigurations\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <EditConfigurationPage />\n    </Page.Protect>\n  );\n};\n\nexport { ProtectedEditConfigurationPage, EditConfigurationPage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,IAAMA,wBAAwB,MAAA;AAC5B,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,yBAAyBC,eAAc,IAAKC,mBAAAA;AACpD,QAAM,EAAEC,WAAWC,iBAAiBC,QAAQC,MAAK,IAAKC,OAAAA;AACtD,QAAM,EAAEJ,WAAWK,iBAAiBC,OAAOC,MAAMC,KAAI,IAAKC,aAAAA;AAE1D,QAAM,EACJC,YACAJ,OAAOK,iBACPX,WAAWY,qBACXC,YAAYC,qBAAoB,IAC9BC,uBAAuBC,QAAW;IACpCC,kBAAkB,CAACC,QAAAA;;AACjB,YAAMR,cAAaS,OAAOC,UAAQF,SAAIG,SAAJH,mBAAUR,eAAc,CAAA,CAAC,EAAGY,OAE5D,CAACC,KAAK,CAACC,eAAe,EAAEC,SAASC,KAAI,CAAE,MAAC;AACxCH,YAAIC,aAAAA,IAAiBE;AAErB,eAAOH;MACT,GAAG,CAAA,CAAC;AAEJ,aAAO;QACLV,YAAYK,IAAIL;QAChBb,WAAWkB,IAAIlB;QACfM,OAAOY,IAAIZ;QACXI,YAAAA;MACF;IACF;EACF,CAAA;AAEAiB,EAAMC,gBAAU,MAAA;AACd,QAAIjB,iBAAiB;AACnBhB,yBAAmB;QACjBkC,MAAM;QACNC,SAAShC,eAAea,eAAAA;MAC1B,CAAA;IACF;KACC;IAACA;IAAiBb;IAAgBH;EAAmB,CAAA;AAExD,QAAMK,YACJC,mBAAmBI,mBAAmBO,uBAAuBE;AAE/D,QAAM,CAACiB,mBAAAA,IAAuBC,0CAAAA;AAC9B,QAAMC,eAAmD,OAAOZ,SAAAA;AAC9D,QAAI;AACF9B,iBAAW,2BAAA;AAOX,YAAM2C,OAAOf,OAAOC,QAAQb,KAAK4B,SAAS,EAAEb,OAC1C,CAACC,KAAK,CAACa,MAAM,EAAEC,WAAWC,YAAY,GAAGC,SAAAA,CAAU,MAAC;AAClD,cAAMC,mBAAmBhC,KAAK2B,UAAUC,IAAK;AAE7C,cAAM,EACJK,cACAf,MAAMgB,OACNN,MAAMO,OACN,GAAGC,eACJ,IAAGvB,KAAKwB,OAAOC,QAAQ,CAACC,QAAQA,IAAIC,QAAQ,EAAEC,KAAK,CAACC,UAAUA,MAAMd,SAASA,IAAAA,KAAS,CAAA;AAEvFb,YAAIa,IAAAA,IAAQ;UACV5B,MAAM;YACJ,GAAGgC;YACH,GAAGI;UACL;UACArC,MAAMgC;QACR;AAEA,eAAOhB;MACT,GACA,CAAA,CAAC;AAGH,YAAML,MAAM,MAAMa,oBAAoB;QACpCoB,SAAS;UACP3C,MAAMa,KAAKwB,OAAOO,IAAI,CAACL,QACrBA,IAAIC,SAAS1B,OAA8C,CAACC,KAAK,EAAEa,MAAMV,KAAI,MAAE;AAC7E,gBAAIU,SAASiB,iBAAiB;AAC5B,qBAAO;gBAAI9B,GAAAA;gBAAK;kBAAEa;kBAAMV;gBAAK;cAAE;YACjC;AAEA,mBAAOH;UACT,GAAG,CAAA,CAAE,CAAA;UAEPhB,MAAMA,KAAKsC,OAAOO,IAAI,CAACF,UAAUA,MAAMd,IAAI;QAC7C;QACAkB,UAAUC,MAAMlC,KAAKiC,UAAU,eAAetC,MAAAA;QAC9CmB,WAAWD;QACXsB,KAAKrD;MACP,CAAA;AAEA,UAAI,UAAUe,KAAK;AACjB3B,mBAAW,qBAAA;AACXI,2BAAmB;UACjBkC,MAAM;UACNC,SAASrC,cAAc;YAAEgE,IAAI;YAA8BC,gBAAgB;UAAQ,CAAA;QACrF,CAAA;aACK;AACL/D,2BAAmB;UACjBkC,MAAM;UACNC,SAAShC,eAAeoB,IAAIZ,KAAK;QACnC,CAAA;MACF;IACF,QAAQ;AACNX,yBAAmB;QACjBkC,MAAM;QACNC,SAASrC,cAAc;UAAEgE,IAAI;UAAsBC,gBAAgB;QAAoB,CAAA;MACzF,CAAA;IACF;EACF;AAEA,MAAI1D,WAAW;AACb,eAAO2D,wBAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,MAAIlD,mBAAmBL,SAAS,CAACJ,QAAQ;AACvC,eAAOyD,wBAACC,KAAKE,OAAK,CAAA,CAAA;EACpB;AAEA,aACEC,yBAAAC,6BAAA;;UACEL,wBAACC,KAAKK,OAAK;kBAAE,aAAazD,KAAK8C,SAASY,WAAW;;UACnDP,wBAACQ,mBAAAA;QACCC,UAAUnC;QACVoC,YAAYnE,OAAOmE;QACnB3D;QACAmC,QAAQrC;;;;AAIhB;AAEA,IAAM8D,iCAAiC,MAAA;AACrC,QAAMC,cAAcC,iBAClB,CAACC,UAAUA;;AAAAA,uBAAMC,UAAUH,YAAYI,mBAA5BF,mBAA4CG;GAAAA;AAGzD,aACEjB,wBAACC,KAAKiB,SAAO;IAACN;IACZ,cAAAZ,wBAACrE,uBAAAA,CAAAA,CAAAA;;AAGP;", "names": ["EditConfigurationPage", "trackUsage", "useTracking", "formatMessage", "useIntl", "toggleNotification", "useNotification", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "isLoading", "isLoadingSchema", "schema", "model", "useDoc", "isLoadingLayout", "error", "list", "edit", "useDocLayout", "fieldSizes", "errorFieldSizes", "isLoadingFieldSizes", "isFetching", "isFetchingFieldSizes", "useGetInitialDataQuery", "undefined", "selectFromResult", "res", "Object", "entries", "data", "reduce", "acc", "attributeName", "default", "size", "React", "useEffect", "type", "message", "updateConfiguration", "useUpdateContentTypeConfigurationMutation", "handleSubmit", "meta", "metadatas", "name", "mainField", "_mainField", "listMeta", "existingEditMeta", "__temp_key__", "_size", "_name", "editedMetadata", "layout", "flatMap", "row", "children", "find", "field", "layouts", "map", "TEMP_FIELD_NAME", "settings", "setIn", "uid", "id", "defaultMessage", "_jsx", "Page", "Loading", "Error", "_jsxs", "_Fragment", "Title", "displayName", "ConfigurationForm", "onSubmit", "attributes", "ProtectedEditConfigurationPage", "permissions", "useTypedSelector", "state", "admin_app", "contentManager", "collectionTypesConfigurations", "Protect"]}