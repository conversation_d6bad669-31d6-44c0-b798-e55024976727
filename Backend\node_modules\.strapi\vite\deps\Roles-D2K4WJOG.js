import {
  require_prop_types
} from "./chunk-ZYDILIPQ.js";
import {
  require_arrayIncludesWith
} from "./chunk-WJHEGEPQ.js";
import {
  require_sortBy
} from "./chunk-DGNRYZKC.js";
import {
  require_map,
  require_tail
} from "./chunk-P3GECRPJ.js";
import "./chunk-T2YF43GM.js";
import "./chunk-NE3KAGU6.js";
import "./chunk-3P5SDLSO.js";
import "./chunk-2GFELOUK.js";
import "./chunk-NX5ETKE4.js";
import "./chunk-ZHZ667KP.js";
import "./chunk-K2IFNJMI.js";
import "./chunk-HPPKX7ND.js";
import "./chunk-DLRJHTTW.js";
import "./chunk-5ELMBTTI.js";
import "./chunk-6T62IZSE.js";
import "./chunk-MEKEEUV3.js";
import "./chunk-PDGPTUUZ.js";
import "./chunk-TGZYNOOA.js";
import "./chunk-6NZ2ITH7.js";
import "./chunk-6OM5ZNLL.js";
import "./chunk-4LPIPK4X.js";
import "./chunk-2D65YUEQ.js";
import "./chunk-QBVVXK5Q.js";
import "./chunk-GZO3GFLN.js";
import "./chunk-A46TUCLT.js";
import "./chunk-HIZVCZYI.js";
import "./chunk-PPYENV7T.js";
import "./chunk-6LY4MOO2.js";
import "./chunk-H4GPAAVJ.js";
import "./chunk-EKXSMIUH.js";
import "./chunk-C7H2BX76.js";
import "./chunk-SYWYLB7I.js";
import "./chunk-YL7CEHOK.js";
import "./chunk-YV3ONWF5.js";
import "./chunk-UMW22TSS.js";
import "./chunk-4QC3H4VA.js";
import "./chunk-XNACAI67.js";
import {
  useMutation,
  useQueries,
  useQuery
} from "./chunk-75D2ZJP5.js";
import "./chunk-VCTAT6B3.js";
import "./chunk-ROZIXYJG.js";
import "./chunk-53OG7EL5.js";
import "./chunk-C72RZIDJ.js";
import "./chunk-HZKRK7AR.js";
import "./chunk-LRN6A2UC.js";
import "./chunk-D2TGW5YS.js";
import "./chunk-M27D4U76.js";
import "./chunk-HX66WGOY.js";
import {
  useFetchClient
} from "./chunk-Y4UEUAII.js";
import {
  SearchInput
} from "./chunk-BN2UQHMJ.js";
import {
  ConfirmDialog
} from "./chunk-NWWGC2Z2.js";
import "./chunk-DD3MYA7D.js";
import "./chunk-O5ZNSDDU.js";
import "./chunk-MBK4V2X7.js";
import {
  require_isEmpty
} from "./chunk-YJEURQPS.js";
import {
  require_isArrayLikeObject
} from "./chunk-DY2RJG3P.js";
import "./chunk-GGK2TLCV.js";
import "./chunk-K65KIEAL.js";
import {
  require_baseRest
} from "./chunk-BUDFB33L.js";
import {
  Form,
  Formik
} from "./chunk-7MILHJ3J.js";
import {
  require_set
} from "./chunk-SGQJOZK5.js";
import "./chunk-AFM2NWPO.js";
import {
  BackButton
} from "./chunk-DUGZ4WIW.js";
import {
  errorsTrads
} from "./chunk-IFOFBKTA.js";
import {
  create4 as create,
  create6 as create2,
  require_upperFirst
} from "./chunk-376QHLWZ.js";
import "./chunk-EGNP2T5O.js";
import {
  useTracking
} from "./chunk-XDCEA27D.js";
import "./chunk-EZSYDDUK.js";
import {
  require_baseSlice
} from "./chunk-YXDCVYVT.js";
import "./chunk-QIJGNK42.js";
import "./chunk-CJHUGFLE.js";
import "./chunk-IQGHPIIW.js";
import "./chunk-DWSGKQEK.js";
import "./chunk-W6ZGCRX6.js";
import "./chunk-PVCRV2LE.js";
import "./chunk-HWAQQGJJ.js";
import "./chunk-L5JCPKMP.js";
import {
  useRBAC
} from "./chunk-ZJEMJY2Q.js";
import "./chunk-D4WYVNVM.js";
import "./chunk-MMOBCIZG.js";
import "./chunk-6DRYEU2W.js";
import {
  Layouts
} from "./chunk-MTTHLNPH.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import {
  Page,
  useAPIErrorHandler
} from "./chunk-7LKLOY7A.js";
import {
  useQueryParams
} from "./chunk-ODQFI753.js";
import "./chunk-ZOP4VV6J.js";
import {
  require_arrayIncludes,
  require_get,
  require_toInteger
} from "./chunk-WH6VCVXU.js";
import "./chunk-IL5G2D22.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-76QM3EFM.js";
import {
  require_SetCache,
  require_arrayMap,
  require_baseUnary,
  require_cacheHas
} from "./chunk-CE4VABH2.js";
import {
  fn
} from "./chunk-QOUV5O5E.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  Accordion,
  Box,
  Button,
  CheckboxImpl,
  Dialog,
  EmptyStateLayout,
  Field,
  Flex,
  Grid,
  IconButton,
  Link,
  LinkButton,
  Main,
  Table,
  Tbody,
  Td,
  TextInput,
  Textarea,
  Th,
  Thead,
  Tr,
  Typography,
  VisuallyHidden,
  useCollator,
  useFilter,
  useIntl,
  useNotifyAT
} from "./chunk-7GC3Y62Q.js";
import "./chunk-5ZC4PE57.js";
import {
  NavLink,
  Route,
  Routes,
  useMatch,
  useNavigate
} from "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$1h,
  ForwardRef$1v,
  ForwardRef$4F,
  ForwardRef$4d,
  ForwardRef$j
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt,
  lt
} from "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  PERMISSIONS,
  getTrad
} from "./chunk-DJJSG3NG.js";
import {
  __commonJS,
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/_baseDifference.js
var require_baseDifference = __commonJS({
  "node_modules/lodash/_baseDifference.js"(exports, module) {
    var SetCache = require_SetCache();
    var arrayIncludes = require_arrayIncludes();
    var arrayIncludesWith = require_arrayIncludesWith();
    var arrayMap = require_arrayMap();
    var baseUnary = require_baseUnary();
    var cacheHas = require_cacheHas();
    var LARGE_ARRAY_SIZE = 200;
    function baseDifference(array, values, iteratee, comparator) {
      var index = -1, includes = arrayIncludes, isCommon = true, length = array.length, result = [], valuesLength = values.length;
      if (!length) {
        return result;
      }
      if (iteratee) {
        values = arrayMap(values, baseUnary(iteratee));
      }
      if (comparator) {
        includes = arrayIncludesWith;
        isCommon = false;
      } else if (values.length >= LARGE_ARRAY_SIZE) {
        includes = cacheHas;
        isCommon = false;
        values = new SetCache(values);
      }
      outer:
        while (++index < length) {
          var value = array[index], computed = iteratee == null ? value : iteratee(value);
          value = comparator || value !== 0 ? value : 0;
          if (isCommon && computed === computed) {
            var valuesIndex = valuesLength;
            while (valuesIndex--) {
              if (values[valuesIndex] === computed) {
                continue outer;
              }
            }
            result.push(value);
          } else if (!includes(values, computed, comparator)) {
            result.push(value);
          }
        }
      return result;
    }
    module.exports = baseDifference;
  }
});

// node_modules/lodash/without.js
var require_without = __commonJS({
  "node_modules/lodash/without.js"(exports, module) {
    var baseDifference = require_baseDifference();
    var baseRest = require_baseRest();
    var isArrayLikeObject = require_isArrayLikeObject();
    var without2 = baseRest(function(array, values) {
      return isArrayLikeObject(array) ? baseDifference(array, values) : [];
    });
    module.exports = without2;
  }
});

// node_modules/lodash/take.js
var require_take = __commonJS({
  "node_modules/lodash/take.js"(exports, module) {
    var baseSlice = require_baseSlice();
    var toInteger = require_toInteger();
    function take2(array, n, guard) {
      if (!(array && array.length)) {
        return [];
      }
      n = guard || n === void 0 ? 1 : toInteger(n);
      return baseSlice(array, 0, n < 0 ? 0 : n);
    }
    module.exports = take2;
  }
});

// node_modules/@strapi/plugin-users-permissions/dist/admin/pages/Roles/index.mjs
var import_jsx_runtime12 = __toESM(require_jsx_runtime(), 1);
var import_react11 = __toESM(require_react(), 1);

// node_modules/@strapi/plugin-users-permissions/dist/admin/pages/Roles/pages/CreatePage.mjs
var import_jsx_runtime8 = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);

// node_modules/@strapi/plugin-users-permissions/dist/admin/components/UsersPermissions/index.mjs
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var import_react7 = __toESM(require_react(), 1);
var import_prop_types5 = __toESM(require_prop_types(), 1);

// node_modules/@strapi/plugin-users-permissions/dist/admin/contexts/UsersPermissionsContext/index.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_prop_types = __toESM(require_prop_types(), 1);
var UsersPermissions = (0, import_react.createContext)({});
var UsersPermissionsProvider = ({ children, value }) => {
  return (0, import_jsx_runtime.jsx)(UsersPermissions.Provider, {
    value,
    children
  });
};
var useUsersPermissions = () => (0, import_react.useContext)(UsersPermissions);
UsersPermissionsProvider.propTypes = {
  children: import_prop_types.default.node.isRequired,
  value: import_prop_types.default.object.isRequired
};

// node_modules/@strapi/plugin-users-permissions/dist/admin/components/Permissions/index.mjs
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var import_react4 = __toESM(require_react(), 1);

// node_modules/@strapi/plugin-users-permissions/dist/admin/utils/formatPluginName.mjs
var import_upperFirst = __toESM(require_upperFirst(), 1);
function formatPluginName(pluginSlug) {
  switch (pluginSlug) {
    case "application":
      return "Application";
    case "plugin::content-manager":
      return "Content manager";
    case "plugin::content-type-builder":
      return "Content types builder";
    case "plugin::documentation":
      return "Documentation";
    case "plugin::email":
      return "Email";
    case "plugin::i18n":
      return "i18n";
    case "plugin::upload":
      return "Media Library";
    case "plugin::users-permissions":
      return "Users-permissions";
    default:
      return (0, import_upperFirst.default)(pluginSlug.replace("api::", "").replace("plugin::", ""));
  }
}

// node_modules/@strapi/plugin-users-permissions/dist/admin/components/Permissions/init.mjs
var init = (initialState3, permissions) => {
  const collapses = Object.keys(permissions).sort().map((name) => ({
    name,
    isOpen: false
  }));
  return {
    ...initialState3,
    collapses
  };
};

// node_modules/@strapi/plugin-users-permissions/dist/admin/components/Permissions/PermissionRow/index.mjs
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_sortBy = __toESM(require_sortBy(), 1);
var import_prop_types3 = __toESM(require_prop_types(), 1);

// node_modules/@strapi/plugin-users-permissions/dist/admin/components/Permissions/PermissionRow/SubCategory.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_get = __toESM(require_get(), 1);
var import_prop_types2 = __toESM(require_prop_types(), 1);

// node_modules/@strapi/plugin-users-permissions/dist/admin/components/Permissions/PermissionRow/CheckboxWrapper.mjs
var activeCheckboxWrapperStyles = lt`
  background: ${(props) => props.theme.colors.primary100};

  #cog {
    opacity: 1;
  }
`;
var CheckboxWrapper = dt(Box)`
  display: flex;
  justify-content: space-between;
  align-items: center;

  #cog {
    opacity: 0;
    path {
      fill: ${(props) => props.theme.colors.primary600};
    }
  }

  /* Show active style both on hover and when the action is selected */
  ${(props) => props.isActive && activeCheckboxWrapperStyles}
  &:hover {
    ${activeCheckboxWrapperStyles}
  }
`;

// node_modules/@strapi/plugin-users-permissions/dist/admin/components/Permissions/PermissionRow/SubCategory.mjs
var Border = dt.div`
  flex: 1;
  align-self: center;
  border-top: 1px solid ${({ theme }) => theme.colors.neutral150};
`;
var SubCategory = ({ subCategory }) => {
  const { formatMessage } = useIntl();
  const { onChange, onChangeSelectAll, onSelectedAction, selectedAction, modifiedData } = useUsersPermissions();
  const currentScopedModifiedData = (0, import_react2.useMemo)(() => {
    return (0, import_get.default)(modifiedData, subCategory.name, {});
  }, [
    modifiedData,
    subCategory
  ]);
  const hasAllActionsSelected = (0, import_react2.useMemo)(() => {
    return Object.values(currentScopedModifiedData).every((action) => action.enabled === true);
  }, [
    currentScopedModifiedData
  ]);
  const hasSomeActionsSelected = (0, import_react2.useMemo)(() => {
    return Object.values(currentScopedModifiedData).some((action) => action.enabled === true) && !hasAllActionsSelected;
  }, [
    currentScopedModifiedData,
    hasAllActionsSelected
  ]);
  const handleChangeSelectAll = (0, import_react2.useCallback)(({ target: { name } }) => {
    onChangeSelectAll({
      target: {
        name,
        value: !hasAllActionsSelected
      }
    });
  }, [
    hasAllActionsSelected,
    onChangeSelectAll
  ]);
  const isActionSelected = (0, import_react2.useCallback)((actionName) => {
    return selectedAction === actionName;
  }, [
    selectedAction
  ]);
  return (0, import_jsx_runtime2.jsxs)(Box, {
    children: [
      (0, import_jsx_runtime2.jsxs)(Flex, {
        justifyContent: "space-between",
        alignItems: "center",
        children: [
          (0, import_jsx_runtime2.jsx)(Box, {
            paddingRight: 4,
            children: (0, import_jsx_runtime2.jsx)(Typography, {
              variant: "sigma",
              textColor: "neutral600",
              children: subCategory.label
            })
          }),
          (0, import_jsx_runtime2.jsx)(Border, {}),
          (0, import_jsx_runtime2.jsx)(Box, {
            paddingLeft: 4,
            children: (0, import_jsx_runtime2.jsx)(CheckboxImpl, {
              name: subCategory.name,
              checked: hasSomeActionsSelected ? "indeterminate" : hasAllActionsSelected,
              onCheckedChange: (value) => handleChangeSelectAll({
                target: {
                  name: subCategory.name,
                  value
                }
              }),
              children: formatMessage({
                id: "app.utils.select-all",
                defaultMessage: "Select all"
              })
            })
          })
        ]
      }),
      (0, import_jsx_runtime2.jsx)(Flex, {
        paddingTop: 6,
        paddingBottom: 6,
        children: (0, import_jsx_runtime2.jsx)(Grid.Root, {
          gap: 2,
          style: {
            flex: 1
          },
          children: subCategory.actions.map((action) => {
            const name = `${action.name}.enabled`;
            return (0, import_jsx_runtime2.jsx)(Grid.Item, {
              col: 6,
              direction: "column",
              alignItems: "stretch",
              children: (0, import_jsx_runtime2.jsxs)(CheckboxWrapper, {
                isActive: isActionSelected(action.name),
                padding: 2,
                hasRadius: true,
                children: [
                  (0, import_jsx_runtime2.jsx)(CheckboxImpl, {
                    checked: (0, import_get.default)(modifiedData, name, false),
                    name,
                    onCheckedChange: (value) => onChange({
                      target: {
                        name,
                        value
                      }
                    }),
                    children: action.label
                  }),
                  (0, import_jsx_runtime2.jsxs)("button", {
                    type: "button",
                    onClick: () => onSelectedAction(action.name),
                    style: {
                      display: "inline-flex",
                      alignItems: "center"
                    },
                    children: [
                      (0, import_jsx_runtime2.jsx)(VisuallyHidden, {
                        tag: "span",
                        children: formatMessage({
                          id: "app.utils.show-bound-route",
                          defaultMessage: "Show bound route for {route}"
                        }, {
                          route: action.name
                        })
                      }),
                      (0, import_jsx_runtime2.jsx)(ForwardRef$4d, {
                        id: "cog",
                        cursor: "pointer"
                      })
                    ]
                  })
                ]
              })
            }, action.name);
          })
        })
      })
    ]
  });
};
SubCategory.propTypes = {
  subCategory: import_prop_types2.default.object.isRequired
};

// node_modules/@strapi/plugin-users-permissions/dist/admin/components/Permissions/PermissionRow/index.mjs
var PermissionRow = ({ name, permissions }) => {
  const subCategories = (0, import_react3.useMemo)(() => {
    return (0, import_sortBy.default)(Object.values(permissions.controllers).reduce((acc, curr, index) => {
      const currentName = `${name}.controllers.${Object.keys(permissions.controllers)[index]}`;
      const actions = (0, import_sortBy.default)(Object.keys(curr).reduce((acc2, current) => {
        return [
          ...acc2,
          {
            ...curr[current],
            label: current,
            name: `${currentName}.${current}`
          }
        ];
      }, []), "label");
      return [
        ...acc,
        {
          actions,
          label: Object.keys(permissions.controllers)[index],
          name: currentName
        }
      ];
    }, []), "label");
  }, [
    name,
    permissions
  ]);
  return (0, import_jsx_runtime3.jsx)(Box, {
    padding: 6,
    children: subCategories.map((subCategory) => (0, import_jsx_runtime3.jsx)(SubCategory, {
      subCategory
    }, subCategory.name))
  });
};
PermissionRow.propTypes = {
  name: import_prop_types3.default.string.isRequired,
  permissions: import_prop_types3.default.object.isRequired
};

// node_modules/@strapi/plugin-users-permissions/dist/admin/components/Permissions/reducer.mjs
var initialState = {
  collapses: []
};
var reducer = (state, action) => (
  // eslint-disable-next-line consistent-return
  fn(state, (draftState) => {
    switch (action.type) {
      case "TOGGLE_COLLAPSE": {
        draftState.collapses = state.collapses.map((collapse, index) => {
          if (index === action.index) {
            return {
              ...collapse,
              isOpen: !collapse.isOpen
            };
          }
          return {
            ...collapse,
            isOpen: false
          };
        });
        break;
      }
      default:
        return draftState;
    }
  })
);

// node_modules/@strapi/plugin-users-permissions/dist/admin/components/Permissions/index.mjs
var Permissions = () => {
  const { modifiedData } = useUsersPermissions();
  const { formatMessage } = useIntl();
  const [{ collapses }] = (0, import_react4.useReducer)(reducer, initialState, (state) => init(state, modifiedData));
  return (0, import_jsx_runtime4.jsx)(Accordion.Root, {
    size: "M",
    children: (0, import_jsx_runtime4.jsx)(Flex, {
      direction: "column",
      alignItems: "stretch",
      gap: 1,
      children: collapses.map((collapse, index) => (0, import_jsx_runtime4.jsxs)(Accordion.Item, {
        value: collapse.name,
        children: [
          (0, import_jsx_runtime4.jsx)(Accordion.Header, {
            variant: index % 2 === 0 ? "secondary" : void 0,
            children: (0, import_jsx_runtime4.jsx)(Accordion.Trigger, {
              caretPosition: "right",
              description: formatMessage({
                id: "users-permissions.Plugin.permissions.plugins.description",
                defaultMessage: "Define all allowed actions for the {name} plugin."
              }, {
                name: collapse.name
              }),
              children: formatPluginName(collapse.name)
            })
          }),
          (0, import_jsx_runtime4.jsx)(Accordion.Content, {
            children: (0, import_jsx_runtime4.jsx)(PermissionRow, {
              permissions: modifiedData[collapse.name],
              name: collapse.name
            })
          })
        ]
      }, collapse.name))
    })
  });
};

// node_modules/@strapi/plugin-users-permissions/dist/admin/components/Policies/index.mjs
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var import_react6 = __toESM(require_react(), 1);
var import_get2 = __toESM(require_get(), 1);
var import_isEmpty = __toESM(require_isEmpty(), 1);
var import_without = __toESM(require_without(), 1);

// node_modules/@strapi/plugin-users-permissions/dist/admin/components/BoundRoute/index.mjs
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_react5 = __toESM(require_react(), 1);
var import_map = __toESM(require_map(), 1);
var import_tail = __toESM(require_tail(), 1);
var import_prop_types4 = __toESM(require_prop_types(), 1);

// node_modules/@strapi/plugin-users-permissions/dist/admin/components/BoundRoute/getMethodColor.mjs
var getMethodColor = (verb) => {
  switch (verb) {
    case "POST": {
      return {
        text: "success600",
        border: "success200",
        background: "success100"
      };
    }
    case "GET": {
      return {
        text: "secondary600",
        border: "secondary200",
        background: "secondary100"
      };
    }
    case "PUT": {
      return {
        text: "warning600",
        border: "warning200",
        background: "warning100"
      };
    }
    case "DELETE": {
      return {
        text: "danger600",
        border: "danger200",
        background: "danger100"
      };
    }
    default: {
      return {
        text: "neutral600",
        border: "neutral200",
        background: "neutral100"
      };
    }
  }
};

// node_modules/@strapi/plugin-users-permissions/dist/admin/components/BoundRoute/index.mjs
var MethodBox = dt(Box)`
  margin: -1px;
  border-radius: ${({ theme }) => theme.spaces[1]} 0 0 ${({ theme }) => theme.spaces[1]};
`;
function BoundRoute({ route }) {
  const { formatMessage } = useIntl();
  const { method, handler: title, path } = route;
  const formattedRoute = path ? (0, import_tail.default)(path.split("/")) : [];
  const [controller = "", action = ""] = title ? title.split(".") : [];
  const colors = getMethodColor(route.method);
  return (0, import_jsx_runtime5.jsxs)(Flex, {
    direction: "column",
    alignItems: "stretch",
    gap: 2,
    children: [
      (0, import_jsx_runtime5.jsxs)(Typography, {
        variant: "delta",
        tag: "h3",
        children: [
          formatMessage({
            id: "users-permissions.BoundRoute.title",
            defaultMessage: "Bound route to"
          }),
          " ",
          (0, import_jsx_runtime5.jsx)("span", {
            children: controller
          }),
          (0, import_jsx_runtime5.jsxs)(Typography, {
            variant: "delta",
            textColor: "primary600",
            children: [
              ".",
              action
            ]
          })
        ]
      }),
      (0, import_jsx_runtime5.jsxs)(Flex, {
        hasRadius: true,
        background: "neutral0",
        borderColor: "neutral200",
        gap: 0,
        children: [
          (0, import_jsx_runtime5.jsx)(MethodBox, {
            background: colors.background,
            borderColor: colors.border,
            padding: 2,
            children: (0, import_jsx_runtime5.jsx)(Typography, {
              fontWeight: "bold",
              textColor: colors.text,
              children: method
            })
          }),
          (0, import_jsx_runtime5.jsx)(Box, {
            paddingLeft: 2,
            paddingRight: 2,
            children: (0, import_map.default)(formattedRoute, (value) => (0, import_jsx_runtime5.jsxs)(Typography, {
              textColor: value.includes(":") ? "neutral600" : "neutral900",
              children: [
                "/",
                value
              ]
            }, value))
          })
        ]
      })
    ]
  });
}
BoundRoute.defaultProps = {
  route: {
    handler: "Nocontroller.error",
    method: "GET",
    path: "/there-is-no-path"
  }
};
BoundRoute.propTypes = {
  route: import_prop_types4.default.shape({
    handler: import_prop_types4.default.string,
    method: import_prop_types4.default.string,
    path: import_prop_types4.default.string
  })
};

// node_modules/@strapi/plugin-users-permissions/dist/admin/components/Policies/index.mjs
var Policies = () => {
  const { formatMessage } = useIntl();
  const { selectedAction, routes } = useUsersPermissions();
  const path = (0, import_without.default)(selectedAction.split("."), "controllers");
  const controllerRoutes = (0, import_get2.default)(routes, path[0]);
  const pathResolved = path.slice(1).join(".");
  const displayedRoutes = (0, import_isEmpty.default)(controllerRoutes) ? [] : controllerRoutes.filter((o) => o.handler.endsWith(pathResolved));
  return (0, import_jsx_runtime6.jsx)(Grid.Item, {
    col: 5,
    background: "neutral150",
    paddingTop: 6,
    paddingBottom: 6,
    paddingLeft: 7,
    paddingRight: 7,
    style: {
      minHeight: "100%"
    },
    direction: "column",
    alignItems: "stretch",
    children: selectedAction ? (0, import_jsx_runtime6.jsx)(Flex, {
      direction: "column",
      alignItems: "stretch",
      gap: 2,
      children: displayedRoutes.map((route, key) => (
        // eslint-disable-next-line react/no-array-index-key
        (0, import_jsx_runtime6.jsx)(BoundRoute, {
          route
        }, key)
      ))
    }) : (0, import_jsx_runtime6.jsxs)(Flex, {
      direction: "column",
      alignItems: "stretch",
      gap: 2,
      children: [
        (0, import_jsx_runtime6.jsx)(Typography, {
          variant: "delta",
          tag: "h3",
          children: formatMessage({
            id: "users-permissions.Policies.header.title",
            defaultMessage: "Advanced settings"
          })
        }),
        (0, import_jsx_runtime6.jsx)(Typography, {
          tag: "p",
          textColor: "neutral600",
          children: formatMessage({
            id: "users-permissions.Policies.header.hint",
            defaultMessage: "Select the application's actions or the plugin's actions and click on the cog icon to display the bound route"
          })
        })
      ]
    })
  });
};

// node_modules/@strapi/plugin-users-permissions/dist/admin/components/UsersPermissions/init.mjs
var init2 = (state, permissions, routes) => {
  return {
    ...state,
    initialData: permissions,
    modifiedData: permissions,
    routes
  };
};

// node_modules/@strapi/plugin-users-permissions/dist/admin/components/UsersPermissions/reducer.mjs
var import_get3 = __toESM(require_get(), 1);
var import_set = __toESM(require_set(), 1);
var import_take = __toESM(require_take(), 1);
var initialState2 = {
  initialData: {},
  modifiedData: {},
  routes: {},
  selectedAction: "",
  policies: []
};
var reducer2 = (state, action) => fn(state, (draftState) => {
  switch (action.type) {
    case "ON_CHANGE": {
      const keysLength = action.keys.length;
      const isChangingCheckbox = action.keys[keysLength - 1] === "enabled";
      if (action.value && isChangingCheckbox) {
        const selectedAction = (0, import_take.default)(action.keys, keysLength - 1).join(".");
        draftState.selectedAction = selectedAction;
      }
      (0, import_set.default)(draftState, [
        "modifiedData",
        ...action.keys
      ], action.value);
      break;
    }
    case "ON_CHANGE_SELECT_ALL": {
      const pathToValue = [
        "modifiedData",
        ...action.keys
      ];
      const oldValues = (0, import_get3.default)(state, pathToValue, {});
      const updatedValues = Object.keys(oldValues).reduce((acc, current) => {
        acc[current] = {
          ...oldValues[current],
          enabled: action.value
        };
        return acc;
      }, {});
      (0, import_set.default)(draftState, pathToValue, updatedValues);
      break;
    }
    case "ON_RESET": {
      draftState.modifiedData = state.initialData;
      break;
    }
    case "ON_SUBMIT_SUCCEEDED": {
      draftState.initialData = state.modifiedData;
      break;
    }
    case "SELECT_ACTION": {
      const { actionToSelect } = action;
      draftState.selectedAction = actionToSelect === state.selectedAction ? "" : actionToSelect;
      break;
    }
    default:
      return draftState;
  }
});

// node_modules/@strapi/plugin-users-permissions/dist/admin/components/UsersPermissions/index.mjs
var UsersPermissions2 = (0, import_react7.forwardRef)(({ permissions, routes }, ref) => {
  const { formatMessage } = useIntl();
  const [state, dispatch] = (0, import_react7.useReducer)(reducer2, initialState2, (state2) => init2(state2, permissions, routes));
  (0, import_react7.useImperativeHandle)(ref, () => ({
    getPermissions() {
      return {
        permissions: state.modifiedData
      };
    },
    resetForm() {
      dispatch({
        type: "ON_RESET"
      });
    },
    setFormAfterSubmit() {
      dispatch({
        type: "ON_SUBMIT_SUCCEEDED"
      });
    }
  }));
  const handleChange = ({ target: { name, value } }) => dispatch({
    type: "ON_CHANGE",
    keys: name.split("."),
    value: value === "empty__string_value" ? "" : value
  });
  const handleChangeSelectAll = ({ target: { name, value } }) => dispatch({
    type: "ON_CHANGE_SELECT_ALL",
    keys: name.split("."),
    value
  });
  const handleSelectedAction = (actionToSelect) => dispatch({
    type: "SELECT_ACTION",
    actionToSelect
  });
  const providerValue = {
    ...state,
    onChange: handleChange,
    onChangeSelectAll: handleChangeSelectAll,
    onSelectedAction: handleSelectedAction
  };
  return (0, import_jsx_runtime7.jsx)(UsersPermissionsProvider, {
    value: providerValue,
    children: (0, import_jsx_runtime7.jsxs)(Grid.Root, {
      gap: 0,
      shadow: "filterShadow",
      hasRadius: true,
      background: "neutral0",
      children: [
        (0, import_jsx_runtime7.jsx)(Grid.Item, {
          col: 7,
          paddingTop: 6,
          paddingBottom: 6,
          paddingLeft: 7,
          paddingRight: 7,
          direction: "column",
          alignItems: "stretch",
          children: (0, import_jsx_runtime7.jsxs)(Flex, {
            direction: "column",
            alignItems: "stretch",
            gap: 6,
            children: [
              (0, import_jsx_runtime7.jsxs)(Flex, {
                direction: "column",
                alignItems: "stretch",
                gap: 2,
                children: [
                  (0, import_jsx_runtime7.jsx)(Typography, {
                    variant: "delta",
                    tag: "h2",
                    children: formatMessage({
                      id: getTrad("Plugins.header.title"),
                      defaultMessage: "Permissions"
                    })
                  }),
                  (0, import_jsx_runtime7.jsx)(Typography, {
                    tag: "p",
                    textColor: "neutral600",
                    children: formatMessage({
                      id: getTrad("Plugins.header.description"),
                      defaultMessage: "Only actions bound by a route are listed below."
                    })
                  })
                ]
              }),
              (0, import_jsx_runtime7.jsx)(Permissions, {})
            ]
          })
        }),
        (0, import_jsx_runtime7.jsx)(Policies, {})
      ]
    })
  });
});
UsersPermissions2.propTypes = {
  permissions: import_prop_types5.default.object.isRequired,
  routes: import_prop_types5.default.object.isRequired
};
var UsersPermissions$1 = (0, import_react7.memo)(UsersPermissions2);

// node_modules/@strapi/plugin-users-permissions/dist/admin/pages/Roles/constants.mjs
var createRoleSchema = create2().shape({
  name: create().required(errorsTrads.required.id),
  description: create().required(errorsTrads.required.id)
});

// node_modules/@strapi/plugin-users-permissions/dist/admin/pages/Roles/hooks/usePlugins.mjs
var import_react8 = __toESM(require_react(), 1);

// node_modules/@strapi/plugin-users-permissions/dist/admin/utils/cleanPermissions.mjs
var import_isEmpty2 = __toESM(require_isEmpty(), 1);
var cleanPermissions = (permissions) => Object.keys(permissions).reduce((acc, current) => {
  const currentPermission = permissions[current].controllers;
  const cleanedControllers = Object.keys(currentPermission).reduce((acc2, curr) => {
    if ((0, import_isEmpty2.default)(currentPermission[curr])) {
      return acc2;
    }
    acc2[curr] = currentPermission[curr];
    return acc2;
  }, {});
  if ((0, import_isEmpty2.default)(cleanedControllers)) {
    return acc;
  }
  acc[current] = {
    controllers: cleanedControllers
  };
  return acc;
}, {});

// node_modules/@strapi/plugin-users-permissions/dist/admin/pages/Roles/hooks/usePlugins.mjs
var usePlugins = () => {
  const { toggleNotification } = useNotification();
  const { get: get4 } = useFetchClient();
  const { formatAPIError } = useAPIErrorHandler(getTrad);
  const [{ data: permissions, isLoading: isLoadingPermissions, error: permissionsError, refetch: refetchPermissions }, { data: routes, isLoading: isLoadingRoutes, error: routesError, refetch: refetchRoutes }] = useQueries([
    {
      queryKey: [
        "users-permissions",
        "permissions"
      ],
      async queryFn() {
        const { data: { permissions: permissions2 } } = await get4(`/users-permissions/permissions`);
        return permissions2;
      }
    },
    {
      queryKey: [
        "users-permissions",
        "routes"
      ],
      async queryFn() {
        const { data: { routes: routes2 } } = await get4(`/users-permissions/routes`);
        return routes2;
      }
    }
  ]);
  const refetchQueries = async () => {
    await Promise.all([
      refetchPermissions(),
      refetchRoutes()
    ]);
  };
  (0, import_react8.useEffect)(() => {
    if (permissionsError) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(permissionsError)
      });
    }
  }, [
    toggleNotification,
    permissionsError,
    formatAPIError
  ]);
  (0, import_react8.useEffect)(() => {
    if (routesError) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(routesError)
      });
    }
  }, [
    toggleNotification,
    routesError,
    formatAPIError
  ]);
  const isLoading = isLoadingPermissions || isLoadingRoutes;
  return {
    // TODO: these return values need to be memoized, otherwise
    // they will create infinite rendering loops when used as
    // effect dependencies
    permissions: permissions ? cleanPermissions(permissions) : {},
    routes: routes ?? {},
    getData: refetchQueries,
    isLoading
  };
};

// node_modules/@strapi/plugin-users-permissions/dist/admin/pages/Roles/pages/CreatePage.mjs
var CreatePage = () => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const navigate = useNavigate();
  const { isLoading: isLoadingPlugins, permissions, routes } = usePlugins();
  const { trackUsage } = useTracking();
  const permissionsRef = React.useRef();
  const { post } = useFetchClient();
  const mutation = useMutation((body) => post(`/users-permissions/roles`, body), {
    onError() {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "An error occurred"
        })
      });
    },
    onSuccess() {
      trackUsage("didCreateRole");
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: getTrad("Settings.roles.created"),
          defaultMessage: "Role created"
        })
      });
      navigate(-1);
    }
  });
  const handleCreateRoleSubmit = async (data) => {
    const permissions2 = permissionsRef.current.getPermissions();
    await mutation.mutate({
      ...data,
      ...permissions2,
      users: []
    });
  };
  return (0, import_jsx_runtime8.jsxs)(Main, {
    children: [
      (0, import_jsx_runtime8.jsx)(Page.Title, {
        children: formatMessage({
          id: "Settings.PageTitle",
          defaultMessage: "Settings - {name}"
        }, {
          name: "Roles"
        })
      }),
      (0, import_jsx_runtime8.jsx)(Formik, {
        enableReinitialize: true,
        initialValues: {
          name: "",
          description: ""
        },
        onSubmit: handleCreateRoleSubmit,
        validationSchema: createRoleSchema,
        children: ({ handleSubmit, values, handleChange, errors }) => (0, import_jsx_runtime8.jsxs)(Form, {
          noValidate: true,
          onSubmit: handleSubmit,
          children: [
            (0, import_jsx_runtime8.jsx)(Layouts.Header, {
              primaryAction: !isLoadingPlugins && (0, import_jsx_runtime8.jsx)(Button, {
                type: "submit",
                loading: mutation.isLoading,
                startIcon: (0, import_jsx_runtime8.jsx)(ForwardRef$4F, {}),
                children: formatMessage({
                  id: "global.save",
                  defaultMessage: "Save"
                })
              }),
              title: formatMessage({
                id: "Settings.roles.create.title",
                defaultMessage: "Create a role"
              }),
              subtitle: formatMessage({
                id: "Settings.roles.create.description",
                defaultMessage: "Define the rights given to the role"
              })
            }),
            (0, import_jsx_runtime8.jsx)(Layouts.Content, {
              children: (0, import_jsx_runtime8.jsxs)(Flex, {
                background: "neutral0",
                direction: "column",
                alignItems: "stretch",
                gap: 7,
                hasRadius: true,
                paddingTop: 6,
                paddingBottom: 6,
                paddingLeft: 7,
                paddingRight: 7,
                shadow: "filterShadow",
                children: [
                  (0, import_jsx_runtime8.jsxs)(Flex, {
                    direction: "column",
                    alignItems: "stretch",
                    children: [
                      (0, import_jsx_runtime8.jsx)(Typography, {
                        variant: "delta",
                        tag: "h2",
                        children: formatMessage({
                          id: getTrad("EditPage.form.roles"),
                          defaultMessage: "Role details"
                        })
                      }),
                      (0, import_jsx_runtime8.jsxs)(Grid.Root, {
                        gap: 4,
                        children: [
                          (0, import_jsx_runtime8.jsx)(Grid.Item, {
                            col: 6,
                            direction: "column",
                            alignItems: "stretch",
                            children: (0, import_jsx_runtime8.jsxs)(Field.Root, {
                              name: "name",
                              error: (errors == null ? void 0 : errors.name) ? formatMessage({
                                id: errors.name,
                                defaultMessage: "Name is required"
                              }) : false,
                              required: true,
                              children: [
                                (0, import_jsx_runtime8.jsx)(Field.Label, {
                                  children: formatMessage({
                                    id: "global.name",
                                    defaultMessage: "Name"
                                  })
                                }),
                                (0, import_jsx_runtime8.jsx)(TextInput, {
                                  value: values.name || "",
                                  onChange: handleChange
                                }),
                                (0, import_jsx_runtime8.jsx)(Field.Error, {})
                              ]
                            })
                          }),
                          (0, import_jsx_runtime8.jsx)(Grid.Item, {
                            col: 6,
                            direction: "column",
                            alignItems: "stretch",
                            children: (0, import_jsx_runtime8.jsxs)(Field.Root, {
                              name: "description",
                              error: (errors == null ? void 0 : errors.description) ? formatMessage({
                                id: errors.description,
                                defaultMessage: "Description is required"
                              }) : false,
                              required: true,
                              children: [
                                (0, import_jsx_runtime8.jsx)(Field.Label, {
                                  children: formatMessage({
                                    id: "global.description",
                                    defaultMessage: "Description"
                                  })
                                }),
                                (0, import_jsx_runtime8.jsx)(Textarea, {
                                  value: values.description || "",
                                  onChange: handleChange
                                }),
                                (0, import_jsx_runtime8.jsx)(Field.Error, {})
                              ]
                            })
                          })
                        ]
                      })
                    ]
                  }),
                  !isLoadingPlugins && (0, import_jsx_runtime8.jsx)(UsersPermissions$1, {
                    ref: permissionsRef,
                    permissions,
                    routes
                  })
                ]
              })
            })
          ]
        })
      })
    ]
  });
};
var ProtectedRolesCreatePage = () => (0, import_jsx_runtime8.jsx)(Page.Protect, {
  permissions: PERMISSIONS.createRole,
  children: (0, import_jsx_runtime8.jsx)(CreatePage, {})
});

// node_modules/@strapi/plugin-users-permissions/dist/admin/pages/Roles/pages/EditPage.mjs
var import_jsx_runtime9 = __toESM(require_jsx_runtime(), 1);
var React2 = __toESM(require_react(), 1);
var EditPage = () => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { params: { id } } = useMatch(`/settings/users-permissions/roles/:id`);
  const { get: get4 } = useFetchClient();
  const { isLoading: isLoadingPlugins, routes } = usePlugins();
  const { data: role, isLoading: isLoadingRole, refetch: refetchRole } = useQuery([
    "users-permissions",
    "role",
    id
  ], async () => {
    const { data: { role: role2 } } = await get4(`/users-permissions/roles/${id}`);
    return role2;
  });
  const permissionsRef = React2.useRef();
  const { put } = useFetchClient();
  const { formatAPIError } = useAPIErrorHandler();
  const mutation = useMutation((body) => put(`/users-permissions/roles/${id}`, body), {
    onError(error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    },
    async onSuccess() {
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: getTrad("Settings.roles.created"),
          defaultMessage: "Role edited"
        })
      });
      await refetchRole();
    }
  });
  const handleEditRoleSubmit = async (data) => {
    const permissions = permissionsRef.current.getPermissions();
    await mutation.mutate({
      ...data,
      ...permissions,
      users: []
    });
  };
  if (isLoadingRole) {
    return (0, import_jsx_runtime9.jsx)(Page.Loading, {});
  }
  return (0, import_jsx_runtime9.jsxs)(Main, {
    children: [
      (0, import_jsx_runtime9.jsx)(Page.Title, {
        children: formatMessage({
          id: "Settings.PageTitle",
          defaultMessage: "Settings - {name}"
        }, {
          name: "Roles"
        })
      }),
      (0, import_jsx_runtime9.jsx)(Formik, {
        enableReinitialize: true,
        initialValues: {
          name: role.name,
          description: role.description
        },
        onSubmit: handleEditRoleSubmit,
        validationSchema: createRoleSchema,
        children: ({ handleSubmit, values, handleChange, errors }) => (0, import_jsx_runtime9.jsxs)(Form, {
          noValidate: true,
          onSubmit: handleSubmit,
          children: [
            (0, import_jsx_runtime9.jsx)(Layouts.Header, {
              primaryAction: !isLoadingPlugins ? (0, import_jsx_runtime9.jsx)(Button, {
                disabled: role.code === "strapi-super-admin",
                type: "submit",
                loading: mutation.isLoading,
                startIcon: (0, import_jsx_runtime9.jsx)(ForwardRef$4F, {}),
                children: formatMessage({
                  id: "global.save",
                  defaultMessage: "Save"
                })
              }) : null,
              title: role.name,
              subtitle: role.description,
              navigationAction: (0, import_jsx_runtime9.jsx)(BackButton, {
                fallback: ".."
              })
            }),
            (0, import_jsx_runtime9.jsx)(Layouts.Content, {
              children: (0, import_jsx_runtime9.jsxs)(Flex, {
                background: "neutral0",
                direction: "column",
                alignItems: "stretch",
                gap: 7,
                hasRadius: true,
                paddingTop: 6,
                paddingBottom: 6,
                paddingLeft: 7,
                paddingRight: 7,
                shadow: "filterShadow",
                children: [
                  (0, import_jsx_runtime9.jsxs)(Flex, {
                    direction: "column",
                    alignItems: "stretch",
                    gap: 4,
                    children: [
                      (0, import_jsx_runtime9.jsx)(Typography, {
                        variant: "delta",
                        tag: "h2",
                        children: formatMessage({
                          id: getTrad("EditPage.form.roles"),
                          defaultMessage: "Role details"
                        })
                      }),
                      (0, import_jsx_runtime9.jsxs)(Grid.Root, {
                        gap: 4,
                        children: [
                          (0, import_jsx_runtime9.jsx)(Grid.Item, {
                            col: 6,
                            direction: "column",
                            alignItems: "stretch",
                            children: (0, import_jsx_runtime9.jsxs)(Field.Root, {
                              name: "name",
                              error: (errors == null ? void 0 : errors.name) ? formatMessage({
                                id: errors.name,
                                defaultMessage: "Name is required"
                              }) : false,
                              required: true,
                              children: [
                                (0, import_jsx_runtime9.jsx)(Field.Label, {
                                  children: formatMessage({
                                    id: "global.name",
                                    defaultMessage: "Name"
                                  })
                                }),
                                (0, import_jsx_runtime9.jsx)(TextInput, {
                                  value: values.name || "",
                                  onChange: handleChange
                                }),
                                (0, import_jsx_runtime9.jsx)(Field.Error, {})
                              ]
                            })
                          }),
                          (0, import_jsx_runtime9.jsx)(Grid.Item, {
                            col: 6,
                            direction: "column",
                            alignItems: "stretch",
                            children: (0, import_jsx_runtime9.jsxs)(Field.Root, {
                              name: "description",
                              error: (errors == null ? void 0 : errors.description) ? formatMessage({
                                id: errors.description,
                                defaultMessage: "Description is required"
                              }) : false,
                              required: true,
                              children: [
                                (0, import_jsx_runtime9.jsx)(Field.Label, {
                                  children: formatMessage({
                                    id: "global.description",
                                    defaultMessage: "Description"
                                  })
                                }),
                                (0, import_jsx_runtime9.jsx)(Textarea, {
                                  value: values.description || "",
                                  onChange: handleChange
                                }),
                                (0, import_jsx_runtime9.jsx)(Field.Error, {})
                              ]
                            })
                          })
                        ]
                      })
                    ]
                  }),
                  !isLoadingPlugins && (0, import_jsx_runtime9.jsx)(UsersPermissions$1, {
                    ref: permissionsRef,
                    permissions: role.permissions,
                    routes
                  })
                ]
              })
            })
          ]
        })
      })
    ]
  });
};
var ProtectedRolesEditPage = () => (0, import_jsx_runtime9.jsx)(Page.Protect, {
  permissions: PERMISSIONS.updateRole,
  children: (0, import_jsx_runtime9.jsx)(EditPage, {})
});

// node_modules/@strapi/plugin-users-permissions/dist/admin/pages/Roles/pages/ListPage/index.mjs
var import_jsx_runtime11 = __toESM(require_jsx_runtime(), 1);
var import_react10 = __toESM(require_react(), 1);
var import_isEmpty3 = __toESM(require_isEmpty(), 1);

// node_modules/@strapi/plugin-users-permissions/dist/admin/pages/Roles/pages/ListPage/components/TableBody.mjs
var import_jsx_runtime10 = __toESM(require_jsx_runtime(), 1);
var import_react9 = __toESM(require_react(), 1);
var import_prop_types6 = __toESM(require_prop_types(), 1);
var EditLink = dt(Link)`
  align-items: center;
  height: 3.2rem;
  width: 3.2rem;
  display: flex;
  justify-content: center;
  padding: ${({ theme }) => `${theme.spaces[2]}`};

  svg {
    height: 1.6rem;
    width: 1.6rem;

    path {
      fill: ${({ theme }) => theme.colors.neutral500};
    }
  }

  &:hover,
  &:focus {
    svg {
      path {
        fill: ${({ theme }) => theme.colors.neutral800};
      }
    }
  }
`;
var TableBody = ({ sortedRoles, canDelete, canUpdate, setRoleToDelete, onDelete }) => {
  const { formatMessage } = useIntl();
  const navigate = useNavigate();
  const [showConfirmDelete, setShowConfirmDelete] = onDelete;
  const checkCanDeleteRole = (role) => canDelete && ![
    "public",
    "authenticated"
  ].includes(role.type);
  const handleClickDelete = (id) => {
    setRoleToDelete(id);
    setShowConfirmDelete(!showConfirmDelete);
  };
  return (0, import_jsx_runtime10.jsx)(Tbody, {
    children: sortedRoles == null ? void 0 : sortedRoles.map((role) => (0, import_jsx_runtime10.jsxs)(Tr, {
      cursor: "pointer",
      onClick: () => navigate(role.id.toString()),
      children: [
        (0, import_jsx_runtime10.jsx)(Td, {
          width: "20%",
          children: (0, import_jsx_runtime10.jsx)(Typography, {
            children: role.name
          })
        }),
        (0, import_jsx_runtime10.jsx)(Td, {
          width: "50%",
          children: (0, import_jsx_runtime10.jsx)(Typography, {
            children: role.description
          })
        }),
        (0, import_jsx_runtime10.jsx)(Td, {
          width: "30%",
          children: (0, import_jsx_runtime10.jsx)(Typography, {
            children: formatMessage({
              id: "Roles.RoleRow.user-count",
              defaultMessage: "{number, plural, =0 {# user} one {# user} other {# users}}"
            }, {
              number: role.nb_users
            })
          })
        }),
        (0, import_jsx_runtime10.jsx)(Td, {
          children: (0, import_jsx_runtime10.jsxs)(Flex, {
            justifyContent: "end",
            onClick: (e) => e.stopPropagation(),
            children: [
              canUpdate ? (0, import_jsx_runtime10.jsx)(EditLink, {
                tag: NavLink,
                to: role.id.toString(),
                "aria-label": formatMessage({
                  id: "app.component.table.edit",
                  defaultMessage: "Edit {target}"
                }, {
                  target: `${role.name}`
                }),
                children: (0, import_jsx_runtime10.jsx)(ForwardRef$1v, {})
              }) : null,
              checkCanDeleteRole(role) && (0, import_jsx_runtime10.jsx)(IconButton, {
                onClick: () => handleClickDelete(role.id.toString()),
                variant: "ghost",
                label: formatMessage({
                  id: "global.delete-target",
                  defaultMessage: "Delete {target}"
                }, {
                  target: `${role.name}`
                }),
                children: (0, import_jsx_runtime10.jsx)(ForwardRef$j, {})
              })
            ]
          })
        })
      ]
    }, role.name))
  });
};
TableBody.defaultProps = {
  canDelete: false,
  canUpdate: false
};
TableBody.propTypes = {
  onDelete: import_prop_types6.default.array.isRequired,
  setRoleToDelete: import_prop_types6.default.func.isRequired,
  sortedRoles: import_prop_types6.default.array.isRequired,
  canDelete: import_prop_types6.default.bool,
  canUpdate: import_prop_types6.default.bool
};

// node_modules/@strapi/plugin-users-permissions/dist/admin/pages/Roles/pages/ListPage/index.mjs
var RolesListPage = () => {
  const { trackUsage } = useTracking();
  const { formatMessage, locale } = useIntl();
  const { toggleNotification } = useNotification();
  const { notifyStatus } = useNotifyAT();
  const [{ query }] = useQueryParams();
  const _q = (query == null ? void 0 : query._q) || "";
  const [showConfirmDelete, setShowConfirmDelete] = (0, import_react10.useState)(false);
  const [roleToDelete, setRoleToDelete] = (0, import_react10.useState)();
  const { del, get: get4 } = useFetchClient();
  const { isLoading: isLoadingForPermissions, allowedActions: { canRead, canDelete, canCreate, canUpdate } } = useRBAC({
    create: PERMISSIONS.createRole,
    read: PERMISSIONS.readRoles,
    update: PERMISSIONS.updateRole,
    delete: PERMISSIONS.deleteRole
  });
  const { isLoading: isLoadingForData, data: { roles }, isFetching, refetch } = useQuery("get-roles", () => fetchData(toggleNotification, formatMessage, notifyStatus), {
    initialData: {},
    enabled: canRead
  });
  const { contains } = useFilter(locale, {
    sensitivity: "base"
  });
  const formatter = useCollator(locale, {
    sensitivity: "base"
  });
  const isLoading = isLoadingForData || isFetching || isLoadingForPermissions;
  const handleShowConfirmDelete = () => {
    setShowConfirmDelete(!showConfirmDelete);
  };
  const deleteData = async (id, formatMessage2, toggleNotification2) => {
    try {
      await del(`/users-permissions/roles/${id}`);
    } catch (error) {
      toggleNotification2({
        type: "danger",
        message: formatMessage2({
          id: "notification.error",
          defaultMessage: "An error occured"
        })
      });
    }
  };
  const fetchData = async (toggleNotification2, formatMessage2, notifyStatus2) => {
    try {
      const { data } = await get4("/users-permissions/roles");
      notifyStatus2("The roles have loaded successfully");
      return data;
    } catch (err) {
      toggleNotification2({
        type: "danger",
        message: formatMessage2({
          id: "notification.error",
          defaultMessage: "An error occurred"
        })
      });
      throw new Error(err);
    }
  };
  const emptyLayout = {
    roles: {
      id: getTrad("Roles.empty"),
      defaultMessage: "You don't have any roles yet."
    },
    search: {
      id: getTrad("Roles.empty.search"),
      defaultMessage: "No roles match the search."
    }
  };
  const pageTitle = formatMessage({
    id: "global.roles",
    defaultMessage: "Roles"
  });
  const deleteMutation = useMutation((id) => deleteData(id, formatMessage, toggleNotification), {
    async onSuccess() {
      await refetch();
    }
  });
  const handleConfirmDelete = async () => {
    await deleteMutation.mutateAsync(roleToDelete);
    setShowConfirmDelete(!showConfirmDelete);
  };
  const sortedRoles = (roles || []).filter((role) => contains(role.name, _q) || contains(role.description, _q)).sort((a, b) => formatter.compare(a.name, b.name) || formatter.compare(a.description, b.description));
  const emptyContent = _q && !sortedRoles.length ? "search" : "roles";
  const colCount = 4;
  const rowCount = ((roles == null ? void 0 : roles.length) || 0) + 1;
  if (isLoading) {
    return (0, import_jsx_runtime11.jsx)(Page.Loading, {});
  }
  return (0, import_jsx_runtime11.jsxs)(Layouts.Root, {
    children: [
      (0, import_jsx_runtime11.jsx)(Page.Title, {
        children: formatMessage({
          id: "Settings.PageTitle",
          defaultMessage: "Settings - {name}"
        }, {
          name: pageTitle
        })
      }),
      (0, import_jsx_runtime11.jsxs)(Page.Main, {
        children: [
          (0, import_jsx_runtime11.jsx)(Layouts.Header, {
            title: formatMessage({
              id: "global.roles",
              defaultMessage: "Roles"
            }),
            subtitle: formatMessage({
              id: "Settings.roles.list.description",
              defaultMessage: "List of roles"
            }),
            primaryAction: canCreate ? (0, import_jsx_runtime11.jsx)(LinkButton, {
              to: "new",
              tag: NavLink,
              onClick: () => trackUsage("willCreateRole"),
              startIcon: (0, import_jsx_runtime11.jsx)(ForwardRef$1h, {}),
              size: "S",
              children: formatMessage({
                id: getTrad("List.button.roles"),
                defaultMessage: "Add new role"
              })
            }) : null
          }),
          (0, import_jsx_runtime11.jsx)(Layouts.Action, {
            startActions: (0, import_jsx_runtime11.jsx)(SearchInput, {
              label: formatMessage({
                id: "app.component.search.label",
                defaultMessage: "Search"
              })
            })
          }),
          (0, import_jsx_runtime11.jsxs)(Layouts.Content, {
            children: [
              !canRead && (0, import_jsx_runtime11.jsx)(Page.NoPermissions, {}),
              canRead && sortedRoles && (sortedRoles == null ? void 0 : sortedRoles.length) ? (0, import_jsx_runtime11.jsxs)(Table, {
                colCount,
                rowCount,
                children: [
                  (0, import_jsx_runtime11.jsx)(Thead, {
                    children: (0, import_jsx_runtime11.jsxs)(Tr, {
                      children: [
                        (0, import_jsx_runtime11.jsx)(Th, {
                          children: (0, import_jsx_runtime11.jsx)(Typography, {
                            variant: "sigma",
                            textColor: "neutral600",
                            children: formatMessage({
                              id: "global.name",
                              defaultMessage: "Name"
                            })
                          })
                        }),
                        (0, import_jsx_runtime11.jsx)(Th, {
                          children: (0, import_jsx_runtime11.jsx)(Typography, {
                            variant: "sigma",
                            textColor: "neutral600",
                            children: formatMessage({
                              id: "global.description",
                              defaultMessage: "Description"
                            })
                          })
                        }),
                        (0, import_jsx_runtime11.jsx)(Th, {
                          children: (0, import_jsx_runtime11.jsx)(Typography, {
                            variant: "sigma",
                            textColor: "neutral600",
                            children: formatMessage({
                              id: "global.users",
                              defaultMessage: "Users"
                            })
                          })
                        }),
                        (0, import_jsx_runtime11.jsx)(Th, {
                          children: (0, import_jsx_runtime11.jsx)(VisuallyHidden, {
                            children: formatMessage({
                              id: "global.actions",
                              defaultMessage: "Actions"
                            })
                          })
                        })
                      ]
                    })
                  }),
                  (0, import_jsx_runtime11.jsx)(TableBody, {
                    sortedRoles,
                    canDelete,
                    canUpdate,
                    permissions: PERMISSIONS,
                    setRoleToDelete,
                    onDelete: [
                      showConfirmDelete,
                      setShowConfirmDelete
                    ]
                  })
                ]
              }) : (0, import_jsx_runtime11.jsx)(EmptyStateLayout, {
                content: formatMessage(emptyLayout[emptyContent])
              })
            ]
          }),
          (0, import_jsx_runtime11.jsx)(Dialog.Root, {
            open: showConfirmDelete,
            onOpenChange: handleShowConfirmDelete,
            children: (0, import_jsx_runtime11.jsx)(ConfirmDialog, {
              onConfirm: handleConfirmDelete
            })
          })
        ]
      })
    ]
  });
};
var ProtectedRolesListPage = () => {
  return (0, import_jsx_runtime11.jsx)(Page.Protect, {
    permissions: PERMISSIONS.accessRoles,
    children: (0, import_jsx_runtime11.jsx)(RolesListPage, {})
  });
};

// node_modules/@strapi/plugin-users-permissions/dist/admin/pages/Roles/index.mjs
var Roles = () => {
  return (0, import_jsx_runtime12.jsx)(Page.Protect, {
    permissions: PERMISSIONS.accessRoles,
    children: (0, import_jsx_runtime12.jsxs)(Routes, {
      children: [
        (0, import_jsx_runtime12.jsx)(Route, {
          index: true,
          element: (0, import_jsx_runtime12.jsx)(ProtectedRolesListPage, {})
        }),
        (0, import_jsx_runtime12.jsx)(Route, {
          path: "new",
          element: (0, import_jsx_runtime12.jsx)(ProtectedRolesCreatePage, {})
        }),
        (0, import_jsx_runtime12.jsx)(Route, {
          path: ":id",
          element: (0, import_jsx_runtime12.jsx)(ProtectedRolesEditPage, {})
        })
      ]
    })
  });
};
export {
  Roles as default
};
//# sourceMappingURL=Roles-D2K4WJOG.js.map
