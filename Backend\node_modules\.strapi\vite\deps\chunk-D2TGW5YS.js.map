{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Auth/components/Login.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { Box, Button, Flex, Main, Typography, Link } from '@strapi/design-system';\nimport camelCase from 'lodash/camelCase';\nimport { useIntl } from 'react-intl';\nimport { NavLink, useLocation, useNavigate } from 'react-router-dom';\nimport * as yup from 'yup';\n\nimport { Form } from '../../../components/Form';\nimport { InputRenderer } from '../../../components/FormInputs/Renderer';\nimport { Logo } from '../../../components/UnauthenticatedLogo';\nimport { useAuth } from '../../../features/Auth';\nimport {\n  UnauthenticatedLayout,\n  Column,\n  LayoutContent,\n} from '../../../layouts/UnauthenticatedLayout';\nimport { translatedErrors } from '../../../utils/translatedErrors';\n\nimport type { Login } from '../../../../../shared/contracts/authentication';\n\ninterface LoginProps {\n  children?: React.ReactNode;\n}\n\nconst LOGIN_SCHEMA = yup.object().shape({\n  email: yup\n    .string()\n    .nullable()\n    .email({\n      id: translatedErrors.email.id,\n      defaultMessage: 'Not a valid email',\n    })\n    .required(translatedErrors.required),\n  password: yup.string().required(translatedErrors.required).nullable(),\n  rememberMe: yup.bool().nullable(),\n});\n\nconst Login = ({ children }: LoginProps) => {\n  const [apiError, setApiError] = React.useState<string>();\n  const { formatMessage } = useIntl();\n  const { search: searchString } = useLocation();\n  const query = React.useMemo(() => new URLSearchParams(searchString), [searchString]);\n  const navigate = useNavigate();\n\n  const { login } = useAuth('Login', (auth) => auth);\n\n  const handleLogin = async (body: Parameters<typeof login>[0]) => {\n    setApiError(undefined);\n\n    const res = await login(body);\n\n    if ('error' in res) {\n      const message = res.error.message ?? 'Something went wrong';\n\n      if (camelCase(message).toLowerCase() === 'usernotactive') {\n        navigate('/auth/oops');\n        return;\n      }\n\n      setApiError(message);\n    } else {\n      const redirectTo = query.get('redirectTo');\n      const redirectUrl = redirectTo ? decodeURIComponent(redirectTo) : '/';\n\n      navigate(redirectUrl);\n    }\n  };\n\n  return (\n    <UnauthenticatedLayout>\n      <Main>\n        <LayoutContent>\n          <Column>\n            <Logo />\n            <Box paddingTop={6} paddingBottom={1}>\n              <Typography variant=\"alpha\" tag=\"h1\">\n                {formatMessage({\n                  id: 'Auth.form.welcome.title',\n                  defaultMessage: 'Welcome!',\n                })}\n              </Typography>\n            </Box>\n            <Box paddingBottom={7}>\n              <Typography variant=\"epsilon\" textColor=\"neutral600\">\n                {formatMessage({\n                  id: 'Auth.form.welcome.subtitle',\n                  defaultMessage: 'Log in to your Strapi account',\n                })}\n              </Typography>\n            </Box>\n            {apiError ? (\n              <Typography id=\"global-form-error\" role=\"alert\" tabIndex={-1} textColor=\"danger600\">\n                {apiError}\n              </Typography>\n            ) : null}\n          </Column>\n          <Form\n            method=\"PUT\"\n            initialValues={{\n              email: '',\n              password: '',\n              rememberMe: false,\n            }}\n            onSubmit={(values) => {\n              handleLogin(values);\n            }}\n            validationSchema={LOGIN_SCHEMA}\n          >\n            <Flex direction=\"column\" alignItems=\"stretch\" gap={6}>\n              {[\n                {\n                  label: formatMessage({ id: 'Auth.form.email.label', defaultMessage: 'Email' }),\n                  name: 'email',\n                  placeholder: formatMessage({\n                    id: 'Auth.form.email.placeholder',\n                    defaultMessage: '<EMAIL>',\n                  }),\n                  required: true,\n                  type: 'string' as const,\n                },\n                {\n                  label: formatMessage({\n                    id: 'global.password',\n                    defaultMessage: 'Password',\n                  }),\n                  name: 'password',\n                  required: true,\n                  type: 'password' as const,\n                },\n                {\n                  label: formatMessage({\n                    id: 'Auth.form.rememberMe.label',\n                    defaultMessage: 'Remember me',\n                  }),\n                  name: 'rememberMe',\n                  type: 'checkbox' as const,\n                },\n              ].map((field) => (\n                <InputRenderer key={field.name} {...field} />\n              ))}\n              <Button fullWidth type=\"submit\">\n                {formatMessage({ id: 'Auth.form.button.login', defaultMessage: 'Login' })}\n              </Button>\n            </Flex>\n          </Form>\n          {children}\n        </LayoutContent>\n        <Flex justifyContent=\"center\">\n          <Box paddingTop={4}>\n            <Link isExternal={false} tag={NavLink} to=\"/auth/forgot-password\">\n              {formatMessage({\n                id: 'Auth.link.forgot-password',\n                defaultMessage: 'Forgot your password?',\n              })}\n            </Link>\n          </Box>\n        </Flex>\n      </Main>\n    </UnauthenticatedLayout>\n  );\n};\n\nexport { Login };\nexport type { LoginProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,IAAMA,eAAmBC,QAAM,EAAGC,MAAM;EACtCC,OACGC,QAAM,EACNC,SAAQ,EACRF,MAAM;IACLG,IAAIC,YAAiBJ,MAAMG;IAC3BE,gBAAgB;GAEjBC,EAAAA,SAASF,YAAiBE,QAAQ;EACrCC,UAAcN,QAAM,EAAGK,SAASF,YAAiBE,QAAQ,EAAEJ,SAAQ;EACnEM,YAAgBC,OAAI,EAAGP,SAAQ;AACjC,CAAA;AAEA,IAAMQ,QAAQ,CAAC,EAAEC,SAAQ,MAAc;AACrC,QAAM,CAACC,UAAUC,WAAY,IAASC,eAAQ;AAC9C,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,QAAQC,aAAY,IAAKC,YAAAA;AACjC,QAAMC,QAAcC,cAAQ,MAAM,IAAIC,gBAAgBJ,YAAe,GAAA;IAACA;EAAa,CAAA;AACnF,QAAMK,WAAWC,YAAAA;AAEjB,QAAM,EAAEC,MAAK,IAAKC,QAAQ,SAAS,CAACC,SAASA,IAAAA;AAE7C,QAAMC,cAAc,OAAOC,SAAAA;AACzBhB,gBAAYiB,MAAAA;AAEZ,UAAMC,MAAM,MAAMN,MAAMI,IAAAA;AAExB,QAAI,WAAWE,KAAK;AAClB,YAAMC,UAAUD,IAAIE,MAAMD,WAAW;AAErC,cAAIE,iBAAAA,SAAUF,OAAAA,EAASG,YAAW,MAAO,iBAAiB;AACxDZ,iBAAS,YAAA;AACT;MACF;AAEAV,kBAAYmB,OAAAA;WACP;AACL,YAAMI,aAAahB,MAAMiB,IAAI,YAAA;AAC7B,YAAMC,cAAcF,aAAaG,mBAAmBH,UAAc,IAAA;AAElEb,eAASe,WAAAA;IACX;EACF;AAEA,aACEE,wBAACC,uBAAAA;IACC,cAAAC,yBAACC,MAAAA;;YACCD,yBAACE,eAAAA;;gBACCF,yBAACG,QAAAA;;oBACCL,wBAACM,MAAAA,CAAAA,CAAAA;oBACDN,wBAACO,KAAAA;kBAAIC,YAAY;kBAAGC,eAAe;kBACjC,cAAAT,wBAACU,YAAAA;oBAAWC,SAAQ;oBAAQC,KAAI;8BAC7BrC,cAAc;sBACbZ,IAAI;sBACJE,gBAAgB;oBAClB,CAAA;;;oBAGJmC,wBAACO,KAAAA;kBAAIE,eAAe;kBAClB,cAAAT,wBAACU,YAAAA;oBAAWC,SAAQ;oBAAUE,WAAU;8BACrCtC,cAAc;sBACbZ,IAAI;sBACJE,gBAAgB;oBAClB,CAAA;;;gBAGHO,eACC4B,wBAACU,YAAAA;kBAAW/C,IAAG;kBAAoBmD,MAAK;kBAAQC,UAAU;kBAAIF,WAAU;kBACrEzC,UAAAA;gBAED,CAAA,IAAA;;;gBAEN4B,wBAACgB,MAAAA;cACCC,QAAO;cACPC,eAAe;gBACb1D,OAAO;gBACPO,UAAU;gBACVC,YAAY;cACd;cACAmD,UAAU,CAACC,WAAAA;AACThC,4BAAYgC,MAAAA;cACd;cACAC,kBAAkBhE;cAElB,cAAA6C,yBAACoB,MAAAA;gBAAKC,WAAU;gBAASC,YAAW;gBAAUC,KAAK;;kBAChD;oBACC;sBACEC,OAAOnD,cAAc;wBAAEZ,IAAI;wBAAyBE,gBAAgB;sBAAQ,CAAA;sBAC5E8D,MAAM;sBACNC,aAAarD,cAAc;wBACzBZ,IAAI;wBACJE,gBAAgB;sBAClB,CAAA;sBACAC,UAAU;sBACV+D,MAAM;oBACR;oBACA;sBACEH,OAAOnD,cAAc;wBACnBZ,IAAI;wBACJE,gBAAgB;sBAClB,CAAA;sBACA8D,MAAM;sBACN7D,UAAU;sBACV+D,MAAM;oBACR;oBACA;sBACEH,OAAOnD,cAAc;wBACnBZ,IAAI;wBACJE,gBAAgB;sBAClB,CAAA;sBACA8D,MAAM;sBACNE,MAAM;oBACR;kBACD,EAACC,IAAI,CAACC,cACL/B,wBAACgC,uBAAAA;oBAAgC,GAAGD;kBAAhBA,GAAAA,MAAMJ,IAAI,CAAA;sBAEhC3B,wBAACiC,QAAAA;oBAAOC,WAAS;oBAACL,MAAK;8BACpBtD,cAAc;sBAAEZ,IAAI;sBAA0BE,gBAAgB;oBAAQ,CAAA;;;;;YAI5EM;;;YAEH6B,wBAACsB,MAAAA;UAAKa,gBAAe;UACnB,cAAAnC,wBAACO,KAAAA;YAAIC,YAAY;YACf,cAAAR,wBAACoC,MAAAA;cAAKC,YAAY;cAAOzB,KAAK0B;cAASC,IAAG;wBACvChE,cAAc;gBACbZ,IAAI;gBACJE,gBAAgB;cAClB,CAAA;;;;;;;AAOd;", "names": ["LOGIN_SCHEMA", "object", "shape", "email", "string", "nullable", "id", "translatedErrors", "defaultMessage", "required", "password", "rememberMe", "bool", "<PERSON><PERSON>", "children", "apiError", "setApiError", "useState", "formatMessage", "useIntl", "search", "searchString", "useLocation", "query", "useMemo", "URLSearchParams", "navigate", "useNavigate", "login", "useAuth", "auth", "handleLogin", "body", "undefined", "res", "message", "error", "camelCase", "toLowerCase", "redirectTo", "get", "redirectUrl", "decodeURIComponent", "_jsx", "UnauthenticatedLayout", "_jsxs", "Main", "LayoutContent", "Column", "Logo", "Box", "paddingTop", "paddingBottom", "Typography", "variant", "tag", "textColor", "role", "tabIndex", "Form", "method", "initialValues", "onSubmit", "values", "validationSchema", "Flex", "direction", "alignItems", "gap", "label", "name", "placeholder", "type", "map", "field", "InputR<PERSON><PERSON>", "<PERSON><PERSON>", "fullWidth", "justifyContent", "Link", "isExternal", "NavLink", "to"]}