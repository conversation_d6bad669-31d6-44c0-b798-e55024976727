import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-type-builder/dist/admin/translations/fr.json.mjs
var from = "de";
var fr = {
  "attribute.boolean": "Booléen",
  "attribute.date": "Date",
  "attribute.email": "Email",
  "attribute.enumeration": "Énumération",
  "attribute.json": "JSON",
  "attribute.media": "Média",
  "attribute.password": "Mot de passe",
  "attribute.relation": "Relation",
  "attribute.richtext": "Texte enrichi",
  "attribute.text": "Texte",
  "button.attributes.add.another": "Ajouter un autre champ",
  "button.component.create": "Créer un composant",
  "button.model.create": "Créer un type de collection",
  "button.single-types.create": "Créer un single type",
  "contentType.kind.change.warning": "Vous venez de changer le type de ce modèle: L'API va redémarrer (Les routes, controllers, et les services seront écrasés).",
  "form.attribute.item.customColumnName": "Nom de colonne personalisée",
  "form.attribute.item.customColumnName.description": "Pratique pour renommer la colonne de la db dans un format plus comprehensible pour les responses de l'API",
  "form.attribute.item.defineRelation.fieldName": "Nom du Champ",
  "form.attribute.item.enumeration.graphql": "Surchage du nom pour GraphQL",
  "form.attribute.item.enumeration.graphql.description": "Vous permet de remplacer le nom généré par défaut pour GraphQL",
  "form.attribute.item.enumeration.placeholder": "Ex:\nmatin\nmidi\nsoir",
  "form.attribute.item.enumeration.rules": "Valeurs (les séparer par une nouvelle ligne)",
  "form.attribute.item.maximum": "Valeur maximum",
  "form.attribute.item.maximumComponents": "Composants maximum",
  "form.attribute.item.maximumComponents.description": "Nombre maximum de composants",
  "form.attribute.item.maximumLength": "Taille maximum",
  "form.attribute.item.minimum": "Valeur minimun",
  "form.attribute.item.minimumComponents": "Composants minimun",
  "form.attribute.item.minimumComponents.description": "Nombre minimum de composants",
  "form.attribute.item.minimumLength": "Taille minimun",
  "form.attribute.item.number.type": "Format nombre",
  "form.attribute.item.number.type.decimal": "décimal approximatif (ex: 2,22)",
  "form.attribute.item.number.type.float": "décimal (ex: 3,33333)",
  "form.attribute.item.number.type.integer": "entier (ex: 10)",
  "form.attribute.item.requiredField": "Champ obligatoire",
  "form.attribute.item.requiredField.description": "Vous ne pourrez pas créer une entrée si ce champ est vide",
  "form.attribute.item.uniqueField": "Champ unique",
  "form.attribute.item.uniqueField.description": "Vous ne pourrez pas créer une entrée s'il existe un champ similaire",
  "form.attribute.settings.default": "Valeur par défault",
  "form.button.add.field.to.collectionType": "Ajouter un nouveau champ à cette collection",
  "form.button.add.field.to.component": "Ajouter un nouveau champ à ce composant",
  "form.button.add.field.to.contentType": "Ajouter un nouveau champ à cette content type",
  "form.button.add.field.to.singleType": "Ajouter un nouveau champ à ce single type",
  "form.button.add-field": "Ajouter un autre champ",
  "form.button.cancel": "Annuler",
  "form.button.configure-view": "Configurer la vue",
  from,
  "modalForm.attribute.form.base.name.placeholder": "ex : slug, urlSeo, urlCanonique",
  "modalForm.attribute.target-field": "Champ associé",
  "modalForm.header.back": "Dos",
  "modalForm.singleType.header-create": "Créer un single type",
  "modalForm.sub-header.chooseAttribute.collectionType": "Selectionnez un champ pour votre collection",
  "modalForm.sub-header.chooseAttribute.component": "Selectionnez un champ pour votre composant",
  "modalForm.sub-header.chooseAttribute.singleType": "Selectionnez un champ pour votre single type",
  "modelPage.attribute.relationWith": "Relation avec",
  "plugin.description.long": "Modélisez la structure de données de votre API. Créer des nouveaux champs et relations en un instant. Les fichiers se créent et se mettent à jour automatiquement.",
  "plugin.description.short": "Modélisez la structure de données de votre API.",
  "plugin.name": "Content-Type Builder",
  "popUpForm.navContainer.advanced": "Réglages avancés",
  "popUpForm.navContainer.base": "Réglages de base",
  "popUpWarning.bodyMessage.contentType.delete": "Êtes-vous sûr de vouloir supprimer cette Collection ? Cela le supprimera aussi de vos types de contenu.",
  "popUpWarning.draft-publish.button.confirm": "Oui, désactiver",
  "popUpWarning.draft-publish.message": "Si vous désactivez le système Brouillon/Publier, vos brouillons seront supprimés.",
  "popUpWarning.draft-publish.second-message": "Êtes-vous sûr de vouloir le désactiver ?",
  "relation.attributeName.placeholder": "Ex : auteur, catégorie, tag",
  "relation.manyToMany": "a et appartient à plusieurs",
  "relation.manyToOne": "a plusieurs",
  "relation.manyWay": "a plusieurs",
  "relation.oneToMany": "appartient à plusieurs",
  "relation.oneToOne": "a et appartient à un",
  "relation.oneWay": "a un"
};
export {
  fr as default,
  from
};
//# sourceMappingURL=fr.json-YMBKIU5L.js.map
