{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/components/DragLayer.tsx", "../../../@strapi/content-manager/admin/src/components/DragPreviews/ComponentDragPreview.tsx", "../../../@strapi/content-manager/admin/src/components/DragPreviews/RelationDragPreview.tsx", "../../../@strapi/content-manager/admin/src/components/LeftMenu.tsx", "../../../@strapi/content-manager/admin/src/hooks/useContentManagerInitData.ts", "../../../@strapi/content-manager/admin/src/layout.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { Box } from '@strapi/design-system';\nimport { DragLayerMonitor, XYCoord, useDragLayer } from 'react-dnd';\n\nfunction getStyle(\n  initialOffset: XYCoord | null,\n  currentOffset: XYCoord | null,\n  mouseOffset: XYCoord | null\n) {\n  if (!initialOffset || !currentOffset || !mouseOffset) {\n    return { display: 'none' };\n  }\n\n  const { x, y } = mouseOffset;\n\n  return {\n    transform: `translate(${x}px, ${y}px)`,\n  };\n}\n\nexport interface DragLayerProps {\n  renderItem: (item: {\n    /**\n     * TODO: it'd be great if we could make this a union where the type infers the item.\n     */\n    item: any;\n    type: ReturnType<DragLayerMonitor['getItemType']>;\n  }) => React.ReactNode;\n}\n\nconst DragLayer = ({ renderItem }: DragLayerProps) => {\n  const { itemType, isDragging, item, initialOffset, currentOffset, mouseOffset } = useDragLayer(\n    (monitor) => ({\n      item: monitor.getItem(),\n      itemType: monitor.getItemType(),\n      initialOffset: monitor.getInitialSourceClientOffset(),\n      currentOffset: monitor.getSourceClientOffset(),\n      isDragging: monitor.isDragging(),\n      mouseOffset: monitor.getClientOffset(),\n    })\n  );\n\n  if (!isDragging) {\n    return null;\n  }\n\n  return (\n    <Box\n      height=\"100%\"\n      left={0}\n      position=\"fixed\"\n      pointerEvents=\"none\"\n      top={0}\n      zIndex={100}\n      width=\"100%\"\n    >\n      <Box style={getStyle(initialOffset, currentOffset, mouseOffset)}>\n        {renderItem({ type: itemType, item })}\n      </Box>\n    </Box>\n  );\n};\n\nexport { DragLayer };\n", "import { Flex, FlexComponent, IconButton, Typography } from '@strapi/design-system';\nimport { CaretDown, Drag, Trash } from '@strapi/icons';\nimport { styled } from 'styled-components';\n\ninterface ComponentDragPreviewProps {\n  displayedValue: string;\n}\n\nconst ComponentDragPreview = ({ displayedValue }: ComponentDragPreviewProps) => {\n  return (\n    <Flex\n      background=\"neutral0\"\n      borderColor=\"neutral200\"\n      justifyContent=\"space-between\"\n      gap={3}\n      padding={3}\n      width=\"30rem\"\n    >\n      <ToggleButton type=\"button\">\n        <Flex gap={6}>\n          <DropdownIconWrapper\n            alignItems=\"center\"\n            justifyContent=\"center\"\n            background=\"neutral200\"\n            height=\"3.2rem\"\n            width=\"3.2rem\"\n          >\n            <CaretDown />\n          </DropdownIconWrapper>\n\n          <Flex maxWidth=\"15rem\">\n            <Typography textColor=\"neutral700\" ellipsis>\n              {displayedValue}\n            </Typography>\n          </Flex>\n        </Flex>\n      </ToggleButton>\n\n      <Flex gap={2}>\n        <IconButton withTooltip={false} label=\"\" variant=\"ghost\">\n          <Trash />\n        </IconButton>\n\n        <IconButton withTooltip={false} label=\"\" variant=\"ghost\">\n          <Drag />\n        </IconButton>\n      </Flex>\n    </Flex>\n  );\n};\n\nconst DropdownIconWrapper = styled<FlexComponent>(Flex)`\n  border-radius: 50%;\n\n  svg {\n    height: 0.6rem;\n    width: 1.1rem;\n    > path {\n      fill: ${({ theme }) => theme.colors.neutral600};\n    }\n  }\n`;\n\n// TODO: we shouldn't have to reset a whole button\nconst ToggleButton = styled.button`\n  border: none;\n  background: transparent;\n  display: block;\n  width: 100%;\n  text-align: unset;\n  padding: 0;\n`;\n\nexport { ComponentDragPreview };\nexport type { ComponentDragPreviewProps };\n", "import { Box, Flex, IconButton, Typography } from '@strapi/design-system';\nimport { Cross, Drag } from '@strapi/icons';\n\nimport { DocumentStatus } from '../../pages/EditView/components/DocumentStatus';\nimport {\n  Disconnect<PERSON>utton,\n  LinkEllipsis,\n  FlexWrapper,\n} from '../../pages/EditView/components/FormInputs/Relations/Relations';\n\nimport type { Data } from '@strapi/types';\n\ninterface RelationDragPreviewProps {\n  status?: string;\n  displayedValue: string;\n  id: Data.ID;\n  index: number;\n  width: number;\n}\n\nconst RelationDragPreview = ({ status, displayedValue, width }: RelationDragPreviewProps) => {\n  return (\n    <Box style={{ width }}>\n      <Flex\n        paddingTop={2}\n        paddingBottom={2}\n        paddingLeft={2}\n        paddingRight={4}\n        hasRadius\n        borderWidth={1}\n        background=\"neutral0\"\n        borderColor=\"neutral200\"\n        justifyContent=\"space-between\"\n        gap={4}\n      >\n        <FlexWrapper gap={1}>\n          <IconButton withTooltip={false} label=\"\" variant=\"ghost\">\n            <Drag />\n          </IconButton>\n          <Flex width=\"100%\" minWidth={0} justifyContent=\"space-between\">\n            <Box minWidth={0} paddingTop={1} paddingBottom={1} paddingRight={4}>\n              <LinkEllipsis href=\"\">\n                <Typography textColor=\"primary600\" ellipsis>\n                  {displayedValue}\n                </Typography>\n              </LinkEllipsis>\n            </Box>\n            {status ? <DocumentStatus status={status} /> : null}\n          </Flex>\n        </FlexWrapper>\n        <DisconnectButton type=\"button\">\n          <Cross width=\"12px\" />\n        </DisconnectButton>\n      </Flex>\n    </Box>\n  );\n};\n\nexport { RelationDragPreview };\nexport type { RelationDragPreviewProps };\n", "import * as React from 'react';\n\nimport { useQueryParams, SubNav } from '@strapi/admin/strapi-admin';\nimport { Divider, Flex, TextInput, useCollator, useFilter } from '@strapi/design-system';\nimport { Cross, Search } from '@strapi/icons';\nimport { parse, stringify } from 'qs';\nimport { useIntl } from 'react-intl';\n\nimport { useContentTypeSchema } from '../hooks/useContentTypeSchema';\nimport { useTypedSelector } from '../modules/hooks';\nimport { getTranslation } from '../utils/translations';\n\nimport type { ContentManagerLink } from '../hooks/useContentManagerInitData';\n\nconst LeftMenu = () => {\n  const [search, setSearch] = React.useState('');\n  const [{ query }] = useQueryParams<{ plugins?: object }>();\n  const { formatMessage, locale } = useIntl();\n\n  const collectionTypeLinks = useTypedSelector(\n    (state) => state['content-manager'].app.collectionTypeLinks\n  );\n\n  const singleTypeLinks = useTypedSelector((state) => state['content-manager'].app.singleTypeLinks);\n  const { schemas } = useContentTypeSchema();\n\n  const { startsWith } = useFilter(locale, {\n    sensitivity: 'base',\n  });\n\n  const formatter = useCollator(locale, {\n    sensitivity: 'base',\n  });\n\n  const menu = React.useMemo(\n    () =>\n      [\n        {\n          id: 'collectionTypes',\n          title: formatMessage({\n            id: getTranslation('components.LeftMenu.collection-types'),\n            defaultMessage: 'Collection Types',\n          }),\n          searchable: true,\n          links: collectionTypeLinks,\n        },\n        {\n          id: 'singleTypes',\n          title: formatMessage({\n            id: getTranslation('components.LeftMenu.single-types'),\n            defaultMessage: 'Single Types',\n          }),\n          searchable: true,\n          links: singleTypeLinks,\n        },\n      ].map((section) => ({\n        ...section,\n        links: section.links\n          /**\n           * Filter by the search value\n           */\n          .filter((link) => startsWith(link.title, search))\n          /**\n           * Sort correctly using the language\n           */\n          .sort((a, b) => formatter.compare(a.title, b.title))\n          /**\n           * Apply the formated strings to the links from react-intl\n           */\n          .map((link) => {\n            return {\n              ...link,\n              title: formatMessage({ id: link.title, defaultMessage: link.title }),\n            };\n          }),\n      })),\n    [collectionTypeLinks, search, singleTypeLinks, startsWith, formatMessage, formatter]\n  );\n\n  const handleClear = () => {\n    setSearch('');\n  };\n\n  const handleChangeSearch = ({ target: { value } }: { target: { value: string } }) => {\n    setSearch(value);\n  };\n\n  const label = formatMessage({\n    id: getTranslation('header.name'),\n    defaultMessage: 'Content Manager',\n  });\n\n  const getPluginsParamsForLink = (link: ContentManagerLink) => {\n    const schema = schemas.find((schema) => schema.uid === link.uid);\n    const isI18nEnabled = Boolean((schema?.pluginOptions?.i18n as any)?.localized);\n\n    // The search params have the i18n plugin\n    if (query.plugins && 'i18n' in query.plugins) {\n      // Prepare removal of i18n from the plugins search params\n      const { i18n, ...restPlugins } = query.plugins;\n\n      // i18n is not enabled, remove it from the plugins search params\n      if (!isI18nEnabled) {\n        return restPlugins;\n      }\n\n      // i18n is enabled, put the plugins search params back together\n      return { i18n, ...restPlugins };\n    }\n\n    return query.plugins;\n  };\n\n  return (\n    <SubNav.Main aria-label={label}>\n      <SubNav.Header label={label} />\n      <Divider background=\"neutral150\" />\n      <Flex padding={5} gap={3} direction={'column'} alignItems={'stretch'}>\n        <TextInput\n          startAction={<Search fill=\"neutral500\" />}\n          value={search}\n          onChange={handleChangeSearch}\n          aria-label=\"Search\"\n          placeholder={formatMessage({\n            id: 'content-manager.components.LeftMenu.Search.label',\n            defaultMessage: 'Search for a content type',\n          })}\n          endAction={<Cross onClick={handleClear} fill=\"neutral500\" cursor=\"pointer\" />}\n          size=\"S\"\n        />\n      </Flex>\n      <SubNav.Sections>\n        {menu.map((section) => {\n          return (\n            <SubNav.Section key={section.id} label={section.title}>\n              {section.links.map((link) => {\n                return (\n                  <SubNav.Link\n                    key={link.uid}\n                    to={{\n                      pathname: link.to,\n                      search: stringify({\n                        ...parse(link.search ?? ''),\n                        plugins: getPluginsParamsForLink(link),\n                      }),\n                    }}\n                    label={link.title}\n                  />\n                );\n              })}\n            </SubNav.Section>\n          );\n        })}\n      </SubNav.Sections>\n    </SubNav.Main>\n  );\n};\n\nexport { LeftMenu };\n", "import { useEffect } from 'react';\n\nimport {\n  useAuth,\n  type Permission,\n  useNotification,\n  useS<PERSON><PERSON><PERSON><PERSON>,\n  use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n} from '@strapi/admin/strapi-admin';\nimport { useNotifyAT } from '@strapi/design-system';\nimport { stringify } from 'qs';\nimport { useIntl } from 'react-intl';\n\nimport { COLLECTION_TYPES, SINGLE_TYPES } from '../constants/collections';\nimport { HOOKS } from '../constants/hooks';\nimport { AppState, setInitialData } from '../modules/app';\nimport { useTypedDispatch, useTypedSelector } from '../modules/hooks';\nimport { useGetAllContentTypeSettingsQuery } from '../services/contentTypes';\nimport { useGetInitialDataQuery } from '../services/init';\nimport { getTranslation } from '../utils/translations';\n\nimport type { Component } from '../../../shared/contracts/components';\nimport type {\n  ContentType,\n  FindContentTypesSettings,\n} from '../../../shared/contracts/content-types';\nimport type { GetInitData } from '../../../shared/contracts/init';\n\nconst { MUTATE_COLLECTION_TYPES_LINKS, MUTATE_SINGLE_TYPES_LINKS } = HOOKS;\n\ninterface ContentManagerLink {\n  permissions: Permission[];\n  search: string | null;\n  kind: string;\n  title: string;\n  to: string;\n  uid: string;\n  name: string;\n  isDisplayed: boolean;\n}\n\nconst useContentManagerInitData = (): AppState => {\n  const { toggleNotification } = useNotification();\n  const dispatch = useTypedDispatch();\n  const runHookWaterfall = useStrapiApp(\n    'useContentManagerInitData',\n    (state) => state.runHookWaterfall\n  );\n  const { notifyStatus } = useNotifyAT();\n  const { formatMessage } = useIntl();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler(getTranslation);\n  const checkUserHasPermissions = useAuth(\n    'useContentManagerInitData',\n    (state) => state.checkUserHasPermissions\n  );\n\n  const state = useTypedSelector((state) => state['content-manager'].app);\n\n  const initialDataQuery = useGetInitialDataQuery(undefined, {\n    /**\n     * TODO: remove this when the CTB has been refactored to use redux-toolkit-query\n     * and it can invalidate the cache on mutation\n     */\n    refetchOnMountOrArgChange: true,\n  });\n\n  useEffect(() => {\n    if (initialDataQuery.data) {\n      notifyStatus(\n        formatMessage({\n          id: getTranslation('App.schemas.data-loaded'),\n          defaultMessage: 'The schemas have been successfully loaded.',\n        })\n      );\n    }\n  }, [formatMessage, initialDataQuery.data, notifyStatus]);\n\n  useEffect(() => {\n    if (initialDataQuery.error) {\n      toggleNotification({ type: 'danger', message: formatAPIError(initialDataQuery.error) });\n    }\n  }, [formatAPIError, initialDataQuery.error, toggleNotification]);\n\n  const contentTypeSettingsQuery = useGetAllContentTypeSettingsQuery();\n\n  useEffect(() => {\n    if (contentTypeSettingsQuery.error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(contentTypeSettingsQuery.error),\n      });\n    }\n  }, [formatAPIError, contentTypeSettingsQuery.error, toggleNotification]);\n\n  const formatData = async (\n    components: Component[],\n    contentTypes: ContentType[],\n    fieldSizes: GetInitData.Response['data']['fieldSizes'],\n    contentTypeConfigurations: FindContentTypesSettings.Response['data']\n  ) => {\n    /**\n     * We group these by the two types we support. We do with an object because we can use default\n     * values of arrays to make sure we always have an array to manipulate further on if, for example,\n     * a user has not made any single types.\n     *\n     * This means we have to manually add new content types to this hook if we add a new type – but\n     * the safety is worth it.\n     */\n    const { collectionType: collectionTypeLinks, singleType: singleTypeLinks } =\n      contentTypes.reduce<{\n        collectionType: ContentType[];\n        singleType: ContentType[];\n      }>(\n        (acc, model) => {\n          acc[model.kind].push(model);\n          return acc;\n        },\n        {\n          collectionType: [],\n          singleType: [],\n        }\n      );\n    const collectionTypeSectionLinks = generateLinks(\n      collectionTypeLinks,\n      'collectionTypes',\n      contentTypeConfigurations\n    );\n    const singleTypeSectionLinks = generateLinks(singleTypeLinks, 'singleTypes');\n\n    // Collection Types verifications\n    const collectionTypeLinksPermissions = await Promise.all(\n      collectionTypeSectionLinks.map(({ permissions }) => checkUserHasPermissions(permissions))\n    );\n\n    const authorizedCollectionTypeLinks = collectionTypeSectionLinks.filter(\n      (_, index) => collectionTypeLinksPermissions[index].length > 0\n    );\n\n    // Single Types verifications\n    const singleTypeLinksPermissions = await Promise.all(\n      singleTypeSectionLinks.map(({ permissions }) => checkUserHasPermissions(permissions))\n    );\n    const authorizedSingleTypeLinks = singleTypeSectionLinks.filter(\n      (_, index) => singleTypeLinksPermissions[index].length > 0\n    );\n    const { ctLinks } = runHookWaterfall(MUTATE_COLLECTION_TYPES_LINKS, {\n      ctLinks: authorizedCollectionTypeLinks,\n      models: contentTypes,\n    });\n    const { stLinks } = runHookWaterfall(MUTATE_SINGLE_TYPES_LINKS, {\n      stLinks: authorizedSingleTypeLinks,\n      models: contentTypes,\n    });\n\n    dispatch(\n      setInitialData({\n        authorizedCollectionTypeLinks: ctLinks,\n        authorizedSingleTypeLinks: stLinks,\n        components,\n        contentTypeSchemas: contentTypes,\n        fieldSizes,\n      })\n    );\n  };\n\n  useEffect(() => {\n    if (initialDataQuery.data && contentTypeSettingsQuery.data) {\n      formatData(\n        initialDataQuery.data.components,\n        initialDataQuery.data.contentTypes,\n        initialDataQuery.data.fieldSizes,\n        contentTypeSettingsQuery.data\n      );\n    }\n  }, [initialDataQuery.data, contentTypeSettingsQuery.data]);\n\n  return { ...state };\n};\n\nconst generateLinks = (\n  links: ContentType[],\n  type: 'collectionTypes' | 'singleTypes',\n  configurations: FindContentTypesSettings.Response['data'] = []\n) => {\n  return links\n    .filter((link) => link.isDisplayed)\n    .map((link) => {\n      const collectionTypesPermissions = [\n        { action: 'plugin::content-manager.explorer.create', subject: link.uid },\n        { action: 'plugin::content-manager.explorer.read', subject: link.uid },\n      ];\n      const singleTypesPermissions = [\n        { action: 'plugin::content-manager.explorer.read', subject: link.uid },\n      ];\n      const permissions =\n        type === 'collectionTypes' ? collectionTypesPermissions : singleTypesPermissions;\n\n      const currentContentTypeConfig = configurations.find(({ uid }) => uid === link.uid);\n\n      let search = null;\n\n      if (currentContentTypeConfig) {\n        const searchParams = {\n          page: 1,\n          pageSize: currentContentTypeConfig.settings.pageSize,\n          sort: `${currentContentTypeConfig.settings.defaultSortBy}:${currentContentTypeConfig.settings.defaultSortOrder}`,\n        };\n\n        search = stringify(searchParams, { encode: false });\n      }\n\n      return {\n        permissions,\n        search,\n        kind: link.kind,\n        title: link.info.displayName,\n        to: `/content-manager/${link.kind === 'collectionType' ? COLLECTION_TYPES : SINGLE_TYPES}/${\n          link.uid\n        }`,\n        uid: link.uid,\n        // Used for the list item key in the helper plugin\n        name: link.uid,\n        isDisplayed: link.isDisplayed,\n      } satisfies ContentManagerLink;\n    });\n};\n\nexport { useContentManagerInitData };\nexport type { ContentManagerLink };\n", "/* eslint-disable check-file/filename-naming-convention */\nimport * as React from 'react';\n\nimport { Page, useGuidedTour, Layouts } from '@strapi/admin/strapi-admin';\nimport { useIntl } from 'react-intl';\nimport { Navigate, Outlet, useLocation, useMatch } from 'react-router-dom';\n\nimport { DragLayer, DragLayerProps } from './components/DragLayer';\nimport { CardDragPreview } from './components/DragPreviews/CardDragPreview';\nimport { ComponentDragPreview } from './components/DragPreviews/ComponentDragPreview';\nimport { RelationDragPreview } from './components/DragPreviews/RelationDragPreview';\nimport { LeftMenu } from './components/LeftMenu';\nimport { ItemTypes } from './constants/dragAndDrop';\nimport { useContentManagerInitData } from './hooks/useContentManagerInitData';\nimport { getTranslation } from './utils/translations';\n\n/* -------------------------------------------------------------------------------------------------\n * Layout\n * -----------------------------------------------------------------------------------------------*/\n\nconst Layout = () => {\n  const contentTypeMatch = useMatch('/content-manager/:kind/:uid/*');\n\n  const { isLoading, collectionTypeLinks, models, singleTypeLinks } = useContentManagerInitData();\n  const authorisedModels = [...collectionTypeLinks, ...singleTypeLinks].sort((a, b) =>\n    a.title.localeCompare(b.title)\n  );\n\n  const { pathname } = useLocation();\n  const { formatMessage } = useIntl();\n  const startSection = useGuidedTour('Layout', (state) => state.startSection);\n  const startSectionRef = React.useRef(startSection);\n\n  React.useEffect(() => {\n    if (startSectionRef.current) {\n      startSectionRef.current('contentManager');\n    }\n  }, []);\n\n  if (isLoading) {\n    return (\n      <>\n        <Page.Title>\n          {formatMessage({\n            id: getTranslation('plugin.name'),\n            defaultMessage: 'Content Manager',\n          })}\n        </Page.Title>\n        <Page.Loading />\n      </>\n    );\n  }\n\n  // Array of models that are displayed in the content manager\n  const supportedModelsToDisplay = models.filter(({ isDisplayed }) => isDisplayed);\n\n  // Redirect the user to the 403 page\n  if (\n    authorisedModels.length === 0 &&\n    supportedModelsToDisplay.length > 0 &&\n    pathname !== '/content-manager/403'\n  ) {\n    return <Navigate to=\"/403\" />;\n  }\n\n  // Redirect the user to the create content type page\n  if (supportedModelsToDisplay.length === 0 && pathname !== '/no-content-types') {\n    return <Navigate to=\"/no-content-types\" />;\n  }\n\n  if (!contentTypeMatch && authorisedModels.length > 0) {\n    return (\n      <Navigate\n        to={{\n          pathname: authorisedModels[0].to,\n          search: authorisedModels[0].search ?? '',\n        }}\n        replace\n      />\n    );\n  }\n\n  return (\n    <>\n      <Page.Title>\n        {formatMessage({\n          id: getTranslation('plugin.name'),\n          defaultMessage: 'Content Manager',\n        })}\n      </Page.Title>\n      <Layouts.Root sideNav={<LeftMenu />}>\n        <DragLayer renderItem={renderDraglayerItem} />\n        <Outlet />\n      </Layouts.Root>\n    </>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * renderDraglayerItem\n * -----------------------------------------------------------------------------------------------*/\n\nfunction renderDraglayerItem({ type, item }: Parameters<DragLayerProps['renderItem']>[0]) {\n  if (!type || (type && typeof type !== 'string')) {\n    return null;\n  }\n\n  /**\n   * Because a user may have multiple relations / dynamic zones / repeable fields in the same content type,\n   * we append the fieldName for the item type to make them unique, however, we then want to extract that\n   * first type to apply the correct preview.\n   */\n  const [actualType] = type.split('_');\n\n  switch (actualType) {\n    case ItemTypes.EDIT_FIELD:\n    case ItemTypes.FIELD:\n      return <CardDragPreview label={item.label} />;\n    case ItemTypes.COMPONENT:\n    case ItemTypes.DYNAMIC_ZONE:\n      return <ComponentDragPreview displayedValue={item.displayedValue} />;\n\n    case ItemTypes.RELATION:\n      return <RelationDragPreview {...item} />;\n\n    default:\n      return null;\n  }\n}\n\nexport { Layout };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,SAASA,SACPC,eACAC,eACAC,aAA2B;AAE3B,MAAI,CAACF,iBAAiB,CAACC,iBAAiB,CAACC,aAAa;AACpD,WAAO;MAAEC,SAAS;IAAO;EAC3B;AAEA,QAAM,EAAEC,GAAGC,EAAC,IAAKH;AAEjB,SAAO;IACLI,WAAW,aAAaF,CAAAA,OAAQC,CAAAA;EAClC;AACF;AAYA,IAAME,YAAY,CAAC,EAAEC,WAAU,MAAkB;AAC/C,QAAM,EAAEC,UAAUC,YAAYC,MAAMX,eAAeC,eAAeC,YAAW,IAAKU,aAChF,CAACC,aAAa;IACZF,MAAME,QAAQC,QAAO;IACrBL,UAAUI,QAAQE,YAAW;IAC7Bf,eAAea,QAAQG,6BAA4B;IACnDf,eAAeY,QAAQI,sBAAqB;IAC5CP,YAAYG,QAAQH,WAAU;IAC9BR,aAAaW,QAAQK,gBAAe;IACtC;AAGF,MAAI,CAACR,YAAY;AACf,WAAO;EACT;AAEA,aACES,wBAACC,KAAAA;IACCC,QAAO;IACPC,MAAM;IACNC,UAAS;IACTC,eAAc;IACdC,KAAK;IACLC,QAAQ;IACRC,OAAM;IAEN,cAAAR,wBAACC,KAAAA;MAAIQ,OAAO7B,SAASC,eAAeC,eAAeC,WAAAA;gBAChDM,WAAW;QAAEqB,MAAMpB;QAAUE;MAAK,CAAA;;;AAI3C;;;;ACtDA,IAAMmB,uBAAuB,CAAC,EAAEC,eAAc,MAA6B;AACzE,aACEC,0BAACC,MAAAA;IACCC,YAAW;IACXC,aAAY;IACZC,gBAAe;IACfC,KAAK;IACLC,SAAS;IACTC,OAAM;;UAENC,yBAACC,cAAAA;QAAaC,MAAK;QACjB,cAAAV,0BAACC,MAAAA;UAAKI,KAAK;;gBACTG,yBAACG,qBAAAA;cACCC,YAAW;cACXR,gBAAe;cACfF,YAAW;cACXW,QAAO;cACPN,OAAM;cAEN,cAAAC,yBAACM,eAAAA,CAAAA,CAAAA;;gBAGHN,yBAACP,MAAAA;cAAKc,UAAS;cACb,cAAAP,yBAACQ,YAAAA;gBAAWC,WAAU;gBAAaC,UAAQ;gBACxCnB,UAAAA;;;;;;UAMTC,0BAACC,MAAAA;QAAKI,KAAK;;cACTG,yBAACW,YAAAA;YAAWC,aAAa;YAAOC,OAAM;YAAGC,SAAQ;YAC/C,cAAAd,yBAACe,cAAAA,CAAAA,CAAAA;;cAGHf,yBAACW,YAAAA;YAAWC,aAAa;YAAOC,OAAM;YAAGC,SAAQ;YAC/C,cAAAd,yBAACgB,eAAAA,CAAAA,CAAAA;;;;;;AAKX;AAEA,IAAMb,sBAAsBc,GAAsBxB,IAAAA;;;;;;;cAOpC,CAAC,EAAEyB,MAAK,MAAOA,MAAMC,OAAOC,UAAU;;;;AAMpD,IAAMnB,eAAegB,GAAOI;;;;;;;;;;;AC5CtBC,IAAAA,sBAAsB,CAAC,EAAEC,QAAQC,gBAAgBC,MAAK,MAA4B;AACtF,aACEC,yBAACC,KAAAA;IAAIC,OAAO;MAAEH;IAAM;IAClB,cAAAI,0BAACC,MAAAA;MACCC,YAAY;MACZC,eAAe;MACfC,aAAa;MACbC,cAAc;MACdC,WAAS;MACTC,aAAa;MACbC,YAAW;MACXC,aAAY;MACZC,gBAAe;MACfC,KAAK;;YAELX,0BAACY,aAAAA;UAAYD,KAAK;;gBAChBd,yBAACgB,YAAAA;cAAWC,aAAa;cAAOC,OAAM;cAAGC,SAAQ;cAC/C,cAAAnB,yBAACoB,eAAAA,CAAAA,CAAAA;;gBAEHjB,0BAACC,MAAAA;cAAKL,OAAM;cAAOsB,UAAU;cAAGR,gBAAe;;oBAC7Cb,yBAACC,KAAAA;kBAAIoB,UAAU;kBAAGhB,YAAY;kBAAGC,eAAe;kBAAGE,cAAc;kBAC/D,cAAAR,yBAACsB,cAAAA;oBAAaC,MAAK;oBACjB,cAAAvB,yBAACwB,YAAAA;sBAAWC,WAAU;sBAAaC,UAAQ;sBACxC5B,UAAAA;;;;gBAIND,aAASG,yBAAC2B,gBAAAA;kBAAe9B;gBAAqB,CAAA,IAAA;;;;;YAGnDG,yBAAC4B,kBAAAA;UAAiBC,MAAK;UACrB,cAAA7B,yBAAC8B,eAAAA;YAAM/B,OAAM;;;;;;AAKvB;;;;;;AC1CA,IAAMgC,WAAW,MAAA;AACf,QAAM,CAACC,QAAQC,SAAAA,IAAmBC,eAAS,EAAA;AAC3C,QAAM,CAAC,EAAEC,MAAK,CAAE,IAAIC,eAAAA;AACpB,QAAM,EAAEC,eAAeC,OAAM,IAAKC,QAAAA;AAElC,QAAMC,sBAAsBC,iBAC1B,CAACC,UAAUA,MAAM,iBAAkB,EAACC,IAAIH,mBAAmB;AAG7D,QAAMI,kBAAkBH,iBAAiB,CAACC,UAAUA,MAAM,iBAAkB,EAACC,IAAIC,eAAe;AAChG,QAAM,EAAEC,QAAO,IAAKC,qBAAAA;AAEpB,QAAM,EAAEC,WAAU,IAAKC,UAAUV,QAAQ;IACvCW,aAAa;EACf,CAAA;AAEA,QAAMC,YAAYC,YAAYb,QAAQ;IACpCW,aAAa;EACf,CAAA;AAEA,QAAMG,OAAaC,cACjB,MACE;IACE;MACEC,IAAI;MACJC,OAAOlB,cAAc;QACnBiB,IAAIE,eAAe,sCAAA;QACnBC,gBAAgB;MAClB,CAAA;MACAC,YAAY;MACZC,OAAOnB;IACT;IACA;MACEc,IAAI;MACJC,OAAOlB,cAAc;QACnBiB,IAAIE,eAAe,kCAAA;QACnBC,gBAAgB;MAClB,CAAA;MACAC,YAAY;MACZC,OAAOf;IACT;EACD,EAACgB,IAAI,CAACC,aAAa;IAClB,GAAGA;IACHF,OAAOE,QAAQF,MAIZG,OAAO,CAACC,SAAShB,WAAWgB,KAAKR,OAAOvB,MACzC,CAAA,EAGCgC,KAAK,CAACC,GAAGC,MAAMhB,UAAUiB,QAAQF,EAAEV,OAAOW,EAAEX,KAAK,CAClD,EAGCK,IAAI,CAACG,SAAAA;AACJ,aAAO;QACL,GAAGA;QACHR,OAAOlB,cAAc;UAAEiB,IAAIS,KAAKR;UAAOE,gBAAgBM,KAAKR;QAAM,CAAA;MACpE;IACF,CAAA;EACJ,EACF,GAAA;IAACf;IAAqBR;IAAQY;IAAiBG;IAAYV;IAAea;EAAU,CAAA;AAGtF,QAAMkB,cAAc,MAAA;AAClBnC,cAAU,EAAA;EACZ;AAEA,QAAMoC,qBAAqB,CAAC,EAAEC,QAAQ,EAAEC,MAAK,EAAE,MAAiC;AAC9EtC,cAAUsC,KAAAA;EACZ;AAEA,QAAMC,QAAQnC,cAAc;IAC1BiB,IAAIE,eAAe,aAAA;IACnBC,gBAAgB;EAClB,CAAA;AAEA,QAAMgB,0BAA0B,CAACV,SAAAA;;AAC/B,UAAMW,SAAS7B,QAAQ8B,KAAK,CAACD,YAAWA,QAAOE,QAAQb,KAAKa,GAAG;AAC/D,UAAMC,gBAAgBC,SAASJ,4CAAQK,kBAARL,mBAAuBM,SAAvBN,mBAAqCO,SAAAA;AAGpE,QAAI9C,MAAM+C,WAAW,UAAU/C,MAAM+C,SAAS;AAE5C,YAAM,EAAEF,MAAM,GAAGG,YAAa,IAAGhD,MAAM+C;AAGvC,UAAI,CAACL,eAAe;AAClB,eAAOM;MACT;AAGA,aAAO;QAAEH;QAAM,GAAGG;MAAY;IAChC;AAEA,WAAOhD,MAAM+C;EACf;AAEA,aACEE,0BAACC,OAAOC,MAAI;IAACC,cAAYf;;UACvBgB,yBAACH,OAAOI,QAAM;QAACjB;;UACfgB,yBAACE,SAAAA;QAAQC,YAAW;;UACpBH,yBAACI,MAAAA;QAAKC,SAAS;QAAGC,KAAK;QAAGC,WAAW;QAAUC,YAAY;QACzD,cAAAR,yBAACS,WAAAA;UACCC,iBAAaV,yBAACW,cAAAA;YAAOC,MAAK;;UAC1B7B,OAAOvC;UACPqE,UAAUhC;UACVkB,cAAW;UACXe,aAAajE,cAAc;YACzBiB,IAAI;YACJG,gBAAgB;UAClB,CAAA;UACA8C,eAAWf,yBAACgB,eAAAA;YAAMC,SAASrC;YAAagC,MAAK;YAAaM,QAAO;;UACjEC,MAAK;;;UAGTnB,yBAACH,OAAOuB,UAAQ;kBACbxD,KAAKQ,IAAI,CAACC,YAAAA;AACT,qBACE2B,yBAACH,OAAOwB,SAAO;YAAkBrC,OAAOX,QAAQN;YAC7CM,UAAAA,QAAQF,MAAMC,IAAI,CAACG,SAAAA;AAClB,yBACEyB,yBAACH,OAAOyB,MAAI;gBAEVC,IAAI;kBACFC,UAAUjD,KAAKgD;kBACf/E,YAAQiF,qBAAU;oBAChB,OAAGC,iBAAMnD,KAAK/B,UAAU,EAAG;oBAC3BkD,SAAST,wBAAwBV,IAAAA;kBACnC,CAAA;gBACF;gBACAS,OAAOT,KAAKR;cARPQ,GAAAA,KAAKa,GAAG;YAWnB,CAAA;UAfmBf,GAAAA,QAAQP,EAAE;QAkBnC,CAAA;;;;AAIR;;;;;AChIA,IAAM,EAAE6D,+BAA+BC,0BAAyB,IAAKC;AAarE,IAAMC,4BAA4B,MAAA;AAChC,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAMC,WAAWC,iBAAAA;AACjB,QAAMC,mBAAmBC,aACvB,6BACA,CAACC,WAAUA,OAAMF,gBAAgB;AAEnC,QAAM,EAAEG,aAAY,IAAKC,YAAAA;AACzB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,yBAAyBC,eAAc,IAAKC,mBAAmBC,cAAAA;AACvE,QAAMC,0BAA0BC,QAC9B,6BACA,CAACV,WAAUA,OAAMS,uBAAuB;AAG1C,QAAMT,QAAQW,iBAAiB,CAACX,WAAUA,OAAM,iBAAA,EAAmBY,GAAG;AAEtE,QAAMC,mBAAmBC,uBAAuBC,QAAW;;;;;IAKzDC,2BAA2B;EAC7B,CAAA;AAEAC,+BAAU,MAAA;AACR,QAAIJ,iBAAiBK,MAAM;AACzBjB,mBACEE,cAAc;QACZgB,IAAIX,eAAe,yBAAA;QACnBY,gBAAgB;MAClB,CAAA,CAAA;IAEJ;KACC;IAACjB;IAAeU,iBAAiBK;IAAMjB;EAAa,CAAA;AAEvDgB,+BAAU,MAAA;AACR,QAAIJ,iBAAiBQ,OAAO;AAC1B3B,yBAAmB;QAAE4B,MAAM;QAAUC,SAASjB,eAAeO,iBAAiBQ,KAAK;MAAE,CAAA;IACvF;KACC;IAACf;IAAgBO,iBAAiBQ;IAAO3B;EAAmB,CAAA;AAE/D,QAAM8B,2BAA2BC,kCAAAA;AAEjCR,+BAAU,MAAA;AACR,QAAIO,yBAAyBH,OAAO;AAClC3B,yBAAmB;QACjB4B,MAAM;QACNC,SAASjB,eAAekB,yBAAyBH,KAAK;MACxD,CAAA;IACF;KACC;IAACf;IAAgBkB,yBAAyBH;IAAO3B;EAAmB,CAAA;AAEvE,QAAMgC,aAAa,OACjBC,YACAC,cACAC,YACAC,8BAAAA;AAUA,UAAM,EAAEC,gBAAgBC,qBAAqBC,YAAYC,gBAAe,IACtEN,aAAaO,OAIX,CAACC,KAAKC,UAAAA;AACJD,UAAIC,MAAMC,IAAI,EAAEC,KAAKF,KAAAA;AACrB,aAAOD;OAET;MACEL,gBAAgB,CAAA;MAChBE,YAAY,CAAA;IACd,CAAA;AAEJ,UAAMO,6BAA6BC,cACjCT,qBACA,mBACAF,yBAAAA;AAEF,UAAMY,yBAAyBD,cAAcP,iBAAiB,aAAA;AAG9D,UAAMS,iCAAiC,MAAMC,QAAQC,IACnDL,2BAA2BM,IAAI,CAAC,EAAEC,YAAW,MAAOtC,wBAAwBsC,WAAAA,CAAAA,CAAAA;AAG9E,UAAMC,gCAAgCR,2BAA2BS,OAC/D,CAACC,GAAGC,UAAUR,+BAA+BQ,KAAAA,EAAOC,SAAS,CAAA;AAI/D,UAAMC,6BAA6B,MAAMT,QAAQC,IAC/CH,uBAAuBI,IAAI,CAAC,EAAEC,YAAW,MAAOtC,wBAAwBsC,WAAAA,CAAAA,CAAAA;AAE1E,UAAMO,4BAA4BZ,uBAAuBO,OACvD,CAACC,GAAGC,UAAUE,2BAA2BF,KAAAA,EAAOC,SAAS,CAAA;AAE3D,UAAM,EAAEG,QAAO,IAAKzD,iBAAiBR,+BAA+B;MAClEiE,SAASP;MACTQ,QAAQ5B;IACV,CAAA;AACA,UAAM,EAAE6B,QAAO,IAAK3D,iBAAiBP,2BAA2B;MAC9DkE,SAASH;MACTE,QAAQ5B;IACV,CAAA;AAEAhC,aACE8D,eAAe;MACbV,+BAA+BO;MAC/BD,2BAA2BG;MAC3B9B;MACAgC,oBAAoB/B;MACpBC;IACF,CAAA,CAAA;EAEJ;AAEAZ,+BAAU,MAAA;AACR,QAAIJ,iBAAiBK,QAAQM,yBAAyBN,MAAM;AAC1DQ,iBACEb,iBAAiBK,KAAKS,YACtBd,iBAAiBK,KAAKU,cACtBf,iBAAiBK,KAAKW,YACtBL,yBAAyBN,IAAI;IAEjC;KACC;IAACL,iBAAiBK;IAAMM,yBAAyBN;EAAK,CAAA;AAEzD,SAAO;IAAE,GAAGlB;EAAM;AACpB;AAEA,IAAMyC,gBAAgB,CACpBmB,OACAtC,MACAuC,iBAA4D,CAAA,MAAE;AAE9D,SAAOD,MACJX,OAAO,CAACa,SAASA,KAAKC,WAAW,EACjCjB,IAAI,CAACgB,SAAAA;AACJ,UAAME,6BAA6B;MACjC;QAAEC,QAAQ;QAA2CC,SAASJ,KAAKK;MAAI;MACvE;QAAEF,QAAQ;QAAyCC,SAASJ,KAAKK;MAAI;IACtE;AACD,UAAMC,yBAAyB;MAC7B;QAAEH,QAAQ;QAAyCC,SAASJ,KAAKK;MAAI;IACtE;AACD,UAAMpB,cACJzB,SAAS,oBAAoB0C,6BAA6BI;AAE5D,UAAMC,2BAA2BR,eAAeS,KAAK,CAAC,EAAEH,IAAG,MAAOA,QAAQL,KAAKK,GAAG;AAElF,QAAII,SAAS;AAEb,QAAIF,0BAA0B;AAC5B,YAAMG,eAAe;QACnBC,MAAM;QACNC,UAAUL,yBAAyBM,SAASD;QAC5CE,MAAM,GAAGP,yBAAyBM,SAASE,aAAa,IAAIR,yBAAyBM,SAASG,gBAAgB;MAChH;AAEAP,mBAASQ,sBAAUP,cAAc;QAAEQ,QAAQ;MAAM,CAAA;IACnD;AAEA,WAAO;MACLjC;MACAwB;MACAjC,MAAMwB,KAAKxB;MACX2C,OAAOnB,KAAKoB,KAAKC;MACjBC,IAAI,oBAAoBtB,KAAKxB,SAAS,mBAAmB+C,mBAAmBC,YAAAA,IAC1ExB,KAAKK,GAAG;MAEVA,KAAKL,KAAKK;;MAEVoB,MAAMzB,KAAKK;MACXJ,aAAaD,KAAKC;IACpB;EACF,CAAA;AACJ;;;AC/MkG,IAE5FyB,SAAS,MAAA;AACb,QAAMC,mBAAmBC,SAAS,+BAAA;AAElC,QAAM,EAAEC,WAAWC,qBAAqBC,QAAQC,gBAAe,IAAKC,0BAAAA;AACpE,QAAMC,mBAAmB;IAAIJ,GAAAA;IAAwBE,GAAAA;IAAiBG,KAAK,CAACC,GAAGC,MAC7ED,EAAEE,MAAMC,cAAcF,EAAEC,KAAK,CAAA;AAG/B,QAAM,EAAEE,SAAQ,IAAKC,YAAAA;AACrB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAMC,eAAeC,cAAc,UAAU,CAACC,UAAUA,MAAMF,YAAY;AAC1E,QAAMG,kBAAwBC,cAAOJ,YAAAA;AAErCK,EAAMC,iBAAU,MAAA;AACd,QAAIH,gBAAgBI,SAAS;AAC3BJ,sBAAgBI,QAAQ,gBAAA;IAC1B;EACF,GAAG,CAAA,CAAE;AAEL,MAAItB,WAAW;AACb,eACEuB,0BAAAC,8BAAA;;YACEC,yBAACC,KAAKC,OAAK;oBACRd,cAAc;YACbe,IAAIC,eAAe,aAAA;YACnBC,gBAAgB;UAClB,CAAA;;YAEFL,yBAACC,KAAKK,SAAO,CAAA,CAAA;;;EAGnB;AAGA,QAAMC,2BAA2B9B,OAAO+B,OAAO,CAAC,EAAEC,YAAW,MAAOA,WAAAA;AAGpE,MACE7B,iBAAiB8B,WAAW,KAC5BH,yBAAyBG,SAAS,KAClCxB,aAAa,wBACb;AACA,eAAOc,yBAACW,UAAAA;MAASC,IAAG;;EACtB;AAGA,MAAIL,yBAAyBG,WAAW,KAAKxB,aAAa,qBAAqB;AAC7E,eAAOc,yBAACW,UAAAA;MAASC,IAAG;;EACtB;AAEA,MAAI,CAACvC,oBAAoBO,iBAAiB8B,SAAS,GAAG;AACpD,eACEV,yBAACW,UAAAA;MACCC,IAAI;QACF1B,UAAUN,iBAAiB,CAAE,EAACgC;QAC9BC,QAAQjC,iBAAiB,CAAE,EAACiC,UAAU;MACxC;MACAC,SAAO;;EAGb;AAEA,aACEhB,0BAAAC,8BAAA;;UACEC,yBAACC,KAAKC,OAAK;kBACRd,cAAc;UACbe,IAAIC,eAAe,aAAA;UACnBC,gBAAgB;QAClB,CAAA;;UAEFP,0BAACiB,QAAQC,MAAI;QAACC,aAASjB,yBAACkB,UAAAA,CAAAA,CAAAA;;cACtBlB,yBAACmB,WAAAA;YAAUC,YAAYC;;cACvBrB,yBAACsB,QAAAA,CAAAA,CAAAA;;;;;AAIT;AAMA,SAASD,oBAAoB,EAAEE,MAAMC,KAAI,GAA+C;AACtF,MAAI,CAACD,QAASA,QAAQ,OAAOA,SAAS,UAAW;AAC/C,WAAO;EACT;AAOA,QAAM,CAACE,UAAAA,IAAcF,KAAKG,MAAM,GAAA;AAEhC,UAAQD,YAAAA;IACN,KAAKE,UAAUC;IACf,KAAKD,UAAUE;AACb,iBAAO7B,yBAAC8B,iBAAAA;QAAgBC,OAAOP,KAAKO;;IACtC,KAAKJ,UAAUK;IACf,KAAKL,UAAUM;AACb,iBAAOjC,yBAACkC,sBAAAA;QAAqBC,gBAAgBX,KAAKW;;IAEpD,KAAKR,UAAUS;AACb,iBAAOpC,yBAACqC,qBAAAA;QAAqB,GAAGb;;IAElC;AACE,aAAO;EACX;AACF;", "names": ["getStyle", "initialOffset", "currentOffset", "mouseOffset", "display", "x", "y", "transform", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "renderItem", "itemType", "isDragging", "item", "useDragLayer", "monitor", "getItem", "getItemType", "getInitialSourceClientOffset", "getSourceClientOffset", "getClientOffset", "_jsx", "Box", "height", "left", "position", "pointerEvents", "top", "zIndex", "width", "style", "type", "ComponentDragPreview", "displayedValue", "_jsxs", "Flex", "background", "borderColor", "justifyContent", "gap", "padding", "width", "_jsx", "ToggleButton", "type", "DropdownIconWrapper", "alignItems", "height", "CaretDown", "max<PERSON><PERSON><PERSON>", "Typography", "textColor", "ellipsis", "IconButton", "withTooltip", "label", "variant", "Trash", "Drag", "styled", "theme", "colors", "neutral600", "button", "RelationDragPreview", "status", "displayedValue", "width", "_jsx", "Box", "style", "_jsxs", "Flex", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "hasRadius", "borderWidth", "background", "borderColor", "justifyContent", "gap", "FlexWrapper", "IconButton", "withTooltip", "label", "variant", "Drag", "min<PERSON><PERSON><PERSON>", "LinkEllipsis", "href", "Typography", "textColor", "ellipsis", "DocumentStatus", "DisconnectButton", "type", "Cross", "LeftMenu", "search", "setSearch", "useState", "query", "useQueryParams", "formatMessage", "locale", "useIntl", "collectionTypeLinks", "useTypedSelector", "state", "app", "singleTypeLinks", "schemas", "useContentTypeSchema", "startsWith", "useFilter", "sensitivity", "formatter", "useCollator", "menu", "useMemo", "id", "title", "getTranslation", "defaultMessage", "searchable", "links", "map", "section", "filter", "link", "sort", "a", "b", "compare", "handleClear", "handleChangeSearch", "target", "value", "label", "getPluginsParamsForLink", "schema", "find", "uid", "isI18nEnabled", "Boolean", "pluginOptions", "i18n", "localized", "plugins", "restPlugins", "_jsxs", "SubNav", "Main", "aria-label", "_jsx", "Header", "Divider", "background", "Flex", "padding", "gap", "direction", "alignItems", "TextInput", "startAction", "Search", "fill", "onChange", "placeholder", "endAction", "Cross", "onClick", "cursor", "size", "Sections", "Section", "Link", "to", "pathname", "stringify", "parse", "MUTATE_COLLECTION_TYPES_LINKS", "MUTATE_SINGLE_TYPES_LINKS", "HOOKS", "useContentManagerInitData", "toggleNotification", "useNotification", "dispatch", "useTypedDispatch", "runHookWaterfall", "useStrapiApp", "state", "notify<PERSON><PERSON><PERSON>", "useNotifyAT", "formatMessage", "useIntl", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "getTranslation", "checkUserHasPermissions", "useAuth", "useTypedSelector", "app", "initialDataQuery", "useGetInitialDataQuery", "undefined", "refetchOnMountOrArgChange", "useEffect", "data", "id", "defaultMessage", "error", "type", "message", "contentTypeSettingsQuery", "useGetAllContentTypeSettingsQuery", "formatData", "components", "contentTypes", "fieldSizes", "contentTypeConfigurations", "collectionType", "collectionTypeLinks", "singleType", "singleTypeLinks", "reduce", "acc", "model", "kind", "push", "collectionTypeSectionLinks", "generateLinks", "singleTypeSectionLinks", "collectionTypeLinksPermissions", "Promise", "all", "map", "permissions", "authorizedCollectionTypeLinks", "filter", "_", "index", "length", "singleTypeLinksPermissions", "authorizedSingleTypeLinks", "ctLinks", "models", "stLinks", "setInitialData", "contentTypeSchemas", "links", "configurations", "link", "isDisplayed", "collectionTypesPermissions", "action", "subject", "uid", "singleTypesPermissions", "currentContentTypeConfig", "find", "search", "searchParams", "page", "pageSize", "settings", "sort", "defaultSortBy", "defaultSortOrder", "stringify", "encode", "title", "info", "displayName", "to", "COLLECTION_TYPES", "SINGLE_TYPES", "name", "Layout", "contentTypeMatch", "useMatch", "isLoading", "collectionTypeLinks", "models", "singleTypeLinks", "useContentManagerInitData", "authorisedModels", "sort", "a", "b", "title", "localeCompare", "pathname", "useLocation", "formatMessage", "useIntl", "startSection", "useGuidedTour", "state", "startSectionRef", "useRef", "React", "useEffect", "current", "_jsxs", "_Fragment", "_jsx", "Page", "Title", "id", "getTranslation", "defaultMessage", "Loading", "supportedModelsToDisplay", "filter", "isDisplayed", "length", "Navigate", "to", "search", "replace", "Layouts", "Root", "sideNav", "LeftMenu", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "renderItem", "renderDraglayerItem", "Outlet", "type", "item", "actualType", "split", "ItemTypes", "EDIT_FIELD", "FIELD", "CardDragPreview", "label", "COMPONENT", "DYNAMIC_ZONE", "ComponentDragPreview", "displayedValue", "RELATION", "RelationDragPreview"]}