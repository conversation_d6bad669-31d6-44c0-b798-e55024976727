/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app-pages-internals"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/lib/metadata/metadata-boundary.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    MetadataBoundary: function() {\n        return MetadataBoundary;\n    },\n    OutletBoundary: function() {\n        return OutletBoundary;\n    },\n    ViewportBoundary: function() {\n        return ViewportBoundary;\n    }\n});\nconst _metadataconstants = __webpack_require__(/*! ./metadata-constants */ \"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-constants.js\");\n// We use a namespace object to allow us to recover the name of the function\n// at runtime even when production bundling/minification is used.\nconst NameSpace = {\n    [_metadataconstants.METADATA_BOUNDARY_NAME]: function(param) {\n        let { children } = param;\n        return children;\n    },\n    [_metadataconstants.VIEWPORT_BOUNDARY_NAME]: function(param) {\n        let { children } = param;\n        return children;\n    },\n    [_metadataconstants.OUTLET_BOUNDARY_NAME]: function(param) {\n        let { children } = param;\n        return children;\n    }\n};\nconst MetadataBoundary = // so it retains the name inferred from the namespace object\nNameSpace[_metadataconstants.METADATA_BOUNDARY_NAME.slice(0)];\nconst ViewportBoundary = // so it retains the name inferred from the namespace object\nNameSpace[_metadataconstants.VIEWPORT_BOUNDARY_NAME.slice(0)];\nconst OutletBoundary = // so it retains the name inferred from the namespace object\nNameSpace[_metadataconstants.OUTLET_BOUNDARY_NAME.slice(0)]; //# sourceMappingURL=metadata-boundary.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/lib/scheduler.js":
/*!*************************************************!*\
  !*** ./node_modules/next/dist/lib/scheduler.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    atLeastOneTask: function() {\n        return atLeastOneTask;\n    },\n    scheduleImmediate: function() {\n        return scheduleImmediate;\n    },\n    scheduleOnNextTick: function() {\n        return scheduleOnNextTick;\n    },\n    waitAtLeastOneReactRenderTask: function() {\n        return waitAtLeastOneReactRenderTask;\n    }\n});\nconst scheduleOnNextTick = (cb)=>{\n    // We use Promise.resolve().then() here so that the operation is scheduled at\n    // the end of the promise job queue, we then add it to the next process tick\n    // to ensure it's evaluated afterwards.\n    //\n    // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n    //\n    Promise.resolve().then(()=>{\n        if (false) {} else {\n            process.nextTick(cb);\n        }\n    });\n};\nconst scheduleImmediate = (cb)=>{\n    if (false) {} else {\n        setImmediate(cb);\n    }\n};\nfunction atLeastOneTask() {\n    return new Promise((resolve)=>scheduleImmediate(resolve));\n}\nfunction waitAtLeastOneReactRenderTask() {\n    if (false) {} else {\n        return new Promise((r)=>setImmediate(r));\n    }\n}\n\n//# sourceMappingURL=scheduler.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/lib/scheduler.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/app-render/after-task-async-storage-instance.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/after-task-async-storage-instance.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"afterTaskAsyncStorageInstance\", ({\n    enumerable: true,\n    get: function() {\n        return afterTaskAsyncStorageInstance;\n    }\n}));\nconst _asynclocalstorage = __webpack_require__(/*! ./async-local-storage */ \"(app-pages-browser)/./node_modules/next/dist/server/app-render/async-local-storage.js\");\nconst afterTaskAsyncStorageInstance = (0, _asynclocalstorage.createAsyncLocalStorage)();\n\n//# sourceMappingURL=after-task-async-storage-instance.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvYWZ0ZXItdGFzay1hc3luYy1zdG9yYWdlLWluc3RhbmNlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YsaUVBQWdFO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YsMkJBQTJCLG1CQUFPLENBQUMsb0hBQXVCO0FBQzFEOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEpveWRldkhhbGRlclxcRGVza3RvcFxcUmVhY3QgV29ya1xcU3RyYXBpX1Byb2plY3RcXEZyb250ZW5kXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXHNlcnZlclxcYXBwLXJlbmRlclxcYWZ0ZXItdGFzay1hc3luYy1zdG9yYWdlLWluc3RhbmNlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiYWZ0ZXJUYXNrQXN5bmNTdG9yYWdlSW5zdGFuY2VcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIGFmdGVyVGFza0FzeW5jU3RvcmFnZUluc3RhbmNlO1xuICAgIH1cbn0pO1xuY29uc3QgX2FzeW5jbG9jYWxzdG9yYWdlID0gcmVxdWlyZShcIi4vYXN5bmMtbG9jYWwtc3RvcmFnZVwiKTtcbmNvbnN0IGFmdGVyVGFza0FzeW5jU3RvcmFnZUluc3RhbmNlID0gKDAsIF9hc3luY2xvY2Fsc3RvcmFnZS5jcmVhdGVBc3luY0xvY2FsU3RvcmFnZSkoKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YWZ0ZXItdGFzay1hc3luYy1zdG9yYWdlLWluc3RhbmNlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/app-render/after-task-async-storage-instance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/app-render/after-task-async-storage.external.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/after-task-async-storage.external.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"afterTaskAsyncStorage\", ({\n    enumerable: true,\n    get: function() {\n        return _aftertaskasyncstorageinstance.afterTaskAsyncStorageInstance;\n    }\n}));\nconst _aftertaskasyncstorageinstance = __webpack_require__(/*! ./after-task-async-storage-instance */ \"(app-pages-browser)/./node_modules/next/dist/server/app-render/after-task-async-storage-instance.js\");\n\n//# sourceMappingURL=after-task-async-storage.external.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvYWZ0ZXItdGFzay1hc3luYy1zdG9yYWdlLmV4dGVybmFsLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YseURBQXdEO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YsdUNBQXVDLG1CQUFPLENBQUMsZ0pBQXFDOztBQUVwRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxKb3lkZXZIYWxkZXJcXERlc2t0b3BcXFJlYWN0IFdvcmtcXFN0cmFwaV9Qcm9qZWN0XFxGcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxzZXJ2ZXJcXGFwcC1yZW5kZXJcXGFmdGVyLXRhc2stYXN5bmMtc3RvcmFnZS5leHRlcm5hbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcImFmdGVyVGFza0FzeW5jU3RvcmFnZVwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gX2FmdGVydGFza2FzeW5jc3RvcmFnZWluc3RhbmNlLmFmdGVyVGFza0FzeW5jU3RvcmFnZUluc3RhbmNlO1xuICAgIH1cbn0pO1xuY29uc3QgX2FmdGVydGFza2FzeW5jc3RvcmFnZWluc3RhbmNlID0gcmVxdWlyZShcIi4vYWZ0ZXItdGFzay1hc3luYy1zdG9yYWdlLWluc3RhbmNlXCIpO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hZnRlci10YXNrLWFzeW5jLXN0b3JhZ2UuZXh0ZXJuYWwuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/app-render/after-task-async-storage.external.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/app-render/async-local-storage.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/async-local-storage.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    bindSnapshot: function() {\n        return bindSnapshot;\n    },\n    createAsyncLocalStorage: function() {\n        return createAsyncLocalStorage;\n    },\n    createSnapshot: function() {\n        return createSnapshot;\n    }\n});\nconst sharedAsyncLocalStorageNotAvailableError = new Error('Invariant: AsyncLocalStorage accessed in runtime where it is not available');\nclass FakeAsyncLocalStorage {\n    disable() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    getStore() {\n        // This fake implementation of AsyncLocalStorage always returns `undefined`.\n        return undefined;\n    }\n    run() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    exit() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    enterWith() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    static bind(fn) {\n        return fn;\n    }\n}\nconst maybeGlobalAsyncLocalStorage = typeof globalThis !== 'undefined' && globalThis.AsyncLocalStorage;\nfunction createAsyncLocalStorage() {\n    if (maybeGlobalAsyncLocalStorage) {\n        return new maybeGlobalAsyncLocalStorage();\n    }\n    return new FakeAsyncLocalStorage();\n}\nfunction bindSnapshot(fn) {\n    if (maybeGlobalAsyncLocalStorage) {\n        return maybeGlobalAsyncLocalStorage.bind(fn);\n    }\n    return FakeAsyncLocalStorage.bind(fn);\n}\nfunction createSnapshot() {\n    if (maybeGlobalAsyncLocalStorage) {\n        return maybeGlobalAsyncLocalStorage.snapshot();\n    }\n    return function(fn, ...args) {\n        return fn(...args);\n    };\n}\n\n//# sourceMappingURL=async-local-storage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/app-render/async-local-storage.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createDedupedByCallsiteServerErrorLoggerDev\", ({\n    enumerable: true,\n    get: function() {\n        return createDedupedByCallsiteServerErrorLoggerDev;\n    }\n}));\nconst _react = /*#__PURE__*/ _interop_require_wildcard(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {\n        __proto__: null\n    };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nconst errorRef = {\n    current: null\n};\n// React.cache is currently only available in canary/experimental React channels.\nconst cache = typeof _react.cache === 'function' ? _react.cache : (fn)=>fn;\n// When Dynamic IO is enabled, we record these as errors so that they\n// are captured by the dev overlay as it's more critical to fix these\n// when enabled.\nconst logErrorOrWarn =  false ? 0 : console.warn;\n// We don't want to dedupe across requests.\n// The developer might've just attempted to fix the warning so we should warn again if it still happens.\nconst flushCurrentErrorIfNew = cache(// eslint-disable-next-line @typescript-eslint/no-unused-vars -- cache key\n(key)=>{\n    try {\n        logErrorOrWarn(errorRef.current);\n    } finally{\n        errorRef.current = null;\n    }\n});\nfunction createDedupedByCallsiteServerErrorLoggerDev(getMessage) {\n    return function logDedupedError(...args) {\n        const message = getMessage(...args);\n        if (true) {\n            var _stack;\n            const callStackFrames = (_stack = new Error().stack) == null ? void 0 : _stack.split('\\n');\n            if (callStackFrames === undefined || callStackFrames.length < 4) {\n                logErrorOrWarn(message);\n            } else {\n                // Error:\n                //   logDedupedError\n                //   asyncApiBeingAccessedSynchronously\n                //   <userland callsite>\n                // TODO: This breaks if sourcemaps with ignore lists are enabled.\n                const key = callStackFrames[4];\n                errorRef.current = message;\n                flushCurrentErrorIfNew(key);\n            }\n        } else {}\n    };\n}\n\n//# sourceMappingURL=create-deduped-by-callsite-server-error-logger.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/request/params.browser.js":
/*!*****************************************************************!*\
  !*** ./node_modules/next/dist/server/request/params.browser.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createRenderParamsFromClient\", ({\n    enumerable: true,\n    get: function() {\n        return createRenderParamsFromClient;\n    }\n}));\nconst _reflect = __webpack_require__(/*! ../web/spec-extension/adapters/reflect */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\");\nconst _invarianterror = __webpack_require__(/*! ../../shared/lib/invariant-error */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js\");\nconst _utils = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/./node_modules/next/dist/server/request/utils.js\");\nfunction createRenderParamsFromClient(underlyingParams) {\n    if (true) {\n        return makeDynamicallyTrackedExoticParamsWithDevWarnings(underlyingParams);\n    } else {}\n}\nconst CachedParams = new WeakMap();\nfunction makeUntrackedExoticParams(underlyingParams) {\n    const cachedParams = CachedParams.get(underlyingParams);\n    if (cachedParams) {\n        return cachedParams;\n    }\n    const promise = Promise.resolve(underlyingParams);\n    CachedParams.set(underlyingParams, promise);\n    Object.keys(underlyingParams).forEach((prop)=>{\n        if (_utils.wellKnownProperties.has(prop)) {\n        // These properties cannot be shadowed because they need to be the\n        // true underlying value for Promises to work correctly at runtime\n        } else {\n            ;\n            promise[prop] = underlyingParams[prop];\n        }\n    });\n    return promise;\n}\nfunction makeDynamicallyTrackedExoticParamsWithDevWarnings(underlyingParams) {\n    const cachedParams = CachedParams.get(underlyingParams);\n    if (cachedParams) {\n        return cachedParams;\n    }\n    // We don't use makeResolvedReactPromise here because params\n    // supports copying with spread and we don't want to unnecessarily\n    // instrument the promise with spreadable properties of ReactPromise.\n    const promise = Promise.resolve(underlyingParams);\n    const proxiedProperties = new Set();\n    const unproxiedProperties = [];\n    Object.keys(underlyingParams).forEach((prop)=>{\n        if (_utils.wellKnownProperties.has(prop)) {\n        // These properties cannot be shadowed because they need to be the\n        // true underlying value for Promises to work correctly at runtime\n        } else {\n            proxiedProperties.add(prop);\n            promise[prop] = underlyingParams[prop];\n        }\n    });\n    const proxiedPromise = new Proxy(promise, {\n        get (target, prop, receiver) {\n            if (typeof prop === 'string') {\n                if (// We are accessing a property that was proxied to the promise instance\n                proxiedProperties.has(prop)) {\n                    const expression = (0, _utils.describeStringPropertyAccess)('params', prop);\n                    warnForSyncAccess(expression);\n                }\n            }\n            return _reflect.ReflectAdapter.get(target, prop, receiver);\n        },\n        set (target, prop, value, receiver) {\n            if (typeof prop === 'string') {\n                proxiedProperties.delete(prop);\n            }\n            return _reflect.ReflectAdapter.set(target, prop, value, receiver);\n        },\n        ownKeys (target) {\n            warnForEnumeration(unproxiedProperties);\n            return Reflect.ownKeys(target);\n        }\n    });\n    CachedParams.set(underlyingParams, proxiedPromise);\n    return proxiedPromise;\n}\nconst noop = ()=>{};\nconst warnForSyncAccess =  false ? 0 : function warnForSyncAccess(expression) {\n    if (false) {}\n    console.error(`A param property was accessed directly with ${expression}. \\`params\\` is now a Promise and should be unwrapped with \\`React.use()\\` before accessing properties of the underlying params object. In this version of Next.js direct access to param properties is still supported to facilitate migration but in a future version you will be required to unwrap \\`params\\` with \\`React.use()\\`.`);\n};\nconst warnForEnumeration =  false ? 0 : function warnForEnumeration(missingProperties) {\n    if (false) {}\n    if (missingProperties.length) {\n        const describedMissingProperties = describeListOfPropertyNames(missingProperties);\n        console.error(`params are being enumerated incompletely missing these properties: ${describedMissingProperties}. ` + `\\`params\\` should be unwrapped with \\`React.use()\\` before using its value. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`);\n    } else {\n        console.error(`params are being enumerated. ` + `\\`params\\` should be unwrapped with \\`React.use()\\` before using its value. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`);\n    }\n};\nfunction describeListOfPropertyNames(properties) {\n    switch(properties.length){\n        case 0:\n            throw new _invarianterror.InvariantError('Expected describeListOfPropertyNames to be called with a non-empty list of strings.');\n        case 1:\n            return `\\`${properties[0]}\\``;\n        case 2:\n            return `\\`${properties[0]}\\` and \\`${properties[1]}\\``;\n        default:\n            {\n                let description = '';\n                for(let i = 0; i < properties.length - 1; i++){\n                    description += `\\`${properties[i]}\\`, `;\n                }\n                description += `, and \\`${properties[properties.length - 1]}\\``;\n                return description;\n            }\n    }\n}\n\n//# sourceMappingURL=params.browser.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/request/params.browser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/request/params.js":
/*!*********************************************************!*\
  !*** ./node_modules/next/dist/server/request/params.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createParamsFromClient: function() {\n        return createParamsFromClient;\n    },\n    createPrerenderParamsForClientSegment: function() {\n        return createPrerenderParamsForClientSegment;\n    },\n    createServerParamsForMetadata: function() {\n        return createServerParamsForMetadata;\n    },\n    createServerParamsForRoute: function() {\n        return createServerParamsForRoute;\n    },\n    createServerParamsForServerSegment: function() {\n        return createServerParamsForServerSegment;\n    }\n});\nconst _reflect = __webpack_require__(/*! ../web/spec-extension/adapters/reflect */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\");\nconst _dynamicrendering = __webpack_require__(/*! ../app-render/dynamic-rendering */ \"(app-pages-browser)/./node_modules/next/dist/server/app-render/dynamic-rendering.js\");\nconst _workunitasyncstorageexternal = __webpack_require__(/*! ../app-render/work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage.external.js\");\nconst _invarianterror = __webpack_require__(/*! ../../shared/lib/invariant-error */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js\");\nconst _utils = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/./node_modules/next/dist/server/request/utils.js\");\nconst _dynamicrenderingutils = __webpack_require__(/*! ../dynamic-rendering-utils */ \"(app-pages-browser)/./node_modules/next/dist/server/dynamic-rendering-utils.js\");\nconst _creatededupedbycallsiteservererrorlogger = __webpack_require__(/*! ../create-deduped-by-callsite-server-error-logger */ \"(app-pages-browser)/./node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js\");\nconst _scheduler = __webpack_require__(/*! ../../lib/scheduler */ \"(app-pages-browser)/./node_modules/next/dist/lib/scheduler.js\");\nfunction createParamsFromClient(underlyingParams, workStore) {\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workUnitStore) {\n        switch(workUnitStore.type){\n            case 'prerender':\n            case 'prerender-ppr':\n            case 'prerender-legacy':\n                return createPrerenderParams(underlyingParams, workStore, workUnitStore);\n            default:\n        }\n    }\n    return createRenderParams(underlyingParams, workStore);\n}\nconst createServerParamsForMetadata = createServerParamsForServerSegment;\nfunction createServerParamsForRoute(underlyingParams, workStore) {\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workUnitStore) {\n        switch(workUnitStore.type){\n            case 'prerender':\n            case 'prerender-ppr':\n            case 'prerender-legacy':\n                return createPrerenderParams(underlyingParams, workStore, workUnitStore);\n            default:\n        }\n    }\n    return createRenderParams(underlyingParams, workStore);\n}\nfunction createServerParamsForServerSegment(underlyingParams, workStore) {\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workUnitStore) {\n        switch(workUnitStore.type){\n            case 'prerender':\n            case 'prerender-ppr':\n            case 'prerender-legacy':\n                return createPrerenderParams(underlyingParams, workStore, workUnitStore);\n            default:\n        }\n    }\n    return createRenderParams(underlyingParams, workStore);\n}\nfunction createPrerenderParamsForClientSegment(underlyingParams, workStore) {\n    const prerenderStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (prerenderStore && prerenderStore.type === 'prerender') {\n        const fallbackParams = workStore.fallbackRouteParams;\n        if (fallbackParams) {\n            for(let key in underlyingParams){\n                if (fallbackParams.has(key)) {\n                    // This params object has one of more fallback params so we need to consider\n                    // the awaiting of this params object \"dynamic\". Since we are in dynamicIO mode\n                    // we encode this as a promise that never resolves\n                    return (0, _dynamicrenderingutils.makeHangingPromise)(prerenderStore.renderSignal, '`params`');\n                }\n            }\n        }\n    }\n    // We're prerendering in a mode that does not abort. We resolve the promise without\n    // any tracking because we're just transporting a value from server to client where the tracking\n    // will be applied.\n    return Promise.resolve(underlyingParams);\n}\nfunction createPrerenderParams(underlyingParams, workStore, prerenderStore) {\n    const fallbackParams = workStore.fallbackRouteParams;\n    if (fallbackParams) {\n        let hasSomeFallbackParams = false;\n        for(const key in underlyingParams){\n            if (fallbackParams.has(key)) {\n                hasSomeFallbackParams = true;\n                break;\n            }\n        }\n        if (hasSomeFallbackParams) {\n            // params need to be treated as dynamic because we have at least one fallback param\n            if (prerenderStore.type === 'prerender') {\n                // We are in a dynamicIO (PPR or otherwise) prerender\n                return makeAbortingExoticParams(underlyingParams, workStore.route, prerenderStore);\n            }\n            // remaining cases are prender-ppr and prerender-legacy\n            // We aren't in a dynamicIO prerender but we do have fallback params at this\n            // level so we need to make an erroring exotic params object which will postpone\n            // if you access the fallback params\n            return makeErroringExoticParams(underlyingParams, fallbackParams, workStore, prerenderStore);\n        }\n    }\n    // We don't have any fallback params so we have an entirely static safe params object\n    return makeUntrackedExoticParams(underlyingParams);\n}\nfunction createRenderParams(underlyingParams, workStore) {\n    if ( true && !workStore.isPrefetchRequest) {\n        return makeDynamicallyTrackedExoticParamsWithDevWarnings(underlyingParams, workStore);\n    } else {\n        return makeUntrackedExoticParams(underlyingParams);\n    }\n}\nconst CachedParams = new WeakMap();\nfunction makeAbortingExoticParams(underlyingParams, route, prerenderStore) {\n    const cachedParams = CachedParams.get(underlyingParams);\n    if (cachedParams) {\n        return cachedParams;\n    }\n    const promise = (0, _dynamicrenderingutils.makeHangingPromise)(prerenderStore.renderSignal, '`params`');\n    CachedParams.set(underlyingParams, promise);\n    Object.keys(underlyingParams).forEach((prop)=>{\n        if (_utils.wellKnownProperties.has(prop)) {\n        // These properties cannot be shadowed because they need to be the\n        // true underlying value for Promises to work correctly at runtime\n        } else {\n            Object.defineProperty(promise, prop, {\n                get () {\n                    const expression = (0, _utils.describeStringPropertyAccess)('params', prop);\n                    const error = createParamsAccessError(route, expression);\n                    (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n                },\n                set (newValue) {\n                    Object.defineProperty(promise, prop, {\n                        value: newValue,\n                        writable: true,\n                        enumerable: true\n                    });\n                },\n                enumerable: true,\n                configurable: true\n            });\n        }\n    });\n    return promise;\n}\nfunction makeErroringExoticParams(underlyingParams, fallbackParams, workStore, prerenderStore) {\n    const cachedParams = CachedParams.get(underlyingParams);\n    if (cachedParams) {\n        return cachedParams;\n    }\n    const augmentedUnderlying = {\n        ...underlyingParams\n    };\n    // We don't use makeResolvedReactPromise here because params\n    // supports copying with spread and we don't want to unnecessarily\n    // instrument the promise with spreadable properties of ReactPromise.\n    const promise = Promise.resolve(augmentedUnderlying);\n    CachedParams.set(underlyingParams, promise);\n    Object.keys(underlyingParams).forEach((prop)=>{\n        if (_utils.wellKnownProperties.has(prop)) {\n        // These properties cannot be shadowed because they need to be the\n        // true underlying value for Promises to work correctly at runtime\n        } else {\n            if (fallbackParams.has(prop)) {\n                Object.defineProperty(augmentedUnderlying, prop, {\n                    get () {\n                        const expression = (0, _utils.describeStringPropertyAccess)('params', prop);\n                        // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n                        // for params is only dynamic when we're generating a fallback shell\n                        // and even when `dynamic = \"error\"` we still support generating dynamic\n                        // fallback shells\n                        // TODO remove this comment when dynamicIO is the default since there\n                        // will be no `dynamic = \"error\"`\n                        if (prerenderStore.type === 'prerender-ppr') {\n                            // PPR Prerender (no dynamicIO)\n                            (0, _dynamicrendering.postponeWithTracking)(workStore.route, expression, prerenderStore.dynamicTracking);\n                        } else {\n                            // Legacy Prerender\n                            (0, _dynamicrendering.throwToInterruptStaticGeneration)(expression, workStore, prerenderStore);\n                        }\n                    },\n                    enumerable: true\n                });\n                Object.defineProperty(promise, prop, {\n                    get () {\n                        const expression = (0, _utils.describeStringPropertyAccess)('params', prop);\n                        // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n                        // for params is only dynamic when we're generating a fallback shell\n                        // and even when `dynamic = \"error\"` we still support generating dynamic\n                        // fallback shells\n                        // TODO remove this comment when dynamicIO is the default since there\n                        // will be no `dynamic = \"error\"`\n                        if (prerenderStore.type === 'prerender-ppr') {\n                            // PPR Prerender (no dynamicIO)\n                            (0, _dynamicrendering.postponeWithTracking)(workStore.route, expression, prerenderStore.dynamicTracking);\n                        } else {\n                            // Legacy Prerender\n                            (0, _dynamicrendering.throwToInterruptStaticGeneration)(expression, workStore, prerenderStore);\n                        }\n                    },\n                    set (newValue) {\n                        Object.defineProperty(promise, prop, {\n                            value: newValue,\n                            writable: true,\n                            enumerable: true\n                        });\n                    },\n                    enumerable: true,\n                    configurable: true\n                });\n            } else {\n                ;\n                promise[prop] = underlyingParams[prop];\n            }\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedExoticParams(underlyingParams) {\n    const cachedParams = CachedParams.get(underlyingParams);\n    if (cachedParams) {\n        return cachedParams;\n    }\n    // We don't use makeResolvedReactPromise here because params\n    // supports copying with spread and we don't want to unnecessarily\n    // instrument the promise with spreadable properties of ReactPromise.\n    const promise = Promise.resolve(underlyingParams);\n    CachedParams.set(underlyingParams, promise);\n    Object.keys(underlyingParams).forEach((prop)=>{\n        if (_utils.wellKnownProperties.has(prop)) {\n        // These properties cannot be shadowed because they need to be the\n        // true underlying value for Promises to work correctly at runtime\n        } else {\n            ;\n            promise[prop] = underlyingParams[prop];\n        }\n    });\n    return promise;\n}\nfunction makeDynamicallyTrackedExoticParamsWithDevWarnings(underlyingParams, store) {\n    const cachedParams = CachedParams.get(underlyingParams);\n    if (cachedParams) {\n        return cachedParams;\n    }\n    // We don't use makeResolvedReactPromise here because params\n    // supports copying with spread and we don't want to unnecessarily\n    // instrument the promise with spreadable properties of ReactPromise.\n    const promise = new Promise((resolve)=>(0, _scheduler.scheduleImmediate)(()=>resolve(underlyingParams)));\n    const proxiedProperties = new Set();\n    const unproxiedProperties = [];\n    Object.keys(underlyingParams).forEach((prop)=>{\n        if (_utils.wellKnownProperties.has(prop)) {\n            // These properties cannot be shadowed because they need to be the\n            // true underlying value for Promises to work correctly at runtime\n            unproxiedProperties.push(prop);\n        } else {\n            proxiedProperties.add(prop);\n            promise[prop] = underlyingParams[prop];\n        }\n    });\n    const proxiedPromise = new Proxy(promise, {\n        get (target, prop, receiver) {\n            if (typeof prop === 'string') {\n                if (// We are accessing a property that was proxied to the promise instance\n                proxiedProperties.has(prop)) {\n                    const expression = (0, _utils.describeStringPropertyAccess)('params', prop);\n                    syncIODev(store.route, expression);\n                }\n            }\n            return _reflect.ReflectAdapter.get(target, prop, receiver);\n        },\n        set (target, prop, value, receiver) {\n            if (typeof prop === 'string') {\n                proxiedProperties.delete(prop);\n            }\n            return _reflect.ReflectAdapter.set(target, prop, value, receiver);\n        },\n        ownKeys (target) {\n            const expression = '`...params` or similar expression';\n            syncIODev(store.route, expression, unproxiedProperties);\n            return Reflect.ownKeys(target);\n        }\n    });\n    CachedParams.set(underlyingParams, proxiedPromise);\n    return proxiedPromise;\n}\nfunction syncIODev(route, expression, missingProperties) {\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workUnitStore && workUnitStore.type === 'request' && workUnitStore.prerenderPhase === true) {\n        // When we're rendering dynamically in dev we need to advance out of the\n        // Prerender environment when we read Request data synchronously\n        const requestStore = workUnitStore;\n        (0, _dynamicrendering.trackSynchronousRequestDataAccessInDev)(requestStore);\n    }\n    // In all cases we warn normally\n    if (missingProperties && missingProperties.length > 0) {\n        warnForIncompleteEnumeration(route, expression, missingProperties);\n    } else {\n        warnForSyncAccess(route, expression);\n    }\n}\nconst noop = ()=>{};\nconst warnForSyncAccess =  false ? 0 : (0, _creatededupedbycallsiteservererrorlogger.createDedupedByCallsiteServerErrorLoggerDev)(createParamsAccessError);\nconst warnForIncompleteEnumeration =  false ? 0 : (0, _creatededupedbycallsiteservererrorlogger.createDedupedByCallsiteServerErrorLoggerDev)(createIncompleteEnumerationError);\nfunction createParamsAccessError(route, expression) {\n    const prefix = route ? `Route \"${route}\" ` : 'This route ';\n    return new Error(`${prefix}used ${expression}. ` + `\\`params\\` should be awaited before using its properties. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`);\n}\nfunction createIncompleteEnumerationError(route, expression, missingProperties) {\n    const prefix = route ? `Route \"${route}\" ` : 'This route ';\n    return new Error(`${prefix}used ${expression}. ` + `\\`params\\` should be awaited before using its properties. ` + `The following properties were not available through enumeration ` + `because they conflict with builtin property names: ` + `${describeListOfPropertyNames(missingProperties)}. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`);\n}\nfunction describeListOfPropertyNames(properties) {\n    switch(properties.length){\n        case 0:\n            throw new _invarianterror.InvariantError('Expected describeListOfPropertyNames to be called with a non-empty list of strings.');\n        case 1:\n            return `\\`${properties[0]}\\``;\n        case 2:\n            return `\\`${properties[0]}\\` and \\`${properties[1]}\\``;\n        default:\n            {\n                let description = '';\n                for(let i = 0; i < properties.length - 1; i++){\n                    description += `\\`${properties[i]}\\`, `;\n                }\n                description += `, and \\`${properties[properties.length - 1]}\\``;\n                return description;\n            }\n    }\n}\n\n//# sourceMappingURL=params.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/request/params.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/request/search-params.browser.js":
/*!************************************************************************!*\
  !*** ./node_modules/next/dist/server/request/search-params.browser.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createRenderSearchParamsFromClient\", ({\n    enumerable: true,\n    get: function() {\n        return createRenderSearchParamsFromClient;\n    }\n}));\nconst _reflect = __webpack_require__(/*! ../web/spec-extension/adapters/reflect */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\");\nconst _utils = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/./node_modules/next/dist/server/request/utils.js\");\nfunction createRenderSearchParamsFromClient(underlyingSearchParams) {\n    if (true) {\n        return makeUntrackedExoticSearchParamsWithDevWarnings(underlyingSearchParams);\n    } else {}\n}\nconst CachedSearchParams = new WeakMap();\nfunction makeUntrackedExoticSearchParamsWithDevWarnings(underlyingSearchParams) {\n    const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams);\n    if (cachedSearchParams) {\n        return cachedSearchParams;\n    }\n    const proxiedProperties = new Set();\n    const unproxiedProperties = [];\n    const promise = Promise.resolve(underlyingSearchParams);\n    Object.keys(underlyingSearchParams).forEach((prop)=>{\n        if (_utils.wellKnownProperties.has(prop)) {\n            // These properties cannot be shadowed because they need to be the\n            // true underlying value for Promises to work correctly at runtime\n            unproxiedProperties.push(prop);\n        } else {\n            proxiedProperties.add(prop);\n            promise[prop] = underlyingSearchParams[prop];\n        }\n    });\n    const proxiedPromise = new Proxy(promise, {\n        get (target, prop, receiver) {\n            if (typeof prop === 'string') {\n                if (!_utils.wellKnownProperties.has(prop) && (proxiedProperties.has(prop) || // We are accessing a property that doesn't exist on the promise nor\n                // the underlying searchParams.\n                Reflect.has(target, prop) === false)) {\n                    const expression = (0, _utils.describeStringPropertyAccess)('searchParams', prop);\n                    warnForSyncAccess(expression);\n                }\n            }\n            return _reflect.ReflectAdapter.get(target, prop, receiver);\n        },\n        set (target, prop, value, receiver) {\n            if (typeof prop === 'string') {\n                proxiedProperties.delete(prop);\n            }\n            return Reflect.set(target, prop, value, receiver);\n        },\n        has (target, prop) {\n            if (typeof prop === 'string') {\n                if (!_utils.wellKnownProperties.has(prop) && (proxiedProperties.has(prop) || // We are accessing a property that doesn't exist on the promise nor\n                // the underlying searchParams.\n                Reflect.has(target, prop) === false)) {\n                    const expression = (0, _utils.describeHasCheckingStringProperty)('searchParams', prop);\n                    warnForSyncAccess(expression);\n                }\n            }\n            return Reflect.has(target, prop);\n        },\n        ownKeys (target) {\n            warnForSyncSpread();\n            return Reflect.ownKeys(target);\n        }\n    });\n    CachedSearchParams.set(underlyingSearchParams, proxiedPromise);\n    return proxiedPromise;\n}\nfunction makeUntrackedExoticSearchParams(underlyingSearchParams) {\n    const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams);\n    if (cachedSearchParams) {\n        return cachedSearchParams;\n    }\n    // We don't use makeResolvedReactPromise here because searchParams\n    // supports copying with spread and we don't want to unnecessarily\n    // instrument the promise with spreadable properties of ReactPromise.\n    const promise = Promise.resolve(underlyingSearchParams);\n    CachedSearchParams.set(underlyingSearchParams, promise);\n    Object.keys(underlyingSearchParams).forEach((prop)=>{\n        if (_utils.wellKnownProperties.has(prop)) {\n        // These properties cannot be shadowed because they need to be the\n        // true underlying value for Promises to work correctly at runtime\n        } else {\n            ;\n            promise[prop] = underlyingSearchParams[prop];\n        }\n    });\n    return promise;\n}\nconst noop = ()=>{};\nconst warnForSyncAccess =  false ? 0 : function warnForSyncAccess(expression) {\n    if (false) {}\n    console.error(`A searchParam property was accessed directly with ${expression}. ` + `\\`searchParams\\` should be unwrapped with \\`React.use()\\` before accessing its properties. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`);\n};\nconst warnForSyncSpread =  false ? 0 : function warnForSyncSpread() {\n    if (false) {}\n    console.error(`The keys of \\`searchParams\\` were accessed directly. ` + `\\`searchParams\\` should be unwrapped with \\`React.use()\\` before accessing its properties. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`);\n};\n\n//# sourceMappingURL=search-params.browser.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/request/search-params.browser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/request/search-params.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/server/request/search-params.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createPrerenderSearchParamsForClientPage: function() {\n        return createPrerenderSearchParamsForClientPage;\n    },\n    createSearchParamsFromClient: function() {\n        return createSearchParamsFromClient;\n    },\n    createServerSearchParamsForMetadata: function() {\n        return createServerSearchParamsForMetadata;\n    },\n    createServerSearchParamsForServerPage: function() {\n        return createServerSearchParamsForServerPage;\n    }\n});\nconst _reflect = __webpack_require__(/*! ../web/spec-extension/adapters/reflect */ \"(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\");\nconst _dynamicrendering = __webpack_require__(/*! ../app-render/dynamic-rendering */ \"(app-pages-browser)/./node_modules/next/dist/server/app-render/dynamic-rendering.js\");\nconst _workunitasyncstorageexternal = __webpack_require__(/*! ../app-render/work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-unit-async-storage.external.js\");\nconst _invarianterror = __webpack_require__(/*! ../../shared/lib/invariant-error */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js\");\nconst _dynamicrenderingutils = __webpack_require__(/*! ../dynamic-rendering-utils */ \"(app-pages-browser)/./node_modules/next/dist/server/dynamic-rendering-utils.js\");\nconst _creatededupedbycallsiteservererrorlogger = __webpack_require__(/*! ../create-deduped-by-callsite-server-error-logger */ \"(app-pages-browser)/./node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js\");\nconst _utils = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/./node_modules/next/dist/server/request/utils.js\");\nconst _scheduler = __webpack_require__(/*! ../../lib/scheduler */ \"(app-pages-browser)/./node_modules/next/dist/lib/scheduler.js\");\nfunction createSearchParamsFromClient(underlyingSearchParams, workStore) {\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workUnitStore) {\n        switch(workUnitStore.type){\n            case 'prerender':\n            case 'prerender-ppr':\n            case 'prerender-legacy':\n                return createPrerenderSearchParams(workStore, workUnitStore);\n            default:\n        }\n    }\n    return createRenderSearchParams(underlyingSearchParams, workStore);\n}\nconst createServerSearchParamsForMetadata = createServerSearchParamsForServerPage;\nfunction createServerSearchParamsForServerPage(underlyingSearchParams, workStore) {\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workUnitStore) {\n        switch(workUnitStore.type){\n            case 'prerender':\n            case 'prerender-ppr':\n            case 'prerender-legacy':\n                return createPrerenderSearchParams(workStore, workUnitStore);\n            default:\n        }\n    }\n    return createRenderSearchParams(underlyingSearchParams, workStore);\n}\nfunction createPrerenderSearchParamsForClientPage(workStore) {\n    if (workStore.forceStatic) {\n        // When using forceStatic we override all other logic and always just return an empty\n        // dictionary object.\n        return Promise.resolve({});\n    }\n    const prerenderStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (prerenderStore && prerenderStore.type === 'prerender') {\n        // dynamicIO Prerender\n        // We're prerendering in a mode that aborts (dynamicIO) and should stall\n        // the promise to ensure the RSC side is considered dynamic\n        return (0, _dynamicrenderingutils.makeHangingPromise)(prerenderStore.renderSignal, '`searchParams`');\n    }\n    // We're prerendering in a mode that does not aborts. We resolve the promise without\n    // any tracking because we're just transporting a value from server to client where the tracking\n    // will be applied.\n    return Promise.resolve({});\n}\nfunction createPrerenderSearchParams(workStore, prerenderStore) {\n    if (workStore.forceStatic) {\n        // When using forceStatic we override all other logic and always just return an empty\n        // dictionary object.\n        return Promise.resolve({});\n    }\n    if (prerenderStore.type === 'prerender') {\n        // We are in a dynamicIO (PPR or otherwise) prerender\n        return makeAbortingExoticSearchParams(workStore.route, prerenderStore);\n    }\n    // The remaining cases are prerender-ppr and prerender-legacy\n    // We are in a legacy static generation and need to interrupt the prerender\n    // when search params are accessed.\n    return makeErroringExoticSearchParams(workStore, prerenderStore);\n}\nfunction createRenderSearchParams(underlyingSearchParams, workStore) {\n    if (workStore.forceStatic) {\n        // When using forceStatic we override all other logic and always just return an empty\n        // dictionary object.\n        return Promise.resolve({});\n    } else {\n        if ( true && !workStore.isPrefetchRequest) {\n            return makeDynamicallyTrackedExoticSearchParamsWithDevWarnings(underlyingSearchParams, workStore);\n        } else {\n            return makeUntrackedExoticSearchParams(underlyingSearchParams, workStore);\n        }\n    }\n}\nconst CachedSearchParams = new WeakMap();\nfunction makeAbortingExoticSearchParams(route, prerenderStore) {\n    const cachedSearchParams = CachedSearchParams.get(prerenderStore);\n    if (cachedSearchParams) {\n        return cachedSearchParams;\n    }\n    const promise = (0, _dynamicrenderingutils.makeHangingPromise)(prerenderStore.renderSignal, '`searchParams`');\n    const proxiedPromise = new Proxy(promise, {\n        get (target, prop, receiver) {\n            if (Object.hasOwn(promise, prop)) {\n                // The promise has this property directly. we must return it.\n                // We know it isn't a dynamic access because it can only be something\n                // that was previously written to the promise and thus not an underlying searchParam value\n                return _reflect.ReflectAdapter.get(target, prop, receiver);\n            }\n            switch(prop){\n                case 'then':\n                    {\n                        const expression = '`await searchParams`, `searchParams.then`, or similar';\n                        (0, _dynamicrendering.annotateDynamicAccess)(expression, prerenderStore);\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                    }\n                case 'status':\n                    {\n                        const expression = '`use(searchParams)`, `searchParams.status`, or similar';\n                        (0, _dynamicrendering.annotateDynamicAccess)(expression, prerenderStore);\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                    }\n                // Object prototype\n                case 'hasOwnProperty':\n                case 'isPrototypeOf':\n                case 'propertyIsEnumerable':\n                case 'toString':\n                case 'valueOf':\n                case 'toLocaleString':\n                // Promise prototype\n                // fallthrough\n                case 'catch':\n                case 'finally':\n                // Common tested properties\n                // fallthrough\n                case 'toJSON':\n                case '$$typeof':\n                case '__esModule':\n                    {\n                        // These properties cannot be shadowed because they need to be the\n                        // true underlying value for Promises to work correctly at runtime\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                    }\n                default:\n                    {\n                        if (typeof prop === 'string') {\n                            const expression = (0, _utils.describeStringPropertyAccess)('searchParams', prop);\n                            const error = createSearchAccessError(route, expression);\n                            (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n                        }\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                    }\n            }\n        },\n        has (target, prop) {\n            // We don't expect key checking to be used except for testing the existence of\n            // searchParams so we make all has tests trigger dynamic. this means that `promise.then`\n            // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n            // you are testing whether the searchParams has a 'then' property.\n            if (typeof prop === 'string') {\n                const expression = (0, _utils.describeHasCheckingStringProperty)('searchParams', prop);\n                const error = createSearchAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n            return _reflect.ReflectAdapter.has(target, prop);\n        },\n        ownKeys () {\n            const expression = '`{...searchParams}`, `Object.keys(searchParams)`, or similar';\n            const error = createSearchAccessError(route, expression);\n            (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n        }\n    });\n    CachedSearchParams.set(prerenderStore, proxiedPromise);\n    return proxiedPromise;\n}\nfunction makeErroringExoticSearchParams(workStore, prerenderStore) {\n    const cachedSearchParams = CachedSearchParams.get(workStore);\n    if (cachedSearchParams) {\n        return cachedSearchParams;\n    }\n    const underlyingSearchParams = {};\n    // For search params we don't construct a ReactPromise because we want to interrupt\n    // rendering on any property access that was not set from outside and so we only want\n    // to have properties like value and status if React sets them.\n    const promise = Promise.resolve(underlyingSearchParams);\n    const proxiedPromise = new Proxy(promise, {\n        get (target, prop, receiver) {\n            if (Object.hasOwn(promise, prop)) {\n                // The promise has this property directly. we must return it.\n                // We know it isn't a dynamic access because it can only be something\n                // that was previously written to the promise and thus not an underlying searchParam value\n                return _reflect.ReflectAdapter.get(target, prop, receiver);\n            }\n            switch(prop){\n                // Object prototype\n                case 'hasOwnProperty':\n                case 'isPrototypeOf':\n                case 'propertyIsEnumerable':\n                case 'toString':\n                case 'valueOf':\n                case 'toLocaleString':\n                // Promise prototype\n                // fallthrough\n                case 'catch':\n                case 'finally':\n                // Common tested properties\n                // fallthrough\n                case 'toJSON':\n                case '$$typeof':\n                case '__esModule':\n                    {\n                        // These properties cannot be shadowed because they need to be the\n                        // true underlying value for Promises to work correctly at runtime\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                    }\n                case 'then':\n                    {\n                        const expression = '`await searchParams`, `searchParams.then`, or similar';\n                        if (workStore.dynamicShouldError) {\n                            (0, _utils.throwWithStaticGenerationBailoutErrorWithDynamicError)(workStore.route, expression);\n                        } else if (prerenderStore.type === 'prerender-ppr') {\n                            // PPR Prerender (no dynamicIO)\n                            (0, _dynamicrendering.postponeWithTracking)(workStore.route, expression, prerenderStore.dynamicTracking);\n                        } else {\n                            // Legacy Prerender\n                            (0, _dynamicrendering.throwToInterruptStaticGeneration)(expression, workStore, prerenderStore);\n                        }\n                        return;\n                    }\n                case 'status':\n                    {\n                        const expression = '`use(searchParams)`, `searchParams.status`, or similar';\n                        if (workStore.dynamicShouldError) {\n                            (0, _utils.throwWithStaticGenerationBailoutErrorWithDynamicError)(workStore.route, expression);\n                        } else if (prerenderStore.type === 'prerender-ppr') {\n                            // PPR Prerender (no dynamicIO)\n                            (0, _dynamicrendering.postponeWithTracking)(workStore.route, expression, prerenderStore.dynamicTracking);\n                        } else {\n                            // Legacy Prerender\n                            (0, _dynamicrendering.throwToInterruptStaticGeneration)(expression, workStore, prerenderStore);\n                        }\n                        return;\n                    }\n                default:\n                    {\n                        if (typeof prop === 'string') {\n                            const expression = (0, _utils.describeStringPropertyAccess)('searchParams', prop);\n                            if (workStore.dynamicShouldError) {\n                                (0, _utils.throwWithStaticGenerationBailoutErrorWithDynamicError)(workStore.route, expression);\n                            } else if (prerenderStore.type === 'prerender-ppr') {\n                                // PPR Prerender (no dynamicIO)\n                                (0, _dynamicrendering.postponeWithTracking)(workStore.route, expression, prerenderStore.dynamicTracking);\n                            } else {\n                                // Legacy Prerender\n                                (0, _dynamicrendering.throwToInterruptStaticGeneration)(expression, workStore, prerenderStore);\n                            }\n                        }\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                    }\n            }\n        },\n        has (target, prop) {\n            // We don't expect key checking to be used except for testing the existence of\n            // searchParams so we make all has tests trigger dynamic. this means that `promise.then`\n            // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n            // you are testing whether the searchParams has a 'then' property.\n            if (typeof prop === 'string') {\n                const expression = (0, _utils.describeHasCheckingStringProperty)('searchParams', prop);\n                if (workStore.dynamicShouldError) {\n                    (0, _utils.throwWithStaticGenerationBailoutErrorWithDynamicError)(workStore.route, expression);\n                } else if (prerenderStore.type === 'prerender-ppr') {\n                    // PPR Prerender (no dynamicIO)\n                    (0, _dynamicrendering.postponeWithTracking)(workStore.route, expression, prerenderStore.dynamicTracking);\n                } else {\n                    // Legacy Prerender\n                    (0, _dynamicrendering.throwToInterruptStaticGeneration)(expression, workStore, prerenderStore);\n                }\n                return false;\n            }\n            return _reflect.ReflectAdapter.has(target, prop);\n        },\n        ownKeys () {\n            const expression = '`{...searchParams}`, `Object.keys(searchParams)`, or similar';\n            if (workStore.dynamicShouldError) {\n                (0, _utils.throwWithStaticGenerationBailoutErrorWithDynamicError)(workStore.route, expression);\n            } else if (prerenderStore.type === 'prerender-ppr') {\n                // PPR Prerender (no dynamicIO)\n                (0, _dynamicrendering.postponeWithTracking)(workStore.route, expression, prerenderStore.dynamicTracking);\n            } else {\n                // Legacy Prerender\n                (0, _dynamicrendering.throwToInterruptStaticGeneration)(expression, workStore, prerenderStore);\n            }\n        }\n    });\n    CachedSearchParams.set(workStore, proxiedPromise);\n    return proxiedPromise;\n}\nfunction makeUntrackedExoticSearchParams(underlyingSearchParams, store) {\n    const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams);\n    if (cachedSearchParams) {\n        return cachedSearchParams;\n    }\n    // We don't use makeResolvedReactPromise here because searchParams\n    // supports copying with spread and we don't want to unnecessarily\n    // instrument the promise with spreadable properties of ReactPromise.\n    const promise = Promise.resolve(underlyingSearchParams);\n    CachedSearchParams.set(underlyingSearchParams, promise);\n    Object.keys(underlyingSearchParams).forEach((prop)=>{\n        switch(prop){\n            // Object prototype\n            case 'hasOwnProperty':\n            case 'isPrototypeOf':\n            case 'propertyIsEnumerable':\n            case 'toString':\n            case 'valueOf':\n            case 'toLocaleString':\n            // Promise prototype\n            // fallthrough\n            case 'then':\n            case 'catch':\n            case 'finally':\n            // React Promise extension\n            // fallthrough\n            case 'status':\n            // Common tested properties\n            // fallthrough\n            case 'toJSON':\n            case '$$typeof':\n            case '__esModule':\n                {\n                    break;\n                }\n            default:\n                {\n                    Object.defineProperty(promise, prop, {\n                        get () {\n                            const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n                            (0, _dynamicrendering.trackDynamicDataInDynamicRender)(store, workUnitStore);\n                            return underlyingSearchParams[prop];\n                        },\n                        set (value) {\n                            Object.defineProperty(promise, prop, {\n                                value,\n                                writable: true,\n                                enumerable: true\n                            });\n                        },\n                        enumerable: true,\n                        configurable: true\n                    });\n                }\n        }\n    });\n    return promise;\n}\nfunction makeDynamicallyTrackedExoticSearchParamsWithDevWarnings(underlyingSearchParams, store) {\n    const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams);\n    if (cachedSearchParams) {\n        return cachedSearchParams;\n    }\n    const proxiedProperties = new Set();\n    const unproxiedProperties = [];\n    // We have an unfortunate sequence of events that requires this initialization logic. We want to instrument the underlying\n    // searchParams object to detect if you are accessing values in dev. This is used for warnings and for things like the static prerender\n    // indicator. However when we pass this proxy to our Promise.resolve() below the VM checks if the resolved value is a promise by looking\n    // at the `.then` property. To our dynamic tracking logic this is indistinguishable from a `then` searchParam and so we would normally trigger\n    // dynamic tracking. However we know that this .then is not real dynamic access, it's just how thenables resolve in sequence. So we introduce\n    // this initialization concept so we omit the dynamic check until after we've constructed our resolved promise.\n    let promiseInitialized = false;\n    const proxiedUnderlying = new Proxy(underlyingSearchParams, {\n        get (target, prop, receiver) {\n            if (typeof prop === 'string' && promiseInitialized) {\n                if (store.dynamicShouldError) {\n                    const expression = (0, _utils.describeStringPropertyAccess)('searchParams', prop);\n                    (0, _utils.throwWithStaticGenerationBailoutErrorWithDynamicError)(store.route, expression);\n                }\n                const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n                (0, _dynamicrendering.trackDynamicDataInDynamicRender)(store, workUnitStore);\n            }\n            return _reflect.ReflectAdapter.get(target, prop, receiver);\n        },\n        has (target, prop) {\n            if (typeof prop === 'string') {\n                if (store.dynamicShouldError) {\n                    const expression = (0, _utils.describeHasCheckingStringProperty)('searchParams', prop);\n                    (0, _utils.throwWithStaticGenerationBailoutErrorWithDynamicError)(store.route, expression);\n                }\n            }\n            return Reflect.has(target, prop);\n        },\n        ownKeys (target) {\n            if (store.dynamicShouldError) {\n                const expression = '`{...searchParams}`, `Object.keys(searchParams)`, or similar';\n                (0, _utils.throwWithStaticGenerationBailoutErrorWithDynamicError)(store.route, expression);\n            }\n            return Reflect.ownKeys(target);\n        }\n    });\n    // We don't use makeResolvedReactPromise here because searchParams\n    // supports copying with spread and we don't want to unnecessarily\n    // instrument the promise with spreadable properties of ReactPromise.\n    const promise = new Promise((resolve)=>(0, _scheduler.scheduleImmediate)(()=>resolve(underlyingSearchParams)));\n    promise.then(()=>{\n        promiseInitialized = true;\n    });\n    Object.keys(underlyingSearchParams).forEach((prop)=>{\n        if (_utils.wellKnownProperties.has(prop)) {\n            // These properties cannot be shadowed because they need to be the\n            // true underlying value for Promises to work correctly at runtime\n            unproxiedProperties.push(prop);\n        } else {\n            proxiedProperties.add(prop);\n            Object.defineProperty(promise, prop, {\n                get () {\n                    return proxiedUnderlying[prop];\n                },\n                set (newValue) {\n                    Object.defineProperty(promise, prop, {\n                        value: newValue,\n                        writable: true,\n                        enumerable: true\n                    });\n                },\n                enumerable: true,\n                configurable: true\n            });\n        }\n    });\n    const proxiedPromise = new Proxy(promise, {\n        get (target, prop, receiver) {\n            if (prop === 'then' && store.dynamicShouldError) {\n                const expression = '`searchParams.then`';\n                (0, _utils.throwWithStaticGenerationBailoutErrorWithDynamicError)(store.route, expression);\n            }\n            if (typeof prop === 'string') {\n                if (!_utils.wellKnownProperties.has(prop) && (proxiedProperties.has(prop) || // We are accessing a property that doesn't exist on the promise nor\n                // the underlying searchParams.\n                Reflect.has(target, prop) === false)) {\n                    const expression = (0, _utils.describeStringPropertyAccess)('searchParams', prop);\n                    syncIODev(store.route, expression);\n                }\n            }\n            return _reflect.ReflectAdapter.get(target, prop, receiver);\n        },\n        set (target, prop, value, receiver) {\n            if (typeof prop === 'string') {\n                proxiedProperties.delete(prop);\n            }\n            return Reflect.set(target, prop, value, receiver);\n        },\n        has (target, prop) {\n            if (typeof prop === 'string') {\n                if (!_utils.wellKnownProperties.has(prop) && (proxiedProperties.has(prop) || // We are accessing a property that doesn't exist on the promise nor\n                // the underlying searchParams.\n                Reflect.has(target, prop) === false)) {\n                    const expression = (0, _utils.describeHasCheckingStringProperty)('searchParams', prop);\n                    syncIODev(store.route, expression);\n                }\n            }\n            return Reflect.has(target, prop);\n        },\n        ownKeys (target) {\n            const expression = '`Object.keys(searchParams)` or similar';\n            syncIODev(store.route, expression, unproxiedProperties);\n            return Reflect.ownKeys(target);\n        }\n    });\n    CachedSearchParams.set(underlyingSearchParams, proxiedPromise);\n    return proxiedPromise;\n}\nfunction syncIODev(route, expression, missingProperties) {\n    // In all cases we warn normally\n    if (missingProperties && missingProperties.length > 0) {\n        warnForIncompleteEnumeration(route, expression, missingProperties);\n    } else {\n        warnForSyncAccess(route, expression);\n    }\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workUnitStore && workUnitStore.type === 'request' && workUnitStore.prerenderPhase === true) {\n        // When we're rendering dynamically in dev we need to advance out of the\n        // Prerender environment when we read Request data synchronously\n        const requestStore = workUnitStore;\n        (0, _dynamicrendering.trackSynchronousRequestDataAccessInDev)(requestStore);\n    }\n}\nconst noop = ()=>{};\nconst warnForSyncAccess =  false ? 0 : (0, _creatededupedbycallsiteservererrorlogger.createDedupedByCallsiteServerErrorLoggerDev)(createSearchAccessError);\nconst warnForIncompleteEnumeration =  false ? 0 : (0, _creatededupedbycallsiteservererrorlogger.createDedupedByCallsiteServerErrorLoggerDev)(createIncompleteEnumerationError);\nfunction createSearchAccessError(route, expression) {\n    const prefix = route ? `Route \"${route}\" ` : 'This route ';\n    return new Error(`${prefix}used ${expression}. ` + `\\`searchParams\\` should be awaited before using its properties. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`);\n}\nfunction createIncompleteEnumerationError(route, expression, missingProperties) {\n    const prefix = route ? `Route \"${route}\" ` : 'This route ';\n    return new Error(`${prefix}used ${expression}. ` + `\\`searchParams\\` should be awaited before using its properties. ` + `The following properties were not available through enumeration ` + `because they conflict with builtin or well-known property names: ` + `${describeListOfPropertyNames(missingProperties)}. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`);\n}\nfunction describeListOfPropertyNames(properties) {\n    switch(properties.length){\n        case 0:\n            throw new _invarianterror.InvariantError('Expected describeListOfPropertyNames to be called with a non-empty list of strings.');\n        case 1:\n            return `\\`${properties[0]}\\``;\n        case 2:\n            return `\\`${properties[0]}\\` and \\`${properties[1]}\\``;\n        default:\n            {\n                let description = '';\n                for(let i = 0; i < properties.length - 1; i++){\n                    description += `\\`${properties[i]}\\`, `;\n                }\n                description += `, and \\`${properties[properties.length - 1]}\\``;\n                return description;\n            }\n    }\n}\n\n//# sourceMappingURL=search-params.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/request/search-params.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/request/utils.js":
/*!********************************************************!*\
  !*** ./node_modules/next/dist/server/request/utils.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    describeHasCheckingStringProperty: function() {\n        return describeHasCheckingStringProperty;\n    },\n    describeStringPropertyAccess: function() {\n        return describeStringPropertyAccess;\n    },\n    isRequestAPICallableInsideAfter: function() {\n        return isRequestAPICallableInsideAfter;\n    },\n    throwWithStaticGenerationBailoutError: function() {\n        return throwWithStaticGenerationBailoutError;\n    },\n    throwWithStaticGenerationBailoutErrorWithDynamicError: function() {\n        return throwWithStaticGenerationBailoutErrorWithDynamicError;\n    },\n    wellKnownProperties: function() {\n        return wellKnownProperties;\n    }\n});\nconst _staticgenerationbailout = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js\");\nconst _aftertaskasyncstorageexternal = __webpack_require__(/*! ../app-render/after-task-async-storage.external */ \"(app-pages-browser)/./node_modules/next/dist/server/app-render/after-task-async-storage.external.js\");\n// This regex will have fast negatives meaning valid identifiers may not pass\n// this test. However this is only used during static generation to provide hints\n// about why a page bailed out of some or all prerendering and we can use bracket notation\n// for example while `ಠ_ಠ` is a valid identifier it's ok to print `searchParams['ಠ_ಠ']`\n// even if this would have been fine too `searchParams.ಠ_ಠ`\nconst isDefinitelyAValidIdentifier = /^[A-Za-z_$][A-Za-z0-9_$]*$/;\nfunction describeStringPropertyAccess(target, prop) {\n    if (isDefinitelyAValidIdentifier.test(prop)) {\n        return `\\`${target}.${prop}\\``;\n    }\n    return `\\`${target}[${JSON.stringify(prop)}]\\``;\n}\nfunction describeHasCheckingStringProperty(target, prop) {\n    const stringifiedProp = JSON.stringify(prop);\n    return `\\`Reflect.has(${target}, ${stringifiedProp})\\`, \\`${stringifiedProp} in ${target}\\`, or similar`;\n}\nfunction throwWithStaticGenerationBailoutError(route, expression) {\n    throw new _staticgenerationbailout.StaticGenBailoutError(`Route ${route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);\n}\nfunction throwWithStaticGenerationBailoutErrorWithDynamicError(route, expression) {\n    throw new _staticgenerationbailout.StaticGenBailoutError(`Route ${route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);\n}\nfunction isRequestAPICallableInsideAfter() {\n    const afterTaskStore = _aftertaskasyncstorageexternal.afterTaskAsyncStorage.getStore();\n    return (afterTaskStore == null ? void 0 : afterTaskStore.rootTaskSpawnPhase) === 'action';\n}\nconst wellKnownProperties = new Set([\n    'hasOwnProperty',\n    'isPrototypeOf',\n    'propertyIsEnumerable',\n    'toString',\n    'valueOf',\n    'toLocaleString',\n    // Promise prototype\n    // fallthrough\n    'then',\n    'catch',\n    'finally',\n    // React Promise extension\n    // fallthrough\n    'status',\n    // React introspection\n    'displayName',\n    // Common tested properties\n    // fallthrough\n    'toJSON',\n    '$$typeof',\n    '__esModule'\n]);\n\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/request/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ReflectAdapter\", ({\n    enumerable: true,\n    get: function() {\n        return ReflectAdapter;\n    }\n}));\nclass ReflectAdapter {\n    static get(target, prop, receiver) {\n        const value = Reflect.get(target, prop, receiver);\n        if (typeof value === 'function') {\n            return value.bind(target);\n        }\n        return value;\n    }\n    static set(target, prop, value, receiver) {\n        return Reflect.set(target, prop, value, receiver);\n    }\n    static has(target, prop) {\n        return Reflect.has(target, prop);\n    }\n    static deleteProperty(target, prop) {\n        return Reflect.deleteProperty(target, prop);\n    }\n}\n\n//# sourceMappingURL=reflect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL3dlYi9zcGVjLWV4dGVuc2lvbi9hZGFwdGVycy9yZWZsZWN0LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Ysa0RBQWlEO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEpveWRldkhhbGRlclxcRGVza3RvcFxcUmVhY3QgV29ya1xcU3RyYXBpX1Byb2plY3RcXEZyb250ZW5kXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXHNlcnZlclxcd2ViXFxzcGVjLWV4dGVuc2lvblxcYWRhcHRlcnNcXHJlZmxlY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJSZWZsZWN0QWRhcHRlclwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gUmVmbGVjdEFkYXB0ZXI7XG4gICAgfVxufSk7XG5jbGFzcyBSZWZsZWN0QWRhcHRlciB7XG4gICAgc3RhdGljIGdldCh0YXJnZXQsIHByb3AsIHJlY2VpdmVyKSB7XG4gICAgICAgIGNvbnN0IHZhbHVlID0gUmVmbGVjdC5nZXQodGFyZ2V0LCBwcm9wLCByZWNlaXZlcik7XG4gICAgICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgICAgIHJldHVybiB2YWx1ZS5iaW5kKHRhcmdldCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHZhbHVlO1xuICAgIH1cbiAgICBzdGF0aWMgc2V0KHRhcmdldCwgcHJvcCwgdmFsdWUsIHJlY2VpdmVyKSB7XG4gICAgICAgIHJldHVybiBSZWZsZWN0LnNldCh0YXJnZXQsIHByb3AsIHZhbHVlLCByZWNlaXZlcik7XG4gICAgfVxuICAgIHN0YXRpYyBoYXModGFyZ2V0LCBwcm9wKSB7XG4gICAgICAgIHJldHVybiBSZWZsZWN0Lmhhcyh0YXJnZXQsIHByb3ApO1xuICAgIH1cbiAgICBzdGF0aWMgZGVsZXRlUHJvcGVydHkodGFyZ2V0LCBwcm9wKSB7XG4gICAgICAgIHJldHVybiBSZWZsZWN0LmRlbGV0ZVByb3BlcnR5KHRhcmdldCwgcHJvcCk7XG4gICAgfVxufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZWZsZWN0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/server/web/spec-extension/adapters/reflect.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":
/*!*****************************************************************!*\
  !*** ./node_modules/next/dist/client/components/client-page.js ***!
  \*****************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ClientPageRoot\", ({\n    enumerable: true,\n    get: function() {\n        return ClientPageRoot;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _invarianterror = __webpack_require__(/*! ../../shared/lib/invariant-error */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js\");\nfunction ClientPageRoot(param) {\n    let { Component, searchParams, params, promises } = param;\n    if (typeof window === 'undefined') {\n        const { workAsyncStorage } = __webpack_require__(/*! ../../server/app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js\");\n        let clientSearchParams;\n        let clientParams;\n        // We are going to instrument the searchParams prop with tracking for the\n        // appropriate context. We wrap differently in prerendering vs rendering\n        const store = workAsyncStorage.getStore();\n        if (!store) {\n            throw new _invarianterror.InvariantError('Expected workStore to exist when handling searchParams in a client Page.');\n        }\n        const { createSearchParamsFromClient } = __webpack_require__(/*! ../../server/request/search-params */ \"(app-pages-browser)/./node_modules/next/dist/server/request/search-params.js\");\n        clientSearchParams = createSearchParamsFromClient(searchParams, store);\n        const { createParamsFromClient } = __webpack_require__(/*! ../../server/request/params */ \"(app-pages-browser)/./node_modules/next/dist/server/request/params.js\");\n        clientParams = createParamsFromClient(params, store);\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(Component, {\n            params: clientParams,\n            searchParams: clientSearchParams\n        });\n    } else {\n        const { createRenderSearchParamsFromClient } = __webpack_require__(/*! ../../server/request/search-params.browser */ \"(app-pages-browser)/./node_modules/next/dist/server/request/search-params.browser.js\");\n        const clientSearchParams = createRenderSearchParamsFromClient(searchParams);\n        const { createRenderParamsFromClient } = __webpack_require__(/*! ../../server/request/params.browser */ \"(app-pages-browser)/./node_modules/next/dist/server/request/params.browser.js\");\n        const clientParams = createRenderParamsFromClient(params);\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(Component, {\n            params: clientParams,\n            searchParams: clientSearchParams\n        });\n    }\n}\n_c = ClientPageRoot;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=client-page.js.map\nvar _c;\n$RefreshReg$(_c, \"ClientPageRoot\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":
/*!********************************************************************!*\
  !*** ./node_modules/next/dist/client/components/client-segment.js ***!
  \********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ClientSegmentRoot\", ({\n    enumerable: true,\n    get: function() {\n        return ClientSegmentRoot;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _invarianterror = __webpack_require__(/*! ../../shared/lib/invariant-error */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js\");\nfunction ClientSegmentRoot(param) {\n    let { Component, slots, params, promise } = param;\n    if (typeof window === 'undefined') {\n        const { workAsyncStorage } = __webpack_require__(/*! ../../server/app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js\");\n        let clientParams;\n        // We are going to instrument the searchParams prop with tracking for the\n        // appropriate context. We wrap differently in prerendering vs rendering\n        const store = workAsyncStorage.getStore();\n        if (!store) {\n            throw new _invarianterror.InvariantError('Expected workStore to exist when handling params in a client segment such as a Layout or Template.');\n        }\n        const { createParamsFromClient } = __webpack_require__(/*! ../../server/request/params */ \"(app-pages-browser)/./node_modules/next/dist/server/request/params.js\");\n        clientParams = createParamsFromClient(params, store);\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(Component, {\n            ...slots,\n            params: clientParams\n        });\n    } else {\n        const { createRenderParamsFromClient } = __webpack_require__(/*! ../../server/request/params.browser */ \"(app-pages-browser)/./node_modules/next/dist/server/request/params.browser.js\");\n        const clientParams = createRenderParamsFromClient(params);\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(Component, {\n            ...slots,\n            params: clientParams\n        });\n    }\n}\n_c = ClientSegmentRoot;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=client-segment.js.map\nvar _c;\n$RefreshReg$(_c, \"ClientSegmentRoot\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next/dist/client/components/layout-router.js ***!
  \*******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return OuterLayoutRouter;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _reactdom = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _fetchserverresponse = __webpack_require__(/*! ./router-reducer/fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _unresolvedthenable = __webpack_require__(/*! ./unresolved-thenable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/unresolved-thenable.js\");\nconst _errorboundary = __webpack_require__(/*! ./error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\");\nconst _matchsegments = __webpack_require__(/*! ./match-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/match-segments.js\");\nconst _handlesmoothscroll = __webpack_require__(/*! ../../shared/lib/router/utils/handle-smooth-scroll */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\");\nconst _redirectboundary = __webpack_require__(/*! ./redirect-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js\");\nconst _errorboundary1 = __webpack_require__(/*! ./http-access-fallback/error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\");\nconst _getsegmentvalue = __webpack_require__(/*! ./router-reducer/reducers/get-segment-value */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/get-segment-value.js\");\nconst _createroutercachekey = __webpack_require__(/*! ./router-reducer/create-router-cache-key */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-router-cache-key.js\");\nconst _hasinterceptionrouteincurrenttree = __webpack_require__(/*! ./router-reducer/reducers/has-interception-route-in-current-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js\");\n/**\n * Add refetch marker to router state at the point of the current layout segment.\n * This ensures the response returned is not further down than the current layout segment.\n */ function walkAddRefetch(segmentPathToWalk, treeToRecreate) {\n    if (segmentPathToWalk) {\n        const [segment, parallelRouteKey] = segmentPathToWalk;\n        const isLast = segmentPathToWalk.length === 2;\n        if ((0, _matchsegments.matchSegment)(treeToRecreate[0], segment)) {\n            if (treeToRecreate[1].hasOwnProperty(parallelRouteKey)) {\n                if (isLast) {\n                    const subTree = walkAddRefetch(undefined, treeToRecreate[1][parallelRouteKey]);\n                    return [\n                        treeToRecreate[0],\n                        {\n                            ...treeToRecreate[1],\n                            [parallelRouteKey]: [\n                                subTree[0],\n                                subTree[1],\n                                subTree[2],\n                                'refetch'\n                            ]\n                        }\n                    ];\n                }\n                return [\n                    treeToRecreate[0],\n                    {\n                        ...treeToRecreate[1],\n                        [parallelRouteKey]: walkAddRefetch(segmentPathToWalk.slice(2), treeToRecreate[1][parallelRouteKey])\n                    }\n                ];\n            }\n        }\n    }\n    return treeToRecreate;\n}\nconst __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = _reactdom.default.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;\n// TODO-APP: Replace with new React API for finding dom nodes without a `ref` when available\n/**\n * Wraps ReactDOM.findDOMNode with additional logic to hide React Strict Mode warning\n */ function findDOMNode(instance) {\n    // Tree-shake for server bundle\n    if (typeof window === 'undefined') return null;\n    // __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode is null during module init.\n    // We need to lazily reference it.\n    const internal_reactDOMfindDOMNode = __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode;\n    return internal_reactDOMfindDOMNode(instance);\n}\nconst rectProperties = [\n    'bottom',\n    'height',\n    'left',\n    'right',\n    'top',\n    'width',\n    'x',\n    'y'\n];\n/**\n * Check if a HTMLElement is hidden or fixed/sticky position\n */ function shouldSkipElement(element) {\n    // we ignore fixed or sticky positioned elements since they'll likely pass the \"in-viewport\" check\n    // and will result in a situation we bail on scroll because of something like a fixed nav,\n    // even though the actual page content is offscreen\n    if ([\n        'sticky',\n        'fixed'\n    ].includes(getComputedStyle(element).position)) {\n        if (true) {\n            console.warn('Skipping auto-scroll behavior due to `position: sticky` or `position: fixed` on element:', element);\n        }\n        return true;\n    }\n    // Uses `getBoundingClientRect` to check if the element is hidden instead of `offsetParent`\n    // because `offsetParent` doesn't consider document/body\n    const rect = element.getBoundingClientRect();\n    return rectProperties.every((item)=>rect[item] === 0);\n}\n/**\n * Check if the top corner of the HTMLElement is in the viewport.\n */ function topOfElementInViewport(element, viewportHeight) {\n    const rect = element.getBoundingClientRect();\n    return rect.top >= 0 && rect.top <= viewportHeight;\n}\n/**\n * Find the DOM node for a hash fragment.\n * If `top` the page has to scroll to the top of the page. This mirrors the browser's behavior.\n * If the hash fragment is an id, the page has to scroll to the element with that id.\n * If the hash fragment is a name, the page has to scroll to the first element with that name.\n */ function getHashFragmentDomNode(hashFragment) {\n    // If the hash fragment is `top` the page has to scroll to the top of the page.\n    if (hashFragment === 'top') {\n        return document.body;\n    }\n    var _document_getElementById;\n    // If the hash fragment is an id, the page has to scroll to the element with that id.\n    return (_document_getElementById = document.getElementById(hashFragment)) != null ? _document_getElementById : document.getElementsByName(hashFragment)[0];\n}\nclass InnerScrollAndFocusHandler extends _react.default.Component {\n    componentDidMount() {\n        this.handlePotentialScroll();\n    }\n    componentDidUpdate() {\n        // Because this property is overwritten in handlePotentialScroll it's fine to always run it when true as it'll be set to false for subsequent renders.\n        if (this.props.focusAndScrollRef.apply) {\n            this.handlePotentialScroll();\n        }\n    }\n    render() {\n        return this.props.children;\n    }\n    constructor(...args){\n        super(...args), this.handlePotentialScroll = ()=>{\n            // Handle scroll and focus, it's only applied once in the first useEffect that triggers that changed.\n            const { focusAndScrollRef, segmentPath } = this.props;\n            if (focusAndScrollRef.apply) {\n                // segmentPaths is an array of segment paths that should be scrolled to\n                // if the current segment path is not in the array, the scroll is not applied\n                // unless the array is empty, in which case the scroll is always applied\n                if (focusAndScrollRef.segmentPaths.length !== 0 && !focusAndScrollRef.segmentPaths.some((scrollRefSegmentPath)=>segmentPath.every((segment, index)=>(0, _matchsegments.matchSegment)(segment, scrollRefSegmentPath[index])))) {\n                    return;\n                }\n                let domNode = null;\n                const hashFragment = focusAndScrollRef.hashFragment;\n                if (hashFragment) {\n                    domNode = getHashFragmentDomNode(hashFragment);\n                }\n                // `findDOMNode` is tricky because it returns just the first child if the component is a fragment.\n                // This already caused a bug where the first child was a <link/> in head.\n                if (!domNode) {\n                    domNode = findDOMNode(this);\n                }\n                // If there is no DOM node this layout-router level is skipped. It'll be handled higher-up in the tree.\n                if (!(domNode instanceof Element)) {\n                    return;\n                }\n                // Verify if the element is a HTMLElement and if we want to consider it for scroll behavior.\n                // If the element is skipped, try to select the next sibling and try again.\n                while(!(domNode instanceof HTMLElement) || shouldSkipElement(domNode)){\n                    // No siblings found that match the criteria are found, so handle scroll higher up in the tree instead.\n                    if (domNode.nextElementSibling === null) {\n                        return;\n                    }\n                    domNode = domNode.nextElementSibling;\n                }\n                // State is mutated to ensure that the focus and scroll is applied only once.\n                focusAndScrollRef.apply = false;\n                focusAndScrollRef.hashFragment = null;\n                focusAndScrollRef.segmentPaths = [];\n                (0, _handlesmoothscroll.handleSmoothScroll)(()=>{\n                    // In case of hash scroll, we only need to scroll the element into view\n                    if (hashFragment) {\n                        ;\n                        domNode.scrollIntoView();\n                        return;\n                    }\n                    // Store the current viewport height because reading `clientHeight` causes a reflow,\n                    // and it won't change during this function.\n                    const htmlElement = document.documentElement;\n                    const viewportHeight = htmlElement.clientHeight;\n                    // If the element's top edge is already in the viewport, exit early.\n                    if (topOfElementInViewport(domNode, viewportHeight)) {\n                        return;\n                    }\n                    // Otherwise, try scrolling go the top of the document to be backward compatible with pages\n                    // scrollIntoView() called on `<html/>` element scrolls horizontally on chrome and firefox (that shouldn't happen)\n                    // We could use it to scroll horizontally following RTL but that also seems to be broken - it will always scroll left\n                    // scrollLeft = 0 also seems to ignore RTL and manually checking for RTL is too much hassle so we will scroll just vertically\n                    htmlElement.scrollTop = 0;\n                    // Scroll to domNode if domNode is not in viewport when scrolled to top of document\n                    if (!topOfElementInViewport(domNode, viewportHeight)) {\n                        // Scroll into view doesn't scroll horizontally by default when not needed\n                        ;\n                        domNode.scrollIntoView();\n                    }\n                }, {\n                    // We will force layout by querying domNode position\n                    dontForceLayout: true,\n                    onlyHashChange: focusAndScrollRef.onlyHashChange\n                });\n                // Mutate after scrolling so that it can be read by `handleSmoothScroll`\n                focusAndScrollRef.onlyHashChange = false;\n                // Set focus on the element\n                domNode.focus();\n            }\n        };\n    }\n}\nfunction ScrollAndFocusHandler(param) {\n    let { segmentPath, children } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.GlobalLayoutRouterContext);\n    if (!context) {\n        throw new Error('invariant global layout router not mounted');\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(InnerScrollAndFocusHandler, {\n        segmentPath: segmentPath,\n        focusAndScrollRef: context.focusAndScrollRef,\n        children: children\n    });\n}\n_c = ScrollAndFocusHandler;\n/**\n * InnerLayoutRouter handles rendering the provided segment based on the cache.\n */ function InnerLayoutRouter(param) {\n    let { parallelRouterKey, url, childNodes, segmentPath, tree, // isActive,\n    cacheKey } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.GlobalLayoutRouterContext);\n    if (!context) {\n        throw new Error('invariant global layout router not mounted');\n    }\n    const { changeByServerResponse, tree: fullTree } = context;\n    // Read segment path from the parallel router cache node.\n    let childNode = childNodes.get(cacheKey);\n    // When data is not available during rendering client-side we need to fetch\n    // it from the server.\n    if (childNode === undefined) {\n        const newLazyCacheNode = {\n            lazyData: null,\n            rsc: null,\n            prefetchRsc: null,\n            head: null,\n            prefetchHead: null,\n            parallelRoutes: new Map(),\n            loading: null\n        };\n        /**\n     * Flight data fetch kicked off during render and put into the cache.\n     */ childNode = newLazyCacheNode;\n        childNodes.set(cacheKey, newLazyCacheNode);\n    }\n    // `rsc` represents the renderable node for this segment.\n    // If this segment has a `prefetchRsc`, it's the statically prefetched data.\n    // We should use that on initial render instead of `rsc`. Then we'll switch\n    // to `rsc` when the dynamic response streams in.\n    //\n    // If no prefetch data is available, then we go straight to rendering `rsc`.\n    const resolvedPrefetchRsc = childNode.prefetchRsc !== null ? childNode.prefetchRsc : childNode.rsc;\n    // We use `useDeferredValue` to handle switching between the prefetched and\n    // final values. The second argument is returned on initial render, then it\n    // re-renders with the first argument.\n    //\n    // @ts-expect-error The second argument to `useDeferredValue` is only\n    // available in the experimental builds. When its disabled, it will always\n    // return `rsc`.\n    const rsc = (0, _react.useDeferredValue)(childNode.rsc, resolvedPrefetchRsc);\n    // `rsc` is either a React node or a promise for a React node, except we\n    // special case `null` to represent that this segment's data is missing. If\n    // it's a promise, we need to unwrap it so we can determine whether or not the\n    // data is missing.\n    const resolvedRsc = typeof rsc === 'object' && rsc !== null && typeof rsc.then === 'function' ? (0, _react.use)(rsc) : rsc;\n    if (!resolvedRsc) {\n        // The data for this segment is not available, and there's no pending\n        // navigation that will be able to fulfill it. We need to fetch more from\n        // the server and patch the cache.\n        // Check if there's already a pending request.\n        let lazyData = childNode.lazyData;\n        if (lazyData === null) {\n            /**\n       * Router state with refetch marker added\n       */ // TODO-APP: remove ''\n            const refetchTree = walkAddRefetch([\n                '',\n                ...segmentPath\n            ], fullTree);\n            const includeNextUrl = (0, _hasinterceptionrouteincurrenttree.hasInterceptionRouteInCurrentTree)(fullTree);\n            childNode.lazyData = lazyData = (0, _fetchserverresponse.fetchServerResponse)(new URL(url, location.origin), {\n                flightRouterState: refetchTree,\n                nextUrl: includeNextUrl ? context.nextUrl : null\n            }).then((serverResponse)=>{\n                (0, _react.startTransition)(()=>{\n                    changeByServerResponse({\n                        previousTree: fullTree,\n                        serverResponse\n                    });\n                });\n                return serverResponse;\n            });\n        }\n        // Suspend infinitely as `changeByServerResponse` will cause a different part of the tree to be rendered.\n        // A falsey `resolvedRsc` indicates missing data -- we should not commit that branch, and we need to wait for the data to arrive.\n        (0, _react.use)(_unresolvedthenable.unresolvedThenable);\n    }\n    // If we get to this point, then we know we have something we can render.\n    const subtree = /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.LayoutRouterContext.Provider, {\n        value: {\n            tree: tree[1][parallelRouterKey],\n            childNodes: childNode.parallelRoutes,\n            // TODO-APP: overriding of url for parallel routes\n            url: url,\n            loading: childNode.loading\n        },\n        children: resolvedRsc\n    });\n    // Ensure root layout is not wrapped in a div as the root layout renders `<html>`\n    return subtree;\n}\n_c1 = InnerLayoutRouter;\n/**\n * Renders suspense boundary with the provided \"loading\" property as the fallback.\n * If no loading property is provided it renders the children without a suspense boundary.\n */ function LoadingBoundary(param) {\n    let { loading, children } = param;\n    // If loading is a promise, unwrap it. This happens in cases where we haven't\n    // yet received the loading data from the server — which includes whether or\n    // not this layout has a loading component at all.\n    //\n    // It's OK to suspend here instead of inside the fallback because this\n    // promise will resolve simultaneously with the data for the segment itself.\n    // So it will never suspend for longer than it would have if we didn't use\n    // a Suspense fallback at all.\n    let loadingModuleData;\n    if (typeof loading === 'object' && loading !== null && typeof loading.then === 'function') {\n        const promiseForLoading = loading;\n        loadingModuleData = (0, _react.use)(promiseForLoading);\n    } else {\n        loadingModuleData = loading;\n    }\n    if (loadingModuleData) {\n        const loadingRsc = loadingModuleData[0];\n        const loadingStyles = loadingModuleData[1];\n        const loadingScripts = loadingModuleData[2];\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.Suspense, {\n            fallback: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    loadingStyles,\n                    loadingScripts,\n                    loadingRsc\n                ]\n            }),\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: children\n    });\n}\n_c2 = LoadingBoundary;\nfunction OuterLayoutRouter(param) {\n    let { parallelRouterKey, segmentPath, error, errorStyles, errorScripts, templateStyles, templateScripts, template, notFound, forbidden, unauthorized } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.LayoutRouterContext);\n    if (!context) {\n        throw new Error('invariant expected layout router to be mounted');\n    }\n    const { childNodes, tree, url, loading } = context;\n    // Get the current parallelRouter cache node\n    let childNodesForParallelRouter = childNodes.get(parallelRouterKey);\n    // If the parallel router cache node does not exist yet, create it.\n    // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n    if (!childNodesForParallelRouter) {\n        childNodesForParallelRouter = new Map();\n        childNodes.set(parallelRouterKey, childNodesForParallelRouter);\n    }\n    // Get the active segment in the tree\n    // The reason arrays are used in the data format is that these are transferred from the server to the browser so it's optimized to save bytes.\n    const treeSegment = tree[1][parallelRouterKey][0];\n    // If segment is an array it's a dynamic route and we want to read the dynamic route value as the segment to get from the cache.\n    const currentChildSegmentValue = (0, _getsegmentvalue.getSegmentValue)(treeSegment);\n    /**\n   * Decides which segments to keep rendering, all segments that are not active will be wrapped in `<Offscreen>`.\n   */ // TODO-APP: Add handling of `<Offscreen>` when it's available.\n    const preservedSegments = [\n        treeSegment\n    ];\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: preservedSegments.map((preservedSegment)=>{\n            const preservedSegmentValue = (0, _getsegmentvalue.getSegmentValue)(preservedSegment);\n            const cacheKey = (0, _createroutercachekey.createRouterCacheKey)(preservedSegment);\n            return(/*\n            - Error boundary\n              - Only renders error boundary if error component is provided.\n              - Rendered for each segment to ensure they have their own error state.\n            - Loading boundary\n              - Only renders suspense boundary if loading components is provided.\n              - Rendered for each segment to ensure they have their own loading state.\n              - Passed to the router during rendering to ensure it can be immediately rendered when suspending on a Flight fetch.\n          */ /*#__PURE__*/ (0, _jsxruntime.jsxs)(_approutercontextsharedruntime.TemplateContext.Provider, {\n                value: /*#__PURE__*/ (0, _jsxruntime.jsx)(ScrollAndFocusHandler, {\n                    segmentPath: segmentPath,\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary.ErrorBoundary, {\n                        errorComponent: error,\n                        errorStyles: errorStyles,\n                        errorScripts: errorScripts,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(LoadingBoundary, {\n                            loading: loading,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary1.HTTPAccessFallbackBoundary, {\n                                notFound: notFound,\n                                forbidden: forbidden,\n                                unauthorized: unauthorized,\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_redirectboundary.RedirectBoundary, {\n                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(InnerLayoutRouter, {\n                                        parallelRouterKey: parallelRouterKey,\n                                        url: url,\n                                        tree: tree,\n                                        childNodes: childNodesForParallelRouter,\n                                        segmentPath: segmentPath,\n                                        cacheKey: cacheKey,\n                                        isActive: currentChildSegmentValue === preservedSegmentValue\n                                    })\n                                })\n                            })\n                        })\n                    })\n                }),\n                children: [\n                    templateStyles,\n                    templateScripts,\n                    template\n                ]\n            }, (0, _createroutercachekey.createRouterCacheKey)(preservedSegment, true)));\n        })\n    });\n}\n_c3 = OuterLayoutRouter;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=layout-router.js.map\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScrollAndFocusHandler\");\n$RefreshReg$(_c1, \"InnerLayoutRouter\");\n$RefreshReg$(_c2, \"LoadingBoundary\");\n$RefreshReg$(_c3, \"OuterLayoutRouter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/render-from-template-context.js ***!
  \**********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return RenderFromTemplateContext;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nfunction RenderFromTemplateContext() {\n    const children = (0, _react.useContext)(_approutercontextsharedruntime.TemplateContext);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: children\n    });\n}\n_c = RenderFromTemplateContext;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=render-from-template-context.js.map\nvar _c;\n$RefreshReg$(_c, \"RenderFromTemplateContext\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7OzJDQUtBOzs7ZUFBd0JBOzs7Ozs2RUFIb0I7MkRBQ1o7QUFFakI7SUFDYixNQUFNQyxXQUFXQyxDQUFBQSxHQUFBQSxPQUFBQSxVQUFBQSxFQUFXQywrQkFBQUEsZUFBZTtJQUMzQyxxQkFBTztrQkFBR0Y7O0FBQ1o7S0FId0JEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEpveWRldkhhbGRlclxcRGVza3RvcFxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlQ29udGV4dCwgdHlwZSBKU1ggfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IFRlbXBsYXRlQ29udGV4dCB9IGZyb20gJy4uLy4uL3NoYXJlZC9saWIvYXBwLXJvdXRlci1jb250ZXh0LnNoYXJlZC1ydW50aW1lJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSZW5kZXJGcm9tVGVtcGxhdGVDb250ZXh0KCk6IEpTWC5FbGVtZW50IHtcbiAgY29uc3QgY2hpbGRyZW4gPSB1c2VDb250ZXh0KFRlbXBsYXRlQ29udGV4dClcbiAgcmV0dXJuIDw+e2NoaWxkcmVufTwvPlxufVxuIl0sIm5hbWVzIjpbIlJlbmRlckZyb21UZW1wbGF0ZUNvbnRleHQiLCJjaGlsZHJlbiIsInVzZUNvbnRleHQiLCJUZW1wbGF0ZUNvbnRleHQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/invariant-error.js ***!
  \**************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"InvariantError\", ({\n    enumerable: true,\n    get: function() {\n        return InvariantError;\n    }\n}));\nclass InvariantError extends Error {\n    constructor(message, options){\n        super(\"Invariant: \" + (message.endsWith('.') ? message : message + '.') + \" This is a bug in Next.js.\", options);\n        this.name = 'InvariantError';\n    }\n} //# sourceMappingURL=invariant-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9pbnZhcmlhbnQtZXJyb3IuanMiLCJtYXBwaW5ncyI6Ijs7OztrREFBYUE7OztlQUFBQTs7O0FBQU4sTUFBTUEsdUJBQXVCQztJQUNsQ0MsWUFBWUMsT0FBZSxFQUFFQyxPQUFzQixDQUFFO1FBQ25ELEtBQUssQ0FDRixnQkFBYUQsQ0FBQUEsUUFBUUUsUUFBUSxDQUFDLE9BQU9GLFVBQVVBLFVBQVUsSUFBRSxHQUFFLDhCQUM5REM7UUFFRixJQUFJLENBQUNFLElBQUksR0FBRztJQUNkO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSm95ZGV2SGFsZGVyXFxEZXNrdG9wXFxzcmNcXHNoYXJlZFxcbGliXFxpbnZhcmlhbnQtZXJyb3IudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNsYXNzIEludmFyaWFudEVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICBjb25zdHJ1Y3RvcihtZXNzYWdlOiBzdHJpbmcsIG9wdGlvbnM/OiBFcnJvck9wdGlvbnMpIHtcbiAgICBzdXBlcihcbiAgICAgIGBJbnZhcmlhbnQ6ICR7bWVzc2FnZS5lbmRzV2l0aCgnLicpID8gbWVzc2FnZSA6IG1lc3NhZ2UgKyAnLid9IFRoaXMgaXMgYSBidWcgaW4gTmV4dC5qcy5gLFxuICAgICAgb3B0aW9uc1xuICAgIClcbiAgICB0aGlzLm5hbWUgPSAnSW52YXJpYW50RXJyb3InXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJJbnZhcmlhbnRFcnJvciIsIkVycm9yIiwiY29uc3RydWN0b3IiLCJtZXNzYWdlIiwib3B0aW9ucyIsImVuZHNXaXRoIiwibmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/invariant-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js ***!
  \********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * Run function with `scroll-behavior: auto` applied to `<html/>`.\n * This css change will be reverted after the function finishes.\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"handleSmoothScroll\", ({\n    enumerable: true,\n    get: function() {\n        return handleSmoothScroll;\n    }\n}));\nfunction handleSmoothScroll(fn, options) {\n    if (options === void 0) options = {};\n    // if only the hash is changed, we don't need to disable smooth scrolling\n    // we only care to prevent smooth scrolling when navigating to a new page to avoid jarring UX\n    if (options.onlyHashChange) {\n        fn();\n        return;\n    }\n    const htmlElement = document.documentElement;\n    const existing = htmlElement.style.scrollBehavior;\n    htmlElement.style.scrollBehavior = 'auto';\n    if (!options.dontForceLayout) {\n        // In Chrome-based browsers we need to force reflow before calling `scrollTo`.\n        // Otherwise it will not pickup the change in scrollBehavior\n        // More info here: https://github.com/vercel/next.js/issues/40719#issuecomment-1336248042\n        htmlElement.getClientRects();\n    }\n    fn();\n    htmlElement.style.scrollBehavior = existing;\n} //# sourceMappingURL=handle-smooth-scroll.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJoydevHalder%5C%5CDesktop%5C%5CReact%20Work%5C%5CStrapi_Project%5C%5CFrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);