import {
  MagicLinkCE,
  SelectRoles
} from "./chunk-ZJEDGGGS.js";
import {
  useEnterprise
} from "./chunk-HZKRK7AR.js";
import {
  SearchInput
} from "./chunk-BN2UQHMJ.js";
import {
  ConfirmDialog
} from "./chunk-NWWGC2Z2.js";
import {
  errorsTrads
} from "./chunk-IFOFBKTA.js";
import {
  create4 as create,
  create6 as create2,
  create7 as create3
} from "./chunk-376QHLWZ.js";
import {
  getDisplayName
} from "./chunk-PVNXTKO6.js";
import {
  Filters
} from "./chunk-DWSGKQEK.js";
import {
  Pagination
} from "./chunk-W6ZGCRX6.js";
import {
  Table
} from "./chunk-PVCRV2LE.js";
import {
  MemoizedInputRenderer
} from "./chunk-HWAQQGJJ.js";
import {
  Form
} from "./chunk-L5JCPKMP.js";
import {
  useRBAC
} from "./chunk-ZJEMJY2Q.js";
import {
  useAdminUsers,
  useCreateUserMutation,
  useDeleteManyUsersMutation
} from "./chunk-6DRYEU2W.js";
import {
  Layouts
} from "./chunk-MTTHLNPH.js";
import {
  Page,
  useAPIErrorHandler
} from "./chunk-7LKLOY7A.js";
import {
  useTypedSelector
} from "./chunk-ZOP4VV6J.js";
import {
  isBaseQueryError,
  require_lib
} from "./chunk-WH6VCVXU.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  Box,
  Breadcrumbs,
  Button,
  Crumb,
  Dialog,
  Flex,
  Grid,
  IconButton,
  Modal,
  Status,
  Typography,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  NavLink,
  useLocation,
  useNavigate
} from "./chunk-S65ZWNEO.js";
import {
  ForwardRef$1v,
  ForwardRef$2d,
  ForwardRef$j
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Users/<USER>
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var React3 = __toESM(require_react(), 1);
var qs = __toESM(require_lib(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Users/<USER>/CreateActionCE.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var CreateActionCE = React.forwardRef((props, ref) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsx)(Button, {
    ref,
    startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$2d, {}),
    size: "S",
    ...props,
    children: formatMessage({
      id: "Settings.permissions.users.create",
      defaultMessage: "Invite new user"
    })
  });
});

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Users/<USER>/NewUserForm.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var React2 = __toESM(require_react(), 1);
var ModalForm = ({ onToggle }) => {
  const [currentStep, setStep] = React2.useState("create");
  const [registrationToken, setRegistrationToken] = React2.useState("");
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError, _unstableFormatValidationErrors: formatValidationErrors } = useAPIErrorHandler();
  const roleLayout = useEnterprise(ROLE_LAYOUT, async () => (await import("./ModalForm-5HYECZO6.js")).ROLE_LAYOUT, {
    combine(ceRoles, eeRoles) {
      return [
        ...ceRoles,
        ...eeRoles
      ];
    },
    defaultValue: []
  });
  const initialValues = useEnterprise(FORM_INITIAL_VALUES, async () => (await import("./ModalForm-5HYECZO6.js")).FORM_INITIAL_VALUES, {
    combine(ceValues, eeValues) {
      return {
        ...ceValues,
        ...eeValues
      };
    },
    defaultValue: FORM_INITIAL_VALUES
  });
  const MagicLink = useEnterprise(MagicLinkCE, async () => (await import("./MagicLinkEE-L6L3227A.js")).MagicLinkEE);
  const [createUser] = useCreateUserMutation();
  const headerTitle = formatMessage({
    id: "Settings.permissions.users.create",
    defaultMessage: "Invite new user"
  });
  const handleSubmit = async (body, { setErrors }) => {
    const res = await createUser({
      ...body,
      roles: body.roles ?? []
    });
    if ("data" in res) {
      if (res.data.registrationToken) {
        setRegistrationToken(res.data.registrationToken);
      }
      goNext();
    } else {
      toggleNotification({
        type: "danger",
        message: formatAPIError(res.error)
      });
      if (isBaseQueryError(res.error) && res.error.name === "ValidationError") {
        setErrors(formatValidationErrors(res.error));
      }
    }
  };
  const goNext = () => {
    if (next) {
      setStep(next);
    } else {
      onToggle();
    }
  };
  const { buttonSubmitLabel, isDisabled, next } = STEPPER[currentStep];
  if (!MagicLink) {
    return null;
  }
  return (0, import_jsx_runtime2.jsx)(Modal.Root, {
    defaultOpen: true,
    onOpenChange: onToggle,
    children: (0, import_jsx_runtime2.jsxs)(Modal.Content, {
      children: [
        (0, import_jsx_runtime2.jsx)(Modal.Header, {
          children: (0, import_jsx_runtime2.jsx)(Breadcrumbs, {
            label: headerTitle,
            children: (0, import_jsx_runtime2.jsx)(Crumb, {
              isCurrent: true,
              children: headerTitle
            })
          })
        }),
        (0, import_jsx_runtime2.jsx)(Form, {
          method: currentStep === "create" ? "POST" : "PUT",
          initialValues: initialValues ?? {},
          onSubmit: handleSubmit,
          validationSchema: FORM_SCHEMA,
          children: ({ isSubmitting }) => {
            return (0, import_jsx_runtime2.jsxs)(import_jsx_runtime2.Fragment, {
              children: [
                (0, import_jsx_runtime2.jsx)(Modal.Body, {
                  children: (0, import_jsx_runtime2.jsxs)(Flex, {
                    direction: "column",
                    alignItems: "stretch",
                    gap: 6,
                    children: [
                      currentStep !== "create" && (0, import_jsx_runtime2.jsx)(MagicLink, {
                        registrationToken
                      }),
                      (0, import_jsx_runtime2.jsxs)(Box, {
                        children: [
                          (0, import_jsx_runtime2.jsx)(Typography, {
                            variant: "beta",
                            tag: "h2",
                            children: formatMessage({
                              id: "app.components.Users.ModalCreateBody.block-title.details",
                              defaultMessage: "User details"
                            })
                          }),
                          (0, import_jsx_runtime2.jsx)(Box, {
                            paddingTop: 4,
                            children: (0, import_jsx_runtime2.jsx)(Flex, {
                              direction: "column",
                              alignItems: "stretch",
                              gap: 1,
                              children: (0, import_jsx_runtime2.jsx)(Grid.Root, {
                                gap: 5,
                                children: FORM_LAYOUT.map((row) => {
                                  return row.map(({ size, ...field }) => {
                                    return (0, import_jsx_runtime2.jsx)(Grid.Item, {
                                      col: size,
                                      direction: "column",
                                      alignItems: "stretch",
                                      children: (0, import_jsx_runtime2.jsx)(MemoizedInputRenderer, {
                                        ...field,
                                        disabled: isDisabled,
                                        label: formatMessage(field.label),
                                        placeholder: formatMessage(field.placeholder)
                                      })
                                    }, field.name);
                                  });
                                })
                              })
                            })
                          })
                        ]
                      }),
                      (0, import_jsx_runtime2.jsxs)(Box, {
                        children: [
                          (0, import_jsx_runtime2.jsx)(Typography, {
                            variant: "beta",
                            tag: "h2",
                            children: formatMessage({
                              id: "global.roles",
                              defaultMessage: "User's role"
                            })
                          }),
                          (0, import_jsx_runtime2.jsx)(Box, {
                            paddingTop: 4,
                            children: (0, import_jsx_runtime2.jsxs)(Grid.Root, {
                              gap: 5,
                              children: [
                                (0, import_jsx_runtime2.jsx)(Grid.Item, {
                                  col: 6,
                                  xs: 12,
                                  direction: "column",
                                  alignItems: "stretch",
                                  children: (0, import_jsx_runtime2.jsx)(SelectRoles, {
                                    disabled: isDisabled
                                  })
                                }),
                                roleLayout.map((row) => {
                                  return row.map(({ size, ...field }) => {
                                    return (0, import_jsx_runtime2.jsx)(Grid.Item, {
                                      col: size,
                                      direction: "column",
                                      alignItems: "stretch",
                                      children: (0, import_jsx_runtime2.jsx)(MemoizedInputRenderer, {
                                        ...field,
                                        disabled: isDisabled,
                                        label: formatMessage(field.label),
                                        placeholder: field.placeholder ? formatMessage(field.placeholder) : void 0,
                                        hint: field.hint ? formatMessage(field.hint) : void 0
                                      })
                                    }, field.name);
                                  });
                                })
                              ]
                            })
                          })
                        ]
                      })
                    ]
                  })
                }),
                (0, import_jsx_runtime2.jsxs)(Modal.Footer, {
                  children: [
                    (0, import_jsx_runtime2.jsx)(Button, {
                      variant: "tertiary",
                      onClick: onToggle,
                      type: "button",
                      children: formatMessage({
                        id: "app.components.Button.cancel",
                        defaultMessage: "Cancel"
                      })
                    }),
                    currentStep === "create" ? (0, import_jsx_runtime2.jsx)(Button, {
                      type: "submit",
                      loading: isSubmitting,
                      children: formatMessage(buttonSubmitLabel)
                    }) : (0, import_jsx_runtime2.jsx)(Button, {
                      type: "button",
                      loading: isSubmitting,
                      onClick: onToggle,
                      children: formatMessage(buttonSubmitLabel)
                    })
                  ]
                })
              ]
            });
          }
        })
      ]
    })
  });
};
var FORM_INITIAL_VALUES = {
  firstname: "",
  lastname: "",
  email: "",
  roles: []
};
var ROLE_LAYOUT = [];
var FORM_LAYOUT = [
  [
    {
      label: {
        id: "Auth.form.firstname.label",
        defaultMessage: "First name"
      },
      name: "firstname",
      placeholder: {
        id: "Auth.form.firstname.placeholder",
        defaultMessage: "e.g. Kai"
      },
      type: "string",
      size: 6,
      required: true
    },
    {
      label: {
        id: "Auth.form.lastname.label",
        defaultMessage: "Last name"
      },
      name: "lastname",
      placeholder: {
        id: "Auth.form.lastname.placeholder",
        defaultMessage: "e.g. Doe"
      },
      type: "string",
      size: 6
    }
  ],
  [
    {
      label: {
        id: "Auth.form.email.label",
        defaultMessage: "Email"
      },
      name: "email",
      placeholder: {
        id: "Auth.form.email.placeholder",
        defaultMessage: "e.g. <EMAIL>"
      },
      type: "email",
      size: 6,
      required: true
    }
  ]
];
var FORM_SCHEMA = create2().shape({
  firstname: create().trim().required({
    id: errorsTrads.required.id,
    defaultMessage: "This field is required"
  }).nullable(),
  lastname: create(),
  email: create().email(errorsTrads.email).required({
    id: errorsTrads.required.id,
    defaultMessage: "This field is required"
  }).nullable(),
  roles: create3().min(1, {
    id: errorsTrads.required.id,
    defaultMessage: "This field is required"
  }).required({
    id: errorsTrads.required.id,
    defaultMessage: "This field is required"
  })
});
var STEPPER = {
  create: {
    buttonSubmitLabel: {
      id: "app.containers.Users.ModalForm.footer.button-success",
      defaultMessage: "Invite user"
    },
    isDisabled: false,
    next: "magic-link"
  },
  "magic-link": {
    buttonSubmitLabel: {
      id: "global.finish",
      defaultMessage: "Finish"
    },
    isDisabled: true,
    next: null
  }
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Users/<USER>
var ListPageCE = () => {
  var _a;
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const [isModalOpened, setIsModalOpen] = React3.useState(false);
  const permissions = useTypedSelector((state) => state.admin_app.permissions);
  const { allowedActions: { canCreate, canDelete, canRead } } = useRBAC((_a = permissions.settings) == null ? void 0 : _a.users);
  const navigate = useNavigate();
  const { toggleNotification } = useNotification();
  const { formatMessage } = useIntl();
  const { search } = useLocation();
  const [showDeleteConfirmation, setShowDeleteConfirmation] = React3.useState(false);
  const [idsToDelete, setIdsToDelete] = React3.useState([]);
  const { data, isError, isLoading } = useAdminUsers(qs.parse(search, {
    ignoreQueryPrefix: true
  }));
  const { pagination, users = [] } = data ?? {};
  const CreateAction = useEnterprise(CreateActionCE, async () => (await import("./CreateActionEE-4AKX3SMO.js")).CreateActionEE);
  const headers = TABLE_HEADERS.map((header) => ({
    ...header,
    label: formatMessage(header.label)
  }));
  const title = formatMessage({
    id: "global.users",
    defaultMessage: "Users"
  });
  const handleToggle = () => {
    setIsModalOpen((prev) => !prev);
  };
  const [deleteAll] = useDeleteManyUsersMutation();
  const handleDeleteAll = async (ids) => {
    try {
      const res = await deleteAll({
        ids
      });
      if ("error" in res) {
        toggleNotification({
          type: "danger",
          message: formatAPIError(res.error)
        });
      }
    } catch (err) {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "global.error",
          defaultMessage: "An error occurred"
        })
      });
    }
  };
  const handleRowClick = (id) => () => {
    if (canRead) {
      navigate(id.toString());
    }
  };
  const handleDeleteClick = (id) => async () => {
    setIdsToDelete([
      id
    ]);
    setShowDeleteConfirmation(true);
  };
  const confirmDelete = async () => {
    await handleDeleteAll(idsToDelete);
    setShowDeleteConfirmation(false);
  };
  if (!CreateAction) {
    return null;
  }
  if (isError) {
    return (0, import_jsx_runtime3.jsx)(Page.Error, {});
  }
  return (0, import_jsx_runtime3.jsxs)(Page.Main, {
    "aria-busy": isLoading,
    children: [
      (0, import_jsx_runtime3.jsx)(Page.Title, {
        children: formatMessage({
          id: "Settings.PageTitle",
          defaultMessage: "Settings - {name}"
        }, {
          name: "Users"
        })
      }),
      (0, import_jsx_runtime3.jsx)(Layouts.Header, {
        primaryAction: canCreate && (0, import_jsx_runtime3.jsx)(CreateAction, {
          onClick: handleToggle
        }),
        title,
        subtitle: formatMessage({
          id: "Settings.permissions.users.listview.header.subtitle",
          defaultMessage: "All the users who have access to the Strapi admin panel"
        })
      }),
      (0, import_jsx_runtime3.jsx)(Layouts.Action, {
        startActions: (0, import_jsx_runtime3.jsxs)(import_jsx_runtime3.Fragment, {
          children: [
            (0, import_jsx_runtime3.jsx)(SearchInput, {
              label: formatMessage({
                id: "app.component.search.label",
                defaultMessage: "Search for {target}"
              }, {
                target: title
              })
            }),
            (0, import_jsx_runtime3.jsxs)(Filters.Root, {
              options: FILTERS,
              children: [
                (0, import_jsx_runtime3.jsx)(Filters.Trigger, {}),
                (0, import_jsx_runtime3.jsx)(Filters.Popover, {}),
                (0, import_jsx_runtime3.jsx)(Filters.List, {})
              ]
            })
          ]
        })
      }),
      (0, import_jsx_runtime3.jsxs)(Layouts.Content, {
        children: [
          (0, import_jsx_runtime3.jsxs)(Table.Root, {
            rows: users,
            headers,
            children: [
              (0, import_jsx_runtime3.jsx)(Table.ActionBar, {}),
              (0, import_jsx_runtime3.jsxs)(Table.Content, {
                children: [
                  (0, import_jsx_runtime3.jsxs)(Table.Head, {
                    children: [
                      canDelete ? (0, import_jsx_runtime3.jsx)(Table.HeaderCheckboxCell, {}) : null,
                      headers.map((header) => (0, import_jsx_runtime3.jsx)(Table.HeaderCell, {
                        ...header
                      }, header.name))
                    ]
                  }),
                  (0, import_jsx_runtime3.jsx)(Table.Empty, {}),
                  (0, import_jsx_runtime3.jsx)(Table.Loading, {}),
                  (0, import_jsx_runtime3.jsx)(Table.Body, {
                    children: users.map((user) => (0, import_jsx_runtime3.jsxs)(Table.Row, {
                      onClick: handleRowClick(user.id),
                      cursor: canRead ? "pointer" : "default",
                      children: [
                        canDelete ? (0, import_jsx_runtime3.jsx)(Table.CheckboxCell, {
                          id: user.id
                        }) : null,
                        headers.map(({ cellFormatter, name, ...rest }) => {
                          return (0, import_jsx_runtime3.jsx)(Table.Cell, {
                            children: typeof cellFormatter === "function" ? cellFormatter(user, {
                              name,
                              ...rest
                            }) : (
                              // @ts-expect-error – name === "roles" has the data value of `AdminRole[]` but the header has a cellFormatter value so this shouldn't be called.
                              (0, import_jsx_runtime3.jsx)(Typography, {
                                textColor: "neutral800",
                                children: user[name] || "-"
                              })
                            )
                          }, name);
                        }),
                        canRead || canDelete ? (0, import_jsx_runtime3.jsx)(Table.Cell, {
                          onClick: (e) => e.stopPropagation(),
                          children: (0, import_jsx_runtime3.jsxs)(Flex, {
                            justifyContent: "end",
                            children: [
                              canRead ? (0, import_jsx_runtime3.jsx)(IconButton, {
                                tag: NavLink,
                                to: user.id.toString(),
                                label: formatMessage({
                                  id: "app.component.table.edit",
                                  defaultMessage: "Edit {target}"
                                }, {
                                  target: getDisplayName(user)
                                }),
                                variant: "ghost",
                                children: (0, import_jsx_runtime3.jsx)(ForwardRef$1v, {})
                              }) : null,
                              canDelete ? (0, import_jsx_runtime3.jsx)(IconButton, {
                                onClick: handleDeleteClick(user.id),
                                label: formatMessage({
                                  id: "global.delete-target",
                                  defaultMessage: "Delete {target}"
                                }, {
                                  target: getDisplayName(user)
                                }),
                                variant: "ghost",
                                children: (0, import_jsx_runtime3.jsx)(ForwardRef$j, {})
                              }) : null
                            ]
                          })
                        }) : null
                      ]
                    }, user.id))
                  })
                ]
              })
            ]
          }),
          (0, import_jsx_runtime3.jsxs)(Pagination.Root, {
            ...pagination,
            children: [
              (0, import_jsx_runtime3.jsx)(Pagination.PageSize, {}),
              (0, import_jsx_runtime3.jsx)(Pagination.Links, {})
            ]
          })
        ]
      }),
      isModalOpened && (0, import_jsx_runtime3.jsx)(ModalForm, {
        onToggle: handleToggle
      }),
      (0, import_jsx_runtime3.jsx)(Dialog.Root, {
        open: showDeleteConfirmation,
        onOpenChange: setShowDeleteConfirmation,
        children: (0, import_jsx_runtime3.jsx)(ConfirmDialog, {
          onConfirm: confirmDelete
        })
      })
    ]
  });
};
var TABLE_HEADERS = [
  {
    name: "firstname",
    label: {
      id: "Settings.permissions.users.firstname",
      defaultMessage: "Firstname"
    },
    sortable: true
  },
  {
    name: "lastname",
    label: {
      id: "Settings.permissions.users.lastname",
      defaultMessage: "Lastname"
    },
    sortable: true
  },
  {
    name: "email",
    label: {
      id: "Settings.permissions.users.email",
      defaultMessage: "Email"
    },
    sortable: true
  },
  {
    name: "roles",
    label: {
      id: "Settings.permissions.users.roles",
      defaultMessage: "Roles"
    },
    sortable: false,
    cellFormatter({ roles }) {
      return (0, import_jsx_runtime3.jsx)(Typography, {
        textColor: "neutral800",
        children: roles.map((role) => role.name).join(",\n")
      });
    }
  },
  {
    name: "username",
    label: {
      id: "Settings.permissions.users.username",
      defaultMessage: "Username"
    },
    sortable: true
  },
  {
    name: "isActive",
    label: {
      id: "Settings.permissions.users.user-status",
      defaultMessage: "User status"
    },
    sortable: false,
    cellFormatter({ isActive }) {
      return (0, import_jsx_runtime3.jsx)(Flex, {
        children: (0, import_jsx_runtime3.jsx)(Status, {
          size: "S",
          borderWidth: 0,
          background: "transparent",
          color: "neutral800",
          variant: isActive ? "success" : "danger",
          children: (0, import_jsx_runtime3.jsx)(Typography, {
            children: isActive ? "Active" : "Inactive"
          })
        })
      });
    }
  }
];
var FILTERS = [
  {
    name: "firstname",
    label: "Firstname",
    type: "string"
  },
  {
    name: "lastname",
    label: "Lastname",
    type: "string"
  },
  {
    name: "email",
    label: "Email",
    type: "email"
  },
  {
    name: "username",
    label: "Username",
    type: "string"
  },
  {
    name: "isActive",
    label: "Active user",
    type: "boolean"
  }
];
var ListPage = () => {
  const UsersListPage = useEnterprise(ListPageCE, async () => (
    // eslint-disable-next-line import/no-cycle
    (await import("./ListPage-3OBJUK4I.js")).UserListPageEE
  ));
  if (!UsersListPage) {
    return null;
  }
  return (0, import_jsx_runtime3.jsx)(UsersListPage, {});
};
var ProtectedListPage = () => {
  const permissions = useTypedSelector((state) => {
    var _a;
    return (_a = state.admin_app.permissions.settings) == null ? void 0 : _a.users.read;
  });
  return (0, import_jsx_runtime3.jsx)(Page.Protect, {
    permissions,
    children: (0, import_jsx_runtime3.jsx)(ListPage, {})
  });
};

export {
  ListPageCE,
  ListPage,
  ProtectedListPage
};
//# sourceMappingURL=chunk-Z3LON2D4.js.map
