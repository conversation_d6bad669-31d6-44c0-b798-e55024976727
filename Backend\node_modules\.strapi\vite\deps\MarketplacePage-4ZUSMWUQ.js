import {
  require_compare,
  require_constants,
  require_debug,
  require_identifiers,
  require_lt,
  require_parse,
  require_parse_options,
  require_re,
  require_semver,
  require_valid
} from "./chunk-WAJBRAZM.js";
import {
  require_pluralize
} from "./chunk-XNACAI67.js";
import {
  img,
  useQuery
} from "./chunk-75D2ZJP5.js";
import "./chunk-MBK4V2X7.js";
import {
  useAppInfo,
  useTracking
} from "./chunk-XDCEA27D.js";
import "./chunk-EZSYDDUK.js";
import "./chunk-YXDCVYVT.js";
import {
  ContentBox,
  useClipboard
} from "./chunk-CJHUGFLE.js";
import {
  Pagination
} from "./chunk-W6ZGCRX6.js";
import {
  Layouts
} from "./chunk-MTTHLNPH.js";
import {
  ForwardRef$J,
  ForwardRef$v,
  ForwardRef$x,
  Page
} from "./chunk-7LKLOY7A.js";
import {
  useQueryParams
} from "./chunk-ODQFI753.js";
import {
  useTypedSelector
} from "./chunk-ZOP4VV6J.js";
import {
  require_lib
} from "./chunk-WH6VCVXU.js";
import "./chunk-IL5G2D22.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-76QM3EFM.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  Box,
  Button,
  Divider,
  Flex,
  Grid,
  LinkButton,
  Main,
  MultiSelect,
  MultiSelectOption,
  Popover,
  Searchbar,
  SingleSelect,
  SingleSelectOption,
  Tabs,
  Tag,
  TooltipImpl,
  Typography,
  useIntl,
  useNotifyAT
} from "./chunk-7GC3Y62Q.js";
import "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$3F,
  ForwardRef$3R,
  ForwardRef$3V,
  ForwardRef$3j,
  ForwardRef$45,
  ForwardRef$4D,
  ForwardRef$4F,
  ForwardRef$D,
  ForwardRef$d
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __commonJS,
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/semver/functions/clean.js
var require_clean = __commonJS({
  "node_modules/semver/functions/clean.js"(exports, module) {
    var parse = require_parse();
    var clean = (version, options) => {
      const s = parse(version.trim().replace(/^[=v]+/, ""), options);
      return s ? s.version : null;
    };
    module.exports = clean;
  }
});

// node_modules/semver/functions/inc.js
var require_inc = __commonJS({
  "node_modules/semver/functions/inc.js"(exports, module) {
    var SemVer = require_semver();
    var inc = (version, release, options, identifier, identifierBase) => {
      if (typeof options === "string") {
        identifierBase = identifier;
        identifier = options;
        options = void 0;
      }
      try {
        return new SemVer(
          version instanceof SemVer ? version.version : version,
          options
        ).inc(release, identifier, identifierBase).version;
      } catch (er) {
        return null;
      }
    };
    module.exports = inc;
  }
});

// node_modules/semver/functions/diff.js
var require_diff = __commonJS({
  "node_modules/semver/functions/diff.js"(exports, module) {
    var parse = require_parse();
    var diff = (version1, version2) => {
      const v1 = parse(version1, null, true);
      const v2 = parse(version2, null, true);
      const comparison = v1.compare(v2);
      if (comparison === 0) {
        return null;
      }
      const v1Higher = comparison > 0;
      const highVersion = v1Higher ? v1 : v2;
      const lowVersion = v1Higher ? v2 : v1;
      const highHasPre = !!highVersion.prerelease.length;
      const lowHasPre = !!lowVersion.prerelease.length;
      if (lowHasPre && !highHasPre) {
        if (!lowVersion.patch && !lowVersion.minor) {
          return "major";
        }
        if (highVersion.patch) {
          return "patch";
        }
        if (highVersion.minor) {
          return "minor";
        }
        return "major";
      }
      const prefix = highHasPre ? "pre" : "";
      if (v1.major !== v2.major) {
        return prefix + "major";
      }
      if (v1.minor !== v2.minor) {
        return prefix + "minor";
      }
      if (v1.patch !== v2.patch) {
        return prefix + "patch";
      }
      return "prerelease";
    };
    module.exports = diff;
  }
});

// node_modules/semver/functions/major.js
var require_major = __commonJS({
  "node_modules/semver/functions/major.js"(exports, module) {
    var SemVer = require_semver();
    var major = (a, loose) => new SemVer(a, loose).major;
    module.exports = major;
  }
});

// node_modules/semver/functions/minor.js
var require_minor = __commonJS({
  "node_modules/semver/functions/minor.js"(exports, module) {
    var SemVer = require_semver();
    var minor = (a, loose) => new SemVer(a, loose).minor;
    module.exports = minor;
  }
});

// node_modules/semver/functions/patch.js
var require_patch = __commonJS({
  "node_modules/semver/functions/patch.js"(exports, module) {
    var SemVer = require_semver();
    var patch = (a, loose) => new SemVer(a, loose).patch;
    module.exports = patch;
  }
});

// node_modules/semver/functions/prerelease.js
var require_prerelease = __commonJS({
  "node_modules/semver/functions/prerelease.js"(exports, module) {
    var parse = require_parse();
    var prerelease = (version, options) => {
      const parsed = parse(version, options);
      return parsed && parsed.prerelease.length ? parsed.prerelease : null;
    };
    module.exports = prerelease;
  }
});

// node_modules/semver/functions/rcompare.js
var require_rcompare = __commonJS({
  "node_modules/semver/functions/rcompare.js"(exports, module) {
    var compare = require_compare();
    var rcompare = (a, b, loose) => compare(b, a, loose);
    module.exports = rcompare;
  }
});

// node_modules/semver/functions/compare-loose.js
var require_compare_loose = __commonJS({
  "node_modules/semver/functions/compare-loose.js"(exports, module) {
    var compare = require_compare();
    var compareLoose = (a, b) => compare(a, b, true);
    module.exports = compareLoose;
  }
});

// node_modules/semver/functions/compare-build.js
var require_compare_build = __commonJS({
  "node_modules/semver/functions/compare-build.js"(exports, module) {
    var SemVer = require_semver();
    var compareBuild = (a, b, loose) => {
      const versionA = new SemVer(a, loose);
      const versionB = new SemVer(b, loose);
      return versionA.compare(versionB) || versionA.compareBuild(versionB);
    };
    module.exports = compareBuild;
  }
});

// node_modules/semver/functions/sort.js
var require_sort = __commonJS({
  "node_modules/semver/functions/sort.js"(exports, module) {
    var compareBuild = require_compare_build();
    var sort = (list, loose) => list.sort((a, b) => compareBuild(a, b, loose));
    module.exports = sort;
  }
});

// node_modules/semver/functions/rsort.js
var require_rsort = __commonJS({
  "node_modules/semver/functions/rsort.js"(exports, module) {
    var compareBuild = require_compare_build();
    var rsort = (list, loose) => list.sort((a, b) => compareBuild(b, a, loose));
    module.exports = rsort;
  }
});

// node_modules/semver/functions/gt.js
var require_gt = __commonJS({
  "node_modules/semver/functions/gt.js"(exports, module) {
    var compare = require_compare();
    var gt = (a, b, loose) => compare(a, b, loose) > 0;
    module.exports = gt;
  }
});

// node_modules/semver/functions/eq.js
var require_eq = __commonJS({
  "node_modules/semver/functions/eq.js"(exports, module) {
    var compare = require_compare();
    var eq = (a, b, loose) => compare(a, b, loose) === 0;
    module.exports = eq;
  }
});

// node_modules/semver/functions/neq.js
var require_neq = __commonJS({
  "node_modules/semver/functions/neq.js"(exports, module) {
    var compare = require_compare();
    var neq = (a, b, loose) => compare(a, b, loose) !== 0;
    module.exports = neq;
  }
});

// node_modules/semver/functions/gte.js
var require_gte = __commonJS({
  "node_modules/semver/functions/gte.js"(exports, module) {
    var compare = require_compare();
    var gte = (a, b, loose) => compare(a, b, loose) >= 0;
    module.exports = gte;
  }
});

// node_modules/semver/functions/lte.js
var require_lte = __commonJS({
  "node_modules/semver/functions/lte.js"(exports, module) {
    var compare = require_compare();
    var lte = (a, b, loose) => compare(a, b, loose) <= 0;
    module.exports = lte;
  }
});

// node_modules/semver/functions/cmp.js
var require_cmp = __commonJS({
  "node_modules/semver/functions/cmp.js"(exports, module) {
    var eq = require_eq();
    var neq = require_neq();
    var gt = require_gt();
    var gte = require_gte();
    var lt = require_lt();
    var lte = require_lte();
    var cmp = (a, op, b, loose) => {
      switch (op) {
        case "===":
          if (typeof a === "object") {
            a = a.version;
          }
          if (typeof b === "object") {
            b = b.version;
          }
          return a === b;
        case "!==":
          if (typeof a === "object") {
            a = a.version;
          }
          if (typeof b === "object") {
            b = b.version;
          }
          return a !== b;
        case "":
        case "=":
        case "==":
          return eq(a, b, loose);
        case "!=":
          return neq(a, b, loose);
        case ">":
          return gt(a, b, loose);
        case ">=":
          return gte(a, b, loose);
        case "<":
          return lt(a, b, loose);
        case "<=":
          return lte(a, b, loose);
        default:
          throw new TypeError(`Invalid operator: ${op}`);
      }
    };
    module.exports = cmp;
  }
});

// node_modules/semver/functions/coerce.js
var require_coerce = __commonJS({
  "node_modules/semver/functions/coerce.js"(exports, module) {
    var SemVer = require_semver();
    var parse = require_parse();
    var { safeRe: re, t } = require_re();
    var coerce = (version, options) => {
      if (version instanceof SemVer) {
        return version;
      }
      if (typeof version === "number") {
        version = String(version);
      }
      if (typeof version !== "string") {
        return null;
      }
      options = options || {};
      let match = null;
      if (!options.rtl) {
        match = version.match(re[t.COERCE]);
      } else {
        let next;
        while ((next = re[t.COERCERTL].exec(version)) && (!match || match.index + match[0].length !== version.length)) {
          if (!match || next.index + next[0].length !== match.index + match[0].length) {
            match = next;
          }
          re[t.COERCERTL].lastIndex = next.index + next[1].length + next[2].length;
        }
        re[t.COERCERTL].lastIndex = -1;
      }
      if (match === null) {
        return null;
      }
      return parse(`${match[2]}.${match[3] || "0"}.${match[4] || "0"}`, options);
    };
    module.exports = coerce;
  }
});

// node_modules/yallist/iterator.js
var require_iterator = __commonJS({
  "node_modules/yallist/iterator.js"(exports, module) {
    "use strict";
    module.exports = function(Yallist) {
      Yallist.prototype[Symbol.iterator] = function* () {
        for (let walker = this.head; walker; walker = walker.next) {
          yield walker.value;
        }
      };
    };
  }
});

// node_modules/yallist/yallist.js
var require_yallist = __commonJS({
  "node_modules/yallist/yallist.js"(exports, module) {
    "use strict";
    module.exports = Yallist;
    Yallist.Node = Node;
    Yallist.create = Yallist;
    function Yallist(list) {
      var self = this;
      if (!(self instanceof Yallist)) {
        self = new Yallist();
      }
      self.tail = null;
      self.head = null;
      self.length = 0;
      if (list && typeof list.forEach === "function") {
        list.forEach(function(item) {
          self.push(item);
        });
      } else if (arguments.length > 0) {
        for (var i = 0, l = arguments.length; i < l; i++) {
          self.push(arguments[i]);
        }
      }
      return self;
    }
    Yallist.prototype.removeNode = function(node) {
      if (node.list !== this) {
        throw new Error("removing node which does not belong to this list");
      }
      var next = node.next;
      var prev = node.prev;
      if (next) {
        next.prev = prev;
      }
      if (prev) {
        prev.next = next;
      }
      if (node === this.head) {
        this.head = next;
      }
      if (node === this.tail) {
        this.tail = prev;
      }
      node.list.length--;
      node.next = null;
      node.prev = null;
      node.list = null;
      return next;
    };
    Yallist.prototype.unshiftNode = function(node) {
      if (node === this.head) {
        return;
      }
      if (node.list) {
        node.list.removeNode(node);
      }
      var head = this.head;
      node.list = this;
      node.next = head;
      if (head) {
        head.prev = node;
      }
      this.head = node;
      if (!this.tail) {
        this.tail = node;
      }
      this.length++;
    };
    Yallist.prototype.pushNode = function(node) {
      if (node === this.tail) {
        return;
      }
      if (node.list) {
        node.list.removeNode(node);
      }
      var tail = this.tail;
      node.list = this;
      node.prev = tail;
      if (tail) {
        tail.next = node;
      }
      this.tail = node;
      if (!this.head) {
        this.head = node;
      }
      this.length++;
    };
    Yallist.prototype.push = function() {
      for (var i = 0, l = arguments.length; i < l; i++) {
        push(this, arguments[i]);
      }
      return this.length;
    };
    Yallist.prototype.unshift = function() {
      for (var i = 0, l = arguments.length; i < l; i++) {
        unshift(this, arguments[i]);
      }
      return this.length;
    };
    Yallist.prototype.pop = function() {
      if (!this.tail) {
        return void 0;
      }
      var res = this.tail.value;
      this.tail = this.tail.prev;
      if (this.tail) {
        this.tail.next = null;
      } else {
        this.head = null;
      }
      this.length--;
      return res;
    };
    Yallist.prototype.shift = function() {
      if (!this.head) {
        return void 0;
      }
      var res = this.head.value;
      this.head = this.head.next;
      if (this.head) {
        this.head.prev = null;
      } else {
        this.tail = null;
      }
      this.length--;
      return res;
    };
    Yallist.prototype.forEach = function(fn, thisp) {
      thisp = thisp || this;
      for (var walker = this.head, i = 0; walker !== null; i++) {
        fn.call(thisp, walker.value, i, this);
        walker = walker.next;
      }
    };
    Yallist.prototype.forEachReverse = function(fn, thisp) {
      thisp = thisp || this;
      for (var walker = this.tail, i = this.length - 1; walker !== null; i--) {
        fn.call(thisp, walker.value, i, this);
        walker = walker.prev;
      }
    };
    Yallist.prototype.get = function(n) {
      for (var i = 0, walker = this.head; walker !== null && i < n; i++) {
        walker = walker.next;
      }
      if (i === n && walker !== null) {
        return walker.value;
      }
    };
    Yallist.prototype.getReverse = function(n) {
      for (var i = 0, walker = this.tail; walker !== null && i < n; i++) {
        walker = walker.prev;
      }
      if (i === n && walker !== null) {
        return walker.value;
      }
    };
    Yallist.prototype.map = function(fn, thisp) {
      thisp = thisp || this;
      var res = new Yallist();
      for (var walker = this.head; walker !== null; ) {
        res.push(fn.call(thisp, walker.value, this));
        walker = walker.next;
      }
      return res;
    };
    Yallist.prototype.mapReverse = function(fn, thisp) {
      thisp = thisp || this;
      var res = new Yallist();
      for (var walker = this.tail; walker !== null; ) {
        res.push(fn.call(thisp, walker.value, this));
        walker = walker.prev;
      }
      return res;
    };
    Yallist.prototype.reduce = function(fn, initial) {
      var acc;
      var walker = this.head;
      if (arguments.length > 1) {
        acc = initial;
      } else if (this.head) {
        walker = this.head.next;
        acc = this.head.value;
      } else {
        throw new TypeError("Reduce of empty list with no initial value");
      }
      for (var i = 0; walker !== null; i++) {
        acc = fn(acc, walker.value, i);
        walker = walker.next;
      }
      return acc;
    };
    Yallist.prototype.reduceReverse = function(fn, initial) {
      var acc;
      var walker = this.tail;
      if (arguments.length > 1) {
        acc = initial;
      } else if (this.tail) {
        walker = this.tail.prev;
        acc = this.tail.value;
      } else {
        throw new TypeError("Reduce of empty list with no initial value");
      }
      for (var i = this.length - 1; walker !== null; i--) {
        acc = fn(acc, walker.value, i);
        walker = walker.prev;
      }
      return acc;
    };
    Yallist.prototype.toArray = function() {
      var arr = new Array(this.length);
      for (var i = 0, walker = this.head; walker !== null; i++) {
        arr[i] = walker.value;
        walker = walker.next;
      }
      return arr;
    };
    Yallist.prototype.toArrayReverse = function() {
      var arr = new Array(this.length);
      for (var i = 0, walker = this.tail; walker !== null; i++) {
        arr[i] = walker.value;
        walker = walker.prev;
      }
      return arr;
    };
    Yallist.prototype.slice = function(from, to) {
      to = to || this.length;
      if (to < 0) {
        to += this.length;
      }
      from = from || 0;
      if (from < 0) {
        from += this.length;
      }
      var ret = new Yallist();
      if (to < from || to < 0) {
        return ret;
      }
      if (from < 0) {
        from = 0;
      }
      if (to > this.length) {
        to = this.length;
      }
      for (var i = 0, walker = this.head; walker !== null && i < from; i++) {
        walker = walker.next;
      }
      for (; walker !== null && i < to; i++, walker = walker.next) {
        ret.push(walker.value);
      }
      return ret;
    };
    Yallist.prototype.sliceReverse = function(from, to) {
      to = to || this.length;
      if (to < 0) {
        to += this.length;
      }
      from = from || 0;
      if (from < 0) {
        from += this.length;
      }
      var ret = new Yallist();
      if (to < from || to < 0) {
        return ret;
      }
      if (from < 0) {
        from = 0;
      }
      if (to > this.length) {
        to = this.length;
      }
      for (var i = this.length, walker = this.tail; walker !== null && i > to; i--) {
        walker = walker.prev;
      }
      for (; walker !== null && i > from; i--, walker = walker.prev) {
        ret.push(walker.value);
      }
      return ret;
    };
    Yallist.prototype.splice = function(start, deleteCount, ...nodes) {
      if (start > this.length) {
        start = this.length - 1;
      }
      if (start < 0) {
        start = this.length + start;
      }
      for (var i = 0, walker = this.head; walker !== null && i < start; i++) {
        walker = walker.next;
      }
      var ret = [];
      for (var i = 0; walker && i < deleteCount; i++) {
        ret.push(walker.value);
        walker = this.removeNode(walker);
      }
      if (walker === null) {
        walker = this.tail;
      }
      if (walker !== this.head && walker !== this.tail) {
        walker = walker.prev;
      }
      for (var i = 0; i < nodes.length; i++) {
        walker = insert(this, walker, nodes[i]);
      }
      return ret;
    };
    Yallist.prototype.reverse = function() {
      var head = this.head;
      var tail = this.tail;
      for (var walker = head; walker !== null; walker = walker.prev) {
        var p = walker.prev;
        walker.prev = walker.next;
        walker.next = p;
      }
      this.head = tail;
      this.tail = head;
      return this;
    };
    function insert(self, node, value) {
      var inserted = node === self.head ? new Node(value, null, node, self) : new Node(value, node, node.next, self);
      if (inserted.next === null) {
        self.tail = inserted;
      }
      if (inserted.prev === null) {
        self.head = inserted;
      }
      self.length++;
      return inserted;
    }
    function push(self, item) {
      self.tail = new Node(item, self.tail, null, self);
      if (!self.head) {
        self.head = self.tail;
      }
      self.length++;
    }
    function unshift(self, item) {
      self.head = new Node(item, null, self.head, self);
      if (!self.tail) {
        self.tail = self.head;
      }
      self.length++;
    }
    function Node(value, prev, next, list) {
      if (!(this instanceof Node)) {
        return new Node(value, prev, next, list);
      }
      this.list = list;
      this.value = value;
      if (prev) {
        prev.next = this;
        this.prev = prev;
      } else {
        this.prev = null;
      }
      if (next) {
        next.prev = this;
        this.next = next;
      } else {
        this.next = null;
      }
    }
    try {
      require_iterator()(Yallist);
    } catch (er) {
    }
  }
});

// node_modules/lru-cache/index.js
var require_lru_cache = __commonJS({
  "node_modules/lru-cache/index.js"(exports, module) {
    "use strict";
    var Yallist = require_yallist();
    var MAX = Symbol("max");
    var LENGTH = Symbol("length");
    var LENGTH_CALCULATOR = Symbol("lengthCalculator");
    var ALLOW_STALE = Symbol("allowStale");
    var MAX_AGE = Symbol("maxAge");
    var DISPOSE = Symbol("dispose");
    var NO_DISPOSE_ON_SET = Symbol("noDisposeOnSet");
    var LRU_LIST = Symbol("lruList");
    var CACHE = Symbol("cache");
    var UPDATE_AGE_ON_GET = Symbol("updateAgeOnGet");
    var naiveLength = () => 1;
    var LRUCache = class {
      constructor(options) {
        if (typeof options === "number")
          options = { max: options };
        if (!options)
          options = {};
        if (options.max && (typeof options.max !== "number" || options.max < 0))
          throw new TypeError("max must be a non-negative number");
        const max = this[MAX] = options.max || Infinity;
        const lc = options.length || naiveLength;
        this[LENGTH_CALCULATOR] = typeof lc !== "function" ? naiveLength : lc;
        this[ALLOW_STALE] = options.stale || false;
        if (options.maxAge && typeof options.maxAge !== "number")
          throw new TypeError("maxAge must be a number");
        this[MAX_AGE] = options.maxAge || 0;
        this[DISPOSE] = options.dispose;
        this[NO_DISPOSE_ON_SET] = options.noDisposeOnSet || false;
        this[UPDATE_AGE_ON_GET] = options.updateAgeOnGet || false;
        this.reset();
      }
      // resize the cache when the max changes.
      set max(mL) {
        if (typeof mL !== "number" || mL < 0)
          throw new TypeError("max must be a non-negative number");
        this[MAX] = mL || Infinity;
        trim(this);
      }
      get max() {
        return this[MAX];
      }
      set allowStale(allowStale) {
        this[ALLOW_STALE] = !!allowStale;
      }
      get allowStale() {
        return this[ALLOW_STALE];
      }
      set maxAge(mA) {
        if (typeof mA !== "number")
          throw new TypeError("maxAge must be a non-negative number");
        this[MAX_AGE] = mA;
        trim(this);
      }
      get maxAge() {
        return this[MAX_AGE];
      }
      // resize the cache when the lengthCalculator changes.
      set lengthCalculator(lC) {
        if (typeof lC !== "function")
          lC = naiveLength;
        if (lC !== this[LENGTH_CALCULATOR]) {
          this[LENGTH_CALCULATOR] = lC;
          this[LENGTH] = 0;
          this[LRU_LIST].forEach((hit) => {
            hit.length = this[LENGTH_CALCULATOR](hit.value, hit.key);
            this[LENGTH] += hit.length;
          });
        }
        trim(this);
      }
      get lengthCalculator() {
        return this[LENGTH_CALCULATOR];
      }
      get length() {
        return this[LENGTH];
      }
      get itemCount() {
        return this[LRU_LIST].length;
      }
      rforEach(fn, thisp) {
        thisp = thisp || this;
        for (let walker = this[LRU_LIST].tail; walker !== null; ) {
          const prev = walker.prev;
          forEachStep(this, fn, walker, thisp);
          walker = prev;
        }
      }
      forEach(fn, thisp) {
        thisp = thisp || this;
        for (let walker = this[LRU_LIST].head; walker !== null; ) {
          const next = walker.next;
          forEachStep(this, fn, walker, thisp);
          walker = next;
        }
      }
      keys() {
        return this[LRU_LIST].toArray().map((k) => k.key);
      }
      values() {
        return this[LRU_LIST].toArray().map((k) => k.value);
      }
      reset() {
        if (this[DISPOSE] && this[LRU_LIST] && this[LRU_LIST].length) {
          this[LRU_LIST].forEach((hit) => this[DISPOSE](hit.key, hit.value));
        }
        this[CACHE] = /* @__PURE__ */ new Map();
        this[LRU_LIST] = new Yallist();
        this[LENGTH] = 0;
      }
      dump() {
        return this[LRU_LIST].map((hit) => isStale(this, hit) ? false : {
          k: hit.key,
          v: hit.value,
          e: hit.now + (hit.maxAge || 0)
        }).toArray().filter((h) => h);
      }
      dumpLru() {
        return this[LRU_LIST];
      }
      set(key, value, maxAge) {
        maxAge = maxAge || this[MAX_AGE];
        if (maxAge && typeof maxAge !== "number")
          throw new TypeError("maxAge must be a number");
        const now = maxAge ? Date.now() : 0;
        const len = this[LENGTH_CALCULATOR](value, key);
        if (this[CACHE].has(key)) {
          if (len > this[MAX]) {
            del(this, this[CACHE].get(key));
            return false;
          }
          const node = this[CACHE].get(key);
          const item = node.value;
          if (this[DISPOSE]) {
            if (!this[NO_DISPOSE_ON_SET])
              this[DISPOSE](key, item.value);
          }
          item.now = now;
          item.maxAge = maxAge;
          item.value = value;
          this[LENGTH] += len - item.length;
          item.length = len;
          this.get(key);
          trim(this);
          return true;
        }
        const hit = new Entry(key, value, len, now, maxAge);
        if (hit.length > this[MAX]) {
          if (this[DISPOSE])
            this[DISPOSE](key, value);
          return false;
        }
        this[LENGTH] += hit.length;
        this[LRU_LIST].unshift(hit);
        this[CACHE].set(key, this[LRU_LIST].head);
        trim(this);
        return true;
      }
      has(key) {
        if (!this[CACHE].has(key)) return false;
        const hit = this[CACHE].get(key).value;
        return !isStale(this, hit);
      }
      get(key) {
        return get(this, key, true);
      }
      peek(key) {
        return get(this, key, false);
      }
      pop() {
        const node = this[LRU_LIST].tail;
        if (!node)
          return null;
        del(this, node);
        return node.value;
      }
      del(key) {
        del(this, this[CACHE].get(key));
      }
      load(arr) {
        this.reset();
        const now = Date.now();
        for (let l = arr.length - 1; l >= 0; l--) {
          const hit = arr[l];
          const expiresAt = hit.e || 0;
          if (expiresAt === 0)
            this.set(hit.k, hit.v);
          else {
            const maxAge = expiresAt - now;
            if (maxAge > 0) {
              this.set(hit.k, hit.v, maxAge);
            }
          }
        }
      }
      prune() {
        this[CACHE].forEach((value, key) => get(this, key, false));
      }
    };
    var get = (self, key, doUse) => {
      const node = self[CACHE].get(key);
      if (node) {
        const hit = node.value;
        if (isStale(self, hit)) {
          del(self, node);
          if (!self[ALLOW_STALE])
            return void 0;
        } else {
          if (doUse) {
            if (self[UPDATE_AGE_ON_GET])
              node.value.now = Date.now();
            self[LRU_LIST].unshiftNode(node);
          }
        }
        return hit.value;
      }
    };
    var isStale = (self, hit) => {
      if (!hit || !hit.maxAge && !self[MAX_AGE])
        return false;
      const diff = Date.now() - hit.now;
      return hit.maxAge ? diff > hit.maxAge : self[MAX_AGE] && diff > self[MAX_AGE];
    };
    var trim = (self) => {
      if (self[LENGTH] > self[MAX]) {
        for (let walker = self[LRU_LIST].tail; self[LENGTH] > self[MAX] && walker !== null; ) {
          const prev = walker.prev;
          del(self, walker);
          walker = prev;
        }
      }
    };
    var del = (self, node) => {
      if (node) {
        const hit = node.value;
        if (self[DISPOSE])
          self[DISPOSE](hit.key, hit.value);
        self[LENGTH] -= hit.length;
        self[CACHE].delete(hit.key);
        self[LRU_LIST].removeNode(node);
      }
    };
    var Entry = class {
      constructor(key, value, length, now, maxAge) {
        this.key = key;
        this.value = value;
        this.length = length;
        this.now = now;
        this.maxAge = maxAge || 0;
      }
    };
    var forEachStep = (self, fn, node, thisp) => {
      let hit = node.value;
      if (isStale(self, hit)) {
        del(self, node);
        if (!self[ALLOW_STALE])
          hit = void 0;
      }
      if (hit)
        fn.call(thisp, hit.value, hit.key, self);
    };
    module.exports = LRUCache;
  }
});

// node_modules/semver/classes/range.js
var require_range = __commonJS({
  "node_modules/semver/classes/range.js"(exports, module) {
    var Range = class _Range {
      constructor(range, options) {
        options = parseOptions(options);
        if (range instanceof _Range) {
          if (range.loose === !!options.loose && range.includePrerelease === !!options.includePrerelease) {
            return range;
          } else {
            return new _Range(range.raw, options);
          }
        }
        if (range instanceof Comparator) {
          this.raw = range.value;
          this.set = [[range]];
          this.format();
          return this;
        }
        this.options = options;
        this.loose = !!options.loose;
        this.includePrerelease = !!options.includePrerelease;
        this.raw = range.trim().split(/\s+/).join(" ");
        this.set = this.raw.split("||").map((r) => this.parseRange(r.trim())).filter((c) => c.length);
        if (!this.set.length) {
          throw new TypeError(`Invalid SemVer Range: ${this.raw}`);
        }
        if (this.set.length > 1) {
          const first = this.set[0];
          this.set = this.set.filter((c) => !isNullSet(c[0]));
          if (this.set.length === 0) {
            this.set = [first];
          } else if (this.set.length > 1) {
            for (const c of this.set) {
              if (c.length === 1 && isAny(c[0])) {
                this.set = [c];
                break;
              }
            }
          }
        }
        this.format();
      }
      format() {
        this.range = this.set.map((comps) => comps.join(" ").trim()).join("||").trim();
        return this.range;
      }
      toString() {
        return this.range;
      }
      parseRange(range) {
        const memoOpts = (this.options.includePrerelease && FLAG_INCLUDE_PRERELEASE) | (this.options.loose && FLAG_LOOSE);
        const memoKey = memoOpts + ":" + range;
        const cached = cache.get(memoKey);
        if (cached) {
          return cached;
        }
        const loose = this.options.loose;
        const hr = loose ? re[t.HYPHENRANGELOOSE] : re[t.HYPHENRANGE];
        range = range.replace(hr, hyphenReplace(this.options.includePrerelease));
        debug("hyphen replace", range);
        range = range.replace(re[t.COMPARATORTRIM], comparatorTrimReplace);
        debug("comparator trim", range);
        range = range.replace(re[t.TILDETRIM], tildeTrimReplace);
        debug("tilde trim", range);
        range = range.replace(re[t.CARETTRIM], caretTrimReplace);
        debug("caret trim", range);
        let rangeList = range.split(" ").map((comp) => parseComparator(comp, this.options)).join(" ").split(/\s+/).map((comp) => replaceGTE0(comp, this.options));
        if (loose) {
          rangeList = rangeList.filter((comp) => {
            debug("loose invalid filter", comp, this.options);
            return !!comp.match(re[t.COMPARATORLOOSE]);
          });
        }
        debug("range list", rangeList);
        const rangeMap = /* @__PURE__ */ new Map();
        const comparators = rangeList.map((comp) => new Comparator(comp, this.options));
        for (const comp of comparators) {
          if (isNullSet(comp)) {
            return [comp];
          }
          rangeMap.set(comp.value, comp);
        }
        if (rangeMap.size > 1 && rangeMap.has("")) {
          rangeMap.delete("");
        }
        const result = [...rangeMap.values()];
        cache.set(memoKey, result);
        return result;
      }
      intersects(range, options) {
        if (!(range instanceof _Range)) {
          throw new TypeError("a Range is required");
        }
        return this.set.some((thisComparators) => {
          return isSatisfiable(thisComparators, options) && range.set.some((rangeComparators) => {
            return isSatisfiable(rangeComparators, options) && thisComparators.every((thisComparator) => {
              return rangeComparators.every((rangeComparator) => {
                return thisComparator.intersects(rangeComparator, options);
              });
            });
          });
        });
      }
      // if ANY of the sets match ALL of its comparators, then pass
      test(version) {
        if (!version) {
          return false;
        }
        if (typeof version === "string") {
          try {
            version = new SemVer(version, this.options);
          } catch (er) {
            return false;
          }
        }
        for (let i = 0; i < this.set.length; i++) {
          if (testSet(this.set[i], version, this.options)) {
            return true;
          }
        }
        return false;
      }
    };
    module.exports = Range;
    var LRU = require_lru_cache();
    var cache = new LRU({ max: 1e3 });
    var parseOptions = require_parse_options();
    var Comparator = require_comparator();
    var debug = require_debug();
    var SemVer = require_semver();
    var {
      safeRe: re,
      t,
      comparatorTrimReplace,
      tildeTrimReplace,
      caretTrimReplace
    } = require_re();
    var { FLAG_INCLUDE_PRERELEASE, FLAG_LOOSE } = require_constants();
    var isNullSet = (c) => c.value === "<0.0.0-0";
    var isAny = (c) => c.value === "";
    var isSatisfiable = (comparators, options) => {
      let result = true;
      const remainingComparators = comparators.slice();
      let testComparator = remainingComparators.pop();
      while (result && remainingComparators.length) {
        result = remainingComparators.every((otherComparator) => {
          return testComparator.intersects(otherComparator, options);
        });
        testComparator = remainingComparators.pop();
      }
      return result;
    };
    var parseComparator = (comp, options) => {
      debug("comp", comp, options);
      comp = replaceCarets(comp, options);
      debug("caret", comp);
      comp = replaceTildes(comp, options);
      debug("tildes", comp);
      comp = replaceXRanges(comp, options);
      debug("xrange", comp);
      comp = replaceStars(comp, options);
      debug("stars", comp);
      return comp;
    };
    var isX = (id) => !id || id.toLowerCase() === "x" || id === "*";
    var replaceTildes = (comp, options) => {
      return comp.trim().split(/\s+/).map((c) => replaceTilde(c, options)).join(" ");
    };
    var replaceTilde = (comp, options) => {
      const r = options.loose ? re[t.TILDELOOSE] : re[t.TILDE];
      return comp.replace(r, (_, M, m, p, pr) => {
        debug("tilde", comp, _, M, m, p, pr);
        let ret;
        if (isX(M)) {
          ret = "";
        } else if (isX(m)) {
          ret = `>=${M}.0.0 <${+M + 1}.0.0-0`;
        } else if (isX(p)) {
          ret = `>=${M}.${m}.0 <${M}.${+m + 1}.0-0`;
        } else if (pr) {
          debug("replaceTilde pr", pr);
          ret = `>=${M}.${m}.${p}-${pr} <${M}.${+m + 1}.0-0`;
        } else {
          ret = `>=${M}.${m}.${p} <${M}.${+m + 1}.0-0`;
        }
        debug("tilde return", ret);
        return ret;
      });
    };
    var replaceCarets = (comp, options) => {
      return comp.trim().split(/\s+/).map((c) => replaceCaret(c, options)).join(" ");
    };
    var replaceCaret = (comp, options) => {
      debug("caret", comp, options);
      const r = options.loose ? re[t.CARETLOOSE] : re[t.CARET];
      const z = options.includePrerelease ? "-0" : "";
      return comp.replace(r, (_, M, m, p, pr) => {
        debug("caret", comp, _, M, m, p, pr);
        let ret;
        if (isX(M)) {
          ret = "";
        } else if (isX(m)) {
          ret = `>=${M}.0.0${z} <${+M + 1}.0.0-0`;
        } else if (isX(p)) {
          if (M === "0") {
            ret = `>=${M}.${m}.0${z} <${M}.${+m + 1}.0-0`;
          } else {
            ret = `>=${M}.${m}.0${z} <${+M + 1}.0.0-0`;
          }
        } else if (pr) {
          debug("replaceCaret pr", pr);
          if (M === "0") {
            if (m === "0") {
              ret = `>=${M}.${m}.${p}-${pr} <${M}.${m}.${+p + 1}-0`;
            } else {
              ret = `>=${M}.${m}.${p}-${pr} <${M}.${+m + 1}.0-0`;
            }
          } else {
            ret = `>=${M}.${m}.${p}-${pr} <${+M + 1}.0.0-0`;
          }
        } else {
          debug("no pr");
          if (M === "0") {
            if (m === "0") {
              ret = `>=${M}.${m}.${p}${z} <${M}.${m}.${+p + 1}-0`;
            } else {
              ret = `>=${M}.${m}.${p}${z} <${M}.${+m + 1}.0-0`;
            }
          } else {
            ret = `>=${M}.${m}.${p} <${+M + 1}.0.0-0`;
          }
        }
        debug("caret return", ret);
        return ret;
      });
    };
    var replaceXRanges = (comp, options) => {
      debug("replaceXRanges", comp, options);
      return comp.split(/\s+/).map((c) => replaceXRange(c, options)).join(" ");
    };
    var replaceXRange = (comp, options) => {
      comp = comp.trim();
      const r = options.loose ? re[t.XRANGELOOSE] : re[t.XRANGE];
      return comp.replace(r, (ret, gtlt, M, m, p, pr) => {
        debug("xRange", comp, ret, gtlt, M, m, p, pr);
        const xM = isX(M);
        const xm = xM || isX(m);
        const xp = xm || isX(p);
        const anyX = xp;
        if (gtlt === "=" && anyX) {
          gtlt = "";
        }
        pr = options.includePrerelease ? "-0" : "";
        if (xM) {
          if (gtlt === ">" || gtlt === "<") {
            ret = "<0.0.0-0";
          } else {
            ret = "*";
          }
        } else if (gtlt && anyX) {
          if (xm) {
            m = 0;
          }
          p = 0;
          if (gtlt === ">") {
            gtlt = ">=";
            if (xm) {
              M = +M + 1;
              m = 0;
              p = 0;
            } else {
              m = +m + 1;
              p = 0;
            }
          } else if (gtlt === "<=") {
            gtlt = "<";
            if (xm) {
              M = +M + 1;
            } else {
              m = +m + 1;
            }
          }
          if (gtlt === "<") {
            pr = "-0";
          }
          ret = `${gtlt + M}.${m}.${p}${pr}`;
        } else if (xm) {
          ret = `>=${M}.0.0${pr} <${+M + 1}.0.0-0`;
        } else if (xp) {
          ret = `>=${M}.${m}.0${pr} <${M}.${+m + 1}.0-0`;
        }
        debug("xRange return", ret);
        return ret;
      });
    };
    var replaceStars = (comp, options) => {
      debug("replaceStars", comp, options);
      return comp.trim().replace(re[t.STAR], "");
    };
    var replaceGTE0 = (comp, options) => {
      debug("replaceGTE0", comp, options);
      return comp.trim().replace(re[options.includePrerelease ? t.GTE0PRE : t.GTE0], "");
    };
    var hyphenReplace = (incPr) => ($0, from, fM, fm, fp, fpr, fb, to, tM, tm, tp, tpr, tb) => {
      if (isX(fM)) {
        from = "";
      } else if (isX(fm)) {
        from = `>=${fM}.0.0${incPr ? "-0" : ""}`;
      } else if (isX(fp)) {
        from = `>=${fM}.${fm}.0${incPr ? "-0" : ""}`;
      } else if (fpr) {
        from = `>=${from}`;
      } else {
        from = `>=${from}${incPr ? "-0" : ""}`;
      }
      if (isX(tM)) {
        to = "";
      } else if (isX(tm)) {
        to = `<${+tM + 1}.0.0-0`;
      } else if (isX(tp)) {
        to = `<${tM}.${+tm + 1}.0-0`;
      } else if (tpr) {
        to = `<=${tM}.${tm}.${tp}-${tpr}`;
      } else if (incPr) {
        to = `<${tM}.${tm}.${+tp + 1}-0`;
      } else {
        to = `<=${to}`;
      }
      return `${from} ${to}`.trim();
    };
    var testSet = (set, version, options) => {
      for (let i = 0; i < set.length; i++) {
        if (!set[i].test(version)) {
          return false;
        }
      }
      if (version.prerelease.length && !options.includePrerelease) {
        for (let i = 0; i < set.length; i++) {
          debug(set[i].semver);
          if (set[i].semver === Comparator.ANY) {
            continue;
          }
          if (set[i].semver.prerelease.length > 0) {
            const allowed = set[i].semver;
            if (allowed.major === version.major && allowed.minor === version.minor && allowed.patch === version.patch) {
              return true;
            }
          }
        }
        return false;
      }
      return true;
    };
  }
});

// node_modules/semver/classes/comparator.js
var require_comparator = __commonJS({
  "node_modules/semver/classes/comparator.js"(exports, module) {
    var ANY = Symbol("SemVer ANY");
    var Comparator = class _Comparator {
      static get ANY() {
        return ANY;
      }
      constructor(comp, options) {
        options = parseOptions(options);
        if (comp instanceof _Comparator) {
          if (comp.loose === !!options.loose) {
            return comp;
          } else {
            comp = comp.value;
          }
        }
        comp = comp.trim().split(/\s+/).join(" ");
        debug("comparator", comp, options);
        this.options = options;
        this.loose = !!options.loose;
        this.parse(comp);
        if (this.semver === ANY) {
          this.value = "";
        } else {
          this.value = this.operator + this.semver.version;
        }
        debug("comp", this);
      }
      parse(comp) {
        const r = this.options.loose ? re[t.COMPARATORLOOSE] : re[t.COMPARATOR];
        const m = comp.match(r);
        if (!m) {
          throw new TypeError(`Invalid comparator: ${comp}`);
        }
        this.operator = m[1] !== void 0 ? m[1] : "";
        if (this.operator === "=") {
          this.operator = "";
        }
        if (!m[2]) {
          this.semver = ANY;
        } else {
          this.semver = new SemVer(m[2], this.options.loose);
        }
      }
      toString() {
        return this.value;
      }
      test(version) {
        debug("Comparator.test", version, this.options.loose);
        if (this.semver === ANY || version === ANY) {
          return true;
        }
        if (typeof version === "string") {
          try {
            version = new SemVer(version, this.options);
          } catch (er) {
            return false;
          }
        }
        return cmp(version, this.operator, this.semver, this.options);
      }
      intersects(comp, options) {
        if (!(comp instanceof _Comparator)) {
          throw new TypeError("a Comparator is required");
        }
        if (this.operator === "") {
          if (this.value === "") {
            return true;
          }
          return new Range(comp.value, options).test(this.value);
        } else if (comp.operator === "") {
          if (comp.value === "") {
            return true;
          }
          return new Range(this.value, options).test(comp.semver);
        }
        options = parseOptions(options);
        if (options.includePrerelease && (this.value === "<0.0.0-0" || comp.value === "<0.0.0-0")) {
          return false;
        }
        if (!options.includePrerelease && (this.value.startsWith("<0.0.0") || comp.value.startsWith("<0.0.0"))) {
          return false;
        }
        if (this.operator.startsWith(">") && comp.operator.startsWith(">")) {
          return true;
        }
        if (this.operator.startsWith("<") && comp.operator.startsWith("<")) {
          return true;
        }
        if (this.semver.version === comp.semver.version && this.operator.includes("=") && comp.operator.includes("=")) {
          return true;
        }
        if (cmp(this.semver, "<", comp.semver, options) && this.operator.startsWith(">") && comp.operator.startsWith("<")) {
          return true;
        }
        if (cmp(this.semver, ">", comp.semver, options) && this.operator.startsWith("<") && comp.operator.startsWith(">")) {
          return true;
        }
        return false;
      }
    };
    module.exports = Comparator;
    var parseOptions = require_parse_options();
    var { safeRe: re, t } = require_re();
    var cmp = require_cmp();
    var debug = require_debug();
    var SemVer = require_semver();
    var Range = require_range();
  }
});

// node_modules/semver/functions/satisfies.js
var require_satisfies = __commonJS({
  "node_modules/semver/functions/satisfies.js"(exports, module) {
    var Range = require_range();
    var satisfies2 = (version, range, options) => {
      try {
        range = new Range(range, options);
      } catch (er) {
        return false;
      }
      return range.test(version);
    };
    module.exports = satisfies2;
  }
});

// node_modules/semver/ranges/to-comparators.js
var require_to_comparators = __commonJS({
  "node_modules/semver/ranges/to-comparators.js"(exports, module) {
    var Range = require_range();
    var toComparators = (range, options) => new Range(range, options).set.map((comp) => comp.map((c) => c.value).join(" ").trim().split(" "));
    module.exports = toComparators;
  }
});

// node_modules/semver/ranges/max-satisfying.js
var require_max_satisfying = __commonJS({
  "node_modules/semver/ranges/max-satisfying.js"(exports, module) {
    var SemVer = require_semver();
    var Range = require_range();
    var maxSatisfying = (versions, range, options) => {
      let max = null;
      let maxSV = null;
      let rangeObj = null;
      try {
        rangeObj = new Range(range, options);
      } catch (er) {
        return null;
      }
      versions.forEach((v) => {
        if (rangeObj.test(v)) {
          if (!max || maxSV.compare(v) === -1) {
            max = v;
            maxSV = new SemVer(max, options);
          }
        }
      });
      return max;
    };
    module.exports = maxSatisfying;
  }
});

// node_modules/semver/ranges/min-satisfying.js
var require_min_satisfying = __commonJS({
  "node_modules/semver/ranges/min-satisfying.js"(exports, module) {
    var SemVer = require_semver();
    var Range = require_range();
    var minSatisfying = (versions, range, options) => {
      let min = null;
      let minSV = null;
      let rangeObj = null;
      try {
        rangeObj = new Range(range, options);
      } catch (er) {
        return null;
      }
      versions.forEach((v) => {
        if (rangeObj.test(v)) {
          if (!min || minSV.compare(v) === 1) {
            min = v;
            minSV = new SemVer(min, options);
          }
        }
      });
      return min;
    };
    module.exports = minSatisfying;
  }
});

// node_modules/semver/ranges/min-version.js
var require_min_version = __commonJS({
  "node_modules/semver/ranges/min-version.js"(exports, module) {
    var SemVer = require_semver();
    var Range = require_range();
    var gt = require_gt();
    var minVersion = (range, loose) => {
      range = new Range(range, loose);
      let minver = new SemVer("0.0.0");
      if (range.test(minver)) {
        return minver;
      }
      minver = new SemVer("0.0.0-0");
      if (range.test(minver)) {
        return minver;
      }
      minver = null;
      for (let i = 0; i < range.set.length; ++i) {
        const comparators = range.set[i];
        let setMin = null;
        comparators.forEach((comparator) => {
          const compver = new SemVer(comparator.semver.version);
          switch (comparator.operator) {
            case ">":
              if (compver.prerelease.length === 0) {
                compver.patch++;
              } else {
                compver.prerelease.push(0);
              }
              compver.raw = compver.format();
            case "":
            case ">=":
              if (!setMin || gt(compver, setMin)) {
                setMin = compver;
              }
              break;
            case "<":
            case "<=":
              break;
            default:
              throw new Error(`Unexpected operation: ${comparator.operator}`);
          }
        });
        if (setMin && (!minver || gt(minver, setMin))) {
          minver = setMin;
        }
      }
      if (minver && range.test(minver)) {
        return minver;
      }
      return null;
    };
    module.exports = minVersion;
  }
});

// node_modules/semver/ranges/valid.js
var require_valid2 = __commonJS({
  "node_modules/semver/ranges/valid.js"(exports, module) {
    var Range = require_range();
    var validRange2 = (range, options) => {
      try {
        return new Range(range, options).range || "*";
      } catch (er) {
        return null;
      }
    };
    module.exports = validRange2;
  }
});

// node_modules/semver/ranges/outside.js
var require_outside = __commonJS({
  "node_modules/semver/ranges/outside.js"(exports, module) {
    var SemVer = require_semver();
    var Comparator = require_comparator();
    var { ANY } = Comparator;
    var Range = require_range();
    var satisfies2 = require_satisfies();
    var gt = require_gt();
    var lt = require_lt();
    var lte = require_lte();
    var gte = require_gte();
    var outside = (version, range, hilo, options) => {
      version = new SemVer(version, options);
      range = new Range(range, options);
      let gtfn, ltefn, ltfn, comp, ecomp;
      switch (hilo) {
        case ">":
          gtfn = gt;
          ltefn = lte;
          ltfn = lt;
          comp = ">";
          ecomp = ">=";
          break;
        case "<":
          gtfn = lt;
          ltefn = gte;
          ltfn = gt;
          comp = "<";
          ecomp = "<=";
          break;
        default:
          throw new TypeError('Must provide a hilo val of "<" or ">"');
      }
      if (satisfies2(version, range, options)) {
        return false;
      }
      for (let i = 0; i < range.set.length; ++i) {
        const comparators = range.set[i];
        let high = null;
        let low = null;
        comparators.forEach((comparator) => {
          if (comparator.semver === ANY) {
            comparator = new Comparator(">=0.0.0");
          }
          high = high || comparator;
          low = low || comparator;
          if (gtfn(comparator.semver, high.semver, options)) {
            high = comparator;
          } else if (ltfn(comparator.semver, low.semver, options)) {
            low = comparator;
          }
        });
        if (high.operator === comp || high.operator === ecomp) {
          return false;
        }
        if ((!low.operator || low.operator === comp) && ltefn(version, low.semver)) {
          return false;
        } else if (low.operator === ecomp && ltfn(version, low.semver)) {
          return false;
        }
      }
      return true;
    };
    module.exports = outside;
  }
});

// node_modules/semver/ranges/gtr.js
var require_gtr = __commonJS({
  "node_modules/semver/ranges/gtr.js"(exports, module) {
    var outside = require_outside();
    var gtr = (version, range, options) => outside(version, range, ">", options);
    module.exports = gtr;
  }
});

// node_modules/semver/ranges/ltr.js
var require_ltr = __commonJS({
  "node_modules/semver/ranges/ltr.js"(exports, module) {
    var outside = require_outside();
    var ltr = (version, range, options) => outside(version, range, "<", options);
    module.exports = ltr;
  }
});

// node_modules/semver/ranges/intersects.js
var require_intersects = __commonJS({
  "node_modules/semver/ranges/intersects.js"(exports, module) {
    var Range = require_range();
    var intersects = (r1, r2, options) => {
      r1 = new Range(r1, options);
      r2 = new Range(r2, options);
      return r1.intersects(r2, options);
    };
    module.exports = intersects;
  }
});

// node_modules/semver/ranges/simplify.js
var require_simplify = __commonJS({
  "node_modules/semver/ranges/simplify.js"(exports, module) {
    var satisfies2 = require_satisfies();
    var compare = require_compare();
    module.exports = (versions, range, options) => {
      const set = [];
      let first = null;
      let prev = null;
      const v = versions.sort((a, b) => compare(a, b, options));
      for (const version of v) {
        const included = satisfies2(version, range, options);
        if (included) {
          prev = version;
          if (!first) {
            first = version;
          }
        } else {
          if (prev) {
            set.push([first, prev]);
          }
          prev = null;
          first = null;
        }
      }
      if (first) {
        set.push([first, null]);
      }
      const ranges = [];
      for (const [min, max] of set) {
        if (min === max) {
          ranges.push(min);
        } else if (!max && min === v[0]) {
          ranges.push("*");
        } else if (!max) {
          ranges.push(`>=${min}`);
        } else if (min === v[0]) {
          ranges.push(`<=${max}`);
        } else {
          ranges.push(`${min} - ${max}`);
        }
      }
      const simplified = ranges.join(" || ");
      const original = typeof range.raw === "string" ? range.raw : String(range);
      return simplified.length < original.length ? simplified : range;
    };
  }
});

// node_modules/semver/ranges/subset.js
var require_subset = __commonJS({
  "node_modules/semver/ranges/subset.js"(exports, module) {
    var Range = require_range();
    var Comparator = require_comparator();
    var { ANY } = Comparator;
    var satisfies2 = require_satisfies();
    var compare = require_compare();
    var subset = (sub, dom, options = {}) => {
      if (sub === dom) {
        return true;
      }
      sub = new Range(sub, options);
      dom = new Range(dom, options);
      let sawNonNull = false;
      OUTER: for (const simpleSub of sub.set) {
        for (const simpleDom of dom.set) {
          const isSub = simpleSubset(simpleSub, simpleDom, options);
          sawNonNull = sawNonNull || isSub !== null;
          if (isSub) {
            continue OUTER;
          }
        }
        if (sawNonNull) {
          return false;
        }
      }
      return true;
    };
    var minimumVersionWithPreRelease = [new Comparator(">=0.0.0-0")];
    var minimumVersion = [new Comparator(">=0.0.0")];
    var simpleSubset = (sub, dom, options) => {
      if (sub === dom) {
        return true;
      }
      if (sub.length === 1 && sub[0].semver === ANY) {
        if (dom.length === 1 && dom[0].semver === ANY) {
          return true;
        } else if (options.includePrerelease) {
          sub = minimumVersionWithPreRelease;
        } else {
          sub = minimumVersion;
        }
      }
      if (dom.length === 1 && dom[0].semver === ANY) {
        if (options.includePrerelease) {
          return true;
        } else {
          dom = minimumVersion;
        }
      }
      const eqSet = /* @__PURE__ */ new Set();
      let gt, lt;
      for (const c of sub) {
        if (c.operator === ">" || c.operator === ">=") {
          gt = higherGT(gt, c, options);
        } else if (c.operator === "<" || c.operator === "<=") {
          lt = lowerLT(lt, c, options);
        } else {
          eqSet.add(c.semver);
        }
      }
      if (eqSet.size > 1) {
        return null;
      }
      let gtltComp;
      if (gt && lt) {
        gtltComp = compare(gt.semver, lt.semver, options);
        if (gtltComp > 0) {
          return null;
        } else if (gtltComp === 0 && (gt.operator !== ">=" || lt.operator !== "<=")) {
          return null;
        }
      }
      for (const eq of eqSet) {
        if (gt && !satisfies2(eq, String(gt), options)) {
          return null;
        }
        if (lt && !satisfies2(eq, String(lt), options)) {
          return null;
        }
        for (const c of dom) {
          if (!satisfies2(eq, String(c), options)) {
            return false;
          }
        }
        return true;
      }
      let higher, lower;
      let hasDomLT, hasDomGT;
      let needDomLTPre = lt && !options.includePrerelease && lt.semver.prerelease.length ? lt.semver : false;
      let needDomGTPre = gt && !options.includePrerelease && gt.semver.prerelease.length ? gt.semver : false;
      if (needDomLTPre && needDomLTPre.prerelease.length === 1 && lt.operator === "<" && needDomLTPre.prerelease[0] === 0) {
        needDomLTPre = false;
      }
      for (const c of dom) {
        hasDomGT = hasDomGT || c.operator === ">" || c.operator === ">=";
        hasDomLT = hasDomLT || c.operator === "<" || c.operator === "<=";
        if (gt) {
          if (needDomGTPre) {
            if (c.semver.prerelease && c.semver.prerelease.length && c.semver.major === needDomGTPre.major && c.semver.minor === needDomGTPre.minor && c.semver.patch === needDomGTPre.patch) {
              needDomGTPre = false;
            }
          }
          if (c.operator === ">" || c.operator === ">=") {
            higher = higherGT(gt, c, options);
            if (higher === c && higher !== gt) {
              return false;
            }
          } else if (gt.operator === ">=" && !satisfies2(gt.semver, String(c), options)) {
            return false;
          }
        }
        if (lt) {
          if (needDomLTPre) {
            if (c.semver.prerelease && c.semver.prerelease.length && c.semver.major === needDomLTPre.major && c.semver.minor === needDomLTPre.minor && c.semver.patch === needDomLTPre.patch) {
              needDomLTPre = false;
            }
          }
          if (c.operator === "<" || c.operator === "<=") {
            lower = lowerLT(lt, c, options);
            if (lower === c && lower !== lt) {
              return false;
            }
          } else if (lt.operator === "<=" && !satisfies2(lt.semver, String(c), options)) {
            return false;
          }
        }
        if (!c.operator && (lt || gt) && gtltComp !== 0) {
          return false;
        }
      }
      if (gt && hasDomLT && !lt && gtltComp !== 0) {
        return false;
      }
      if (lt && hasDomGT && !gt && gtltComp !== 0) {
        return false;
      }
      if (needDomGTPre || needDomLTPre) {
        return false;
      }
      return true;
    };
    var higherGT = (a, b, options) => {
      if (!a) {
        return b;
      }
      const comp = compare(a.semver, b.semver, options);
      return comp > 0 ? a : comp < 0 ? b : b.operator === ">" && a.operator === ">=" ? b : a;
    };
    var lowerLT = (a, b, options) => {
      if (!a) {
        return b;
      }
      const comp = compare(a.semver, b.semver, options);
      return comp < 0 ? a : comp > 0 ? b : b.operator === "<" && a.operator === "<=" ? b : a;
    };
    module.exports = subset;
  }
});

// node_modules/semver/index.js
var require_semver2 = __commonJS({
  "node_modules/semver/index.js"(exports, module) {
    var internalRe = require_re();
    var constants = require_constants();
    var SemVer = require_semver();
    var identifiers = require_identifiers();
    var parse = require_parse();
    var valid = require_valid();
    var clean = require_clean();
    var inc = require_inc();
    var diff = require_diff();
    var major = require_major();
    var minor = require_minor();
    var patch = require_patch();
    var prerelease = require_prerelease();
    var compare = require_compare();
    var rcompare = require_rcompare();
    var compareLoose = require_compare_loose();
    var compareBuild = require_compare_build();
    var sort = require_sort();
    var rsort = require_rsort();
    var gt = require_gt();
    var lt = require_lt();
    var eq = require_eq();
    var neq = require_neq();
    var gte = require_gte();
    var lte = require_lte();
    var cmp = require_cmp();
    var coerce = require_coerce();
    var Comparator = require_comparator();
    var Range = require_range();
    var satisfies2 = require_satisfies();
    var toComparators = require_to_comparators();
    var maxSatisfying = require_max_satisfying();
    var minSatisfying = require_min_satisfying();
    var minVersion = require_min_version();
    var validRange2 = require_valid2();
    var outside = require_outside();
    var gtr = require_gtr();
    var ltr = require_ltr();
    var intersects = require_intersects();
    var simplifyRange = require_simplify();
    var subset = require_subset();
    module.exports = {
      parse,
      valid,
      clean,
      inc,
      diff,
      major,
      minor,
      patch,
      prerelease,
      compare,
      rcompare,
      compareLoose,
      compareBuild,
      sort,
      rsort,
      gt,
      lt,
      eq,
      neq,
      gte,
      lte,
      cmp,
      coerce,
      Comparator,
      Range,
      satisfies: satisfies2,
      toComparators,
      maxSatisfying,
      minSatisfying,
      minVersion,
      validRange: validRange2,
      outside,
      gtr,
      ltr,
      intersects,
      simplifyRange,
      subset,
      SemVer,
      re: internalRe.re,
      src: internalRe.src,
      tokens: internalRe.t,
      SEMVER_SPEC_VERSION: constants.SEMVER_SPEC_VERSION,
      RELEASE_TYPES: constants.RELEASE_TYPES,
      compareIdentifiers: identifiers.compareIdentifiers,
      rcompareIdentifiers: identifiers.rcompareIdentifiers
    };
  }
});

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Marketplace/MarketplacePage.mjs
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var React3 = __toESM(require_react(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useDebounce.mjs
var React = __toESM(require_react(), 1);
function useDebounce(value, delay) {
  const [debouncedValue, setDebouncedValue] = React.useState(value);
  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    return () => {
      clearTimeout(handler);
    };
  }, [
    value,
    delay
  ]);
  return debouncedValue;
}

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Marketplace/components/NpmPackagesFilters.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var NpmPackagesFilters = ({ handleSelectClear, handleSelectChange, npmPackageType, possibleCategories, possibleCollections, query }) => {
  var _a, _b;
  const { formatMessage } = useIntl();
  const handleTagRemove = (tagToRemove, filterType) => {
    const update = {
      [filterType]: (query[filterType] ?? []).filter((previousTag) => previousTag !== tagToRemove)
    };
    handleSelectChange(update);
  };
  return (0, import_jsx_runtime.jsxs)(Popover.Root, {
    children: [
      (0, import_jsx_runtime.jsx)(Popover.Trigger, {
        children: (0, import_jsx_runtime.jsx)(Button, {
          variant: "tertiary",
          startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$3j, {}),
          children: formatMessage({
            id: "app.utils.filters",
            defaultMessage: "Filters"
          })
        })
      }),
      (0, import_jsx_runtime.jsx)(Popover.Content, {
        sideOffset: 4,
        children: (0, import_jsx_runtime.jsxs)(Flex, {
          padding: 3,
          direction: "column",
          alignItems: "stretch",
          gap: 1,
          children: [
            (0, import_jsx_runtime.jsx)(FilterSelect, {
              message: formatMessage({
                id: "admin.pages.MarketPlacePage.filters.collections",
                defaultMessage: "Collections"
              }),
              value: (query == null ? void 0 : query.collections) || [],
              onChange: (newCollections) => {
                const update = {
                  collections: newCollections
                };
                handleSelectChange(update);
              },
              onClear: () => handleSelectClear("collections"),
              possibleFilters: possibleCollections,
              customizeContent: (values) => formatMessage({
                id: "admin.pages.MarketPlacePage.filters.collectionsSelected",
                defaultMessage: "{count, plural, =0 {No collections} one {# collection} other {# collections}} selected"
              }, {
                count: (values == null ? void 0 : values.length) ?? 0
              })
            }),
            npmPackageType === "plugin" && (0, import_jsx_runtime.jsx)(FilterSelect, {
              message: formatMessage({
                id: "admin.pages.MarketPlacePage.filters.categories",
                defaultMessage: "Categories"
              }),
              value: (query == null ? void 0 : query.categories) || [],
              onChange: (newCategories) => {
                const update = {
                  categories: newCategories
                };
                handleSelectChange(update);
              },
              onClear: () => handleSelectClear("categories"),
              possibleFilters: possibleCategories,
              customizeContent: (values) => formatMessage({
                id: "admin.pages.MarketPlacePage.filters.categoriesSelected",
                defaultMessage: "{count, plural, =0 {No categories} one {# category} other {# categories}} selected"
              }, {
                count: (values == null ? void 0 : values.length) ?? 0
              })
            })
          ]
        })
      }),
      (_a = query.collections) == null ? void 0 : _a.map((collection) => (0, import_jsx_runtime.jsx)(Box, {
        padding: 1,
        children: (0, import_jsx_runtime.jsx)(Tag, {
          icon: (0, import_jsx_runtime.jsx)(ForwardRef$45, {}),
          onClick: () => handleTagRemove(collection, "collections"),
          children: collection
        })
      }, collection)),
      npmPackageType === "plugin" && ((_b = query.categories) == null ? void 0 : _b.map((category) => (0, import_jsx_runtime.jsx)(Box, {
        padding: 1,
        children: (0, import_jsx_runtime.jsx)(Tag, {
          icon: (0, import_jsx_runtime.jsx)(ForwardRef$45, {}),
          onClick: () => handleTagRemove(category, "categories"),
          children: category
        })
      }, category)))
    ]
  });
};
var FilterSelect = ({ message, value, onChange, possibleFilters, onClear, customizeContent }) => {
  return (0, import_jsx_runtime.jsx)(MultiSelect, {
    "data-testid": `${message}-button`,
    "aria-label": message,
    placeholder: message,
    onChange,
    onClear,
    value,
    customizeContent,
    children: Object.entries(possibleFilters).map(([filterName, count]) => {
      return (0, import_jsx_runtime.jsx)(MultiSelectOption, {
        "data-testid": `${filterName}-${count}`,
        value: filterName,
        children: `${filterName} (${count})`
      }, filterName);
    })
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Marketplace/components/NpmPackagesGrid.mjs
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Marketplace/components/NpmPackageCard.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_pluralize = __toESM(require_pluralize(), 1);
var semver = __toESM(require_semver2(), 1);
var EllipsisText = dt(Typography)`
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
`;
var NpmPackageCard = ({ npmPackage, isInstalled, useYarn, isInDevelopmentMode, npmPackageType, strapiAppVersion }) => {
  const { attributes } = npmPackage;
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const commandToCopy = useYarn ? `yarn add ${attributes.npmPackageName}` : `npm install ${attributes.npmPackageName}`;
  const madeByStrapiMessage = formatMessage({
    id: "admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi",
    defaultMessage: "Made by Strapi"
  });
  const npmPackageHref = `https://market.strapi.io/${import_pluralize.default.plural(npmPackageType)}/${attributes.slug}`;
  const versionRange = semver.validRange(attributes.strapiVersion);
  const isCompatible = versionRange ? semver.satisfies(strapiAppVersion ?? "", versionRange) : false;
  return (0, import_jsx_runtime2.jsxs)(Flex, {
    direction: "column",
    justifyContent: "space-between",
    paddingTop: 4,
    paddingRight: 4,
    paddingBottom: 4,
    paddingLeft: 4,
    hasRadius: true,
    background: "neutral0",
    shadow: "tableShadow",
    height: "100%",
    alignItems: "normal",
    "data-testid": "npm-package-card",
    children: [
      (0, import_jsx_runtime2.jsxs)(Box, {
        children: [
          (0, import_jsx_runtime2.jsxs)(Flex, {
            direction: "row",
            justifyContent: "space-between",
            alignItems: "flex-start",
            children: [
              (0, import_jsx_runtime2.jsx)(Box, {
                tag: "img",
                src: attributes.logo.url,
                alt: `${attributes.name} logo`,
                hasRadius: true,
                width: 11,
                height: 11
              }),
              (0, import_jsx_runtime2.jsx)(PackageStats, {
                githubStars: attributes.githubStars,
                npmDownloads: attributes.npmDownloads,
                npmPackageType
              })
            ]
          }),
          (0, import_jsx_runtime2.jsx)(Box, {
            paddingTop: 4,
            children: (0, import_jsx_runtime2.jsx)(Typography, {
              tag: "h3",
              variant: "delta",
              children: (0, import_jsx_runtime2.jsxs)(Flex, {
                alignItems: "center",
                gap: attributes.validated && !attributes.madeByStrapi ? 2 : 1,
                children: [
                  attributes.name,
                  attributes.validated && !attributes.madeByStrapi && (0, import_jsx_runtime2.jsx)(TooltipImpl, {
                    description: formatMessage({
                      id: "admin.pages.MarketPlacePage.plugin.tooltip.verified",
                      defaultMessage: "Plugin verified by Strapi"
                    }),
                    children: (0, import_jsx_runtime2.jsx)(ForwardRef$4D, {
                      fill: "success600"
                    })
                  }),
                  attributes.madeByStrapi && (0, import_jsx_runtime2.jsx)(TooltipImpl, {
                    description: madeByStrapiMessage,
                    children: (0, import_jsx_runtime2.jsx)(Box, {
                      tag: "img",
                      src: img,
                      alt: madeByStrapiMessage,
                      width: 6,
                      height: "auto"
                    })
                  })
                ]
              })
            })
          }),
          (0, import_jsx_runtime2.jsx)(Box, {
            paddingTop: 2,
            children: (0, import_jsx_runtime2.jsx)(EllipsisText, {
              tag: "p",
              variant: "omega",
              textColor: "neutral600",
              children: attributes.description
            })
          })
        ]
      }),
      (0, import_jsx_runtime2.jsxs)(Flex, {
        gap: 2,
        style: {
          alignSelf: "flex-end"
        },
        paddingTop: 6,
        children: [
          (0, import_jsx_runtime2.jsx)(LinkButton, {
            size: "S",
            href: npmPackageHref,
            isExternal: true,
            endIcon: (0, import_jsx_runtime2.jsx)(ForwardRef$3F, {}),
            "aria-label": formatMessage({
              id: "admin.pages.MarketPlacePage.plugin.info.label",
              defaultMessage: "Learn more about {pluginName}"
            }, {
              pluginName: attributes.name
            }),
            variant: "tertiary",
            onClick: () => trackUsage("didPluginLearnMore"),
            children: formatMessage({
              id: "admin.pages.MarketPlacePage.plugin.info.text",
              defaultMessage: "More"
            })
          }),
          (0, import_jsx_runtime2.jsx)(InstallPluginButton, {
            isInstalled,
            isInDevelopmentMode,
            isCompatible,
            commandToCopy,
            strapiAppVersion,
            strapiPeerDepVersion: attributes.strapiVersion,
            pluginName: attributes.name
          })
        ]
      })
    ]
  });
};
var InstallPluginButton = ({ isInstalled, isInDevelopmentMode, isCompatible, commandToCopy, strapiAppVersion, strapiPeerDepVersion, pluginName }) => {
  const { toggleNotification } = useNotification();
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const { copy } = useClipboard();
  const handleCopy = async () => {
    const didCopy = await copy(commandToCopy);
    if (didCopy) {
      trackUsage("willInstallPlugin");
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: "admin.pages.MarketPlacePage.plugin.copy.success"
        })
      });
    }
  };
  if (isInstalled) {
    return (0, import_jsx_runtime2.jsxs)(Flex, {
      gap: 2,
      paddingLeft: 4,
      children: [
        (0, import_jsx_runtime2.jsx)(ForwardRef$4F, {
          width: "1.2rem",
          height: "1.2rem",
          color: "success600"
        }),
        (0, import_jsx_runtime2.jsx)(Typography, {
          variant: "omega",
          textColor: "success600",
          fontWeight: "bold",
          children: formatMessage({
            id: "admin.pages.MarketPlacePage.plugin.installed",
            defaultMessage: "Installed"
          })
        })
      ]
    });
  }
  if (isInDevelopmentMode && isCompatible !== false) {
    return (0, import_jsx_runtime2.jsx)(CardButton, {
      strapiAppVersion,
      strapiPeerDepVersion,
      handleCopy,
      pluginName
    });
  }
  return null;
};
var CardButton = ({ strapiPeerDepVersion, strapiAppVersion, handleCopy, pluginName }) => {
  const { formatMessage } = useIntl();
  const versionRange = semver.validRange(strapiPeerDepVersion);
  const isCompatible = semver.satisfies(strapiAppVersion ?? "", versionRange ?? "");
  const installMessage = formatMessage({
    id: "admin.pages.MarketPlacePage.plugin.copy",
    defaultMessage: "Copy install command"
  });
  if (strapiAppVersion) {
    if (!versionRange || !isCompatible) {
      return (0, import_jsx_runtime2.jsx)(TooltipImpl, {
        "data-testid": `tooltip-${pluginName}`,
        label: formatMessage({
          id: "admin.pages.MarketPlacePage.plugin.version",
          defaultMessage: 'Update your Strapi version: "{strapiAppVersion}" to: "{versionRange}"'
        }, {
          strapiAppVersion,
          versionRange
        }),
        children: (0, import_jsx_runtime2.jsx)("span", {
          children: (0, import_jsx_runtime2.jsx)(Button, {
            size: "S",
            startIcon: (0, import_jsx_runtime2.jsx)(ForwardRef$3R, {}),
            variant: "secondary",
            onClick: handleCopy,
            disabled: !isCompatible,
            children: installMessage
          })
        })
      });
    }
  }
  return (0, import_jsx_runtime2.jsx)(Button, {
    size: "S",
    startIcon: (0, import_jsx_runtime2.jsx)(ForwardRef$3R, {}),
    variant: "secondary",
    onClick: handleCopy,
    children: installMessage
  });
};
var PackageStats = ({ githubStars = 0, npmDownloads = 0, npmPackageType }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime2.jsxs)(Flex, {
    gap: 1,
    children: [
      !!githubStars && (0, import_jsx_runtime2.jsxs)(import_jsx_runtime2.Fragment, {
        children: [
          (0, import_jsx_runtime2.jsx)(ForwardRef$x, {
            height: "1.2rem",
            width: "1.2rem",
            "aria-hidden": true
          }),
          (0, import_jsx_runtime2.jsx)(ForwardRef$D, {
            height: "1.2rem",
            width: "1.2rem",
            fill: "warning500",
            "aria-hidden": true
          }),
          (0, import_jsx_runtime2.jsx)("p", {
            "aria-label": formatMessage({
              id: `admin.pages.MarketPlacePage.${npmPackageType}.githubStars`,
              defaultMessage: `This {package} was starred {starsCount} on GitHub`
            }, {
              starsCount: githubStars,
              package: npmPackageType
            }),
            children: (0, import_jsx_runtime2.jsx)(Typography, {
              variant: "pi",
              textColor: "neutral800",
              children: githubStars
            })
          }),
          (0, import_jsx_runtime2.jsx)(VerticalDivider, {})
        ]
      }),
      (0, import_jsx_runtime2.jsx)(ForwardRef$3V, {
        height: "1.2rem",
        width: "1.2rem",
        "aria-hidden": true
      }),
      (0, import_jsx_runtime2.jsx)("p", {
        "aria-label": formatMessage({
          id: `admin.pages.MarketPlacePage.${npmPackageType}.downloads`,
          defaultMessage: `This {package} has {downloadsCount} weekly downloads`
        }, {
          downloadsCount: npmDownloads,
          package: npmPackageType
        }),
        children: (0, import_jsx_runtime2.jsx)(Typography, {
          variant: "pi",
          textColor: "neutral800",
          children: npmDownloads
        })
      })
    ]
  });
};
var VerticalDivider = dt(Divider)`
  width: 1.2rem;
  transform: rotate(90deg);
`;

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Marketplace/components/NpmPackagesGrid.mjs
var NpmPackagesGrid = ({ status, npmPackages = [], installedPackageNames = [], useYarn, isInDevelopmentMode, npmPackageType, strapiAppVersion, debouncedSearch }) => {
  const { formatMessage } = useIntl();
  if (status === "error") {
    return (0, import_jsx_runtime3.jsx)(Page.Error, {});
  }
  if (status === "loading") {
    return (0, import_jsx_runtime3.jsx)(Page.Loading, {});
  }
  const emptySearchMessage = formatMessage({
    id: "admin.pages.MarketPlacePage.search.empty",
    defaultMessage: 'No result for "{target}"'
  }, {
    target: debouncedSearch
  });
  if (npmPackages.length === 0) {
    return (0, import_jsx_runtime3.jsxs)(Box, {
      position: "relative",
      children: [
        (0, import_jsx_runtime3.jsx)(Layouts.Grid, {
          size: "M",
          children: Array(12).fill(null).map((_, idx) => (0, import_jsx_runtime3.jsx)(EmptyPluginCard, {
            height: "234px",
            hasRadius: true
          }, idx))
        }),
        (0, import_jsx_runtime3.jsx)(Box, {
          position: "absolute",
          top: 11,
          width: "100%",
          children: (0, import_jsx_runtime3.jsxs)(Flex, {
            alignItems: "center",
            justifyContent: "center",
            direction: "column",
            children: [
              (0, import_jsx_runtime3.jsx)(ForwardRef$J, {
                width: "160px",
                height: "88px"
              }),
              (0, import_jsx_runtime3.jsx)(Box, {
                paddingTop: 6,
                children: (0, import_jsx_runtime3.jsx)(Typography, {
                  variant: "delta",
                  tag: "p",
                  textColor: "neutral600",
                  children: emptySearchMessage
                })
              })
            ]
          })
        })
      ]
    });
  }
  return (0, import_jsx_runtime3.jsx)(Grid.Root, {
    gap: 4,
    children: npmPackages.map((npmPackage) => (0, import_jsx_runtime3.jsx)(Grid.Item, {
      col: 4,
      s: 6,
      xs: 12,
      style: {
        height: "100%"
      },
      direction: "column",
      alignItems: "stretch",
      children: (0, import_jsx_runtime3.jsx)(NpmPackageCard, {
        npmPackage,
        isInstalled: installedPackageNames.includes(npmPackage.attributes.npmPackageName),
        useYarn,
        isInDevelopmentMode,
        npmPackageType,
        strapiAppVersion
      })
    }, npmPackage.id))
  });
};
var EmptyPluginCard = dt(Box)`
  background: ${({ theme }) => `linear-gradient(180deg, rgba(234, 234, 239, 0) 0%, ${theme.colors.neutral150} 100%)`};
  opacity: 0.33;
`;

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Marketplace/components/OfflineLayout.mjs
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Marketplace/components/PageHeader.mjs
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var PageHeader = ({ isOnline, npmPackageType = "plugin" }) => {
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const tracking = npmPackageType === "provider" ? "didSubmitProvider" : "didSubmitPlugin";
  return (0, import_jsx_runtime4.jsx)(Layouts.Header, {
    title: formatMessage({
      id: "global.marketplace",
      defaultMessage: "Marketplace"
    }),
    subtitle: formatMessage({
      id: "admin.pages.MarketPlacePage.subtitle",
      defaultMessage: "Get more out of Strapi"
    }),
    primaryAction: isOnline && (0, import_jsx_runtime4.jsx)(LinkButton, {
      startIcon: (0, import_jsx_runtime4.jsx)(ForwardRef$d, {}),
      variant: "tertiary",
      href: `https://market.strapi.io/submit-${npmPackageType}`,
      onClick: () => trackUsage(tracking),
      isExternal: true,
      children: formatMessage({
        id: `admin.pages.MarketPlacePage.submit.${npmPackageType}.link`,
        defaultMessage: `Submit ${npmPackageType}`
      })
    })
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Marketplace/components/OfflineLayout.mjs
var OfflineLayout = () => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime5.jsx)(Layouts.Root, {
    children: (0, import_jsx_runtime5.jsxs)(Main, {
      children: [
        (0, import_jsx_runtime5.jsx)(PageHeader, {}),
        (0, import_jsx_runtime5.jsxs)(Flex, {
          width: "100%",
          direction: "column",
          alignItems: "center",
          justifyContent: "center",
          paddingTop: `12rem`,
          children: [
            (0, import_jsx_runtime5.jsx)(Box, {
              paddingBottom: 2,
              children: (0, import_jsx_runtime5.jsx)(Typography, {
                textColor: "neutral700",
                variant: "alpha",
                children: formatMessage({
                  id: "admin.pages.MarketPlacePage.offline.title",
                  defaultMessage: "You are offline"
                })
              })
            }),
            (0, import_jsx_runtime5.jsx)(Box, {
              paddingBottom: 6,
              children: (0, import_jsx_runtime5.jsx)(Typography, {
                textColor: "neutral700",
                variant: "epsilon",
                children: formatMessage({
                  id: "admin.pages.MarketPlacePage.offline.subtitle",
                  defaultMessage: "You need to be connected to the Internet to access Strapi Market."
                })
              })
            }),
            (0, import_jsx_runtime5.jsxs)("svg", {
              width: "88",
              height: "88",
              viewBox: "0 0 88 88",
              fill: "none",
              xmlns: "http://www.w3.org/2000/svg",
              children: [
                (0, import_jsx_runtime5.jsx)("rect", {
                  x: ".5",
                  y: ".5",
                  width: "87",
                  height: "87",
                  rx: "43.5",
                  fill: "#F0F0FF"
                }),
                (0, import_jsx_runtime5.jsx)("path", {
                  d: "M34 39.3h-4c-2.6 0-4.7 1-6.6 2.8a9 9 0 0 0-2.7 6.6 9 9 0 0 0 2.7 6.6A9 9 0 0 0 30 58h22.8L34 39.3Zm-11-11 3-3 39 39-3 3-4.7-4.6H30a13.8 13.8 0 0 1-14-14c0-3.8 1.3-7 4-9.7 2.6-2.7 5.7-4.2 9.5-4.3L23 28.2Zm38.2 11.1c3 .2 5.5 1.5 7.6 3.7A11 11 0 0 1 72 51c0 4-1.6 7.2-5 9.5l-3.3-3.4a6.5 6.5 0 0 0 3.6-6.1c0-1.9-.7-3.5-2-5-1.5-1.3-3.1-2-5-2h-3.5v-1.2c0-3.6-1.2-6.6-3.7-9a13 13 0 0 0-15-2.3L34.6 28a17 17 0 0 1 20.3 1.5c3.5 2.7 5.5 6 6.3 10Z",
                  fill: "#4945FF"
                }),
                (0, import_jsx_runtime5.jsx)("rect", {
                  x: ".5",
                  y: ".5",
                  width: "87",
                  height: "87",
                  rx: "43.5",
                  stroke: "#D9D8FF"
                })
              ]
            })
          ]
        })
      ]
    })
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Marketplace/components/SortSelect.mjs
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var SORT_TYPES = {
  "name:asc": {
    selected: {
      id: "admin.pages.MarketPlacePage.sort.alphabetical.selected",
      defaultMessage: "Sort by alphabetical order"
    },
    option: {
      id: "admin.pages.MarketPlacePage.sort.alphabetical",
      defaultMessage: "Alphabetical order"
    }
  },
  "submissionDate:desc": {
    selected: {
      id: "admin.pages.MarketPlacePage.sort.newest.selected",
      defaultMessage: "Sort by newest"
    },
    option: {
      id: "admin.pages.MarketPlacePage.sort.newest",
      defaultMessage: "Newest"
    }
  },
  "githubStars:desc": {
    selected: {
      id: "admin.pages.MarketPlacePage.sort.githubStars.selected",
      defaultMessage: "Sort by GitHub stars"
    },
    option: {
      id: "admin.pages.MarketPlacePage.sort.githubStars",
      defaultMessage: "Number of GitHub stars"
    }
  },
  "npmDownloads:desc": {
    selected: {
      id: "admin.pages.MarketPlacePage.sort.npmDownloads.selected",
      defaultMessage: "Sort by npm downloads"
    },
    option: {
      id: "admin.pages.MarketPlacePage.sort.npmDownloads",
      defaultMessage: "Number of downloads"
    }
  }
};
var SortSelect = ({ sortQuery, handleSelectChange }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime6.jsx)(SelectWrapper, {
    children: (0, import_jsx_runtime6.jsx)(SingleSelect, {
      value: sortQuery,
      customizeContent: () => formatMessage(SORT_TYPES[sortQuery].selected),
      onChange: (sortName) => {
        handleSelectChange({
          sort: sortName
        });
      },
      "aria-label": formatMessage({
        id: "admin.pages.MarketPlacePage.sort.label",
        defaultMessage: "Sort by"
      }),
      size: "S",
      children: Object.entries(SORT_TYPES).map(([sortName, messages]) => {
        return (0, import_jsx_runtime6.jsx)(SingleSelectOption, {
          value: sortName,
          children: formatMessage(messages.option)
        }, sortName);
      })
    })
  });
};
var SelectWrapper = dt(Box)`
  font-weight: ${({ theme }) => theme.fontWeights.semiBold};

  span {
    font-size: ${({ theme }) => theme.fontSizes[1]};
  }
`;

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Marketplace/hooks/useMarketplaceData.mjs
var qs = __toESM(require_lib(), 1);
var MARKETPLACE_API_URL = "https://market-api.strapi.io";
function useMarketplaceData({ npmPackageType, debouncedSearch, query, tabQuery, strapiVersion }) {
  const { notifyStatus } = useNotifyAT();
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const marketplaceTitle = formatMessage({
    id: "global.marketplace",
    defaultMessage: "Marketplace"
  });
  const notifyMarketplaceLoad = () => {
    notifyStatus(formatMessage({
      id: "app.utils.notify.data-loaded",
      defaultMessage: "The {target} has loaded"
    }, {
      target: marketplaceTitle
    }));
  };
  const paginationParams = {
    page: (query == null ? void 0 : query.page) || 1,
    pageSize: (query == null ? void 0 : query.pageSize) || 24
  };
  const pluginParams = {
    ...tabQuery.plugin,
    pagination: paginationParams,
    search: debouncedSearch,
    version: strapiVersion
  };
  const { data: pluginsResponse, status: pluginsStatus } = useQuery([
    "marketplace",
    "plugins",
    pluginParams
  ], async () => {
    try {
      const queryString = qs.stringify(pluginParams);
      const res = await fetch(`${MARKETPLACE_API_URL}/plugins?${queryString}`);
      if (!res.ok) {
        throw new Error("Failed to fetch marketplace plugins.");
      }
      const data = await res.json();
      return data;
    } catch (error) {
    }
    return null;
  }, {
    onSuccess() {
      notifyMarketplaceLoad();
    },
    onError() {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "An error occured"
        })
      });
    }
  });
  const providerParams = {
    ...tabQuery.provider,
    pagination: paginationParams,
    search: debouncedSearch,
    version: strapiVersion
  };
  const { data: providersResponse, status: providersStatus } = useQuery([
    "marketplace",
    "providers",
    providerParams
  ], async () => {
    const queryString = qs.stringify(providerParams);
    const res = await fetch(`${MARKETPLACE_API_URL}/providers?${queryString}`);
    if (!res.ok) {
      throw new Error("Failed to fetch marketplace providers.");
    }
    const data = await res.json();
    return data;
  }, {
    onSuccess() {
      notifyMarketplaceLoad();
    },
    onError() {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "An error occured"
        })
      });
    }
  });
  const npmPackageTypeResponse = npmPackageType === "plugin" ? pluginsResponse : providersResponse;
  const possibleCollections = (npmPackageTypeResponse == null ? void 0 : npmPackageTypeResponse.meta.collections) ?? {};
  const possibleCategories = (pluginsResponse == null ? void 0 : pluginsResponse.meta.categories) ?? {};
  const { pagination } = (npmPackageTypeResponse == null ? void 0 : npmPackageTypeResponse.meta) ?? {};
  return {
    pluginsResponse,
    providersResponse,
    pluginsStatus,
    providersStatus,
    possibleCollections,
    possibleCategories,
    pagination
  };
}

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Marketplace/hooks/useNavigatorOnline.mjs
var React2 = __toESM(require_react(), 1);
var useNavigatorOnline = () => {
  const onlineStatus = typeof navigator !== "undefined" && typeof navigator.onLine === "boolean" ? navigator.onLine : true;
  const [isOnline, setIsOnline] = React2.useState(onlineStatus);
  const setOnline = () => setIsOnline(true);
  const setOffline = () => setIsOnline(false);
  React2.useEffect(() => {
    window.addEventListener("online", setOnline);
    window.addEventListener("offline", setOffline);
    return () => {
      window.removeEventListener("online", setOnline);
      window.removeEventListener("offline", setOffline);
    };
  }, []);
  return isOnline;
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Marketplace/MarketplacePage.mjs
var PLUGIN = "plugin";
var PROVIDER = "provider";
var MarketplacePage = () => {
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const { toggleNotification } = useNotification();
  const [{ query }, setQuery] = useQueryParams();
  const debouncedSearch = useDebounce(query == null ? void 0 : query.search, 500) || "";
  const { autoReload: isInDevelopmentMode, dependencies, useYarn, strapiVersion } = useAppInfo("MarketplacePage", (state) => state);
  const isOnline = useNavigatorOnline();
  const npmPackageType = (query == null ? void 0 : query.npmPackageType) || PLUGIN;
  const [tabQuery, setTabQuery] = React3.useState({
    plugin: npmPackageType === PLUGIN ? {
      ...query
    } : {},
    provider: npmPackageType === PROVIDER ? {
      ...query
    } : {}
  });
  React3.useEffect(() => {
    trackUsage("didGoToMarketplace");
  }, [
    trackUsage
  ]);
  React3.useEffect(() => {
    if (!isInDevelopmentMode) {
      toggleNotification({
        type: "info",
        message: formatMessage({
          id: "admin.pages.MarketPlacePage.production",
          defaultMessage: "Manage plugins from the development environment"
        })
      });
    }
  }, [
    toggleNotification,
    isInDevelopmentMode,
    formatMessage
  ]);
  const { pluginsResponse, providersResponse, pluginsStatus, providersStatus, possibleCollections, possibleCategories, pagination } = useMarketplaceData({
    npmPackageType,
    debouncedSearch,
    query,
    tabQuery,
    strapiVersion
  });
  if (!isOnline) {
    return (0, import_jsx_runtime7.jsx)(OfflineLayout, {});
  }
  const handleTabChange = (tab) => {
    const selectedTab = tab === PLUGIN || tab === PROVIDER ? tab : PLUGIN;
    const hasTabQuery = tabQuery[selectedTab] && Object.keys(tabQuery[selectedTab]).length;
    if (hasTabQuery) {
      setQuery({
        // Keep filters and search
        ...tabQuery[selectedTab],
        search: (query == null ? void 0 : query.search) || "",
        // Set tab and reset page
        npmPackageType: selectedTab,
        page: 1
      });
    } else {
      setQuery({
        // Set tab
        npmPackageType: selectedTab,
        // Clear filters
        collections: [],
        categories: [],
        sort: "name:asc",
        page: 1,
        // Keep search
        search: (query == null ? void 0 : query.search) || ""
      });
    }
  };
  const handleSelectChange = (update) => {
    setQuery({
      ...update,
      page: 1
    });
    setTabQuery((prev) => ({
      ...prev,
      [npmPackageType]: {
        ...prev[npmPackageType],
        ...update
      }
    }));
  };
  const handleSelectClear = (filterType) => {
    setQuery({
      [filterType]: [],
      page: void 0
    }, "remove");
    setTabQuery((prev) => ({
      ...prev,
      [npmPackageType]: {}
    }));
  };
  const handleSortSelectChange = ({ sort }) => (
    // @ts-expect-error - this is a narrowing issue.
    handleSelectChange({
      sort
    })
  );
  const installedPackageNames = Object.keys(dependencies ?? {});
  return (0, import_jsx_runtime7.jsx)(Layouts.Root, {
    children: (0, import_jsx_runtime7.jsxs)(Page.Main, {
      children: [
        (0, import_jsx_runtime7.jsx)(Page.Title, {
          children: formatMessage({
            id: "admin.pages.MarketPlacePage.head",
            defaultMessage: "Marketplace - Plugins"
          })
        }),
        (0, import_jsx_runtime7.jsx)(PageHeader, {
          isOnline,
          npmPackageType
        }),
        (0, import_jsx_runtime7.jsx)(Layouts.Content, {
          children: (0, import_jsx_runtime7.jsxs)(Tabs.Root, {
            variant: "simple",
            onValueChange: handleTabChange,
            value: npmPackageType,
            children: [
              (0, import_jsx_runtime7.jsxs)(Flex, {
                justifyContent: "space-between",
                paddingBottom: 4,
                children: [
                  (0, import_jsx_runtime7.jsxs)(Tabs.List, {
                    "aria-label": formatMessage({
                      id: "admin.pages.MarketPlacePage.tab-group.label",
                      defaultMessage: "Plugins and Providers for Strapi"
                    }),
                    children: [
                      (0, import_jsx_runtime7.jsxs)(Tabs.Trigger, {
                        value: PLUGIN,
                        children: [
                          formatMessage({
                            id: "admin.pages.MarketPlacePage.plugins",
                            defaultMessage: "Plugins"
                          }),
                          " ",
                          pluginsResponse ? `(${pluginsResponse.meta.pagination.total})` : "..."
                        ]
                      }),
                      (0, import_jsx_runtime7.jsxs)(Tabs.Trigger, {
                        value: PROVIDER,
                        children: [
                          formatMessage({
                            id: "admin.pages.MarketPlacePage.providers",
                            defaultMessage: "Providers"
                          }),
                          " ",
                          providersResponse ? `(${providersResponse.meta.pagination.total})` : "..."
                        ]
                      })
                    ]
                  }),
                  (0, import_jsx_runtime7.jsx)(Box, {
                    width: "25%",
                    children: (0, import_jsx_runtime7.jsx)(Searchbar, {
                      name: "searchbar",
                      onClear: () => setQuery({
                        search: "",
                        page: 1
                      }),
                      value: query == null ? void 0 : query.search,
                      onChange: (e) => setQuery({
                        search: e.target.value,
                        page: 1
                      }),
                      clearLabel: formatMessage({
                        id: "admin.pages.MarketPlacePage.search.clear",
                        defaultMessage: "Clear the search"
                      }),
                      placeholder: formatMessage({
                        id: "admin.pages.MarketPlacePage.search.placeholder",
                        defaultMessage: "Search"
                      }),
                      children: formatMessage({
                        id: "admin.pages.MarketPlacePage.search.placeholder",
                        defaultMessage: "Search"
                      })
                    })
                  })
                ]
              }),
              (0, import_jsx_runtime7.jsxs)(Flex, {
                paddingBottom: 4,
                gap: 2,
                children: [
                  (0, import_jsx_runtime7.jsx)(SortSelect, {
                    sortQuery: (query == null ? void 0 : query.sort) || "name:asc",
                    handleSelectChange: handleSortSelectChange
                  }),
                  (0, import_jsx_runtime7.jsx)(NpmPackagesFilters, {
                    npmPackageType,
                    possibleCollections,
                    possibleCategories,
                    query: query || {},
                    handleSelectChange,
                    handleSelectClear
                  })
                ]
              }),
              (0, import_jsx_runtime7.jsx)(Tabs.Content, {
                value: PLUGIN,
                children: (0, import_jsx_runtime7.jsx)(NpmPackagesGrid, {
                  npmPackages: pluginsResponse == null ? void 0 : pluginsResponse.data,
                  status: pluginsStatus,
                  installedPackageNames,
                  useYarn,
                  isInDevelopmentMode,
                  npmPackageType: "plugin",
                  strapiAppVersion: strapiVersion,
                  debouncedSearch
                })
              }),
              (0, import_jsx_runtime7.jsx)(Tabs.Content, {
                value: PROVIDER,
                children: (0, import_jsx_runtime7.jsx)(NpmPackagesGrid, {
                  npmPackages: providersResponse == null ? void 0 : providersResponse.data,
                  status: providersStatus,
                  installedPackageNames,
                  useYarn,
                  isInDevelopmentMode,
                  npmPackageType: "provider",
                  debouncedSearch
                })
              }),
              (0, import_jsx_runtime7.jsxs)(Pagination.Root, {
                ...pagination,
                defaultPageSize: 24,
                children: [
                  (0, import_jsx_runtime7.jsx)(Pagination.PageSize, {
                    options: [
                      "12",
                      "24",
                      "50",
                      "100"
                    ]
                  }),
                  (0, import_jsx_runtime7.jsx)(Pagination.Links, {})
                ]
              }),
              (0, import_jsx_runtime7.jsx)(Box, {
                paddingTop: 8,
                children: (0, import_jsx_runtime7.jsx)("a", {
                  href: "https://strapi.canny.io/plugin-requests",
                  target: "_blank",
                  rel: "noopener noreferrer nofollow",
                  style: {
                    textDecoration: "none"
                  },
                  onClick: () => trackUsage("didMissMarketplacePlugin"),
                  children: (0, import_jsx_runtime7.jsx)(ContentBox, {
                    title: formatMessage({
                      id: "admin.pages.MarketPlacePage.missingPlugin.title",
                      defaultMessage: "Documentation"
                    }),
                    subtitle: formatMessage({
                      id: "admin.pages.MarketPlacePage.missingPlugin.description",
                      defaultMessage: "Tell us what plugin you are looking for and we'll let our community plugin developers know in case they are in search for inspiration!"
                    }),
                    icon: (0, import_jsx_runtime7.jsx)(ForwardRef$v, {}),
                    iconBackground: "alternative100",
                    endAction: (0, import_jsx_runtime7.jsx)(ForwardRef$3F, {
                      fill: "neutral600",
                      width: "1.2rem",
                      height: "1.2rem",
                      style: {
                        marginLeft: "0.8rem"
                      }
                    })
                  })
                })
              })
            ]
          })
        })
      ]
    })
  });
};
var ProtectedMarketplacePage = () => {
  const permissions = useTypedSelector((state) => {
    var _a;
    return (_a = state.admin_app.permissions.marketplace) == null ? void 0 : _a.main;
  });
  return (0, import_jsx_runtime7.jsx)(Page.Protect, {
    permissions,
    children: (0, import_jsx_runtime7.jsx)(MarketplacePage, {})
  });
};
export {
  MarketplacePage,
  ProtectedMarketplacePage
};
//# sourceMappingURL=MarketplacePage-4ZUSMWUQ.js.map
