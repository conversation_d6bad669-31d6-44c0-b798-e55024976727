{"version": 3, "sources": ["../../../@strapi/review-workflows/admin/src/modules/hooks.ts"], "sourcesContent": ["import { Dispatch } from '@reduxjs/toolkit';\nimport { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';\n\nimport type { Store } from '@strapi/admin/strapi-admin';\n\ntype RootState = ReturnType<Store['getState']>;\n\nconst useTypedDispatch: () => Dispatch = useDispatch;\nconst useTypedSelector: TypedUseSelectorHook<RootState> = useSelector;\n\nexport { useTypedSelector, useTypedDispatch };\n"], "mappings": ";;;;;AAQA,IAAMA,mBAAoDC;", "names": ["useTypedSelector", "useSelector"]}