import {
  useAdminRoles
} from "./chunk-LDJANAVJ.js";
import {
  Permissions
} from "./chunk-PFR3HBO7.js";
import "./chunk-Y2KSLFI4.js";
import "./chunk-NE3KAGU6.js";
import "./chunk-YJEURQPS.js";
import "./chunk-DY2RJG3P.js";
import "./chunk-GGK2TLCV.js";
import "./chunk-K65KIEAL.js";
import "./chunk-BUDFB33L.js";
import {
  Formik
} from "./chunk-7MILHJ3J.js";
import "./chunk-SGQJOZK5.js";
import "./chunk-AFM2NWPO.js";
import {
  BackButton
} from "./chunk-DUGZ4WIW.js";
import {
  errorsTrads
} from "./chunk-IFOFBKTA.js";
import {
  create4 as create,
  create6 as create2
} from "./chunk-376QHLWZ.js";
import "./chunk-EGNP2T5O.js";
import {
  useTracking
} from "./chunk-XDCEA27D.js";
import "./chunk-EZSYDDUK.js";
import "./chunk-YXDCVYVT.js";
import {
  useGetRolePermissionLayoutQuery,
  useGetRolePermissionsQuery,
  useUpdateRoleMutation,
  useUpdateRolePermissionsMutation
} from "./chunk-6DRYEU2W.js";
import {
  Layouts
} from "./chunk-MTTHLNPH.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import {
  Page,
  useAPIErrorHandler
} from "./chunk-7LKLOY7A.js";
import "./chunk-ODQFI753.js";
import {
  useTypedSelector
} from "./chunk-ZOP4VV6J.js";
import {
  isBaseQueryError
} from "./chunk-WH6VCVXU.js";
import "./chunk-IL5G2D22.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-76QM3EFM.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  Box,
  Button,
  Field,
  Flex,
  Grid,
  Main,
  TextInput,
  Textarea,
  Typography,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  Navigate,
  useMatch
} from "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$4F
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/EditPage.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/components/RoleForm.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var RoleForm = ({ disabled, role, values, errors, onChange, onBlur }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsx)(Box, {
    background: "neutral0",
    padding: 6,
    shadow: "filterShadow",
    hasRadius: true,
    children: (0, import_jsx_runtime.jsxs)(Flex, {
      direction: "column",
      alignItems: "stretch",
      gap: 4,
      children: [
        (0, import_jsx_runtime.jsxs)(Flex, {
          justifyContent: "space-between",
          children: [
            (0, import_jsx_runtime.jsxs)(Box, {
              children: [
                (0, import_jsx_runtime.jsx)(Box, {
                  children: (0, import_jsx_runtime.jsx)(Typography, {
                    fontWeight: "bold",
                    children: role ? role.name : formatMessage({
                      id: "global.details",
                      defaultMessage: "Details"
                    })
                  })
                }),
                (0, import_jsx_runtime.jsx)(Box, {
                  children: (0, import_jsx_runtime.jsx)(Typography, {
                    textColor: "neutral500",
                    variant: "pi",
                    children: role ? role.description : formatMessage({
                      id: "Settings.roles.form.description",
                      defaultMessage: "Name and description of the role"
                    })
                  })
                })
              ]
            }),
            (0, import_jsx_runtime.jsx)(Button, {
              disabled: true,
              variant: "secondary",
              children: formatMessage({
                id: "Settings.roles.form.button.users-with-role",
                defaultMessage: "{number, plural, =0 {# users} one {# user} other {# users}} with this role"
              }, {
                number: role.usersCount
              })
            })
          ]
        }),
        (0, import_jsx_runtime.jsxs)(Grid.Root, {
          gap: 4,
          children: [
            (0, import_jsx_runtime.jsx)(Grid.Item, {
              col: 6,
              direction: "column",
              alignItems: "stretch",
              children: (0, import_jsx_runtime.jsxs)(Field.Root, {
                name: "name",
                error: errors.name && formatMessage({
                  id: errors.name
                }),
                required: true,
                children: [
                  (0, import_jsx_runtime.jsx)(Field.Label, {
                    children: formatMessage({
                      id: "global.name",
                      defaultMessage: "Name"
                    })
                  }),
                  (0, import_jsx_runtime.jsx)(TextInput, {
                    disabled,
                    onChange,
                    onBlur,
                    value: values.name || ""
                  }),
                  (0, import_jsx_runtime.jsx)(Field.Error, {})
                ]
              })
            }),
            (0, import_jsx_runtime.jsx)(Grid.Item, {
              col: 6,
              direction: "column",
              alignItems: "stretch",
              children: (0, import_jsx_runtime.jsxs)(Field.Root, {
                name: "description",
                error: errors.description && formatMessage({
                  id: errors.description
                }),
                children: [
                  (0, import_jsx_runtime.jsx)(Field.Label, {
                    children: formatMessage({
                      id: "global.description",
                      defaultMessage: "Description"
                    })
                  }),
                  (0, import_jsx_runtime.jsx)(Textarea, {
                    disabled,
                    onChange,
                    onBlur,
                    value: values.description
                  }),
                  (0, import_jsx_runtime.jsx)(Field.Error, {})
                ]
              })
            })
          ]
        })
      ]
    })
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/EditPage.mjs
var EDIT_ROLE_SCHEMA = create2().shape({
  name: create().required(errorsTrads.required.id),
  description: create().optional()
});
var EditPage = () => {
  const { toggleNotification } = useNotification();
  const { formatMessage } = useIntl();
  const match = useMatch("/settings/roles/:id");
  const id = match == null ? void 0 : match.params.id;
  const permissionsRef = React.useRef(null);
  const { trackUsage } = useTracking();
  const { _unstableFormatAPIError: formatAPIError, _unstableFormatValidationErrors: formatValidationErrors } = useAPIErrorHandler();
  const { isLoading: isLoadingPermissionsLayout, data: permissionsLayout } = useGetRolePermissionLayoutQuery({
    /**
    * Role here is a query param so if there's no role we pass an empty string
    * which returns us a default layout.
    */
    role: id ?? ""
  });
  const { roles, isLoading: isRoleLoading, refetch: refetchRole } = useAdminRoles({
    id
  }, {
    refetchOnMountOrArgChange: true
  });
  const role = roles[0] ?? {};
  const { data: permissions, isLoading: isLoadingPermissions } = useGetRolePermissionsQuery({
    id
  }, {
    skip: !id,
    refetchOnMountOrArgChange: true
  });
  const [updateRole] = useUpdateRoleMutation();
  const [updateRolePermissions] = useUpdateRolePermissionsMutation();
  if (!id) {
    return (0, import_jsx_runtime2.jsx)(Navigate, {
      to: "/settings/roles"
    });
  }
  const handleEditRoleSubmit = async (data, formik) => {
    var _a, _b;
    try {
      const { permissionsToSend, didUpdateConditions } = ((_a = permissionsRef.current) == null ? void 0 : _a.getPermissions()) ?? {};
      const res = await updateRole({
        id,
        ...data
      });
      if ("error" in res) {
        if (isBaseQueryError(res.error) && res.error.name === "ValidationError") {
          formik.setErrors(formatValidationErrors(res.error));
        } else {
          toggleNotification({
            type: "danger",
            message: formatAPIError(res.error)
          });
        }
        return;
      }
      if (role.code !== "strapi-super-admin" && permissionsToSend) {
        const updateRes = await updateRolePermissions({
          id: res.data.id,
          permissions: permissionsToSend
        });
        if ("error" in updateRes) {
          if (isBaseQueryError(updateRes.error) && updateRes.error.name === "ValidationError") {
            formik.setErrors(formatValidationErrors(updateRes.error));
          } else {
            toggleNotification({
              type: "danger",
              message: formatAPIError(updateRes.error)
            });
          }
          return;
        }
        if (didUpdateConditions) {
          trackUsage("didUpdateConditions");
        }
      }
      (_b = permissionsRef.current) == null ? void 0 : _b.setFormAfterSubmit();
      await refetchRole();
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: "notification.success.saved"
        })
      });
    } catch (error) {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "An error occurred"
        })
      });
    }
  };
  const isFormDisabled = !isRoleLoading && role.code === "strapi-super-admin";
  if (isLoadingPermissionsLayout || isRoleLoading || isLoadingPermissions || !permissionsLayout) {
    return (0, import_jsx_runtime2.jsx)(Page.Loading, {});
  }
  return (0, import_jsx_runtime2.jsxs)(Main, {
    children: [
      (0, import_jsx_runtime2.jsx)(Page.Title, {
        children: formatMessage({
          id: "Settings.PageTitle",
          defaultMessage: "Settings - {name}"
        }, {
          name: "Roles"
        })
      }),
      (0, import_jsx_runtime2.jsx)(Formik, {
        enableReinitialize: true,
        initialValues: {
          name: role.name ?? "",
          description: role.description ?? ""
        },
        onSubmit: handleEditRoleSubmit,
        validationSchema: EDIT_ROLE_SCHEMA,
        validateOnChange: false,
        children: ({ handleSubmit, values, errors, handleChange, handleBlur, isSubmitting }) => (0, import_jsx_runtime2.jsxs)("form", {
          onSubmit: handleSubmit,
          children: [
            (0, import_jsx_runtime2.jsx)(Layouts.Header, {
              primaryAction: (0, import_jsx_runtime2.jsx)(Flex, {
                gap: 2,
                children: (0, import_jsx_runtime2.jsx)(Button, {
                  type: "submit",
                  startIcon: (0, import_jsx_runtime2.jsx)(ForwardRef$4F, {}),
                  disabled: role.code === "strapi-super-admin",
                  loading: isSubmitting,
                  children: formatMessage({
                    id: "global.save",
                    defaultMessage: "Save"
                  })
                })
              }),
              title: formatMessage({
                id: "Settings.roles.edit.title",
                defaultMessage: "Edit a role"
              }),
              subtitle: formatMessage({
                id: "Settings.roles.create.description",
                defaultMessage: "Define the rights given to the role"
              }),
              navigationAction: (0, import_jsx_runtime2.jsx)(BackButton, {
                fallback: "../roles"
              })
            }),
            (0, import_jsx_runtime2.jsx)(Layouts.Content, {
              children: (0, import_jsx_runtime2.jsxs)(Flex, {
                direction: "column",
                alignItems: "stretch",
                gap: 6,
                children: [
                  (0, import_jsx_runtime2.jsx)(RoleForm, {
                    disabled: isFormDisabled,
                    errors,
                    values,
                    onChange: handleChange,
                    onBlur: handleBlur,
                    role
                  }),
                  (0, import_jsx_runtime2.jsx)(Box, {
                    shadow: "filterShadow",
                    hasRadius: true,
                    children: (0, import_jsx_runtime2.jsx)(Permissions, {
                      isFormDisabled,
                      permissions,
                      ref: permissionsRef,
                      layout: permissionsLayout
                    })
                  })
                ]
              })
            })
          ]
        })
      })
    ]
  });
};
var ProtectedEditPage = () => {
  const permissions = useTypedSelector((state) => {
    var _a;
    return (_a = state.admin_app.permissions.settings) == null ? void 0 : _a.roles.update;
  });
  return (0, import_jsx_runtime2.jsx)(Page.Protect, {
    permissions,
    children: (0, import_jsx_runtime2.jsx)(EditPage, {})
  });
};
export {
  EditPage,
  ProtectedEditPage
};
//# sourceMappingURL=EditPage-B3K6FUQO.js.map
