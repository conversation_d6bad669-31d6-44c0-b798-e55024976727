import {
  errorsTrads
} from "./chunk-IFOFBKTA.js";
import {
  create,
  create4 as create2
} from "./chunk-376QHLWZ.js";

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Users/<USER>/validation.mjs
var COMMON_USER_SCHEMA = {
  firstname: create2().trim().required({
    id: errorsTrads.required.id,
    defaultMessage: "This field is required"
  }),
  lastname: create2().nullable(),
  email: create2().email(errorsTrads.email).lowercase().required({
    id: errorsTrads.required.id,
    defaultMessage: "This field is required"
  }),
  username: create2().transform((value) => value === "" ? void 0 : value).nullable(),
  password: create2().transform((value) => value === "" || value === null ? void 0 : value).nullable().min(8, {
    ...errorsTrads.minLength,
    values: {
      min: 8
    }
  }).test("max-bytes", {
    id: "components.Input.error.contain.maxBytes",
    defaultMessage: "Password must be less than 73 bytes"
  }, function(value) {
    if (!value) return true;
    return new TextEncoder().encode(value).length <= 72;
  }).matches(/[a-z]/, {
    id: "components.Input.error.contain.lowercase",
    defaultMessage: "Password must contain at least one lowercase character"
  }).matches(/[A-Z]/, {
    id: "components.Input.error.contain.uppercase",
    defaultMessage: "Password must contain at least one uppercase character"
  }).matches(/\d/, {
    id: "components.Input.error.contain.number",
    defaultMessage: "Password must contain at least one number"
  }),
  confirmPassword: create2().transform((value) => value === "" ? null : value).nullable().min(8, {
    ...errorsTrads.minLength,
    values: {
      min: 8
    }
  }).oneOf([
    create("password"),
    null
  ], {
    id: "components.Input.error.password.noMatch",
    defaultMessage: "Passwords must match"
  }).when("password", (password, passSchema) => {
    return password ? passSchema.required({
      id: errorsTrads.required.id,
      defaultMessage: "This field is required"
    }).nullable() : passSchema;
  })
};

export {
  COMMON_USER_SCHEMA
};
//# sourceMappingURL=chunk-AHWGSUAE.js.map
