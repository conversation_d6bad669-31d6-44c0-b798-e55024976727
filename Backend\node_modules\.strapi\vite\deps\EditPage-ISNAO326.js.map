{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/Users/<USER>"], "sourcesContent": ["import * as React from 'react';\n\nimport { Box, Button, Flex, Grid, Typography } from '@strapi/design-system';\nimport { Check } from '@strapi/icons';\nimport pick from 'lodash/pick';\nimport { useIntl } from 'react-intl';\nimport { useMatch, useNavigate } from 'react-router-dom';\nimport * as yup from 'yup';\n\nimport { Update } from '../../../../../../shared/contracts/user';\nimport { Form, FormHelpers } from '../../../../components/Form';\nimport { InputRenderer } from '../../../../components/FormInputs/Renderer';\nimport { Layouts } from '../../../../components/Layouts/Layout';\nimport { Page } from '../../../../components/PageHelpers';\nimport { useTypedSelector } from '../../../../core/store/hooks';\nimport { BackButton } from '../../../../features/BackButton';\nimport { useNotification } from '../../../../features/Notifications';\nimport { useAPIErrorHandler } from '../../../../hooks/useAPIErrorHandler';\nimport { useEnterprise } from '../../../../hooks/useEnterprise';\nimport { useRBAC } from '../../../../hooks/useRBAC';\nimport { selectAdminPermissions } from '../../../../selectors';\nimport { useAdminUsers, useUpdateUserMutation } from '../../../../services/users';\nimport { isBaseQueryError } from '../../../../utils/baseQuery';\nimport { translatedErrors } from '../../../../utils/translatedErrors';\nimport { getDisplayName } from '../../../../utils/users';\n\nimport { MagicLinkCE } from './components/MagicLinkCE';\nimport { SelectRoles } from './components/SelectRoles';\nimport { COMMON_USER_SCHEMA } from './utils/validation';\n\nconst EDIT_VALIDATION_SCHEMA = yup.object().shape({\n  ...COMMON_USER_SCHEMA,\n  isActive: yup.bool(),\n  roles: yup\n    .array()\n    .min(1, {\n      id: translatedErrors.required.id,\n      defaultMessage: 'This field is required',\n    })\n    .required({\n      id: translatedErrors.required.id,\n      defaultMessage: 'This field is required',\n    }),\n});\n\nconst fieldsToPick = ['email', 'firstname', 'lastname', 'username', 'isActive', 'roles'] as const;\n\n/* -------------------------------------------------------------------------------------------------\n * EditPage\n * -----------------------------------------------------------------------------------------------*/\n\nconst EditPage = () => {\n  const { formatMessage } = useIntl();\n  const match = useMatch('/settings/users/:id');\n  const id = match?.params?.id ?? '';\n  const navigate = useNavigate();\n  const { toggleNotification } = useNotification();\n  const MagicLink = useEnterprise(\n    MagicLinkCE,\n    async () =>\n      (\n        await import(\n          '../../../../../../ee/admin/src/pages/SettingsPage/pages/Users/<USER>/MagicLinkEE'\n        )\n      ).MagicLinkEE\n  );\n  const {\n    _unstableFormatAPIError: formatAPIError,\n    _unstableFormatValidationErrors: formatValidationErrors,\n  } = useAPIErrorHandler();\n\n  const permissions = useTypedSelector(selectAdminPermissions);\n\n  const {\n    isLoading: isLoadingRBAC,\n    allowedActions: { canUpdate },\n  } = useRBAC({\n    read: permissions.settings?.users.read ?? [],\n    update: permissions.settings?.users.update ?? [],\n  });\n\n  const [updateUser] = useUpdateUserMutation();\n\n  const {\n    data,\n    error,\n    isLoading: isLoadingAdminUsers,\n  } = useAdminUsers(\n    { id },\n    {\n      refetchOnMountOrArgChange: true,\n    }\n  );\n\n  const [user] = data?.users ?? [];\n\n  React.useEffect(() => {\n    if (error) {\n      // Redirect the user to the homepage if is not allowed to read\n      if (error.name === 'UnauthorizedError') {\n        toggleNotification({\n          type: 'info',\n          message: formatMessage({\n            id: 'notification.permission.not-allowed-read',\n            defaultMessage: 'You are not allowed to see this document',\n          }),\n        });\n\n        navigate('/');\n      } else {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(error),\n        });\n      }\n    }\n  }, [error, formatAPIError, formatMessage, navigate, toggleNotification]);\n\n  const isLoading = isLoadingAdminUsers || !MagicLink || isLoadingRBAC;\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  type InitialData = Pick<Update.Request['body'], (typeof fieldsToPick)[number]> & {\n    confirmPassword: string;\n    password: string;\n  };\n\n  const initialData = {\n    ...pick(user, fieldsToPick),\n    roles: user.roles.map(({ id }) => id),\n    password: '',\n    confirmPassword: '',\n  } satisfies InitialData;\n\n  const handleSubmit = async (body: InitialData, actions: FormHelpers<InitialData>) => {\n    const { confirmPassword: _confirmPassword, ...bodyRest } = body;\n\n    const res = await updateUser({\n      id,\n      ...bodyRest,\n    });\n\n    if ('error' in res && isBaseQueryError(res.error)) {\n      if (res.error.name === 'ValidationError') {\n        actions.setErrors(formatValidationErrors(res.error));\n      }\n\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(res.error),\n      });\n    } else {\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({ id: 'notification.success.saved', defaultMessage: 'Saved' }),\n      });\n\n      actions.setValues({\n        ...pick(body, fieldsToPick),\n        password: '',\n        confirmPassword: '',\n      });\n    }\n  };\n\n  return (\n    <Page.Main>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: 'Users',\n          }\n        )}\n      </Page.Title>\n      <Form\n        method=\"PUT\"\n        onSubmit={handleSubmit}\n        initialValues={initialData}\n        validationSchema={EDIT_VALIDATION_SCHEMA}\n      >\n        {({ isSubmitting, modified }) => {\n          return (\n            <>\n              <Layouts.Header\n                primaryAction={\n                  <Button\n                    disabled={isSubmitting || !canUpdate || !modified}\n                    startIcon={<Check />}\n                    loading={isSubmitting}\n                    type=\"submit\"\n                  >\n                    {formatMessage({ id: 'global.save', defaultMessage: 'Save' })}\n                  </Button>\n                }\n                title={formatMessage(\n                  {\n                    id: 'app.containers.Users.EditPage.header.label',\n                    defaultMessage: 'Edit {name}',\n                  },\n                  {\n                    // @ts-expect-error – issues with the Entity ID type, still.\n                    name: getDisplayName(initialData),\n                  }\n                )}\n                navigationAction={<BackButton fallback=\"../users\" />}\n              />\n              <Layouts.Content>\n                {user?.registrationToken && (\n                  <Box paddingBottom={6}>\n                    <MagicLink registrationToken={user.registrationToken} />\n                  </Box>\n                )}\n                <Flex direction=\"column\" alignItems=\"stretch\" gap={7}>\n                  <Box\n                    background=\"neutral0\"\n                    hasRadius\n                    shadow=\"filterShadow\"\n                    paddingTop={6}\n                    paddingBottom={6}\n                    paddingLeft={7}\n                    paddingRight={7}\n                  >\n                    <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n                      <Typography variant=\"delta\" tag=\"h2\">\n                        {formatMessage({\n                          id: 'app.components.Users.ModalCreateBody.block-title.details',\n                          defaultMessage: 'Details',\n                        })}\n                      </Typography>\n                      <Grid.Root gap={5}>\n                        {LAYOUT.map((row) =>\n                          row.map(({ size, label, ...field }) => {\n                            return (\n                              <Grid.Item\n                                key={field.name}\n                                col={size}\n                                direction=\"column\"\n                                alignItems=\"stretch\"\n                              >\n                                <InputRenderer\n                                  {...field}\n                                  disabled={!canUpdate}\n                                  label={formatMessage(label)}\n                                  placeholder={\n                                    'placeholder' in field\n                                      ? formatMessage(field.placeholder)\n                                      : undefined\n                                  }\n                                />\n                              </Grid.Item>\n                            );\n                          })\n                        )}\n                      </Grid.Root>\n                    </Flex>\n                  </Box>\n                  <Box\n                    background=\"neutral0\"\n                    hasRadius\n                    shadow=\"filterShadow\"\n                    paddingTop={6}\n                    paddingBottom={6}\n                    paddingLeft={7}\n                    paddingRight={7}\n                  >\n                    <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n                      <Typography variant=\"delta\" tag=\"h2\">\n                        {formatMessage({\n                          id: 'global.roles',\n                          defaultMessage: \"User's role\",\n                        })}\n                      </Typography>\n                      <Grid.Root gap={5}>\n                        <Grid.Item col={6} xs={12} direction=\"column\" alignItems=\"stretch\">\n                          <SelectRoles disabled={!canUpdate} />\n                        </Grid.Item>\n                      </Grid.Root>\n                    </Flex>\n                  </Box>\n                </Flex>\n              </Layouts.Content>\n            </>\n          );\n        }}\n      </Form>\n    </Page.Main>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * EditPage LAYOUT\n * -----------------------------------------------------------------------------------------------*/\n\nconst LAYOUT = [\n  [\n    {\n      label: {\n        id: 'Auth.form.firstname.label',\n        defaultMessage: 'First name',\n      },\n      name: 'firstname',\n      placeholder: {\n        id: 'Auth.form.firstname.placeholder',\n        defaultMessage: 'e.g. Kai',\n      },\n      type: 'string' as const,\n      size: 6,\n      required: true,\n    },\n    {\n      label: {\n        id: 'Auth.form.lastname.label',\n        defaultMessage: 'Last name',\n      },\n      name: 'lastname',\n      placeholder: {\n        id: 'Auth.form.lastname.placeholder',\n        defaultMessage: 'e.g. Doe',\n      },\n      type: 'string' as const,\n      size: 6,\n    },\n  ],\n  [\n    {\n      label: {\n        id: 'Auth.form.email.label',\n        defaultMessage: 'Email',\n      },\n      name: 'email',\n      placeholder: {\n        id: 'Auth.form.email.placeholder',\n        defaultMessage: 'e.g. <EMAIL>',\n      },\n      type: 'email' as const,\n      size: 6,\n      required: true,\n    },\n    {\n      label: {\n        id: 'Auth.form.username.label',\n        defaultMessage: 'Username',\n      },\n      name: 'username',\n      placeholder: {\n        id: 'Auth.form.username.placeholder',\n        defaultMessage: 'e.g. Kai_Doe',\n      },\n      type: 'string' as const,\n      size: 6,\n    },\n  ],\n  [\n    {\n      autoComplete: 'new-password',\n      label: {\n        id: 'global.password',\n        defaultMessage: 'Password',\n      },\n      name: 'password',\n      type: 'password' as const,\n      size: 6,\n    },\n    {\n      autoComplete: 'new-password',\n      label: {\n        id: 'Auth.form.confirmPassword.label',\n        defaultMessage: 'Password confirmation',\n      },\n      name: 'confirmPassword',\n      type: 'password' as const,\n      size: 6,\n    },\n  ],\n  [\n    {\n      label: {\n        id: 'Auth.form.active.label',\n        defaultMessage: 'Active',\n      },\n      name: 'isActive',\n      type: 'boolean' as const,\n      size: 6,\n    },\n  ],\n];\n\nconst ProtectedEditPage = () => {\n  const permissions = useTypedSelector((state) => state.admin_app.permissions.settings?.users.read);\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <EditPage />\n    </Page.Protect>\n  );\n};\n\nexport { EditPage, ProtectedEditPage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,IAAMA,yBAA6BC,QAAM,EAAGC,MAAM;EAChD,GAAGC;EACHC,UAAcC,OAAI;EAClBC,OACGC,QAAK,EACLC,IAAI,GAAG;IACNC,IAAIC,YAAiBC,SAASF;IAC9BG,gBAAgB;EAClB,CAAA,EACCD,SAAS;IACRF,IAAIC,YAAiBC,SAASF;IAC9BG,gBAAgB;EAClB,CAAA;AACJ,CAAA;AAEA,IAAMC,eAAe;EAAC;EAAS;EAAa;EAAY;EAAY;EAAY;AAAQ;AAIU,IAE5FC,WAAW,MAAA;;AACf,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAMC,QAAQC,SAAS,qBAAA;AACvB,QAAMT,OAAKQ,oCAAOE,WAAPF,mBAAeR,OAAM;AAChC,QAAMW,WAAWC,YAAAA;AACjB,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAMC,YAAYC,cAChBC,aACA,aAEI,MAAM,OACJ,2BACF,GACAC,WAAW;AAEjB,QAAM,EACJC,yBAAyBC,gBACzBC,iCAAiCC,uBAAsB,IACrDC,mBAAAA;AAEJ,QAAMC,cAAcC,iBAAiBC,sBAAAA;AAErC,QAAM,EACJC,WAAWC,eACXC,gBAAgB,EAAEC,UAAS,EAAE,IAC3BC,QAAQ;IACVC,QAAMR,iBAAYS,aAAZT,mBAAsBU,MAAMF,SAAQ,CAAA;IAC1CG,UAAQX,iBAAYS,aAAZT,mBAAsBU,MAAMC,WAAU,CAAA;EAChD,CAAA;AAEA,QAAM,CAACC,UAAAA,IAAcC,sBAAAA;AAErB,QAAM,EACJC,MACAC,OACAZ,WAAWa,oBAAmB,IAC5BC,cACF;IAAEzC;KACF;IACE0C,2BAA2B;EAC7B,CAAA;AAGF,QAAM,CAACC,IAAAA,KAAQL,6BAAMJ,UAAS,CAAA;AAE9BU,EAAMC,gBAAU,MAAA;AACd,QAAIN,OAAO;AAET,UAAIA,MAAMO,SAAS,qBAAqB;AACtCjC,2BAAmB;UACjBkC,MAAM;UACNC,SAAS1C,cAAc;YACrBN,IAAI;YACJG,gBAAgB;UAClB,CAAA;QACF,CAAA;AAEAQ,iBAAS,GAAA;aACJ;AACLE,2BAAmB;UACjBkC,MAAM;UACNC,SAAS5B,eAAemB,KAAAA;QAC1B,CAAA;MACF;IACF;KACC;IAACA;IAAOnB;IAAgBd;IAAeK;IAAUE;EAAmB,CAAA;AAEvE,QAAMc,YAAYa,uBAAuB,CAACzB,aAAaa;AAEvD,MAAID,WAAW;AACb,eAAOsB,wBAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAOA,QAAMC,cAAc;IAClB,OAAGC,YAAAA,SAAKV,MAAMvC,YAAa;IAC3BP,OAAO8C,KAAK9C,MAAMyD,IAAI,CAAC,EAAEtD,IAAAA,IAAE,MAAOA,GAAAA;IAClCuD,UAAU;IACVC,iBAAiB;EACnB;AAEA,QAAMC,eAAe,OAAOC,MAAmBC,YAAAA;AAC7C,UAAM,EAAEH,iBAAiBI,kBAAkB,GAAGC,SAAAA,IAAaH;AAE3D,UAAMI,MAAM,MAAM1B,WAAW;MAC3BpC;MACA,GAAG6D;IACL,CAAA;AAEA,QAAI,WAAWC,OAAOC,iBAAiBD,IAAIvB,KAAK,GAAG;AACjD,UAAIuB,IAAIvB,MAAMO,SAAS,mBAAmB;AACxCa,gBAAQK,UAAU1C,uBAAuBwC,IAAIvB,KAAK,CAAA;MACpD;AAEA1B,yBAAmB;QACjBkC,MAAM;QACNC,SAAS5B,eAAe0C,IAAIvB,KAAK;MACnC,CAAA;WACK;AACL1B,yBAAmB;QACjBkC,MAAM;QACNC,SAAS1C,cAAc;UAAEN,IAAI;UAA8BG,gBAAgB;QAAQ,CAAA;MACrF,CAAA;AAEAwD,cAAQM,UAAU;QAChB,OAAGZ,YAAAA,SAAKK,MAAMtD,YAAa;QAC3BmD,UAAU;QACVC,iBAAiB;MACnB,CAAA;IACF;EACF;AAEA,aACEU,yBAAChB,KAAKiB,MAAI;;UACRlB,wBAACC,KAAKkB,OAAK;kBACR9D,cACC;UAAEN,IAAI;UAAsBG,gBAAgB;WAC5C;UACE2C,MAAM;QACR,CAAA;;UAGJG,wBAACoB,MAAAA;QACCC,QAAO;QACPC,UAAUd;QACVe,eAAepB;QACfqB,kBAAkBlF;QAEjB,UAAA,CAAC,EAAEmF,cAAcC,SAAQ,MAAE;AAC1B,qBACET,yBAAAU,6BAAA;;kBACE3B,wBAAC4B,QAAQC,QAAM;gBACbC,mBACE9B,wBAAC+B,QAAAA;kBACCC,UAAUP,gBAAgB,CAAC5C,aAAa,CAAC6C;kBACzCO,eAAWjC,wBAACkC,eAAAA,CAAAA,CAAAA;kBACZC,SAASV;kBACT3B,MAAK;4BAEJzC,cAAc;oBAAEN,IAAI;oBAAeG,gBAAgB;kBAAO,CAAA;;gBAG/DkF,OAAO/E,cACL;kBACEN,IAAI;kBACJG,gBAAgB;mBAElB;;kBAEE2C,MAAMwC,eAAelC,WAAAA;gBACvB,CAAA;gBAEFmC,sBAAkBtC,wBAACuC,YAAAA;kBAAWC,UAAS;;;kBAEzCvB,yBAACW,QAAQa,SAAO;;mBACb/C,6BAAMgD,0BACL1C,wBAAC2C,KAAAA;oBAAIC,eAAe;oBAClB,cAAA5C,wBAAClC,WAAAA;sBAAU4E,mBAAmBhD,KAAKgD;;;sBAGvCzB,yBAAC4B,MAAAA;oBAAKC,WAAU;oBAASC,YAAW;oBAAUC,KAAK;;0BACjDhD,wBAAC2C,KAAAA;wBACCM,YAAW;wBACXC,WAAS;wBACTC,QAAO;wBACPC,YAAY;wBACZR,eAAe;wBACfS,aAAa;wBACbC,cAAc;wBAEd,cAAArC,yBAAC4B,MAAAA;0BAAKC,WAAU;0BAASC,YAAW;0BAAUC,KAAK;;gCACjDhD,wBAACuD,YAAAA;8BAAWC,SAAQ;8BAAQC,KAAI;wCAC7BpG,cAAc;gCACbN,IAAI;gCACJG,gBAAgB;8BAClB,CAAA;;gCAEF8C,wBAAC0D,KAAKC,MAAI;8BAACX,KAAK;8BACbY,UAAAA,OAAOvD,IAAI,CAACwD,QACXA,IAAIxD,IAAI,CAAC,EAAEyD,MAAMC,OAAO,GAAGC,MAAO,MAAA;AAChC,2CACEhE,wBAAC0D,KAAKO,MAAI;kCAERC,KAAKJ;kCACLhB,WAAU;kCACVC,YAAW;kCAEX,cAAA/C,wBAACmE,uBAAAA;oCACE,GAAGH;oCACJhC,UAAU,CAACnD;oCACXkF,OAAO1G,cAAc0G,KAAAA;oCACrBK,aACE,iBAAiBJ,QACb3G,cAAc2G,MAAMI,WAAW,IAC/BC;;gCAZHL,GAAAA,MAAMnE,IAAI;8BAiBrB,CAAA,CAAA;;;;;0BAKRG,wBAAC2C,KAAAA;wBACCM,YAAW;wBACXC,WAAS;wBACTC,QAAO;wBACPC,YAAY;wBACZR,eAAe;wBACfS,aAAa;wBACbC,cAAc;wBAEd,cAAArC,yBAAC4B,MAAAA;0BAAKC,WAAU;0BAASC,YAAW;0BAAUC,KAAK;;gCACjDhD,wBAACuD,YAAAA;8BAAWC,SAAQ;8BAAQC,KAAI;wCAC7BpG,cAAc;gCACbN,IAAI;gCACJG,gBAAgB;8BAClB,CAAA;;gCAEF8C,wBAAC0D,KAAKC,MAAI;8BAACX,KAAK;4CACdhD,wBAAC0D,KAAKO,MAAI;gCAACC,KAAK;gCAAGI,IAAI;gCAAIxB,WAAU;gCAASC,YAAW;gCACvD,cAAA/C,wBAACuE,aAAAA;kCAAYvC,UAAU,CAACnD;;;;;;;;;;;;;QAS1C;;;;AAIR;AAMA,IAAM+E,SAAS;EACb;IACE;MACEG,OAAO;QACLhH,IAAI;QACJG,gBAAgB;MAClB;MACA2C,MAAM;MACNuE,aAAa;QACXrH,IAAI;QACJG,gBAAgB;MAClB;MACA4C,MAAM;MACNgE,MAAM;MACN7G,UAAU;IACZ;IACA;MACE8G,OAAO;QACLhH,IAAI;QACJG,gBAAgB;MAClB;MACA2C,MAAM;MACNuE,aAAa;QACXrH,IAAI;QACJG,gBAAgB;MAClB;MACA4C,MAAM;MACNgE,MAAM;IACR;EACD;EACD;IACE;MACEC,OAAO;QACLhH,IAAI;QACJG,gBAAgB;MAClB;MACA2C,MAAM;MACNuE,aAAa;QACXrH,IAAI;QACJG,gBAAgB;MAClB;MACA4C,MAAM;MACNgE,MAAM;MACN7G,UAAU;IACZ;IACA;MACE8G,OAAO;QACLhH,IAAI;QACJG,gBAAgB;MAClB;MACA2C,MAAM;MACNuE,aAAa;QACXrH,IAAI;QACJG,gBAAgB;MAClB;MACA4C,MAAM;MACNgE,MAAM;IACR;EACD;EACD;IACE;MACEU,cAAc;MACdT,OAAO;QACLhH,IAAI;QACJG,gBAAgB;MAClB;MACA2C,MAAM;MACNC,MAAM;MACNgE,MAAM;IACR;IACA;MACEU,cAAc;MACdT,OAAO;QACLhH,IAAI;QACJG,gBAAgB;MAClB;MACA2C,MAAM;MACNC,MAAM;MACNgE,MAAM;IACR;EACD;EACD;IACE;MACEC,OAAO;QACLhH,IAAI;QACJG,gBAAgB;MAClB;MACA2C,MAAM;MACNC,MAAM;MACNgE,MAAM;IACR;EACD;AACF;AAED,IAAMW,oBAAoB,MAAA;AACxB,QAAMlG,cAAcC,iBAAiB,CAACkG,UAAUA;;AAAAA,uBAAMC,UAAUpG,YAAYS,aAA5B0F,mBAAsCzF,MAAMF;GAAAA;AAE5F,aACEiB,wBAACC,KAAK2E,SAAO;IAACrG;IACZ,cAAAyB,wBAAC5C,UAAAA,CAAAA,CAAAA;;AAGP;", "names": ["EDIT_VALIDATION_SCHEMA", "object", "shape", "COMMON_USER_SCHEMA", "isActive", "bool", "roles", "array", "min", "id", "translatedErrors", "required", "defaultMessage", "fieldsToPick", "EditPage", "formatMessage", "useIntl", "match", "useMatch", "params", "navigate", "useNavigate", "toggleNotification", "useNotification", "MagicLink", "useEnterprise", "MagicLinkCE", "MagicLinkEE", "_unstableFormatAPIError", "formatAPIError", "_unstableFormatValidationErrors", "formatValidationErrors", "useAPIErrorHandler", "permissions", "useTypedSelector", "selectAdminPermissions", "isLoading", "isLoadingRBAC", "allowedActions", "canUpdate", "useRBAC", "read", "settings", "users", "update", "updateUser", "useUpdateUserMutation", "data", "error", "isLoadingAdminUsers", "useAdminUsers", "refetchOnMountOrArgChange", "user", "React", "useEffect", "name", "type", "message", "_jsx", "Page", "Loading", "initialData", "pick", "map", "password", "confirmPassword", "handleSubmit", "body", "actions", "_confirmPassword", "bodyRest", "res", "isBaseQueryError", "setErrors", "set<PERSON><PERSON><PERSON>", "_jsxs", "Main", "Title", "Form", "method", "onSubmit", "initialValues", "validationSchema", "isSubmitting", "modified", "_Fragment", "Layouts", "Header", "primaryAction", "<PERSON><PERSON>", "disabled", "startIcon", "Check", "loading", "title", "getDisplayName", "navigationAction", "BackButton", "fallback", "Content", "registrationToken", "Box", "paddingBottom", "Flex", "direction", "alignItems", "gap", "background", "hasRadius", "shadow", "paddingTop", "paddingLeft", "paddingRight", "Typography", "variant", "tag", "Grid", "Root", "LAYOUT", "row", "size", "label", "field", "<PERSON><PERSON>", "col", "InputR<PERSON><PERSON>", "placeholder", "undefined", "xs", "SelectRoles", "autoComplete", "ProtectedEditPage", "state", "admin_app", "Protect"]}