import {
  require_isNil
} from "./chunk-AOLNGJZC.js";
import {
  useLicenseLimits
} from "./chunk-53OG7EL5.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  useLocation
} from "./chunk-S65ZWNEO.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/ee/admin/src/hooks/useLicenseLimitNotification.mjs
var React = __toESM(require_react(), 1);
var import_isNil = __toESM(require_isNil(), 1);
var STORAGE_KEY_PREFIX = "strapi-notification-seat-limit";
var BILLING_SELF_HOSTED_URL = "https://strapi.io/billing/request-seats";
var MANAGE_SEATS_URL = "https://strapi.io/billing/manage-seats";
var useLicenseLimitNotification = () => {
  const { formatMessage } = useIntl();
  const { license, isError, isLoading } = useLicenseLimits();
  const { toggleNotification } = useNotification();
  const { pathname } = useLocation();
  const { enforcementUserCount, permittedSeats, licenseLimitStatus, type } = license ?? {};
  React.useEffect(() => {
    if (isError || isLoading) {
      return;
    }
    const shouldDisplayNotification = !(0, import_isNil.default)(permittedSeats) && !window.sessionStorage.getItem(`${STORAGE_KEY_PREFIX}-${pathname}`) && licenseLimitStatus === "OVER_LIMIT";
    let notificationType;
    if (licenseLimitStatus === "OVER_LIMIT") {
      notificationType = "danger";
    }
    if (shouldDisplayNotification) {
      toggleNotification({
        type: notificationType,
        message: formatMessage({
          id: "notification.ee.warning.over-.message",
          defaultMessage: "Add seats to {licenseLimitStatus, select, OVER_LIMIT {invite} other {re-enable}} Users. If you already did it but it's not reflected in Strapi yet, make sure to restart your app."
        }, {
          licenseLimitStatus
        }),
        title: formatMessage({
          id: "notification.ee.warning.at-seat-limit.title",
          defaultMessage: "{licenseLimitStatus, select, OVER_LIMIT {Over} other {At}} seat limit ({enforcementUserCount}/{permittedSeats})"
        }, {
          licenseLimitStatus,
          enforcementUserCount,
          permittedSeats
        }),
        link: {
          url: type === "gold" ? BILLING_SELF_HOSTED_URL : MANAGE_SEATS_URL,
          label: formatMessage({
            id: "notification.ee.warning.seat-limit.link",
            defaultMessage: type === "gold" ? "Contact sales" : "Manage seats"
          })
        },
        blockTransition: true,
        onClose() {
          window.sessionStorage.setItem(`${STORAGE_KEY_PREFIX}-${pathname}`, "true");
        }
      });
    }
  }, [
    toggleNotification,
    license,
    pathname,
    formatMessage,
    isLoading,
    permittedSeats,
    licenseLimitStatus,
    enforcementUserCount,
    isError,
    type
  ]);
};

export {
  useLicenseLimitNotification
};
//# sourceMappingURL=chunk-DHDIEZFY.js.map
