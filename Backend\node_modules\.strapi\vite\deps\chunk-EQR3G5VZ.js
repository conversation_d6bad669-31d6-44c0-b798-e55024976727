import {
  useAuth
} from "./chunk-ODQFI753.js";
import {
  Navigate,
  useLocation
} from "./chunk-S65ZWNEO.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/components/PrivateRoute.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var PrivateRoute = ({ children }) => {
  const token = useAuth("PrivateRoute", (state) => state.token);
  const { pathname, search } = useLocation();
  return token !== null ? children : (0, import_jsx_runtime.jsx)(Navigate, {
    to: {
      pathname: "/auth/login",
      search: pathname !== "/" ? `?redirectTo=${encodeURIComponent(`${pathname}${search}`)}` : void 0
    }
  });
};

export {
  PrivateRoute
};
//# sourceMappingURL=chunk-EQR3G5VZ.js.map
