{"version": 3, "sources": ["../../../@strapi/admin/admin/src/components/UnauthenticatedLogo.tsx", "../../../@strapi/admin/admin/src/layouts/UnauthenticatedLayout.tsx"], "sourcesContent": ["import { styled } from 'styled-components';\n\nimport { useConfiguration } from '../features/Configuration';\n\nconst Img = styled.img`\n  height: 7.2rem;\n`;\n\nconst Logo = () => {\n  const {\n    logos: { auth },\n  } = useConfiguration('UnauthenticatedLogo');\n\n  return <Img src={auth?.custom?.url || auth.default} aria-hidden alt=\"\" />;\n};\n\nexport { Logo };\n", "import * as React from 'react';\n\nimport {\n  Box,\n  BoxComponent,\n  Flex,\n  FlexComponent,\n  SingleSelect,\n  SingleSelectOption,\n} from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { useTypedDispatch, useTypedSelector } from '../core/store/hooks';\nimport { setLocale } from '../reducer';\n\nconst Wrapper = styled<BoxComponent>(Box)`\n  margin: 0 auto;\n  width: 552px;\n`;\n\nexport const Column = styled<FlexComponent>(Flex)`\n  flex-direction: column;\n`;\n\nconst LocaleToggle = () => {\n  const localeNames = useTypedSelector((state) => state.admin_app.language.localeNames);\n  const dispatch = useTypedDispatch();\n  const { formatMessage, locale } = useIntl();\n\n  return (\n    <SingleSelect\n      aria-label={formatMessage({\n        id: 'global.localeToggle.label',\n        defaultMessage: 'Select interface language',\n      })}\n      value={locale}\n      onChange={(language) => {\n        dispatch(setLocale(language as string));\n      }}\n    >\n      {Object.entries(localeNames).map(([language, name]) => (\n        <SingleSelectOption key={language} value={language}>\n          {name}\n        </SingleSelectOption>\n      ))}\n    </SingleSelect>\n  );\n};\n\ninterface LayoutContentProps {\n  children: React.ReactNode;\n}\n\nexport const LayoutContent = ({ children }: LayoutContentProps) => (\n  <Wrapper\n    shadow=\"tableShadow\"\n    hasRadius\n    paddingTop={9}\n    paddingBottom={9}\n    paddingLeft={10}\n    paddingRight={10}\n    background=\"neutral0\"\n  >\n    {children}\n  </Wrapper>\n);\n\ninterface UnauthenticatedLayoutProps {\n  children: React.ReactNode;\n}\n\nexport const UnauthenticatedLayout = ({ children }: UnauthenticatedLayoutProps) => {\n  return (\n    <div>\n      <Flex tag=\"header\" justifyContent=\"flex-end\">\n        <Box paddingTop={6} paddingRight={8}>\n          <LocaleToggle />\n        </Box>\n      </Flex>\n      <Box paddingTop={2} paddingBottom={11}>\n        {children}\n      </Box>\n    </div>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAMA,MAAMC,GAAOC;;;AAInB,IAAMC,OAAO,MAAA;;AACX,QAAM,EACJC,OAAO,EAAEC,KAAI,EAAE,IACbC,iBAAiB,qBAAA;AAErB,aAAOC,wBAACP,KAAAA;IAAIQ,OAAKH,kCAAMI,WAANJ,mBAAcK,QAAOL,KAAKM;IAASC,eAAW;IAACC,KAAI;;AACtE;;;;;ACEA,IAAMC,UAAUC,GAAqBC,GAAAA;;;;AAKxBC,IAAAA,SAASF,GAAsBG,IAAAA;;;AAI5C,IAAMC,eAAe,MAAA;AACnB,QAAMC,cAAcC,iBAAiB,CAACC,UAAUA,MAAMC,UAAUC,SAASJ,WAAW;AACpF,QAAMK,WAAWC,iBAAAA;AACjB,QAAM,EAAEC,eAAeC,OAAM,IAAKC,QAAAA;AAElC,aACEC,yBAACC,cAAAA;IACCC,cAAYL,cAAc;MACxBM,IAAI;MACJC,gBAAgB;IAClB,CAAA;IACAC,OAAOP;IACPQ,UAAU,CAACZ,aAAAA;AACTC,eAASY,UAAUb,QAAAA,CAAAA;IACrB;cAECc,OAAOC,QAAQnB,WAAAA,EAAaoB,IAAI,CAAC,CAAChB,UAAUiB,IAAK,UAChDX,yBAACY,oBAAAA;MAAkCP,OAAOX;MACvCiB,UAAAA;IADsBjB,GAAAA,QAAAA,CAAAA;;AAMjC;IAMamB,gBAAgB,CAAC,EAAEC,SAAQ,UACtCd,yBAAChB,SAAAA;EACC+B,QAAO;EACPC,WAAS;EACTC,YAAY;EACZC,eAAe;EACfC,aAAa;EACbC,cAAc;EACdC,YAAW;EAEVP;AAEH,CAAA;AAMWQ,IAAAA,wBAAwB,CAAC,EAAER,SAAQ,MAA8B;AAC5E,aACES,0BAACC,OAAAA;;UACCxB,yBAACZ,MAAAA;QAAKqC,KAAI;QAASC,gBAAe;QAChC,cAAA1B,yBAACd,KAAAA;UAAI+B,YAAY;UAAGG,cAAc;UAChC,cAAApB,yBAACX,cAAAA,CAAAA,CAAAA;;;UAGLW,yBAACd,KAAAA;QAAI+B,YAAY;QAAGC,eAAe;QAChCJ;;;;AAIT;", "names": ["Img", "styled", "img", "Logo", "logos", "auth", "useConfiguration", "_jsx", "src", "custom", "url", "default", "aria-hidden", "alt", "Wrapper", "styled", "Box", "Column", "Flex", "LocaleToggle", "localeNames", "useTypedSelector", "state", "admin_app", "language", "dispatch", "useTypedDispatch", "formatMessage", "locale", "useIntl", "_jsx", "SingleSelect", "aria-label", "id", "defaultMessage", "value", "onChange", "setLocale", "Object", "entries", "map", "name", "SingleSelectOption", "LayoutContent", "children", "shadow", "hasRadius", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "background", "UnauthenticatedLayout", "_jsxs", "div", "tag", "justifyContent"]}