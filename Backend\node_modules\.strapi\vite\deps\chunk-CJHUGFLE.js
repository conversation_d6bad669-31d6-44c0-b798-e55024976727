import {
  Flex,
  Typo<PERSON>
} from "./chunk-7GC3Y62Q.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useClipboard.mjs
var import_react = __toESM(require_react(), 1);
var useClipboard = () => {
  const copy = (0, import_react.useCallback)(async (value) => {
    try {
      if (typeof value !== "string" && typeof value !== "number") {
        throw new Error(`Cannot copy typeof ${typeof value} to clipboard, must be a string or number`);
      } else if (value === "") {
        throw new Error(`Cannot copy empty string to clipboard.`);
      }
      const stringifiedValue = value.toString();
      await navigator.clipboard.writeText(stringifiedValue);
      return true;
    } catch (error) {
      if (true) {
        console.warn("Copy failed", error);
      }
      return false;
    }
  }, []);
  return {
    copy
  };
};

// node_modules/@strapi/admin/dist/admin/admin/src/components/ContentBox.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var ContentBox = ({ title, subtitle, icon, iconBackground, endAction, titleEllipsis = false }) => {
  if (title && title.length > 70 && titleEllipsis) {
    title = `${title.substring(0, 70)}...`;
  }
  return (0, import_jsx_runtime.jsxs)(Flex, {
    shadow: "tableShadow",
    hasRadius: true,
    padding: 6,
    background: "neutral0",
    children: [
      (0, import_jsx_runtime.jsx)(IconWrapper, {
        background: iconBackground,
        hasRadius: true,
        padding: 3,
        children: icon
      }),
      (0, import_jsx_runtime.jsxs)(Flex, {
        direction: "column",
        alignItems: "stretch",
        gap: endAction ? 0 : 1,
        children: [
          (0, import_jsx_runtime.jsxs)(Flex, {
            children: [
              (0, import_jsx_runtime.jsx)(TypographyWordBreak, {
                fontWeight: "semiBold",
                variant: "pi",
                children: title
              }),
              endAction
            ]
          }),
          (0, import_jsx_runtime.jsx)(Typography, {
            textColor: "neutral600",
            children: subtitle
          })
        ]
      })
    ]
  });
};
var IconWrapper = dt(Flex)`
  margin-right: ${({ theme }) => theme.spaces[6]};

  svg {
    width: 3.2rem;
    height: 3.2rem;
  }
`;
var TypographyWordBreak = dt(Typography)`
  color: ${({ theme }) => theme.colors.neutral800};
  word-break: break-all;
`;

export {
  useClipboard,
  ContentBox
};
//# sourceMappingURL=chunk-CJHUGFLE.js.map
