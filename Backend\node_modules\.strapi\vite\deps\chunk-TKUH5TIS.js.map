{"version": 3, "sources": ["../../../lodash/_baseIndexOfWith.js", "../../../lodash/_basePullAll.js", "../../../lodash/pullAll.js", "../../../lodash/pull.js", "../../../@strapi/admin/admin/src/services/contentApi.ts", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/apiTokenPermissions.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/components/FormApiTokenContainer.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/components/BoundRoute.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/components/ActionBoundRoutes.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/components/CollapsableContentType.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/components/ContentTypesSection.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/components/Permissions.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/constants.ts", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/utils/transformPermissionsData.ts", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/reducer.ts", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/EditViewPage.tsx"], "sourcesContent": ["/**\n * This function is like `baseIndexOf` except that it accepts a comparator.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} value The value to search for.\n * @param {number} fromIndex The index to search from.\n * @param {Function} comparator The comparator invoked per element.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction baseIndexOfWith(array, value, fromIndex, comparator) {\n  var index = fromIndex - 1,\n      length = array.length;\n\n  while (++index < length) {\n    if (comparator(array[index], value)) {\n      return index;\n    }\n  }\n  return -1;\n}\n\nmodule.exports = baseIndexOfWith;\n", "var arrayMap = require('./_arrayMap'),\n    baseIndexOf = require('./_baseIndexOf'),\n    baseIndexOfWith = require('./_baseIndexOfWith'),\n    baseUnary = require('./_baseUnary'),\n    copyArray = require('./_copyArray');\n\n/** Used for built-in method references. */\nvar arrayProto = Array.prototype;\n\n/** Built-in value references. */\nvar splice = arrayProto.splice;\n\n/**\n * The base implementation of `_.pullAllBy` without support for iteratee\n * shorthands.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {Array} values The values to remove.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns `array`.\n */\nfunction basePullAll(array, values, iteratee, comparator) {\n  var indexOf = comparator ? baseIndexOfWith : baseIndexOf,\n      index = -1,\n      length = values.length,\n      seen = array;\n\n  if (array === values) {\n    values = copyArray(values);\n  }\n  if (iteratee) {\n    seen = arrayMap(array, baseUnary(iteratee));\n  }\n  while (++index < length) {\n    var fromIndex = 0,\n        value = values[index],\n        computed = iteratee ? iteratee(value) : value;\n\n    while ((fromIndex = indexOf(seen, computed, fromIndex, comparator)) > -1) {\n      if (seen !== array) {\n        splice.call(seen, fromIndex, 1);\n      }\n      splice.call(array, fromIndex, 1);\n    }\n  }\n  return array;\n}\n\nmodule.exports = basePullAll;\n", "var basePullAll = require('./_basePullAll');\n\n/**\n * This method is like `_.pull` except that it accepts an array of values to remove.\n *\n * **Note:** Unlike `_.difference`, this method mutates `array`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to modify.\n * @param {Array} values The values to remove.\n * @returns {Array} Returns `array`.\n * @example\n *\n * var array = ['a', 'b', 'c', 'a', 'b', 'c'];\n *\n * _.pullAll(array, ['a', 'c']);\n * console.log(array);\n * // => ['b', 'b']\n */\nfunction pullAll(array, values) {\n  return (array && array.length && values && values.length)\n    ? basePullAll(array, values)\n    : array;\n}\n\nmodule.exports = pullAll;\n", "var baseRest = require('./_baseRest'),\n    pullAll = require('./pullAll');\n\n/**\n * Removes all given values from `array` using\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * **Note:** Unlike `_.without`, this method mutates `array`. Use `_.remove`\n * to remove elements from an array by predicate.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @category Array\n * @param {Array} array The array to modify.\n * @param {...*} [values] The values to remove.\n * @returns {Array} Returns `array`.\n * @example\n *\n * var array = ['a', 'b', 'c', 'a', 'b', 'c'];\n *\n * _.pull(array, 'a', 'c');\n * console.log(array);\n * // => ['b', 'b']\n */\nvar pull = baseRest(pullAll);\n\nmodule.exports = pull;\n", "import { adminApi } from './api';\n\nimport type { List as ListContentApiPermissions } from '../../../shared/contracts/content-api/permissions';\nimport type { List as ListContentApiRoutes } from '../../../shared/contracts/content-api/routes';\n\nconst contentApiService = adminApi.injectEndpoints({\n  endpoints: (builder) => ({\n    getPermissions: builder.query<ListContentApiPermissions.Response['data'], void>({\n      query: () => '/admin/content-api/permissions',\n      transformResponse: (response: ListContentApiPermissions.Response) => response.data,\n    }),\n    getRoutes: builder.query<ListContentApiRoutes.Response['data'], void>({\n      query: () => '/admin/content-api/routes',\n      transformResponse: (response: ListContentApiRoutes.Response) => response.data,\n    }),\n  }),\n  overrideExisting: false,\n});\n\nconst { useGetPermissionsQuery, useGetRoutesQuery } = contentApiService;\n\nexport { useGetPermissionsQuery, useGetRoutesQuery };\n", "/* eslint-disable check-file/filename-naming-convention */\n\nimport * as React from 'react';\n\nimport { createContext } from '@radix-ui/react-context';\n\nimport { List as ListContentApiPermissions } from '../../../../../../../shared/contracts/content-api/permissions';\nimport { List as ListContentApiRoutes } from '../../../../../../../shared/contracts/content-api/routes';\n\nexport interface PseudoEvent {\n  target: { value: string };\n}\n\ninterface ApiTokenPermissionsContextValue {\n  value: {\n    selectedAction: string | null;\n    routes: ListContentApiRoutes.Response['data'];\n    selectedActions: string[];\n    data: {\n      allActionsIds: string[];\n      permissions: ListContentApiPermissions.Response['data'][];\n    };\n    onChange: ({ target: { value } }: PseudoEvent) => void;\n    onChangeSelectAll: ({\n      target: { value },\n    }: {\n      target: { value: { action: string; actionId: string }[] };\n    }) => void;\n    setSelectedAction: ({ target: { value } }: PseudoEvent) => void;\n  };\n}\n\ninterface ApiTokenPermissionsContextProviderProps extends ApiTokenPermissionsContextValue {\n  children: React.ReactNode | React.ReactNode[];\n}\n\nconst [ApiTokenPermissionsContextProvider, useApiTokenPermissionsContext] =\n  createContext<ApiTokenPermissionsContextValue>('ApiTokenPermissionsContext');\n\nconst ApiTokenPermissionsProvider = ({\n  children,\n  ...rest\n}: ApiTokenPermissionsContextProviderProps) => {\n  return (\n    <ApiTokenPermissionsContextProvider {...rest}>{children}</ApiTokenPermissionsContextProvider>\n  );\n};\n\nconst useApiTokenPermissions = () => useApiTokenPermissionsContext('useApiTokenPermissions');\n\nexport { ApiTokenPermissionsProvider, useApiTokenPermissions };\nexport type { ApiTokenPermissionsContextValue, ApiTokenPermissionsContextProviderProps };\n", "import * as React from 'react';\n\nimport { Box, Flex, Grid, Typography } from '@strapi/design-system';\nimport { FormikErrors } from 'formik';\nimport { useIntl } from 'react-intl';\n\nimport { LifeSpanInput } from '../../../../components/Tokens/LifeSpanInput';\nimport { TokenDescription } from '../../../../components/Tokens/TokenDescription';\nimport { TokenName } from '../../../../components/Tokens/TokenName';\nimport { TokenTypeSelect } from '../../../../components/Tokens/TokenTypeSelect';\n\nimport type { ApiToken } from '../../../../../../../../shared/contracts/api-token';\n\ninterface FormApiTokenContainerProps {\n  errors?: FormikErrors<Pick<ApiToken, 'name' | 'description' | 'lifespan' | 'type'>>;\n  onChange: ({ target: { name, value } }: { target: { name: string; value: string } }) => void;\n  canEditInputs: boolean;\n  values?: Partial<Pick<ApiToken, 'name' | 'description' | 'lifespan' | 'type'>>;\n  isCreating: boolean;\n  apiToken?: null | Partial<ApiToken>;\n  onDispatch: React.Dispatch<any>;\n  setHasChangedPermissions: (hasChanged: boolean) => void;\n}\n\nexport const FormApiTokenContainer = ({\n  errors = {},\n  onChange,\n  canEditInputs,\n  isCreating,\n  values = {},\n  apiToken = {},\n  onDispatch,\n  setHasChangedPermissions,\n}: FormApiTokenContainerProps) => {\n  const { formatMessage } = useIntl();\n\n  const handleChangeSelectApiTokenType = ({ target: { value } }: { target: { value: string } }) => {\n    setHasChangedPermissions(false);\n\n    if (value === 'full-access') {\n      onDispatch({\n        type: 'SELECT_ALL_ACTIONS',\n      });\n    }\n    if (value === 'read-only') {\n      onDispatch({\n        type: 'ON_CHANGE_READ_ONLY',\n      });\n    }\n  };\n\n  const typeOptions = [\n    {\n      value: 'read-only',\n      label: {\n        id: 'Settings.tokens.types.read-only',\n        defaultMessage: 'Read-only',\n      },\n    },\n    {\n      value: 'full-access',\n      label: {\n        id: 'Settings.tokens.types.full-access',\n        defaultMessage: 'Full access',\n      },\n    },\n    {\n      value: 'custom',\n      label: {\n        id: 'Settings.tokens.types.custom',\n        defaultMessage: 'Custom',\n      },\n    },\n  ];\n\n  return (\n    <Box\n      background=\"neutral0\"\n      hasRadius\n      shadow=\"filterShadow\"\n      paddingTop={6}\n      paddingBottom={6}\n      paddingLeft={7}\n      paddingRight={7}\n    >\n      <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n        <Typography variant=\"delta\" tag=\"h2\">\n          {formatMessage({\n            id: 'global.details',\n            defaultMessage: 'Details',\n          })}\n        </Typography>\n        <Grid.Root gap={5}>\n          <Grid.Item key=\"name\" col={6} xs={12} direction=\"column\" alignItems=\"stretch\">\n            <TokenName\n              error={errors['name']}\n              value={values['name']}\n              canEditInputs={canEditInputs}\n              onChange={onChange}\n            />\n          </Grid.Item>\n          <Grid.Item key=\"description\" col={6} xs={12} direction=\"column\" alignItems=\"stretch\">\n            <TokenDescription\n              error={errors['description']}\n              value={values['description']}\n              canEditInputs={canEditInputs}\n              onChange={onChange}\n            />\n          </Grid.Item>\n          <Grid.Item key=\"lifespan\" col={6} xs={12} direction=\"column\" alignItems=\"stretch\">\n            <LifeSpanInput\n              isCreating={isCreating}\n              error={errors['lifespan']}\n              value={values['lifespan']}\n              onChange={onChange}\n              token={apiToken}\n            />\n          </Grid.Item>\n\n          <Grid.Item key=\"type\" col={6} xs={12} direction=\"column\" alignItems=\"stretch\">\n            <TokenTypeSelect\n              value={values['type']}\n              error={errors['type']}\n              label={{\n                id: 'Settings.tokens.form.type',\n                defaultMessage: 'Token type',\n              }}\n              onChange={(value) => {\n                // @ts-expect-error – DS Select supports numbers & strings, will be removed in V2\n                handleChangeSelectApiTokenType({ target: { value } });\n\n                // @ts-expect-error – DS Select supports numbers & strings, will be removed in V2\n                onChange({ target: { name: 'type', value } });\n              }}\n              options={typeOptions}\n              canEditInputs={canEditInputs}\n            />\n          </Grid.Item>\n        </Grid.Root>\n      </Flex>\n    </Box>\n  );\n};\n", "import { Box, BoxComponent, Flex, Typography } from '@strapi/design-system';\nimport map from 'lodash/map';\nimport tail from 'lodash/tail';\nimport { useIntl } from 'react-intl';\nimport { styled, DefaultTheme } from 'styled-components';\n\ntype HttpVerb = 'POST' | 'GET' | 'PUT' | 'DELETE';\n\ntype MethodColor = {\n  text: keyof DefaultTheme['colors'];\n  border: keyof DefaultTheme['colors'];\n  background: keyof DefaultTheme['colors'];\n};\n\nconst getMethodColor = (verb: HttpVerb): MethodColor => {\n  switch (verb) {\n    case 'POST': {\n      return {\n        text: 'success600',\n        border: 'success200',\n        background: 'success100',\n      };\n    }\n    case 'GET': {\n      return {\n        text: 'secondary600',\n        border: 'secondary200',\n        background: 'secondary100',\n      };\n    }\n    case 'PUT': {\n      return {\n        text: 'warning600',\n        border: 'warning200',\n        background: 'warning100',\n      };\n    }\n    case 'DELETE': {\n      return {\n        text: 'danger600',\n        border: 'danger200',\n        background: 'danger100',\n      };\n    }\n    default: {\n      return {\n        text: 'neutral600',\n        border: 'neutral200',\n        background: 'neutral100',\n      };\n    }\n  }\n};\n\nconst MethodBox = styled<BoxComponent>(Box)`\n  margin: -1px;\n  border-radius: ${({ theme }) => theme.spaces[1]} 0 0 ${({ theme }) => theme.spaces[1]};\n`;\n\ninterface BoundRouteProps {\n  route: {\n    handler: string;\n    method: HttpVerb;\n    path: string;\n  };\n}\n\nexport const BoundRoute = ({\n  route = {\n    handler: 'Nocontroller.error',\n    method: 'GET',\n    path: '/there-is-no-path',\n  },\n}: BoundRouteProps) => {\n  const { formatMessage } = useIntl();\n\n  const { method, handler: title, path } = route;\n  const formattedRoute = path ? tail(path.split('/')) : [];\n  const [controller = '', action = ''] = title ? title.split('.') : [];\n  const colors = getMethodColor(route.method);\n\n  return (\n    <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n      <Typography variant=\"delta\" tag=\"h3\">\n        {formatMessage({\n          id: 'Settings.apiTokens.createPage.BoundRoute.title',\n          defaultMessage: 'Bound route to',\n        })}\n        &nbsp;\n        <span>{controller}</span>\n        <Typography variant=\"delta\" textColor=\"primary600\">\n          .{action}\n        </Typography>\n      </Typography>\n      <Flex hasRadius background=\"neutral0\" borderColor=\"neutral200\" gap={0}>\n        <MethodBox background={colors.background} borderColor={colors.border} padding={2}>\n          <Typography fontWeight=\"bold\" textColor={colors.text}>\n            {method}\n          </Typography>\n        </MethodBox>\n        <Box paddingLeft={2} paddingRight={2}>\n          {map(formattedRoute, (value) => (\n            <Typography key={value} textColor={value.includes(':') ? 'neutral600' : 'neutral900'}>\n              /{value}\n            </Typography>\n          ))}\n        </Box>\n      </Flex>\n    </Flex>\n  );\n};\n", "import { Grid, Flex, Typography } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { useApiTokenPermissions } from '../apiTokenPermissions';\n\nimport { BoundRoute } from './BoundRoute';\n\nexport const ActionBoundRoutes = () => {\n  const {\n    value: { selectedAction, routes },\n  } = useApiTokenPermissions();\n  const { formatMessage } = useIntl();\n  const actionSection = selectedAction?.split('.')[0];\n\n  return (\n    <Grid.Item\n      col={5}\n      background=\"neutral150\"\n      paddingTop={6}\n      paddingBottom={6}\n      paddingLeft={7}\n      paddingRight={7}\n      style={{ minHeight: '100%' }}\n      direction=\"column\"\n      alignItems=\"stretch\"\n    >\n      {selectedAction ? (\n        <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n          {actionSection &&\n            actionSection in routes &&\n            routes[actionSection].map((route) => {\n              return route.config.auth?.scope?.includes(selectedAction) ||\n                route.handler === selectedAction ? (\n                <BoundRoute key={route.handler} route={route} />\n              ) : null;\n            })}\n        </Flex>\n      ) : (\n        <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n          <Typography variant=\"delta\" tag=\"h3\">\n            {formatMessage({\n              id: 'Settings.apiTokens.createPage.permissions.header.title',\n              defaultMessage: 'Advanced settings',\n            })}\n          </Typography>\n          <Typography tag=\"p\" textColor=\"neutral600\">\n            {formatMessage({\n              id: 'Settings.apiTokens.createPage.permissions.header.hint',\n              defaultMessage:\n                \"Select the application's actions or the plugin's actions and click on the cog icon to display the bound route\",\n            })}\n          </Typography>\n        </Flex>\n      )}\n    </Grid.Item>\n  );\n};\n", "import * as React from 'react';\n\nimport {\n  Accordion,\n  Box,\n  BoxComponent,\n  Checkbox,\n  Flex,\n  Grid,\n  Typography,\n} from '@strapi/design-system';\nimport { Cog } from '@strapi/icons';\nimport capitalize from 'lodash/capitalize';\nimport { useIntl } from 'react-intl';\nimport { styled, css } from 'styled-components';\n\nimport { ContentApiPermission } from '../../../../../../../../shared/contracts/content-api/permissions';\nimport { useApiTokenPermissions } from '../apiTokenPermissions';\n\nconst activeCheckboxWrapperStyles = css`\n  background: ${(props) => props.theme.colors.primary100};\n\n  #cog {\n    opacity: 1;\n  }\n`;\n\nconst CheckboxWrapper = styled<BoxComponent>(Box)<{ $isActive: boolean }>`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  #cog {\n    opacity: 0;\n    path {\n      fill: ${(props) => props.theme.colors.primary600};\n    }\n  }\n\n  /* Show active style both on hover and when the action is selected */\n  ${(props) => props.$isActive && activeCheckboxWrapperStyles}\n  &:hover {\n    ${activeCheckboxWrapperStyles}\n  }\n`;\n\nconst Border = styled.div`\n  flex: 1;\n  align-self: center;\n  border-top: 1px solid ${({ theme }) => theme.colors.neutral150};\n`;\n\ninterface CollapsableContentTypeProps {\n  controllers?: ContentApiPermission['controllers'];\n  label: ContentApiPermission['label'];\n  orderNumber?: number;\n  disabled?: boolean;\n}\n\nexport const CollapsableContentType = ({\n  controllers = [],\n  label,\n  orderNumber = 0,\n  disabled = false,\n}: CollapsableContentTypeProps) => {\n  const {\n    value: { onChangeSelectAll, onChange, selectedActions, setSelectedAction, selectedAction },\n  } = useApiTokenPermissions();\n  const { formatMessage } = useIntl();\n\n  const isActionSelected = (actionId: string) => actionId === selectedAction;\n\n  return (\n    <Accordion.Item value={`${label}-${orderNumber}`}>\n      <Accordion.Header variant={orderNumber % 2 ? 'primary' : 'secondary'}>\n        <Accordion.Trigger>{capitalize(label)}</Accordion.Trigger>\n      </Accordion.Header>\n      <Accordion.Content>\n        {controllers?.map((controller) => {\n          const allActionsSelected = controller.actions.every((action) =>\n            selectedActions.includes(action.actionId)\n          );\n\n          const someActionsSelected = controller.actions.some((action) =>\n            selectedActions.includes(action.actionId)\n          );\n\n          return (\n            <Box key={`${label}.${controller?.controller}`}>\n              <Flex justifyContent=\"space-between\" alignItems=\"center\" padding={4}>\n                <Box paddingRight={4}>\n                  <Typography variant=\"sigma\" textColor=\"neutral600\">\n                    {controller?.controller}\n                  </Typography>\n                </Box>\n                <Border />\n                <Box paddingLeft={4}>\n                  <Checkbox\n                    checked={\n                      !allActionsSelected && someActionsSelected\n                        ? 'indeterminate'\n                        : allActionsSelected\n                    }\n                    onCheckedChange={() => {\n                      onChangeSelectAll({ target: { value: [...controller.actions] } });\n                    }}\n                    disabled={disabled}\n                  >\n                    {formatMessage({ id: 'app.utils.select-all', defaultMessage: 'Select all' })}\n                  </Checkbox>\n                </Box>\n              </Flex>\n              <Grid.Root gap={4} padding={4}>\n                {controller?.actions &&\n                  controller?.actions.map((action) => {\n                    return (\n                      <Grid.Item\n                        col={6}\n                        key={action.actionId}\n                        direction=\"column\"\n                        alignItems=\"stretch\"\n                      >\n                        <CheckboxWrapper\n                          $isActive={isActionSelected(action.actionId)}\n                          padding={2}\n                          hasRadius\n                        >\n                          <Checkbox\n                            checked={selectedActions.includes(action.actionId)}\n                            name={action.actionId}\n                            onCheckedChange={() => {\n                              onChange({ target: { value: action.actionId } });\n                            }}\n                            disabled={disabled}\n                          >\n                            {action.action}\n                          </Checkbox>\n                          <button\n                            type=\"button\"\n                            data-testid=\"action-cog\"\n                            onClick={() =>\n                              setSelectedAction({ target: { value: action.actionId } })\n                            }\n                            style={{ display: 'inline-flex', alignItems: 'center' }}\n                          >\n                            <Cog id=\"cog\" />\n                          </button>\n                        </CheckboxWrapper>\n                      </Grid.Item>\n                    );\n                  })}\n              </Grid.Root>\n            </Box>\n          );\n        })}\n      </Accordion.Content>\n    </Accordion.Item>\n  );\n};\n", "import { Accordion, Box } from '@strapi/design-system';\n\nimport { ContentApiPermission } from '../../../../../../../../shared/contracts/content-api/permissions';\n\nimport { CollapsableContentType } from './CollapsableContentType';\n\ninterface ContentTypesSectionProps {\n  section: ContentApiPermission[] | null;\n}\n\nexport const ContentTypesSection = ({ section = null, ...props }: ContentTypesSectionProps) => {\n  return (\n    <Box>\n      <Accordion.Root size=\"M\">\n        {section &&\n          section.map((api, index) => (\n            <CollapsableContentType\n              key={api.apiId}\n              label={api.label}\n              controllers={api.controllers}\n              orderNumber={index}\n              {...props}\n            />\n          ))}\n      </Accordion.Root>\n    </Box>\n  );\n};\n", "import { Flex, Grid, Typography } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { useApiTokenPermissions } from '../apiTokenPermissions';\n\nimport { ActionBoundRoutes } from './ActionBoundRoutes';\nimport { ContentTypesSection } from './ContentTypesSection';\n\nexport const Permissions = ({ ...props }) => {\n  const {\n    value: { data },\n  } = useApiTokenPermissions();\n  const { formatMessage } = useIntl();\n\n  return (\n    <Grid.Root gap={0} shadow=\"filterShadow\" hasRadius background=\"neutral0\">\n      <Grid.Item\n        col={7}\n        paddingTop={6}\n        paddingBottom={6}\n        paddingLeft={7}\n        paddingRight={7}\n        direction=\"column\"\n        alignItems=\"stretch\"\n        gap={6}\n      >\n        <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n          <Typography variant=\"delta\" tag=\"h2\">\n            {formatMessage({\n              id: 'Settings.apiTokens.createPage.permissions.title',\n              defaultMessage: 'Permissions',\n            })}\n          </Typography>\n          <Typography tag=\"p\" textColor=\"neutral600\">\n            {formatMessage({\n              id: 'Settings.apiTokens.createPage.permissions.description',\n              defaultMessage: 'Only actions bound by a route are listed below.',\n            })}\n          </Typography>\n        </Flex>\n        {data?.permissions && <ContentTypesSection section={data?.permissions} {...props} />}\n      </Grid.Item>\n      <ActionBoundRoutes />\n    </Grid.Root>\n  );\n};\n", "import * as yup from 'yup';\n\nimport { translatedErrors } from '../../../../../utils/translatedErrors';\n\nexport const schema = yup.object().shape({\n  name: yup.string().max(100).required(translatedErrors.required.id),\n  type: yup\n    .string()\n    .oneOf(['read-only', 'full-access', 'custom'])\n    .required(translatedErrors.required.id),\n  description: yup.string().nullable(),\n  lifespan: yup.number().integer().min(0).nullable().defined(translatedErrors.required.id),\n});\n", "import { ContentApiPermission } from '../../../../../../../../shared/contracts/content-api/permissions';\n\ninterface Layout {\n  allActionsIds: string[];\n  permissions: {\n    apiId: string;\n    label: string;\n    controllers: { controller: string; actions: { action: string; actionId: string }[] }[];\n  }[];\n}\n\nexport const transformPermissionsData = (data: ContentApiPermission) => {\n  const layout: Layout = {\n    allActionsIds: [],\n    permissions: [],\n  };\n\n  layout.permissions = Object.entries(data).map(([apiId, permission]) => ({\n    apiId,\n    label: apiId.split('::')[1],\n    controllers: Object.keys(permission.controllers)\n      .map((controller) => ({\n        controller,\n        actions:\n          controller in permission.controllers\n            ? permission.controllers[controller]\n                .map((action: ContentApiPermission['controllers']) => {\n                  const actionId = `${apiId}.${controller}.${action}`;\n\n                  if (apiId.includes('api::')) {\n                    layout.allActionsIds.push(actionId);\n                  }\n\n                  return {\n                    action,\n                    actionId,\n                  };\n                })\n                .flat()\n            : [],\n      }))\n      .flat(),\n  }));\n\n  return layout;\n};\n", "/* eslint-disable consistent-return */\nimport { produce } from 'immer';\nimport pull from 'lodash/pull';\n\nimport { ContentApiPermission } from '../../../../../../../shared/contracts/content-api/permissions';\n\nimport { ApiTokenPermissionsContextValue } from './apiTokenPermissions';\nimport { transformPermissionsData } from './utils/transformPermissionsData';\n\ntype InitialState = Pick<\n  ApiTokenPermissionsContextValue['value'],\n  'data' | 'routes' | 'selectedAction' | 'selectedActions'\n>;\n\ninterface ActionOnChange {\n  type: 'ON_CHANGE';\n  value: string;\n}\n\ninterface ActionSelectAllInPermission {\n  type: 'SELECT_ALL_IN_PERMISSION';\n  value: { action: string; actionId: string }[];\n}\n\ninterface ActionSelectAllActions {\n  type: 'SELECT_ALL_ACTIONS';\n}\n\ninterface ActionOnChangeReadOnly {\n  type: 'ON_CHANGE_READ_ONLY';\n}\n\ninterface ActionUpdatePermissionsLayout {\n  type: 'UPDATE_PERMISSIONS_LAYOUT';\n  value: ContentApiPermission;\n}\n\ninterface ActionUpdateRoutes {\n  type: 'UPDATE_ROUTES';\n  value: ApiTokenPermissionsContextValue['value']['routes'] | undefined;\n}\n\ninterface ActionUpdatePermissions {\n  type: 'UPDATE_PERMISSIONS';\n  value: any[];\n}\n\ninterface ActionSetSelectedAction {\n  type: 'SET_SELECTED_ACTION';\n  value: string;\n}\n\ntype Action =\n  | ActionOnChange\n  | ActionSelectAllInPermission\n  | ActionSelectAllActions\n  | ActionOnChangeReadOnly\n  | ActionUpdatePermissionsLayout\n  | ActionUpdateRoutes\n  | ActionUpdatePermissions\n  | ActionSetSelectedAction;\n\nexport const initialState: InitialState = {\n  data: {\n    allActionsIds: [],\n    permissions: [],\n  },\n  routes: {},\n  selectedAction: '',\n  selectedActions: [],\n};\n\nexport const reducer = (state: InitialState, action: Action) =>\n  produce(state, (draftState) => {\n    switch (action.type) {\n      case 'ON_CHANGE': {\n        if (draftState.selectedActions.includes(action.value)) {\n          pull(draftState.selectedActions, action.value);\n        } else {\n          draftState.selectedActions.push(action.value);\n        }\n        break;\n      }\n      case 'SELECT_ALL_IN_PERMISSION': {\n        const areAllSelected = action.value.every((item) =>\n          draftState.selectedActions.includes(item.actionId)\n        );\n\n        if (areAllSelected) {\n          action.value.forEach((item) => {\n            pull(draftState.selectedActions, item.actionId);\n          });\n        } else {\n          action.value.forEach((item) => {\n            draftState.selectedActions.push(item.actionId);\n          });\n        }\n        break;\n      }\n\n      case 'SELECT_ALL_ACTIONS': {\n        draftState.selectedActions = [...draftState.data.allActionsIds];\n\n        break;\n      }\n      case 'ON_CHANGE_READ_ONLY': {\n        const onlyReadOnlyActions = draftState.data.allActionsIds.filter(\n          (actionId) => actionId.includes('find') || actionId.includes('findOne')\n        );\n        draftState.selectedActions = [...onlyReadOnlyActions];\n        break;\n      }\n      case 'UPDATE_PERMISSIONS_LAYOUT': {\n        draftState.data = transformPermissionsData(action.value);\n        break;\n      }\n      case 'UPDATE_ROUTES': {\n        draftState.routes = { ...action.value };\n        break;\n      }\n      case 'UPDATE_PERMISSIONS': {\n        draftState.selectedActions = [...action.value];\n        break;\n      }\n      case 'SET_SELECTED_ACTION': {\n        draftState.selectedAction = action.value;\n        break;\n      }\n      default:\n        return draftState;\n    }\n  });\n", "import * as React from 'react';\n\nimport { Flex } from '@strapi/design-system';\nimport { Formik, Form, FormikHelpers } from 'formik';\nimport { useIntl } from 'react-intl';\nimport { useLocation, useMatch, useNavigate } from 'react-router-dom';\n\nimport { useGuidedTour } from '../../../../../components/GuidedTour/Provider';\nimport { Layouts } from '../../../../../components/Layouts/Layout';\nimport { Page } from '../../../../../components/PageHelpers';\nimport { useTypedSelector } from '../../../../../core/store/hooks';\nimport { useNotification } from '../../../../../features/Notifications';\nimport { useTracking } from '../../../../../features/Tracking';\nimport { useAPIErrorHandler } from '../../../../../hooks/useAPIErrorHandler';\nimport { useRBAC } from '../../../../../hooks/useRBAC';\nimport {\n  useCreateAPITokenMutation,\n  useGetAPITokenQuery,\n  useUpdateAPITokenMutation,\n} from '../../../../../services/apiTokens';\nimport { useGetPermissionsQuery, useGetRoutesQuery } from '../../../../../services/contentApi';\nimport { isBaseQueryError } from '../../../../../utils/baseQuery';\nimport { API_TOKEN_TYPE } from '../../../components/Tokens/constants';\nimport { FormHead } from '../../../components/Tokens/FormHead';\nimport { TokenBox } from '../../../components/Tokens/TokenBox';\n\nimport {\n  ApiTokenPermissionsContextValue,\n  ApiTokenPermissionsProvider,\n} from './apiTokenPermissions';\nimport { FormApiTokenContainer } from './components/FormApiTokenContainer';\nimport { Permissions } from './components/Permissions';\nimport { schema } from './constants';\nimport { initialState, reducer } from './reducer';\n\nimport type { Get, ApiToken } from '../../../../../../../shared/contracts/api-token';\n\n/**\n * TODO: this could definitely be refactored to avoid using redux and instead just use the\n * server response as the source of the truth for the data.\n */\nexport const EditView = () => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { state: locationState } = useLocation();\n  const permissions = useTypedSelector((state) => state.admin_app.permissions);\n  const [apiToken, setApiToken] = React.useState<ApiToken | null>(\n    locationState?.apiToken?.accessKey\n      ? {\n          ...locationState.apiToken,\n        }\n      : null\n  );\n  const [showToken, setShowToken] = React.useState(Boolean(locationState?.apiToken?.accessKey));\n  const hideTimerRef = React.useRef<ReturnType<typeof setTimeout> | null>(null);\n  const { trackUsage } = useTracking();\n  const setCurrentStep = useGuidedTour('EditView', (state) => state.setCurrentStep);\n  const {\n    allowedActions: { canCreate, canUpdate, canRegenerate },\n  } = useRBAC(permissions.settings?.['api-tokens']);\n  const [state, dispatch] = React.useReducer(reducer, initialState);\n  const match = useMatch('/settings/api-tokens/:id');\n  const id = match?.params?.id;\n  const isCreating = id === 'create';\n  const {\n    _unstableFormatAPIError: formatAPIError,\n    _unstableFormatValidationErrors: formatValidtionErrors,\n  } = useAPIErrorHandler();\n\n  const navigate = useNavigate();\n\n  const contentAPIPermissionsQuery = useGetPermissionsQuery();\n  const contentAPIRoutesQuery = useGetRoutesQuery();\n  /**\n   * Separate effects otherwise we could end\n   * up duplicating the same notification.\n   */\n  React.useEffect(() => {\n    if (contentAPIPermissionsQuery.error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(contentAPIPermissionsQuery.error),\n      });\n    }\n  }, [contentAPIPermissionsQuery.error, formatAPIError, toggleNotification]);\n\n  React.useEffect(() => {\n    if (contentAPIRoutesQuery.error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(contentAPIRoutesQuery.error),\n      });\n    }\n  }, [contentAPIRoutesQuery.error, formatAPIError, toggleNotification]);\n\n  React.useEffect(() => {\n    if (contentAPIPermissionsQuery.data) {\n      dispatch({\n        type: 'UPDATE_PERMISSIONS_LAYOUT',\n        value: contentAPIPermissionsQuery.data,\n      });\n    }\n  }, [contentAPIPermissionsQuery.data]);\n\n  React.useEffect(() => {\n    if (contentAPIRoutesQuery.data) {\n      dispatch({\n        type: 'UPDATE_ROUTES',\n        value: contentAPIRoutesQuery.data,\n      });\n    }\n  }, [contentAPIRoutesQuery.data]);\n\n  React.useEffect(() => {\n    if (apiToken) {\n      if (apiToken.type === 'read-only') {\n        dispatch({\n          type: 'ON_CHANGE_READ_ONLY',\n        });\n      }\n      if (apiToken.type === 'full-access') {\n        dispatch({\n          type: 'SELECT_ALL_ACTIONS',\n        });\n      }\n      if (apiToken.type === 'custom') {\n        dispatch({\n          type: 'UPDATE_PERMISSIONS',\n          value: apiToken?.permissions,\n        });\n      }\n    }\n  }, [apiToken]);\n\n  React.useEffect(() => {\n    trackUsage(isCreating ? 'didAddTokenFromList' : 'didEditTokenFromList', {\n      tokenType: API_TOKEN_TYPE,\n    });\n  }, [isCreating, trackUsage]);\n\n  const { data, error, isLoading } = useGetAPITokenQuery(id!, {\n    skip: !id || isCreating || !!apiToken,\n  });\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    }\n  }, [error, formatAPIError, toggleNotification]);\n\n  React.useEffect(() => {\n    if (data) {\n      setApiToken(data);\n\n      if (data.type === 'read-only') {\n        dispatch({\n          type: 'ON_CHANGE_READ_ONLY',\n        });\n      }\n      if (data.type === 'full-access') {\n        dispatch({\n          type: 'SELECT_ALL_ACTIONS',\n        });\n      }\n      if (data.type === 'custom') {\n        dispatch({\n          type: 'UPDATE_PERMISSIONS',\n          value: data?.permissions,\n        });\n      }\n    }\n  }, [data]);\n\n  React.useEffect(() => {\n    // Only set up timer when token is shown\n    if (showToken) {\n      hideTimerRef.current = setTimeout(() => {\n        setShowToken(false);\n      }, 30000); // 30 seconds\n\n      // Cleanup on unmount or when showToken changes\n      return () => {\n        if (hideTimerRef.current) {\n          clearTimeout(hideTimerRef.current);\n          hideTimerRef.current = null;\n        }\n      };\n    }\n  }, [showToken]);\n\n  const [createToken] = useCreateAPITokenMutation();\n  const [updateToken] = useUpdateAPITokenMutation();\n\n  interface FormValues extends Pick<Get.Response['data'], 'name' | 'description'> {\n    lifespan: Get.Response['data']['lifespan'] | undefined;\n    type: Get.Response['data']['type'] | undefined;\n  }\n\n  const handleSubmit = async (body: FormValues, formik: FormikHelpers<FormValues>) => {\n    trackUsage(isCreating ? 'willCreateToken' : 'willEditToken', {\n      tokenType: API_TOKEN_TYPE,\n    });\n\n    try {\n      if (isCreating) {\n        const res = await createToken({\n          ...body,\n          // lifespan must be \"null\" for unlimited (0 would mean instantly expired and isn't accepted)\n          lifespan:\n            body?.lifespan && body.lifespan !== '0' ? parseInt(body.lifespan.toString(), 10) : null,\n          permissions: body.type === 'custom' ? state.selectedActions : null,\n        });\n\n        if ('error' in res) {\n          if (isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n            formik.setErrors(formatValidtionErrors(res.error));\n          } else {\n            toggleNotification({\n              type: 'danger',\n              message: formatAPIError(res.error),\n            });\n          }\n\n          return;\n        }\n\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({\n            id: 'notification.success.apitokencreated',\n            defaultMessage: 'API Token successfully created',\n          }),\n        });\n\n        trackUsage('didCreateToken', {\n          type: res.data.type,\n          tokenType: API_TOKEN_TYPE,\n        });\n\n        navigate(`../api-tokens/${res.data.id.toString()}`, {\n          state: { apiToken: res.data },\n          replace: true,\n        });\n        setCurrentStep('apiTokens.success');\n      } else {\n        const res = await updateToken({\n          id: id!,\n          name: body.name,\n          description: body.description,\n          type: body.type,\n          permissions: body.type === 'custom' ? state.selectedActions : null,\n        });\n\n        if ('error' in res) {\n          if (isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n            formik.setErrors(formatValidtionErrors(res.error));\n          } else {\n            toggleNotification({\n              type: 'danger',\n              message: formatAPIError(res.error),\n            });\n          }\n\n          return;\n        }\n\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({\n            id: 'notification.success.apitokenedited',\n            defaultMessage: 'API Token successfully edited',\n          }),\n        });\n\n        trackUsage('didEditToken', {\n          type: res.data.type,\n          tokenType: API_TOKEN_TYPE,\n        });\n      }\n    } catch {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'Something went wrong',\n        }),\n      });\n    }\n  };\n\n  const [hasChangedPermissions, setHasChangedPermissions] = React.useState(false);\n\n  const handleChangeCheckbox = ({\n    target: { value },\n  }: Parameters<ApiTokenPermissionsContextValue['value']['onChange']>[0]) => {\n    setHasChangedPermissions(true);\n    dispatch({\n      type: 'ON_CHANGE',\n      value,\n    });\n  };\n\n  const handleChangeSelectAllCheckbox = ({\n    target: { value },\n  }: Parameters<ApiTokenPermissionsContextValue['value']['onChangeSelectAll']>[0]) => {\n    setHasChangedPermissions(true);\n    dispatch({\n      type: 'SELECT_ALL_IN_PERMISSION',\n      value,\n    });\n  };\n\n  const setSelectedAction = ({\n    target: { value },\n  }: Parameters<ApiTokenPermissionsContextValue['value']['setSelectedAction']>[0]) => {\n    dispatch({\n      type: 'SET_SELECTED_ACTION',\n      value,\n    });\n  };\n\n  const toggleToken = () => {\n    setShowToken((prev) => !prev);\n    if (hideTimerRef.current) {\n      clearTimeout(hideTimerRef.current);\n      hideTimerRef.current = null;\n    }\n  };\n\n  const providerValue = {\n    ...state,\n    onChange: handleChangeCheckbox,\n    onChangeSelectAll: handleChangeSelectAllCheckbox,\n    setSelectedAction,\n  };\n\n  const canEditInputs = (canUpdate && !isCreating) || (canCreate && isCreating);\n  const canShowToken = !!apiToken?.accessKey;\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <ApiTokenPermissionsProvider value={providerValue}>\n      <Page.Main>\n        <Page.Title>\n          {formatMessage(\n            { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n            { name: 'API Tokens' }\n          )}\n        </Page.Title>\n        <Formik\n          validationSchema={schema}\n          validateOnChange={false}\n          initialValues={{\n            name: apiToken?.name || '',\n            description: apiToken?.description || '',\n            type: apiToken?.type,\n            lifespan: apiToken?.lifespan,\n          }}\n          enableReinitialize\n          onSubmit={(body, actions) => handleSubmit(body, actions)}\n        >\n          {({ errors, handleChange, isSubmitting, values, setFieldValue }) => {\n            if (hasChangedPermissions && values?.type !== 'custom') {\n              setFieldValue('type', 'custom');\n            }\n\n            return (\n              <Form>\n                <FormHead\n                  title={{\n                    id: 'Settings.apiTokens.createPage.title',\n                    defaultMessage: 'Create API Token',\n                  }}\n                  token={apiToken}\n                  setToken={setApiToken}\n                  toggleToken={toggleToken}\n                  showToken={showToken}\n                  canEditInputs={canEditInputs}\n                  canRegenerate={canRegenerate}\n                  canShowToken={canShowToken}\n                  isSubmitting={isSubmitting}\n                  regenerateUrl=\"/admin/api-tokens/\"\n                />\n\n                <Layouts.Content>\n                  <Flex direction=\"column\" alignItems=\"stretch\" gap={6}>\n                    {apiToken?.accessKey && showToken && (\n                      <TokenBox token={apiToken.accessKey} tokenType={API_TOKEN_TYPE} />\n                    )}\n\n                    <FormApiTokenContainer\n                      errors={errors}\n                      onChange={handleChange}\n                      canEditInputs={canEditInputs}\n                      isCreating={isCreating}\n                      values={values}\n                      apiToken={apiToken}\n                      onDispatch={dispatch}\n                      setHasChangedPermissions={setHasChangedPermissions}\n                    />\n                    <Permissions\n                      disabled={\n                        !canEditInputs ||\n                        values?.type === 'read-only' ||\n                        values?.type === 'full-access'\n                      }\n                    />\n                  </Flex>\n                </Layouts.Content>\n              </Form>\n            );\n          }}\n        </Formik>\n      </Page.Main>\n    </ApiTokenPermissionsProvider>\n  );\n};\n\nexport const ProtectedEditView = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.['api-tokens'].read\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <EditView />\n    </Page.Protect>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAUA,aAAS,gBAAgB,OAAO,OAAO,WAAW,YAAY;AAC5D,UAAI,QAAQ,YAAY,GACpB,SAAS,MAAM;AAEnB,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,WAAW,MAAM,KAAK,GAAG,KAAK,GAAG;AACnC,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtBjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,cAAc;AADlB,QAEI,kBAAkB;AAFtB,QAGI,YAAY;AAHhB,QAII,YAAY;AAGhB,QAAI,aAAa,MAAM;AAGvB,QAAI,SAAS,WAAW;AAaxB,aAAS,YAAY,OAAO,QAAQ,UAAU,YAAY;AACxD,UAAI,UAAU,aAAa,kBAAkB,aACzC,QAAQ,IACR,SAAS,OAAO,QAChB,OAAO;AAEX,UAAI,UAAU,QAAQ;AACpB,iBAAS,UAAU,MAAM;AAAA,MAC3B;AACA,UAAI,UAAU;AACZ,eAAO,SAAS,OAAO,UAAU,QAAQ,CAAC;AAAA,MAC5C;AACA,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,YAAY,GACZ,QAAQ,OAAO,KAAK,GACpB,WAAW,WAAW,SAAS,KAAK,IAAI;AAE5C,gBAAQ,YAAY,QAAQ,MAAM,UAAU,WAAW,UAAU,KAAK,IAAI;AACxE,cAAI,SAAS,OAAO;AAClB,mBAAO,KAAK,MAAM,WAAW,CAAC;AAAA,UAChC;AACA,iBAAO,KAAK,OAAO,WAAW,CAAC;AAAA,QACjC;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClDjB;AAAA;AAAA,QAAI,cAAc;AAsBlB,aAAS,QAAQ,OAAO,QAAQ;AAC9B,aAAQ,SAAS,MAAM,UAAU,UAAU,OAAO,SAC9C,YAAY,OAAO,MAAM,IACzB;AAAA,IACN;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC5BjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,UAAU;AAyBd,QAAIA,QAAO,SAAS,OAAO;AAE3B,WAAO,UAAUA;AAAA;AAAA;;;;;;;ACvBjB,IAAMC,oBAAoBC,SAASC,gBAAgB;EACjDC,WAAW,CAACC,aAAa;IACvBC,gBAAgBD,QAAQE,MAAwD;MAC9EA,OAAO,MAAM;MACbC,mBAAmB,CAACC,aAAiDA,SAASC;IAChF,CAAA;IACAC,WAAWN,QAAQE,MAAmD;MACpEA,OAAO,MAAM;MACbC,mBAAmB,CAACC,aAA4CA,SAASC;IAC3E,CAAA;;EAEFE,kBAAkB;AACpB,CAAA;AAEA,IAAM,EAAEC,wBAAwBC,kBAAiB,IAAKb;;;;;ACiBtD,IAAM,CAACc,oCAAoCC,6BAA8B,IACvEC,0CAA+C,4BAAA;AAEjD,IAAMC,8BAA8B,CAAC,EACnCC,UACA,GAAGC,KACqC,MAAA;AACxC,aACEC,wBAACN,oCAAAA;IAAoC,GAAGK;IAAOD;;AAEnD;AAEMG,IAAAA,yBAAyB,MAAMN,8BAA8B,wBAAA;;;;;ACxB5D,IAAMO,wBAAwB,CAAC,EACpCC,SAAS,CAAA,GACTC,UACAC,eACAC,YACAC,SAAS,CAAA,GACTC,WAAW,CAAA,GACXC,YACAC,yBAAwB,MACG;AAC3B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,QAAMC,iCAAiC,CAAC,EAAEC,QAAQ,EAAEC,MAAK,EAAE,MAAiC;AAC1FL,6BAAyB,KAAA;AAEzB,QAAIK,UAAU,eAAe;AAC3BN,iBAAW;QACTO,MAAM;MACR,CAAA;IACF;AACA,QAAID,UAAU,aAAa;AACzBN,iBAAW;QACTO,MAAM;MACR,CAAA;IACF;EACF;AAEA,QAAMC,cAAc;IAClB;MACEF,OAAO;MACPG,OAAO;QACLC,IAAI;QACJC,gBAAgB;MAClB;IACF;IACA;MACEL,OAAO;MACPG,OAAO;QACLC,IAAI;QACJC,gBAAgB;MAClB;IACF;IACA;MACEL,OAAO;MACPG,OAAO;QACLC,IAAI;QACJC,gBAAgB;MAClB;IACF;EACD;AAED,aACEC,yBAACC,KAAAA;IACCC,YAAW;IACXC,WAAS;IACTC,QAAO;IACPC,YAAY;IACZC,eAAe;IACfC,aAAa;IACbC,cAAc;IAEd,cAAAC,0BAACC,MAAAA;MAAKC,WAAU;MAASC,YAAW;MAAUC,KAAK;;YACjDb,yBAACc,YAAAA;UAAWC,SAAQ;UAAQC,KAAI;oBAC7B1B,cAAc;YACbQ,IAAI;YACJC,gBAAgB;UAClB,CAAA;;YAEFU,0BAACQ,KAAKC,MAAI;UAACL,KAAK;;gBACdb,yBAACiB,KAAKE,MAAI;cAAYC,KAAK;cAAGC,IAAI;cAAIV,WAAU;cAASC,YAAW;cAClE,cAAAZ,yBAACsB,WAAAA;gBACCC,OAAOzC,OAAO,MAAO;gBACrBY,OAAOR,OAAO,MAAO;gBACrBF;gBACAD;;YALW,GAAA,MAAA;gBAQfiB,yBAACiB,KAAKE,MAAI;cAAmBC,KAAK;cAAGC,IAAI;cAAIV,WAAU;cAASC,YAAW;cACzE,cAAAZ,yBAACwB,kBAAAA;gBACCD,OAAOzC,OAAO,aAAc;gBAC5BY,OAAOR,OAAO,aAAc;gBAC5BF;gBACAD;;YALW,GAAA,aAAA;gBAQfiB,yBAACiB,KAAKE,MAAI;cAAgBC,KAAK;cAAGC,IAAI;cAAIV,WAAU;cAASC,YAAW;cACtE,cAAAZ,yBAACyB,eAAAA;gBACCxC;gBACAsC,OAAOzC,OAAO,UAAW;gBACzBY,OAAOR,OAAO,UAAW;gBACzBH;gBACA2C,OAAOvC;;YANI,GAAA,UAAA;gBAUfa,yBAACiB,KAAKE,MAAI;cAAYC,KAAK;cAAGC,IAAI;cAAIV,WAAU;cAASC,YAAW;cAClE,cAAAZ,yBAAC2B,iBAAAA;gBACCjC,OAAOR,OAAO,MAAO;gBACrBqC,OAAOzC,OAAO,MAAO;gBACrBe,OAAO;kBACLC,IAAI;kBACJC,gBAAgB;gBAClB;gBACAhB,UAAU,CAACW,UAAAA;AAETF,iDAA+B;oBAAEC,QAAQ;sBAAEC;oBAAM;kBAAE,CAAA;AAGnDX,2BAAS;oBAAEU,QAAQ;sBAAEmC,MAAM;sBAAQlC;oBAAM;kBAAE,CAAA;gBAC7C;gBACAmC,SAASjC;gBACTZ;;YAhBW,GAAA,MAAA;;;;;;AAuBzB;;;;;;;;;;;;AChIA,IAAM8C,iBAAiB,CAACC,SAAAA;AACtB,UAAQA,MAAAA;IACN,KAAK,QAAQ;AACX,aAAO;QACLC,MAAM;QACNC,QAAQ;QACRC,YAAY;MACd;IACF;IACA,KAAK,OAAO;AACV,aAAO;QACLF,MAAM;QACNC,QAAQ;QACRC,YAAY;MACd;IACF;IACA,KAAK,OAAO;AACV,aAAO;QACLF,MAAM;QACNC,QAAQ;QACRC,YAAY;MACd;IACF;IACA,KAAK,UAAU;AACb,aAAO;QACLF,MAAM;QACNC,QAAQ;QACRC,YAAY;MACd;IACF;IACA,SAAS;AACP,aAAO;QACLF,MAAM;QACNC,QAAQ;QACRC,YAAY;MACd;IACF;EACF;AACF;AAEA,IAAMC,YAAYC,GAAqBC,GAAAA;;mBAEpB,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAO,CAAE,CAAA,QAAQ,CAAC,EAAED,MAAK,MAAOA,MAAMC,OAAO,CAAA,CAAE;;AAW1EC,IAAAA,aAAa,CAAC,EACzBC,QAAQ;EACNC,SAAS;EACTC,QAAQ;EACRC,MAAM;AACR,EAAC,MACe;AAChB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,QAAM,EAAEH,QAAQD,SAASK,OAAOH,KAAI,IAAKH;AACzC,QAAMO,iBAAiBJ,WAAOK,YAAAA,SAAKL,KAAKM,MAAM,GAAA,CAAA,IAAQ,CAAA;AACtD,QAAM,CAACC,aAAa,IAAIC,SAAS,EAAE,IAAIL,QAAQA,MAAMG,MAAM,GAAA,IAAO,CAAA;AAClE,QAAMG,SAASvB,eAAeW,MAAME,MAAM;AAE1C,aACEW,0BAACC,MAAAA;IAAKC,WAAU;IAASC,YAAW;IAAUC,KAAK;;UACjDJ,0BAACK,YAAAA;QAAWC,SAAQ;QAAQC,KAAI;;UAC7BhB,cAAc;YACbiB,IAAI;YACJC,gBAAgB;UAClB,CAAA;UAAG;cAEHC,yBAACC,QAAAA;YAAMd,UAAAA;;cACPG,0BAACK,YAAAA;YAAWC,SAAQ;YAAQM,WAAU;;cAAa;cAC/Cd;;;;;UAGNE,0BAACC,MAAAA;QAAKY,WAAS;QAACjC,YAAW;QAAWkC,aAAY;QAAaV,KAAK;;cAClEM,yBAAC7B,WAAAA;YAAUD,YAAYmB,OAAOnB;YAAYkC,aAAaf,OAAOpB;YAAQoC,SAAS;YAC7E,cAAAL,yBAACL,YAAAA;cAAWW,YAAW;cAAOJ,WAAWb,OAAOrB;cAC7CW,UAAAA;;;cAGLqB,yBAAC3B,KAAAA;YAAIkC,aAAa;YAAGC,cAAc;0BAChCC,WAAAA,SAAIzB,gBAAgB,CAAC0B,cACpBpB,0BAACK,YAAAA;cAAuBO,WAAWQ,MAAMC,SAAS,GAAA,IAAO,eAAe;;gBAAc;gBAClFD;;YADaA,GAAAA,KAAAA,CAAAA;;;;;;AAQ7B;;;ICvGaE,oBAAoB,MAAA;AAC/B,QAAM,EACJC,OAAO,EAAEC,gBAAgBC,OAAM,EAAE,IAC/BC,uBAAAA;AACJ,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAMC,gBAAgBL,iDAAgBM,MAAM,KAAK;AAEjD,aACEC,yBAACC,KAAKC,MAAI;IACRC,KAAK;IACLC,YAAW;IACXC,YAAY;IACZC,eAAe;IACfC,aAAa;IACbC,cAAc;IACdC,OAAO;MAAEC,WAAW;IAAO;IAC3BC,WAAU;IACVC,YAAW;IAEVnB,UAAAA,qBACCO,yBAACa,MAAAA;MAAKF,WAAU;MAASC,YAAW;MAAUE,KAAK;gBAChDhB,iBACCA,iBAAiBJ,UACjBA,OAAOI,aAAAA,EAAeiB,IAAI,CAACC,UAAAA;;AACzB,iBAAOA,iBAAMC,OAAOC,SAAbF,mBAAmBG,UAAnBH,mBAA0BI,SAAS3B,oBACxCuB,MAAMK,YAAY5B,qBAClBO,yBAACsB,YAAAA;UAA+BN;QAAfA,GAAAA,MAAMK,OAAO,IAC5B;MACN,CAAA;aAGJE,0BAACV,MAAAA;MAAKF,WAAU;MAASC,YAAW;MAAUE,KAAK;;YACjDd,yBAACwB,YAAAA;UAAWC,SAAQ;UAAQC,KAAI;oBAC7B9B,cAAc;YACb+B,IAAI;YACJC,gBAAgB;UAClB,CAAA;;YAEF5B,yBAACwB,YAAAA;UAAWE,KAAI;UAAIG,WAAU;oBAC3BjC,cAAc;YACb+B,IAAI;YACJC,gBACE;UACJ,CAAA;;;;;AAMZ;;;;;;;;;ACrCA,IAAME,8BAA8BC;gBACpB,CAACC,UAAUA,MAAMC,MAAMC,OAAOC,UAAU;;;;;;AAOxD,IAAMC,kBAAkBC,GAAqBC,GAAAA;;;;;;;;cAQ/B,CAACN,UAAUA,MAAMC,MAAMC,OAAOK,UAAU;;;;;IAKlD,CAACP,UAAUA,MAAMQ,aAAaV,2BAA4B;;MAExDA,2BAA4B;;;AAIlC,IAAMW,SAASJ,GAAOK;;;0BAGI,CAAC,EAAET,MAAK,MAAOA,MAAMC,OAAOS,UAAU;;AAUnDC,IAAAA,yBAAyB,CAAC,EACrCC,cAAc,CAAA,GACdC,OACAC,cAAc,GACdC,WAAW,MAAK,MACY;AAC5B,QAAM,EACJC,OAAO,EAAEC,mBAAmBC,UAAUC,iBAAiBC,mBAAmBC,eAAc,EAAE,IACxFC,uBAAAA;AACJ,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,QAAMC,mBAAmB,CAACC,aAAqBA,aAAaL;AAE5D,aACEM,0BAACC,UAAUC,MAAI;IAACb,OAAO,GAAGH,KAAAA,IAASC,WAAAA;;UACjCgB,yBAACF,UAAUG,QAAM;QAACC,SAASlB,cAAc,IAAI,YAAY;sBACvDgB,yBAACF,UAAUK,SAAO;wBAAEC,kBAAAA,SAAWrB,KAAAA;;;UAEjCiB,yBAACF,UAAUO,SAAO;QACfvB,UAAAA,2CAAawB,IAAI,CAACC,eAAAA;AACjB,gBAAMC,qBAAqBD,WAAWE,QAAQC,MAAM,CAACC,WACnDtB,gBAAgBuB,SAASD,OAAOf,QAAQ,CAAA;AAG1C,gBAAMiB,sBAAsBN,WAAWE,QAAQK,KAAK,CAACH,WACnDtB,gBAAgBuB,SAASD,OAAOf,QAAQ,CAAA;AAG1C,qBACEC,0BAACtB,KAAAA;;kBACCsB,0BAACkB,MAAAA;gBAAKC,gBAAe;gBAAgBC,YAAW;gBAASC,SAAS;;sBAChElB,yBAACzB,KAAAA;oBAAI4C,cAAc;oBACjB,cAAAnB,yBAACoB,YAAAA;sBAAWlB,SAAQ;sBAAQmB,WAAU;gCACnCd,yCAAYA;;;sBAGjBP,yBAACtB,QAAAA,CAAAA,CAAAA;sBACDsB,yBAACzB,KAAAA;oBAAI+C,aAAa;oBAChB,cAAAtB,yBAACuB,cAAAA;sBACCC,SACE,CAAChB,sBAAsBK,sBACnB,kBACAL;sBAENiB,iBAAiB,MAAA;AACftC,0CAAkB;0BAAEuC,QAAQ;4BAAExC,OAAO;8BAAIqB,GAAAA,WAAWE;4BAAQ;0BAAC;wBAAE,CAAA;sBACjE;sBACAxB;gCAECQ,cAAc;wBAAEkC,IAAI;wBAAwBC,gBAAgB;sBAAa,CAAA;;;;;kBAIhF5B,yBAAC6B,KAAKC,MAAI;gBAACC,KAAK;gBAAGb,SAAS;gBACzBX,WAAAA,yCAAYE,aACXF,yCAAYE,QAAQH,IAAI,CAACK,WAAAA;AACvB,6BACEX,yBAAC6B,KAAK9B,MAAI;oBACRiC,KAAK;oBAELC,WAAU;oBACVhB,YAAW;oBAEX,cAAApB,0BAACxB,iBAAAA;sBACCI,WAAWkB,iBAAiBgB,OAAOf,QAAQ;sBAC3CsB,SAAS;sBACTgB,WAAS;;4BAETlC,yBAACuB,cAAAA;0BACCC,SAASnC,gBAAgBuB,SAASD,OAAOf,QAAQ;0BACjDuC,MAAMxB,OAAOf;0BACb6B,iBAAiB,MAAA;AACfrC,qCAAS;8BAAEsC,QAAQ;gCAAExC,OAAOyB,OAAOf;8BAAS;4BAAE,CAAA;0BAChD;0BACAX;0BAEC0B,UAAAA,OAAOA;;4BAEVX,yBAACoC,UAAAA;0BACCC,MAAK;0BACLC,eAAY;0BACZC,SAAS,MACPjD,kBAAkB;4BAAEoC,QAAQ;8BAAExC,OAAOyB,OAAOf;4BAAS;0BAAE,CAAA;0BAEzD4C,OAAO;4BAAEC,SAAS;4BAAexB,YAAY;0BAAS;0BAEtD,cAAAjB,yBAAC0C,eAAAA;4BAAIf,IAAG;;;;;kBA3BPhB,GAAAA,OAAOf,QAAQ;gBAgC1B;;;UA9DI,GAAA,GAAGb,KAAM,IAAGwB,yCAAYA,UAAAA,EAAY;QAkElD;;;;AAIR;;;ACpJO,IAAMoC,sBAAsB,CAAC,EAAEC,UAAU,MAAM,GAAGC,MAAiC,MAAA;AACxF,aACEC,yBAACC,KAAAA;kBACCD,yBAACE,UAAUC,MAAI;MAACC,MAAK;MAClBN,UAAAA,WACCA,QAAQO,IAAI,CAACC,KAAKC,cAChBP,yBAACQ,wBAAAA;QAECC,OAAOH,IAAIG;QACXC,aAAaJ,IAAII;QACjBC,aAAaJ;QACZ,GAAGR;MAJCO,GAAAA,IAAIM,KAAK,CAAA;;;AAU5B;;;ACnBaC,IAAAA,cAAc,CAAC,EAAE,GAAGC,MAAO,MAAA;AACtC,QAAM,EACJC,OAAO,EAAEC,KAAI,EAAE,IACbC,uBAAAA;AACJ,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEC,0BAACC,KAAKC,MAAI;IAACC,KAAK;IAAGC,QAAO;IAAeC,WAAS;IAACC,YAAW;;UAC5DN,0BAACC,KAAKM,MAAI;QACRC,KAAK;QACLC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,cAAc;QACdC,WAAU;QACVC,YAAW;QACXX,KAAK;;cAELH,0BAACe,MAAAA;YAAKF,WAAU;YAASC,YAAW;YAAUX,KAAK;;kBACjDa,yBAACC,YAAAA;gBAAWC,SAAQ;gBAAQC,KAAI;0BAC7BrB,cAAc;kBACbsB,IAAI;kBACJC,gBAAgB;gBAClB,CAAA;;kBAEFL,yBAACC,YAAAA;gBAAWE,KAAI;gBAAIG,WAAU;0BAC3BxB,cAAc;kBACbsB,IAAI;kBACJC,gBAAgB;gBAClB,CAAA;;;;WAGHzB,6BAAM2B,oBAAeP,yBAACQ,qBAAAA;YAAoBC,SAAS7B,6BAAM2B;YAAc,GAAG7B;;;;UAE7EsB,yBAACU,mBAAAA,CAAAA,CAAAA;;;AAGP;;;ICzCaC,SAAaC,QAAM,EAAGC,MAAM;EACvCC,MAAUC,OAAM,EAAGC,IAAI,GAAKC,EAAAA,SAASC,YAAiBD,SAASE,EAAE;EACjEC,MACGL,OAAM,EACNM,MAAM;IAAC;IAAa;IAAe;EAAS,CAAA,EAC5CJ,SAASC,YAAiBD,SAASE,EAAE;EACxCG,aAAiBP,OAAM,EAAGQ,SAAQ;EAClCC,UAAcC,QAAM,EAAGC,QAAO,EAAGC,IAAI,CAAGJ,EAAAA,SAAQ,EAAGK,QAAQV,YAAiBD,SAASE,EAAE;AACzF,CAAG;;;;;;ACDI,IAAMU,2BAA2B,CAACC,SAAAA;AACvC,QAAMC,SAAiB;IACrBC,eAAe,CAAA;IACfC,aAAa,CAAA;EACf;AAEAF,SAAOE,cAAcC,OAAOC,QAAQL,IAAAA,EAAMM,IAAI,CAAC,CAACC,OAAOC,UAAAA,OAAiB;IACtED;IACAE,OAAOF,MAAMG,MAAM,IAAA,EAAM,CAAE;IAC3BC,aAAaP,OAAOQ,KAAKJ,WAAWG,WAAW,EAC5CL,IAAI,CAACO,gBAAgB;MACpBA;MACAC,SACED,cAAcL,WAAWG,cACrBH,WAAWG,YAAYE,UAAW,EAC/BP,IAAI,CAACS,WAAAA;AACJ,cAAMC,WAAW,GAAGT,KAAM,IAAGM,UAAW,IAAGE,MAAAA;AAE3C,YAAIR,MAAMU,SAAS,OAAU,GAAA;AAC3BhB,iBAAOC,cAAcgB,KAAKF,QAAAA;QAC5B;AAEA,eAAO;UACLD;UACAC;QACF;OAEDG,EAAAA,KAAI,IACP,CAAA;IACR,EAAA,EACCA,KAAI;IACT;AAEA,SAAOlB;AACT;;;ICiBamB,eAA6B;EACxCC,MAAM;IACJC,eAAe,CAAA;IACfC,aAAa,CAAA;EACf;EACAC,QAAQ,CAAA;EACRC,gBAAgB;EAChBC,iBAAiB,CAAA;AACnB;IAEaC,UAAU,CAACC,OAAqBC,WAC3CC,GAAQF,OAAO,CAACG,eAAAA;AACd,UAAQF,OAAOG,MAAI;IACjB,KAAK,aAAa;AAChB,UAAID,WAAWL,gBAAgBO,SAASJ,OAAOK,KAAK,GAAG;AACrDC,wBAAAA,SAAKJ,WAAWL,iBAAiBG,OAAOK,KAAK;aACxC;AACLH,mBAAWL,gBAAgBU,KAAKP,OAAOK,KAAK;MAC9C;AACA;IACF;IACA,KAAK,4BAA4B;AAC/B,YAAMG,iBAAiBR,OAAOK,MAAMI,MAAM,CAACC,SACzCR,WAAWL,gBAAgBO,SAASM,KAAKC,QAAQ,CAAA;AAGnD,UAAIH,gBAAgB;AAClBR,eAAOK,MAAMO,QAAQ,CAACF,SAAAA;AACpBJ,0BAAAA,SAAKJ,WAAWL,iBAAiBa,KAAKC,QAAQ;QAChD,CAAA;aACK;AACLX,eAAOK,MAAMO,QAAQ,CAACF,SAAAA;AACpBR,qBAAWL,gBAAgBU,KAAKG,KAAKC,QAAQ;QAC/C,CAAA;MACF;AACA;IACF;IAEA,KAAK,sBAAsB;AACzBT,iBAAWL,kBAAkB;WAAIK,WAAWV,KAAKC;MAAc;AAE/D;IACF;IACA,KAAK,uBAAuB;AAC1B,YAAMoB,sBAAsBX,WAAWV,KAAKC,cAAcqB,OACxD,CAACH,aAAaA,SAASP,SAAS,MAAWO,KAAAA,SAASP,SAAS,SAAA,CAAA;AAE/DF,iBAAWL,kBAAkB;QAAIgB,GAAAA;MAAoB;AACrD;IACF;IACA,KAAK,6BAA6B;AAChCX,iBAAWV,OAAOuB,yBAAyBf,OAAOK,KAAK;AACvD;IACF;IACA,KAAK,iBAAiB;AACpBH,iBAAWP,SAAS;QAAE,GAAGK,OAAOK;MAAM;AACtC;IACF;IACA,KAAK,sBAAsB;AACzBH,iBAAWL,kBAAkB;QAAIG,GAAAA,OAAOK;MAAM;AAC9C;IACF;IACA,KAAK,uBAAuB;AAC1BH,iBAAWN,iBAAiBI,OAAOK;AACnC;IACF;IACA;AACE,aAAOH;EACX;CACC;;;IC1FQc,WAAW,MAAA;;AACtB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,OAAOC,cAAa,IAAKC,YAAAA;AACjC,QAAMC,cAAcC,iBAAiB,CAACJ,WAAUA,OAAMK,UAAUF,WAAW;AAC3E,QAAM,CAACG,UAAUC,WAAY,IAASC,iBACpCP,oDAAeK,aAAfL,mBAAyBQ,aACrB;IACE,GAAGR,cAAcK;MAEnB,IAAA;AAEN,QAAM,CAACI,WAAWC,YAAa,IAASH,eAASI,SAAQX,oDAAeK,aAAfL,mBAAyBQ,SAAAA,CAAAA;AAClF,QAAMI,eAAqBC,aAA6C,IAAA;AACxE,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAMC,iBAAiBC,cAAc,YAAY,CAAClB,WAAUA,OAAMiB,cAAc;AAChF,QAAM,EACJE,gBAAgB,EAAEC,WAAWC,WAAWC,cAAa,EAAE,IACrDC,SAAQpB,iBAAYqB,aAAZrB,mBAAuB,aAAa;AAChD,QAAM,CAACH,OAAOyB,QAAAA,IAAkBC,iBAAWC,SAASC,YAAAA;AACpD,QAAMC,QAAQC,SAAS,0BAAA;AACvB,QAAMC,MAAKF,oCAAOG,WAAPH,mBAAeE;AAC1B,QAAME,aAAaF,OAAO;AAC1B,QAAM,EACJG,yBAAyBC,gBACzBC,iCAAiCC,sBAAqB,IACpDC,mBAAAA;AAEJ,QAAMC,WAAWC,YAAAA;AAEjB,QAAMC,6BAA6BC,uBAAAA;AACnC,QAAMC,wBAAwBC,kBAAAA;AAK9BC,EAAMC,gBAAU,MAAA;AACd,QAAIL,2BAA2BM,OAAO;AACpCjD,yBAAmB;QACjBkD,MAAM;QACNC,SAASd,eAAeM,2BAA2BM,KAAK;MAC1D,CAAA;IACF;KACC;IAACN,2BAA2BM;IAAOZ;IAAgBrC;EAAmB,CAAA;AAEzE+C,EAAMC,gBAAU,MAAA;AACd,QAAIH,sBAAsBI,OAAO;AAC/BjD,yBAAmB;QACjBkD,MAAM;QACNC,SAASd,eAAeQ,sBAAsBI,KAAK;MACrD,CAAA;IACF;KACC;IAACJ,sBAAsBI;IAAOZ;IAAgBrC;EAAmB,CAAA;AAEpE+C,EAAMC,gBAAU,MAAA;AACd,QAAIL,2BAA2BS,MAAM;AACnCzB,eAAS;QACPuB,MAAM;QACNG,OAAOV,2BAA2BS;MACpC,CAAA;IACF;KACC;IAACT,2BAA2BS;EAAK,CAAA;AAEpCL,EAAMC,gBAAU,MAAA;AACd,QAAIH,sBAAsBO,MAAM;AAC9BzB,eAAS;QACPuB,MAAM;QACNG,OAAOR,sBAAsBO;MAC/B,CAAA;IACF;KACC;IAACP,sBAAsBO;EAAK,CAAA;AAE/BL,EAAMC,gBAAU,MAAA;AACd,QAAIxC,UAAU;AACZ,UAAIA,SAAS0C,SAAS,aAAa;AACjCvB,iBAAS;UACPuB,MAAM;QACR,CAAA;MACF;AACA,UAAI1C,SAAS0C,SAAS,eAAe;AACnCvB,iBAAS;UACPuB,MAAM;QACR,CAAA;MACF;AACA,UAAI1C,SAAS0C,SAAS,UAAU;AAC9BvB,iBAAS;UACPuB,MAAM;UACNG,OAAO7C,qCAAUH;QACnB,CAAA;MACF;IACF;KACC;IAACG;EAAS,CAAA;AAEbuC,EAAMC,gBAAU,MAAA;AACd/B,eAAWkB,aAAa,wBAAwB,wBAAwB;MACtEmB,WAAWC;IACb,CAAA;KACC;IAACpB;IAAYlB;EAAW,CAAA;AAE3B,QAAM,EAAEmC,MAAMH,OAAOO,UAAS,IAAKC,oBAAoBxB,IAAK;IAC1DyB,MAAM,CAACzB,MAAME,cAAc,CAAC,CAAC3B;EAC/B,CAAA;AAEAuC,EAAMC,gBAAU,MAAA;AACd,QAAIC,OAAO;AACTjD,yBAAmB;QACjBkD,MAAM;QACNC,SAASd,eAAeY,KAAAA;MAC1B,CAAA;IACF;KACC;IAACA;IAAOZ;IAAgBrC;EAAmB,CAAA;AAE9C+C,EAAMC,gBAAU,MAAA;AACd,QAAII,MAAM;AACR3C,kBAAY2C,IAAAA;AAEZ,UAAIA,KAAKF,SAAS,aAAa;AAC7BvB,iBAAS;UACPuB,MAAM;QACR,CAAA;MACF;AACA,UAAIE,KAAKF,SAAS,eAAe;AAC/BvB,iBAAS;UACPuB,MAAM;QACR,CAAA;MACF;AACA,UAAIE,KAAKF,SAAS,UAAU;AAC1BvB,iBAAS;UACPuB,MAAM;UACNG,OAAOD,6BAAM/C;QACf,CAAA;MACF;IACF;KACC;IAAC+C;EAAK,CAAA;AAETL,EAAMC,gBAAU,MAAA;AAEd,QAAIpC,WAAW;AACbG,mBAAa4C,UAAUC,WAAW,MAAA;AAChC/C,qBAAa,KAAA;MACf,GAAG,GAAA;AAGH,aAAO,MAAA;AACL,YAAIE,aAAa4C,SAAS;AACxBE,uBAAa9C,aAAa4C,OAAO;AACjC5C,uBAAa4C,UAAU;QACzB;MACF;IACF;KACC;IAAC/C;EAAU,CAAA;AAEd,QAAM,CAACkD,WAAAA,IAAeC,0BAAAA;AACtB,QAAM,CAACC,WAAAA,IAAeC,0BAAAA;AAOtB,QAAMC,eAAe,OAAOC,MAAkBC,WAAAA;AAC5CnD,eAAWkB,aAAa,oBAAoB,iBAAiB;MAC3DmB,WAAWC;IACb,CAAA;AAEA,QAAI;AACF,UAAIpB,YAAY;AACd,cAAMkC,MAAM,MAAMP,YAAY;UAC5B,GAAGK;;UAEHG,WACEH,6BAAMG,aAAYH,KAAKG,aAAa,MAAMC,SAASJ,KAAKG,SAASE,SAAQ,GAAI,EAAM,IAAA;UACrFnE,aAAa8D,KAAKjB,SAAS,WAAWhD,MAAMuE,kBAAkB;QAChE,CAAA;AAEA,YAAI,WAAWJ,KAAK;AAClB,cAAIK,iBAAiBL,IAAIpB,KAAK,KAAKoB,IAAIpB,MAAM0B,SAAS,mBAAmB;AACvEP,mBAAOQ,UAAUrC,sBAAsB8B,IAAIpB,KAAK,CAAA;iBAC3C;AACLjD,+BAAmB;cACjBkD,MAAM;cACNC,SAASd,eAAegC,IAAIpB,KAAK;YACnC,CAAA;UACF;AAEA;QACF;AAEAjD,2BAAmB;UACjBkD,MAAM;UACNC,SAASrD,cAAc;YACrBmC,IAAI;YACJ4C,gBAAgB;UAClB,CAAA;QACF,CAAA;AAEA5D,mBAAW,kBAAkB;UAC3BiC,MAAMmB,IAAIjB,KAAKF;UACfI,WAAWC;QACb,CAAA;AAEAd,iBAAS,iBAAiB4B,IAAIjB,KAAKnB,GAAGuC,SAAQ,CAAG,IAAG;UAClDtE,OAAO;YAAEM,UAAU6D,IAAIjB;UAAK;UAC5B0B,SAAS;QACX,CAAA;AACA3D,uBAAe,mBAAA;aACV;AACL,cAAMkD,MAAM,MAAML,YAAY;UAC5B/B;UACA0C,MAAMR,KAAKQ;UACXI,aAAaZ,KAAKY;UAClB7B,MAAMiB,KAAKjB;UACX7C,aAAa8D,KAAKjB,SAAS,WAAWhD,MAAMuE,kBAAkB;QAChE,CAAA;AAEA,YAAI,WAAWJ,KAAK;AAClB,cAAIK,iBAAiBL,IAAIpB,KAAK,KAAKoB,IAAIpB,MAAM0B,SAAS,mBAAmB;AACvEP,mBAAOQ,UAAUrC,sBAAsB8B,IAAIpB,KAAK,CAAA;iBAC3C;AACLjD,+BAAmB;cACjBkD,MAAM;cACNC,SAASd,eAAegC,IAAIpB,KAAK;YACnC,CAAA;UACF;AAEA;QACF;AAEAjD,2BAAmB;UACjBkD,MAAM;UACNC,SAASrD,cAAc;YACrBmC,IAAI;YACJ4C,gBAAgB;UAClB,CAAA;QACF,CAAA;AAEA5D,mBAAW,gBAAgB;UACzBiC,MAAMmB,IAAIjB,KAAKF;UACfI,WAAWC;QACb,CAAA;MACF;IACF,QAAQ;AACNvD,yBAAmB;QACjBkD,MAAM;QACNC,SAASrD,cAAc;UACrBmC,IAAI;UACJ4C,gBAAgB;QAClB,CAAA;MACF,CAAA;IACF;EACF;AAEA,QAAM,CAACG,uBAAuBC,wBAAAA,IAAkCvE,eAAS,KAAA;AAEzE,QAAMwE,uBAAuB,CAAC,EAC5BC,QAAQ,EAAE9B,MAAK,EAAE,MACmD;AACpE4B,6BAAyB,IAAA;AACzBtD,aAAS;MACPuB,MAAM;MACNG;IACF,CAAA;EACF;AAEA,QAAM+B,gCAAgC,CAAC,EACrCD,QAAQ,EAAE9B,MAAK,EAAE,MAC4D;AAC7E4B,6BAAyB,IAAA;AACzBtD,aAAS;MACPuB,MAAM;MACNG;IACF,CAAA;EACF;AAEA,QAAMgC,oBAAoB,CAAC,EACzBF,QAAQ,EAAE9B,MAAK,EAAE,MAC4D;AAC7E1B,aAAS;MACPuB,MAAM;MACNG;IACF,CAAA;EACF;AAEA,QAAMiC,cAAc,MAAA;AAClBzE,iBAAa,CAAC0E,SAAS,CAACA,IAAAA;AACxB,QAAIxE,aAAa4C,SAAS;AACxBE,mBAAa9C,aAAa4C,OAAO;AACjC5C,mBAAa4C,UAAU;IACzB;EACF;AAEA,QAAM6B,gBAAgB;IACpB,GAAGtF;IACHuF,UAAUP;IACVQ,mBAAmBN;IACnBC;EACF;AAEA,QAAMM,gBAAiBpE,aAAa,CAACY,cAAgBb,aAAaa;AAClE,QAAMyD,eAAe,CAAC,EAACpF,qCAAUG;AAEjC,MAAI6C,WAAW;AACb,eAAOqC,yBAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,aACEF,yBAACG,6BAAAA;IAA4B3C,OAAOmC;kBAClCS,0BAACH,KAAKI,MAAI;;YACRL,yBAACC,KAAKK,OAAK;oBACRrG,cACC;YAAEmC,IAAI;YAAsB4C,gBAAgB;aAC5C;YAAEF,MAAM;UAAa,CAAA;;YAGzBkB,yBAACO,QAAAA;UACCC,kBAAkBC;UAClBC,kBAAkB;UAClBC,eAAe;YACb7B,OAAMnE,qCAAUmE,SAAQ;YACxBI,cAAavE,qCAAUuE,gBAAe;YACtC7B,MAAM1C,qCAAU0C;YAChBoB,UAAU9D,qCAAU8D;UACtB;UACAmC,oBAAkB;UAClBC,UAAU,CAACvC,MAAMwC,YAAYzC,aAAaC,MAAMwC,OAAAA;oBAE/C,CAAC,EAAEC,QAAQC,cAAcC,cAAcC,QAAQC,cAAa,MAAE;AAC7D,gBAAIhC,0BAAyB+B,iCAAQ7D,UAAS,UAAU;AACtD8D,4BAAc,QAAQ,QAAA;YACxB;AAEA,uBACEf,0BAACgB,MAAAA;;oBACCpB,yBAACqB,UAAAA;kBACCC,OAAO;oBACLlF,IAAI;oBACJ4C,gBAAgB;kBAClB;kBACAuC,OAAO5G;kBACP6G,UAAU5G;kBACV6E;kBACA1E;kBACA+E;kBACAnE;kBACAoE;kBACAkB;kBACAQ,eAAc;;oBAGhBzB,yBAAC0B,QAAQC,SAAO;kBACd,cAAAvB,0BAACwB,MAAAA;oBAAKC,WAAU;oBAASC,YAAW;oBAAUC,KAAK;;uBAChDpH,qCAAUG,cAAaC,iBACtBiF,yBAACgC,UAAAA;wBAAST,OAAO5G,SAASG;wBAAW2C,WAAWC;;0BAGlDsC,yBAACiC,uBAAAA;wBACClB;wBACAnB,UAAUoB;wBACVlB;wBACAxD;wBACA4E;wBACAvG;wBACAuH,YAAYpG;wBACZsD;;0BAEFY,yBAACmC,aAAAA;wBACCC,UACE,CAACtC,kBACDoB,iCAAQ7D,UAAS,gBACjB6D,iCAAQ7D,UAAS;;;;;;;UAO/B;;;;;AAKV;IAEagF,oBAAoB,MAAA;AAC/B,QAAM7H,cAAcC,iBAClB,CAACJ,UAAAA;;AAAUA,uBAAMK,UAAUF,YAAYqB,aAA5BxB,mBAAuC,cAAciI;GAAAA;AAGlE,aACEtC,yBAACC,KAAKsC,SAAO;IAAC/H;IACZ,cAAAwF,yBAAChG,UAAAA,CAAAA,CAAAA;;AAGP;", "names": ["pull", "contentApiService", "adminApi", "injectEndpoints", "endpoints", "builder", "getPermissions", "query", "transformResponse", "response", "data", "getRoutes", "overrideExisting", "useGetPermissionsQuery", "useGetRoutesQuery", "ApiTokenPermissionsContextProvider", "useApiTokenPermissionsContext", "createContext", "ApiTokenPermissionsProvider", "children", "rest", "_jsx", "useApiTokenPermissions", "FormApiTokenContainer", "errors", "onChange", "canEditInputs", "isCreating", "values", "apiToken", "onDispatch", "setHasChangedPermissions", "formatMessage", "useIntl", "handleChangeSelectApiTokenType", "target", "value", "type", "typeOptions", "label", "id", "defaultMessage", "_jsx", "Box", "background", "hasRadius", "shadow", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "_jsxs", "Flex", "direction", "alignItems", "gap", "Typography", "variant", "tag", "Grid", "Root", "<PERSON><PERSON>", "col", "xs", "TokenName", "error", "TokenDescription", "LifeSpanInput", "token", "TokenTypeSelect", "name", "options", "getMethodColor", "verb", "text", "border", "background", "MethodBox", "styled", "Box", "theme", "spaces", "BoundRoute", "route", "handler", "method", "path", "formatMessage", "useIntl", "title", "formattedRoute", "tail", "split", "controller", "action", "colors", "_jsxs", "Flex", "direction", "alignItems", "gap", "Typography", "variant", "tag", "id", "defaultMessage", "_jsx", "span", "textColor", "hasRadius", "borderColor", "padding", "fontWeight", "paddingLeft", "paddingRight", "map", "value", "includes", "ActionBoundRoutes", "value", "selectedAction", "routes", "useApiTokenPermissions", "formatMessage", "useIntl", "actionSection", "split", "_jsx", "Grid", "<PERSON><PERSON>", "col", "background", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "style", "minHeight", "direction", "alignItems", "Flex", "gap", "map", "route", "config", "auth", "scope", "includes", "handler", "BoundRoute", "_jsxs", "Typography", "variant", "tag", "id", "defaultMessage", "textColor", "activeCheckboxWrapperStyles", "css", "props", "theme", "colors", "primary100", "CheckboxWrapper", "styled", "Box", "primary600", "$isActive", "Border", "div", "neutral150", "CollapsableContentType", "controllers", "label", "orderNumber", "disabled", "value", "onChangeSelectAll", "onChange", "selectedActions", "setSelectedAction", "selectedAction", "useApiTokenPermissions", "formatMessage", "useIntl", "isActionSelected", "actionId", "_jsxs", "Accordion", "<PERSON><PERSON>", "_jsx", "Header", "variant", "<PERSON><PERSON>", "capitalize", "Content", "map", "controller", "allActionsSelected", "actions", "every", "action", "includes", "someActionsSelected", "some", "Flex", "justifyContent", "alignItems", "padding", "paddingRight", "Typography", "textColor", "paddingLeft", "Checkbox", "checked", "onCheckedChange", "target", "id", "defaultMessage", "Grid", "Root", "gap", "col", "direction", "hasRadius", "name", "button", "type", "data-testid", "onClick", "style", "display", "Cog", "ContentTypesSection", "section", "props", "_jsx", "Box", "Accordion", "Root", "size", "map", "api", "index", "CollapsableContentType", "label", "controllers", "orderNumber", "apiId", "Permissions", "props", "value", "data", "useApiTokenPermissions", "formatMessage", "useIntl", "_jsxs", "Grid", "Root", "gap", "shadow", "hasRadius", "background", "<PERSON><PERSON>", "col", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "direction", "alignItems", "Flex", "_jsx", "Typography", "variant", "tag", "id", "defaultMessage", "textColor", "permissions", "ContentTypesSection", "section", "ActionBoundRoutes", "schema", "object", "shape", "name", "string", "max", "required", "translatedErrors", "id", "type", "oneOf", "description", "nullable", "lifespan", "number", "integer", "min", "defined", "transformPermissionsData", "data", "layout", "allActionsIds", "permissions", "Object", "entries", "map", "apiId", "permission", "label", "split", "controllers", "keys", "controller", "actions", "action", "actionId", "includes", "push", "flat", "initialState", "data", "allActionsIds", "permissions", "routes", "selectedAction", "selectedActions", "reducer", "state", "action", "produce", "draftState", "type", "includes", "value", "pull", "push", "areAllSelected", "every", "item", "actionId", "for<PERSON>ach", "onlyReadOnlyActions", "filter", "transformPermissionsData", "EditView", "formatMessage", "useIntl", "toggleNotification", "useNotification", "state", "locationState", "useLocation", "permissions", "useTypedSelector", "admin_app", "apiToken", "setApiToken", "useState", "accessKey", "showToken", "setShowToken", "Boolean", "hideTimerRef", "useRef", "trackUsage", "useTracking", "setCurrentStep", "useGuidedTour", "allowedActions", "canCreate", "canUpdate", "canRegenerate", "useRBAC", "settings", "dispatch", "useReducer", "reducer", "initialState", "match", "useMatch", "id", "params", "isCreating", "_unstableFormatAPIError", "formatAPIError", "_unstableFormatValidationErrors", "formatValidtionErrors", "useAPIErrorHandler", "navigate", "useNavigate", "contentAPIPermissionsQuery", "useGetPermissionsQuery", "contentAPIRoutesQuery", "useGetRoutesQuery", "React", "useEffect", "error", "type", "message", "data", "value", "tokenType", "API_TOKEN_TYPE", "isLoading", "useGetAPITokenQuery", "skip", "current", "setTimeout", "clearTimeout", "createToken", "useCreateAPITokenMutation", "updateToken", "useUpdateAPITokenMutation", "handleSubmit", "body", "formik", "res", "lifespan", "parseInt", "toString", "selectedActions", "isBaseQueryError", "name", "setErrors", "defaultMessage", "replace", "description", "hasChangedPermissions", "setHasChangedPermissions", "handleChangeCheckbox", "target", "handleChangeSelectAllCheckbox", "setSelectedAction", "toggleToken", "prev", "providerValue", "onChange", "onChangeSelectAll", "canEditInputs", "canShowToken", "_jsx", "Page", "Loading", "ApiTokenPermissionsProvider", "_jsxs", "Main", "Title", "<PERSON><PERSON>", "validationSchema", "schema", "validateOnChange", "initialValues", "enableReinitialize", "onSubmit", "actions", "errors", "handleChange", "isSubmitting", "values", "setFieldValue", "Form", "FormHead", "title", "token", "setToken", "regenerateUrl", "Layouts", "Content", "Flex", "direction", "alignItems", "gap", "TokenBox", "FormApiTokenContainer", "onDispatch", "Permissions", "disabled", "ProtectedEditView", "read", "Protect"]}