import {
  adminApi
} from "./chunk-WH6VCVXU.js";

// node_modules/@strapi/i18n/dist/admin/pluginId.mjs
var pluginId = "i18n";

// node_modules/@strapi/i18n/dist/admin/utils/getTranslation.mjs
var getTranslation = (id) => `${pluginId}.${id}`;

// node_modules/@strapi/i18n/dist/admin/services/api.mjs
var i18nApi = adminApi.enhanceEndpoints({
  addTagTypes: [
    "Locale"
  ]
});

// node_modules/@strapi/i18n/dist/admin/services/locales.mjs
var localesApi = i18nApi.injectEndpoints({
  endpoints: (builder) => ({
    createLocale: builder.mutation({
      query: (data) => ({
        url: "/i18n/locales",
        method: "POST",
        data
      }),
      invalidatesTags: [
        {
          type: "Locale",
          id: "LIST"
        }
      ]
    }),
    deleteLocale: builder.mutation({
      query: (id) => ({
        url: `/i18n/locales/${id}`,
        method: "DELETE"
      }),
      invalidatesTags: (result, error, id) => [
        {
          type: "Locale",
          id
        }
      ]
    }),
    getLocales: builder.query({
      query: () => "/i18n/locales",
      providesTags: (res) => [
        {
          type: "Locale",
          id: "LIST"
        },
        ...Array.isArray(res) ? res.map((locale) => ({
          type: "Locale",
          id: locale.id
        })) : []
      ]
    }),
    getDefaultLocales: builder.query({
      query: () => "/i18n/iso-locales"
    }),
    updateLocale: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `/i18n/locales/${id}`,
        method: "PUT",
        data
      }),
      invalidatesTags: (result, error, { id }) => [
        {
          type: "Locale",
          id
        }
      ]
    })
  })
});
var { useCreateLocaleMutation, useDeleteLocaleMutation, useGetLocalesQuery, useGetDefaultLocalesQuery, useUpdateLocaleMutation } = localesApi;

// node_modules/@strapi/i18n/dist/admin/constants.mjs
var PERMISSIONS = {
  accessMain: [
    {
      action: "plugin::i18n.locale.read",
      subject: null
    }
  ],
  create: [
    {
      action: "plugin::i18n.locale.create",
      subject: null
    }
  ],
  delete: [
    {
      action: "plugin::i18n.locale.delete",
      subject: null
    }
  ],
  update: [
    {
      action: "plugin::i18n.locale.update",
      subject: null
    }
  ],
  read: [
    {
      action: "plugin::i18n.locale.read",
      subject: null
    }
  ]
};

export {
  pluginId,
  getTranslation,
  i18nApi,
  useCreateLocaleMutation,
  useDeleteLocaleMutation,
  useGetLocalesQuery,
  useGetDefaultLocalesQuery,
  useUpdateLocaleMutation,
  PERMISSIONS
};
//# sourceMappingURL=chunk-DLRJHTTW.js.map
