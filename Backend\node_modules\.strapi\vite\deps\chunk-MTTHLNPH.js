import {
  Box,
  Flex,
  Typography,
  useCallbackRef
} from "./chunk-7GC3Y62Q.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/hooks/useElementOnScreen.mjs
var React = __toESM(require_react(), 1);
var useElementOnScreen = (onVisiblityChange, options) => {
  const containerRef = React.useRef(null);
  React.useEffect(() => {
    const containerEl = containerRef.current;
    const observer = new IntersectionObserver(([entry]) => {
      onVisiblityChange(entry.isIntersecting);
    }, options);
    if (containerEl) {
      observer.observe(containerRef.current);
    }
    return () => {
      if (containerEl) {
        observer.disconnect();
      }
    };
  }, [
    containerRef,
    options,
    onVisiblityChange
  ]);
  return containerRef;
};

// node_modules/@strapi/admin/dist/admin/admin/src/components/Layouts/Layout.mjs
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var import_react4 = __toESM(require_react(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/components/Layouts/ActionLayout.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var ActionLayout = ({ startActions, endActions }) => {
  if (!startActions && !endActions) {
    return null;
  }
  return (0, import_jsx_runtime.jsxs)(Flex, {
    justifyContent: "space-between",
    alignItems: "flex-start",
    paddingBottom: 4,
    paddingLeft: 10,
    paddingRight: 10,
    children: [
      (0, import_jsx_runtime.jsx)(Flex, {
        gap: 2,
        wrap: "wrap",
        children: startActions
      }),
      (0, import_jsx_runtime.jsx)(Flex, {
        gap: 2,
        shrink: 0,
        wrap: "wrap",
        children: endActions
      })
    ]
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/components/Layouts/ContentLayout.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var ContentLayout = ({ children }) => {
  return (0, import_jsx_runtime2.jsx)(Box, {
    paddingLeft: 10,
    paddingRight: 10,
    children
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/components/Layouts/GridLayout.mjs
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_react3 = __toESM(require_react(), 1);
var GridColSize = {
  S: 180,
  M: 250
};
var StyledGrid = dt(Box)`
  display: grid;
  grid-template-columns: repeat(
    auto-fit,
    minmax(${({ $size }) => `${GridColSize[$size]}px`}, 1fr)
  );
  grid-gap: ${({ theme }) => theme.spaces[4]};
`;
var GridLayout = ({ size, children }) => {
  return (0, import_jsx_runtime3.jsx)(StyledGrid, {
    $size: size,
    children
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/components/Layouts/HeaderLayout.mjs
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var React2 = __toESM(require_react(), 1);
var BaseHeaderLayout = React2.forwardRef(({ navigationAction, primaryAction, secondaryAction, subtitle, title, sticky, width, ...props }, ref) => {
  const isSubtitleString = typeof subtitle === "string";
  if (sticky) {
    return (0, import_jsx_runtime4.jsx)(Box, {
      paddingLeft: 6,
      paddingRight: 6,
      paddingTop: 3,
      paddingBottom: 3,
      position: "fixed",
      top: 0,
      right: 0,
      background: "neutral0",
      shadow: "tableShadow",
      width: `${width}px`,
      zIndex: 3,
      "data-strapi-header-sticky": true,
      children: (0, import_jsx_runtime4.jsxs)(Flex, {
        justifyContent: "space-between",
        children: [
          (0, import_jsx_runtime4.jsxs)(Flex, {
            children: [
              navigationAction && (0, import_jsx_runtime4.jsx)(Box, {
                paddingRight: 3,
                children: navigationAction
              }),
              (0, import_jsx_runtime4.jsxs)(Box, {
                children: [
                  (0, import_jsx_runtime4.jsx)(Typography, {
                    variant: "beta",
                    tag: "h1",
                    ...props,
                    children: title
                  }),
                  isSubtitleString ? (0, import_jsx_runtime4.jsx)(Typography, {
                    variant: "pi",
                    textColor: "neutral600",
                    children: subtitle
                  }) : subtitle
                ]
              }),
              secondaryAction ? (0, import_jsx_runtime4.jsx)(Box, {
                paddingLeft: 4,
                children: secondaryAction
              }) : null
            ]
          }),
          (0, import_jsx_runtime4.jsx)(Flex, {
            children: primaryAction ? (0, import_jsx_runtime4.jsx)(Box, {
              paddingLeft: 2,
              children: primaryAction
            }) : void 0
          })
        ]
      })
    });
  }
  return (0, import_jsx_runtime4.jsxs)(Box, {
    ref,
    paddingLeft: 10,
    paddingRight: 10,
    paddingBottom: 8,
    paddingTop: navigationAction ? 6 : 8,
    background: "neutral100",
    "data-strapi-header": true,
    children: [
      navigationAction ? (0, import_jsx_runtime4.jsx)(Box, {
        paddingBottom: 2,
        children: navigationAction
      }) : null,
      (0, import_jsx_runtime4.jsxs)(Flex, {
        justifyContent: "space-between",
        children: [
          (0, import_jsx_runtime4.jsxs)(Flex, {
            minWidth: 0,
            children: [
              (0, import_jsx_runtime4.jsx)(Typography, {
                tag: "h1",
                variant: "alpha",
                ...props,
                children: title
              }),
              secondaryAction ? (0, import_jsx_runtime4.jsx)(Box, {
                paddingLeft: 4,
                children: secondaryAction
              }) : null
            ]
          }),
          primaryAction
        ]
      }),
      isSubtitleString ? (0, import_jsx_runtime4.jsx)(Typography, {
        variant: "epsilon",
        textColor: "neutral600",
        tag: "p",
        children: subtitle
      }) : subtitle
    ]
  });
});
var HeaderLayout = (props) => {
  const baseHeaderLayoutRef = React2.useRef(null);
  const [headerSize, setHeaderSize] = React2.useState(null);
  const [isVisible, setIsVisible] = React2.useState(true);
  const containerRef = useElementOnScreen(setIsVisible, {
    root: null,
    rootMargin: "0px",
    threshold: 0
  });
  useResizeObserver(containerRef, () => {
    if (containerRef.current) {
      setHeaderSize(containerRef.current.getBoundingClientRect());
    }
  });
  React2.useEffect(() => {
    if (baseHeaderLayoutRef.current) {
      setHeaderSize(baseHeaderLayoutRef.current.getBoundingClientRect());
    }
  }, [
    baseHeaderLayoutRef
  ]);
  return (0, import_jsx_runtime4.jsxs)(import_jsx_runtime4.Fragment, {
    children: [
      (0, import_jsx_runtime4.jsx)("div", {
        style: {
          height: headerSize == null ? void 0 : headerSize.height
        },
        ref: containerRef,
        children: isVisible && (0, import_jsx_runtime4.jsx)(BaseHeaderLayout, {
          ref: baseHeaderLayoutRef,
          ...props
        })
      }),
      !isVisible && (0, import_jsx_runtime4.jsx)(BaseHeaderLayout, {
        ...props,
        sticky: true,
        width: headerSize == null ? void 0 : headerSize.width
      })
    ]
  });
};
HeaderLayout.displayName = "HeaderLayout";
var useResizeObserver = (sources, onResize) => {
  const handleResize = useCallbackRef(onResize);
  React2.useLayoutEffect(() => {
    const resizeObs = new ResizeObserver(handleResize);
    if (Array.isArray(sources)) {
      sources.forEach((source) => {
        if (source.current) {
          resizeObs.observe(source.current);
        }
      });
    } else if (sources.current) {
      resizeObs.observe(sources.current);
    }
    return () => {
      resizeObs.disconnect();
    };
  }, [
    sources,
    handleResize
  ]);
};

// node_modules/@strapi/admin/dist/admin/admin/src/components/Layouts/Layout.mjs
var GridContainer = dt(Box)`
  display: grid;
  grid-template-columns: ${({ $hasSideNav }) => $hasSideNav ? `auto 1fr` : "1fr"};
`;
var OverflowingItem = dt(Box)`
  overflow-x: hidden;
`;
var RootLayout = ({ sideNav, children }) => {
  return (0, import_jsx_runtime5.jsxs)(GridContainer, {
    $hasSideNav: Boolean(sideNav),
    children: [
      sideNav,
      (0, import_jsx_runtime5.jsx)(OverflowingItem, {
        paddingBottom: 10,
        children
      })
    ]
  });
};
var Layouts = {
  Root: RootLayout,
  Header: HeaderLayout,
  BaseHeader: BaseHeaderLayout,
  Grid: GridLayout,
  Action: ActionLayout,
  Content: ContentLayout
};

export {
  useElementOnScreen,
  Layouts
};
//# sourceMappingURL=chunk-MTTHLNPH.js.map
