{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/components/RoleForm.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/EditPage.tsx"], "sourcesContent": ["import {\n  Box,\n  Button,\n  Field,\n  Flex,\n  Grid,\n  Textarea,\n  TextInput,\n  Typography,\n} from '@strapi/design-system';\nimport { FormikProps } from 'formik';\nimport { useIntl } from 'react-intl';\n\nimport type { AdminRole } from '../../../../../hooks/useAdminRoles';\nimport type { EditRoleFormValues } from '../EditPage';\n\ninterface RoleFormProps extends Pick<FormikProps<EditRoleFormValues>, 'values' | 'errors'> {\n  onBlur: FormikProps<EditRoleFormValues>['handleBlur'];\n  onChange: FormikProps<EditRoleFormValues>['handleChange'];\n  disabled?: boolean;\n  role: AdminRole;\n}\n\nconst RoleForm = ({ disabled, role, values, errors, onChange, onBlur }: RoleFormProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Box background=\"neutral0\" padding={6} shadow=\"filterShadow\" hasRadius>\n      <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n        <Flex justifyContent=\"space-between\">\n          <Box>\n            <Box>\n              <Typography fontWeight=\"bold\">\n                {role\n                  ? role.name\n                  : formatMessage({\n                      id: 'global.details',\n                      defaultMessage: 'Details',\n                    })}\n              </Typography>\n            </Box>\n            <Box>\n              <Typography textColor=\"neutral500\" variant=\"pi\">\n                {role\n                  ? role.description\n                  : formatMessage({\n                      id: 'Settings.roles.form.description',\n                      defaultMessage: 'Name and description of the role',\n                    })}\n              </Typography>\n            </Box>\n          </Box>\n          <Button disabled variant=\"secondary\">\n            {formatMessage(\n              {\n                id: 'Settings.roles.form.button.users-with-role',\n                defaultMessage:\n                  '{number, plural, =0 {# users} one {# user} other {# users}} with this role',\n              },\n              { number: role.usersCount }\n            )}\n          </Button>\n        </Flex>\n        <Grid.Root gap={4}>\n          <Grid.Item col={6} direction=\"column\" alignItems=\"stretch\">\n            <Field.Root\n              name=\"name\"\n              error={errors.name && formatMessage({ id: errors.name })}\n              required\n            >\n              <Field.Label>\n                {formatMessage({\n                  id: 'global.name',\n                  defaultMessage: 'Name',\n                })}\n              </Field.Label>\n              <TextInput\n                disabled={disabled}\n                onChange={onChange}\n                onBlur={onBlur}\n                value={values.name || ''}\n              />\n              <Field.Error />\n            </Field.Root>\n          </Grid.Item>\n          <Grid.Item col={6} direction=\"column\" alignItems=\"stretch\">\n            <Field.Root\n              name=\"description\"\n              error={errors.description && formatMessage({ id: errors.description })}\n            >\n              <Field.Label>\n                {formatMessage({\n                  id: 'global.description',\n                  defaultMessage: 'Description',\n                })}\n              </Field.Label>\n              <Textarea\n                disabled={disabled}\n                onChange={onChange}\n                onBlur={onBlur}\n                value={values.description}\n              />\n              <Field.Error />\n            </Field.Root>\n          </Grid.Item>\n        </Grid.Root>\n      </Flex>\n    </Box>\n  );\n};\n\nexport { RoleForm };\nexport type { RoleFormProps };\n", "import * as React from 'react';\n\nimport { Box, Button, Flex, Main } from '@strapi/design-system';\nimport { Check } from '@strapi/icons';\nimport { Formik, FormikHelpers } from 'formik';\nimport { useIntl } from 'react-intl';\nimport { Navigate, useMatch } from 'react-router-dom';\nimport * as yup from 'yup';\n\nimport { Layouts } from '../../../../components/Layouts/Layout';\nimport { Page } from '../../../../components/PageHelpers';\nimport { useTypedSelector } from '../../../../core/store/hooks';\nimport { BackButton } from '../../../../features/BackButton';\nimport { useNotification } from '../../../../features/Notifications';\nimport { useTracking } from '../../../../features/Tracking';\nimport { useAdminRoles } from '../../../../hooks/useAdminRoles';\nimport { useAPIErrorHandler } from '../../../../hooks/useAPIErrorHandler';\nimport {\n  useGetRolePermissionLayoutQuery,\n  useGetRolePermissionsQuery,\n  useUpdateRoleMutation,\n  useUpdateRolePermissionsMutation,\n} from '../../../../services/users';\nimport { isBaseQueryError } from '../../../../utils/baseQuery';\nimport { translatedErrors } from '../../../../utils/translatedErrors';\n\nimport { Permissions, PermissionsAPI } from './components/Permissions';\nimport { RoleForm } from './components/RoleForm';\n\nconst EDIT_ROLE_SCHEMA = yup.object().shape({\n  name: yup.string().required(translatedErrors.required.id),\n  description: yup.string().optional(),\n});\n\n/**\n * TODO: be nice if we could just infer this from the schema\n */\ninterface EditRoleFormValues {\n  name: string;\n  description: string;\n}\n\nconst EditPage = () => {\n  const { toggleNotification } = useNotification();\n  const { formatMessage } = useIntl();\n  const match = useMatch('/settings/roles/:id');\n  const id = match?.params.id;\n  const permissionsRef = React.useRef<PermissionsAPI>(null);\n  const { trackUsage } = useTracking();\n  const {\n    _unstableFormatAPIError: formatAPIError,\n    _unstableFormatValidationErrors: formatValidationErrors,\n  } = useAPIErrorHandler();\n\n  const { isLoading: isLoadingPermissionsLayout, data: permissionsLayout } =\n    useGetRolePermissionLayoutQuery({\n      /**\n       * Role here is a query param so if there's no role we pass an empty string\n       * which returns us a default layout.\n       */\n      role: id ?? '',\n    });\n\n  const {\n    roles,\n    isLoading: isRoleLoading,\n    refetch: refetchRole,\n  } = useAdminRoles(\n    { id },\n    {\n      refetchOnMountOrArgChange: true,\n    }\n  );\n\n  const role = roles[0] ?? {};\n\n  const { data: permissions, isLoading: isLoadingPermissions } = useGetRolePermissionsQuery(\n    {\n      id: id!,\n    },\n    {\n      skip: !id,\n      refetchOnMountOrArgChange: true,\n    }\n  );\n\n  const [updateRole] = useUpdateRoleMutation();\n  const [updateRolePermissions] = useUpdateRolePermissionsMutation();\n\n  if (!id) {\n    return <Navigate to=\"/settings/roles\" />;\n  }\n\n  const handleEditRoleSubmit = async (\n    data: EditRoleFormValues,\n    formik: FormikHelpers<EditRoleFormValues>\n  ) => {\n    try {\n      const { permissionsToSend, didUpdateConditions } =\n        permissionsRef.current?.getPermissions() ?? {};\n\n      const res = await updateRole({\n        id,\n        ...data,\n      });\n\n      if ('error' in res) {\n        if (isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n          formik.setErrors(formatValidationErrors(res.error));\n        } else {\n          toggleNotification({\n            type: 'danger',\n            message: formatAPIError(res.error),\n          });\n        }\n\n        return;\n      }\n\n      if (role.code !== 'strapi-super-admin' && permissionsToSend) {\n        const updateRes = await updateRolePermissions({\n          id: res.data.id,\n          permissions: permissionsToSend,\n        });\n\n        if ('error' in updateRes) {\n          if (isBaseQueryError(updateRes.error) && updateRes.error.name === 'ValidationError') {\n            formik.setErrors(formatValidationErrors(updateRes.error));\n          } else {\n            toggleNotification({\n              type: 'danger',\n              message: formatAPIError(updateRes.error),\n            });\n          }\n\n          return;\n        }\n\n        if (didUpdateConditions) {\n          trackUsage('didUpdateConditions');\n        }\n      }\n\n      permissionsRef.current?.setFormAfterSubmit();\n\n      await refetchRole();\n\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({ id: 'notification.success.saved' }),\n      });\n    } catch (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occurred' }),\n      });\n    }\n  };\n\n  const isFormDisabled = !isRoleLoading && role.code === 'strapi-super-admin';\n\n  if (isLoadingPermissionsLayout || isRoleLoading || isLoadingPermissions || !permissionsLayout) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Main>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: 'Roles',\n          }\n        )}\n      </Page.Title>\n      <Formik\n        enableReinitialize\n        initialValues={\n          {\n            name: role.name ?? '',\n            description: role.description ?? '',\n          } satisfies EditRoleFormValues\n        }\n        onSubmit={handleEditRoleSubmit}\n        validationSchema={EDIT_ROLE_SCHEMA}\n        validateOnChange={false}\n      >\n        {({ handleSubmit, values, errors, handleChange, handleBlur, isSubmitting }) => (\n          <form onSubmit={handleSubmit}>\n            <Layouts.Header\n              primaryAction={\n                <Flex gap={2}>\n                  <Button\n                    type=\"submit\"\n                    startIcon={<Check />}\n                    disabled={role.code === 'strapi-super-admin'}\n                    loading={isSubmitting}\n                  >\n                    {formatMessage({\n                      id: 'global.save',\n                      defaultMessage: 'Save',\n                    })}\n                  </Button>\n                </Flex>\n              }\n              title={formatMessage({\n                id: 'Settings.roles.edit.title',\n                defaultMessage: 'Edit a role',\n              })}\n              subtitle={formatMessage({\n                id: 'Settings.roles.create.description',\n                defaultMessage: 'Define the rights given to the role',\n              })}\n              navigationAction={<BackButton fallback=\"../roles\" />}\n            />\n            <Layouts.Content>\n              <Flex direction=\"column\" alignItems=\"stretch\" gap={6}>\n                <RoleForm\n                  disabled={isFormDisabled}\n                  errors={errors}\n                  values={values}\n                  onChange={handleChange}\n                  onBlur={handleBlur}\n                  role={role}\n                />\n                <Box shadow=\"filterShadow\" hasRadius>\n                  <Permissions\n                    isFormDisabled={isFormDisabled}\n                    permissions={permissions}\n                    ref={permissionsRef}\n                    layout={permissionsLayout}\n                  />\n                </Box>\n              </Flex>\n            </Layouts.Content>\n          </form>\n        )}\n      </Formik>\n    </Main>\n  );\n};\n\nconst ProtectedEditPage = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.roles.update\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <EditPage />\n    </Page.Protect>\n  );\n};\n\nexport { EditPage, ProtectedEditPage };\nexport type { EditRoleFormValues };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,IAAMA,WAAW,CAAC,EAAEC,UAAUC,MAAMC,QAAQC,QAAQC,UAAUC,OAAM,MAAiB;AACnF,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEC,wBAACC,KAAAA;IAAIC,YAAW;IAAWC,SAAS;IAAGC,QAAO;IAAeC,WAAS;IACpE,cAAAC,yBAACC,MAAAA;MAAKC,WAAU;MAASC,YAAW;MAAUC,KAAK;;YACjDJ,yBAACC,MAAAA;UAAKI,gBAAe;;gBACnBL,yBAACL,KAAAA;;oBACCD,wBAACC,KAAAA;kBACC,cAAAD,wBAACY,YAAAA;oBAAWC,YAAW;8BACpBpB,OACGA,KAAKqB,OACLhB,cAAc;sBACZiB,IAAI;sBACJC,gBAAgB;oBAClB,CAAA;;;oBAGRhB,wBAACC,KAAAA;kBACC,cAAAD,wBAACY,YAAAA;oBAAWK,WAAU;oBAAaC,SAAQ;8BACxCzB,OACGA,KAAK0B,cACLrB,cAAc;sBACZiB,IAAI;sBACJC,gBAAgB;oBAClB,CAAA;;;;;gBAIVhB,wBAACoB,QAAAA;cAAO5B,UAAQ;cAAC0B,SAAQ;wBACtBpB,cACC;gBACEiB,IAAI;gBACJC,gBACE;iBAEJ;gBAAEK,QAAQ5B,KAAK6B;cAAW,CAAA;;;;YAIhChB,yBAACiB,KAAKC,MAAI;UAACd,KAAK;;gBACdV,wBAACuB,KAAKE,MAAI;cAACC,KAAK;cAAGlB,WAAU;cAASC,YAAW;4BAC/CH,yBAACqB,MAAMH,MAAI;gBACTV,MAAK;gBACLc,OAAOjC,OAAOmB,QAAQhB,cAAc;kBAAEiB,IAAIpB,OAAOmB;gBAAK,CAAA;gBACtDe,UAAQ;;sBAER7B,wBAAC2B,MAAMG,OAAK;8BACThC,cAAc;sBACbiB,IAAI;sBACJC,gBAAgB;oBAClB,CAAA;;sBAEFhB,wBAAC+B,WAAAA;oBACCvC;oBACAI;oBACAC;oBACAmC,OAAOtC,OAAOoB,QAAQ;;sBAExBd,wBAAC2B,MAAMM,OAAK,CAAA,CAAA;;;;gBAGhBjC,wBAACuB,KAAKE,MAAI;cAACC,KAAK;cAAGlB,WAAU;cAASC,YAAW;4BAC/CH,yBAACqB,MAAMH,MAAI;gBACTV,MAAK;gBACLc,OAAOjC,OAAOwB,eAAerB,cAAc;kBAAEiB,IAAIpB,OAAOwB;gBAAY,CAAA;;sBAEpEnB,wBAAC2B,MAAMG,OAAK;8BACThC,cAAc;sBACbiB,IAAI;sBACJC,gBAAgB;oBAClB,CAAA;;sBAEFhB,wBAACkC,UAAAA;oBACC1C;oBACAI;oBACAC;oBACAmC,OAAOtC,OAAOyB;;sBAEhBnB,wBAAC2B,MAAMM,OAAK,CAAA,CAAA;;;;;;;;;AAO1B;;;AChFA,IAAME,mBAAuBC,QAAM,EAAGC,MAAM;EAC1CC,MAAUC,OAAM,EAAGC,SAASC,YAAiBD,SAASE,EAAE;EACxDC,aAAiBJ,OAAM,EAAGK,SAAQ;AACpC,CAAA;AAUA,IAAMC,WAAW,MAAA;AACf,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAMC,QAAQC,SAAS,qBAAA;AACvB,QAAMT,KAAKQ,+BAAOE,OAAOV;AACzB,QAAMW,iBAAuBC,aAAuB,IAAA;AACpD,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAM,EACJC,yBAAyBC,gBACzBC,iCAAiCC,uBAAsB,IACrDC,mBAAAA;AAEJ,QAAM,EAAEC,WAAWC,4BAA4BC,MAAMC,kBAAiB,IACpEC,gCAAgC;;;;;IAK9BC,MAAMzB,MAAM;EACd,CAAA;AAEF,QAAM,EACJ0B,OACAN,WAAWO,eACXC,SAASC,YAAW,IAClBC,cACF;IAAE9B;KACF;IACE+B,2BAA2B;EAC7B,CAAA;AAGF,QAAMN,OAAOC,MAAM,CAAA,KAAM,CAAA;AAEzB,QAAM,EAAEJ,MAAMU,aAAaZ,WAAWa,qBAAoB,IAAKC,2BAC7D;IACElC;KAEF;IACEmC,MAAM,CAACnC;IACP+B,2BAA2B;EAC7B,CAAA;AAGF,QAAM,CAACK,UAAAA,IAAcC,sBAAAA;AACrB,QAAM,CAACC,qBAAAA,IAAyBC,iCAAAA;AAEhC,MAAI,CAACvC,IAAI;AACP,eAAOwC,yBAACC,UAAAA;MAASC,IAAG;;EACtB;AAEA,QAAMC,uBAAuB,OAC3BrB,MACAsB,WAAAA;;AAEA,QAAI;AACF,YAAM,EAAEC,mBAAmBC,oBAAmB,MAC5CnC,oBAAeoC,YAAfpC,mBAAwBqC,qBAAoB,CAAA;AAE9C,YAAMC,MAAM,MAAMb,WAAW;QAC3BpC;QACA,GAAGsB;MACL,CAAA;AAEA,UAAI,WAAW2B,KAAK;AAClB,YAAIC,iBAAiBD,IAAIE,KAAK,KAAKF,IAAIE,MAAMvD,SAAS,mBAAmB;AACvEgD,iBAAOQ,UAAUlC,uBAAuB+B,IAAIE,KAAK,CAAA;eAC5C;AACL/C,6BAAmB;YACjBiD,MAAM;YACNC,SAAStC,eAAeiC,IAAIE,KAAK;UACnC,CAAA;QACF;AAEA;MACF;AAEA,UAAI1B,KAAK8B,SAAS,wBAAwBV,mBAAmB;AAC3D,cAAMW,YAAY,MAAMlB,sBAAsB;UAC5CtC,IAAIiD,IAAI3B,KAAKtB;UACbgC,aAAaa;QACf,CAAA;AAEA,YAAI,WAAWW,WAAW;AACxB,cAAIN,iBAAiBM,UAAUL,KAAK,KAAKK,UAAUL,MAAMvD,SAAS,mBAAmB;AACnFgD,mBAAOQ,UAAUlC,uBAAuBsC,UAAUL,KAAK,CAAA;iBAClD;AACL/C,+BAAmB;cACjBiD,MAAM;cACNC,SAAStC,eAAewC,UAAUL,KAAK;YACzC,CAAA;UACF;AAEA;QACF;AAEA,YAAIL,qBAAqB;AACvBjC,qBAAW,qBAAA;QACb;MACF;AAEAF,2BAAeoC,YAAfpC,mBAAwB8C;AAExB,YAAM5B,YAAAA;AAENzB,yBAAmB;QACjBiD,MAAM;QACNC,SAAShD,cAAc;UAAEN,IAAI;QAA6B,CAAA;MAC5D,CAAA;IACF,SAASmD,OAAO;AACd/C,yBAAmB;QACjBiD,MAAM;QACNC,SAAShD,cAAc;UAAEN,IAAI;UAAsB0D,gBAAgB;QAAoB,CAAA;MACzF,CAAA;IACF;EACF;AAEA,QAAMC,iBAAiB,CAAChC,iBAAiBF,KAAK8B,SAAS;AAEvD,MAAIlC,8BAA8BM,iBAAiBM,wBAAwB,CAACV,mBAAmB;AAC7F,eAAOiB,yBAACoB,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,aACEC,0BAACC,MAAAA;;UACCvB,yBAACoB,KAAKI,OAAK;kBACR1D,cACC;UAAEN,IAAI;UAAsB0D,gBAAgB;WAC5C;UACE9D,MAAM;QACR,CAAA;;UAGJ4C,yBAACyB,QAAAA;QACCC,oBAAkB;QAClBC,eACE;UACEvE,MAAM6B,KAAK7B,QAAQ;UACnBK,aAAawB,KAAKxB,eAAe;QACnC;QAEFmE,UAAUzB;QACV0B,kBAAkB5E;QAClB6E,kBAAkB;QAEjB,UAAA,CAAC,EAAEC,cAAcC,QAAQC,QAAQC,cAAcC,YAAYC,aAAY,UACtEd,0BAACe,QAAAA;UAAKT,UAAUG;;gBACd/B,yBAACsC,QAAQC,QAAM;cACbC,mBACExC,yBAACyC,MAAAA;gBAAKC,KAAK;gBACT,cAAA1C,yBAAC2C,QAAAA;kBACC9B,MAAK;kBACL+B,eAAW5C,yBAAC6C,eAAAA,CAAAA,CAAAA;kBACZC,UAAU7D,KAAK8B,SAAS;kBACxBgC,SAASX;4BAERtE,cAAc;oBACbN,IAAI;oBACJ0D,gBAAgB;kBAClB,CAAA;;;cAIN8B,OAAOlF,cAAc;gBACnBN,IAAI;gBACJ0D,gBAAgB;cAClB,CAAA;cACA+B,UAAUnF,cAAc;gBACtBN,IAAI;gBACJ0D,gBAAgB;cAClB,CAAA;cACAgC,sBAAkBlD,yBAACmD,YAAAA;gBAAWC,UAAS;;;gBAEzCpD,yBAACsC,QAAQe,SAAO;cACd,cAAA/B,0BAACmB,MAAAA;gBAAKa,WAAU;gBAASC,YAAW;gBAAUb,KAAK;;sBACjD1C,yBAACwD,UAAAA;oBACCV,UAAU3B;oBACVc;oBACAD;oBACAyB,UAAUvB;oBACVwB,QAAQvB;oBACRlD;;sBAEFe,yBAAC2D,KAAAA;oBAAIC,QAAO;oBAAeC,WAAS;oBAClC,cAAA7D,yBAAC8D,aAAAA;sBACC3C;sBACA3B;sBACAuE,KAAK5F;sBACL6F,QAAQjF;;;;;;;;;;;AAU5B;AAEA,IAAMkF,oBAAoB,MAAA;AACxB,QAAMzE,cAAc0E,iBAClB,CAACC,UAAUA;;AAAAA,uBAAMC,UAAU5E,YAAY6E,aAA5BF,mBAAsCjF,MAAMoF;GAAAA;AAGzD,aACEtE,yBAACoB,KAAKmD,SAAO;IAAC/E;IACZ,cAAAQ,yBAACrC,UAAAA,CAAAA,CAAAA;;AAGP;", "names": ["RoleForm", "disabled", "role", "values", "errors", "onChange", "onBlur", "formatMessage", "useIntl", "_jsx", "Box", "background", "padding", "shadow", "hasRadius", "_jsxs", "Flex", "direction", "alignItems", "gap", "justifyContent", "Typography", "fontWeight", "name", "id", "defaultMessage", "textColor", "variant", "description", "<PERSON><PERSON>", "number", "usersCount", "Grid", "Root", "<PERSON><PERSON>", "col", "Field", "error", "required", "Label", "TextInput", "value", "Error", "Textarea", "EDIT_ROLE_SCHEMA", "object", "shape", "name", "string", "required", "translatedErrors", "id", "description", "optional", "EditPage", "toggleNotification", "useNotification", "formatMessage", "useIntl", "match", "useMatch", "params", "permissionsRef", "useRef", "trackUsage", "useTracking", "_unstableFormatAPIError", "formatAPIError", "_unstableFormatValidationErrors", "formatValidationErrors", "useAPIErrorHandler", "isLoading", "isLoadingPermissionsLayout", "data", "permissionsLayout", "useGetRolePermissionLayoutQuery", "role", "roles", "isRoleLoading", "refetch", "refetchRole", "useAdminRoles", "refetchOnMountOrArgChange", "permissions", "isLoadingPermissions", "useGetRolePermissionsQuery", "skip", "updateRole", "useUpdateRoleMutation", "updateRolePermissions", "useUpdateRolePermissionsMutation", "_jsx", "Navigate", "to", "handleEditRoleSubmit", "formik", "permissionsToSend", "didUpdateConditions", "current", "getPermissions", "res", "isBaseQueryError", "error", "setErrors", "type", "message", "code", "updateRes", "setFormAfterSubmit", "defaultMessage", "isFormDisabled", "Page", "Loading", "_jsxs", "Main", "Title", "<PERSON><PERSON>", "enableReinitialize", "initialValues", "onSubmit", "validationSchema", "validateOnChange", "handleSubmit", "values", "errors", "handleChange", "handleBlur", "isSubmitting", "form", "Layouts", "Header", "primaryAction", "Flex", "gap", "<PERSON><PERSON>", "startIcon", "Check", "disabled", "loading", "title", "subtitle", "navigationAction", "BackButton", "fallback", "Content", "direction", "alignItems", "RoleForm", "onChange", "onBlur", "Box", "shadow", "hasRadius", "Permissions", "ref", "layout", "ProtectedEditPage", "useTypedSelector", "state", "admin_app", "settings", "update", "Protect"]}