import {
  require_escape_string_regexp
} from "./chunk-6LY4MOO2.js";
import {
  getRelationType
} from "./chunk-H4GPAAVJ.js";
import {
  require_pluralize
} from "./chunk-XNACAI67.js";
import {
  require_merge,
  require_omit
} from "./chunk-DY2RJG3P.js";
import {
  require_set
} from "./chunk-SGQJOZK5.js";
import {
  require_snakeCase
} from "./chunk-376QHLWZ.js";
import {
  createSlice
} from "./chunk-IL5G2D22.js";
import {
  e
} from "./chunk-QOUV5O5E.js";
import {
  Box,
  Flex,
  Link,
  Typography,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  require_react_dom
} from "./chunk-FOD4ENRR.js";
import {
  ForwardRef$4r,
  ForwardRef$5n
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt,
  mt
} from "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __commonJS,
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash.deburr/index.js
var require_lodash = __commonJS({
  "node_modules/lodash.deburr/index.js"(exports, module) {
    var INFINITY = 1 / 0;
    var symbolTag = "[object Symbol]";
    var reLatin = /[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g;
    var rsComboMarksRange = "\\u0300-\\u036f\\ufe20-\\ufe23";
    var rsComboSymbolsRange = "\\u20d0-\\u20f0";
    var rsCombo = "[" + rsComboMarksRange + rsComboSymbolsRange + "]";
    var reComboMark = RegExp(rsCombo, "g");
    var deburredLetters = {
      // Latin-1 Supplement block.
      "À": "A",
      "Á": "A",
      "Â": "A",
      "Ã": "A",
      "Ä": "A",
      "Å": "A",
      "à": "a",
      "á": "a",
      "â": "a",
      "ã": "a",
      "ä": "a",
      "å": "a",
      "Ç": "C",
      "ç": "c",
      "Ð": "D",
      "ð": "d",
      "È": "E",
      "É": "E",
      "Ê": "E",
      "Ë": "E",
      "è": "e",
      "é": "e",
      "ê": "e",
      "ë": "e",
      "Ì": "I",
      "Í": "I",
      "Î": "I",
      "Ï": "I",
      "ì": "i",
      "í": "i",
      "î": "i",
      "ï": "i",
      "Ñ": "N",
      "ñ": "n",
      "Ò": "O",
      "Ó": "O",
      "Ô": "O",
      "Õ": "O",
      "Ö": "O",
      "Ø": "O",
      "ò": "o",
      "ó": "o",
      "ô": "o",
      "õ": "o",
      "ö": "o",
      "ø": "o",
      "Ù": "U",
      "Ú": "U",
      "Û": "U",
      "Ü": "U",
      "ù": "u",
      "ú": "u",
      "û": "u",
      "ü": "u",
      "Ý": "Y",
      "ý": "y",
      "ÿ": "y",
      "Æ": "Ae",
      "æ": "ae",
      "Þ": "Th",
      "þ": "th",
      "ß": "ss",
      // Latin Extended-A block.
      "Ā": "A",
      "Ă": "A",
      "Ą": "A",
      "ā": "a",
      "ă": "a",
      "ą": "a",
      "Ć": "C",
      "Ĉ": "C",
      "Ċ": "C",
      "Č": "C",
      "ć": "c",
      "ĉ": "c",
      "ċ": "c",
      "č": "c",
      "Ď": "D",
      "Đ": "D",
      "ď": "d",
      "đ": "d",
      "Ē": "E",
      "Ĕ": "E",
      "Ė": "E",
      "Ę": "E",
      "Ě": "E",
      "ē": "e",
      "ĕ": "e",
      "ė": "e",
      "ę": "e",
      "ě": "e",
      "Ĝ": "G",
      "Ğ": "G",
      "Ġ": "G",
      "Ģ": "G",
      "ĝ": "g",
      "ğ": "g",
      "ġ": "g",
      "ģ": "g",
      "Ĥ": "H",
      "Ħ": "H",
      "ĥ": "h",
      "ħ": "h",
      "Ĩ": "I",
      "Ī": "I",
      "Ĭ": "I",
      "Į": "I",
      "İ": "I",
      "ĩ": "i",
      "ī": "i",
      "ĭ": "i",
      "į": "i",
      "ı": "i",
      "Ĵ": "J",
      "ĵ": "j",
      "Ķ": "K",
      "ķ": "k",
      "ĸ": "k",
      "Ĺ": "L",
      "Ļ": "L",
      "Ľ": "L",
      "Ŀ": "L",
      "Ł": "L",
      "ĺ": "l",
      "ļ": "l",
      "ľ": "l",
      "ŀ": "l",
      "ł": "l",
      "Ń": "N",
      "Ņ": "N",
      "Ň": "N",
      "Ŋ": "N",
      "ń": "n",
      "ņ": "n",
      "ň": "n",
      "ŋ": "n",
      "Ō": "O",
      "Ŏ": "O",
      "Ő": "O",
      "ō": "o",
      "ŏ": "o",
      "ő": "o",
      "Ŕ": "R",
      "Ŗ": "R",
      "Ř": "R",
      "ŕ": "r",
      "ŗ": "r",
      "ř": "r",
      "Ś": "S",
      "Ŝ": "S",
      "Ş": "S",
      "Š": "S",
      "ś": "s",
      "ŝ": "s",
      "ş": "s",
      "š": "s",
      "Ţ": "T",
      "Ť": "T",
      "Ŧ": "T",
      "ţ": "t",
      "ť": "t",
      "ŧ": "t",
      "Ũ": "U",
      "Ū": "U",
      "Ŭ": "U",
      "Ů": "U",
      "Ű": "U",
      "Ų": "U",
      "ũ": "u",
      "ū": "u",
      "ŭ": "u",
      "ů": "u",
      "ű": "u",
      "ų": "u",
      "Ŵ": "W",
      "ŵ": "w",
      "Ŷ": "Y",
      "ŷ": "y",
      "Ÿ": "Y",
      "Ź": "Z",
      "Ż": "Z",
      "Ž": "Z",
      "ź": "z",
      "ż": "z",
      "ž": "z",
      "Ĳ": "IJ",
      "ĳ": "ij",
      "Œ": "Oe",
      "œ": "oe",
      "ŉ": "'n",
      "ſ": "ss"
    };
    var freeGlobal = typeof global == "object" && global && global.Object === Object && global;
    var freeSelf = typeof self == "object" && self && self.Object === Object && self;
    var root = freeGlobal || freeSelf || Function("return this")();
    function basePropertyOf(object) {
      return function(key) {
        return object == null ? void 0 : object[key];
      };
    }
    var deburrLetter = basePropertyOf(deburredLetters);
    var objectProto = Object.prototype;
    var objectToString = objectProto.toString;
    var Symbol = root.Symbol;
    var symbolProto = Symbol ? Symbol.prototype : void 0;
    var symbolToString = symbolProto ? symbolProto.toString : void 0;
    function baseToString(value) {
      if (typeof value == "string") {
        return value;
      }
      if (isSymbol(value)) {
        return symbolToString ? symbolToString.call(value) : "";
      }
      var result = value + "";
      return result == "0" && 1 / value == -INFINITY ? "-0" : result;
    }
    function isObjectLike(value) {
      return !!value && typeof value == "object";
    }
    function isSymbol(value) {
      return typeof value == "symbol" || isObjectLike(value) && objectToString.call(value) == symbolTag;
    }
    function toString(value) {
      return value == null ? "" : baseToString(value);
    }
    function deburr(string) {
      string = toString(string);
      return string && string.replace(reLatin, deburrLetter).replace(reComboMark, "");
    }
    module.exports = deburr;
  }
});

// node_modules/@sindresorhus/transliterate/node_modules/escape-string-regexp/index.js
var require_escape_string_regexp2 = __commonJS({
  "node_modules/@sindresorhus/transliterate/node_modules/escape-string-regexp/index.js"(exports, module) {
    "use strict";
    var matchOperatorsRegex = /[|\\{}()[\]^$+*?.-]/g;
    module.exports = (string) => {
      if (typeof string !== "string") {
        throw new TypeError("Expected a string");
      }
      return string.replace(matchOperatorsRegex, "\\$&");
    };
  }
});

// node_modules/@sindresorhus/transliterate/replacements.js
var require_replacements = __commonJS({
  "node_modules/@sindresorhus/transliterate/replacements.js"(exports, module) {
    "use strict";
    module.exports = [
      // German umlauts
      ["ß", "ss"],
      ["ä", "ae"],
      ["Ä", "Ae"],
      ["ö", "oe"],
      ["Ö", "Oe"],
      ["ü", "ue"],
      ["Ü", "Ue"],
      // Latin
      ["À", "A"],
      ["Á", "A"],
      ["Â", "A"],
      ["Ã", "A"],
      ["Ä", "Ae"],
      ["Å", "A"],
      ["Æ", "AE"],
      ["Ç", "C"],
      ["È", "E"],
      ["É", "E"],
      ["Ê", "E"],
      ["Ë", "E"],
      ["Ì", "I"],
      ["Í", "I"],
      ["Î", "I"],
      ["Ï", "I"],
      ["Ð", "D"],
      ["Ñ", "N"],
      ["Ò", "O"],
      ["Ó", "O"],
      ["Ô", "O"],
      ["Õ", "O"],
      ["Ö", "Oe"],
      ["Ő", "O"],
      ["Ø", "O"],
      ["Ù", "U"],
      ["Ú", "U"],
      ["Û", "U"],
      ["Ü", "Ue"],
      ["Ű", "U"],
      ["Ý", "Y"],
      ["Þ", "TH"],
      ["ß", "ss"],
      ["à", "a"],
      ["á", "a"],
      ["â", "a"],
      ["ã", "a"],
      ["ä", "ae"],
      ["å", "a"],
      ["æ", "ae"],
      ["ç", "c"],
      ["è", "e"],
      ["é", "e"],
      ["ê", "e"],
      ["ë", "e"],
      ["ì", "i"],
      ["í", "i"],
      ["î", "i"],
      ["ï", "i"],
      ["ð", "d"],
      ["ñ", "n"],
      ["ò", "o"],
      ["ó", "o"],
      ["ô", "o"],
      ["õ", "o"],
      ["ö", "oe"],
      ["ő", "o"],
      ["ø", "o"],
      ["ù", "u"],
      ["ú", "u"],
      ["û", "u"],
      ["ü", "ue"],
      ["ű", "u"],
      ["ý", "y"],
      ["þ", "th"],
      ["ÿ", "y"],
      ["ẞ", "SS"],
      // Vietnamese
      ["à", "a"],
      ["À", "A"],
      ["á", "a"],
      ["Á", "A"],
      ["â", "a"],
      ["Â", "A"],
      ["ã", "a"],
      ["Ã", "A"],
      ["è", "e"],
      ["È", "E"],
      ["é", "e"],
      ["É", "E"],
      ["ê", "e"],
      ["Ê", "E"],
      ["ì", "i"],
      ["Ì", "I"],
      ["í", "i"],
      ["Í", "I"],
      ["ò", "o"],
      ["Ò", "O"],
      ["ó", "o"],
      ["Ó", "O"],
      ["ô", "o"],
      ["Ô", "O"],
      ["õ", "o"],
      ["Õ", "O"],
      ["ù", "u"],
      ["Ù", "U"],
      ["ú", "u"],
      ["Ú", "U"],
      ["ý", "y"],
      ["Ý", "Y"],
      ["ă", "a"],
      ["Ă", "A"],
      ["Đ", "D"],
      ["đ", "d"],
      ["ĩ", "i"],
      ["Ĩ", "I"],
      ["ũ", "u"],
      ["Ũ", "U"],
      ["ơ", "o"],
      ["Ơ", "O"],
      ["ư", "u"],
      ["Ư", "U"],
      ["ạ", "a"],
      ["Ạ", "A"],
      ["ả", "a"],
      ["Ả", "A"],
      ["ấ", "a"],
      ["Ấ", "A"],
      ["ầ", "a"],
      ["Ầ", "A"],
      ["ẩ", "a"],
      ["Ẩ", "A"],
      ["ẫ", "a"],
      ["Ẫ", "A"],
      ["ậ", "a"],
      ["Ậ", "A"],
      ["ắ", "a"],
      ["Ắ", "A"],
      ["ằ", "a"],
      ["Ằ", "A"],
      ["ẳ", "a"],
      ["Ẳ", "A"],
      ["ẵ", "a"],
      ["Ẵ", "A"],
      ["ặ", "a"],
      ["Ặ", "A"],
      ["ẹ", "e"],
      ["Ẹ", "E"],
      ["ẻ", "e"],
      ["Ẻ", "E"],
      ["ẽ", "e"],
      ["Ẽ", "E"],
      ["ế", "e"],
      ["Ế", "E"],
      ["ề", "e"],
      ["Ề", "E"],
      ["ể", "e"],
      ["Ể", "E"],
      ["ễ", "e"],
      ["Ễ", "E"],
      ["ệ", "e"],
      ["Ệ", "E"],
      ["ỉ", "i"],
      ["Ỉ", "I"],
      ["ị", "i"],
      ["Ị", "I"],
      ["ọ", "o"],
      ["Ọ", "O"],
      ["ỏ", "o"],
      ["Ỏ", "O"],
      ["ố", "o"],
      ["Ố", "O"],
      ["ồ", "o"],
      ["Ồ", "O"],
      ["ổ", "o"],
      ["Ổ", "O"],
      ["ỗ", "o"],
      ["Ỗ", "O"],
      ["ộ", "o"],
      ["Ộ", "O"],
      ["ớ", "o"],
      ["Ớ", "O"],
      ["ờ", "o"],
      ["Ờ", "O"],
      ["ở", "o"],
      ["Ở", "O"],
      ["ỡ", "o"],
      ["Ỡ", "O"],
      ["ợ", "o"],
      ["Ợ", "O"],
      ["ụ", "u"],
      ["Ụ", "U"],
      ["ủ", "u"],
      ["Ủ", "U"],
      ["ứ", "u"],
      ["Ứ", "U"],
      ["ừ", "u"],
      ["Ừ", "U"],
      ["ử", "u"],
      ["Ử", "U"],
      ["ữ", "u"],
      ["Ữ", "U"],
      ["ự", "u"],
      ["Ự", "U"],
      ["ỳ", "y"],
      ["Ỳ", "Y"],
      ["ỵ", "y"],
      ["Ỵ", "Y"],
      ["ỷ", "y"],
      ["Ỷ", "Y"],
      ["ỹ", "y"],
      ["Ỹ", "Y"],
      // Arabic
      ["ء", "e"],
      ["آ", "a"],
      ["أ", "a"],
      ["ؤ", "w"],
      ["إ", "i"],
      ["ئ", "y"],
      ["ا", "a"],
      ["ب", "b"],
      ["ة", "t"],
      ["ت", "t"],
      ["ث", "th"],
      ["ج", "j"],
      ["ح", "h"],
      ["خ", "kh"],
      ["د", "d"],
      ["ذ", "dh"],
      ["ر", "r"],
      ["ز", "z"],
      ["س", "s"],
      ["ش", "sh"],
      ["ص", "s"],
      ["ض", "d"],
      ["ط", "t"],
      ["ظ", "z"],
      ["ع", "e"],
      ["غ", "gh"],
      ["ـ", "_"],
      ["ف", "f"],
      ["ق", "q"],
      ["ك", "k"],
      ["ل", "l"],
      ["م", "m"],
      ["ن", "n"],
      ["ه", "h"],
      ["و", "w"],
      ["ى", "a"],
      ["ي", "y"],
      ["َ‎", "a"],
      ["ُ", "u"],
      ["ِ‎", "i"],
      ["٠", "0"],
      ["١", "1"],
      ["٢", "2"],
      ["٣", "3"],
      ["٤", "4"],
      ["٥", "5"],
      ["٦", "6"],
      ["٧", "7"],
      ["٨", "8"],
      ["٩", "9"],
      // Persian / Farsi
      ["چ", "ch"],
      ["ک", "k"],
      ["گ", "g"],
      ["پ", "p"],
      ["ژ", "zh"],
      ["ی", "y"],
      ["۰", "0"],
      ["۱", "1"],
      ["۲", "2"],
      ["۳", "3"],
      ["۴", "4"],
      ["۵", "5"],
      ["۶", "6"],
      ["۷", "7"],
      ["۸", "8"],
      ["۹", "9"],
      // Pashto
      ["ټ", "p"],
      ["ځ", "z"],
      ["څ", "c"],
      ["ډ", "d"],
      ["ﺫ", "d"],
      ["ﺭ", "r"],
      ["ړ", "r"],
      ["ﺯ", "z"],
      ["ږ", "g"],
      ["ښ", "x"],
      ["ګ", "g"],
      ["ڼ", "n"],
      ["ۀ", "e"],
      ["ې", "e"],
      ["ۍ", "ai"],
      // Urdu
      ["ٹ", "t"],
      ["ڈ", "d"],
      ["ڑ", "r"],
      ["ں", "n"],
      ["ہ", "h"],
      ["ھ", "h"],
      ["ے", "e"],
      // Russian
      ["А", "A"],
      ["а", "a"],
      ["Б", "B"],
      ["б", "b"],
      ["В", "V"],
      ["в", "v"],
      ["Г", "G"],
      ["г", "g"],
      ["Д", "D"],
      ["д", "d"],
      ["Е", "E"],
      ["е", "e"],
      ["Ж", "Zh"],
      ["ж", "zh"],
      ["З", "Z"],
      ["з", "z"],
      ["И", "I"],
      ["и", "i"],
      ["Й", "J"],
      ["й", "j"],
      ["К", "K"],
      ["к", "k"],
      ["Л", "L"],
      ["л", "l"],
      ["М", "M"],
      ["м", "m"],
      ["Н", "N"],
      ["н", "n"],
      ["О", "O"],
      ["о", "o"],
      ["П", "P"],
      ["п", "p"],
      ["Р", "R"],
      ["р", "r"],
      ["С", "S"],
      ["с", "s"],
      ["Т", "T"],
      ["т", "t"],
      ["У", "U"],
      ["у", "u"],
      ["Ф", "F"],
      ["ф", "f"],
      ["Х", "H"],
      ["х", "h"],
      ["Ц", "Cz"],
      ["ц", "cz"],
      ["Ч", "Ch"],
      ["ч", "ch"],
      ["Ш", "Sh"],
      ["ш", "sh"],
      ["Щ", "Shh"],
      ["щ", "shh"],
      ["Ъ", ""],
      ["ъ", ""],
      ["Ы", "Y"],
      ["ы", "y"],
      ["Ь", ""],
      ["ь", ""],
      ["Э", "E"],
      ["э", "e"],
      ["Ю", "Yu"],
      ["ю", "yu"],
      ["Я", "Ya"],
      ["я", "ya"],
      ["Ё", "Yo"],
      ["ё", "yo"],
      // Romanian
      ["ă", "a"],
      ["Ă", "A"],
      ["ș", "s"],
      ["Ș", "S"],
      ["ț", "t"],
      ["Ț", "T"],
      ["ţ", "t"],
      ["Ţ", "T"],
      // Turkish
      ["ş", "s"],
      ["Ş", "S"],
      ["ç", "c"],
      ["Ç", "C"],
      ["ğ", "g"],
      ["Ğ", "G"],
      ["ı", "i"],
      ["İ", "I"],
      // Armenian
      ["ա", "a"],
      ["Ա", "A"],
      ["բ", "b"],
      ["Բ", "B"],
      ["գ", "g"],
      ["Գ", "G"],
      ["դ", "d"],
      ["Դ", "D"],
      ["ե", "ye"],
      ["Ե", "Ye"],
      ["զ", "z"],
      ["Զ", "Z"],
      ["է", "e"],
      ["Է", "E"],
      ["ը", "y"],
      ["Ը", "Y"],
      ["թ", "t"],
      ["Թ", "T"],
      ["ժ", "zh"],
      ["Ժ", "Zh"],
      ["ի", "i"],
      ["Ի", "I"],
      ["լ", "l"],
      ["Լ", "L"],
      ["խ", "kh"],
      ["Խ", "Kh"],
      ["ծ", "ts"],
      ["Ծ", "Ts"],
      ["կ", "k"],
      ["Կ", "K"],
      ["հ", "h"],
      ["Հ", "H"],
      ["ձ", "dz"],
      ["Ձ", "Dz"],
      ["ղ", "gh"],
      ["Ղ", "Gh"],
      ["ճ", "tch"],
      ["Ճ", "Tch"],
      ["մ", "m"],
      ["Մ", "M"],
      ["յ", "y"],
      ["Յ", "Y"],
      ["ն", "n"],
      ["Ն", "N"],
      ["շ", "sh"],
      ["Շ", "Sh"],
      ["ո", "vo"],
      ["Ո", "Vo"],
      ["չ", "ch"],
      ["Չ", "Ch"],
      ["պ", "p"],
      ["Պ", "P"],
      ["ջ", "j"],
      ["Ջ", "J"],
      ["ռ", "r"],
      ["Ռ", "R"],
      ["ս", "s"],
      ["Ս", "S"],
      ["վ", "v"],
      ["Վ", "V"],
      ["տ", "t"],
      ["Տ", "T"],
      ["ր", "r"],
      ["Ր", "R"],
      ["ց", "c"],
      ["Ց", "C"],
      ["ու", "u"],
      ["ՈՒ", "U"],
      ["Ու", "U"],
      ["փ", "p"],
      ["Փ", "P"],
      ["ք", "q"],
      ["Ք", "Q"],
      ["օ", "o"],
      ["Օ", "O"],
      ["ֆ", "f"],
      ["Ֆ", "F"],
      ["և", "yev"],
      // Georgian
      ["ა", "a"],
      ["ბ", "b"],
      ["გ", "g"],
      ["დ", "d"],
      ["ე", "e"],
      ["ვ", "v"],
      ["ზ", "z"],
      ["თ", "t"],
      ["ი", "i"],
      ["კ", "k"],
      ["ლ", "l"],
      ["მ", "m"],
      ["ნ", "n"],
      ["ო", "o"],
      ["პ", "p"],
      ["ჟ", "zh"],
      ["რ", "r"],
      ["ს", "s"],
      ["ტ", "t"],
      ["უ", "u"],
      ["ფ", "ph"],
      ["ქ", "q"],
      ["ღ", "gh"],
      ["ყ", "k"],
      ["შ", "sh"],
      ["ჩ", "ch"],
      ["ც", "ts"],
      ["ძ", "dz"],
      ["წ", "ts"],
      ["ჭ", "tch"],
      ["ხ", "kh"],
      ["ჯ", "j"],
      ["ჰ", "h"],
      // Czech
      ["č", "c"],
      ["ď", "d"],
      ["ě", "e"],
      ["ň", "n"],
      ["ř", "r"],
      ["š", "s"],
      ["ť", "t"],
      ["ů", "u"],
      ["ž", "z"],
      ["Č", "C"],
      ["Ď", "D"],
      ["Ě", "E"],
      ["Ň", "N"],
      ["Ř", "R"],
      ["Š", "S"],
      ["Ť", "T"],
      ["Ů", "U"],
      ["Ž", "Z"],
      // Dhivehi
      ["ހ", "h"],
      ["ށ", "sh"],
      ["ނ", "n"],
      ["ރ", "r"],
      ["ބ", "b"],
      ["ޅ", "lh"],
      ["ކ", "k"],
      ["އ", "a"],
      ["ވ", "v"],
      ["މ", "m"],
      ["ފ", "f"],
      ["ދ", "dh"],
      ["ތ", "th"],
      ["ލ", "l"],
      ["ގ", "g"],
      ["ޏ", "gn"],
      ["ސ", "s"],
      ["ޑ", "d"],
      ["ޒ", "z"],
      ["ޓ", "t"],
      ["ޔ", "y"],
      ["ޕ", "p"],
      ["ޖ", "j"],
      ["ޗ", "ch"],
      ["ޘ", "tt"],
      ["ޙ", "hh"],
      ["ޚ", "kh"],
      ["ޛ", "th"],
      ["ޜ", "z"],
      ["ޝ", "sh"],
      ["ޞ", "s"],
      ["ޟ", "d"],
      ["ޠ", "t"],
      ["ޡ", "z"],
      ["ޢ", "a"],
      ["ޣ", "gh"],
      ["ޤ", "q"],
      ["ޥ", "w"],
      ["ަ", "a"],
      ["ާ", "aa"],
      ["ި", "i"],
      ["ީ", "ee"],
      ["ު", "u"],
      ["ޫ", "oo"],
      ["ެ", "e"],
      ["ޭ", "ey"],
      ["ޮ", "o"],
      ["ޯ", "oa"],
      ["ް", ""],
      // Greek
      ["α", "a"],
      ["β", "v"],
      ["γ", "g"],
      ["δ", "d"],
      ["ε", "e"],
      ["ζ", "z"],
      ["η", "i"],
      ["θ", "th"],
      ["ι", "i"],
      ["κ", "k"],
      ["λ", "l"],
      ["μ", "m"],
      ["ν", "n"],
      ["ξ", "ks"],
      ["ο", "o"],
      ["π", "p"],
      ["ρ", "r"],
      ["σ", "s"],
      ["τ", "t"],
      ["υ", "y"],
      ["φ", "f"],
      ["χ", "x"],
      ["ψ", "ps"],
      ["ω", "o"],
      ["ά", "a"],
      ["έ", "e"],
      ["ί", "i"],
      ["ό", "o"],
      ["ύ", "y"],
      ["ή", "i"],
      ["ώ", "o"],
      ["ς", "s"],
      ["ϊ", "i"],
      ["ΰ", "y"],
      ["ϋ", "y"],
      ["ΐ", "i"],
      ["Α", "A"],
      ["Β", "B"],
      ["Γ", "G"],
      ["Δ", "D"],
      ["Ε", "E"],
      ["Ζ", "Z"],
      ["Η", "I"],
      ["Θ", "TH"],
      ["Ι", "I"],
      ["Κ", "K"],
      ["Λ", "L"],
      ["Μ", "M"],
      ["Ν", "N"],
      ["Ξ", "KS"],
      ["Ο", "O"],
      ["Π", "P"],
      ["Ρ", "R"],
      ["Σ", "S"],
      ["Τ", "T"],
      ["Υ", "Y"],
      ["Φ", "F"],
      ["Χ", "X"],
      ["Ψ", "PS"],
      ["Ω", "O"],
      ["Ά", "A"],
      ["Έ", "E"],
      ["Ί", "I"],
      ["Ό", "O"],
      ["Ύ", "Y"],
      ["Ή", "I"],
      ["Ώ", "O"],
      ["Ϊ", "I"],
      ["Ϋ", "Y"],
      // Disabled as it conflicts with German and Latin.
      // Hungarian
      // ['ä', 'a'],
      // ['Ä', 'A'],
      // ['ö', 'o'],
      // ['Ö', 'O'],
      // ['ü', 'u'],
      // ['Ü', 'U'],
      // ['ű', 'u'],
      // ['Ű', 'U'],
      // Latvian
      ["ā", "a"],
      ["ē", "e"],
      ["ģ", "g"],
      ["ī", "i"],
      ["ķ", "k"],
      ["ļ", "l"],
      ["ņ", "n"],
      ["ū", "u"],
      ["Ā", "A"],
      ["Ē", "E"],
      ["Ģ", "G"],
      ["Ī", "I"],
      ["Ķ", "K"],
      ["Ļ", "L"],
      ["Ņ", "N"],
      ["Ū", "U"],
      ["č", "c"],
      ["š", "s"],
      ["ž", "z"],
      ["Č", "C"],
      ["Š", "S"],
      ["Ž", "Z"],
      // Lithuanian
      ["ą", "a"],
      ["č", "c"],
      ["ę", "e"],
      ["ė", "e"],
      ["į", "i"],
      ["š", "s"],
      ["ų", "u"],
      ["ū", "u"],
      ["ž", "z"],
      ["Ą", "A"],
      ["Č", "C"],
      ["Ę", "E"],
      ["Ė", "E"],
      ["Į", "I"],
      ["Š", "S"],
      ["Ų", "U"],
      ["Ū", "U"],
      // Macedonian
      ["Ќ", "Kj"],
      ["ќ", "kj"],
      ["Љ", "Lj"],
      ["љ", "lj"],
      ["Њ", "Nj"],
      ["њ", "nj"],
      ["Тс", "Ts"],
      ["тс", "ts"],
      // Polish
      ["ą", "a"],
      ["ć", "c"],
      ["ę", "e"],
      ["ł", "l"],
      ["ń", "n"],
      ["ś", "s"],
      ["ź", "z"],
      ["ż", "z"],
      ["Ą", "A"],
      ["Ć", "C"],
      ["Ę", "E"],
      ["Ł", "L"],
      ["Ń", "N"],
      ["Ś", "S"],
      ["Ź", "Z"],
      ["Ż", "Z"],
      // Disabled as it conflicts with Vietnamese.
      // Serbian
      // ['љ', 'lj'],
      // ['њ', 'nj'],
      // ['Љ', 'Lj'],
      // ['Њ', 'Nj'],
      // ['đ', 'dj'],
      // ['Đ', 'Dj'],
      // ['ђ', 'dj'],
      // ['ј', 'j'],
      // ['ћ', 'c'],
      // ['џ', 'dz'],
      // ['Ђ', 'Dj'],
      // ['Ј', 'j'],
      // ['Ћ', 'C'],
      // ['Џ', 'Dz'],
      // Disabled as it conflicts with German and Latin.
      // Slovak
      // ['ä', 'a'],
      // ['Ä', 'A'],
      // ['ľ', 'l'],
      // ['ĺ', 'l'],
      // ['ŕ', 'r'],
      // ['Ľ', 'L'],
      // ['Ĺ', 'L'],
      // ['Ŕ', 'R'],
      // Disabled as it conflicts with German and Latin.
      // Swedish
      // ['å', 'o'],
      // ['Å', 'o'],
      // ['ä', 'a'],
      // ['Ä', 'A'],
      // ['ë', 'e'],
      // ['Ë', 'E'],
      // ['ö', 'o'],
      // ['Ö', 'O'],
      // Ukrainian
      ["Є", "Ye"],
      ["І", "I"],
      ["Ї", "Yi"],
      ["Ґ", "G"],
      ["є", "ye"],
      ["і", "i"],
      ["ї", "yi"],
      ["ґ", "g"]
      // Danish
      // ['Æ', 'Ae'],
      // ['Ø', 'Oe'],
      // ['Å', 'Aa'],
      // ['æ', 'ae'],
      // ['ø', 'oe'],
      // ['å', 'aa']
    ];
  }
});

// node_modules/@sindresorhus/transliterate/index.js
var require_transliterate = __commonJS({
  "node_modules/@sindresorhus/transliterate/index.js"(exports, module) {
    "use strict";
    var deburr = require_lodash();
    var escapeStringRegexp = require_escape_string_regexp2();
    var builtinReplacements = require_replacements();
    var doCustomReplacements = (string, replacements) => {
      for (const [key, value] of replacements) {
        string = string.replace(new RegExp(escapeStringRegexp(key), "g"), value);
      }
      return string;
    };
    module.exports = (string, options) => {
      if (typeof string !== "string") {
        throw new TypeError(`Expected a string, got \`${typeof string}\``);
      }
      options = {
        customReplacements: [],
        ...options
      };
      const customReplacements = new Map([
        ...builtinReplacements,
        ...options.customReplacements
      ]);
      string = string.normalize();
      string = doCustomReplacements(string, customReplacements);
      string = deburr(string);
      return string;
    };
  }
});

// node_modules/@sindresorhus/slugify/overridable-replacements.js
var require_overridable_replacements = __commonJS({
  "node_modules/@sindresorhus/slugify/overridable-replacements.js"(exports, module) {
    "use strict";
    module.exports = [
      ["&", " and "],
      ["🦄", " unicorn "],
      ["♥", " love "]
    ];
  }
});

// node_modules/@sindresorhus/slugify/index.js
var require_slugify = __commonJS({
  "node_modules/@sindresorhus/slugify/index.js"(exports, module) {
    "use strict";
    var escapeStringRegexp = require_escape_string_regexp();
    var transliterate = require_transliterate();
    var builtinOverridableReplacements = require_overridable_replacements();
    var decamelize = (string) => {
      return string.replace(/([A-Z]{2,})(\d+)/g, "$1 $2").replace(/([a-z\d]+)([A-Z]{2,})/g, "$1 $2").replace(/([a-z\d])([A-Z])/g, "$1 $2").replace(/([A-Z]+)([A-Z][a-z\d]+)/g, "$1 $2");
    };
    var removeMootSeparators = (string, separator) => {
      const escapedSeparator = escapeStringRegexp(separator);
      return string.replace(new RegExp(`${escapedSeparator}{2,}`, "g"), separator).replace(new RegExp(`^${escapedSeparator}|${escapedSeparator}$`, "g"), "");
    };
    var slugify2 = (string, options) => {
      if (typeof string !== "string") {
        throw new TypeError(`Expected a string, got \`${typeof string}\``);
      }
      options = {
        separator: "-",
        lowercase: true,
        decamelize: true,
        customReplacements: [],
        preserveLeadingUnderscore: false,
        ...options
      };
      const shouldPrependUnderscore = options.preserveLeadingUnderscore && string.startsWith("_");
      const customReplacements = new Map([
        ...builtinOverridableReplacements,
        ...options.customReplacements
      ]);
      string = transliterate(string, { customReplacements });
      if (options.decamelize) {
        string = decamelize(string);
      }
      let patternSlug = /[^a-zA-Z\d]+/g;
      if (options.lowercase) {
        string = string.toLowerCase();
        patternSlug = /[^a-z\d]+/g;
      }
      string = string.replace(patternSlug, options.separator);
      string = string.replace(/\\/g, "");
      if (options.separator) {
        string = removeMootSeparators(string, options.separator);
      }
      if (shouldPrependUnderscore) {
        string = `_${string}`;
      }
      return string;
    };
    var counter = () => {
      const occurrences = /* @__PURE__ */ new Map();
      const countable = (string, options) => {
        string = slugify2(string, options);
        if (!string) {
          return "";
        }
        const stringLower = string.toLowerCase();
        const numberless = occurrences.get(stringLower.replace(/(?:-\d+?)+?$/, "")) || 0;
        const counter2 = occurrences.get(stringLower);
        occurrences.set(stringLower, typeof counter2 === "number" ? counter2 + 1 : 1);
        const newCounter = occurrences.get(stringLower) || 2;
        if (newCounter >= 2 || numberless > 2) {
          string = `${string}-${newCounter}`;
        }
        return string;
      };
      countable.reset = () => {
        occurrences.clear();
      };
      return countable;
    };
    module.exports = slugify2;
    module.exports.counter = counter;
  }
});

// node_modules/@strapi/content-type-builder/dist/admin/constants.mjs
var PERMISSIONS = {
  // This permission regards the main component (App) and is used to tell
  // If the plugin link should be displayed in the menu
  // And also if the plugin is accessible. This use case is found when a user types the url of the
  // plugin directly in the browser
  main: [
    {
      action: "plugin::content-type-builder.read",
      subject: null
    }
  ]
};
var MAX_COMPONENT_DEPTH = 6;

// node_modules/@strapi/content-type-builder/dist/admin/components/AutoReloadOverlayBlocker.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_react_dom = __toESM(require_react_dom(), 1);
var AutoReloadOverlayBlockerContext = React.createContext({
  lockAppWithAutoreload: () => {
  },
  unlockAppWithAutoreload: () => {
  }
});
var MAX_ELAPSED_TIME = 300 * 1e3;
var AutoReloadOverlayBlockerProvider = ({ children }) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const [config, setConfig] = React.useState({});
  const [failed, setFailed] = React.useState(false);
  const lockAppWithAutoreload = React.useCallback((config2 = {}) => {
    setIsOpen(true);
    setConfig(config2);
  }, []);
  const unlockAppWithAutoreload = React.useCallback(() => {
    setIsOpen(false);
    setConfig({});
  }, []);
  React.useEffect(() => {
    if (isOpen) {
      const timeout = setTimeout(() => {
        setFailed(true);
      }, MAX_ELAPSED_TIME);
      return () => {
        clearTimeout(timeout);
      };
    }
  }, [
    isOpen
  ]);
  let displayedIcon = (config == null ? void 0 : config.icon) || "reload";
  let description = {
    id: (config == null ? void 0 : config.description) || "components.OverlayBlocker.description",
    defaultMessage: "You're using a feature that needs the server to restart. The page will reload automatically."
  };
  let title = {
    id: (config == null ? void 0 : config.title) || "components.OverlayBlocker.title",
    defaultMessage: "Waiting for restart"
  };
  if (failed) {
    displayedIcon = "time";
    description = {
      id: "components.OverlayBlocker.description.serverError",
      defaultMessage: "The server should have restarted, please check your logs in the terminal."
    };
    title = {
      id: "components.OverlayBlocker.title.serverError",
      defaultMessage: "The restart is taking longer than expected"
    };
  }
  const autoReloadValue = React.useMemo(() => ({
    lockAppWithAutoreload,
    unlockAppWithAutoreload
  }), [
    lockAppWithAutoreload,
    unlockAppWithAutoreload
  ]);
  return (0, import_jsx_runtime.jsxs)(AutoReloadOverlayBlockerContext.Provider, {
    value: autoReloadValue,
    children: [
      (0, import_jsx_runtime.jsx)(Blocker, {
        displayedIcon,
        isOpen,
        description,
        title
      }),
      children
    ]
  });
};
var Blocker = ({ displayedIcon, description, title, isOpen }) => {
  var _a;
  const { formatMessage } = useIntl();
  return isOpen && ((_a = globalThis == null ? void 0 : globalThis.document) == null ? void 0 : _a.body) ? (0, import_react_dom.createPortal)(
    (0, import_jsx_runtime.jsxs)(Overlay, {
      id: "autoReloadOverlayBlocker",
      direction: "column",
      alignItems: "center",
      gap: 6,
      children: [
        (0, import_jsx_runtime.jsxs)(Flex, {
          direction: "column",
          alignItems: "center",
          gap: 2,
          children: [
            (0, import_jsx_runtime.jsx)(Typography, {
              tag: "h1",
              variant: "alpha",
              children: formatMessage(title)
            }),
            (0, import_jsx_runtime.jsx)(Typography, {
              tag: "h2",
              textColor: "neutral600",
              fontSize: 4,
              fontWeight: "regular",
              children: formatMessage(description)
            })
          ]
        }),
        displayedIcon === "reload" && (0, import_jsx_runtime.jsx)(IconBox, {
          padding: 6,
          background: "primary100",
          borderColor: "primary200",
          children: (0, import_jsx_runtime.jsx)(LoaderReload, {
            width: "4rem",
            height: "4rem"
          })
        }),
        displayedIcon === "time" && (0, import_jsx_runtime.jsx)(IconBox, {
          padding: 6,
          background: "primary100",
          borderColor: "primary200",
          children: (0, import_jsx_runtime.jsx)(ForwardRef$4r, {
            width: "4rem",
            height: "4rem"
          })
        }),
        (0, import_jsx_runtime.jsx)(Box, {
          marginTop: 2,
          children: (0, import_jsx_runtime.jsx)(Link, {
            href: "https://docs.strapi.io",
            isExternal: true,
            children: formatMessage({
              id: "global.documentation",
              defaultMessage: "Read the documentation"
            })
          })
        })
      ]
    }),
    // eslint-disable-next-line no-undef
    globalThis.document.body
  ) : null;
};
var rotation = mt`
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(359deg);
    }
  `;
var LoaderReload = dt(ForwardRef$5n)`
  animation: ${rotation} 1s infinite linear;
`;
var Overlay = dt(Flex)`
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  /* TODO: set this up in the theme for consistence z-index values */
  z-index: 1140;
  padding-top: 16rem;

  & > * {
    position: relative;
    z-index: 1;
  }

  &:before {
    content: '';
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: ${({ theme }) => theme.colors.neutral0};
    opacity: 0.9;
  }
`;
var IconBox = dt(Box)`
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  svg {
    > path {
      fill: ${({ theme }) => theme.colors.primary600} !important;
    }
  }
`;
var useAutoReloadOverlayBlocker = () => React.useContext(AutoReloadOverlayBlockerContext);

// node_modules/@strapi/content-type-builder/dist/admin/components/DataManager/reducer.mjs
var import_merge = __toESM(require_merge(), 1);
var import_omit = __toESM(require_omit(), 1);

// node_modules/@strapi/content-type-builder/dist/admin/utils/makeUnique.mjs
var makeUnique = (array) => [
  ...new Set(array)
];

// node_modules/@strapi/content-type-builder/dist/admin/components/DataManager/undoRedo.mjs
var isCallable = (obj) => {
  return typeof obj === "function";
};
var createUndoRedoSlice = (sliceOpts, opts) => {
  const initialState3 = {
    past: [],
    future: [],
    current: isCallable(sliceOpts.initialState) ? sliceOpts.initialState() : sliceOpts.initialState
  };
  const { limit = 10 } = opts ?? {};
  const selector = opts.stateSelector || ((state) => state);
  const wrappedReducers = Object.keys(sliceOpts.reducers).reduce((acc, actionName) => {
    const reducer3 = sliceOpts.reducers[actionName];
    if (!isCallable(reducer3)) {
      throw new Error("Reducer must be a function. prepapre not support in UndoRedo wrapper");
    }
    acc[actionName] = (state, action) => {
      var _a;
      const newCurrent = reducer3(state.current, action);
      if ((_a = opts.excludeActionsFromHistory) == null ? void 0 : _a.includes(actionName)) {
        if (newCurrent !== void 0) {
          state.current = newCurrent;
        }
        return;
      }
      const originalCurrent = e(state.current);
      state.past.push(selector(originalCurrent));
      if (state.past.length > limit) {
        state.past.shift();
      }
      state.future = [];
      if (newCurrent !== void 0) {
        state.current = newCurrent;
      }
    };
    return acc;
  }, {});
  return createSlice({
    name: sliceOpts.name,
    initialState: initialState3,
    // @ts-expect-error - TS doesn't like the fact that we're adding extra reducers
    reducers: {
      ...wrappedReducers,
      undo: (state) => {
        if (state.past.length === 0) {
          return;
        }
        const previous = state.past.pop();
        if (previous !== void 0) {
          state.future = [
            state.current,
            ...state.future
          ];
          state.current = {
            ...state.current,
            ...previous
          };
        }
      },
      redo: (state) => {
        if (state.future.length === 0) {
          return;
        }
        const next = state.future.shift();
        if (next != void 0) {
          state.past = [
            ...state.past,
            state.current
          ];
          state.current = {
            ...state.current,
            ...next
          };
        }
      },
      discardAll: (state) => {
        if (opts.discard) {
          opts.discard(state.current);
        } else {
          state.current = initialState3.current;
        }
        state.past = [];
        state.future = [];
      },
      clearHistory: (state) => {
        state.past = [];
        state.future = [];
      }
    }
  });
};

// node_modules/@strapi/content-type-builder/dist/admin/components/DataManager/utils/formatSchemas.mjs
var formatSchema = (schema) => {
  return {
    ...schema,
    attributes: toAttributesArray(schema.attributes)
  };
};
var toAttributesArray = (attributes) => {
  return Object.keys(attributes).reduce((acc, current) => {
    acc.push({
      ...attributes[current],
      name: current
    });
    return acc;
  }, []);
};

// node_modules/@strapi/content-type-builder/dist/admin/components/DataManager/reducer.mjs
var initialState = {
  components: {},
  contentTypes: {},
  initialComponents: {},
  initialContentTypes: {},
  reservedNames: {
    models: [],
    attributes: []
  },
  isLoading: true
};
var ONE_SIDE_RELATIONS = [
  "oneWay",
  "manyWay"
];
var getOppositeRelation = (originalRelation) => {
  if (originalRelation === "manyToOne") {
    return "oneToMany";
  }
  if (originalRelation === "oneToMany") {
    return "manyToOne";
  }
  return originalRelation;
};
var findAttributeIndex = (type, attributeToFind) => {
  return type.attributes.findIndex(({ name }) => name === attributeToFind);
};
var getType = (state, { forTarget, targetUid }) => {
  return forTarget === "contentType" ? state.contentTypes[targetUid] : state.components[targetUid];
};
var setStatus = (type, status) => {
  switch (type.status) {
    case "NEW":
    case "REMOVED": {
      break;
    }
    default: {
      type.status = status;
    }
  }
};
var getNewStatus = (oldStatus, newStatus) => {
  if (oldStatus === "NEW" || oldStatus === "REMOVED") {
    return oldStatus;
  }
  return newStatus;
};
var setAttributeStatus = (attribute, status) => {
  attribute.status = getNewStatus(attribute.status, status);
};
var createAttribute = (properties) => {
  return {
    ...properties,
    status: "NEW"
  };
};
var setAttributeAt = (type, index, attribute) => {
  const previousAttribute = type.attributes[index];
  const newStatus = getNewStatus(previousAttribute.status, "CHANGED");
  type.attributes[index] = {
    ...attribute,
    status: newStatus
  };
  setStatus(type, "CHANGED");
};
var pushAttribute = (type, attribute) => {
  type.attributes.push(attribute);
  setStatus(type, "CHANGED");
};
var removeAttributeAt = (type, index) => {
  const attr = type.attributes[index];
  setStatus(type, "CHANGED");
  if (attr.status === "NEW") {
    type.attributes.splice(index, 1);
  } else {
    setAttributeStatus(attr, "REMOVED");
  }
};
var replaceAttributeAt = (type, index, attribute) => {
  type.attributes[index] = attribute;
  setStatus(type, "CHANGED");
};
var removeAttributeByName = (type, name) => {
  const idx = type.attributes.findIndex((attr2) => attr2.name === name);
  const attr = type.attributes[idx];
  setStatus(type, "CHANGED");
  if (attr.status === "NEW") {
    type.attributes.splice(idx, 1);
  } else {
    setAttributeStatus(attr, "REMOVED");
  }
};
var updateType = (type, data) => {
  (0, import_merge.default)(type, data);
  setStatus(type, "CHANGED");
};
var slice = createUndoRedoSlice({
  name: "data-manager",
  initialState,
  reducers: {
    init: (state, action) => {
      const { components, contentTypes, reservedNames } = action.payload;
      state.components = components;
      state.initialComponents = components;
      state.initialContentTypes = contentTypes;
      state.contentTypes = contentTypes;
      state.reservedNames = reservedNames;
      state.isLoading = false;
    },
    createComponentSchema: (state, action) => {
      const { uid, data, componentCategory } = action.payload;
      const newSchema = {
        uid,
        status: "NEW",
        category: componentCategory,
        modelName: data.displayName,
        globalId: data.displayName,
        info: {
          icon: data.icon,
          displayName: data.displayName
        },
        attributes: [],
        modelType: "component"
      };
      state.components[uid] = newSchema;
    },
    createSchema: (state, action) => {
      const { uid, data } = action.payload;
      const { displayName, singularName, pluralName, kind, draftAndPublish, pluginOptions } = data;
      const newSchema = {
        uid,
        status: "NEW",
        visible: true,
        modelType: "contentType",
        restrictRelationsTo: null,
        attributes: [],
        kind,
        modelName: displayName,
        globalId: displayName,
        options: {
          draftAndPublish
        },
        info: {
          displayName,
          singularName,
          pluralName
        },
        pluginOptions
      };
      state.contentTypes[uid] = newSchema;
    },
    addAttribute: (state, action) => {
      const { attributeToSet, forTarget, targetUid } = action.payload;
      const type = getType(state, {
        forTarget,
        targetUid
      });
      const attribute = createAttribute((0, import_omit.default)(attributeToSet, "createComponent"));
      if (attribute.type === "relation") {
        const target = attribute.target;
        const targetAttribute = attribute.targetAttribute || null;
        const relation = attribute.relation;
        const relationType = getRelationType(relation, targetAttribute);
        const isBidirectionalRelation = ![
          "oneWay",
          "manyWay"
        ].includes(relationType);
        if (isBidirectionalRelation) {
          const oppositeAttribute = createAttribute({
            name: targetAttribute,
            relation: getOppositeRelation(relationType),
            target: type.uid,
            targetAttribute: attribute.name,
            type: "relation",
            private: attribute.private
          });
          const targetType = getType(state, {
            forTarget,
            targetUid: target
          });
          pushAttribute(targetType, oppositeAttribute);
        }
      }
      pushAttribute(type, attribute);
      setStatus(type, "CHANGED");
    },
    moveAttribute: (state, action) => {
      const { forTarget, targetUid, from, to } = action.payload;
      const type = getType(state, {
        forTarget,
        targetUid
      });
      const attribute = type.attributes[from];
      type.attributes.splice(from, 1);
      type.attributes.splice(to, 0, attribute);
      setStatus(type, "CHANGED");
    },
    addCustomFieldAttribute: (state, action) => {
      const { attributeToSet, forTarget, targetUid } = action.payload;
      const type = getType(state, {
        forTarget,
        targetUid
      });
      pushAttribute(type, createAttribute(attributeToSet));
    },
    addCreatedComponentToDynamicZone: (state, action) => {
      const { dynamicZoneTarget, componentsToAdd, forTarget, targetUid } = action.payload;
      const type = getType(state, {
        forTarget,
        targetUid
      });
      const dzAttributeIndex = findAttributeIndex(type, dynamicZoneTarget);
      const attr = type.attributes[dzAttributeIndex];
      componentsToAdd.forEach((componentUid) => {
        attr.components.push(componentUid);
      });
      setAttributeStatus(attr, "CHANGED");
      setStatus(type, "CHANGED");
    },
    changeDynamicZoneComponents: (state, action) => {
      const { dynamicZoneTarget, newComponents, forTarget, targetUid } = action.payload;
      const type = getType(state, {
        forTarget,
        targetUid
      });
      const dzAttributeIndex = findAttributeIndex(type, dynamicZoneTarget);
      const attr = type.attributes[dzAttributeIndex];
      const currentDZComponents = attr.components;
      const updatedComponents = makeUnique([
        ...currentDZComponents,
        ...newComponents
      ]);
      setStatus(type, "CHANGED");
      setAttributeStatus(attr, "CHANGED");
      attr.components = updatedComponents;
    },
    editAttribute: (state, action) => {
      const { name, attributeToSet, forTarget, targetUid } = action.payload;
      const type = getType(state, {
        forTarget,
        targetUid
      });
      const initialAttributeIndex = findAttributeIndex(type, name);
      if (initialAttributeIndex === -1) {
        return;
      }
      const previousAttribute = type.attributes[initialAttributeIndex];
      setAttributeAt(type, initialAttributeIndex, attributeToSet);
      if (previousAttribute.type !== attributeToSet.type) {
        return;
      }
      if (previousAttribute.type !== "relation" || attributeToSet.type !== "relation") {
        return;
      }
      const previousTarget = getType(state, {
        forTarget: "contentType",
        targetUid: previousAttribute.target
      });
      const newTarget = getType(state, {
        forTarget: "contentType",
        targetUid: attributeToSet.target
      });
      const previousTargetAttributeIndex = findAttributeIndex(previousTarget, previousAttribute.targetAttribute ?? "");
      if (previousAttribute.targetAttribute) {
        removeAttributeByName(previousTarget, previousAttribute.targetAttribute);
      }
      const newRelationType = getRelationType(attributeToSet.relation, attributeToSet.targetAttribute);
      const isBidirectionnal = !ONE_SIDE_RELATIONS.includes(newRelationType);
      if (isBidirectionnal) {
        const newTargetAttribute = {
          name: attributeToSet.targetAttribute,
          type: "relation",
          relation: getOppositeRelation(attributeToSet.relation),
          targetAttribute: attributeToSet.name,
          target: type.uid,
          private: previousAttribute.private ?? attributeToSet.private,
          pluginOptions: previousAttribute.pluginOptions ?? attributeToSet.pluginOptions,
          status: "CHANGED"
        };
        if (previousTargetAttributeIndex !== -1 && previousTarget.uid === newTarget.uid) {
          replaceAttributeAt(newTarget, previousTargetAttributeIndex, newTargetAttribute);
        } else {
          pushAttribute(newTarget, {
            ...newTargetAttribute,
            status: "NEW"
          });
        }
      }
    },
    editCustomFieldAttribute: (state, action) => {
      const { forTarget, targetUid, name, attributeToSet } = action.payload;
      const initialAttributeName = name;
      const type = getType(state, {
        forTarget,
        targetUid
      });
      const initialAttributeIndex = findAttributeIndex(type, initialAttributeName);
      setAttributeAt(type, initialAttributeIndex, attributeToSet);
    },
    reloadPlugin: () => {
      return initialState;
    },
    removeComponentFromDynamicZone: (state, action) => {
      const { dzName, componentToRemoveIndex, forTarget, targetUid } = action.payload;
      const type = forTarget === "contentType" ? state.contentTypes[targetUid] : state.components[targetUid];
      if (!type) {
        return;
      }
      const dzAttributeIndex = findAttributeIndex(type, dzName);
      const attr = type.attributes[dzAttributeIndex];
      setStatus(type, "CHANGED");
      setAttributeStatus(attr, "CHANGED");
      attr.components.splice(componentToRemoveIndex, 1);
    },
    removeField: (state, action) => {
      const { forTarget, targetUid, attributeToRemoveName } = action.payload;
      const type = getType(state, {
        forTarget,
        targetUid
      });
      const attributeToRemoveIndex = findAttributeIndex(type, attributeToRemoveName);
      const attribute = type.attributes[attributeToRemoveIndex];
      if (attribute.type === "relation") {
        const { target, relation, targetAttribute: targetAttributeName } = attribute;
        const relationType = getRelationType(relation, targetAttributeName);
        const isBidirectionnal = !ONE_SIDE_RELATIONS.includes(relationType);
        if (isBidirectionnal && targetAttributeName) {
          const targetContentType = getType(state, {
            forTarget,
            targetUid: target
          });
          const targetAttributeIndex = findAttributeIndex(targetContentType, targetAttributeName);
          removeAttributeAt(targetContentType, targetAttributeIndex);
        }
      }
      type.attributes.forEach((attribute2) => {
        if (attribute2.type === "uid") {
          if (attribute2.targetField === attributeToRemoveName) {
            delete attribute2.targetField;
          }
        }
      });
      removeAttributeAt(type, attributeToRemoveIndex);
    },
    // only edits a component in practice
    updateComponentSchema: (state, action) => {
      const { data, uid } = action.payload;
      const type = state.components[uid];
      if (!type) {
        return;
      }
      updateType(type, {
        info: {
          displayName: data.displayName,
          icon: data.icon
        }
      });
    },
    updateComponentUid: (state, action) => {
      const { newComponentUID, uid } = action.payload;
      const type = state.components[uid];
      if (!type || type.status !== "NEW") {
        return;
      }
      if (newComponentUID !== uid) {
        const newType = {
          ...type,
          uid: newComponentUID
        };
        state.components[newComponentUID] = newType;
        delete state.components[uid];
        Object.keys(state.contentTypes).forEach((contentTypeUid) => {
          const contentType = state.contentTypes[contentTypeUid];
          contentType.attributes.forEach((attribute) => {
            if (attribute.type === "dynamiczone") {
              const newComponents = attribute.components.map((component) => {
                if (component === uid) {
                  return newComponentUID;
                }
                return component;
              });
              attribute.components = newComponents;
            }
          });
          contentType.attributes.forEach((attribute) => {
            if (attribute.type === "component" && attribute.component === uid) {
              attribute.component = newComponentUID;
            }
          });
        });
        Object.keys(state.components).forEach((componentUid) => {
          const component = state.components[componentUid];
          component.attributes.forEach((attribute) => {
            if (attribute.type === "component" && attribute.component === uid) {
              attribute.component = newComponentUID;
            }
          });
        });
      }
    },
    updateSchema: (state, action) => {
      const { data, uid } = action.payload;
      const { displayName, kind, draftAndPublish, pluginOptions } = data;
      const type = state.contentTypes[uid];
      if (!type) {
        return;
      }
      updateType(type, {
        info: {
          displayName
        },
        kind,
        options: {
          draftAndPublish
        },
        pluginOptions
      });
    },
    deleteComponent: (state, action) => {
      const uid = action.payload;
      if (state.components[uid].status === "NEW") {
        delete state.components[uid];
      } else {
        setStatus(state.components[uid], "REMOVED");
      }
      Object.keys(state.contentTypes).forEach((contentTypeUid) => {
        const contentType = state.contentTypes[contentTypeUid];
        contentType.attributes.forEach((attribute) => {
          if (attribute.type === "dynamiczone") {
            const newComponents = attribute.components.filter((component) => component !== uid);
            attribute.components = newComponents;
          }
        });
        contentType.attributes.forEach((attribute) => {
          if (attribute.type === "component" && attribute.component === uid) {
            removeAttributeByName(contentType, attribute.name);
          }
        });
      });
      Object.keys(state.components).forEach((componentUid) => {
        const component = state.components[componentUid];
        component.attributes.forEach((attribute) => {
          if (attribute.type === "component" && attribute.component === uid) {
            removeAttributeByName(component, attribute.name);
          }
        });
      });
    },
    deleteContentType: (state, action) => {
      const uid = action.payload;
      const type = state.contentTypes[uid];
      if (type.status === "NEW") {
        delete state.contentTypes[uid];
      } else {
        setStatus(type, "REMOVED");
      }
      Object.keys(state.components).forEach((componentUid) => {
        const component = state.components[componentUid];
        component.attributes.forEach((attribute) => {
          if (attribute.type === "relation" && attribute.target === uid) {
            removeAttributeByName(component, attribute.name);
          }
        });
      });
      Object.keys(state.contentTypes).forEach((contentTypeUid) => {
        const contentType = state.contentTypes[contentTypeUid];
        contentType.attributes.forEach((attribute) => {
          if (attribute.type === "relation" && attribute.target === uid) {
            removeAttributeByName(contentType, attribute.name);
          }
        });
      });
    },
    applyChange(state, reducerAction) {
      const { action, schema } = reducerAction.payload;
      switch (action) {
        case "add": {
          const uid = schema.uid;
          if (schema.modelType === "component") {
            state.components[uid] = {
              ...formatSchema(schema),
              status: "NEW"
            };
          } else {
            state.contentTypes[uid] = {
              ...formatSchema(schema),
              status: "NEW"
            };
          }
        }
      }
    }
  }
}, {
  limit: 50,
  excludeActionsFromHistory: [
    "reloadPlugin",
    "init"
  ],
  stateSelector: (state) => {
    if (!state) {
      return {};
    }
    return {
      components: state.components,
      contentTypes: state.contentTypes
    };
  },
  discard: (state) => {
    state.components = state.initialComponents;
    state.contentTypes = state.initialContentTypes;
  }
});
var { reducer, actions } = slice;

// node_modules/@strapi/content-type-builder/dist/admin/utils/nameToSlug.mjs
var import_slugify = __toESM(require_slugify(), 1);
var nameToSlug = (name) => (0, import_slugify.default)(name, {
  separator: "-"
});

// node_modules/@strapi/content-type-builder/dist/admin/components/FormModal/utils/createUid.mjs
var createUid = (name) => {
  const modelName = nameToSlug(name);
  return `api::${modelName}.${modelName}`;
};
var createComponentUid = (name, category) => {
  return `${nameToSlug(category)}.${nameToSlug(name)}`;
};

// node_modules/@strapi/content-type-builder/dist/admin/components/FormModal/reducer.mjs
var import_set = __toESM(require_set(), 1);
var import_snakeCase = __toESM(require_snakeCase(), 1);
var import_pluralize = __toESM(require_pluralize(), 1);

// node_modules/@strapi/content-type-builder/dist/admin/components/FormModal/utils/customFieldDefaultOptionsReducer.mjs
var customFieldDefaultOptionsReducer = (acc, option) => {
  if (option.items) {
    return option.items.reduce(customFieldDefaultOptionsReducer, acc);
  }
  if ("defaultValue" in option) {
    const { name, defaultValue } = option;
    acc.push({
      name,
      defaultValue
    });
  }
  return acc;
};

// node_modules/@strapi/content-type-builder/dist/admin/components/FormModal/utils/relations.mjs
var shouldPluralizeName = (nature) => [
  "manyToMany",
  "oneToMany",
  "manyWay"
].includes(nature) ? 2 : 1;
var shouldPluralizeTargetAttribute = (nature) => [
  "manyToMany",
  "manyToOne"
].includes(nature) ? 2 : 1;

// node_modules/@strapi/content-type-builder/dist/admin/components/FormModal/reducer.mjs
var initialState2 = {
  formErrors: {},
  modifiedData: {},
  initialData: {},
  componentToCreate: {},
  isCreatingComponentWhileAddingAField: false
};
var slice2 = createSlice({
  name: "formModal",
  initialState: initialState2,
  reducers: {
    onChange: (state, action) => {
      const { keys, value } = action.payload;
      const obj = state.modifiedData;
      const hasDefaultValue = Boolean(obj.default);
      if (hasDefaultValue && keys.length === 1 && keys.includes("type")) {
        const previousType = obj.type;
        if (previousType && [
          "date",
          "datetime",
          "time"
        ].includes(previousType)) {
          delete state.modifiedData.default;
        }
      }
      (0, import_set.default)(state, [
        "modifiedData",
        ...keys
      ], value);
    },
    onChangeRelationTarget: (state, action) => {
      const { target: { oneThatIsCreatingARelationWithAnother, selectedContentTypeFriendlyName, targetContentTypeAllowedRelations, value } } = action.payload;
      let didChangeRelationTypeBecauseOfRestrictedRelation = false;
      let changedRelationType = null;
      (0, import_set.default)(state, [
        "modifiedData",
        "target"
      ], value);
      const modifiedData = state.modifiedData;
      if (Array.isArray(targetContentTypeAllowedRelations)) {
        const currentRelationType = getRelationType(modifiedData.relation, modifiedData.targetAttribute);
        if (currentRelationType && !targetContentTypeAllowedRelations.includes(currentRelationType)) {
          const relationToSet = targetContentTypeAllowedRelations[0];
          didChangeRelationTypeBecauseOfRestrictedRelation = true;
          changedRelationType = relationToSet;
          if (relationToSet === "oneWay") {
            (0, import_set.default)(state, [
              "modifiedData",
              "relation"
            ], "oneToOne");
          } else if (relationToSet === "manyWay") {
            (0, import_set.default)(state, [
              "modifiedData",
              "relation"
            ], "oneToMany");
          } else {
            (0, import_set.default)(state, [
              "modifiedData",
              "relation"
            ], relationToSet);
          }
        }
      }
      let nameToSet;
      if (didChangeRelationTypeBecauseOfRestrictedRelation && changedRelationType) {
        nameToSet = (0, import_pluralize.default)((0, import_snakeCase.default)(nameToSlug(selectedContentTypeFriendlyName)), shouldPluralizeName(changedRelationType));
      } else {
        nameToSet = (0, import_pluralize.default)((0, import_snakeCase.default)(nameToSlug(selectedContentTypeFriendlyName)), shouldPluralizeName(modifiedData.relation));
      }
      (0, import_set.default)(state, [
        "modifiedData",
        "name"
      ], nameToSet);
      const currentTargetAttribute = state.modifiedData.targetAttribute;
      if (currentTargetAttribute === null) {
        return;
      }
      if (didChangeRelationTypeBecauseOfRestrictedRelation && changedRelationType && [
        "oneWay",
        "manyWay"
      ].includes(changedRelationType)) {
        (0, import_set.default)(state, [
          "modifiedData",
          "targetAttribute"
        ], null);
        return;
      }
      const targetAttributeToSet = (0, import_pluralize.default)((0, import_snakeCase.default)(nameToSlug(oneThatIsCreatingARelationWithAnother)), shouldPluralizeTargetAttribute(modifiedData.relation));
      (0, import_set.default)(state, [
        "modifiedData",
        "targetAttribute"
      ], targetAttributeToSet);
    },
    onChangeRelationType: (state, action) => {
      const { target: { oneThatIsCreatingARelationWithAnother, value } } = action.payload;
      const currentName = state.modifiedData.name;
      if (![
        "oneWay",
        "manyWay"
      ].includes(value)) {
        (0, import_set.default)(state, [
          "modifiedData",
          "relation"
        ], value);
        const currentTargetAttribute = state.modifiedData.targetAttribute;
        (0, import_set.default)(state, [
          "modifiedData",
          "name"
        ], (0, import_pluralize.default)((0, import_snakeCase.default)(nameToSlug(currentName)), shouldPluralizeName(value)));
        (0, import_set.default)(state, [
          "modifiedData",
          "targetAttribute"
        ], (0, import_pluralize.default)(currentTargetAttribute || (0, import_snakeCase.default)(nameToSlug(oneThatIsCreatingARelationWithAnother)), shouldPluralizeTargetAttribute(value)));
        return;
      }
      if (value === "oneWay") {
        (0, import_set.default)(state, [
          "modifiedData",
          "relation"
        ], "oneToOne");
        (0, import_set.default)(state, [
          "modifiedData",
          "targetAttribute"
        ], null);
        (0, import_set.default)(state, [
          "modifiedData",
          "name"
        ], (0, import_pluralize.default)((0, import_snakeCase.default)(currentName), 1));
        return;
      }
      (0, import_set.default)(state, [
        "modifiedData",
        "relation"
      ], "oneToMany");
      (0, import_set.default)(state, [
        "modifiedData",
        "targetAttribute"
      ], null);
      (0, import_set.default)(state, [
        "modifiedData",
        "name"
      ], (0, import_pluralize.default)((0, import_snakeCase.default)(currentName), 2));
    },
    resetProps: () => {
      return initialState2;
    },
    resetPropsAndSetFormForAddingAnExistingCompo: (state, action) => {
      const { options = {} } = action.payload;
      return {
        ...initialState2,
        modifiedData: {
          type: "component",
          repeatable: true,
          ...options
        }
      };
    },
    resetPropsAndSaveCurrentData: (state, action) => {
      const { options = {} } = action.payload;
      const componentToCreate = state.modifiedData.componentToCreate;
      const modifiedData = {
        displayName: componentToCreate.displayName,
        type: "component",
        repeatable: false,
        ...options,
        component: createComponentUid(componentToCreate.displayName, componentToCreate.category)
      };
      return {
        ...initialState2,
        componentToCreate,
        modifiedData,
        isCreatingComponentWhileAddingAField: state.modifiedData.createComponent
      };
    },
    resetPropsAndSetTheFormForAddingACompoToADz: (state) => {
      const createdDZ = state.modifiedData;
      const dataToSet = {
        ...createdDZ,
        createComponent: true,
        componentToCreate: {
          type: "component"
        }
      };
      return {
        ...initialState2,
        modifiedData: dataToSet
      };
    },
    setDataToEdit: (state, action) => {
      const { data } = action.payload;
      state.modifiedData = data;
      state.initialData = data;
    },
    setAttributeDataSchema: (state, action) => {
      const { isEditing } = action.payload;
      if (isEditing === true) {
        const { modifiedDataToSetForEditing } = action.payload;
        state.modifiedData = modifiedDataToSetForEditing;
        state.initialData = modifiedDataToSetForEditing;
        return;
      }
      const { attributeType, nameToSetForRelation, targetUid, step, options = {} } = action.payload;
      let dataToSet;
      if (attributeType === "component") {
        if (step === "1") {
          dataToSet = {
            type: "component",
            createComponent: true,
            componentToCreate: {
              type: "component"
            }
          };
        } else {
          dataToSet = {
            ...options,
            type: "component",
            repeatable: true
          };
        }
      } else if (attributeType === "dynamiczone") {
        dataToSet = {
          ...options,
          type: "dynamiczone",
          components: []
        };
      } else if (attributeType === "text") {
        dataToSet = {
          ...options,
          type: "string"
        };
      } else if (attributeType === "number" || attributeType === "date") {
        dataToSet = options;
      } else if (attributeType === "media") {
        dataToSet = {
          allowedTypes: [
            "images",
            "files",
            "videos",
            "audios"
          ],
          type: "media",
          multiple: true,
          ...options
        };
      } else if (attributeType === "enumeration") {
        dataToSet = {
          ...options,
          type: "enumeration",
          enum: []
        };
      } else if (attributeType === "relation") {
        dataToSet = {
          name: (0, import_snakeCase.default)(nameToSetForRelation),
          relation: "oneToOne",
          targetAttribute: null,
          target: targetUid,
          type: "relation"
        };
      } else {
        dataToSet = {
          ...options,
          type: attributeType,
          default: null
        };
      }
      state.modifiedData = dataToSet;
    },
    setCustomFieldDataSchema: (state, action) => {
      var _a, _b;
      const { payload } = action;
      if (payload.isEditing === true) {
        const { modifiedDataToSetForEditing } = action.payload;
        state.modifiedData = modifiedDataToSetForEditing;
        state.initialData = modifiedDataToSetForEditing;
        return;
      }
      const { customField, options = {} } = payload;
      state.modifiedData = {
        ...options,
        type: customField.type
      };
      const allOptions = [
        ...((_a = customField == null ? void 0 : customField.options) == null ? void 0 : _a.base) || [],
        ...((_b = customField == null ? void 0 : customField.options) == null ? void 0 : _b.advanced) || []
      ];
      const optionDefaults = allOptions.reduce(customFieldDefaultOptionsReducer, []);
      if (optionDefaults.length) {
        optionDefaults.forEach(({ name, defaultValue }) => (0, import_set.default)(state.modifiedData, name, defaultValue));
      }
    },
    setDynamicZoneDataSchema: (state, action) => {
      const { attributeToEdit } = action.payload;
      state.modifiedData = attributeToEdit;
      state.initialData = attributeToEdit;
    },
    setErrors: (state, action) => {
      state.formErrors = action.payload.errors;
    }
  }
});
var { actions: actions2, reducer: reducer2 } = slice2;

export {
  PERMISSIONS,
  MAX_COMPONENT_DEPTH,
  makeUnique,
  initialState,
  reducer,
  actions,
  require_slugify,
  nameToSlug,
  createUid,
  createComponentUid,
  initialState2,
  actions2,
  reducer2,
  AutoReloadOverlayBlockerProvider,
  useAutoReloadOverlayBlocker
};
//# sourceMappingURL=chunk-PPYENV7T.js.map
