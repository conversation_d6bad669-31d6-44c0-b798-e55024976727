import {
  DocumentActionButton,
  DocumentActionsMenu,
  useDocumentActions,
  useDocumentRBAC
} from "./chunk-6OM5ZNLL.js";
import {
  DocumentStatus
} from "./chunk-4LPIPK4X.js";
import {
  buildValidParams,
  createYupSchema,
  isBaseQueryError,
  useContentTypeSchema,
  useDoc,
  useDocLayout,
  useGetAllDocumentsQuery,
  useGetManyDraftRelationCountQuery,
  usePublishManyDocumentsMutation
} from "./chunk-GZO3GFLN.js";
import {
  contentManagerApi
} from "./chunk-A46TUCLT.js";
import {
  getTranslation
} from "./chunk-HIZVCZYI.js";
import {
  DescriptionComponentRenderer
} from "./chunk-EKXSMIUH.js";
import {
  ValidationError
} from "./chunk-376QHLWZ.js";
import {
  Table,
  useTable
} from "./chunk-PVCRV2LE.js";
import {
  getYupValidationErrors
} from "./chunk-L5JCPKMP.js";
import {
  useAPIErrorHandler
} from "./chunk-7LKLOY7A.js";
import {
  useQueryParams,
  useStrapiApp
} from "./chunk-ODQFI753.js";
import {
  require_lib
} from "./chunk-WH6VCVXU.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  Box,
  Button,
  Dialog,
  Flex,
  IconButton,
  LinkButton,
  Loader,
  Modal,
  RawTable,
  Tbody,
  Td,
  TooltipImpl,
  Tr,
  Typography,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  Link,
  NavLink,
  useLocation,
  useNavigate
} from "./chunk-S65ZWNEO.js";
import {
  ForwardRef$1v,
  ForwardRef$3,
  ForwardRef$3R,
  ForwardRef$43,
  ForwardRef$4D,
  ForwardRef$4F,
  ForwardRef$4v,
  ForwardRef$59
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/dist/admin/pages/ListView/components/BulkActions/Actions.mjs
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var React3 = __toESM(require_react(), 1);

// node_modules/@strapi/content-manager/dist/admin/pages/ListView/components/BulkActions/PublishAction.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var React2 = __toESM(require_react(), 1);

// node_modules/@strapi/content-manager/dist/admin/pages/ListView/components/BulkActions/ConfirmBulkActionDialog.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var ConfirmBulkActionDialog = ({ onToggleDialog, isOpen = false, dialogBody, endAction }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsx)(Dialog.Root, {
    open: isOpen,
    children: (0, import_jsx_runtime.jsxs)(Dialog.Content, {
      children: [
        (0, import_jsx_runtime.jsx)(Dialog.Header, {
          children: formatMessage({
            id: "app.components.ConfirmDialog.title",
            defaultMessage: "Confirmation"
          })
        }),
        (0, import_jsx_runtime.jsx)(Dialog.Body, {
          children: (0, import_jsx_runtime.jsxs)(Flex, {
            direction: "column",
            alignItems: "stretch",
            gap: 2,
            children: [
              (0, import_jsx_runtime.jsx)(Flex, {
                justifyContent: "center",
                children: (0, import_jsx_runtime.jsx)(ForwardRef$3, {
                  width: "24px",
                  height: "24px",
                  fill: "danger600"
                })
              }),
              dialogBody
            ]
          })
        }),
        (0, import_jsx_runtime.jsxs)(Dialog.Footer, {
          children: [
            (0, import_jsx_runtime.jsx)(Dialog.Cancel, {
              children: (0, import_jsx_runtime.jsx)(Button, {
                width: "50%",
                onClick: onToggleDialog,
                variant: "tertiary",
                children: formatMessage({
                  id: "app.components.Button.cancel",
                  defaultMessage: "Cancel"
                })
              })
            }),
            endAction
          ]
        })
      ]
    })
  });
};
var BoldChunk = (chunks) => (0, import_jsx_runtime.jsx)(Typography, {
  fontWeight: "bold",
  children: chunks
});
var ConfirmDialogPublishAll = ({ isOpen, onToggleDialog, isConfirmButtonLoading = false, onConfirm }) => {
  var _a, _b;
  const { formatMessage } = useIntl();
  const selectedEntries = useTable("ConfirmDialogPublishAll", (state) => state.selectedRows);
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler(getTranslation);
  const { model, schema } = useDoc();
  const [{ query }] = useQueryParams();
  const enableDraftRelationsCount = false;
  const { data: countDraftRelations = 0, isLoading, error } = useGetManyDraftRelationCountQuery({
    model,
    documentIds: selectedEntries.map((entry) => entry.documentId),
    locale: (_b = (_a = query == null ? void 0 : query.plugins) == null ? void 0 : _a.i18n) == null ? void 0 : _b.locale
  }, {
    skip: !enableDraftRelationsCount
  });
  React.useEffect(() => {
    if (error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    }
  }, [
    error,
    formatAPIError,
    toggleNotification
  ]);
  if (error) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)(ConfirmBulkActionDialog, {
    isOpen: isOpen && !isLoading,
    onToggleDialog,
    dialogBody: (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, {
      children: [
        (0, import_jsx_runtime.jsxs)(Typography, {
          id: "confirm-description",
          textAlign: "center",
          children: [
            countDraftRelations > 0 && formatMessage({
              id: getTranslation(`popUpwarning.warning.bulk-has-draft-relations.message`),
              defaultMessage: "<b>{count} {count, plural, one { relation } other { relations } } out of {entities} { entities, plural, one { entry } other { entries } } {count, plural, one { is } other { are } }</b> not published yet and might lead to unexpected behavior. "
            }, {
              b: BoldChunk,
              count: countDraftRelations,
              entities: selectedEntries.length
            }),
            formatMessage({
              id: getTranslation("popUpWarning.bodyMessage.contentType.publish.all"),
              defaultMessage: "Are you sure you want to publish these entries?"
            })
          ]
        }),
        (schema == null ? void 0 : schema.pluginOptions) && "i18n" in schema.pluginOptions && (schema == null ? void 0 : schema.pluginOptions.i18n) && (0, import_jsx_runtime.jsx)(Typography, {
          textColor: "danger500",
          textAlign: "center",
          children: formatMessage({
            id: getTranslation("Settings.list.actions.publishAdditionalInfos"),
            defaultMessage: "This will publish the active locale versions <em>(from Internationalization)</em>"
          }, {
            em: Emphasis
          })
        })
      ]
    }),
    endAction: (0, import_jsx_runtime.jsx)(Button, {
      width: "50%",
      onClick: onConfirm,
      variant: "secondary",
      startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4F, {}),
      loading: isConfirmButtonLoading,
      children: formatMessage({
        id: "app.utils.publish",
        defaultMessage: "Publish"
      })
    })
  });
};

// node_modules/@strapi/content-manager/dist/admin/pages/ListView/components/BulkActions/PublishAction.mjs
var TypographyMaxWidth = dt(Typography)`
  max-width: 300px;
`;
var TableComponent = dt(RawTable)`
  width: 100%;
  table-layout: fixed;
  td:first-child {
    border-right: 1px solid ${({ theme }) => theme.colors.neutral150};
  }
  td:first-of-type {
    padding: ${({ theme }) => theme.spaces[4]};
  }
`;
var formatErrorMessages = (errors, parentKey, formatMessage) => {
  const messages = [];
  Object.entries(errors).forEach(([key, value]) => {
    const currentKey = parentKey ? `${parentKey}.${key}` : key;
    if (typeof value === "object" && value !== null && !Array.isArray(value)) {
      if ("id" in value && "defaultMessage" in value) {
        messages.push(formatMessage({
          id: `${value.id}.withField`,
          defaultMessage: value.defaultMessage
        }, {
          field: currentKey
        }));
      } else {
        messages.push(...formatErrorMessages(
          // @ts-expect-error TODO: check why value is not compatible with FormErrors
          value,
          currentKey,
          formatMessage
        ));
      }
    } else {
      messages.push(formatMessage({
        id: `${value}.withField`,
        defaultMessage: value
      }, {
        field: currentKey
      }));
    }
  });
  return messages;
};
var EntryValidationText = ({ validationErrors, status }) => {
  const { formatMessage } = useIntl();
  if (validationErrors) {
    const validationErrorsMessages = formatErrorMessages(validationErrors, "", formatMessage).join(" ");
    return (0, import_jsx_runtime2.jsxs)(Flex, {
      gap: 2,
      children: [
        (0, import_jsx_runtime2.jsx)(ForwardRef$43, {
          fill: "danger600"
        }),
        (0, import_jsx_runtime2.jsx)(TooltipImpl, {
          description: validationErrorsMessages,
          children: (0, import_jsx_runtime2.jsx)(TypographyMaxWidth, {
            textColor: "danger600",
            variant: "omega",
            fontWeight: "semiBold",
            ellipsis: true,
            children: validationErrorsMessages
          })
        })
      ]
    });
  }
  if (status === "published") {
    return (0, import_jsx_runtime2.jsxs)(Flex, {
      gap: 2,
      children: [
        (0, import_jsx_runtime2.jsx)(ForwardRef$4D, {
          fill: "success600"
        }),
        (0, import_jsx_runtime2.jsx)(Typography, {
          textColor: "success600",
          fontWeight: "bold",
          children: formatMessage({
            id: "content-manager.bulk-publish.already-published",
            defaultMessage: "Already Published"
          })
        })
      ]
    });
  }
  if (status === "modified") {
    return (0, import_jsx_runtime2.jsxs)(Flex, {
      gap: 2,
      children: [
        (0, import_jsx_runtime2.jsx)(ForwardRef$59, {
          fill: "alternative600"
        }),
        (0, import_jsx_runtime2.jsx)(Typography, {
          children: formatMessage({
            id: "content-manager.bulk-publish.modified",
            defaultMessage: "Ready to publish changes"
          })
        })
      ]
    });
  }
  return (0, import_jsx_runtime2.jsxs)(Flex, {
    gap: 2,
    children: [
      (0, import_jsx_runtime2.jsx)(ForwardRef$4D, {
        fill: "success600"
      }),
      (0, import_jsx_runtime2.jsx)(Typography, {
        children: formatMessage({
          id: "app.utils.ready-to-publish",
          defaultMessage: "Ready to publish"
        })
      })
    ]
  });
};
var TABLE_HEADERS = [
  {
    name: "id",
    label: "id"
  },
  {
    name: "name",
    label: "name"
  },
  {
    name: "status",
    label: "status"
  },
  {
    name: "publicationStatus",
    label: "Publication status"
  }
];
var SelectedEntriesTableContent = ({ isPublishing, rowsToDisplay = [], entriesToPublish = [], validationErrors = {} }) => {
  const { pathname } = useLocation();
  const { formatMessage } = useIntl();
  const { list: { settings: { mainField } } } = useDocLayout();
  const shouldDisplayMainField = mainField != null && mainField !== "id";
  return (0, import_jsx_runtime2.jsxs)(Table.Content, {
    children: [
      (0, import_jsx_runtime2.jsxs)(Table.Head, {
        children: [
          (0, import_jsx_runtime2.jsx)(Table.HeaderCheckboxCell, {}),
          TABLE_HEADERS.filter((head) => head.name !== "name" || shouldDisplayMainField).map((head) => (0, import_jsx_runtime2.jsx)(Table.HeaderCell, {
            ...head
          }, head.name))
        ]
      }),
      (0, import_jsx_runtime2.jsx)(Table.Loading, {}),
      (0, import_jsx_runtime2.jsx)(Table.Body, {
        children: rowsToDisplay.map((row) => (0, import_jsx_runtime2.jsxs)(Table.Row, {
          children: [
            (0, import_jsx_runtime2.jsx)(Table.CheckboxCell, {
              id: row.id
            }),
            (0, import_jsx_runtime2.jsx)(Table.Cell, {
              children: (0, import_jsx_runtime2.jsx)(Typography, {
                children: row.id
              })
            }),
            shouldDisplayMainField && (0, import_jsx_runtime2.jsx)(Table.Cell, {
              children: (0, import_jsx_runtime2.jsx)(Typography, {
                children: row[mainField]
              })
            }),
            (0, import_jsx_runtime2.jsx)(Table.Cell, {
              children: (0, import_jsx_runtime2.jsx)(DocumentStatus, {
                status: row.status,
                maxWidth: "min-content"
              })
            }),
            (0, import_jsx_runtime2.jsx)(Table.Cell, {
              children: isPublishing && entriesToPublish.includes(row.documentId) ? (0, import_jsx_runtime2.jsxs)(Flex, {
                gap: 2,
                children: [
                  (0, import_jsx_runtime2.jsx)(Typography, {
                    children: formatMessage({
                      id: "content-manager.success.record.publishing",
                      defaultMessage: "Publishing..."
                    })
                  }),
                  (0, import_jsx_runtime2.jsx)(Loader, {
                    small: true
                  })
                ]
              }) : (0, import_jsx_runtime2.jsx)(EntryValidationText, {
                validationErrors: validationErrors[row.documentId],
                status: row.status
              })
            }),
            (0, import_jsx_runtime2.jsx)(Table.Cell, {
              children: (0, import_jsx_runtime2.jsx)(Flex, {
                children: (0, import_jsx_runtime2.jsx)(IconButton, {
                  tag: Link,
                  to: {
                    pathname: `${pathname}/${row.documentId}`,
                    search: row.locale && `?plugins[i18n][locale]=${row.locale}`
                  },
                  state: {
                    from: pathname
                  },
                  label: formatMessage({
                    id: "content-manager.bulk-publish.edit",
                    defaultMessage: "Edit"
                  }),
                  target: "_blank",
                  marginLeft: "auto",
                  variant: "ghost",
                  children: (0, import_jsx_runtime2.jsx)(ForwardRef$1v, {
                    width: "1.6rem",
                    height: "1.6rem"
                  })
                })
              })
            })
          ]
        }, row.id))
      })
    ]
  });
};
var PublicationStatusSummary = ({ count, icon, message }) => {
  return (0, import_jsx_runtime2.jsxs)(Flex, {
    justifyContent: "space-between",
    flex: 1,
    gap: 3,
    children: [
      (0, import_jsx_runtime2.jsxs)(Flex, {
        gap: 2,
        children: [
          icon,
          (0, import_jsx_runtime2.jsx)(Typography, {
            children: message
          })
        ]
      }),
      (0, import_jsx_runtime2.jsx)(Typography, {
        fontWeight: "bold",
        children: count
      })
    ]
  });
};
var PublicationStatusGrid = ({ entriesReadyToPublishCount, entriesPublishedCount, entriesModifiedCount, entriesWithErrorsCount }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime2.jsx)(Box, {
    hasRadius: true,
    borderColor: "neutral150",
    children: (0, import_jsx_runtime2.jsx)(TableComponent, {
      colCount: 2,
      rowCount: 2,
      children: (0, import_jsx_runtime2.jsxs)(Tbody, {
        children: [
          (0, import_jsx_runtime2.jsxs)(Tr, {
            children: [
              (0, import_jsx_runtime2.jsx)(Td, {
                children: (0, import_jsx_runtime2.jsx)(PublicationStatusSummary, {
                  count: entriesReadyToPublishCount,
                  icon: (0, import_jsx_runtime2.jsx)(ForwardRef$4D, {
                    fill: "success600"
                  }),
                  message: formatMessage({
                    id: "app.utils.ready-to-publish",
                    defaultMessage: "Ready to publish"
                  })
                })
              }),
              (0, import_jsx_runtime2.jsx)(Td, {
                children: (0, import_jsx_runtime2.jsx)(PublicationStatusSummary, {
                  count: entriesPublishedCount,
                  icon: (0, import_jsx_runtime2.jsx)(ForwardRef$4D, {
                    fill: "success600"
                  }),
                  message: formatMessage({
                    id: "app.utils.already-published",
                    defaultMessage: "Already published"
                  })
                })
              })
            ]
          }),
          (0, import_jsx_runtime2.jsxs)(Tr, {
            children: [
              (0, import_jsx_runtime2.jsx)(Td, {
                children: (0, import_jsx_runtime2.jsx)(PublicationStatusSummary, {
                  count: entriesModifiedCount,
                  icon: (0, import_jsx_runtime2.jsx)(ForwardRef$59, {
                    fill: "alternative600"
                  }),
                  message: formatMessage({
                    id: "content-manager.bulk-publish.modified",
                    defaultMessage: "Ready to publish changes"
                  })
                })
              }),
              (0, import_jsx_runtime2.jsx)(Td, {
                children: (0, import_jsx_runtime2.jsx)(PublicationStatusSummary, {
                  count: entriesWithErrorsCount,
                  icon: (0, import_jsx_runtime2.jsx)(ForwardRef$43, {
                    fill: "danger600"
                  }),
                  message: formatMessage({
                    id: "content-manager.bulk-publish.waiting-for-action",
                    defaultMessage: "Waiting for action"
                  })
                })
              })
            ]
          })
        ]
      })
    })
  });
};
var SelectedEntriesModalContent = ({ listViewSelectedEntries, toggleModal, setListViewSelectedDocuments, model }) => {
  var _a, _b;
  const { formatMessage } = useIntl();
  const { schema, components } = useContentTypeSchema(model);
  const documentIds = listViewSelectedEntries.map(({ documentId }) => documentId);
  const [{ query }] = useQueryParams();
  const params = React2.useMemo(() => buildValidParams(query), [
    query
  ]);
  const { data, isLoading, isFetching, refetch } = useGetAllDocumentsQuery({
    model,
    params: {
      page: "1",
      pageSize: documentIds.length.toString(),
      sort: query.sort,
      filters: {
        documentId: {
          $in: documentIds
        }
      },
      locale: (_b = (_a = query.plugins) == null ? void 0 : _a.i18n) == null ? void 0 : _b.locale
    }
  }, {
    selectFromResult: ({ data: data2, ...restRes }) => ({
      data: (data2 == null ? void 0 : data2.results) ?? [],
      ...restRes
    })
  });
  const { rows, validationErrors } = React2.useMemo(() => {
    if (data.length > 0 && schema) {
      const validate = createYupSchema(
        schema.attributes,
        components,
        // Since this is the "Publish" action, the validation
        // schema must enforce the rules for published entities
        {
          status: "published"
        }
      );
      const validationErrors2 = {};
      const rows2 = data.map((entry) => {
        try {
          validate.validateSync(entry, {
            abortEarly: false
          });
          return entry;
        } catch (e) {
          if (e instanceof ValidationError) {
            validationErrors2[entry.documentId] = getYupValidationErrors(e);
          }
          return entry;
        }
      });
      return {
        rows: rows2,
        validationErrors: validationErrors2
      };
    }
    return {
      rows: [],
      validationErrors: {}
    };
  }, [
    components,
    data,
    schema
  ]);
  const [isDialogOpen, setIsDialogOpen] = React2.useState(false);
  const { publishMany: bulkPublishAction, isLoading: isPublishing } = useDocumentActions();
  const [, { isLoading: isSubmittingForm }] = usePublishManyDocumentsMutation();
  const selectedRows = useTable("publishAction", (state) => state.selectedRows);
  const selectedEntries = rows.filter((entry) => selectedRows.some((selectedEntry) => selectedEntry.documentId === entry.documentId));
  const entriesToPublish = selectedEntries.filter((entry) => !validationErrors[entry.documentId]).map((entry) => entry.documentId);
  const selectedEntriesWithErrorsCount = selectedEntries.filter(({ documentId }) => validationErrors[documentId]).length;
  const selectedEntriesPublishedCount = selectedEntries.filter(({ status }) => status === "published").length;
  const selectedEntriesModifiedCount = selectedEntries.filter(({ status, documentId }) => status === "modified" && !validationErrors[documentId]).length;
  const selectedEntriesWithNoErrorsCount = selectedEntries.length - selectedEntriesWithErrorsCount - selectedEntriesPublishedCount;
  const toggleDialog = () => setIsDialogOpen((prev) => !prev);
  const handleConfirmBulkPublish = async () => {
    toggleDialog();
    const res = await bulkPublishAction({
      model,
      documentIds: entriesToPublish,
      params
    });
    if (!("error" in res)) {
      const unpublishedEntries = rows.filter((row) => {
        return !entriesToPublish.includes(row.documentId);
      });
      setListViewSelectedDocuments(unpublishedEntries);
    }
  };
  return (0, import_jsx_runtime2.jsxs)(import_jsx_runtime2.Fragment, {
    children: [
      (0, import_jsx_runtime2.jsxs)(Modal.Body, {
        children: [
          (0, import_jsx_runtime2.jsx)(PublicationStatusGrid, {
            entriesReadyToPublishCount: selectedEntriesWithNoErrorsCount - selectedEntriesModifiedCount,
            entriesPublishedCount: selectedEntriesPublishedCount,
            entriesModifiedCount: selectedEntriesModifiedCount,
            entriesWithErrorsCount: selectedEntriesWithErrorsCount
          }),
          (0, import_jsx_runtime2.jsx)(Box, {
            marginTop: 7,
            children: (0, import_jsx_runtime2.jsx)(SelectedEntriesTableContent, {
              isPublishing: isSubmittingForm,
              rowsToDisplay: rows,
              entriesToPublish,
              validationErrors
            })
          })
        ]
      }),
      (0, import_jsx_runtime2.jsxs)(Modal.Footer, {
        children: [
          (0, import_jsx_runtime2.jsx)(Button, {
            onClick: toggleModal,
            variant: "tertiary",
            children: formatMessage({
              id: "app.components.Button.cancel",
              defaultMessage: "Cancel"
            })
          }),
          (0, import_jsx_runtime2.jsxs)(Flex, {
            gap: 2,
            children: [
              (0, import_jsx_runtime2.jsx)(Button, {
                onClick: refetch,
                variant: "tertiary",
                loading: isFetching,
                children: formatMessage({
                  id: "app.utils.refresh",
                  defaultMessage: "Refresh"
                })
              }),
              (0, import_jsx_runtime2.jsx)(Button, {
                onClick: toggleDialog,
                disabled: selectedEntries.length === 0 || selectedEntries.length === selectedEntriesWithErrorsCount || selectedEntriesPublishedCount === selectedEntries.length || isLoading,
                loading: isPublishing || isSubmittingForm,
                children: formatMessage({
                  id: "app.utils.publish",
                  defaultMessage: "Publish"
                })
              })
            ]
          })
        ]
      }),
      (0, import_jsx_runtime2.jsx)(ConfirmDialogPublishAll, {
        isOpen: isDialogOpen,
        onToggleDialog: toggleDialog,
        isConfirmButtonLoading: isPublishing || isSubmittingForm,
        onConfirm: handleConfirmBulkPublish
      })
    ]
  });
};
var PublishAction = ({ documents, model }) => {
  const { formatMessage } = useIntl();
  const hasPublishPermission = useDocumentRBAC("unpublishAction", (state) => state.canPublish);
  const showPublishButton = hasPublishPermission && documents.some(({ status }) => status !== "published");
  const setListViewSelectedDocuments = useTable("publishAction", (state) => state.selectRow);
  const refetchList = () => {
    contentManagerApi.util.invalidateTags([
      {
        type: "Document",
        id: `${model}_LIST`
      }
    ]);
  };
  if (!showPublishButton) return null;
  return {
    actionType: "publish",
    variant: "tertiary",
    label: formatMessage({
      id: "app.utils.publish",
      defaultMessage: "Publish"
    }),
    dialog: {
      type: "modal",
      title: formatMessage({
        id: getTranslation("containers.ListPage.selectedEntriesModal.title"),
        defaultMessage: "Publish entries"
      }),
      content: ({ onClose }) => {
        return (0, import_jsx_runtime2.jsx)(Table.Root, {
          rows: documents,
          defaultSelectedRows: documents,
          headers: TABLE_HEADERS,
          children: (0, import_jsx_runtime2.jsx)(SelectedEntriesModalContent, {
            listViewSelectedEntries: documents,
            toggleModal: () => {
              onClose();
              refetchList();
            },
            setListViewSelectedDocuments,
            model
          })
        });
      },
      onClose: () => {
        refetchList();
      }
    }
  };
};

// node_modules/@strapi/content-manager/dist/admin/pages/ListView/components/BulkActions/Actions.mjs
var BulkActionsRenderer = () => {
  const plugins = useStrapiApp("BulkActionsRenderer", (state) => state.plugins);
  const { model, collectionType } = useDoc();
  const { selectedRows } = useTable("BulkActionsRenderer", (state) => state);
  return (0, import_jsx_runtime3.jsx)(Flex, {
    gap: 2,
    children: (0, import_jsx_runtime3.jsx)(DescriptionComponentRenderer, {
      props: {
        model,
        collectionType,
        documents: selectedRows
      },
      descriptions: plugins["content-manager"].apis.getBulkActions(),
      children: (actions) => actions.map((action) => (0, import_jsx_runtime3.jsx)(DocumentActionButton, {
        ...action
      }, action.id))
    })
  });
};
var DeleteAction = ({ documents, model }) => {
  var _a;
  const { formatMessage } = useIntl();
  const { schema: contentType } = useDoc();
  const selectRow = useTable("DeleteAction", (state) => state.selectRow);
  const hasI18nEnabled = Boolean((_a = contentType == null ? void 0 : contentType.pluginOptions) == null ? void 0 : _a.i18n);
  const [{ query }] = useQueryParams();
  const params = React3.useMemo(() => buildValidParams(query), [
    query
  ]);
  const hasDeletePermission = useDocumentRBAC("deleteAction", (state) => state.canDelete);
  const { deleteMany: bulkDeleteAction, isLoading } = useDocumentActions();
  const documentIds = documents.map(({ documentId }) => documentId);
  const handleConfirmBulkDelete = async () => {
    const res = await bulkDeleteAction({
      documentIds,
      model,
      params
    });
    if (!("error" in res)) {
      selectRow([]);
    }
  };
  if (!hasDeletePermission) return null;
  return {
    variant: "danger-light",
    label: formatMessage({
      id: "global.delete",
      defaultMessage: "Delete"
    }),
    dialog: {
      type: "dialog",
      title: formatMessage({
        id: "app.components.ConfirmDialog.title",
        defaultMessage: "Confirmation"
      }),
      loading: isLoading,
      content: (0, import_jsx_runtime3.jsxs)(Flex, {
        direction: "column",
        alignItems: "stretch",
        gap: 2,
        children: [
          (0, import_jsx_runtime3.jsx)(Flex, {
            justifyContent: "center",
            children: (0, import_jsx_runtime3.jsx)(ForwardRef$3, {
              width: "24px",
              height: "24px",
              fill: "danger600"
            })
          }),
          (0, import_jsx_runtime3.jsx)(Typography, {
            id: "confirm-description",
            textAlign: "center",
            children: formatMessage({
              id: "popUpWarning.bodyMessage.contentType.delete.all",
              defaultMessage: "Are you sure you want to delete these entries?"
            })
          }),
          hasI18nEnabled && (0, import_jsx_runtime3.jsx)(Box, {
            textAlign: "center",
            padding: 3,
            children: (0, import_jsx_runtime3.jsx)(Typography, {
              textColor: "danger500",
              children: formatMessage({
                id: getTranslation("Settings.list.actions.deleteAdditionalInfos"),
                defaultMessage: "This will delete the active locale versions <em>(from Internationalization)</em>"
              }, {
                em: Emphasis
              })
            })
          })
        ]
      }),
      onConfirm: handleConfirmBulkDelete
    }
  };
};
DeleteAction.type = "delete";
var UnpublishAction = ({ documents, model }) => {
  var _a, _b;
  const { formatMessage } = useIntl();
  const { schema } = useDoc();
  const selectRow = useTable("UnpublishAction", (state) => state.selectRow);
  const hasPublishPermission = useDocumentRBAC("unpublishAction", (state) => state.canPublish);
  const hasI18nEnabled = Boolean((_a = schema == null ? void 0 : schema.pluginOptions) == null ? void 0 : _a.i18n);
  const hasDraftAndPublishEnabled = Boolean((_b = schema == null ? void 0 : schema.options) == null ? void 0 : _b.draftAndPublish);
  const { unpublishMany: bulkUnpublishAction, isLoading } = useDocumentActions();
  const documentIds = documents.map(({ documentId }) => documentId);
  const [{ query }] = useQueryParams();
  const params = React3.useMemo(() => buildValidParams(query), [
    query
  ]);
  const handleConfirmBulkUnpublish = async () => {
    const data = await bulkUnpublishAction({
      documentIds,
      model,
      params
    });
    if (!("error" in data)) {
      selectRow([]);
    }
  };
  const showUnpublishButton = hasDraftAndPublishEnabled && hasPublishPermission && documents.some((entry) => entry.status === "published" || entry.status === "modified");
  if (!showUnpublishButton) return null;
  return {
    variant: "tertiary",
    label: formatMessage({
      id: "app.utils.unpublish",
      defaultMessage: "Unpublish"
    }),
    dialog: {
      type: "dialog",
      title: formatMessage({
        id: "app.components.ConfirmDialog.title",
        defaultMessage: "Confirmation"
      }),
      loading: isLoading,
      content: (0, import_jsx_runtime3.jsxs)(Flex, {
        direction: "column",
        alignItems: "stretch",
        gap: 2,
        children: [
          (0, import_jsx_runtime3.jsx)(Flex, {
            justifyContent: "center",
            children: (0, import_jsx_runtime3.jsx)(ForwardRef$3, {
              width: "24px",
              height: "24px",
              fill: "danger600"
            })
          }),
          (0, import_jsx_runtime3.jsx)(Typography, {
            id: "confirm-description",
            textAlign: "center",
            children: formatMessage({
              id: "popUpWarning.bodyMessage.contentType.unpublish.all",
              defaultMessage: "Are you sure you want to unpublish these entries?"
            })
          }),
          hasI18nEnabled && (0, import_jsx_runtime3.jsx)(Box, {
            textAlign: "center",
            padding: 3,
            children: (0, import_jsx_runtime3.jsx)(Typography, {
              textColor: "danger500",
              children: formatMessage({
                id: getTranslation("Settings.list.actions.unpublishAdditionalInfos"),
                defaultMessage: "This will unpublish the active locale versions <em>(from Internationalization)</em>"
              }, {
                em: Emphasis
              })
            })
          })
        ]
      }),
      confirmButton: formatMessage({
        id: "app.utils.unpublish",
        defaultMessage: "Unpublish"
      }),
      onConfirm: handleConfirmBulkUnpublish
    }
  };
};
UnpublishAction.type = "unpublish";
var Emphasis = (chunks) => (0, import_jsx_runtime3.jsx)(Typography, {
  fontWeight: "semiBold",
  textColor: "danger500",
  children: chunks
});
var DEFAULT_BULK_ACTIONS = [
  PublishAction,
  UnpublishAction,
  DeleteAction
];

// node_modules/@strapi/content-manager/dist/admin/pages/ListView/components/TableActions.mjs
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var React4 = __toESM(require_react(), 1);
var import_qs = __toESM(require_lib(), 1);

// node_modules/@strapi/content-manager/dist/admin/pages/ListView/components/AutoCloneFailureModal.mjs
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var AutoCloneFailureModalBody = ({ prohibitedFields }) => {
  const { formatMessage } = useIntl();
  const getDefaultErrorMessage = (reason) => {
    switch (reason) {
      case "relation":
        return "Duplicating the relation could remove it from the original entry.";
      case "unique":
        return "Identical values in a unique field are not allowed";
      default:
        return reason;
    }
  };
  return (0, import_jsx_runtime4.jsxs)(import_jsx_runtime4.Fragment, {
    children: [
      (0, import_jsx_runtime4.jsx)(Typography, {
        variant: "beta",
        children: formatMessage({
          id: getTranslation("containers.list.autoCloneModal.title"),
          defaultMessage: "This entry can't be duplicated directly."
        })
      }),
      (0, import_jsx_runtime4.jsx)(Box, {
        marginTop: 2,
        children: (0, import_jsx_runtime4.jsx)(Typography, {
          textColor: "neutral600",
          children: formatMessage({
            id: getTranslation("containers.list.autoCloneModal.description"),
            defaultMessage: "A new entry will be created with the same content, but you'll have to change the following fields to save it."
          })
        })
      }),
      (0, import_jsx_runtime4.jsx)(Flex, {
        marginTop: 6,
        gap: 2,
        direction: "column",
        alignItems: "stretch",
        children: prohibitedFields.map(([fieldPath, reason]) => (0, import_jsx_runtime4.jsxs)(Flex, {
          direction: "column",
          gap: 2,
          alignItems: "flex-start",
          borderColor: "neutral200",
          hasRadius: true,
          padding: 6,
          children: [
            (0, import_jsx_runtime4.jsx)(Flex, {
              direction: "row",
              tag: "ol",
              children: fieldPath.map((pathSegment, index) => (0, import_jsx_runtime4.jsxs)(Typography, {
                fontWeight: "semiBold",
                tag: "li",
                children: [
                  pathSegment,
                  index !== fieldPath.length - 1 && (0, import_jsx_runtime4.jsx)(ForwardRef$4v, {
                    fill: "neutral500",
                    height: "0.8rem",
                    width: "0.8rem",
                    style: {
                      margin: "0 0.8rem"
                    }
                  })
                ]
              }, index))
            }),
            (0, import_jsx_runtime4.jsx)(Typography, {
              tag: "p",
              textColor: "neutral600",
              children: formatMessage({
                id: getTranslation(`containers.list.autoCloneModal.error.${reason}`),
                defaultMessage: getDefaultErrorMessage(reason)
              })
            })
          ]
        }, fieldPath.join()))
      })
    ]
  });
};

// node_modules/@strapi/content-manager/dist/admin/pages/ListView/components/TableActions.mjs
var TableActions = ({ document }) => {
  const { formatMessage } = useIntl();
  const { model, collectionType } = useDoc();
  const plugins = useStrapiApp("TableActions", (state) => state.plugins);
  const props = {
    activeTab: null,
    model,
    documentId: document.documentId,
    collectionType,
    document
  };
  return (0, import_jsx_runtime5.jsx)(DescriptionComponentRenderer, {
    props,
    descriptions: plugins["content-manager"].apis.getDocumentActions("table-row").filter((action) => action.name !== "PublishAction"),
    children: (actions) => {
      const tableRowActions = actions.filter((action) => {
        const positions = Array.isArray(action.position) ? action.position : [
          action.position
        ];
        return positions.includes("table-row");
      });
      return (0, import_jsx_runtime5.jsx)(DocumentActionsMenu, {
        actions: tableRowActions,
        label: formatMessage({
          id: "content-manager.containers.list.table.row-actions",
          defaultMessage: "Row actions"
        }),
        variant: "ghost"
      });
    }
  });
};
var EditAction = ({ documentId }) => {
  const navigate = useNavigate();
  const { formatMessage } = useIntl();
  const { canRead } = useDocumentRBAC("EditAction", ({ canRead: canRead2 }) => ({
    canRead: canRead2
  }));
  const { toggleNotification } = useNotification();
  const [{ query }] = useQueryParams();
  return {
    disabled: !canRead,
    icon: (0, import_jsx_runtime5.jsx)(StyledPencil, {}),
    label: formatMessage({
      id: "content-manager.actions.edit.label",
      defaultMessage: "Edit"
    }),
    position: "table-row",
    onClick: async () => {
      if (!documentId) {
        console.error("You're trying to edit a document without an id, this is likely a bug with Strapi. Please open an issue.");
        toggleNotification({
          message: formatMessage({
            id: "content-manager.actions.edit.error",
            defaultMessage: "An error occurred while trying to edit the document."
          }),
          type: "danger"
        });
        return;
      }
      navigate({
        pathname: documentId,
        search: (0, import_qs.stringify)({
          plugins: query.plugins
        })
      });
    }
  };
};
EditAction.type = "edit";
EditAction.position = "table-row";
var StyledPencil = dt(ForwardRef$1v)`
  path {
    fill: currentColor;
  }
`;
var CloneAction = ({ model, documentId }) => {
  const navigate = useNavigate();
  const { formatMessage } = useIntl();
  const { canCreate } = useDocumentRBAC("CloneAction", ({ canCreate: canCreate2 }) => ({
    canCreate: canCreate2
  }));
  const { toggleNotification } = useNotification();
  const { autoClone } = useDocumentActions();
  const [prohibitedFields, setProhibitedFields] = React4.useState([]);
  const [{ query }] = useQueryParams();
  return {
    disabled: !canCreate,
    icon: (0, import_jsx_runtime5.jsx)(StyledDuplicate, {}),
    label: formatMessage({
      id: "content-manager.actions.clone.label",
      defaultMessage: "Duplicate"
    }),
    position: "table-row",
    onClick: async () => {
      var _a, _b;
      if (!documentId) {
        console.error("You're trying to clone a document in the table without an id, this is likely a bug with Strapi. Please open an issue.");
        toggleNotification({
          message: formatMessage({
            id: "content-manager.actions.clone.error",
            defaultMessage: "An error occurred while trying to clone the document."
          }),
          type: "danger"
        });
        return;
      }
      const res = await autoClone({
        model,
        sourceId: documentId,
        locale: (_b = (_a = query.plugins) == null ? void 0 : _a.i18n) == null ? void 0 : _b.locale
      });
      if ("data" in res) {
        navigate({
          pathname: res.data.documentId,
          search: (0, import_qs.stringify)({
            plugins: query.plugins
          })
        });
        return true;
      }
      if (isBaseQueryError(res.error) && res.error.details && typeof res.error.details === "object" && "prohibitedFields" in res.error.details && Array.isArray(res.error.details.prohibitedFields)) {
        const prohibitedFields2 = res.error.details.prohibitedFields;
        setProhibitedFields(prohibitedFields2);
      }
    },
    dialog: {
      type: "modal",
      title: formatMessage({
        id: "content-manager.containers.list.autoCloneModal.header",
        defaultMessage: "Duplicate"
      }),
      content: (0, import_jsx_runtime5.jsx)(AutoCloneFailureModalBody, {
        prohibitedFields
      }),
      footer: ({ onClose }) => {
        return (0, import_jsx_runtime5.jsxs)(Modal.Footer, {
          children: [
            (0, import_jsx_runtime5.jsx)(Button, {
              onClick: onClose,
              variant: "tertiary",
              children: formatMessage({
                id: "cancel",
                defaultMessage: "Cancel"
              })
            }),
            (0, import_jsx_runtime5.jsx)(LinkButton, {
              tag: NavLink,
              to: {
                pathname: `clone/${documentId}`,
                search: (0, import_qs.stringify)({
                  plugins: query.plugins
                })
              },
              children: formatMessage({
                id: "content-manager.containers.list.autoCloneModal.create",
                defaultMessage: "Create"
              })
            })
          ]
        });
      }
    }
  };
};
CloneAction.type = "clone";
CloneAction.position = "table-row";
var StyledDuplicate = dt(ForwardRef$3R)`
  path {
    fill: currentColor;
  }
`;
var DEFAULT_TABLE_ROW_ACTIONS = [
  EditAction,
  CloneAction
];

export {
  BulkActionsRenderer,
  DEFAULT_BULK_ACTIONS,
  TableActions,
  DEFAULT_TABLE_ROW_ACTIONS
};
//# sourceMappingURL=chunk-6NZ2ITH7.js.map
