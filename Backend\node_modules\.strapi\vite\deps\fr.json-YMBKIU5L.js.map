{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/admin/translations/fr.json.mjs"], "sourcesContent": ["var from = \"de\";\nvar fr = {\n    \"attribute.boolean\": \"Booléen\",\n    \"attribute.date\": \"Date\",\n    \"attribute.email\": \"Email\",\n    \"attribute.enumeration\": \"Énumération\",\n    \"attribute.json\": \"JSON\",\n    \"attribute.media\": \"<PERSON>édia\",\n    \"attribute.password\": \"Mot de passe\",\n    \"attribute.relation\": \"Relation\",\n    \"attribute.richtext\": \"Texte enrichi\",\n    \"attribute.text\": \"Texte\",\n    \"button.attributes.add.another\": \"Ajouter un autre champ\",\n    \"button.component.create\": \"Créer un composant\",\n    \"button.model.create\": \"Créer un type de collection\",\n    \"button.single-types.create\": \"Créer un single type\",\n    \"contentType.kind.change.warning\": \"Vous venez de changer le type de ce modèle: L'API va redémarrer (Les routes, controllers, et les services seront écrasés).\",\n    \"form.attribute.item.customColumnName\": \"Nom de colonne personalisée\",\n    \"form.attribute.item.customColumnName.description\": \"Pratique pour renommer la colonne de la db dans un format plus comprehensible pour les responses de l'API\",\n    \"form.attribute.item.defineRelation.fieldName\": \"Nom du Champ\",\n    \"form.attribute.item.enumeration.graphql\": \"Surchage du nom pour GraphQL\",\n    \"form.attribute.item.enumeration.graphql.description\": \"Vous permet de remplacer le nom généré par défaut pour GraphQL\",\n    \"form.attribute.item.enumeration.placeholder\": \"Ex:\\nmatin\\nmidi\\nsoir\",\n    \"form.attribute.item.enumeration.rules\": \"Valeurs (les séparer par une nouvelle ligne)\",\n    \"form.attribute.item.maximum\": \"Valeur maximum\",\n    \"form.attribute.item.maximumComponents\": \"Composants maximum\",\n    \"form.attribute.item.maximumComponents.description\": \"Nombre maximum de composants\",\n    \"form.attribute.item.maximumLength\": \"Taille maximum\",\n    \"form.attribute.item.minimum\": \"Valeur minimun\",\n    \"form.attribute.item.minimumComponents\": \"Composants minimun\",\n    \"form.attribute.item.minimumComponents.description\": \"Nombre minimum de composants\",\n    \"form.attribute.item.minimumLength\": \"Taille minimun\",\n    \"form.attribute.item.number.type\": \"Format nombre\",\n    \"form.attribute.item.number.type.decimal\": \"décimal approximatif (ex: 2,22)\",\n    \"form.attribute.item.number.type.float\": \"décimal (ex: 3,33333)\",\n    \"form.attribute.item.number.type.integer\": \"entier (ex: 10)\",\n    \"form.attribute.item.requiredField\": \"Champ obligatoire\",\n    \"form.attribute.item.requiredField.description\": \"Vous ne pourrez pas créer une entrée si ce champ est vide\",\n    \"form.attribute.item.uniqueField\": \"Champ unique\",\n    \"form.attribute.item.uniqueField.description\": \"Vous ne pourrez pas créer une entrée s'il existe un champ similaire\",\n    \"form.attribute.settings.default\": \"Valeur par défault\",\n    \"form.button.add.field.to.collectionType\": \"Ajouter un nouveau champ à cette collection\",\n    \"form.button.add.field.to.component\": \"Ajouter un nouveau champ à ce composant\",\n    \"form.button.add.field.to.contentType\": \"Ajouter un nouveau champ à cette content type\",\n    \"form.button.add.field.to.singleType\": \"Ajouter un nouveau champ à ce single type\",\n    \"form.button.add-field\": \"Ajouter un autre champ\",\n    \"form.button.cancel\": \"Annuler\",\n    \"form.button.configure-view\": \"Configurer la vue\",\n    from: from,\n    \"modalForm.attribute.form.base.name.placeholder\": \"ex : slug, urlSeo, urlCanonique\",\n    \"modalForm.attribute.target-field\": \"Champ associé\",\n    \"modalForm.header.back\": \"Dos\",\n    \"modalForm.singleType.header-create\": \"Créer un single type\",\n    \"modalForm.sub-header.chooseAttribute.collectionType\": \"Selectionnez un champ pour votre collection\",\n    \"modalForm.sub-header.chooseAttribute.component\": \"Selectionnez un champ pour votre composant\",\n    \"modalForm.sub-header.chooseAttribute.singleType\": \"Selectionnez un champ pour votre single type\",\n    \"modelPage.attribute.relationWith\": \"Relation avec\",\n    \"plugin.description.long\": \"Modélisez la structure de données de votre API. Créer des nouveaux champs et relations en un instant. Les fichiers se créent et se mettent à jour automatiquement.\",\n    \"plugin.description.short\": \"Modélisez la structure de données de votre API.\",\n    \"plugin.name\": \"Content-Type Builder\",\n    \"popUpForm.navContainer.advanced\": \"Réglages avancés\",\n    \"popUpForm.navContainer.base\": \"Réglages de base\",\n    \"popUpWarning.bodyMessage.contentType.delete\": \"Êtes-vous sûr de vouloir supprimer cette Collection ? Cela le supprimera aussi de vos types de contenu.\",\n    \"popUpWarning.draft-publish.button.confirm\": \"Oui, désactiver\",\n    \"popUpWarning.draft-publish.message\": \"Si vous désactivez le système Brouillon/Publier, vos brouillons seront supprimés.\",\n    \"popUpWarning.draft-publish.second-message\": \"Êtes-vous sûr de vouloir le désactiver ?\",\n    \"relation.attributeName.placeholder\": \"Ex : auteur, catégorie, tag\",\n    \"relation.manyToMany\": \"a et appartient à plusieurs\",\n    \"relation.manyToOne\": \"a plusieurs\",\n    \"relation.manyWay\": \"a plusieurs\",\n    \"relation.oneToMany\": \"appartient à plusieurs\",\n    \"relation.oneToOne\": \"a et appartient à un\",\n    \"relation.oneWay\": \"a un\"\n};\n\nexport { fr as default, from };\n//# sourceMappingURL=fr.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,OAAO;AACX,IAAI,KAAK;AAAA,EACL,qBAAqB;AAAA,EACrB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,iCAAiC;AAAA,EACjC,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,mCAAmC;AAAA,EACnC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,mCAAmC;AAAA,EACnC,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,8BAA8B;AAAA,EAC9B;AAAA,EACA,kDAAkD;AAAA,EAClD,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,sCAAsC;AAAA,EACtC,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AACvB;", "names": []}