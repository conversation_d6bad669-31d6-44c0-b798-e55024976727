{"version": 3, "sources": ["../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/Webhooks/components/EventsTable.tsx"], "sourcesContent": ["import { Events } from '../../../../../../../../admin/src/pages/Settings/pages/Webhooks/components/Events';\n\nconst eeTables = {\n  'review-workflows': {\n    'review-workflows': ['review-workflows.updateEntryStage'],\n  },\n  releases: {\n    releases: ['releases.publish'],\n  },\n};\n\nconst getHeaders = (table: keyof typeof eeTables) => {\n  switch (table) {\n    case 'review-workflows':\n      return () => [{ id: 'review-workflows.updateEntryStage', defaultMessage: 'Stage Change' }];\n    case 'releases':\n      return () => [{ id: 'releases.publish', defaultMessage: 'Publish' }];\n  }\n};\n\nconst EventsTableEE = () => {\n  return (\n    <Events.Root>\n      <Events.Headers />\n      <Events.Body />\n      {(Object.keys(eeTables) as Array<keyof typeof eeTables>).map((table) => (\n        <>\n          <Events.Headers getHeaders={getHeaders(table)} />\n          <Events.Body providedEvents={eeTables[table]} />\n        </>\n      ))}\n    </Events.Root>\n  );\n};\n\nexport { EventsTableEE };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAMA,WAAW;EACf,oBAAoB;IAClB,oBAAoB;MAAC;IAAoC;EAC3D;EACAC,UAAU;IACRA,UAAU;MAAC;IAAmB;EAChC;AACF;AAEA,IAAMC,aAAa,CAACC,UAAAA;AAClB,UAAQA,OAAAA;IACN,KAAK;AACH,aAAO,MAAM;QAAC;UAAEC,IAAI;UAAqCC,gBAAgB;QAAe;MAAE;IAC5F,KAAK;AACH,aAAO,MAAM;QAAC;UAAED,IAAI;UAAoBC,gBAAgB;QAAU;MAAE;EACxE;AACF;AAEA,IAAMC,gBAAgB,MAAA;AACpB,aACEC,yBAACC,OAAOC,MAAI;;UACVC,wBAACF,OAAOG,SAAO,CAAA,CAAA;UACfD,wBAACF,OAAOI,MAAI,CAAA,CAAA;MACVC,OAAOC,KAAKd,QAAAA,EAA2Ce,IAAI,CAACZ,cAC5DI,yBAAAS,6BAAA;;cACEN,wBAACF,OAAOG,SAAO;YAACT,YAAYA,WAAWC,KAAAA;;cACvCO,wBAACF,OAAOI,MAAI;YAACK,gBAAgBjB,SAASG,KAAM;;;;;;AAKtD;", "names": ["eeTables", "releases", "getHeaders", "table", "id", "defaultMessage", "EventsTableEE", "_jsxs", "Events", "Root", "_jsx", "Headers", "Body", "Object", "keys", "map", "_Fragment", "providedEvents"]}