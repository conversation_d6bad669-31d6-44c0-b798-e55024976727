import {
  useAdminRoles
} from "./chunk-LDJANAVJ.js";
import {
  MagicLinkWrapper
} from "./chunk-BGSCGOFP.js";
import {
  getBasename
} from "./chunk-QIJGNK42.js";
import {
  useField
} from "./chunk-L5JCPKMP.js";
import {
  Field,
  MultiSelect,
  MultiSelectOption,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  ForwardRef$2j
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt,
  mt
} from "./chunk-ACIMPXWY.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Users/<USER>/MagicLinkCE.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var MagicLinkCE = ({ registrationToken }) => {
  const { formatMessage } = useIntl();
  const target = `${window.location.origin}${getBasename()}/auth/register?registrationToken=${registrationToken}`;
  return (0, import_jsx_runtime.jsx)(MagicLinkWrapper, {
    target,
    children: formatMessage({
      id: "app.components.Users.MagicLink.connect",
      defaultMessage: "Copy and share this link to give access to this user"
    })
  });
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Users/<USER>/SelectRoles.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var SelectRoles = ({ disabled }) => {
  const { isLoading, roles } = useAdminRoles();
  const { formatMessage } = useIntl();
  const { value = [], onChange, error } = useField("roles");
  return (0, import_jsx_runtime2.jsxs)(Field.Root, {
    error,
    hint: formatMessage({
      id: "app.components.Users.ModalCreateBody.block-title.roles.description",
      defaultMessage: "A user can have one or several roles"
    }),
    name: "roles",
    required: true,
    children: [
      (0, import_jsx_runtime2.jsx)(Field.Label, {
        children: formatMessage({
          id: "app.components.Users.ModalCreateBody.block-title.roles",
          defaultMessage: "User's roles"
        })
      }),
      (0, import_jsx_runtime2.jsx)(MultiSelect, {
        disabled,
        onChange: (v) => {
          onChange("roles", v);
        },
        placeholder: formatMessage({
          id: "app.components.Select.placeholder",
          defaultMessage: "Select"
        }),
        startIcon: isLoading ? (0, import_jsx_runtime2.jsx)(Loader, {}) : void 0,
        value: value.map((v) => v.toString()),
        withTags: true,
        children: roles.map((role) => {
          return (0, import_jsx_runtime2.jsx)(MultiSelectOption, {
            value: role.id.toString(),
            children: formatMessage({
              id: `global.${role.code}`,
              defaultMessage: role.name
            })
          }, role.id);
        })
      }),
      (0, import_jsx_runtime2.jsx)(Field.Error, {}),
      (0, import_jsx_runtime2.jsx)(Field.Hint, {})
    ]
  });
};
var rotation = mt`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(359deg);
  }
`;
var LoadingWrapper = dt.div`
  animation: ${rotation} 2s infinite linear;
`;
var Loader = () => (0, import_jsx_runtime2.jsx)(LoadingWrapper, {
  children: (0, import_jsx_runtime2.jsx)(ForwardRef$2j, {})
});

export {
  MagicLinkCE,
  SelectRoles
};
//# sourceMappingURL=chunk-ZJEDGGGS.js.map
