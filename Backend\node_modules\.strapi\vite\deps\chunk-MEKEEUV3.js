import {
  errorsTrads
} from "./chunk-IFOFBKTA.js";
import {
  create4 as create,
  create6 as create2
} from "./chunk-376QHLWZ.js";
import {
  require_lib
} from "./chunk-WH6VCVXU.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/upload/dist/admin/package.json.mjs
var name = "@strapi/upload";
var version = "5.15.1";
var description = "Makes it easy to upload images and files to your Strapi Application.";
var license = "SEE LICENSE IN LICENSE";
var author = {
  name: "Strapi Solutions SAS",
  email: "<EMAIL>",
  url: "https://strapi.io"
};
var maintainers = [
  {
    name: "Strapi Solutions SAS",
    email: "<EMAIL>",
    url: "https://strapi.io"
  }
];
var exports = {
  "./strapi-admin": {
    types: "./dist/admin/src/index.d.ts",
    source: "./admin/src/index.ts",
    "import": "./dist/admin/index.mjs",
    require: "./dist/admin/index.js",
    "default": "./dist/admin/index.js"
  },
  "./_internal/shared": {
    types: "./dist/shared/index.d.ts",
    source: "./shared/index.ts",
    "import": "./dist/shared/index.mjs",
    require: "./dist/shared/index.js",
    "default": "./dist/shared/index.js"
  },
  "./strapi-server": {
    types: "./dist/server/src/index.d.ts",
    source: "./server/src/index.ts",
    "import": "./dist/server/index.mjs",
    require: "./dist/server/index.js",
    "default": "./dist/server/index.js"
  },
  "./package.json": "./package.json"
};
var files = [
  "dist/",
  "strapi-server.js"
];
var scripts = {
  build: "run -T npm-run-all clean --parallel build:code build:types",
  "build:code": "run -T rollup -c",
  "build:types": "run -T run-p build:types:server build:types:admin",
  "build:types:server": "run -T tsc -p server/tsconfig.build.json --emitDeclarationOnly",
  "build:types:admin": "run -T tsc -p admin/tsconfig.build.json --emitDeclarationOnly",
  clean: "run -T rimraf dist",
  lint: "run -T eslint .",
  "test:front": "run -T cross-env IS_EE=true jest --config ./jest.config.front.js",
  "test:unit": "run -T jest",
  "test:ts:back": "run -T tsc --noEmit -p server/tsconfig.json",
  "test:ts:front": "run -T tsc -p admin/tsconfig.json",
  "test:front:watch": "run -T cross-env IS_EE=true jest --config ./jest.config.front.js --watch",
  "test:unit:watch": "run -T jest --watch",
  watch: "run -T rollup -c -w"
};
var dependencies = {
  "@mux/mux-player-react": "3.1.0",
  "@strapi/design-system": "2.0.0-rc.26",
  "@strapi/icons": "2.0.0-rc.26",
  "@strapi/provider-upload-local": "5.15.1",
  "@strapi/utils": "5.15.1",
  "byte-size": "8.1.1",
  cropperjs: "1.6.1",
  "date-fns": "2.30.0",
  formik: "2.4.5",
  "fs-extra": "11.2.0",
  immer: "9.0.21",
  "koa-range": "0.3.0",
  "koa-static": "5.0.0",
  lodash: "4.17.21",
  "mime-types": "2.1.35",
  "prop-types": "^15.8.1",
  qs: "6.11.1",
  "react-dnd": "16.0.1",
  "react-intl": "6.6.2",
  "react-query": "3.39.3",
  "react-redux": "8.1.3",
  "react-select": "5.8.0",
  sharp: "0.33.5",
  yup: "0.32.9"
};
var devDependencies = {
  "@strapi/admin": "5.15.1",
  "@strapi/types": "5.15.1",
  "@testing-library/dom": "10.1.0",
  "@testing-library/react": "15.0.7",
  "@testing-library/user-event": "14.5.2",
  "@types/byte-size": "8.1.2",
  "@types/fs-extra": "11.0.4",
  "@types/koa": "2.13.4",
  "@types/koa-range": "0.3.5",
  "@types/koa-static": "4.0.2",
  formidable: "3.5.4",
  koa: "2.16.1",
  "koa-body": "6.0.1",
  msw: "1.3.0",
  react: "18.3.1",
  "react-dom": "18.3.1",
  "react-router-dom": "6.22.3",
  "styled-components": "6.1.8"
};
var peerDependencies = {
  "@strapi/admin": "^5.0.0",
  react: "^17.0.0 || ^18.0.0",
  "react-dom": "^17.0.0 || ^18.0.0",
  "react-router-dom": "^6.0.0",
  "styled-components": "^6.0.0"
};
var engines = {
  node: ">=18.0.0 <=22.x.x",
  npm: ">=6.0.0"
};
var strapi = {
  displayName: "Media Library",
  name: "upload",
  description: "Media file management.",
  required: true,
  kind: "plugin"
};
var pluginPkg = {
  name,
  version,
  description,
  license,
  author,
  maintainers,
  exports,
  files,
  scripts,
  dependencies,
  devDependencies,
  peerDependencies,
  engines,
  strapi
};

// node_modules/@strapi/upload/dist/admin/pluginId.mjs
var pluginId = pluginPkg.name.replace(/^@strapi\//i, "");

// node_modules/byte-size/index.js
var defaultOptions = {};
var _options = /* @__PURE__ */ new WeakMap();
var referenceTables = {
  metric: [
    { from: 0, to: 1e3, unit: "B", long: "bytes" },
    { from: 1e3, to: 1e6, unit: "kB", long: "kilobytes" },
    { from: 1e6, to: 1e9, unit: "MB", long: "megabytes" },
    { from: 1e9, to: 1e12, unit: "GB", long: "gigabytes" },
    { from: 1e12, to: 1e15, unit: "TB", long: "terabytes" },
    { from: 1e15, to: 1e18, unit: "PB", long: "petabytes" },
    { from: 1e18, to: 1e21, unit: "EB", long: "exabytes" },
    { from: 1e21, to: 1e24, unit: "ZB", long: "zettabytes" },
    { from: 1e24, to: 1e27, unit: "YB", long: "yottabytes" }
  ],
  metric_octet: [
    { from: 0, to: 1e3, unit: "o", long: "octets" },
    { from: 1e3, to: 1e6, unit: "ko", long: "kilooctets" },
    { from: 1e6, to: 1e9, unit: "Mo", long: "megaoctets" },
    { from: 1e9, to: 1e12, unit: "Go", long: "gigaoctets" },
    { from: 1e12, to: 1e15, unit: "To", long: "teraoctets" },
    { from: 1e15, to: 1e18, unit: "Po", long: "petaoctets" },
    { from: 1e18, to: 1e21, unit: "Eo", long: "exaoctets" },
    { from: 1e21, to: 1e24, unit: "Zo", long: "zettaoctets" },
    { from: 1e24, to: 1e27, unit: "Yo", long: "yottaoctets" }
  ],
  iec: [
    { from: 0, to: Math.pow(1024, 1), unit: "B", long: "bytes" },
    { from: Math.pow(1024, 1), to: Math.pow(1024, 2), unit: "KiB", long: "kibibytes" },
    { from: Math.pow(1024, 2), to: Math.pow(1024, 3), unit: "MiB", long: "mebibytes" },
    { from: Math.pow(1024, 3), to: Math.pow(1024, 4), unit: "GiB", long: "gibibytes" },
    { from: Math.pow(1024, 4), to: Math.pow(1024, 5), unit: "TiB", long: "tebibytes" },
    { from: Math.pow(1024, 5), to: Math.pow(1024, 6), unit: "PiB", long: "pebibytes" },
    { from: Math.pow(1024, 6), to: Math.pow(1024, 7), unit: "EiB", long: "exbibytes" },
    { from: Math.pow(1024, 7), to: Math.pow(1024, 8), unit: "ZiB", long: "zebibytes" },
    { from: Math.pow(1024, 8), to: Math.pow(1024, 9), unit: "YiB", long: "yobibytes" }
  ],
  iec_octet: [
    { from: 0, to: Math.pow(1024, 1), unit: "o", long: "octets" },
    { from: Math.pow(1024, 1), to: Math.pow(1024, 2), unit: "Kio", long: "kibioctets" },
    { from: Math.pow(1024, 2), to: Math.pow(1024, 3), unit: "Mio", long: "mebioctets" },
    { from: Math.pow(1024, 3), to: Math.pow(1024, 4), unit: "Gio", long: "gibioctets" },
    { from: Math.pow(1024, 4), to: Math.pow(1024, 5), unit: "Tio", long: "tebioctets" },
    { from: Math.pow(1024, 5), to: Math.pow(1024, 6), unit: "Pio", long: "pebioctets" },
    { from: Math.pow(1024, 6), to: Math.pow(1024, 7), unit: "Eio", long: "exbioctets" },
    { from: Math.pow(1024, 7), to: Math.pow(1024, 8), unit: "Zio", long: "zebioctets" },
    { from: Math.pow(1024, 8), to: Math.pow(1024, 9), unit: "Yio", long: "yobioctets" }
  ]
};
var ByteSize = class {
  constructor(bytes, options) {
    options = Object.assign({
      units: "metric",
      precision: 1,
      locale: void 0
      // Default to the user's system locale
    }, defaultOptions, options);
    _options.set(this, options);
    Object.assign(referenceTables, options.customUnits);
    const prefix = bytes < 0 ? "-" : "";
    bytes = Math.abs(bytes);
    const table = referenceTables[options.units];
    if (table) {
      const units = table.find((u) => bytes >= u.from && bytes < u.to);
      if (units) {
        const defaultFormat = new Intl.NumberFormat(options.locale, {
          style: "decimal",
          minimumFractionDigits: options.precision,
          maximumFractionDigits: options.precision
        });
        const value = units.from === 0 ? prefix + bytes : prefix + defaultFormat.format(bytes / units.from);
        this.value = value;
        this.unit = units.unit;
        this.long = units.long;
      } else {
        this.value = prefix + bytes;
        this.unit = "";
        this.long = "";
      }
    } else {
      throw new Error(`Invalid units specified: ${options.units}`);
    }
  }
  toString() {
    const options = _options.get(this);
    return options.toStringFn ? options.toStringFn.bind(this)() : `${this.value} ${this.unit}`;
  }
};
function byteSize(bytes, options) {
  return new ByteSize(bytes, options);
}
byteSize.defaultOptions = function(options) {
  defaultOptions = options;
};
var byte_size_default = byteSize;

// node_modules/@strapi/upload/dist/admin/utils/getTrad.mjs
var getTrad = (id) => `${pluginId}.${id}`;

// node_modules/@strapi/upload/dist/admin/utils/urlYupSchema.mjs
var urlSchema = create2().shape({
  urls: create().test({
    name: "isUrlValid",
    // eslint-disable-next-line no-template-curly-in-string
    message: "${path}",
    test(values = "") {
      const urls = values.split(/\r?\n/);
      if (urls.length === 0) {
        return this.createError({
          path: this.path,
          message: errorsTrads.min.id
        });
      }
      if (urls.length > 20) {
        return this.createError({
          path: this.path,
          message: errorsTrads.max.id
        });
      }
      const filtered = urls.filter((val) => {
        try {
          new URL(val);
          return false;
        } catch (err) {
          return true;
        }
      });
      const filteredLength = filtered.length;
      if (filteredLength === 0) {
        return true;
      }
      const errorMessage = filteredLength > 1 ? "form.upload-url.error.url.invalids" : "form.upload-url.error.url.invalid";
      return this.createError({
        path: this.path,
        message: getTrad(errorMessage),
        params: {
          number: filtered.length
        }
      });
    }
  })
});

// node_modules/@strapi/upload/dist/admin/constants.mjs
var import_qs = __toESM(require_lib(), 1);
var AssetType;
(function(AssetType2) {
  AssetType2["Video"] = "video";
  AssetType2["Image"] = "image";
  AssetType2["Document"] = "doc";
  AssetType2["Audio"] = "audio";
})(AssetType || (AssetType = {}));
var AssetSource;
(function(AssetSource2) {
  AssetSource2["Url"] = "url";
  AssetSource2["Computer"] = "computer";
})(AssetSource || (AssetSource = {}));
var PERMISSIONS = {
  // This permission regards the main component (App) and is used to tell
  // If the plugin link should be displayed in the menu
  // And also if the plugin is accessible. This use case is found when a user types the url of the
  // plugin directly in the browser
  main: [
    {
      action: "plugin::upload.read",
      subject: null
    },
    {
      action: "plugin::upload.assets.create",
      subject: null
    },
    {
      action: "plugin::upload.assets.update",
      subject: null
    }
  ],
  copyLink: [
    {
      action: "plugin::upload.assets.copy-link",
      subject: null
    }
  ],
  create: [
    {
      action: "plugin::upload.assets.create",
      subject: null
    }
  ],
  download: [
    {
      action: "plugin::upload.assets.download",
      subject: null
    }
  ],
  read: [
    {
      action: "plugin::upload.read",
      subject: null
    }
  ],
  configureView: [
    {
      action: "plugin::upload.configure-view",
      subject: null
    }
  ],
  settings: [
    {
      action: "plugin::upload.settings.read",
      subject: null
    }
  ],
  update: [
    {
      action: "plugin::upload.assets.update",
      subject: null,
      fields: null
    }
  ]
};
var tableHeaders = [
  {
    name: "preview",
    key: "preview",
    metadatas: {
      label: {
        id: getTrad("list.table.header.preview"),
        defaultMessage: "preview"
      },
      isSortable: false
    },
    type: "image"
  },
  {
    name: "name",
    key: "name",
    metadatas: {
      label: {
        id: getTrad("list.table.header.name"),
        defaultMessage: "name"
      },
      isSortable: true
    },
    type: "text"
  },
  {
    name: "ext",
    key: "extension",
    metadatas: {
      label: {
        id: getTrad("list.table.header.ext"),
        defaultMessage: "extension"
      },
      isSortable: false
    },
    type: "ext"
  },
  {
    name: "size",
    key: "size",
    metadatas: {
      label: {
        id: getTrad("list.table.header.size"),
        defaultMessage: "size"
      },
      isSortable: false
    },
    type: "size"
  },
  {
    name: "createdAt",
    key: "createdAt",
    metadatas: {
      label: {
        id: getTrad("list.table.header.createdAt"),
        defaultMessage: "created"
      },
      isSortable: true
    },
    type: "date"
  },
  {
    name: "updatedAt",
    key: "updatedAt",
    metadatas: {
      label: {
        id: getTrad("list.table.header.updatedAt"),
        defaultMessage: "last update"
      },
      isSortable: true
    },
    type: "date"
  }
];
var sortOptions = [
  {
    key: "sort.created_at_desc",
    value: "createdAt:DESC"
  },
  {
    key: "sort.created_at_asc",
    value: "createdAt:ASC"
  },
  {
    key: "sort.name_asc",
    value: "name:ASC"
  },
  {
    key: "sort.name_desc",
    value: "name:DESC"
  },
  {
    key: "sort.updated_at_desc",
    value: "updatedAt:DESC"
  },
  {
    key: "sort.updated_at_asc",
    value: "updatedAt:ASC"
  }
];
var pageSizes = [
  10,
  20,
  50,
  100
];
var localStorageKeys = {
  modalView: `STRAPI_UPLOAD_MODAL_VIEW`,
  view: `STRAPI_UPLOAD_LIBRARY_VIEW`
};
var viewOptions = {
  GRID: 0,
  LIST: 1
};

export {
  pluginPkg,
  pluginId,
  byte_size_default,
  getTrad,
  urlSchema,
  AssetType,
  AssetSource,
  PERMISSIONS,
  tableHeaders,
  sortOptions,
  pageSizes,
  localStorageKeys,
  viewOptions
};
//# sourceMappingURL=chunk-MEKEEUV3.js.map
