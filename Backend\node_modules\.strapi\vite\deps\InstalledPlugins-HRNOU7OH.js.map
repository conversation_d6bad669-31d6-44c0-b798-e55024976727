{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/InstalledPlugins.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { Table, Tbody, Td, Th, Thead, Tr, Typography, useNotifyAT } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { Layouts } from '../../../components/Layouts/Layout';\nimport { Page } from '../../../components/PageHelpers';\nimport { useTypedSelector } from '../../../core/store/hooks';\nimport { useNotification } from '../../../features/Notifications';\nimport { useAPIErrorHandler } from '../../../hooks/useAPIErrorHandler';\nimport { useGetPluginsQuery } from '../../../services/admin';\n\nconst InstalledPlugins = () => {\n  const { formatMessage } = useIntl();\n  const { notifyStatus } = useNotifyAT();\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n\n  const { isLoading, data, error } = useGetPluginsQuery();\n\n  React.useEffect(() => {\n    if (data) {\n      notifyStatus(\n        formatMessage(\n          {\n            id: 'app.utils.notify.data-loaded',\n            defaultMessage: 'The {target} has loaded',\n          },\n          {\n            target: formatMessage({\n              id: 'global.plugins',\n              defaultMessage: 'Plugins',\n            }),\n          }\n        )\n      );\n    }\n\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    }\n  }, [data, error, formatAPIError, formatMessage, notifyStatus, toggleNotification]);\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Layouts.Root>\n      <Page.Main>\n        <Layouts.Header\n          title={formatMessage({\n            id: 'global.plugins',\n            defaultMessage: 'Plugins',\n          })}\n          subtitle={formatMessage({\n            id: 'app.components.ListPluginsPage.description',\n            defaultMessage: 'List of the installed plugins in the project.',\n          })}\n        />\n        <Layouts.Content>\n          <Table colCount={2} rowCount={data?.plugins?.length ?? 0 + 1}>\n            <Thead>\n              <Tr>\n                <Th>\n                  <Typography variant=\"sigma\" textColor=\"neutral600\">\n                    {formatMessage({\n                      id: 'global.name',\n                      defaultMessage: 'Name',\n                    })}\n                  </Typography>\n                </Th>\n                <Th>\n                  <Typography variant=\"sigma\" textColor=\"neutral600\">\n                    {formatMessage({\n                      id: 'global.description',\n                      defaultMessage: 'description',\n                    })}\n                  </Typography>\n                </Th>\n              </Tr>\n            </Thead>\n            <Tbody>\n              {data?.plugins.map(({ name, displayName, description }) => {\n                return (\n                  <Tr key={name}>\n                    <Td>\n                      <Typography textColor=\"neutral800\" variant=\"omega\" fontWeight=\"bold\">\n                        {formatMessage({\n                          id: `global.plugins.${name}`,\n                          defaultMessage: displayName,\n                        })}\n                      </Typography>\n                    </Td>\n                    <Td>\n                      <Typography textColor=\"neutral800\">\n                        {formatMessage({\n                          id: `global.plugins.${name}.description`,\n                          defaultMessage: description,\n                        })}\n                      </Typography>\n                    </Td>\n                  </Tr>\n                );\n              })}\n            </Tbody>\n          </Table>\n        </Layouts.Content>\n      </Page.Main>\n    </Layouts.Root>\n  );\n};\n\nconst ProtectedInstalledPlugins = () => {\n  const { formatMessage } = useIntl();\n  const permissions = useTypedSelector((state) => state.admin_app.permissions);\n\n  return (\n    <Page.Protect permissions={permissions.marketplace?.main}>\n      <Page.Title>\n        {formatMessage({\n          id: 'global.plugins',\n          defaultMessage: 'Plugins',\n        })}\n      </Page.Title>\n      <InstalledPlugins />\n    </Page.Protect>\n  );\n};\n\nexport { ProtectedInstalledPlugins, InstalledPlugins };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,IAAMA,mBAAmB,MAAA;;AACvB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,aAAY,IAAKC,YAAAA;AACzB,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,yBAAyBC,eAAc,IAAKC,mBAAAA;AAEpD,QAAM,EAAEC,WAAWC,MAAMC,MAAK,IAAKC,mBAAAA;AAEnCC,EAAMC,gBAAU,MAAA;AACd,QAAIJ,MAAM;AACRR,mBACEF,cACE;QACEe,IAAI;QACJC,gBAAgB;SAElB;QACEC,QAAQjB,cAAc;UACpBe,IAAI;UACJC,gBAAgB;QAClB,CAAA;MACF,CAAA,CAAA;IAGN;AAEA,QAAIL,OAAO;AACTP,yBAAmB;QACjBc,MAAM;QACNC,SAASZ,eAAeI,KAAAA;MAC1B,CAAA;IACF;KACC;IAACD;IAAMC;IAAOJ;IAAgBP;IAAeE;IAAcE;EAAmB,CAAA;AAEjF,MAAIK,WAAW;AACb,eAAOW,wBAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,aACEF,wBAACG,QAAQC,MAAI;kBACXC,yBAACJ,KAAKK,MAAI;;YACRN,wBAACG,QAAQI,QAAM;UACbC,OAAO5B,cAAc;YACnBe,IAAI;YACJC,gBAAgB;UAClB,CAAA;UACAa,UAAU7B,cAAc;YACtBe,IAAI;YACJC,gBAAgB;UAClB,CAAA;;YAEFI,wBAACG,QAAQO,SAAO;UACd,cAAAL,yBAACM,OAAAA;YAAMC,UAAU;YAAGC,YAAUvB,kCAAMwB,YAANxB,mBAAeyB,WAAU,IAAI;;kBACzDf,wBAACgB,OAAAA;gBACC,cAAAX,yBAACY,IAAAA;;wBACCjB,wBAACkB,IAAAA;sBACC,cAAAlB,wBAACmB,YAAAA;wBAAWC,SAAQ;wBAAQC,WAAU;kCACnCzC,cAAc;0BACbe,IAAI;0BACJC,gBAAgB;wBAClB,CAAA;;;wBAGJI,wBAACkB,IAAAA;sBACC,cAAAlB,wBAACmB,YAAAA;wBAAWC,SAAQ;wBAAQC,WAAU;kCACnCzC,cAAc;0BACbe,IAAI;0BACJC,gBAAgB;wBAClB,CAAA;;;;;;kBAKRI,wBAACsB,OAAAA;0BACEhC,6BAAMwB,QAAQS,IAAI,CAAC,EAAEC,MAAMC,aAAaC,YAAW,MAAE;AACpD,6BACErB,yBAACY,IAAAA;;0BACCjB,wBAAC2B,IAAAA;wBACC,cAAA3B,wBAACmB,YAAAA;0BAAWE,WAAU;0BAAaD,SAAQ;0BAAQQ,YAAW;oCAC3DhD,cAAc;4BACbe,IAAI,kBAAkB6B,IAAAA;4BACtB5B,gBAAgB6B;0BAClB,CAAA;;;0BAGJzB,wBAAC2B,IAAAA;wBACC,cAAA3B,wBAACmB,YAAAA;0BAAWE,WAAU;oCACnBzC,cAAc;4BACbe,IAAI,kBAAkB6B,IAAAA;4BACtB5B,gBAAgB8B;0BAClB,CAAA;;;;kBAdGF,GAAAA,IAAAA;gBAmBb;;;;;;;;AAOd;AAEA,IAAMK,4BAA4B,MAAA;;AAChC,QAAM,EAAEjD,cAAa,IAAKC,QAAAA;AAC1B,QAAMiD,cAAcC,iBAAiB,CAACC,UAAUA,MAAMC,UAAUH,WAAW;AAE3E,aACEzB,yBAACJ,KAAKiC,SAAO;IAACJ,cAAaA,iBAAYK,gBAAZL,mBAAyBM;;UAClDpC,wBAACC,KAAKoC,OAAK;kBACRzD,cAAc;UACbe,IAAI;UACJC,gBAAgB;QAClB,CAAA;;UAEFI,wBAACrB,kBAAAA,CAAAA,CAAAA;;;AAGP;", "names": ["InstalledPlugins", "formatMessage", "useIntl", "notify<PERSON><PERSON><PERSON>", "useNotifyAT", "toggleNotification", "useNotification", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "isLoading", "data", "error", "useGetPluginsQuery", "React", "useEffect", "id", "defaultMessage", "target", "type", "message", "_jsx", "Page", "Loading", "Layouts", "Root", "_jsxs", "Main", "Header", "title", "subtitle", "Content", "Table", "col<PERSON>ount", "rowCount", "plugins", "length", "<PERSON><PERSON>", "Tr", "Th", "Typography", "variant", "textColor", "Tbody", "map", "name", "displayName", "description", "Td", "fontWeight", "ProtectedInstalledPlugins", "permissions", "useTypedSelector", "state", "admin_app", "Protect", "marketplace", "main", "Title"]}