{"version": 3, "sources": ["../../../@strapi/admin/admin/src/services/webhooks.ts", "../../../@strapi/admin/admin/src/pages/Settings/pages/Webhooks/hooks/useWebhooks.ts"], "sourcesContent": ["import * as Webhooks from '../../../shared/contracts/webhooks';\n\nimport { adminApi } from './api';\n\nconst webhooksSerivce = adminApi\n  .enhanceEndpoints({\n    addTagTypes: ['Webhook'],\n  })\n  .injectEndpoints({\n    endpoints: (builder) => ({\n      getWebhooks: builder.query<\n        Webhooks.GetWebhooks.Response['data'],\n        Webhooks.GetWebhook.Params | void\n      >({\n        query: (args) => ({\n          url: `/admin/webhooks/${args?.id ?? ''}`,\n          method: 'GET',\n        }),\n        transformResponse: (\n          response: Webhooks.GetWebhooks.Response | Webhooks.GetWebhook.Response\n        ) => {\n          if (Array.isArray(response.data)) {\n            return response.data;\n          } else {\n            return [response.data];\n          }\n        },\n        providesTags: (res, _err, arg) => {\n          if (typeof arg === 'object' && 'id' in arg) {\n            return [{ type: 'Webhook' as const, id: arg.id }];\n          } else {\n            return [\n              ...(res?.map(({ id }) => ({ type: 'Webhook' as const, id })) ?? []),\n              { type: 'Webhook' as const, id: 'LIST' },\n            ];\n          }\n        },\n      }),\n      createWebhook: builder.mutation<\n        Webhooks.CreateWebhook.Response['data'],\n        Omit<Webhooks.CreateWebhook.Request['body'], 'id' | 'isEnabled'>\n      >({\n        query: (body) => ({\n          url: `/admin/webhooks`,\n          method: 'POST',\n          data: body,\n        }),\n        transformResponse: (response: Webhooks.CreateWebhook.Response) => response.data,\n        invalidatesTags: [{ type: 'Webhook', id: 'LIST' }],\n      }),\n      updateWebhook: builder.mutation<\n        Webhooks.UpdateWebhook.Response['data'],\n        Webhooks.UpdateWebhook.Request['body'] & Webhooks.UpdateWebhook.Params\n      >({\n        query: ({ id, ...body }) => ({\n          url: `/admin/webhooks/${id}`,\n          method: 'PUT',\n          data: body,\n        }),\n        transformResponse: (response: Webhooks.UpdateWebhook.Response) => response.data,\n        invalidatesTags: (_res, _err, { id }) => [{ type: 'Webhook', id }],\n      }),\n      triggerWebhook: builder.mutation<\n        Webhooks.TriggerWebhook.Response['data'],\n        Webhooks.TriggerWebhook.Params['id']\n      >({\n        query: (webhookId) => ({\n          url: `/admin/webhooks/${webhookId}/trigger`,\n          method: 'POST',\n        }),\n        transformResponse: (response: Webhooks.TriggerWebhook.Response) => response.data,\n      }),\n      deleteManyWebhooks: builder.mutation<\n        Webhooks.DeleteWebhooks.Response['data'],\n        Webhooks.DeleteWebhooks.Request['body']\n      >({\n        query: (body) => ({\n          url: `/admin/webhooks/batch-delete`,\n          method: 'POST',\n          data: body,\n        }),\n        transformResponse: (response: Webhooks.DeleteWebhooks.Response) => response.data,\n        invalidatesTags: (_res, _err, { ids }) => ids.map((id) => ({ type: 'Webhook', id })),\n      }),\n    }),\n    overrideExisting: false,\n  });\n\nconst {\n  useGetWebhooksQuery,\n  useCreateWebhookMutation,\n  useUpdateWebhookMutation,\n  useTriggerWebhookMutation,\n  useDeleteManyWebhooksMutation,\n} = webhooksSerivce;\n\nexport {\n  useGetWebhooksQuery,\n  useCreateWebhookMutation,\n  useUpdateWebhookMutation,\n  useTriggerWebhookMutation,\n  useDeleteManyWebhooksMutation,\n};\n", "import { SerializedError } from '@reduxjs/toolkit';\n\nimport { GetWebhook, GetWebhooks } from '../../../../../../../shared/contracts/webhooks';\nimport {\n  useGetWebhooksQuery,\n  useCreateWebhookMutation,\n  useUpdateWebhookMutation,\n  useTriggerWebhookMutation,\n  useDeleteManyWebhooksMutation,\n} from '../../../../../services/webhooks';\nimport { BaseQueryError } from '../../../../../utils/baseQuery';\n\nconst useWebhooks = (\n  args: GetWebhook.Params | void = undefined,\n  queryArgs?: Parameters<typeof useGetWebhooksQuery>[1]\n) => {\n  const { data: webhooks, isLoading, error } = useGetWebhooksQuery(args, queryArgs);\n  const [createWebhook, { error: createError }] = useCreateWebhookMutation();\n  const [updateWebhook, { error: updateError }] = useUpdateWebhookMutation();\n\n  const [triggerWebhook] = useTriggerWebhookMutation();\n  const [deleteManyWebhooks] = useDeleteManyWebhooksMutation();\n\n  return {\n    webhooks: webhooks as GetWebhooks.Response['data'] | undefined,\n    isLoading: isLoading as boolean,\n    error: (error || createError || updateError) as BaseQueryError | SerializedError,\n    createWebhook,\n    updateWebhook,\n    triggerWebhook,\n    deleteManyWebhooks,\n  };\n};\n\nexport { useWebhooks };\n"], "mappings": ";;;;;AAIA,IAAMA,kBAAkBC,SACrBC,iBAAiB;EAChBC,aAAa;IAAC;EAAU;AAC1B,CAAA,EACCC,gBAAgB;EACfC,WAAW,CAACC,aAAa;IACvBC,aAAaD,QAAQE,MAGnB;MACAA,OAAO,CAACC,UAAU;QAChBC,KAAK,oBAAmBD,6BAAME,OAAM,EAAA;QACpCC,QAAQ;;MAEVC,mBAAmB,CACjBC,aAAAA;AAEA,YAAIC,MAAMC,QAAQF,SAASG,IAAI,GAAG;AAChC,iBAAOH,SAASG;eACX;AACL,iBAAO;YAACH,SAASG;UAAK;QACxB;MACF;MACAC,cAAc,CAACC,KAAKC,MAAMC,QAAAA;AACxB,YAAI,OAAOA,QAAQ,YAAY,QAAQA,KAAK;AAC1C,iBAAO;YAAC;cAAEC,MAAM;cAAoBX,IAAIU,IAAIV;YAAG;UAAE;eAC5C;AACL,iBAAO;YACDQ,IAAAA,2BAAKI,IAAI,CAAC,EAAEZ,GAAE,OAAQ;cAAEW,MAAM;cAAoBX;YAAG,QAAO,CAAA;YAChE;cAAEW,MAAM;cAAoBX,IAAI;YAAO;UACxC;QACH;MACF;IACF,CAAA;IACAa,eAAelB,QAAQmB,SAGrB;MACAjB,OAAO,CAACkB,UAAU;QAChBhB,KAAK;QACLE,QAAQ;QACRK,MAAMS;;MAERb,mBAAmB,CAACC,aAA8CA,SAASG;MAC3EU,iBAAiB;QAAC;UAAEL,MAAM;UAAWX,IAAI;QAAO;MAAE;IACpD,CAAA;IACAiB,eAAetB,QAAQmB,SAGrB;MACAjB,OAAO,CAAC,EAAEG,IAAI,GAAGe,KAAAA,OAAY;QAC3BhB,KAAK,mBAAmBC,EAAAA;QACxBC,QAAQ;QACRK,MAAMS;;MAERb,mBAAmB,CAACC,aAA8CA,SAASG;MAC3EU,iBAAiB,CAACE,MAAMT,MAAM,EAAET,GAAE,MAAO;QAAC;UAAEW,MAAM;UAAWX;QAAG;MAAE;IACpE,CAAA;IACAmB,gBAAgBxB,QAAQmB,SAGtB;MACAjB,OAAO,CAACuB,eAAe;QACrBrB,KAAK,mBAAmBqB,SAAAA;QACxBnB,QAAQ;;MAEVC,mBAAmB,CAACC,aAA+CA,SAASG;IAC9E,CAAA;IACAe,oBAAoB1B,QAAQmB,SAG1B;MACAjB,OAAO,CAACkB,UAAU;QAChBhB,KAAK;QACLE,QAAQ;QACRK,MAAMS;;MAERb,mBAAmB,CAACC,aAA+CA,SAASG;MAC5EU,iBAAiB,CAACE,MAAMT,MAAM,EAAEa,IAAG,MAAOA,IAAIV,IAAI,CAACZ,QAAQ;QAAEW,MAAM;QAAWX;QAAG;IACnF,CAAA;;EAEFuB,kBAAkB;AACpB,CAAA;AAEI,IAAA,EACJC,qBACAC,0BACAC,0BACAC,2BACAC,8BAA6B,IAC3BvC;;;AClFJ,IAAMwC,cAAc,CAClBC,OAAiCC,QACjCC,cAAAA;AAEA,QAAM,EAAEC,MAAMC,UAAUC,WAAWC,MAAK,IAAKC,oBAAoBP,MAAME,SAAAA;AACvE,QAAM,CAACM,eAAe,EAAEF,OAAOG,YAAW,CAAE,IAAIC,yBAAAA;AAChD,QAAM,CAACC,eAAe,EAAEL,OAAOM,YAAW,CAAE,IAAIC,yBAAAA;AAEhD,QAAM,CAACC,cAAAA,IAAkBC,0BAAAA;AACzB,QAAM,CAACC,kBAAAA,IAAsBC,8BAAAA;AAE7B,SAAO;IACLb;IACAC;IACAC,OAAQA,SAASG,eAAeG;IAChCJ;IACAG;IACAG;IACAE;EACF;AACF;", "names": ["webhooksSerivce", "adminApi", "enhanceEndpoints", "addTagTypes", "injectEndpoints", "endpoints", "builder", "getWebhooks", "query", "args", "url", "id", "method", "transformResponse", "response", "Array", "isArray", "data", "providesTags", "res", "_err", "arg", "type", "map", "createWebhook", "mutation", "body", "invalidatesTags", "updateWebhook", "_res", "triggerWebhook", "webhookId", "deleteManyWebhooks", "ids", "overrideExisting", "useGetWebhooksQuery", "useCreateWebhookMutation", "useUpdateWebhookMutation", "useTriggerWebhookMutation", "useDeleteManyWebhooksMutation", "useWebhooks", "args", "undefined", "queryArgs", "data", "webhooks", "isLoading", "error", "useGetWebhooksQuery", "createWebhook", "createError", "useCreateWebhookMutation", "updateWebhook", "updateError", "useUpdateWebhookMutation", "triggerWebhook", "useTriggerWebhookMutation", "deleteManyWebhooks", "useDeleteManyWebhooksMutation"]}