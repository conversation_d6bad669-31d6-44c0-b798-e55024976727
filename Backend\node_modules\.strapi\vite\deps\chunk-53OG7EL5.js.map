{"version": 3, "sources": ["../../../@strapi/admin/ee/admin/src/hooks/useLicenseLimits.ts"], "sourcesContent": ["import * as React from 'react';\n\nimport { useGetLicenseLimitsQuery } from '../../../../admin/src/services/admin';\nimport { GetLicenseLimitInformation } from '../../../../shared/contracts/admin';\n\ninterface UseLicenseLimitsArgs {\n  enabled?: boolean;\n}\n\nfunction useLicenseLimits({ enabled }: UseLicenseLimitsArgs = { enabled: true }) {\n  const { data, isError, isLoading } = useGetLicenseLimitsQuery(undefined, {\n    skip: !enabled,\n  });\n\n  type FeatureNames = GetLicenseLimitInformation.Response['data']['features'][number]['name'];\n\n  type GetFeatureType = <T>(name: FeatureNames) => Record<string, T> | undefined;\n\n  const getFeature = React.useCallback<GetFeatureType>(\n    (name) => {\n      const feature = data?.data?.features.find((feature) => feature.name === name);\n\n      if (feature && 'options' in feature) {\n        return feature.options;\n      } else {\n        return {};\n      }\n    },\n    [data]\n  );\n\n  return {\n    license: data?.data,\n    getFeature,\n    isError,\n    isLoading,\n    isTrial: data?.data?.isTrial ?? false,\n  };\n}\n\nexport { useLicenseLimits };\nexport type { UseLicenseLimitsArgs };\n"], "mappings": ";;;;;;;;;;;;AASA,SAASA,iBAAiB,EAAEC,QAAO,IAA2B;EAAEA,SAAS;AAAK,GAAC;;AAC7E,QAAM,EAAEC,MAAMC,SAASC,UAAS,IAAKC,yBAAyBC,QAAW;IACvEC,MAAM,CAACN;EACT,CAAA;AAMA,QAAMO,aAAmBC,kBACvB,CAACC,SAAAA;;AACC,UAAMC,WAAUT,MAAAA,6BAAMA,SAANA,gBAAAA,IAAYU,SAASC,KAAK,CAACF,aAAYA,SAAQD,SAASA;AAExE,QAAIC,WAAW,aAAaA,SAAS;AACnC,aAAOA,QAAQG;WACV;AACL,aAAO,CAAA;IACT;KAEF;IAACZ;EAAK,CAAA;AAGR,SAAO;IACLa,SAASb,6BAAMA;IACfM;IACAL;IACAC;IACAY,WAASd,kCAAMA,SAANA,mBAAYc,YAAW;EAClC;AACF;", "names": ["useLicenseLimits", "enabled", "data", "isError", "isLoading", "useGetLicenseLimitsQuery", "undefined", "skip", "getFeature", "useCallback", "name", "feature", "features", "find", "options", "license", "isTrial"]}