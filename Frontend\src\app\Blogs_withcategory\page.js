export default async function BlogPage({ searchParams }) {
  const resolvedSearchParams = await searchParams;
  const currentPage = Number(resolvedSearchParams?.page) || 1;
  const pageSize = 5;

  // ✅ Correct query string for Strapi
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_STRAPI_API_URL}/api/blog-categories?populate=*&pagination[page]=${currentPage}&pagination[pageSize]=${pageSize}`,
    {
      headers: {
        Authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_TOKEN}`,
      },
      cache: 'no-store',
    }
  );

  const { data: posts, meta } = await res.json();

  // Debug: Log the data structure
  console.log('Posts data:', posts);
  console.log('Meta data:', meta);

  // Custom pagination logic
  const totalPages = meta.pagination.pageCount;
  const totalItems = meta.pagination.total;

  // Generate page numbers to display
  const generatePageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total pages is less than max visible
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show pages around current page
      let startPage = Math.max(1, currentPage - 2);
      let endPage = Math.min(totalPages, currentPage + 2);

      // Adjust if we're near the beginning or end
      if (currentPage <= 3) {
        endPage = Math.min(totalPages, 5);
      }
      if (currentPage >= totalPages - 2) {
        startPage = Math.max(1, totalPages - 4);
      }

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
    }

    return pages;
  };

  const pageNumbers = generatePageNumbers();

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Blog Categories</h1>

      <ul className="space-y-2">
        {posts && posts.length > 0 ? (
          posts.map((post) => (
            <li key={post.id} className="border p-4">
              <h3 className="font-semibold">
                {post.Name || 'Untitled'}
              </h3>
              {post.createdAt && (
                <p className="text-sm text-gray-500 mt-1">
                  Created: {new Date(post.createdAt).toLocaleDateString()}
                </p>
              )}
              {post.blogs && post.blogs.length > 0 && (
                <p className="text-sm text-blue-600 mt-1">
                  {post.blogs.length} blog{post.blogs.length !== 1 ? 's' : ''}
                </p>
              )}
            </li>
          ))
        ) : (
          <li className="border p-4 text-gray-500">No blog categories found.</li>
        )}
      </ul>

      {/* Pagination Controls */}
      <div className="flex gap-2 mt-4 items-center">
        {/* Previous Button */}
        {currentPage > 1 && (
          <a
            href={`/Blogs_withcategory?page=${currentPage - 1}`}
            className="px-3 py-1 rounded border bg-gray-200 hover:bg-gray-300"
          >
            Previous
          </a>
        )}

        {/* Page Numbers */}
        {pageNumbers.map((p) => (
          <a
            key={p}
            href={`/Blogs_withcategory?page=${p}`}
            className={`px-3 py-1 rounded border ${
              p === currentPage ? 'bg-blue-500 text-white' : 'bg-gray-200 hover:bg-gray-300'
            }`}
          >
            {p}
          </a>
        ))}

        {/* Next Button */}
        {currentPage < totalPages && (
          <a
            href={`/Blogs_withcategory?page=${currentPage + 1}`}
            className="px-3 py-1 rounded border bg-gray-200 hover:bg-gray-300"
          >
            Next
          </a>
        )}

        {/* Page Info */}
        <span className="ml-4 text-sm text-gray-600">
          Page {currentPage} of {totalPages} ({totalItems} total items)
        </span>
      </div>
    </div>
  );
}
