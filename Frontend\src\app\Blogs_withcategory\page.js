import { getPagination } from 'next-paginate';

export default async function BlogPage({ searchParams }) {
  const currentPage = Number(searchParams?.page) || 1;
  const pageSize = 5;

  // ✅ Correct query string for Strapi
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_STRAPI_API_URL}/api/blog-categories?populate=*&pagination[page]=${currentPage}&pagination[pageSize]=${pageSize}`,
    {
      headers: {
        Authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_TOKEN}`,
      },
      next: { revalidate: 30 },
      cache: 'no-store',
    }
  );

  const { data: posts, meta } = await res.json();

  const pagination = getPagination({
    totalItems: meta.pagination.total, // ✅ Correct field
    currentPage,
    pageSize,
    maxPages: 5,
  });

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Blog Categories</h1>

      <ul className="space-y-2">
        {posts.map((post) => (
          <li key={post.id} className="border p-4">
            {post.attributes.title}
          </li>
        ))}
      </ul>

      {/* Pagination Controls */}
      <div className="flex gap-2 mt-4">
        {pagination.pages.map((p) => (
          <a
            key={p}
            href={`/blogs?page=${p}`}
            className={`px-3 py-1 rounded border ${
              p === currentPage ? 'bg-blue-500 text-white' : 'bg-gray-200'
            }`}
          >
            {p}
          </a>
        ))}
      </div>
    </div>
  );
}
