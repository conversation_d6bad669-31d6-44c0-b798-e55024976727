import {
  adminApi
} from "./chunk-WH6VCVXU.js";

// node_modules/@strapi/content-manager/dist/admin/services/api.mjs
var contentManagerApi = adminApi.enhanceEndpoints({
  addTagTypes: [
    "ComponentConfiguration",
    "ContentTypesConfiguration",
    "ContentTypeSettings",
    "Document",
    "InitialData",
    "HistoryVersion",
    "Relations",
    "UidAvailability",
    "RecentDocumentList"
  ]
});

// node_modules/@strapi/content-manager/dist/admin/utils/strings.mjs
var capitalise = (str) => str.charAt(0).toUpperCase() + str.slice(1);

export {
  contentManagerApi,
  capitalise
};
//# sourceMappingURL=chunk-A46TUCLT.js.map
