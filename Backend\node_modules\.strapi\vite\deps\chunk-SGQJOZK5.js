import {
  require_baseSet
} from "./chunk-AFM2NWPO.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/set.js
var require_set = __commonJS({
  "node_modules/lodash/set.js"(exports, module) {
    var baseSet = require_baseSet();
    function set(object, path, value) {
      return object == null ? object : baseSet(object, path, value);
    }
    module.exports = set;
  }
});

export {
  require_set
};
//# sourceMappingURL=chunk-SGQJOZK5.js.map
