// node_modules/@strapi/admin/dist/admin/ee/admin/src/constants.mjs
var ADMIN_PERMISSIONS_EE = {
  settings: {
    auditLogs: {
      main: [
        {
          action: "admin::audit-logs.read",
          subject: null
        }
      ],
      read: [
        {
          action: "admin::audit-logs.read",
          subject: null
        }
      ],
      update: [
        {
          action: "admin::audit-logs.update",
          subject: null
        }
      ]
    },
    "review-workflows": {
      main: [
        {
          action: "admin::review-workflows.read",
          subject: null
        }
      ],
      read: [
        {
          action: "admin::review-workflows.read",
          subject: null
        }
      ],
      create: [
        {
          action: "admin::review-workflows.create",
          subject: null
        }
      ],
      delete: [
        {
          action: "admin::review-workflows.delete",
          subject: null
        }
      ],
      update: [
        {
          action: "admin::review-workflows.update",
          subject: null
        }
      ]
    },
    sso: {
      main: [
        {
          action: "admin::provider-login.read",
          subject: null
        }
      ],
      read: [
        {
          action: "admin::provider-login.read",
          subject: null
        }
      ],
      update: [
        {
          action: "admin::provider-login.update",
          subject: null
        }
      ]
    },
    releases: {
      read: [
        {
          action: "plugin::content-releases.settings.read",
          subject: null
        }
      ],
      update: [
        {
          action: "plugin::content-releases.settings.update",
          subject: null
        }
      ]
    }
  }
};
var getEERoutes = () => window.strapi.isEE ? [
  {
    path: "auth/login/:authResponse",
    lazy: async () => {
      const { AuthResponse } = await import("./AuthResponse-OZCQKGEF.js");
      return {
        Component: AuthResponse
      };
    }
  }
] : [];
var SETTINGS_LINKS_EE = () => ({
  global: [
    ...window.strapi.features.isEnabled(window.strapi.features.SSO) ? [
      {
        intlLabel: {
          id: "Settings.sso.title",
          defaultMessage: "Single Sign-On"
        },
        to: "/settings/single-sign-on",
        id: "sso"
      }
    ] : []
  ],
  admin: [
    ...window.strapi.features.isEnabled(window.strapi.features.AUDIT_LOGS) ? [
      {
        intlLabel: {
          id: "global.auditLogs",
          defaultMessage: "Audit Logs"
        },
        to: "/settings/audit-logs?pageSize=50&page=1&sort=date:DESC",
        id: "auditLogs"
      }
    ] : []
  ]
});

export {
  ADMIN_PERMISSIONS_EE,
  getEERoutes,
  SETTINGS_LINKS_EE
};
//# sourceMappingURL=chunk-IQGHPIIW.js.map
