{"version": 3, "sources": ["../../../@strapi/review-workflows/admin/src/services/admin.ts", "../../../@strapi/review-workflows/admin/src/routes/settings/hooks/useKeyboardDragAndDrop.ts", "../../../@strapi/review-workflows/admin/src/routes/settings/hooks/useDragAndDrop.ts", "../../../@strapi/review-workflows/admin/src/routes/settings/components/AddStage.tsx", "../../../@strapi/review-workflows/admin/src/routes/settings/components/Stages.tsx", "../../../@strapi/review-workflows/admin/src/routes/settings/components/WorkflowAttributes.tsx", "../../../@strapi/review-workflows/admin/src/routes/settings/id.tsx"], "sourcesContent": ["import { SanitizedAdminUser } from '@strapi/admin/strapi-admin';\n\nimport { reviewWorkflowsApi } from './api';\n\ntype Roles = SanitizedAdminUser['roles'];\ntype RolesResponse = { data: Roles };\n\nconst adminApi = reviewWorkflowsApi.injectEndpoints({\n  endpoints(builder) {\n    return {\n      getAdminRoles: builder.query<Roles, void>({\n        query: () => ({\n          url: `/admin/roles`,\n          method: 'GET',\n        }),\n        transformResponse: (res: RolesResponse) => {\n          return res.data;\n        },\n      }),\n    };\n  },\n});\n\nconst { useGetAdminRolesQuery } = adminApi;\n\nexport { useGetAdminRolesQuery };\nexport type { SanitizedAdminUser, Roles };\n", "import * as React from 'react';\n\nexport type UseKeyboardDragAndDropCallbacks<TIndex extends number | Array<number> = number> = {\n  onCancel?: (index: TIndex) => void;\n  onDropItem?: (currentIndex: TIndex, newIndex?: TIndex) => void;\n  onGrabItem?: (index: TIndex) => void;\n  onMoveItem?: (newIndex: TIndex, currentIndex: TIndex) => void;\n};\n\n/**\n * Utility hook designed to implement keyboard accessibile drag and drop by\n * returning an onKeyDown handler to be passed to the drag icon button.\n *\n * @internal - You should use `useDragAndDrop` instead.\n */\nexport const useKeyboardDragAndDrop = <TIndex extends number | Array<number> = number>(\n  active: boolean,\n  index: TIndex,\n  { onCancel, onDropItem, onGrabItem, onMoveItem }: UseKeyboardDragAndDropCallbacks<TIndex>\n) => {\n  const [isSelected, setIsSelected] = React.useState(false);\n\n  const handleMove = (movement: 'UP' | 'DOWN') => {\n    if (!isSelected) {\n      return;\n    }\n    if (typeof index === 'number' && onMoveItem) {\n      if (movement === 'UP') {\n        onMoveItem((index - 1) as TIndex, index);\n      } else if (movement === 'DOWN') {\n        onMoveItem((index + 1) as TIndex, index);\n      }\n    }\n  };\n\n  const handleDragClick = () => {\n    if (isSelected) {\n      if (onDropItem) {\n        onDropItem(index);\n      }\n      setIsSelected(false);\n    } else {\n      if (onGrabItem) {\n        onGrabItem(index);\n      }\n      setIsSelected(true);\n    }\n  };\n\n  const handleCancel = () => {\n    if (isSelected) {\n      setIsSelected(false);\n\n      if (onCancel) {\n        onCancel(index);\n      }\n    }\n  };\n\n  const handleKeyDown = <E extends Element>(e: React.KeyboardEvent<E>) => {\n    if (!active) {\n      return;\n    }\n\n    if (e.key === 'Tab' && !isSelected) {\n      return;\n    }\n\n    e.preventDefault();\n\n    switch (e.key) {\n      case ' ':\n      case 'Enter':\n        handleDragClick();\n        break;\n\n      case 'Escape':\n        handleCancel();\n        break;\n\n      case 'ArrowDown':\n      case 'ArrowRight':\n        handleMove('DOWN');\n        break;\n\n      case 'ArrowUp':\n      case 'ArrowLeft':\n        handleMove('UP');\n        break;\n\n      default:\n    }\n  };\n\n  return handleKeyDown;\n};\n", "import * as React from 'react';\n\nimport {\n  useDrag,\n  useDrop,\n  type HandlerManager,\n  type ConnectDragSource,\n  type ConnectDropTarget,\n  type ConnectDragPreview,\n  type DragSourceMonitor,\n} from 'react-dnd';\n\nimport {\n  useKeyboardDragAndDrop,\n  type UseKeyboardDragAndDropCallbacks,\n} from './useKeyboardDragAndDrop';\n\nimport type { Data } from '@strapi/types';\n\nconst DIRECTIONS = {\n  UPWARD: 'upward',\n  DOWNWARD: 'downward',\n} as const;\n\nconst DROP_SENSITIVITY = {\n  REGULAR: 'regular',\n  IMMEDIATE: 'immediate',\n} as const;\n\ninterface UseDragAndDropOptions<\n  TIndex extends number | Array<number> = number,\n  TItem extends { index: TIndex } = { index: TIndex },\n> extends UseKeyboardDragAndDropCallbacks<TIndex> {\n  type?: string;\n  index: TIndex;\n  item?: TItem;\n  onStart?: () => void;\n  onEnd?: () => void;\n  dropSensitivity?: (typeof DROP_SENSITIVITY)[keyof typeof DROP_SENSITIVITY];\n}\n\ntype Identifier = ReturnType<HandlerManager['getHandlerId']>;\n\ntype UseDragAndDropReturn<E extends Element = HTMLElement> = [\n  props: {\n    handlerId: Identifier;\n    isDragging: boolean;\n    handleKeyDown: <E extends Element>(event: React.KeyboardEvent<E>) => void;\n    isOverDropTarget: boolean;\n    direction: (typeof DIRECTIONS)[keyof typeof DIRECTIONS] | null;\n  },\n  objectRef: React.RefObject<E>,\n  dropRef: ConnectDropTarget,\n  dragRef: ConnectDragSource,\n  dragPreviewRef: ConnectDragPreview,\n];\n\ntype DropCollectedProps = {\n  handlerId: Identifier;\n  isOver: boolean;\n};\n\n/**\n * A utility hook abstracting the general drag and drop hooks from react-dnd.\n * Centralising the same behaviours and by default offering keyboard support.\n */\nconst useDragAndDrop = <\n  TIndex extends number | Array<number>,\n  TItem extends { index: TIndex; id?: Data.ID; [key: string]: unknown } = {\n    index: TIndex;\n    [key: string]: unknown;\n  },\n  E extends Element = HTMLElement,\n>(\n  active: boolean,\n  {\n    type = 'STRAPI_DND',\n    index,\n    item,\n    onStart,\n    onEnd,\n    onGrabItem,\n    onDropItem,\n    onCancel,\n    onMoveItem,\n    dropSensitivity = DROP_SENSITIVITY.REGULAR,\n  }: UseDragAndDropOptions<TIndex, TItem>\n): UseDragAndDropReturn<E> => {\n  const objectRef = React.useRef<E>(null);\n\n  const [{ handlerId, isOver }, dropRef] = useDrop<TItem, void, DropCollectedProps>({\n    accept: type,\n    collect(monitor) {\n      return {\n        handlerId: monitor.getHandlerId(),\n        isOver: monitor.isOver({ shallow: true }),\n      };\n    },\n    drop(item) {\n      const draggedIndex = item.index;\n      const newIndex = index;\n\n      if (isOver && onDropItem) {\n        onDropItem(draggedIndex, newIndex);\n      }\n    },\n    hover(item, monitor) {\n      if (!objectRef.current || !onMoveItem) {\n        return;\n      }\n\n      const dragIndex = item.index;\n      const newIndex = index;\n\n      const hoverBoundingRect = objectRef.current?.getBoundingClientRect();\n      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;\n      const clientOffset = monitor.getClientOffset();\n      if (!clientOffset) return;\n\n      const hoverClientY = clientOffset && clientOffset.y - hoverBoundingRect.top;\n      if (typeof dragIndex === 'number' && typeof newIndex === 'number') {\n        if (dragIndex === newIndex) {\n          // Don't replace items with themselves\n          return;\n        }\n\n        if (dropSensitivity === DROP_SENSITIVITY.REGULAR) {\n          // Dragging downwards\n          if (dragIndex < newIndex && hoverClientY < hoverMiddleY) {\n            return;\n          }\n\n          // Dragging upwards\n          if (dragIndex > newIndex && hoverClientY > hoverMiddleY) {\n            return;\n          }\n        }\n\n        // Time to actually perform the action\n        onMoveItem(newIndex, dragIndex);\n        item.index = newIndex;\n      } else {\n        // Using numbers as indices doesn't work for nested list items with path like [1, 1, 0]\n        if (Array.isArray(dragIndex) && Array.isArray(newIndex)) {\n          // Indices comparison to find item position in nested list\n          const minLength = Math.min(dragIndex.length, newIndex.length);\n          let areEqual = true;\n          let isLessThan = false;\n          let isGreaterThan = false;\n\n          for (let i = 0; i < minLength; i++) {\n            if (dragIndex[i] < newIndex[i]) {\n              isLessThan = true;\n              areEqual = false;\n              break;\n            } else if (dragIndex[i] > newIndex[i]) {\n              isGreaterThan = true;\n              areEqual = false;\n              break;\n            }\n          }\n\n          // Don't replace items with themselves\n          if (areEqual && dragIndex.length === newIndex.length) {\n            return;\n          }\n\n          if (dropSensitivity === DROP_SENSITIVITY.REGULAR) {\n            // Dragging downwards\n            if (isLessThan && !isGreaterThan && hoverClientY < hoverMiddleY) {\n              return;\n            }\n\n            // Dragging upwards\n            if (isGreaterThan && !isLessThan && hoverClientY > hoverMiddleY) {\n              return;\n            }\n          }\n        }\n\n        onMoveItem(newIndex, dragIndex);\n        item.index = newIndex;\n      }\n    },\n  });\n\n  const getDragDirection = (monitor: DragSourceMonitor<TItem, void>) => {\n    if (\n      monitor &&\n      monitor.isDragging() &&\n      !monitor.didDrop() &&\n      monitor.getInitialClientOffset() &&\n      monitor.getClientOffset()\n    ) {\n      const deltaY = monitor.getInitialClientOffset()!.y - monitor.getClientOffset()!.y;\n\n      if (deltaY > 0) return DIRECTIONS.UPWARD;\n\n      if (deltaY < 0) return DIRECTIONS.DOWNWARD;\n\n      return null;\n    }\n\n    return null;\n  };\n\n  const [{ isDragging, direction }, dragRef, dragPreviewRef] = useDrag({\n    type,\n    item() {\n      if (onStart) {\n        onStart();\n      }\n\n      /**\n       * This will be attached and it helps define the preview sizes\n       * when a component is flexy e.g. Relations\n       */\n      const { width } = objectRef.current?.getBoundingClientRect() ?? {};\n\n      return { index, width, ...item };\n    },\n    end() {\n      if (onEnd) {\n        onEnd();\n      }\n    },\n    canDrag: active,\n    /**\n     * This is useful when the item is in a virtualized list.\n     * However, if we don't have an ID then we want the libraries\n     * defaults to take care of this.\n     */\n    isDragging: item?.id\n      ? (monitor) => {\n          return item.id === monitor.getItem().id;\n        }\n      : undefined,\n    collect: (monitor) => ({\n      isDragging: monitor.isDragging(),\n      initialOffset: monitor.getInitialClientOffset(),\n      currentOffset: monitor.getClientOffset(),\n      direction: getDragDirection(monitor),\n    }),\n  });\n\n  const handleKeyDown = useKeyboardDragAndDrop(active, index, {\n    onGrabItem,\n    onDropItem,\n    onCancel,\n    onMoveItem,\n  });\n\n  return [\n    { handlerId, isDragging, handleKeyDown, isOverDropTarget: isOver, direction },\n    objectRef,\n    dropRef,\n    dragRef,\n    dragPreviewRef,\n  ];\n};\n\nexport {\n  useDragAndDrop,\n  UseDragAndDropReturn,\n  UseDragAndDropOptions,\n  DIRECTIONS,\n  DROP_SENSITIVITY,\n};\n", "import { Box, BoxComponent, ButtonProps, Flex, Typography } from '@strapi/design-system';\nimport { PlusCircle } from '@strapi/icons';\nimport { styled } from 'styled-components';\n\nexport const AddStage = ({ children, ...props }: ButtonProps) => {\n  return (\n    <StyledButton\n      tag=\"button\"\n      background=\"neutral0\"\n      borderColor=\"neutral150\"\n      paddingBottom={3}\n      paddingLeft={4}\n      paddingRight={4}\n      paddingTop={3}\n      shadow=\"filterShadow\"\n      {...props}\n    >\n      <Typography variant=\"pi\" fontWeight=\"bold\">\n        <Flex tag=\"span\" gap={2}>\n          <PlusCircle width=\"2.4rem\" height=\"2.4rem\" aria-hidden />\n          {children}\n        </Flex>\n      </Typography>\n    </StyledButton>\n  );\n};\n\nconst StyledButton = styled<BoxComponent<'button'>>(Box)`\n  border-radius: 26px;\n  color: ${({ theme }) => theme.colors.neutral500};\n\n  &:hover {\n    color: ${({ theme }) => theme.colors.primary600};\n  }\n\n  &:active {\n    color: ${({ theme }) => theme.colors.primary600};\n  }\n`;\n", "import * as React from 'react';\n\nimport {\n  useField,\n  useForm,\n  useTracking,\n  ConfirmDialog,\n  useNotification,\n  InputRenderer as AdminInputRenderer,\n  InputProps,\n} from '@strapi/admin/strapi-admin';\nimport {\n  Box,\n  Flex,\n  MultiSelectOption,\n  Accordion,\n  Grid,\n  IconButton,\n  MultiSelect,\n  MultiSelectGroup,\n  SingleSelect,\n  SingleSelectOption,\n  TextInput,\n  VisuallyHidden,\n  useComposedRefs,\n  Menu,\n  MenuItem,\n  Field,\n  Dialog,\n} from '@strapi/design-system';\nimport { Duplicate, Drag, More, EyeStriked } from '@strapi/icons';\nimport { getEmptyImage } from 'react-dnd-html5-backend';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { Stage as IStage, StagePermission } from '../../../../../shared/contracts/review-workflows';\nimport { useGetAdminRolesQuery } from '../../../services/admin';\nimport { AVAILABLE_COLORS, getStageColorByHex } from '../../../utils/colors';\nimport { DRAG_DROP_TYPES } from '../constants';\nimport { useDragAndDrop } from '../hooks/useDragAndDrop';\n\nimport { AddStage } from './AddStage';\n\ninterface WorkflowStage extends Pick<IStage, 'id' | 'name' | 'permissions' | 'color'> {\n  __temp_key__: string;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * Stages\n * -----------------------------------------------------------------------------------------------*/\ninterface StagesProps {\n  canDelete?: boolean;\n  canUpdate?: boolean;\n  isCreating?: boolean;\n}\n\nconst Stages = ({ canDelete = true, canUpdate = true, isCreating }: StagesProps) => {\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n  const addFieldRow = useForm('Stages', (state) => state.addFieldRow);\n  const { value: stages = [] } = useField<WorkflowStage[]>('stages');\n\n  return (\n    <Flex direction=\"column\" gap={6} width=\"100%\">\n      <Box position=\"relative\" width=\"100%\">\n        <Background\n          background=\"neutral200\"\n          height=\"100%\"\n          left=\"50%\"\n          position=\"absolute\"\n          top=\"0\"\n          width={2}\n        />\n\n        <Flex direction=\"column\" alignItems=\"stretch\" gap={6} position=\"relative\" tag=\"ol\">\n          {stages.map((stage, index) => {\n            return (\n              <Box key={stage.__temp_key__} tag=\"li\">\n                <Stage\n                  index={index}\n                  canDelete={stages.length > 1 && canDelete}\n                  canReorder={stages.length > 1}\n                  canUpdate={canUpdate}\n                  stagesCount={stages.length}\n                  defaultOpen={!stage.id}\n                  {...stage}\n                />\n              </Box>\n            );\n          })}\n        </Flex>\n      </Box>\n\n      {canUpdate && (\n        <AddStage\n          type=\"button\"\n          onClick={() => {\n            addFieldRow('stages', { name: '' });\n            trackUsage('willCreateStage');\n          }}\n        >\n          {formatMessage({\n            id: 'Settings.review-workflows.stage.add',\n            defaultMessage: 'Add new stage',\n          })}\n        </AddStage>\n      )}\n    </Flex>\n  );\n};\n\nconst Background = styled(Box)`\n  transform: translateX(-50%);\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * Stage\n * -----------------------------------------------------------------------------------------------*/\ninterface StageProps extends WorkflowStage {\n  canDelete?: boolean;\n  canReorder?: boolean;\n  canUpdate?: boolean;\n  index: number;\n  stagesCount: number;\n  defaultOpen?: boolean;\n}\n\nconst Stage = ({\n  index,\n  canDelete = false,\n  canReorder = false,\n  canUpdate = false,\n  stagesCount,\n  name,\n  permissions,\n  color,\n  defaultOpen,\n}: StageProps) => {\n  const [liveText, setLiveText] = React.useState<string>();\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n  const stageErrors = useForm('Stages', (state) => state.errors.stages as object[]);\n  const error = stageErrors?.[index];\n  const addFieldRow = useForm('Stage', (state) => state.addFieldRow);\n  const moveFieldRow = useForm('Stage', (state) => state.moveFieldRow);\n  const removeFieldRow = useForm('Stage', (state) => state.removeFieldRow);\n\n  const getItemPos = (index: number) => `${index + 1} of ${stagesCount}`;\n\n  const handleGrabStage = (index: number) => {\n    setLiveText(\n      formatMessage(\n        {\n          id: 'dnd.grab-item',\n          defaultMessage: `{item}, grabbed. Current position in list: {position}. Press up and down arrow to change position, Spacebar to drop, Escape to cancel.`,\n        },\n        {\n          item: name,\n          position: getItemPos(index),\n        }\n      )\n    );\n  };\n\n  const handleDropStage = (index: number) => {\n    setLiveText(\n      formatMessage(\n        {\n          id: 'dnd.drop-item',\n          defaultMessage: `{item}, dropped. Final position in list: {position}.`,\n        },\n        {\n          item: name,\n          position: getItemPos(index),\n        }\n      )\n    );\n  };\n\n  const handleCancelDragStage = () => {\n    setLiveText(\n      formatMessage(\n        {\n          id: 'dnd.cancel-item',\n          defaultMessage: '{item}, dropped. Re-order cancelled.',\n        },\n        {\n          item: name,\n        }\n      )\n    );\n  };\n\n  const handleMoveStage = (newIndex: number, oldIndex: number) => {\n    setLiveText(\n      formatMessage(\n        {\n          id: 'dnd.reorder',\n          defaultMessage: '{item}, moved. New position in list: {position}.',\n        },\n        {\n          item: name,\n          position: getItemPos(newIndex),\n        }\n      )\n    );\n\n    moveFieldRow('stages', oldIndex, newIndex);\n  };\n\n  const [{ handlerId, isDragging, handleKeyDown }, stageRef, dropRef, dragRef, dragPreviewRef] =\n    useDragAndDrop(canReorder, {\n      index,\n      item: {\n        index,\n        name,\n      },\n      onGrabItem: handleGrabStage,\n      onDropItem: handleDropStage,\n      onMoveItem: handleMoveStage,\n      onCancel: handleCancelDragStage,\n      type: DRAG_DROP_TYPES.STAGE,\n    });\n\n  // @ts-expect-error – the stageRef is incorrectly typed.\n  const composedRef = useComposedRefs<HTMLDivElement>(stageRef, dropRef);\n\n  React.useEffect(() => {\n    dragPreviewRef(getEmptyImage(), { captureDraggingState: false });\n  }, [dragPreviewRef, index]);\n\n  const handleCloneClick = () => {\n    addFieldRow('stages', { name, color, permissions });\n  };\n\n  const id = React.useId();\n\n  return (\n    <Box ref={composedRef} shadow=\"tableShadow\">\n      {liveText && <VisuallyHidden aria-live=\"assertive\">{liveText}</VisuallyHidden>}\n\n      {isDragging ? (\n        <Box\n          background=\"primary100\"\n          borderStyle=\"dashed\"\n          borderColor=\"primary600\"\n          borderWidth=\"1px\"\n          display=\"block\"\n          hasRadius\n          padding={6}\n        />\n      ) : (\n        <AccordionRoot\n          onValueChange={(value) => {\n            if (value) {\n              trackUsage('willEditStage');\n            }\n          }}\n          defaultValue={defaultOpen ? id : undefined}\n          $error={Object.values(error ?? {}).length > 0}\n        >\n          <Accordion.Item value={id}>\n            <Accordion.Header>\n              <Accordion.Trigger>{name}</Accordion.Trigger>\n              <Accordion.Actions>\n                {canDelete || canUpdate ? (\n                  <>\n                    <Menu.Root>\n                      <ContextMenuTrigger size=\"S\" endIcon={null} paddingLeft={2} paddingRight={2}>\n                        <More aria-hidden focusable={false} />\n                        <VisuallyHidden tag=\"span\">\n                          {formatMessage({\n                            id: '[tbdb].components.DynamicZone.more-actions',\n                            defaultMessage: 'More actions',\n                          })}\n                        </VisuallyHidden>\n                      </ContextMenuTrigger>\n                      {/* z-index needs to be as big as the one defined for the wrapper in Stages, otherwise the menu\n                       * disappears behind the accordion\n                       */}\n                      <Menu.Content popoverPlacement=\"bottom-end\" zIndex={2}>\n                        <Menu.SubRoot>\n                          {canUpdate && (\n                            <MenuItem onClick={handleCloneClick}>\n                              {formatMessage({\n                                id: 'Settings.review-workflows.stage.delete',\n                                defaultMessage: 'Duplicate stage',\n                              })}\n                            </MenuItem>\n                          )}\n\n                          {canDelete && (\n                            <DeleteMenuItem onClick={() => removeFieldRow('stages', index)}>\n                              {formatMessage({\n                                id: 'Settings.review-workflows.stage.delete',\n                                defaultMessage: 'Delete',\n                              })}\n                            </DeleteMenuItem>\n                          )}\n                        </Menu.SubRoot>\n                      </Menu.Content>\n                    </Menu.Root>\n\n                    {canUpdate && (\n                      <IconButton\n                        background=\"transparent\"\n                        hasRadius\n                        variant=\"ghost\"\n                        data-handler-id={handlerId}\n                        ref={dragRef}\n                        label={formatMessage({\n                          id: 'Settings.review-workflows.stage.drag',\n                          defaultMessage: 'Drag',\n                        })}\n                        onClick={(e) => e.stopPropagation()}\n                        onKeyDown={handleKeyDown}\n                      >\n                        <Drag />\n                      </IconButton>\n                    )}\n                  </>\n                ) : null}\n              </Accordion.Actions>\n            </Accordion.Header>\n            <Accordion.Content>\n              <Grid.Root gap={4} padding={6}>\n                {[\n                  {\n                    disabled: !canUpdate,\n                    label: formatMessage({\n                      id: 'Settings.review-workflows.stage.name.label',\n                      defaultMessage: 'Stage name',\n                    }),\n                    name: `stages.${index}.name`,\n                    required: true,\n                    size: 6,\n                    type: 'string' as const,\n                  },\n                  {\n                    disabled: !canUpdate,\n                    label: formatMessage({\n                      id: 'content-manager.reviewWorkflows.stage.color',\n                      defaultMessage: 'Color',\n                    }),\n                    name: `stages.${index}.color`,\n                    required: true,\n                    size: 6,\n                    type: 'color' as const,\n                  },\n                  {\n                    disabled: !canUpdate,\n                    label: formatMessage({\n                      id: 'Settings.review-workflows.stage.permissions.label',\n                      defaultMessage: 'Roles that can change this stage',\n                    }),\n                    name: `stages.${index}.permissions`,\n                    placeholder: formatMessage({\n                      id: 'Settings.review-workflows.stage.permissions.placeholder',\n                      defaultMessage: 'Select a role',\n                    }),\n                    required: true,\n                    size: 6,\n                    type: 'permissions' as const,\n                  },\n                ].map(({ size, ...field }) => (\n                  <Grid.Item key={field.name} col={size} direction=\"column\" alignItems=\"stretch\">\n                    <InputRenderer {...field} />\n                  </Grid.Item>\n                ))}\n              </Grid.Root>\n            </Accordion.Content>\n          </Accordion.Item>\n        </AccordionRoot>\n      )}\n    </Box>\n  );\n};\n\nconst AccordionRoot = styled(Accordion.Root)<{ $error?: boolean }>`\n  border: 1px solid\n    ${({ theme, $error }) => ($error ? theme.colors.danger600 : theme.colors.neutral200)};\n`;\n\nconst DeleteMenuItem = styled(MenuItem)`\n  color: ${({ theme }) => theme.colors.danger600};\n`;\n\n// Removing the font-size from the child-span aligns the\n// more icon vertically\nconst ContextMenuTrigger = styled(Menu.Trigger)`\n  :hover,\n  :focus {\n    background-color: ${({ theme }) => theme.colors.neutral100};\n  }\n\n  > span {\n    font-size: 0;\n  }\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * InputRenderer\n * -----------------------------------------------------------------------------------------------*/\n\ntype InputRendererProps = InputProps | ColorSelectorProps | PermissionsFieldProps;\n\nconst InputRenderer = (props: InputRendererProps) => {\n  switch (props.type) {\n    case 'color':\n      return <ColorSelector {...props} />;\n    case 'permissions':\n      return <PermissionsField {...props} />;\n    default:\n      return <AdminInputRenderer {...props} />;\n  }\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ColorSelector\n * -----------------------------------------------------------------------------------------------*/\n\ninterface ColorSelectorProps\n  extends Omit<Extract<InputProps, { type: 'enumeration' }>, 'type' | 'options'> {\n  type: 'color';\n}\n\nconst ColorSelector = ({ disabled, label, name, required }: ColorSelectorProps) => {\n  const { formatMessage } = useIntl();\n  const { value, error, onChange } = useField<string>(name);\n\n  const colorOptions = AVAILABLE_COLORS.map(({ hex, name }) => ({\n    value: hex,\n    label: formatMessage(\n      {\n        id: 'Settings.review-workflows.stage.color.name',\n        defaultMessage: '{name}',\n      },\n      { name }\n    ),\n    color: hex,\n  }));\n\n  const { themeColorName } = getStageColorByHex(value) ?? {};\n\n  return (\n    <Field.Root error={error} name={name} required={required}>\n      <Field.Label>{label}</Field.Label>\n      <SingleSelect\n        disabled={disabled}\n        onChange={(v) => {\n          onChange(name, v.toString());\n        }}\n        value={value?.toUpperCase()}\n        startIcon={\n          <Flex\n            tag=\"span\"\n            height={2}\n            background={value}\n            borderColor={themeColorName === 'neutral0' ? 'neutral150' : 'transparent'}\n            hasRadius\n            shrink={0}\n            width={2}\n          />\n        }\n      >\n        {colorOptions.map(({ value, label, color }) => {\n          const { themeColorName } = getStageColorByHex(color) || {};\n\n          return (\n            <SingleSelectOption\n              value={value}\n              key={value}\n              startIcon={\n                <Flex\n                  tag=\"span\"\n                  height={2}\n                  background={color}\n                  borderColor={themeColorName === 'neutral0' ? 'neutral150' : 'transparent'}\n                  hasRadius\n                  shrink={0}\n                  width={2}\n                />\n              }\n            >\n              {label}\n            </SingleSelectOption>\n          );\n        })}\n      </SingleSelect>\n      <Field.Error />\n    </Field.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * PermissionsField\n * -----------------------------------------------------------------------------------------------*/\ninterface PermissionsFieldProps\n  extends Omit<Extract<InputProps, { type: 'enumeration' }>, 'type' | 'options'> {\n  type: 'permissions';\n}\n\nconst PermissionsField = ({ disabled, name, placeholder, required }: PermissionsFieldProps) => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const [isApplyAllConfirmationOpen, setIsApplyAllConfirmationOpen] = React.useState(false);\n  const { value = [], error, onChange } = useField<StagePermission[]>(name);\n  const allStages = useForm<WorkflowStage[]>('PermissionsField', (state) => state.values.stages);\n  const onFormValueChange = useForm('PermissionsField', (state) => state.onChange);\n  const rolesErrorCount = React.useRef(0);\n\n  const { data: roles = [], isLoading, error: getRolesError } = useGetAdminRolesQuery();\n\n  // Super admins always have permissions to do everything and therefore\n  // there is no point for this role to show up in the role combobox\n  const filteredRoles = roles?.filter((role) => role.code !== 'strapi-super-admin') ?? [];\n\n  React.useEffect(() => {\n    if (\n      !isLoading &&\n      getRolesError &&\n      'status' in getRolesError &&\n      getRolesError.status == 403 &&\n      rolesErrorCount.current === 0\n    ) {\n      rolesErrorCount.current = 1;\n\n      toggleNotification({\n        blockTransition: true,\n        type: 'danger',\n        message: formatMessage({\n          id: 'review-workflows.stage.permissions.noPermissions.description',\n          defaultMessage: 'You don’t have the permission to see roles. Contact your administrator.',\n        }),\n      });\n    }\n  }, [formatMessage, isLoading, roles, toggleNotification, getRolesError]);\n\n  if (!isLoading && filteredRoles.length === 0) {\n    return (\n      <Field.Root\n        name={name}\n        hint={formatMessage({\n          id: 'Settings.review-workflows.stage.permissions.noPermissions.description',\n          defaultMessage: 'You don’t have the permission to see roles',\n        })}\n        required={required}\n      >\n        <Field.Label>\n          {formatMessage({\n            id: 'Settings.review-workflows.stage.permissions.label',\n            defaultMessage: 'Roles that can change this stage',\n          })}\n        </Field.Label>\n        <TextInput\n          disabled\n          placeholder={formatMessage({\n            id: 'components.NotAllowedInput.text',\n            defaultMessage: 'No permissions to see this field',\n          })}\n          startAction={<EyeStriked fill=\"neutral600\" />}\n          type=\"text\"\n          value=\"\"\n        />\n        <Field.Hint />\n      </Field.Root>\n    );\n  }\n\n  return (\n    <>\n      <Flex alignItems=\"flex-end\" gap={3}>\n        <PermissionWrapper grow={1}>\n          <Field.Root error={error} name={name} required>\n            <Field.Label>\n              {formatMessage({\n                id: 'Settings.review-workflows.stage.permissions.label',\n                defaultMessage: 'Roles that can change this stage',\n              })}\n            </Field.Label>\n            <MultiSelect\n              disabled={disabled}\n              onChange={(values) => {\n                // Because the select components expects strings for values, but\n                // the yup schema validates we are sending full permission objects to the API,\n                // we must coerce the string value back to an object\n                const permissions = values.map((value) => ({\n                  role: parseInt(value, 10),\n                  action: 'admin::review-workflows.stage.transition',\n                }));\n\n                onChange(name, permissions);\n              }}\n              placeholder={placeholder}\n              // The Select component expects strings for values\n              value={value.map((permission) => `${permission.role}`)}\n              withTags\n            >\n              <MultiSelectGroup\n                label={formatMessage({\n                  id: 'Settings.review-workflows.stage.permissions.allRoles.label',\n                  defaultMessage: 'All roles',\n                })}\n                values={filteredRoles.map((r) => `${r.id}`)}\n              >\n                {filteredRoles.map((role) => {\n                  return (\n                    <NestedOption key={role.id} value={`${role.id}`}>\n                      {role.name}\n                    </NestedOption>\n                  );\n                })}\n              </MultiSelectGroup>\n            </MultiSelect>\n            <Field.Error />\n          </Field.Root>\n        </PermissionWrapper>\n        <Dialog.Root open={isApplyAllConfirmationOpen} onOpenChange={setIsApplyAllConfirmationOpen}>\n          <Dialog.Trigger>\n            <IconButton\n              disabled={disabled}\n              label={formatMessage({\n                id: 'Settings.review-workflows.stage.permissions.apply.label',\n                defaultMessage: 'Apply to all stages',\n              })}\n              size=\"L\"\n            >\n              <Duplicate />\n            </IconButton>\n          </Dialog.Trigger>\n          <ConfirmDialog\n            onConfirm={() => {\n              onFormValueChange(\n                'stages',\n                allStages.map((stage) => ({\n                  ...stage,\n                  permissions: value,\n                }))\n              );\n\n              setIsApplyAllConfirmationOpen(false);\n              toggleNotification({\n                type: 'success',\n                message: formatMessage({\n                  id: 'Settings.review-workflows.page.edit.confirm.stages.permissions.copy.success',\n                  defaultMessage: 'Applied roles to all other stages of the workflow',\n                }),\n              });\n            }}\n            variant=\"default\"\n          >\n            {formatMessage({\n              id: 'Settings.review-workflows.page.edit.confirm.stages.permissions.copy',\n              defaultMessage:\n                'Roles that can change that stage will be applied to all the other stages.',\n            })}\n          </ConfirmDialog>\n        </Dialog.Root>\n      </Flex>\n    </>\n  );\n};\n\nconst NestedOption = styled(MultiSelectOption)`\n  padding-left: ${({ theme }) => theme.spaces[7]};\n`;\n\n// Grow the size of the permission Select\nconst PermissionWrapper = styled(Flex)`\n  > * {\n    flex-grow: 1;\n  }\n`;\n\nexport { Stages };\nexport type { StagesProps, WorkflowStage };\n", "import { Input<PERSON><PERSON>er, useField, useForm } from '@strapi/admin/strapi-admin';\nimport {\n  Field,\n  Grid,\n  MultiSelect,\n  MultiSelectGroup,\n  MultiSelectOption,\n  Typography,\n  useCollator,\n  SingleSelect,\n  SingleSelectOption,\n} from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { useGetContentTypesQuery } from '../../../services/content-manager';\nimport { useReviewWorkflows } from '../hooks/useReviewWorkflows';\n\nimport type { WorkflowStage } from './Stages';\n\n/* -------------------------------------------------------------------------------------------------\n * WorkflowAttributes\n * -----------------------------------------------------------------------------------------------*/\ninterface WorkflowAttributesProps {\n  canUpdate?: boolean;\n}\n\nconst WorkflowAttributes = ({ canUpdate = true }: WorkflowAttributesProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Grid.Root background=\"neutral0\" hasRadius gap={4} padding={6} shadow=\"tableShadow\">\n      <Grid.Item col={6} direction=\"column\" alignItems=\"stretch\">\n        <InputRenderer\n          disabled={!canUpdate}\n          label={formatMessage({\n            id: 'Settings.review-workflows.workflow.name.label',\n            defaultMessage: 'Workflow Name',\n          })}\n          name=\"name\"\n          required\n          type=\"string\"\n        />\n      </Grid.Item>\n      <Grid.Item col={6} direction=\"column\" alignItems=\"stretch\">\n        <ContentTypesSelector disabled={!canUpdate} />\n      </Grid.Item>\n      <Grid.Item col={6} direction=\"column\" alignItems=\"stretch\">\n        <StageSelector disabled={!canUpdate} />\n      </Grid.Item>\n    </Grid.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ContentTypesSelector\n * -----------------------------------------------------------------------------------------------*/\ninterface ContentTypesSelectorProps {\n  disabled?: boolean;\n}\n\nconst ContentTypesSelector = ({ disabled }: ContentTypesSelectorProps) => {\n  const { formatMessage, locale } = useIntl();\n  const { data: contentTypes, isLoading } = useGetContentTypesQuery();\n  const { workflows } = useReviewWorkflows();\n  const currentWorkflow = useForm('ContentTypesSelector', (state) => state.values);\n\n  const { error, value, onChange } = useField('contentTypes');\n\n  const formatter = useCollator(locale, {\n    sensitivity: 'base',\n  });\n\n  const isDisabled =\n    disabled ||\n    isLoading ||\n    !contentTypes ||\n    (contentTypes.collectionType.length === 0 && contentTypes.singleType.length === 0);\n\n  const collectionTypes = (contentTypes?.collectionType ?? [])\n    .toSorted((a, b) => formatter.compare(a.info.displayName, b.info.displayName))\n    .map((contentType) => ({\n      label: contentType.info.displayName,\n      value: contentType.uid,\n    }));\n\n  const singleTypes = (contentTypes?.singleType ?? []).map((contentType) => ({\n    label: contentType.info.displayName,\n    value: contentType.uid,\n  }));\n\n  return (\n    <Field.Root error={error} name={'contentTypes'}>\n      <Field.Label>\n        {formatMessage({\n          id: 'Settings.review-workflows.workflow.contentTypes.label',\n          defaultMessage: 'Associated to',\n        })}\n      </Field.Label>\n      <MultiSelect\n        customizeContent={(value) =>\n          formatMessage(\n            {\n              id: 'Settings.review-workflows.workflow.contentTypes.displayValue',\n              defaultMessage:\n                '{count} {count, plural, one {content type} other {content types}} selected',\n            },\n            { count: value?.length }\n          )\n        }\n        disabled={isDisabled}\n        onChange={(values) => {\n          onChange('contentTypes', values);\n        }}\n        value={value}\n        placeholder={formatMessage({\n          id: 'Settings.review-workflows.workflow.contentTypes.placeholder',\n          defaultMessage: 'Select',\n        })}\n      >\n        {[\n          ...(collectionTypes.length > 0\n            ? [\n                {\n                  label: formatMessage({\n                    id: 'Settings.review-workflows.workflow.contentTypes.collectionTypes.label',\n                    defaultMessage: 'Collection Types',\n                  }),\n                  children: collectionTypes,\n                },\n              ]\n            : []),\n\n          ...(singleTypes.length > 0\n            ? [\n                {\n                  label: formatMessage({\n                    id: 'Settings.review-workflows.workflow.contentTypes.singleTypes.label',\n                    defaultMessage: 'Single Types',\n                  }),\n                  children: singleTypes,\n                },\n              ]\n            : []),\n        ].map((opt) => {\n          return (\n            <MultiSelectGroup\n              key={opt.label}\n              label={opt.label}\n              values={opt.children.map((child) => child.value.toString())}\n            >\n              {opt.children.map((child) => {\n                const { name: assignedWorkflowName } =\n                  workflows?.find(\n                    (workflow) =>\n                      ((currentWorkflow && workflow.id !== currentWorkflow.id) ||\n                        !currentWorkflow) &&\n                      workflow.contentTypes.includes(child.value)\n                  ) ?? {};\n\n                return (\n                  <NestedOption key={child.value} value={child.value}>\n                    <Typography>\n                      {\n                        // @ts-expect-error - formatMessage options doesn't expect to be a React component but that's what we need actually for the <i> and <em> components\n                        formatMessage(\n                          {\n                            id: 'Settings.review-workflows.workflow.contentTypes.assigned.notice',\n                            defaultMessage:\n                              '{label} {name, select, undefined {} other {<i>(assigned to <em>{name}</em> workflow)</i>}}',\n                          },\n                          {\n                            label: child.label,\n                            name: assignedWorkflowName,\n                            em: (...children) => (\n                              <Typography tag=\"em\" fontWeight=\"bold\">\n                                {children}\n                              </Typography>\n                            ),\n                            i: (...children) => (\n                              <ContentTypeTakeNotice>{children}</ContentTypeTakeNotice>\n                            ),\n                          }\n                        )\n                      }\n                    </Typography>\n                  </NestedOption>\n                );\n              })}\n            </MultiSelectGroup>\n          );\n        })}\n      </MultiSelect>\n    </Field.Root>\n  );\n};\n\nconst NestedOption = styled(MultiSelectOption)`\n  padding-left: ${({ theme }) => theme.spaces[7]};\n`;\n\nconst ContentTypeTakeNotice = styled(Typography)`\n  font-style: italic;\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * StageSelector\n * -----------------------------------------------------------------------------------------------*/\ninterface StageSelectorProps {\n  disabled?: boolean;\n}\n\nconst StageSelector = ({ disabled }: StageSelectorProps) => {\n  const { value: stages = [] } = useField<WorkflowStage[]>('stages');\n  const { formatMessage } = useIntl();\n\n  const { error, value, onChange } = useField('stageRequiredToPublish');\n\n  // stages with empty names are not valid, so we avoid them from being used to avoid errors\n  const validStages = stages.filter((stage) => stage.name);\n\n  return (\n    <Field.Root\n      error={error}\n      name=\"stageRequiredToPublish\"\n      hint={formatMessage({\n        id: 'settings.review-workflows.workflow.stageRequiredToPublish.hint',\n        defaultMessage:\n          'Prevents entries from being published if they are not at the required stage.',\n      })}\n    >\n      <Field.Label>\n        {formatMessage({\n          id: 'settings.review-workflows.workflow.stageRequiredToPublish.label',\n          defaultMessage: 'Required stage for publishing',\n        })}\n      </Field.Label>\n      <SingleSelect\n        disabled={disabled}\n        onChange={(value) => {\n          onChange('stageRequiredToPublish', value);\n        }}\n        value={value}\n      >\n        <SingleSelectOption value={''}>\n          {formatMessage({\n            id: 'settings.review-workflows.workflow.stageRequiredToPublish.any',\n            defaultMessage: 'Any stage',\n          })}\n        </SingleSelectOption>\n        {validStages.map((stage, i) => (\n          <SingleSelectOption\n            key={`requiredToPublishStage-${stage.id || stage.__temp_key__}`}\n            value={stage.id?.toString() || stage.__temp_key__}\n          >\n            {stage.name}\n          </SingleSelectOption>\n        ))}\n      </SingleSelect>\n      <Field.Hint />\n    </Field.Root>\n  );\n};\n\nexport { WorkflowAttributes };\nexport type { WorkflowAttributesProps };\n", "import * as React from 'react';\n\nimport {\n  Con<PERSON><PERSON><PERSON><PERSON><PERSON>,\n  BackButton,\n  useNotification,\n  useAPIErrorHand<PERSON>,\n  useRBAC,\n  Form,\n  Page,\n  FormProps,\n  FormHelpers,\n} from '@strapi/admin/strapi-admin';\nimport { useLicenseLimits } from '@strapi/admin/strapi-admin/ee';\nimport { <PERSON>ton, <PERSON>alog, Flex, Typography } from '@strapi/design-system';\nimport { Check } from '@strapi/icons';\nimport { generateNKeysBetween } from 'fractional-indexing';\nimport { useIntl } from 'react-intl';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport * as yup from 'yup';\n\nimport { LimitsModal } from '../../components/LimitsModal';\nimport {\n  CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME,\n  CHARGEBEE_STAGES_PER_WORKFLOW_ENTITLEMENT_NAME,\n} from '../../constants';\nimport { useTypedSelector } from '../../modules/hooks';\nimport { isBaseQueryError } from '../../utils/api';\n\nimport * as Layout from './components/Layout';\nimport { Stages, WorkflowStage } from './components/Stages';\nimport { WorkflowAttributes } from './components/WorkflowAttributes';\nimport { useReviewWorkflows } from './hooks/useReviewWorkflows';\n\nimport type { Stage, Workflow } from '../../../../shared/contracts/review-workflows';\n\n/* -------------------------------------------------------------------------------------------------\n * EditPage\n * -----------------------------------------------------------------------------------------------*/\n\nconst WORKFLOW_SCHEMA = yup.object({\n  contentTypes: yup.array().of(yup.string()),\n  name: yup\n    .string()\n    .max(255, {\n      id: 'review-workflows.validation.name.max-length',\n      defaultMessage: 'Name can not be longer than 255 characters',\n    })\n    .required()\n    .nullable(),\n  stages: yup\n    .array()\n    .of(\n      yup.object().shape({\n        name: yup\n          .string()\n          .nullable()\n          .required({\n            id: 'review-workflows.validation.stage.name',\n            defaultMessage: 'Name is required',\n          })\n          .max(255, {\n            id: 'review-workflows.validation.stage.max-length',\n            defaultMessage: 'Name can not be longer than 255 characters',\n          })\n          .test(\n            'unique-name',\n            {\n              id: 'review-workflows.validation.stage.duplicate',\n              defaultMessage: 'Stage name must be unique',\n            },\n            (stageName, context) => {\n              // @ts-expect-error it does exist.\n              const { stages } = context.from[1].value;\n\n              return stages.filter((stage: Stage) => stage.name === stageName).length === 1;\n            }\n          ),\n        color: yup\n          .string()\n          .nullable()\n          .required({\n            id: 'review-workflows.validation.stage.color',\n            defaultMessage: 'Color is required',\n          })\n          .matches(/^#(?:[0-9a-fA-F]{3}){1,2}$/i),\n\n        permissions: yup\n          .array(\n            yup.object({\n              role: yup\n                .number()\n                .strict()\n                .typeError({\n                  id: 'review-workflows.validation.stage.permissions.role.number',\n                  defaultMessage: 'Role must be of type number',\n                })\n                .required(),\n              action: yup.string().required({\n                id: 'review-workflows.validation.stage.permissions.action.required',\n                defaultMessage: 'Action is a required argument',\n              }),\n            })\n          )\n          .strict(),\n      })\n    )\n    .min(1),\n  stageRequiredToPublish: yup.string().nullable(),\n});\n\nconst EditPage = () => {\n  const { id = '' } = useParams<{ id: string }>();\n  const isCreatingWorkflow = id === 'create';\n  const { formatMessage } = useIntl();\n  const { _unstableFormatValidationErrors: formatValidationErrors } = useAPIErrorHandler();\n  const navigate = useNavigate();\n  const { toggleNotification } = useNotification();\n  const {\n    isLoading: isLoadingWorkflow,\n    meta,\n    workflows,\n    error,\n    update,\n    create,\n  } = useReviewWorkflows();\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions['settings']?.['review-workflows']\n  );\n  const {\n    allowedActions: { canDelete, canUpdate, canCreate },\n  } = useRBAC(permissions);\n\n  const [savePrompts, setSavePrompts] = React.useState<{\n    hasDeletedServerStages?: boolean;\n    hasReassignedContentTypes?: boolean;\n  }>({});\n  const { getFeature, isLoading: isLicenseLoading } = useLicenseLimits();\n  const [showLimitModal, setShowLimitModal] = React.useState<'workflow' | 'stage' | null>(null);\n\n  const currentWorkflow = workflows?.find((workflow) => workflow.id === parseInt(id, 10));\n  const contentTypesFromOtherWorkflows = workflows\n    ?.filter((workflow) => workflow.id !== parseInt(id, 10))\n    .flatMap((workflow) => workflow.contentTypes);\n\n  const limits = getFeature<string>('review-workflows');\n  const numberOfWorkflows = limits?.[CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME];\n  const stagesPerWorkflow = limits?.[CHARGEBEE_STAGES_PER_WORKFLOW_ENTITLEMENT_NAME];\n\n  interface FormValues {\n    name: string;\n    stages: WorkflowStage[];\n    contentTypes: string[];\n    stageRequiredToPublish: string | null;\n  }\n\n  const submitForm = async (data: FormValues, helpers: Pick<FormHelpers, 'setErrors'>) => {\n    try {\n      const { stageRequiredToPublish, ...rest } = data;\n      const stageRequiredToPublishName =\n        stageRequiredToPublish === ''\n          ? null\n          : rest.stages.find(\n              (stage) =>\n                stage.id === Number(stageRequiredToPublish) ||\n                stage.__temp_key__ === stageRequiredToPublish\n            )?.name;\n\n      if (!isCreatingWorkflow) {\n        const res = await update(id, {\n          ...rest,\n          // compare permissions of stages and only submit them if at least one has\n          // changed; this enables partial updates e.g. for users who don't have\n          // permissions to see roles\n          stages: rest.stages.map((stage) => {\n            let hasUpdatedPermissions = true;\n            const serverStage = currentWorkflow?.stages?.find(\n              (serverStage) => serverStage.id === stage?.id\n            );\n            if (serverStage) {\n              hasUpdatedPermissions =\n                serverStage.permissions?.length !== stage.permissions?.length ||\n                !serverStage.permissions?.every(\n                  (serverPermission) =>\n                    !!stage.permissions?.find(\n                      (permission) => permission.role === serverPermission.role\n                    )\n                );\n            }\n            return {\n              ...stage,\n              permissions: hasUpdatedPermissions ? stage.permissions : undefined,\n            } satisfies Stage;\n          }),\n          stageRequiredToPublishName,\n        });\n\n        if ('error' in res && isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n          helpers.setErrors(formatValidationErrors(res.error));\n        }\n      } else {\n        const res = await create({\n          ...rest,\n          stageRequiredToPublishName,\n        });\n\n        if ('error' in res && isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n          helpers.setErrors(formatValidationErrors(res.error));\n        } else if ('data' in res) {\n          navigate(`../${res.data.id}`, { replace: true });\n        }\n      }\n    } catch (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'An error occurred',\n        }),\n      });\n    }\n    setSavePrompts({});\n  };\n\n  const handleConfirmDeleteDialog =\n    (data: FormValues, helpers: Pick<FormHelpers, 'setErrors'>) => async () => {\n      await submitForm(data, helpers);\n    };\n\n  const handleConfirmClose = () => {\n    setSavePrompts({});\n  };\n\n  const handleSubmit: FormProps<FormValues>['onSubmit'] = async (data, helpers) => {\n    const isContentTypeReassignment = data.contentTypes.some((contentType) =>\n      contentTypesFromOtherWorkflows?.includes(contentType)\n    );\n    const hasDeletedServerStages =\n      !isCreatingWorkflow &&\n      !currentWorkflow?.stages.every((stage) =>\n        data.stages.some((newStage) => newStage.id === stage.id)\n      );\n\n    if (meta && numberOfWorkflows && meta?.workflowCount > parseInt(numberOfWorkflows, 10)) {\n      /**\n       * If the current license has a limit, check if the total count of workflows\n       * exceeds that limit and display the limits modal instead of sending the\n       * update, because it would throw an API error.\n       */\n      setShowLimitModal('workflow');\n\n      /**\n       * If the current license has a limit, check if the total count of stages\n       * exceeds that limit and display the limits modal instead of sending the\n       * update, because it would throw an API error.\n       */\n    } else if (\n      data.stages &&\n      stagesPerWorkflow &&\n      data.stages.length > parseInt(stagesPerWorkflow, 10)\n    ) {\n      setShowLimitModal('stage');\n    } else if (hasDeletedServerStages || isContentTypeReassignment) {\n      if (hasDeletedServerStages) {\n        setSavePrompts((prev) => ({ ...prev, hasDeletedServerStages: true }));\n      }\n\n      if (isContentTypeReassignment) {\n        setSavePrompts((prev) => ({ ...prev, hasReassignedContentTypes: true }));\n      }\n    } else {\n      await submitForm(data, helpers);\n    }\n  };\n\n  /**\n   * If the current license has a limit:\n   * check if the total count of workflows or stages exceeds that limit and display\n   * the limits modal on page load. It can be closed by the user, but the\n   * API will throw an error in case they try to create a new workflow or update the\n   * stages.\n   *\n   * If the current license does not have a limit (e.g. offline license):\n   * do nothing (for now). In case they are trying to create the 201st workflow/ stage\n   * the API will throw an error.\n   *\n   */\n  React.useEffect(() => {\n    if (!isLoadingWorkflow && !isLicenseLoading) {\n      if (meta && numberOfWorkflows && meta?.workflowCount > parseInt(numberOfWorkflows, 10)) {\n        setShowLimitModal('workflow');\n      } else if (\n        currentWorkflow &&\n        currentWorkflow.stages &&\n        stagesPerWorkflow &&\n        currentWorkflow.stages.length > parseInt(stagesPerWorkflow, 10)\n      ) {\n        setShowLimitModal('stage');\n      }\n    }\n  }, [\n    currentWorkflow,\n    isLicenseLoading,\n    isLoadingWorkflow,\n    limits,\n    meta,\n    numberOfWorkflows,\n    stagesPerWorkflow,\n  ]);\n\n  const initialValues: FormValues = React.useMemo(() => {\n    if (isCreatingWorkflow || !currentWorkflow) {\n      return {\n        name: '',\n        stages: [],\n        contentTypes: [],\n        stageRequiredToPublish: '',\n      };\n    } else {\n      return {\n        name: currentWorkflow.name,\n        stages: addTmpKeysToStages(currentWorkflow.stages),\n        contentTypes: currentWorkflow.contentTypes,\n        stageRequiredToPublish: currentWorkflow.stageRequiredToPublish?.id.toString() ?? '',\n      };\n    }\n  }, [currentWorkflow, isCreatingWorkflow]);\n\n  if (isLoadingWorkflow) {\n    return <Page.Loading />;\n  }\n\n  if (error) {\n    return <Page.Error />;\n  }\n\n  return (\n    <>\n      <Layout.DragLayerRendered />\n\n      <Form\n        method={isCreatingWorkflow ? 'POST' : 'PUT'}\n        initialValues={initialValues}\n        validationSchema={WORKFLOW_SCHEMA}\n        onSubmit={handleSubmit}\n      >\n        {({ modified, isSubmitting, values, setErrors }) => (\n          <>\n            <Layout.Header\n              navigationAction={<BackButton fallback=\"..\" />}\n              primaryAction={\n                canUpdate || canCreate ? (\n                  <Button\n                    startIcon={<Check />}\n                    type=\"submit\"\n                    disabled={!modified || isSubmitting || values.stages.length === 0}\n                    // if the confirm dialog is open the loading state is on\n                    // the confirm button already\n                    loading={!Boolean(Object.keys(savePrompts).length > 0) && isSubmitting}\n                  >\n                    {formatMessage({\n                      id: 'global.save',\n                      defaultMessage: 'Save',\n                    })}\n                  </Button>\n                ) : null\n              }\n              subtitle={formatMessage(\n                {\n                  id: 'review-workflows.page.subtitle',\n                  defaultMessage: '{count, plural, one {# stage} other {# stages}}',\n                },\n                { count: currentWorkflow?.stages?.length ?? 0 }\n              )}\n              title={\n                currentWorkflow?.name ||\n                formatMessage({\n                  id: 'Settings.review-workflows.create.page.title',\n                  defaultMessage: 'Create Review Workflow',\n                })\n              }\n            />\n            <Layout.Root>\n              <Flex alignItems=\"stretch\" direction=\"column\" gap={7}>\n                <WorkflowAttributes canUpdate={canUpdate || canCreate} />\n                <Stages\n                  canDelete={canDelete}\n                  canUpdate={canUpdate || canCreate}\n                  isCreating={isCreatingWorkflow}\n                />\n              </Flex>\n            </Layout.Root>\n            <Dialog.Root\n              open={Object.keys(savePrompts).length > 0}\n              onOpenChange={handleConfirmClose}\n            >\n              <ConfirmDialog onConfirm={handleConfirmDeleteDialog(values, { setErrors })}>\n                <Flex direction=\"column\" gap={5}>\n                  {savePrompts.hasDeletedServerStages && (\n                    <Typography textAlign=\"center\" variant=\"omega\">\n                      {formatMessage({\n                        id: 'review-workflows.page.delete.confirm.stages.body',\n                        defaultMessage:\n                          'All entries assigned to deleted stages will be moved to the previous stage.',\n                      })}\n                    </Typography>\n                  )}\n\n                  {savePrompts.hasReassignedContentTypes && (\n                    <Typography textAlign=\"center\" variant=\"omega\">\n                      {formatMessage(\n                        {\n                          id: 'review-workflows.page.delete.confirm.contentType.body',\n                          defaultMessage:\n                            '{count} {count, plural, one {content-type} other {content-types}} {count, plural, one {is} other {are}} already mapped to {count, plural, one {another workflow} other {other workflows}}. If you save changes, {count, plural, one {this} other {these}} {count, plural, one {content-type} other {{count} content-types}} will no more be mapped to the {count, plural, one {another workflow} other {other workflows}} and all corresponding information will be removed.',\n                        },\n                        {\n                          count:\n                            contentTypesFromOtherWorkflows?.filter((contentType) =>\n                              values.contentTypes.includes(contentType)\n                            ).length ?? 0,\n                        }\n                      )}\n                    </Typography>\n                  )}\n\n                  <Typography textAlign=\"center\" variant=\"omega\">\n                    {formatMessage({\n                      id: 'review-workflows.page.delete.confirm.confirm',\n                      defaultMessage: 'Are you sure you want to save?',\n                    })}\n                  </Typography>\n                </Flex>\n              </ConfirmDialog>\n            </Dialog.Root>\n          </>\n        )}\n      </Form>\n\n      <LimitsModal.Root\n        open={showLimitModal === 'workflow'}\n        onOpenChange={() => setShowLimitModal(null)}\n      >\n        <LimitsModal.Title>\n          {formatMessage({\n            id: 'review-workflows.edit.page.workflows.limit.title',\n            defaultMessage: 'You’ve reached the limit of workflows in your plan',\n          })}\n        </LimitsModal.Title>\n\n        <LimitsModal.Body>\n          {formatMessage({\n            id: 'review-workflows.edit.page.workflows.limit.body',\n            defaultMessage: 'Delete a workflow or contact Sales to enable more workflows.',\n          })}\n        </LimitsModal.Body>\n      </LimitsModal.Root>\n\n      <LimitsModal.Root\n        open={showLimitModal === 'stage'}\n        onOpenChange={() => setShowLimitModal(null)}\n      >\n        <LimitsModal.Title>\n          {formatMessage({\n            id: 'review-workflows.edit.page.stages.limit.title',\n            defaultMessage: 'You have reached the limit of stages for this workflow in your plan',\n          })}\n        </LimitsModal.Title>\n\n        <LimitsModal.Body>\n          {formatMessage({\n            id: 'review-workflows.edit.page.stages.limit.body',\n            defaultMessage: 'Try deleting some stages or contact Sales to enable more stages.',\n          })}\n        </LimitsModal.Body>\n      </LimitsModal.Root>\n    </>\n  );\n};\n\nconst addTmpKeysToStages = (data: Workflow['stages']) => {\n  const keys = generateNKeysBetween(undefined, undefined, data.length);\n\n  return data.map((datum, index) => ({\n    ...datum,\n    __temp_key__: keys[index],\n  }));\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ProtectedEditPage\n * -----------------------------------------------------------------------------------------------*/\n\nconst ProtectedEditPage = () => {\n  const permissions = useTypedSelector((state) => {\n    const {\n      create = [],\n      update = [],\n      read = [],\n    } = state.admin_app.permissions.settings?.['review-workflows'] ?? {};\n\n    return [...create, ...update, ...read];\n  });\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <EditPage />\n    </Page.Protect>\n  );\n};\n\nexport { ProtectedEditPage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAMA,WAAWC,mBAAmBC,gBAAgB;EAClDC,UAAUC,SAAO;AACf,WAAO;MACLC,eAAeD,QAAQE,MAAmB;QACxCA,OAAO,OAAO;UACZC,KAAK;UACLC,QAAQ;;QAEVC,mBAAmB,CAACC,QAAAA;AAClB,iBAAOA,IAAIC;QACb;MACF,CAAA;IACF;EACF;AACF,CAAA;AAEM,IAAA,EAAEC,sBAAqB,IAAKZ;;;;;;;ACR3B,IAAMa,yBAAyB,CACpCC,QACAC,OACA,EAAEC,UAAUC,YAAYC,YAAYC,WAAU,MAA2C;AAEzF,QAAM,CAACC,YAAYC,aAAAA,IAAuBC,eAAS,KAAA;AAEnD,QAAMC,aAAa,CAACC,aAAAA;AAClB,QAAI,CAACJ,YAAY;AACf;IACF;AACA,QAAI,OAAOL,UAAU,YAAYI,YAAY;AAC3C,UAAIK,aAAa,MAAM;AACrBL,mBAAYJ,QAAQ,GAAcA,KAAAA;iBACzBS,aAAa,QAAQ;AAC9BL,mBAAYJ,QAAQ,GAAcA,KAAAA;MACpC;IACF;EACF;AAEA,QAAMU,kBAAkB,MAAA;AACtB,QAAIL,YAAY;AACd,UAAIH,YAAY;AACdA,mBAAWF,KAAAA;MACb;AACAM,oBAAc,KAAA;WACT;AACL,UAAIH,YAAY;AACdA,mBAAWH,KAAAA;MACb;AACAM,oBAAc,IAAA;IAChB;EACF;AAEA,QAAMK,eAAe,MAAA;AACnB,QAAIN,YAAY;AACdC,oBAAc,KAAA;AAEd,UAAIL,UAAU;AACZA,iBAASD,KAAAA;MACX;IACF;EACF;AAEA,QAAMY,gBAAgB,CAAoBC,MAAAA;AACxC,QAAI,CAACd,QAAQ;AACX;IACF;AAEA,QAAIc,EAAEC,QAAQ,SAAS,CAACT,YAAY;AAClC;IACF;AAEAQ,MAAEE,eAAc;AAEhB,YAAQF,EAAEC,KAAG;MACX,KAAK;MACL,KAAK;AACHJ,wBAAAA;AACA;MAEF,KAAK;AACHC,qBAAAA;AACA;MAEF,KAAK;MACL,KAAK;AACHH,mBAAW,MAAA;AACX;MAEF,KAAK;MACL,KAAK;AACHA,mBAAW,IAAA;AACX;IAGJ;EACF;AAEA,SAAOI;AACT;;;AC5EA,IAAMI,aAAa;EACjBC,QAAQ;EACRC,UAAU;AACZ;AAEA,IAAMC,mBAAmB;EACvBC,SAAS;EACTC,WAAW;AACb;AAuCA,IAAMC,iBAAiB,CAQrBC,QACA,EACEC,OAAO,cACPC,OACAC,MACAC,SACAC,OACAC,YACAC,YACAC,UACAC,YACAC,kBAAkBd,iBAAiBC,QAAO,MACL;AAEvC,QAAMc,YAAkBC,cAAU,IAAA;AAElC,QAAM,CAAC,EAAEC,WAAWC,OAAM,GAAIC,OAAQ,IAAGC,QAAyC;IAChFC,QAAQhB;IACRiB,QAAQC,SAAO;AACb,aAAO;QACLN,WAAWM,QAAQC,aAAY;QAC/BN,QAAQK,QAAQL,OAAO;UAAEO,SAAS;QAAK,CAAA;MACzC;IACF;IACAC,KAAKnB,OAAI;AACP,YAAMoB,eAAepB,MAAKD;AAC1B,YAAMsB,WAAWtB;AAEjB,UAAIY,UAAUP,YAAY;AACxBA,mBAAWgB,cAAcC,QAAAA;MAC3B;IACF;IACAC,MAAMtB,OAAMgB,SAAO;;AACjB,UAAI,CAACR,UAAUe,WAAW,CAACjB,YAAY;AACrC;MACF;AAEA,YAAMkB,YAAYxB,MAAKD;AACvB,YAAMsB,WAAWtB;AAEjB,YAAM0B,qBAAoBjB,eAAUe,YAAVf,mBAAmBkB;AAC7C,YAAMC,gBAAgBF,kBAAkBG,SAASH,kBAAkBI,OAAO;AAC1E,YAAMC,eAAed,QAAQe,gBAAe;AAC5C,UAAI,CAACD,aAAc;AAEnB,YAAME,eAAeF,gBAAgBA,aAAaG,IAAIR,kBAAkBI;AACxE,UAAI,OAAOL,cAAc,YAAY,OAAOH,aAAa,UAAU;AACjE,YAAIG,cAAcH,UAAU;AAE1B;QACF;AAEA,YAAId,oBAAoBd,iBAAiBC,SAAS;AAEhD,cAAI8B,YAAYH,YAAYW,eAAeL,cAAc;AACvD;UACF;AAGA,cAAIH,YAAYH,YAAYW,eAAeL,cAAc;AACvD;UACF;QACF;AAGArB,mBAAWe,UAAUG,SAAAA;AACrBxB,QAAAA,MAAKD,QAAQsB;aACR;AAEL,YAAIa,MAAMC,QAAQX,SAAAA,KAAcU,MAAMC,QAAQd,QAAW,GAAA;AAEvD,gBAAMe,YAAYC,KAAKC,IAAId,UAAUe,QAAQlB,SAASkB,MAAM;AAC5D,cAAIC,WAAW;AACf,cAAIC,aAAa;AACjB,cAAIC,gBAAgB;AAEpB,mBAASC,IAAI,GAAGA,IAAIP,WAAWO,KAAK;AAClC,gBAAInB,UAAUmB,CAAAA,IAAKtB,SAASsB,CAAAA,GAAI;AAC9BF,2BAAa;AACbD,yBAAW;AACX;uBACShB,UAAUmB,CAAAA,IAAKtB,SAASsB,CAAAA,GAAI;AACrCD,8BAAgB;AAChBF,yBAAW;AACX;YACF;UACF;AAGA,cAAIA,YAAYhB,UAAUe,WAAWlB,SAASkB,QAAQ;AACpD;UACF;AAEA,cAAIhC,oBAAoBd,iBAAiBC,SAAS;AAEhD,gBAAI+C,cAAc,CAACC,iBAAiBV,eAAeL,cAAc;AAC/D;YACF;AAGA,gBAAIe,iBAAiB,CAACD,cAAcT,eAAeL,cAAc;AAC/D;YACF;UACF;QACF;AAEArB,mBAAWe,UAAUG,SAAAA;AACrBxB,QAAAA,MAAKD,QAAQsB;MACf;IACF;EACF,CAAA;AAEA,QAAMuB,mBAAmB,CAAC5B,YAAAA;AACxB,QACEA,WACAA,QAAQ6B,WAAU,KAClB,CAAC7B,QAAQ8B,QAAO,KAChB9B,QAAQ+B,uBAAsB,KAC9B/B,QAAQe,gBAAe,GACvB;AACA,YAAMiB,SAAShC,QAAQ+B,uBAAsB,EAAId,IAAIjB,QAAQe,gBAAe,EAAIE;AAEhF,UAAIe,SAAS,EAAG,QAAO1D,WAAWC;AAElC,UAAIyD,SAAS,EAAG,QAAO1D,WAAWE;AAElC,aAAO;IACT;AAEA,WAAO;EACT;AAEA,QAAM,CAAC,EAAEqD,YAAYI,UAAS,GAAIC,SAASC,cAAe,IAAGC,QAAQ;IACnEtD;IACAE,OAAAA;;AACE,UAAIC,SAAS;AACXA,gBAAAA;MACF;AAMA,YAAM,EAAEoD,MAAK,MAAK7C,eAAUe,YAAVf,mBAAmBkB,4BAA2B,CAAA;AAEhE,aAAO;QAAE3B;QAAOsD;QAAO,GAAGrD;MAAK;IACjC;IACAsD,MAAAA;AACE,UAAIpD,OAAO;AACTA,cAAAA;MACF;IACF;IACAqD,SAAS1D;;;;;;IAMTgD,aAAY7C,6BAAMwD,MACd,CAACxC,YAAAA;AACC,aAAOhB,KAAKwD,OAAOxC,QAAQyC,QAAO,EAAGD;QAEvCE;IACJ3C,SAAS,CAACC,aAAa;MACrB6B,YAAY7B,QAAQ6B,WAAU;MAC9Bc,eAAe3C,QAAQ+B,uBAAsB;MAC7Ca,eAAe5C,QAAQe,gBAAe;MACtCkB,WAAWL,iBAAiB5B,OAAAA;;EAEhC,CAAA;AAEA,QAAM6C,gBAAgBC,uBAAuBjE,QAAQE,OAAO;IAC1DI;IACAC;IACAC;IACAC;EACF,CAAA;AAEA,SAAO;IACL;MAAEI;MAAWmC;MAAYgB;MAAeE,kBAAkBpD;MAAQsC;IAAU;IAC5EzC;IACAI;IACAsC;IACAC;EACD;AACH;;;;IC/Paa,WAAW,CAAC,EAAEC,UAAU,GAAGC,MAAoB,MAAA;AAC1D,aACEC,wBAACC,cAAAA;IACCC,KAAI;IACJC,YAAW;IACXC,aAAY;IACZC,eAAe;IACfC,aAAa;IACbC,cAAc;IACdC,YAAY;IACZC,QAAO;IACN,GAAGV;IAEJ,cAAAC,wBAACU,YAAAA;MAAWC,SAAQ;MAAKC,YAAW;MAClC,cAAAC,yBAACC,MAAAA;QAAKZ,KAAI;QAAOa,KAAK;;cACpBf,wBAACgB,eAAAA;YAAWC,OAAM;YAASC,QAAO;YAASC,eAAW;;UACrDrB;;;;;AAKX;AAEA,IAAMG,eAAemB,GAA+BC,GAAAA;;WAEzC,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAOC,UAAU;;;aAGpC,CAAC,EAAEF,MAAK,MAAOA,MAAMC,OAAOE,UAAU;;;;aAItC,CAAC,EAAEH,MAAK,MAAOA,MAAMC,OAAOE,UAAU;;;;;ACoB7CC,IAAAA,SAAS,CAAC,EAAEC,YAAY,MAAMC,YAAY,MAAMC,WAAU,MAAe;AAC7E,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAMC,cAAcC,QAAQ,UAAU,CAACC,UAAUA,MAAMF,WAAW;AAClE,QAAM,EAAEG,OAAOC,SAAS,CAAA,EAAE,IAAKC,SAA0B,QAAA;AAEzD,aACEC,0BAACC,MAAAA;IAAKC,WAAU;IAASC,KAAK;IAAGC,OAAM;;UACrCJ,0BAACK,KAAAA;QAAIC,UAAS;QAAWF,OAAM;;cAC7BG,yBAACC,YAAAA;YACCC,YAAW;YACXC,QAAO;YACPC,MAAK;YACLL,UAAS;YACTM,KAAI;YACJR,OAAO;;cAGTG,yBAACN,MAAAA;YAAKC,WAAU;YAASW,YAAW;YAAUV,KAAK;YAAGG,UAAS;YAAWQ,KAAI;sBAC3EhB,OAAOiB,IAAI,CAACC,OAAOC,UAAAA;AAClB,yBACEV,yBAACF,KAAAA;gBAA6BS,KAAI;gBAChC,cAAAP,yBAACW,OAAAA;kBACCD;kBACA9B,WAAWW,OAAOqB,SAAS,KAAKhC;kBAChCiC,YAAYtB,OAAOqB,SAAS;kBAC5B/B;kBACAiC,aAAavB,OAAOqB;kBACpBG,aAAa,CAACN,MAAMO;kBACnB,GAAGP;;cAREA,GAAAA,MAAMQ,YAAY;YAYhC,CAAA;;;;MAIHpC,iBACCmB,yBAACkB,UAAAA;QACCC,MAAK;QACLC,SAAS,MAAA;AACPjC,sBAAY,UAAU;YAAEkC,MAAM;UAAG,CAAA;AACjCpC,qBAAW,iBAAA;QACb;kBAECF,cAAc;UACbiC,IAAI;UACJM,gBAAgB;QAClB,CAAA;;;;AAKV;AAEA,IAAMrB,aAAasB,GAAOzB,GAAAA;;;AAgB1B,IAAMa,QAAQ,CAAC,EACbD,OACA9B,YAAY,OACZiC,aAAa,OACbhC,YAAY,OACZiC,aACAO,MACAG,aACAC,OACAV,YAAW,MACA;AACX,QAAM,CAACW,UAAUC,WAAY,IAASC,gBAAQ;AAC9C,QAAM,EAAE7C,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAM2C,cAAczC,QAAQ,UAAU,CAACC,UAAUA,MAAMyC,OAAOvC,MAAM;AACpE,QAAMwC,QAAQF,2CAAcnB;AAC5B,QAAMvB,cAAcC,QAAQ,SAAS,CAACC,UAAUA,MAAMF,WAAW;AACjE,QAAM6C,eAAe5C,QAAQ,SAAS,CAACC,UAAUA,MAAM2C,YAAY;AACnE,QAAMC,iBAAiB7C,QAAQ,SAAS,CAACC,UAAUA,MAAM4C,cAAc;AAEvE,QAAMC,aAAa,CAACxB,WAAkB,GAAGA,SAAQ,CAAE,OAAMI,WAAAA;AAEzD,QAAMqB,kBAAkB,CAACzB,WAAAA;AACvBiB,gBACE5C,cACE;MACEiC,IAAI;MACJM,gBAAgB;OAElB;MACEc,MAAMf;MACNtB,UAAUmC,WAAWxB,MAAAA;IACvB,CAAA,CAAA;EAGN;AAEA,QAAM2B,kBAAkB,CAAC3B,WAAAA;AACvBiB,gBACE5C,cACE;MACEiC,IAAI;MACJM,gBAAgB;OAElB;MACEc,MAAMf;MACNtB,UAAUmC,WAAWxB,MAAAA;IACvB,CAAA,CAAA;EAGN;AAEA,QAAM4B,wBAAwB,MAAA;AAC5BX,gBACE5C,cACE;MACEiC,IAAI;MACJM,gBAAgB;OAElB;MACEc,MAAMf;IACR,CAAA,CAAA;EAGN;AAEA,QAAMkB,kBAAkB,CAACC,UAAkBC,aAAAA;AACzCd,gBACE5C,cACE;MACEiC,IAAI;MACJM,gBAAgB;OAElB;MACEc,MAAMf;MACNtB,UAAUmC,WAAWM,QAAAA;IACvB,CAAA,CAAA;AAIJR,iBAAa,UAAUS,UAAUD,QAAAA;EACnC;AAEA,QAAM,CAAC,EAAEE,WAAWC,YAAYC,cAAa,GAAIC,UAAUC,SAASC,SAASC,cAAe,IAC1FC,eAAepC,YAAY;IACzBH;IACA0B,MAAM;MACJ1B;MACAW;IACF;IACA6B,YAAYf;IACZgB,YAAYd;IACZe,YAAYb;IACZc,UAAUf;IACVnB,MAAMmC,gBAAgBC;EACxB,CAAA;AAGF,QAAMC,cAAcC,gBAAgCZ,UAAUC,OAAAA;AAE9DY,EAAMC,iBAAU,MAAA;AACdX,mBAAeY,cAAiB,GAAA;MAAEC,sBAAsB;IAAM,CAAA;KAC7D;IAACb;IAAgBtC;EAAM,CAAA;AAE1B,QAAMoD,mBAAmB,MAAA;AACvB3E,gBAAY,UAAU;MAAEkC;MAAMI;MAAOD;IAAY,CAAA;EACnD;AAEA,QAAMR,KAAW+C,aAAK;AAEtB,aACEtE,0BAACK,KAAAA;IAAIkE,KAAKR;IAAaS,QAAO;;MAC3BvC,gBAAY1B,yBAACkE,gBAAAA;QAAeC,aAAU;QAAazC,UAAAA;;MAEnDiB,iBACC3C,yBAACF,KAAAA;QACCI,YAAW;QACXkE,aAAY;QACZC,aAAY;QACZC,aAAY;QACZC,SAAQ;QACRC,WAAS;QACTC,SAAS;eAGXzE,yBAAC0E,eAAAA;QACCC,eAAe,CAACrF,UAAAA;AACd,cAAIA,OAAO;AACTL,uBAAW,eAAA;UACb;QACF;QACA2F,cAAc7D,cAAcC,KAAK6D;QACjCC,QAAQC,OAAOC,OAAOjD,SAAS,CAAA,CAAC,EAAGnB,SAAS;sBAE5CnB,0BAACwF,UAAUC,MAAI;UAAC5F,OAAO0B;;gBACrBvB,0BAACwF,UAAUE,QAAM;;oBACfnF,yBAACiF,UAAUG,SAAO;kBAAE/D,UAAAA;;oBACpBrB,yBAACiF,UAAUI,SAAO;kBACfzG,UAAAA,aAAaC,gBACZY,0BAAA6F,8BAAA;;0BACE7F,0BAAC8F,KAAKC,MAAI;;8BACR/F,0BAACgG,oBAAAA;4BAAmBC,MAAK;4BAAIC,SAAS;4BAAMC,aAAa;4BAAGC,cAAc;;kCACxE7F,yBAAC8F,eAAAA;gCAAKC,eAAW;gCAACC,WAAW;;kCAC7BhG,yBAACkE,gBAAAA;gCAAe3D,KAAI;0CACjBxB,cAAc;kCACbiC,IAAI;kCACJM,gBAAgB;gCAClB,CAAA;;;;8BAMJtB,yBAACuF,KAAKU,SAAO;4BAACC,kBAAiB;4BAAaC,QAAQ;0CAClD1G,0BAAC8F,KAAKa,SAAO;;gCACVvH,iBACCmB,yBAACqG,UAAAA;kCAASjF,SAAS0C;4CAChB/E,cAAc;oCACbiC,IAAI;oCACJM,gBAAgB;kCAClB,CAAA;;gCAIH1C,iBACCoB,yBAACsG,gBAAAA;kCAAelF,SAAS,MAAMa,eAAe,UAAUvB,KAAAA;4CACrD3B,cAAc;oCACbiC,IAAI;oCACJM,gBAAgB;kCAClB,CAAA;;;;;;;sBAOTzC,iBACCmB,yBAACuG,YAAAA;wBACCrG,YAAW;wBACXsE,WAAS;wBACTgC,SAAQ;wBACRC,mBAAiB/D;wBACjBsB,KAAKjB;wBACL2D,OAAO3H,cAAc;0BACnBiC,IAAI;0BACJM,gBAAgB;wBAClB,CAAA;wBACAF,SAAS,CAACuF,MAAMA,EAAEC,gBAAe;wBACjCC,WAAWjE;wBAEX,cAAA5C,yBAAC8G,eAAAA,CAAAA,CAAAA;;;kBAIL,CAAA,IAAA;;;;gBAGR9G,yBAACiF,UAAUgB,SAAO;4BAChBjG,yBAAC+G,KAAKvB,MAAI;gBAAC5F,KAAK;gBAAG6E,SAAS;gBACzB,UAAA;kBACC;oBACEuC,UAAU,CAACnI;oBACX6H,OAAO3H,cAAc;sBACnBiC,IAAI;sBACJM,gBAAgB;oBAClB,CAAA;oBACAD,MAAM,UAAUX,KAAAA;oBAChBuG,UAAU;oBACVvB,MAAM;oBACNvE,MAAM;kBACR;kBACA;oBACE6F,UAAU,CAACnI;oBACX6H,OAAO3H,cAAc;sBACnBiC,IAAI;sBACJM,gBAAgB;oBAClB,CAAA;oBACAD,MAAM,UAAUX,KAAAA;oBAChBuG,UAAU;oBACVvB,MAAM;oBACNvE,MAAM;kBACR;kBACA;oBACE6F,UAAU,CAACnI;oBACX6H,OAAO3H,cAAc;sBACnBiC,IAAI;sBACJM,gBAAgB;oBAClB,CAAA;oBACAD,MAAM,UAAUX,KAAAA;oBAChBwG,aAAanI,cAAc;sBACzBiC,IAAI;sBACJM,gBAAgB;oBAClB,CAAA;oBACA2F,UAAU;oBACVvB,MAAM;oBACNvE,MAAM;kBACR;kBACAX,IAAI,CAAC,EAAEkF,MAAM,GAAGyB,MAAO,UACvBnH,yBAAC+G,KAAK7B,MAAI;kBAAkBkC,KAAK1B;kBAAM/F,WAAU;kBAASW,YAAW;kBACnE,cAAAN,yBAACqH,eAAAA;oBAAe,GAAGF;;gBADLA,GAAAA,MAAM9F,IAAI,CAAA;;;;;;;;AAW5C;AAEA,IAAMqD,gBAAgBnD,GAAO0D,UAAUO,IAAI;;MAErC,CAAC,EAAE8B,OAAOxC,OAAM,MAAQA,SAASwC,MAAMC,OAAOC,YAAYF,MAAMC,OAAOE,UAAU;;AAGvF,IAAMnB,iBAAiB/E,GAAO8E,QAAAA;WACnB,CAAC,EAAEiB,MAAK,MAAOA,MAAMC,OAAOC,SAAS;;AAKhD,IAAM/B,qBAAqBlE,GAAOgE,KAAKH,OAAO;;;wBAGtB,CAAC,EAAEkC,MAAK,MAAOA,MAAMC,OAAOG,UAAU;;;;;;;AAc9D,IAAML,gBAAgB,CAACM,UAAAA;AACrB,UAAQA,MAAMxG,MAAI;IAChB,KAAK;AACH,iBAAOnB,yBAAC4H,eAAAA;QAAe,GAAGD;;IAC5B,KAAK;AACH,iBAAO3H,yBAAC6H,kBAAAA;QAAkB,GAAGF;;IAC/B;AACE,iBAAO3H,yBAAC8H,uBAAAA;QAAoB,GAAGH;;EACnC;AACF;AAWA,IAAMC,gBAAgB,CAAC,EAAEZ,UAAUN,OAAOrF,MAAM4F,SAAQ,MAAsB;AAC5E,QAAM,EAAElI,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEM,OAAOyC,OAAOgG,SAAQ,IAAKvI,SAAiB6B,IAAAA;AAEpD,QAAM2G,eAAeC,iBAAiBzH,IAAI,CAAC,EAAE0H,KAAK7G,MAAAA,MAAI,OAAQ;IAC5D/B,OAAO4I;IACPxB,OAAO3H,cACL;MACEiC,IAAI;MACJM,gBAAgB;OAElB;MAAED,MAAAA;IAAK,CAAA;IAETI,OAAOyG;IACT;AAEA,QAAM,EAAEC,eAAc,IAAKC,mBAAmB9I,KAAAA,KAAU,CAAA;AAExD,aACEG,0BAAC4I,MAAM7C,MAAI;IAACzD;IAAcV;IAAY4F;;UACpCjH,yBAACqI,MAAMC,OAAK;QAAE5B,UAAAA;;UACd1G,yBAACuI,cAAAA;QACCvB;QACAe,UAAU,CAACS,MAAAA;AACTT,mBAAS1G,MAAMmH,EAAEC,SAAQ,CAAA;QAC3B;QACAnJ,OAAOA,+BAAOoJ;QACdC,eACE3I,yBAACN,MAAAA;UACCa,KAAI;UACJJ,QAAQ;UACRD,YAAYZ;UACZ+E,aAAa8D,mBAAmB,aAAa,eAAe;UAC5D3D,WAAS;UACToE,QAAQ;UACR/I,OAAO;;kBAIVmI,aAAaxH,IAAI,CAAC,EAAElB,OAAAA,QAAOoH,OAAAA,QAAOjF,MAAK,MAAE;AACxC,gBAAM,EAAE0G,gBAAAA,gBAAc,IAAKC,mBAAmB3G,KAAAA,KAAU,CAAA;AAExD,qBACEzB,yBAAC6I,oBAAAA;YACCvJ,OAAOA;YAEPqJ,eACE3I,yBAACN,MAAAA;cACCa,KAAI;cACJJ,QAAQ;cACRD,YAAYuB;cACZ4C,aAAa8D,oBAAmB,aAAa,eAAe;cAC5D3D,WAAS;cACToE,QAAQ;cACR/I,OAAO;;YAIV6G,UAAAA;UAbIpH,GAAAA,MAAAA;QAgBX,CAAA;;UAEFU,yBAACqI,MAAMS,OAAK,CAAA,CAAA;;;AAGlB;AAUA,IAAMjB,mBAAmB,CAAC,EAAEb,UAAU3F,MAAM6F,aAAaD,SAAQ,MAAyB;AACxF,QAAM,EAAElI,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAE+J,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,CAACC,4BAA4BC,6BAAAA,IAAuCtH,gBAAS,KAAA;AACnF,QAAM,EAAEtC,QAAQ,CAAA,GAAIyC,OAAOgG,SAAQ,IAAKvI,SAA4B6B,IAAAA;AACpE,QAAM8H,YAAY/J,QAAyB,oBAAoB,CAACC,UAAUA,MAAM2F,OAAOzF,MAAM;AAC7F,QAAM6J,oBAAoBhK,QAAQ,oBAAoB,CAACC,UAAUA,MAAM0I,QAAQ;AAC/E,QAAMsB,kBAAwBC,cAAO,CAAA;AAErC,QAAM,EAAEC,MAAMC,QAAQ,CAAA,GAAIC,WAAW1H,OAAO2H,cAAa,IAAKC,sBAAAA;AAI9D,QAAMC,iBAAgBJ,+BAAOK,OAAO,CAACC,SAASA,KAAKC,SAAS,0BAAyB,CAAA;AAErFrG,EAAMC,iBAAU,MAAA;AACd,QACE,CAAC8F,aACDC,iBACA,YAAYA,iBACZA,cAAcM,UAAU,OACxBX,gBAAgBY,YAAY,GAC5B;AACAZ,sBAAgBY,UAAU;AAE1BlB,yBAAmB;QACjBmB,iBAAiB;QACjB/I,MAAM;QACNgJ,SAASpL,cAAc;UACrBiC,IAAI;UACJM,gBAAgB;QAClB,CAAA;MACF,CAAA;IACF;KACC;IAACvC;IAAe0K;IAAWD;IAAOT;IAAoBW;EAAc,CAAA;AAEvE,MAAI,CAACD,aAAaG,cAAchJ,WAAW,GAAG;AAC5C,eACEnB,0BAAC4I,MAAM7C,MAAI;MACTnE;MACA+I,MAAMrL,cAAc;QAClBiC,IAAI;QACJM,gBAAgB;MAClB,CAAA;MACA2F;;YAEAjH,yBAACqI,MAAMC,OAAK;oBACTvJ,cAAc;YACbiC,IAAI;YACJM,gBAAgB;UAClB,CAAA;;YAEFtB,yBAACqK,WAAAA;UACCrD,UAAQ;UACRE,aAAanI,cAAc;YACzBiC,IAAI;YACJM,gBAAgB;UAClB,CAAA;UACAgJ,iBAAatK,yBAACuK,eAAAA;YAAWC,MAAK;;UAC9BrJ,MAAK;UACL7B,OAAM;;YAERU,yBAACqI,MAAMoC,MAAI,CAAA,CAAA;;;EAGjB;AAEA,aACEzK,yBAAAsF,8BAAA;IACE,cAAA7F,0BAACC,MAAAA;MAAKY,YAAW;MAAWV,KAAK;;YAC/BI,yBAAC0K,mBAAAA;UAAkBC,MAAM;wBACvBlL,0BAAC4I,MAAM7C,MAAI;YAACzD;YAAcV;YAAY4F,UAAQ;;kBAC5CjH,yBAACqI,MAAMC,OAAK;0BACTvJ,cAAc;kBACbiC,IAAI;kBACJM,gBAAgB;gBAClB,CAAA;;kBAEFtB,yBAAC4K,aAAAA;gBACC5D;gBACAe,UAAU,CAAC/C,WAAAA;AAIT,wBAAMxD,cAAcwD,OAAOxE,IAAI,CAAClB,YAAW;oBACzCwK,MAAMe,SAASvL,QAAO,EAAA;oBACtBwL,QAAQ;oBACV;AAEA/C,2BAAS1G,MAAMG,WAAAA;gBACjB;gBACA0F;;gBAEA5H,OAAOA,MAAMkB,IAAI,CAACuK,eAAe,GAAGA,WAAWjB,IAAI,EAAE;gBACrDkB,UAAQ;gBAER,cAAAhL,yBAACiL,kBAAAA;kBACCvE,OAAO3H,cAAc;oBACnBiC,IAAI;oBACJM,gBAAgB;kBAClB,CAAA;kBACA0D,QAAQ4E,cAAcpJ,IAAI,CAAC0K,MAAM,GAAGA,EAAElK,EAAE,EAAE;4BAEzC4I,cAAcpJ,IAAI,CAACsJ,SAAAA;AAClB,+BACE9J,yBAACmL,cAAAA;sBAA2B7L,OAAO,GAAGwK,KAAK9I,EAAE;sBAC1C8I,UAAAA,KAAKzI;oBADWyI,GAAAA,KAAK9I,EAAE;kBAI9B,CAAA;;;kBAGJhB,yBAACqI,MAAMS,OAAK,CAAA,CAAA;;;;YAGhBrJ,0BAAC2L,OAAO5F,MAAI;UAAC6F,MAAMpC;UAA4BqC,cAAcpC;;gBAC3DlJ,yBAACoL,OAAOhG,SAAO;cACb,cAAApF,yBAACuG,YAAAA;gBACCS;gBACAN,OAAO3H,cAAc;kBACnBiC,IAAI;kBACJM,gBAAgB;gBAClB,CAAA;gBACAoE,MAAK;gBAEL,cAAA1F,yBAACuL,eAAAA,CAAAA,CAAAA;;;gBAGLvL,yBAACwL,eAAAA;cACCC,WAAW,MAAA;AACTrC,kCACE,UACAD,UAAU3I,IAAI,CAACC,WAAW;kBACxB,GAAGA;kBACHe,aAAalC;kBACf,CAAA;AAGF4J,8CAA8B,KAAA;AAC9BH,mCAAmB;kBACjB5H,MAAM;kBACNgJ,SAASpL,cAAc;oBACrBiC,IAAI;oBACJM,gBAAgB;kBAClB,CAAA;gBACF,CAAA;cACF;cACAkF,SAAQ;wBAEPzH,cAAc;gBACbiC,IAAI;gBACJM,gBACE;cACJ,CAAA;;;;;;;AAMZ;AAEA,IAAM6J,eAAe5J,GAAOmK,iBAAAA;kBACV,CAAC,EAAEpE,MAAK,MAAOA,MAAMqE,OAAO,CAAA,CAAE;;AAIhD,IAAMjB,oBAAoBnJ,GAAO7B,IAAAA;;;;;;;;ACjoBjC,IAAMkM,qBAAqB,CAAC,EAAEC,YAAY,KAAI,MAA2B;AACvE,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEC,0BAACC,KAAKC,MAAI;IAACC,YAAW;IAAWC,WAAS;IAACC,KAAK;IAAGC,SAAS;IAAGC,QAAO;;UACpEC,yBAACP,KAAKQ,MAAI;QAACC,KAAK;QAAGC,WAAU;QAASC,YAAW;QAC/C,cAAAJ,yBAACK,uBAAAA;UACCC,UAAU,CAACjB;UACXkB,OAAOjB,cAAc;YACnBkB,IAAI;YACJC,gBAAgB;UAClB,CAAA;UACAC,MAAK;UACLC,UAAQ;UACRC,MAAK;;;UAGTZ,yBAACP,KAAKQ,MAAI;QAACC,KAAK;QAAGC,WAAU;QAASC,YAAW;QAC/C,cAAAJ,yBAACa,sBAAAA;UAAqBP,UAAU,CAACjB;;;UAEnCW,yBAACP,KAAKQ,MAAI;QAACC,KAAK;QAAGC,WAAU;QAASC,YAAW;QAC/C,cAAAJ,yBAACc,eAAAA;UAAcR,UAAU,CAACjB;;;;;AAIlC;AASA,IAAMwB,uBAAuB,CAAC,EAAEP,SAAQ,MAA6B;AACnE,QAAM,EAAEhB,eAAeyB,OAAM,IAAKxB,QAAAA;AAClC,QAAM,EAAEyB,MAAMC,cAAcC,UAAS,IAAKC,wBAAAA;AAC1C,QAAM,EAAEC,UAAS,IAAKC,mBAAAA;AACtB,QAAMC,kBAAkBC,QAAQ,wBAAwB,CAACC,UAAUA,MAAMC,MAAM;AAE/E,QAAM,EAAEC,OAAOC,OAAOC,SAAQ,IAAKC,SAAS,cAAA;AAE5C,QAAMC,YAAYC,YAAYhB,QAAQ;IACpCiB,aAAa;EACf,CAAA;AAEA,QAAMC,aACJ3B,YACAY,aACA,CAACD,gBACAA,aAAaiB,eAAeC,WAAW,KAAKlB,aAAamB,WAAWD,WAAW;AAElF,QAAME,oBAAmBpB,6CAAciB,mBAAkB,CAAA,GACtDI,SAAS,CAACC,GAAGC,MAAMV,UAAUW,QAAQF,EAAEG,KAAKC,aAAaH,EAAEE,KAAKC,WAAW,CAC3EC,EAAAA,IAAI,CAACC,iBAAiB;IACrBtC,OAAOsC,YAAYH,KAAKC;IACxBhB,OAAOkB,YAAYC;IACrB;AAEF,QAAMC,gBAAe9B,6CAAcmB,eAAc,CAAA,GAAIQ,IAAI,CAACC,iBAAiB;IACzEtC,OAAOsC,YAAYH,KAAKC;IACxBhB,OAAOkB,YAAYC;IACrB;AAEA,aACEtD,0BAACwD,MAAMtD,MAAI;IAACgC;IAAchB,MAAM;;UAC9BV,yBAACgD,MAAMC,OAAK;kBACT3D,cAAc;UACbkB,IAAI;UACJC,gBAAgB;QAClB,CAAA;;UAEFT,yBAACkD,aAAAA;QACCC,kBAAkB,CAACxB,WACjBrC,cACE;UACEkB,IAAI;UACJC,gBACE;WAEJ;UAAE2C,OAAOzB,UAAAA,gBAAAA,OAAOQ;QAAO,CAAA;QAG3B7B,UAAU2B;QACVL,UAAU,CAACH,WAAAA;AACTG,mBAAS,gBAAgBH,MAAAA;QAC3B;QACAE;QACA0B,aAAa/D,cAAc;UACzBkB,IAAI;UACJC,gBAAgB;QAClB,CAAA;QAEC,UAAA;aACK4B,gBAAgBF,SAAS,IACzB;YACE;cACE5B,OAAOjB,cAAc;gBACnBkB,IAAI;gBACJC,gBAAgB;cAClB,CAAA;cACA6C,UAAUjB;YACZ;UACD,IACD,CAAA;aAEAU,YAAYZ,SAAS,IACrB;YACE;cACE5B,OAAOjB,cAAc;gBACnBkB,IAAI;gBACJC,gBAAgB;cAClB,CAAA;cACA6C,UAAUP;YACZ;UACD,IACD,CAAA;UACJH,IAAI,CAACW,QAAAA;AACL,qBACEvD,yBAACwD,kBAAAA;YAECjD,OAAOgD,IAAIhD;YACXkB,QAAQ8B,IAAID,SAASV,IAAI,CAACa,UAAUA,MAAM9B,MAAM+B,SAAQ,CAAA;YAEvDH,UAAAA,IAAID,SAASV,IAAI,CAACa,UAAAA;AACjB,oBAAM,EAAE/C,MAAMiD,qBAAoB,KAChCvC,uCAAWwC,KACT,CAACC,cACGvC,mBAAmBuC,SAASrD,OAAOc,gBAAgBd,MACnD,CAACc,oBACHuC,SAAS5C,aAAa6C,SAASL,MAAM9B,KAAK,OACzC,CAAA;AAEP,yBACE3B,yBAAC+D,eAAAA;gBAA+BpC,OAAO8B,MAAM9B;gBAC3C,cAAA3B,yBAACgE,YAAAA;kBAGG1E;;oBAAAA,cACE;sBACEkB,IAAI;sBACJC,gBACE;uBAEJ;sBACEF,OAAOkD,MAAMlD;sBACbG,MAAMiD;sBACNM,IAAI,IAAIX,iBACNtD,yBAACgE,YAAAA;wBAAWE,KAAI;wBAAKC,YAAW;wBAC7Bb;;sBAGLc,GAAG,IAAId,iBACLtD,yBAACqE,uBAAAA;wBAAuBf;;oBAE5B,CAAA;;;cArBWG,GAAAA,MAAM9B,KAAK;YA2BlC,CAAA;UAzCK4B,GAAAA,IAAIhD,KAAK;QA4CpB,CAAA;;;;AAIR;AAEA,IAAMwD,gBAAeO,GAAOC,iBAAAA;kBACV,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAO,CAAA,CAAE;;AAGhD,IAAMJ,wBAAwBC,GAAON,UAAAA;;;AAWrC,IAAMlD,gBAAgB,CAAC,EAAER,SAAQ,MAAsB;AACrD,QAAM,EAAEqB,OAAO+C,SAAS,CAAA,EAAE,IAAK7C,SAA0B,QAAA;AACzD,QAAM,EAAEvC,cAAa,IAAKC,QAAAA;AAE1B,QAAM,EAAEmC,OAAOC,OAAOC,SAAQ,IAAKC,SAAS,wBAAA;AAG5C,QAAM8C,cAAcD,OAAOE,OAAO,CAACC,UAAUA,MAAMnE,IAAI;AAEvD,aACElB,0BAACwD,MAAMtD,MAAI;IACTgC;IACAhB,MAAK;IACLoE,MAAMxF,cAAc;MAClBkB,IAAI;MACJC,gBACE;IACJ,CAAA;;UAEAT,yBAACgD,MAAMC,OAAK;kBACT3D,cAAc;UACbkB,IAAI;UACJC,gBAAgB;QAClB,CAAA;;UAEFjB,0BAACuF,cAAAA;QACCzE;QACAsB,UAAU,CAACD,WAAAA;AACTC,mBAAS,0BAA0BD,MAAAA;QACrC;QACAA;;cAEA3B,yBAACgF,oBAAAA;YAAmBrD,OAAO;sBACxBrC,cAAc;cACbkB,IAAI;cACJC,gBAAgB;YAClB,CAAA;;UAEDkE,YAAY/B,IAAI,CAACiC,OAAOT,MAAAA;;AACvBpE,gDAACgF,oBAAAA;cAECrD,SAAOkD,WAAMrE,OAANqE,mBAAUnB,eAAcmB,MAAMI;cAEpCJ,UAAAA,MAAMnE;eAHF,0BAA0BmE,MAAMrE,MAAMqE,MAAMI,YAAY,EAAE;WAAA;;;UAOrEjF,yBAACgD,MAAMkC,MAAI,CAAA,CAAA;;;AAGjB;;;AC9NA,IAAMC,kBAAsBC,QAAO;EACjCC,cAAkBC,QAAK,EAAGC,GAAOC,OAAM,CAAA;EACvCC,MACGD,OAAM,EACNE,IAAI,KAAK;IACRC,IAAI;IACJC,gBAAgB;GAEjBC,EAAAA,SAAQ,EACRC,SAAQ;EACXC,QACGT,QAAK,EACLC,GACKH,QAAM,EAAGY,MAAM;IACjBP,MACGD,OAAM,EACNM,SAAQ,EACRD,SAAS;MACRF,IAAI;MACJC,gBAAgB;KAEjBF,EAAAA,IAAI,KAAK;MACRC,IAAI;MACJC,gBAAgB;KAEjBK,EAAAA,KACC,eACA;MACEN,IAAI;MACJC,gBAAgB;IAClB,GACA,CAACM,WAAWC,YAAAA;AAEV,YAAM,EAAEJ,OAAM,IAAKI,QAAQC,KAAK,CAAE,EAACC;AAEnC,aAAON,OAAOO,OAAO,CAACC,UAAiBA,MAAMd,SAASS,SAAWM,EAAAA,WAAW;IAC9E,CAAA;IAEJC,OACGjB,OAAM,EACNM,SAAQ,EACRD,SAAS;MACRF,IAAI;MACJC,gBAAgB;IAClB,CAAA,EACCc,QAAQ,6BAAA;IAEXC,aACGrB,QACKF,QAAO;MACTwB,MACGC,QAAM,EACNC,OAAM,EACNC,UAAU;QACTpB,IAAI;QACJC,gBAAgB;MAClB,CAAA,EACCC,SAAQ;MACXmB,QAAYxB,OAAM,EAAGK,SAAS;QAC5BF,IAAI;QACJC,gBAAgB;MAClB,CAAA;IACF,CAAA,CAAA,EAEDkB,OAAM;EACX,CAAA,CAAA,EAEDG,IAAI,CAAA;EACPC,wBAA4B1B,OAAM,EAAGM,SAAQ;AAC/C,CAAA;AAEA,IAAMqB,WAAW,MAAA;AACf,QAAM,EAAExB,KAAK,GAAE,IAAKyB,UAAAA;AACpB,QAAMC,qBAAqB1B,OAAO;AAClC,QAAM,EAAE2B,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,iCAAiCC,uBAAsB,IAAKC,mBAAAA;AACpE,QAAMC,WAAWC,YAAAA;AACjB,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EACJC,WAAWC,mBACXC,MACAC,WACAC,OACAC,QACAC,QAAAA,QAAM,IACJC,mBAAAA;AACJ,QAAM3B,cAAc4B,iBAClB,CAACC,UAAAA;;AAAUA,uBAAMC,UAAU9B,YAAY,UAAA,MAA5B6B,mBAA0C;GAAmB;AAE1E,QAAM,EACJE,gBAAgB,EAAEC,WAAWC,WAAWC,UAAS,EAAE,IACjDC,QAAQnC,WAAAA;AAEZ,QAAM,CAACoC,aAAaC,cAAAA,IAAwBC,gBAGzC,CAAA,CAAC;AACJ,QAAM,EAAEC,YAAYnB,WAAWoB,iBAAgB,IAAKC,iBAAAA;AACpD,QAAM,CAACC,gBAAgBC,iBAAAA,IAA2BL,gBAAsC,IAAA;AAExF,QAAMM,kBAAkBrB,uCAAWsB,KAAK,CAACC,aAAaA,SAAS9D,OAAO+D,SAAS/D,IAAI,EAAA;AACnF,QAAMgE,iCAAiCzB,uCACnC5B,OAAO,CAACmD,aAAaA,SAAS9D,OAAO+D,SAAS/D,IAAI,EACnDiE,GAAAA,QAAQ,CAACH,aAAaA,SAASpE;AAElC,QAAMwE,SAASX,WAAmB,kBAAA;AAClC,QAAMY,oBAAoBD,iCAASE;AACnC,QAAMC,oBAAoBH,iCAASI;AASnC,QAAMC,aAAa,OAAOC,MAAkBC,YAAAA;;AAC1C,QAAI;AACF,YAAM,EAAElD,wBAAwB,GAAGmD,KAAAA,IAASF;AAC5C,YAAMG,6BACJpD,2BAA2B,KACvB,QACAmD,UAAKtE,OAAOyD,KACV,CAACjD,UACCA,MAAMZ,OAAO4E,OAAOrD,sBAAAA,KACpBX,MAAMiE,iBAAiBtD,sBACxBzB,MAJH4E,mBAIG5E;AAET,UAAI,CAAC4B,oBAAoB;AACvB,cAAMoD,MAAM,MAAMrC,OAAOzC,IAAI;UAC3B,GAAG0E;;;;UAIHtE,QAAQsE,KAAKtE,OAAO2E,IAAI,CAACnE,UAAAA;;AACvB,gBAAIoE,wBAAwB;AAC5B,kBAAMC,eAAcrB,MAAAA,mDAAiBxD,WAAjBwD,gBAAAA,IAAyBC,KAC3C,CAACoB,iBAAgBA,aAAYjF,QAAOY,+BAAOZ;AAE7C,gBAAIiF,aAAa;AACfD,wCACEC,iBAAYjE,gBAAZiE,mBAAyBpE,cAAWD,WAAMI,gBAANJ,mBAAmBC,WACvD,GAACoE,iBAAYjE,gBAAZiE,mBAAyBC,MACxB,CAACC,qBAAAA;;AACC,wBAAC,GAACvE,MAAAA,MAAMI,gBAANJ,gBAAAA,IAAmBiD,KACnB,CAACuB,eAAeA,WAAWnE,SAASkE,iBAAiBlE;;YAG/D;AACA,mBAAO;cACL,GAAGL;cACHI,aAAagE,wBAAwBpE,MAAMI,cAAcqE;YAC3D;UACF,CAAA;UACAV;QACF,CAAA;AAEA,YAAI,WAAWG,OAAOQ,iBAAiBR,IAAItC,KAAK,KAAKsC,IAAItC,MAAM1C,SAAS,mBAAmB;AACzF2E,kBAAQc,UAAUzD,uBAAuBgD,IAAItC,KAAK,CAAA;QACpD;aACK;AACL,cAAMsC,MAAM,MAAMpC,QAAO;UACvB,GAAGgC;UACHC;QACF,CAAA;AAEA,YAAI,WAAWG,OAAOQ,iBAAiBR,IAAItC,KAAK,KAAKsC,IAAItC,MAAM1C,SAAS,mBAAmB;AACzF2E,kBAAQc,UAAUzD,uBAAuBgD,IAAItC,KAAK,CAAA;mBACzC,UAAUsC,KAAK;AACxB9C,mBAAS,MAAM8C,IAAIN,KAAKxE,EAAE,IAAI;YAAEwF,SAAS;UAAK,CAAA;QAChD;MACF;IACF,SAAShD,QAAO;AACdN,yBAAmB;QACjBuD,MAAM;QACNC,SAAS/D,cAAc;UACrB3B,IAAI;UACJC,gBAAgB;QAClB,CAAA;MACF,CAAA;IACF;AACAoD,mBAAe,CAAA,CAAC;EAClB;AAEA,QAAMsC,4BACJ,CAACnB,MAAkBC,YAA4C,YAAA;AAC7D,UAAMF,WAAWC,MAAMC,OAAAA;EACzB;AAEF,QAAMmB,qBAAqB,MAAA;AACzBvC,mBAAe,CAAA,CAAC;EAClB;AAEA,QAAMwC,eAAkD,OAAOrB,MAAMC,YAAAA;AACnE,UAAMqB,4BAA4BtB,KAAK9E,aAAaqG,KAAK,CAACC,gBACxDhC,iFAAgCiC,SAASD,YAAAA;AAE3C,UAAME,yBACJ,CAACxE,sBACD,EAACkC,mDAAiBxD,OAAO8E,MAAM,CAACtE,UAC9B4D,KAAKpE,OAAO2F,KAAK,CAACI,aAAaA,SAASnG,OAAOY,MAAMZ,EAAE;AAG3D,QAAIsC,QAAQ6B,sBAAqB7B,6BAAM8D,iBAAgBrC,SAASI,mBAAmB,EAAK,GAAA;AAMtFR,wBAAkB,UAAA;IAMjB,WAEDa,KAAKpE,UACLiE,qBACAG,KAAKpE,OAAOS,SAASkD,SAASM,mBAAmB,EACjD,GAAA;AACAV,wBAAkB,OAAA;eACTuC,0BAA0BJ,2BAA2B;AAC9D,UAAII,wBAAwB;AAC1B7C,uBAAe,CAACgD,UAAU;UAAE,GAAGA;UAAMH,wBAAwB;UAAK;MACpE;AAEA,UAAIJ,2BAA2B;AAC7BzC,uBAAe,CAACgD,UAAU;UAAE,GAAGA;UAAMC,2BAA2B;UAAK;MACvE;WACK;AACL,YAAM/B,WAAWC,MAAMC,OAAAA;IACzB;EACF;AAcA8B,EAAMC,iBAAU,MAAA;AACd,QAAI,CAACnE,qBAAqB,CAACmB,kBAAkB;AAC3C,UAAIlB,QAAQ6B,sBAAqB7B,6BAAM8D,iBAAgBrC,SAASI,mBAAmB,EAAK,GAAA;AACtFR,0BAAkB,UAAA;MACpB,WACEC,mBACAA,gBAAgBxD,UAChBiE,qBACAT,gBAAgBxD,OAAOS,SAASkD,SAASM,mBAAmB,EAC5D,GAAA;AACAV,0BAAkB,OAAA;MACpB;IACF;KACC;IACDC;IACAJ;IACAnB;IACA6B;IACA5B;IACA6B;IACAE;EACD,CAAA;AAED,QAAMoC,gBAAkCC,eAAQ,MAAA;;AAC9C,QAAIhF,sBAAsB,CAACkC,iBAAiB;AAC1C,aAAO;QACL9D,MAAM;QACNM,QAAQ,CAAA;QACRV,cAAc,CAAA;QACd6B,wBAAwB;MAC1B;WACK;AACL,aAAO;QACLzB,MAAM8D,gBAAgB9D;QACtBM,QAAQuG,mBAAmB/C,gBAAgBxD,MAAM;QACjDV,cAAckE,gBAAgBlE;QAC9B6B,0BAAwBqC,qBAAgBrC,2BAAhBqC,mBAAwC5D,GAAG4G,eAAc;MACnF;IACF;KACC;IAAChD;IAAiBlC;EAAmB,CAAA;AAExC,MAAIW,mBAAmB;AACrB,eAAOwE,yBAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,MAAIvE,OAAO;AACT,eAAOqE,yBAACC,KAAKE,OAAK,CAAA,CAAA;EACpB;AAEA,aACEC,0BAAAC,8BAAA;;UACEL,yBAACM,mBAAwB,CAAA,CAAA;UAEzBN,yBAACO,MAAAA;QACCC,QAAQ3F,qBAAqB,SAAS;QACtC+E;QACAa,kBAAkB9H;QAClB+H,UAAU1B;kBAET,CAAC,EAAE2B,UAAUC,cAAcC,QAAQnC,UAAS,MAAE;;AAC7C0B,+CAAAC,8BAAA;;kBACEL,yBAACM,QAAa;gBACZQ,sBAAkBd,yBAACe,YAAAA;kBAAWC,UAAS;;gBACvCC,eACE7E,aAAaC,gBACX2D,yBAACkB,QAAAA;kBACCC,eAAWnB,yBAACoB,eAAAA,CAAAA,CAAAA;kBACZxC,MAAK;kBACLyC,UAAU,CAACV,YAAYC,gBAAgBC,OAAOtH,OAAOS,WAAW;;;kBAGhEsH,SAAS,CAACC,QAAQC,OAAOC,KAAKlF,WAAavC,EAAAA,SAAS,CAAM4G,KAAAA;4BAEzD9F,cAAc;oBACb3B,IAAI;oBACJC,gBAAgB;kBAClB,CAAA;gBAEA,CAAA,IAAA;gBAENsI,UAAU5G,cACR;kBACE3B,IAAI;kBACJC,gBAAgB;mBAElB;kBAAEuI,SAAO5E,wDAAiBxD,WAAjBwD,mBAAyB/C,WAAU;gBAAE,CAAA;gBAEhD4H,QACE7E,mDAAiB9D,SACjB6B,cAAc;kBACZ3B,IAAI;kBACJC,gBAAgB;gBAClB,CAAA;;kBAGJ4G,yBAACM,MAAW;gBACV,cAAAF,0BAACyB,MAAAA;kBAAKC,YAAW;kBAAUC,WAAU;kBAASC,KAAK;;wBACjDhC,yBAACiC,oBAAAA;sBAAmB7F,WAAWA,aAAaC;;wBAC5C2D,yBAACkC,QAAAA;sBACC/F;sBACAC,WAAWA,aAAaC;sBACxB8F,YAAYtH;;;;;kBAIlBmF,yBAACoC,OAAOC,MAAI;gBACVC,MAAMd,OAAOC,KAAKlF,WAAAA,EAAavC,SAAS;gBACxCuI,cAAcxD;gBAEd,cAAAiB,yBAACwC,eAAAA;kBAAcC,WAAW3D,0BAA0B+B,QAAQ;oBAAEnC;kBAAU,CAAA;kBACtE,cAAA0B,0BAACyB,MAAAA;oBAAKE,WAAU;oBAASC,KAAK;;sBAC3BzF,YAAY8C,8BACXW,yBAAC0C,YAAAA;wBAAWC,WAAU;wBAASC,SAAQ;kCACpC9H,cAAc;0BACb3B,IAAI;0BACJC,gBACE;wBACJ,CAAA;;sBAIHmD,YAAYkD,iCACXO,yBAAC0C,YAAAA;wBAAWC,WAAU;wBAASC,SAAQ;kCACpC9H,cACC;0BACE3B,IAAI;0BACJC,gBACE;2BAEJ;0BACEuI,QACExE,iFAAgCrD,OAAO,CAACqF,gBACtC0B,OAAOhI,aAAauG,SAASD,WAAAA,GAC7BnF,WAAU;wBAChB,CAAA;;0BAKNgG,yBAAC0C,YAAAA;wBAAWC,WAAU;wBAASC,SAAQ;kCACpC9H,cAAc;0BACb3B,IAAI;0BACJC,gBAAgB;wBAClB,CAAA;;;;;;;;;;UASdgH,0BAACyC,YAAYR,MAAI;QACfC,MAAMzF,mBAAmB;QACzB0F,cAAc,MAAMzF,kBAAkB,IAAA;;cAEtCkD,yBAAC6C,YAAYC,OAAK;sBACfhI,cAAc;cACb3B,IAAI;cACJC,gBAAgB;YAClB,CAAA;;cAGF4G,yBAAC6C,YAAYE,MAAI;sBACdjI,cAAc;cACb3B,IAAI;cACJC,gBAAgB;YAClB,CAAA;;;;UAIJgH,0BAACyC,YAAYR,MAAI;QACfC,MAAMzF,mBAAmB;QACzB0F,cAAc,MAAMzF,kBAAkB,IAAA;;cAEtCkD,yBAAC6C,YAAYC,OAAK;sBACfhI,cAAc;cACb3B,IAAI;cACJC,gBAAgB;YAClB,CAAA;;cAGF4G,yBAAC6C,YAAYE,MAAI;sBACdjI,cAAc;cACb3B,IAAI;cACJC,gBAAgB;YAClB,CAAA;;;;;;AAKV;AAEA,IAAM0G,qBAAqB,CAACnC,SAAAA;AAC1B,QAAM8D,OAAOuB,qBAAqBxE,QAAWA,QAAWb,KAAK3D,MAAM;AAEnE,SAAO2D,KAAKO,IAAI,CAAC+E,OAAOC,WAAW;IACjC,GAAGD;IACHjF,cAAcyD,KAAKyB,KAAM;IAC3B;AACF;AAIkG,IAE5FC,oBAAoB,MAAA;AACxB,QAAMhJ,cAAc4B,iBAAiB,CAACC,UAAAA;;AACpC,UAAM,EACJH,QAAAA,UAAS,CAAA,GACTD,SAAS,CAAA,GACTwH,OAAO,CAAA,EAAE,MACPpH,WAAMC,UAAU9B,YAAYkJ,aAA5BrH,mBAAuC,wBAAuB,CAAA;AAElE,WAAO;MAAIH,GAAAA;MAAWD,GAAAA;MAAWwH,GAAAA;IAAK;EACxC,CAAA;AAEA,aACEpD,yBAACC,KAAKqD,SAAO;IAACnJ;IACZ,cAAA6F,yBAACrF,UAAAA,CAAAA,CAAAA;;AAGP;", "names": ["adminApi", "reviewWorkflowsApi", "injectEndpoints", "endpoints", "builder", "getAdminRoles", "query", "url", "method", "transformResponse", "res", "data", "useGetAdminRolesQuery", "useKeyboardDragAndDrop", "active", "index", "onCancel", "onDropItem", "onGrabItem", "onMoveItem", "isSelected", "setIsSelected", "useState", "handleMove", "movement", "handleDragClick", "handleCancel", "handleKeyDown", "e", "key", "preventDefault", "DIRECTIONS", "UPWARD", "DOWNWARD", "DROP_SENSITIVITY", "REGULAR", "IMMEDIATE", "useDragAndDrop", "active", "type", "index", "item", "onStart", "onEnd", "onGrabItem", "onDropItem", "onCancel", "onMoveItem", "dropSensitivity", "objectRef", "useRef", "handlerId", "isOver", "dropRef", "useDrop", "accept", "collect", "monitor", "getHandlerId", "shallow", "drop", "draggedIndex", "newIndex", "hover", "current", "dragIndex", "hoverBoundingRect", "getBoundingClientRect", "hoverMiddleY", "bottom", "top", "clientOffset", "getClientOffset", "hoverClientY", "y", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON>", "Math", "min", "length", "areEqual", "is<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i", "getDragDirection", "isDragging", "didDrop", "getInitialClientOffset", "deltaY", "direction", "dragRef", "dragPreviewRef", "useDrag", "width", "end", "canDrag", "id", "getItem", "undefined", "initialOffset", "currentOffset", "handleKeyDown", "useKeyboardDragAndDrop", "isOverDropTarget", "AddStage", "children", "props", "_jsx", "StyledButton", "tag", "background", "borderColor", "paddingBottom", "paddingLeft", "paddingRight", "paddingTop", "shadow", "Typography", "variant", "fontWeight", "_jsxs", "Flex", "gap", "PlusCircle", "width", "height", "aria-hidden", "styled", "Box", "theme", "colors", "neutral500", "primary600", "Stages", "canDelete", "canUpdate", "isCreating", "formatMessage", "useIntl", "trackUsage", "useTracking", "addFieldRow", "useForm", "state", "value", "stages", "useField", "_jsxs", "Flex", "direction", "gap", "width", "Box", "position", "_jsx", "Background", "background", "height", "left", "top", "alignItems", "tag", "map", "stage", "index", "Stage", "length", "can<PERSON>eorder", "stagesCount", "defaultOpen", "id", "__temp_key__", "AddStage", "type", "onClick", "name", "defaultMessage", "styled", "permissions", "color", "liveText", "setLiveText", "useState", "stageErrors", "errors", "error", "moveFieldRow", "removeFieldRow", "getItemPos", "handleGrabStage", "item", "handleDropStage", "handleCancelDragStage", "handleMoveStage", "newIndex", "oldIndex", "handlerId", "isDragging", "handleKeyDown", "stageRef", "dropRef", "dragRef", "dragPreviewRef", "useDragAndDrop", "onGrabItem", "onDropItem", "onMoveItem", "onCancel", "DRAG_DROP_TYPES", "STAGE", "composedRef", "useComposedRefs", "React", "useEffect", "getEmptyImage", "captureDraggingState", "handleCloneClick", "useId", "ref", "shadow", "VisuallyHidden", "aria-live", "borderStyle", "borderColor", "borderWidth", "display", "hasRadius", "padding", "AccordionRoot", "onValueChange", "defaultValue", "undefined", "$error", "Object", "values", "Accordion", "<PERSON><PERSON>", "Header", "<PERSON><PERSON>", "Actions", "_Fragment", "<PERSON><PERSON>", "Root", "ContextMenuTrigger", "size", "endIcon", "paddingLeft", "paddingRight", "More", "aria-hidden", "focusable", "Content", "popoverPlacement", "zIndex", "SubRoot", "MenuItem", "DeleteMenuItem", "IconButton", "variant", "data-handler-id", "label", "e", "stopPropagation", "onKeyDown", "Drag", "Grid", "disabled", "required", "placeholder", "field", "col", "InputR<PERSON><PERSON>", "theme", "colors", "danger600", "neutral200", "neutral100", "props", "ColorSelector", "PermissionsField", "AdminInput<PERSON><PERSON><PERSON>", "onChange", "colorOptions", "AVAILABLE_COLORS", "hex", "themeColorName", "getStageColorByHex", "Field", "Label", "SingleSelect", "v", "toString", "toUpperCase", "startIcon", "shrink", "SingleSelectOption", "Error", "toggleNotification", "useNotification", "isApplyAllConfirmationOpen", "setIsApplyAllConfirmationOpen", "allStages", "onFormValueChange", "roles<PERSON><PERSON>r<PERSON>ount", "useRef", "data", "roles", "isLoading", "getRolesError", "useGetAdminRolesQuery", "filteredRoles", "filter", "role", "code", "status", "current", "blockTransition", "message", "hint", "TextInput", "startAction", "EyeStriked", "fill", "Hint", "PermissionWrapper", "grow", "MultiSelect", "parseInt", "action", "permission", "withTags", "MultiSelectGroup", "r", "NestedOption", "Dialog", "open", "onOpenChange", "Duplicate", "ConfirmDialog", "onConfirm", "MultiSelectOption", "spaces", "WorkflowAttributes", "canUpdate", "formatMessage", "useIntl", "_jsxs", "Grid", "Root", "background", "hasRadius", "gap", "padding", "shadow", "_jsx", "<PERSON><PERSON>", "col", "direction", "alignItems", "InputR<PERSON><PERSON>", "disabled", "label", "id", "defaultMessage", "name", "required", "type", "ContentTypesSelector", "StageSelector", "locale", "data", "contentTypes", "isLoading", "useGetContentTypesQuery", "workflows", "useReviewWorkflows", "currentWorkflow", "useForm", "state", "values", "error", "value", "onChange", "useField", "formatter", "useCollator", "sensitivity", "isDisabled", "collectionType", "length", "singleType", "collectionTypes", "toSorted", "a", "b", "compare", "info", "displayName", "map", "contentType", "uid", "singleTypes", "Field", "Label", "MultiSelect", "customizeContent", "count", "placeholder", "children", "opt", "MultiSelectGroup", "child", "toString", "assignedWorkflowName", "find", "workflow", "includes", "NestedOption", "Typography", "em", "tag", "fontWeight", "i", "ContentTypeTakeNotice", "styled", "MultiSelectOption", "theme", "spaces", "stages", "validStages", "filter", "stage", "hint", "SingleSelect", "SingleSelectOption", "__temp_key__", "Hint", "WORKFLOW_SCHEMA", "object", "contentTypes", "array", "of", "string", "name", "max", "id", "defaultMessage", "required", "nullable", "stages", "shape", "test", "stageName", "context", "from", "value", "filter", "stage", "length", "color", "matches", "permissions", "role", "number", "strict", "typeError", "action", "min", "stageRequiredToPublish", "EditPage", "useParams", "isCreatingWorkflow", "formatMessage", "useIntl", "_unstableFormatValidationErrors", "formatValidationErrors", "useAPIErrorHandler", "navigate", "useNavigate", "toggleNotification", "useNotification", "isLoading", "isLoadingWorkflow", "meta", "workflows", "error", "update", "create", "useReviewWorkflows", "useTypedSelector", "state", "admin_app", "allowedActions", "canDelete", "canUpdate", "canCreate", "useRBAC", "savePrompts", "setSavePrompts", "useState", "getFeature", "isLicenseLoading", "useLicenseLimits", "showLimitModal", "setShowLimitModal", "currentWorkflow", "find", "workflow", "parseInt", "contentTypesFromOtherWorkflows", "flatMap", "limits", "numberOfWorkflows", "CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME", "stagesPerWorkflow", "CHARGEBEE_STAGES_PER_WORKFLOW_ENTITLEMENT_NAME", "submitForm", "data", "helpers", "rest", "stageRequiredToPublishName", "Number", "__temp_key__", "res", "map", "hasUpdatedPermissions", "serverStage", "every", "serverPermission", "permission", "undefined", "isBaseQueryError", "setErrors", "replace", "type", "message", "handleConfirmDeleteDialog", "handleConfirmClose", "handleSubmit", "isContentTypeReassignment", "some", "contentType", "includes", "hasDeletedServerStages", "newStage", "workflowCount", "prev", "hasReassignedContentTypes", "React", "useEffect", "initialValues", "useMemo", "addTmpKeysToStages", "toString", "_jsx", "Page", "Loading", "Error", "_jsxs", "_Fragment", "Layout", "Form", "method", "validationSchema", "onSubmit", "modified", "isSubmitting", "values", "navigationAction", "BackButton", "fallback", "primaryAction", "<PERSON><PERSON>", "startIcon", "Check", "disabled", "loading", "Boolean", "Object", "keys", "subtitle", "count", "title", "Flex", "alignItems", "direction", "gap", "WorkflowAttributes", "Stages", "isCreating", "Dialog", "Root", "open", "onOpenChange", "ConfirmDialog", "onConfirm", "Typography", "textAlign", "variant", "LimitsModal", "Title", "Body", "generateNKeysBetween", "datum", "index", "ProtectedEditPage", "read", "settings", "Protect"]}