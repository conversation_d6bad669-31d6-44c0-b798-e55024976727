import {
  FormHead,
  LifeSpanInput,
  TokenBox,
  TokenDescription,
  TokenName,
  TokenTypeSelect
} from "./chunk-F2MLD4QC.js";
import {
  useCreateTransferTokenMutation,
  useGetTransferTokenQuery,
  useUpdateTransferTokenMutation
} from "./chunk-YFK6SHIX.js";
import {
  TRANSFER_TOKEN_TYPE
} from "./chunk-7GAX2FTH.js";
import {
  useGuidedTour
} from "./chunk-ROZIXYJG.js";
import {
  Form,
  Formik
} from "./chunk-7MILHJ3J.js";
import {
  errorsTrads
} from "./chunk-IFOFBKTA.js";
import {
  create4 as create,
  create5 as create2,
  create6 as create3
} from "./chunk-376QHLWZ.js";
import {
  useTracking
} from "./chunk-XDCEA27D.js";
import {
  useRBAC
} from "./chunk-ZJEMJY2Q.js";
import {
  Layouts
} from "./chunk-MTTHLNPH.js";
import {
  Page,
  useAPIErrorHandler
} from "./chunk-7LKLOY7A.js";
import {
  useTypedSelector
} from "./chunk-ZOP4VV6J.js";
import {
  isBaseQueryError
} from "./chunk-WH6VCVXU.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  Box,
  Flex,
  Grid,
  Typography,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import {
  useLocation,
  useMatch,
  useNavigate
} from "./chunk-S65ZWNEO.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/TransferTokens/EditView.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var schema = create3().shape({
  name: create().max(100).required(errorsTrads.required.id),
  description: create().nullable(),
  lifespan: create2().integer().min(0).nullable().defined(errorsTrads.required.id),
  permissions: create().required(errorsTrads.required.id)
});
var EditView = () => {
  var _a;
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const navigate = useNavigate();
  const { state: locationState } = useLocation();
  const [transferToken, setTransferToken] = React.useState(locationState && "accessKey" in locationState.transferToken ? {
    ...locationState.transferToken
  } : null);
  const { trackUsage } = useTracking();
  useGuidedTour("EditView", (state) => state.setCurrentStep);
  const permissions = useTypedSelector((state) => {
    var _a2;
    return (_a2 = state.admin_app.permissions.settings) == null ? void 0 : _a2["transfer-tokens"];
  });
  const { allowedActions: { canCreate, canUpdate, canRegenerate } } = useRBAC(permissions);
  const match = useMatch("/settings/transfer-tokens/:id");
  const id = (_a = match == null ? void 0 : match.params) == null ? void 0 : _a.id;
  const isCreating = id === "create";
  const { _unstableFormatAPIError: formatAPIError, _unstableFormatValidationErrors: formatValidationErrors } = useAPIErrorHandler();
  React.useEffect(() => {
    trackUsage(isCreating ? "didAddTokenFromList" : "didEditTokenFromList", {
      tokenType: TRANSFER_TOKEN_TYPE
    });
  }, [
    isCreating,
    trackUsage
  ]);
  const { data, error } = useGetTransferTokenQuery(id, {
    skip: isCreating || transferToken !== null || !id
  });
  React.useEffect(() => {
    if (error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    }
  }, [
    error,
    formatAPIError,
    toggleNotification
  ]);
  React.useEffect(() => {
    if (data) {
      setTransferToken(data);
    }
  }, [
    data
  ]);
  const [createToken] = useCreateTransferTokenMutation();
  const [updateToken] = useUpdateTransferTokenMutation();
  const handleSubmit = async (body, formik) => {
    trackUsage(isCreating ? "willCreateToken" : "willEditToken", {
      tokenType: TRANSFER_TOKEN_TYPE
    });
    const permissions2 = body.permissions.split("-");
    const isPermissionsTransferPermission = (permission) => {
      if (permission.length === 1) {
        return permission[0] === "push" || permission[0] === "pull";
      }
      return permission[0] === "push" && permission[1] === "pull";
    };
    if (isPermissionsTransferPermission(permissions2)) {
      try {
        if (isCreating) {
          const res = await createToken({
            ...body,
            // lifespan must be "null" for unlimited (0 would mean instantly expired and isn't accepted)
            lifespan: (body == null ? void 0 : body.lifespan) && body.lifespan !== "0" ? parseInt(body.lifespan.toString(), 10) : null,
            permissions: permissions2
          });
          if ("error" in res) {
            if (isBaseQueryError(res.error) && res.error.name === "ValidationError") {
              formik.setErrors(formatValidationErrors(res.error));
            } else {
              toggleNotification({
                type: "danger",
                message: formatAPIError(res.error)
              });
            }
            return;
          }
          setTransferToken(res.data);
          toggleNotification({
            type: "success",
            message: formatMessage({
              id: "notification.success.transfertokencreated",
              defaultMessage: "Transfer Token successfully created"
            })
          });
          trackUsage("didCreateToken", {
            type: transferToken == null ? void 0 : transferToken.permissions,
            tokenType: TRANSFER_TOKEN_TYPE
          });
          navigate(`../transfer-tokens/${res.data.id.toString()}`, {
            replace: true,
            state: {
              transferToken: res.data
            }
          });
        } else {
          const res = await updateToken({
            id,
            name: body.name,
            description: body.description,
            permissions: permissions2
          });
          if ("error" in res) {
            if (isBaseQueryError(res.error) && res.error.name === "ValidationError") {
              formik.setErrors(formatValidationErrors(res.error));
            } else {
              toggleNotification({
                type: "danger",
                message: formatAPIError(res.error)
              });
            }
            return;
          }
          setTransferToken(res.data);
          toggleNotification({
            type: "success",
            message: formatMessage({
              id: "notification.success.transfertokenedited",
              defaultMessage: "Transfer Token successfully edited"
            })
          });
          trackUsage("didEditToken", {
            type: transferToken == null ? void 0 : transferToken.permissions,
            tokenType: TRANSFER_TOKEN_TYPE
          });
        }
      } catch (err) {
        toggleNotification({
          type: "danger",
          message: formatMessage({
            id: "notification.error",
            defaultMessage: "Something went wrong"
          })
        });
      }
    }
  };
  const canEditInputs = canUpdate && !isCreating || canCreate && isCreating;
  const isLoading = !isCreating && !transferToken;
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  return (0, import_jsx_runtime.jsxs)(Page.Main, {
    children: [
      (0, import_jsx_runtime.jsx)(Page.Title, {
        children: formatMessage({
          id: "Settings.PageTitle",
          defaultMessage: "Settings - {name}"
        }, {
          name: "Transfer Tokens"
        })
      }),
      (0, import_jsx_runtime.jsx)(Formik, {
        validationSchema: schema,
        validateOnChange: false,
        initialValues: {
          name: (transferToken == null ? void 0 : transferToken.name) || "",
          description: (transferToken == null ? void 0 : transferToken.description) || "",
          lifespan: (transferToken == null ? void 0 : transferToken.lifespan) || null,
          /**
          * We need to cast the permissions to satisfy the type for `permissions`
          * in the request body incase we don't have a transferToken and instead
          * use an empty string.
          */
          permissions: (transferToken == null ? void 0 : transferToken.permissions.join("-")) ?? ""
        },
        enableReinitialize: true,
        onSubmit: (body, actions) => handleSubmit(body, actions),
        children: ({ errors, handleChange, isSubmitting, values }) => {
          return (0, import_jsx_runtime.jsxs)(Form, {
            children: [
              (0, import_jsx_runtime.jsx)(FormHead, {
                title: {
                  id: "Settings.transferTokens.createPage.title",
                  defaultMessage: "TokenCreate Transfer Token"
                },
                token: transferToken,
                setToken: setTransferToken,
                canShowToken: false,
                canEditInputs,
                canRegenerate,
                isSubmitting,
                regenerateUrl: "/admin/transfer/tokens/"
              }),
              (0, import_jsx_runtime.jsx)(Layouts.Content, {
                children: (0, import_jsx_runtime.jsxs)(Flex, {
                  direction: "column",
                  alignItems: "stretch",
                  gap: 6,
                  children: [
                    transferToken && Boolean(transferToken == null ? void 0 : transferToken.name) && "accessKey" in transferToken && (0, import_jsx_runtime.jsx)(TokenBox, {
                      token: transferToken.accessKey,
                      tokenType: TRANSFER_TOKEN_TYPE
                    }),
                    (0, import_jsx_runtime.jsx)(FormTransferTokenContainer, {
                      errors,
                      onChange: handleChange,
                      canEditInputs,
                      isCreating,
                      values,
                      transferToken
                    })
                  ]
                })
              })
            ]
          });
        }
      })
    ]
  });
};
var ProtectedEditView = () => {
  const permissions = useTypedSelector((state) => {
    var _a;
    return (_a = state.admin_app.permissions.settings) == null ? void 0 : _a["transfer-tokens"].read;
  });
  return (0, import_jsx_runtime.jsx)(Page.Protect, {
    permissions,
    children: (0, import_jsx_runtime.jsx)(EditView, {})
  });
};
var FormTransferTokenContainer = ({ errors = {}, onChange, canEditInputs, isCreating, values, transferToken = {} }) => {
  const { formatMessage } = useIntl();
  const typeOptions = [
    {
      value: "push",
      label: {
        id: "Settings.transferTokens.types.push",
        defaultMessage: "Push"
      }
    },
    {
      value: "pull",
      label: {
        id: "Settings.transferTokens.types.pull",
        defaultMessage: "Pull"
      }
    },
    {
      value: "push-pull",
      label: {
        id: "Settings.transferTokens.types.push-pull",
        defaultMessage: "Full Access"
      }
    }
  ];
  return (0, import_jsx_runtime.jsx)(Box, {
    background: "neutral0",
    hasRadius: true,
    shadow: "filterShadow",
    paddingTop: 6,
    paddingBottom: 6,
    paddingLeft: 7,
    paddingRight: 7,
    children: (0, import_jsx_runtime.jsxs)(Flex, {
      direction: "column",
      alignItems: "stretch",
      gap: 4,
      children: [
        (0, import_jsx_runtime.jsx)(Typography, {
          variant: "delta",
          tag: "h2",
          children: formatMessage({
            id: "global.details",
            defaultMessage: "Details"
          })
        }),
        (0, import_jsx_runtime.jsxs)(Grid.Root, {
          gap: 5,
          children: [
            (0, import_jsx_runtime.jsx)(Grid.Item, {
              col: 6,
              xs: 12,
              direction: "column",
              alignItems: "stretch",
              children: (0, import_jsx_runtime.jsx)(TokenName, {
                error: errors["name"],
                value: values["name"],
                canEditInputs,
                onChange
              })
            }, "name"),
            (0, import_jsx_runtime.jsx)(Grid.Item, {
              col: 6,
              xs: 12,
              direction: "column",
              alignItems: "stretch",
              children: (0, import_jsx_runtime.jsx)(TokenDescription, {
                error: errors["description"],
                value: values["description"],
                canEditInputs,
                onChange
              })
            }, "description"),
            (0, import_jsx_runtime.jsx)(Grid.Item, {
              col: 6,
              xs: 12,
              direction: "column",
              alignItems: "stretch",
              children: (0, import_jsx_runtime.jsx)(LifeSpanInput, {
                isCreating,
                error: errors["lifespan"],
                value: values["lifespan"],
                onChange,
                token: transferToken
              })
            }, "lifespan"),
            (0, import_jsx_runtime.jsx)(Grid.Item, {
              col: 6,
              xs: 12,
              direction: "column",
              alignItems: "stretch",
              children: (0, import_jsx_runtime.jsx)(TokenTypeSelect, {
                name: "permissions",
                value: values["permissions"],
                error: errors["permissions"],
                label: {
                  id: "Settings.tokens.form.type",
                  defaultMessage: "Token type"
                },
                // @ts-expect-error – DS Select passes number | string, will be fixed in V2
                onChange: (value) => {
                  onChange({
                    target: {
                      name: "permissions",
                      value
                    }
                  });
                },
                options: typeOptions,
                canEditInputs
              })
            }, "permissions")
          ]
        })
      ]
    })
  });
};

export {
  EditView,
  ProtectedEditView
};
//# sourceMappingURL=chunk-KFD43ASO.js.map
