import {
  PERMISSIONS,
  getTranslation,
  useCreateLocaleMutation,
  useDeleteLocaleMutation,
  useGetDefaultLocalesQuery,
  useGetLocalesQuery,
  useUpdateLocaleMutation
} from "./chunk-DLRJHTTW.js";
import "./chunk-EKXSMIUH.js";
import "./chunk-YV3ONWF5.js";
import "./chunk-UMW22TSS.js";
import "./chunk-4QC3H4VA.js";
import "./chunk-75D2ZJP5.js";
import "./chunk-VCTAT6B3.js";
import "./chunk-ROZIXYJG.js";
import "./chunk-C72RZIDJ.js";
import "./chunk-HZKRK7AR.js";
import "./chunk-LRN6A2UC.js";
import "./chunk-D2TGW5YS.js";
import "./chunk-M27D4U76.js";
import "./chunk-HX66WGOY.js";
import "./chunk-Y4UEUAII.js";
import "./chunk-BN2UQHMJ.js";
import {
  ConfirmDialog
} from "./chunk-NWWGC2Z2.js";
import "./chunk-MBK4V2X7.js";
import "./chunk-DY2RJG3P.js";
import "./chunk-K65KIEAL.js";
import "./chunk-BUDFB33L.js";
import "./chunk-7MILHJ3J.js";
import "./chunk-SGQJOZK5.js";
import "./chunk-AFM2NWPO.js";
import "./chunk-DUGZ4WIW.js";
import "./chunk-IFOFBKTA.js";
import {
  create3 as create,
  create4 as create2,
  create6 as create3
} from "./chunk-376QHLWZ.js";
import "./chunk-EGNP2T5O.js";
import "./chunk-XDCEA27D.js";
import "./chunk-EZSYDDUK.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-QIJGNK42.js";
import "./chunk-CJHUGFLE.js";
import "./chunk-IQGHPIIW.js";
import "./chunk-DWSGKQEK.js";
import "./chunk-W6ZGCRX6.js";
import "./chunk-PVCRV2LE.js";
import {
  MemoizedInputRenderer
} from "./chunk-HWAQQGJJ.js";
import {
  Form,
  useField,
  useForm
} from "./chunk-L5JCPKMP.js";
import {
  useRBAC
} from "./chunk-ZJEMJY2Q.js";
import "./chunk-6DRYEU2W.js";
import {
  Layouts
} from "./chunk-MTTHLNPH.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import {
  ForwardRef$J,
  Page,
  useAPIErrorHandler
} from "./chunk-7LKLOY7A.js";
import {
  useAuth
} from "./chunk-ODQFI753.js";
import "./chunk-ZOP4VV6J.js";
import "./chunk-WH6VCVXU.js";
import "./chunk-IL5G2D22.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-76QM3EFM.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import {
  useNotification
} from "./chunk-UBCTZOSQ.js";
import {
  Box,
  Button,
  Dialog,
  Divider,
  EmptyStateLayout,
  Field,
  Flex,
  Grid,
  IconButton,
  Modal,
  SingleSelect,
  SingleSelectOption,
  Table,
  Tabs,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
  Typography,
  VisuallyHidden,
  useId,
  useIntl
} from "./chunk-7GC3Y62Q.js";
import "./chunk-5ZC4PE57.js";
import "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$1h,
  ForwardRef$1v,
  ForwardRef$4F,
  ForwardRef$j
} from "./chunk-WRD5KPDH.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import "./chunk-ACIMPXWY.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/i18n/dist/admin/pages/SettingsPage.mjs
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var React5 = __toESM(require_react(), 1);

// node_modules/@strapi/i18n/dist/admin/components/CreateLocale.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);

// node_modules/@strapi/i18n/dist/admin/utils/baseQuery.mjs
var isBaseQueryError = (error) => {
  return error.name !== void 0;
};

// node_modules/@strapi/i18n/dist/admin/components/CreateLocale.mjs
var CreateLocale = ({ disabled, variant = "default" }) => {
  const { formatMessage } = useIntl();
  const [visible, setVisible] = React.useState(false);
  return (0, import_jsx_runtime.jsxs)(Modal.Root, {
    open: visible,
    onOpenChange: setVisible,
    children: [
      (0, import_jsx_runtime.jsx)(Modal.Trigger, {
        children: (0, import_jsx_runtime.jsx)(Button, {
          variant,
          disabled,
          startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1h, {}),
          onClick: () => setVisible(true),
          size: "S",
          children: formatMessage({
            id: getTranslation("Settings.list.actions.add"),
            defaultMessage: "Add new locale"
          })
        })
      }),
      (0, import_jsx_runtime.jsx)(CreateModal, {
        onClose: () => setVisible(false)
      })
    ]
  });
};
var LOCALE_SCHEMA = create3().shape({
  code: create2().nullable().required({
    id: "Settings.locales.modal.create.code.error",
    defaultMessage: "Please select a locale"
  }),
  name: create2().nullable().max(50, {
    id: "Settings.locales.modal.create.name.error.min",
    defaultMessage: "The locale display name can only be less than 50 characters."
  }).required({
    id: "Settings.locales.modal.create.name.error.required",
    defaultMessage: "Please give the locale a display name"
  }),
  isDefault: create()
});
var initialFormValues = {
  code: "",
  name: "",
  isDefault: false
};
var CreateModal = ({ onClose }) => {
  const titleId = useId();
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError, _unstableFormatValidationErrors: formatValidationErrors } = useAPIErrorHandler();
  const [createLocale] = useCreateLocaleMutation();
  const { formatMessage } = useIntl();
  const refetchPermissions = useAuth("CreateModal", (state) => state.refetchPermissions);
  const handleSubmit = async (values, helpers) => {
    try {
      const res = await createLocale(values);
      if ("error" in res) {
        if (isBaseQueryError(res.error) && res.error.name === "ValidationError") {
          helpers.setErrors(formatValidationErrors(res.error));
        } else {
          toggleNotification({
            type: "danger",
            message: formatAPIError(res.error)
          });
        }
        return;
      }
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: getTranslation("Settings.locales.modal.create.success"),
          defaultMessage: "Created locale"
        })
      });
      refetchPermissions();
      onClose();
    } catch (err) {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "An error occurred, please try again"
        })
      });
    }
  };
  return (0, import_jsx_runtime.jsx)(Modal.Content, {
    children: (0, import_jsx_runtime.jsxs)(Form, {
      method: "POST",
      initialValues: initialFormValues,
      validationSchema: LOCALE_SCHEMA,
      onSubmit: handleSubmit,
      children: [
        (0, import_jsx_runtime.jsx)(Modal.Header, {
          children: (0, import_jsx_runtime.jsx)(Modal.Title, {
            children: formatMessage({
              id: getTranslation("Settings.list.actions.add"),
              defaultMessage: "Add new locale"
            })
          })
        }),
        (0, import_jsx_runtime.jsx)(Modal.Body, {
          children: (0, import_jsx_runtime.jsxs)(Tabs.Root, {
            variant: "simple",
            defaultValue: "basic",
            children: [
              (0, import_jsx_runtime.jsxs)(Flex, {
                justifyContent: "space-between",
                children: [
                  (0, import_jsx_runtime.jsx)(Typography, {
                    tag: "h2",
                    variant: "beta",
                    id: titleId,
                    children: formatMessage({
                      id: getTranslation("Settings.locales.modal.title"),
                      defaultMessage: "Configuration"
                    })
                  }),
                  (0, import_jsx_runtime.jsxs)(Tabs.List, {
                    "aria-labelledby": titleId,
                    children: [
                      (0, import_jsx_runtime.jsx)(Tabs.Trigger, {
                        value: "basic",
                        children: formatMessage({
                          id: getTranslation("Settings.locales.modal.base"),
                          defaultMessage: "Basic settings"
                        })
                      }),
                      (0, import_jsx_runtime.jsx)(Tabs.Trigger, {
                        value: "advanced",
                        children: formatMessage({
                          id: getTranslation("Settings.locales.modal.advanced"),
                          defaultMessage: "Advanced settings"
                        })
                      })
                    ]
                  })
                ]
              }),
              (0, import_jsx_runtime.jsx)(Divider, {}),
              (0, import_jsx_runtime.jsxs)(Box, {
                paddingTop: 7,
                paddingBottom: 7,
                children: [
                  (0, import_jsx_runtime.jsx)(Tabs.Content, {
                    value: "basic",
                    children: (0, import_jsx_runtime.jsx)(BaseForm, {})
                  }),
                  (0, import_jsx_runtime.jsx)(Tabs.Content, {
                    value: "advanced",
                    children: (0, import_jsx_runtime.jsx)(AdvancedForm, {})
                  })
                ]
              })
            ]
          })
        }),
        (0, import_jsx_runtime.jsxs)(Modal.Footer, {
          children: [
            (0, import_jsx_runtime.jsx)(Modal.Close, {
              children: (0, import_jsx_runtime.jsx)(Button, {
                variant: "tertiary",
                children: formatMessage({
                  id: "app.components.Button.cancel",
                  defaultMessage: "Cancel"
                })
              })
            }),
            (0, import_jsx_runtime.jsx)(SubmitButton, {})
          ]
        })
      ]
    })
  });
};
var SubmitButton = () => {
  const { formatMessage } = useIntl();
  const isSubmitting = useForm("SubmitButton", (state) => state.isSubmitting);
  const modified = useForm("SubmitButton", (state) => state.modified);
  return (0, import_jsx_runtime.jsx)(Button, {
    type: "submit",
    startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4F, {}),
    disabled: isSubmitting || !modified,
    children: formatMessage({
      id: "global.save",
      defaultMessage: "Save"
    })
  });
};
var BaseForm = ({ mode = "create" }) => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const { data: defaultLocales, error } = useGetDefaultLocalesQuery();
  React.useEffect(() => {
    if (error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    }
  }, [
    error,
    formatAPIError,
    toggleNotification
  ]);
  if (!Array.isArray(defaultLocales)) {
    return null;
  }
  const options = defaultLocales.map((locale) => ({
    label: locale.name,
    value: locale.code
  }));
  const translatedForm = [
    {
      disabled: mode !== "create",
      label: {
        id: getTranslation("Settings.locales.modal.create.code.label"),
        defaultMessage: "Locales"
      },
      name: "code",
      options,
      placeholder: {
        id: "components.placeholder.select",
        defaultMessage: "Select"
      },
      required: true,
      size: 6,
      type: "enumeration"
    },
    {
      hint: {
        id: getTranslation("Settings.locales.modal.create.name.label.description"),
        defaultMessage: "Locale will be displayed under that name in the administration panel"
      },
      label: {
        id: getTranslation("Settings.locales.modal.create.name.label"),
        defaultMessage: "Locale display name"
      },
      name: "name",
      required: true,
      size: 6,
      type: "string"
    }
  ].map((field) => ({
    ...field,
    hint: field.hint ? formatMessage(field.hint) : void 0,
    label: formatMessage(field.label),
    placeholder: field.placeholder ? formatMessage(field.placeholder) : void 0
  }));
  return (0, import_jsx_runtime.jsx)(Grid.Root, {
    gap: 4,
    children: translatedForm.map(({ size, ...field }) => (0, import_jsx_runtime.jsx)(Grid.Item, {
      col: size,
      direction: "column",
      alignItems: "stretch",
      children: (0, import_jsx_runtime.jsx)(FormRenderer, {
        ...field
      })
    }, field.name))
  });
};
var AdvancedForm = ({ isDefaultLocale }) => {
  const { formatMessage } = useIntl();
  const form = [
    {
      disabled: isDefaultLocale,
      hint: {
        id: getTranslation("Settings.locales.modal.advanced.setAsDefault.hint"),
        defaultMessage: "One default locale is required, change it by selecting another one"
      },
      label: {
        id: getTranslation("Settings.locales.modal.advanced.setAsDefault"),
        defaultMessage: "Set as default locale"
      },
      name: "isDefault",
      size: 6,
      type: "boolean"
    }
  ].map((field) => ({
    ...field,
    hint: field.hint ? formatMessage(field.hint) : void 0,
    label: formatMessage(field.label)
  }));
  return (0, import_jsx_runtime.jsx)(Grid.Root, {
    gap: 4,
    children: form.map(({ size, ...field }) => (0, import_jsx_runtime.jsx)(Grid.Item, {
      col: size,
      direction: "column",
      alignItems: "stretch",
      children: (0, import_jsx_runtime.jsx)(FormRenderer, {
        ...field
      })
    }, field.name))
  });
};
var FormRenderer = (field) => {
  switch (field.type) {
    case "enumeration":
      return (0, import_jsx_runtime.jsx)(EnumerationInput, {
        ...field
      });
    default:
      return (0, import_jsx_runtime.jsx)(MemoizedInputRenderer, {
        ...field
      });
  }
};
var EnumerationInput = ({ disabled, hint, label, name, options, placeholder, required }) => {
  const { value, error, onChange } = useField(name);
  const { data: defaultLocales = [] } = useGetDefaultLocalesQuery();
  const handleChange = (value2) => {
    if (Array.isArray(defaultLocales)) {
      const locale = defaultLocales.find((locale2) => locale2.code === value2);
      onChange(name, value2);
      onChange("name", locale.name);
    } else {
      onChange(name, value2);
    }
  };
  return (0, import_jsx_runtime.jsxs)(Field.Root, {
    error,
    hint,
    name,
    required,
    children: [
      (0, import_jsx_runtime.jsx)(Field.Label, {
        children: label
      }),
      (0, import_jsx_runtime.jsx)(SingleSelect, {
        disabled,
        // @ts-expect-error – This will dissapear when the DS removes support for numbers to be returned by SingleSelect.
        onChange: handleChange,
        placeholder,
        value,
        children: options.map((option) => (0, import_jsx_runtime.jsx)(SingleSelectOption, {
          value: option.value,
          children: option.label
        }, option.value))
      }),
      (0, import_jsx_runtime.jsx)(Field.Error, {}),
      (0, import_jsx_runtime.jsx)(Field.Hint, {})
    ]
  });
};

// node_modules/@strapi/i18n/dist/admin/components/LocaleTable.mjs
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var React4 = __toESM(require_react(), 1);

// node_modules/@strapi/i18n/dist/admin/components/DeleteLocale.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var React2 = __toESM(require_react(), 1);
var DeleteLocale = ({ id, name }) => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const [visible, setVisible] = React2.useState(false);
  const [deleteLocale] = useDeleteLocaleMutation();
  const handleConfirm = async () => {
    try {
      const res = await deleteLocale(id);
      if ("error" in res) {
        toggleNotification({
          type: "danger",
          message: formatAPIError(res.error)
        });
        return;
      }
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: getTranslation("Settings.locales.modal.delete.success"),
          defaultMessage: "Deleted locale"
        })
      });
      setVisible(false);
    } catch (err) {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "An error occurred, please try again"
        })
      });
    }
  };
  return (0, import_jsx_runtime2.jsxs)(Dialog.Root, {
    open: visible,
    onOpenChange: setVisible,
    children: [
      (0, import_jsx_runtime2.jsx)(Dialog.Trigger, {
        children: (0, import_jsx_runtime2.jsx)(IconButton, {
          onClick: () => setVisible(true),
          label: formatMessage({
            id: getTranslation("Settings.list.actions.delete"),
            defaultMessage: "Delete {name} locale"
          }, {
            name
          }),
          variant: "ghost",
          children: (0, import_jsx_runtime2.jsx)(ForwardRef$j, {})
        })
      }),
      (0, import_jsx_runtime2.jsx)(ConfirmDialog, {
        onConfirm: handleConfirm
      })
    ]
  });
};

// node_modules/@strapi/i18n/dist/admin/components/EditLocale.mjs
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var React3 = __toESM(require_react(), 1);
var EditLocale = (props) => {
  const { formatMessage } = useIntl();
  const [visible, setVisible] = React3.useState(false);
  return (0, import_jsx_runtime3.jsxs)(import_jsx_runtime3.Fragment, {
    children: [
      (0, import_jsx_runtime3.jsx)(IconButton, {
        onClick: () => setVisible(true),
        label: formatMessage({
          id: getTranslation("Settings.list.actions.edit"),
          defaultMessage: "Edit {name} locale"
        }, {
          name: props.name
        }),
        variant: "ghost",
        children: (0, import_jsx_runtime3.jsx)(ForwardRef$1v, {})
      }),
      (0, import_jsx_runtime3.jsx)(EditModal, {
        ...props,
        open: visible,
        onOpenChange: setVisible
      })
    ]
  });
};
var EditModal = ({ id, code, isDefault, name, open, onOpenChange }) => {
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError, _unstableFormatValidationErrors: formatValidationErrors } = useAPIErrorHandler();
  const refetchPermissions = useAuth("EditModal", (state) => state.refetchPermissions);
  const { formatMessage } = useIntl();
  const titleId = useId();
  const [updateLocale] = useUpdateLocaleMutation();
  const handleSubmit = async ({ code: _code, ...data }, helpers) => {
    try {
      const res = await updateLocale({
        id,
        ...data
      });
      if ("error" in res) {
        if (isBaseQueryError(res.error) && res.error.name === "ValidationError") {
          helpers.setErrors(formatValidationErrors(res.error));
        } else {
          toggleNotification({
            type: "danger",
            message: formatAPIError(res.error)
          });
        }
        return;
      }
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: getTranslation("Settings.locales.modal.edit.success"),
          defaultMessage: "Updated locale"
        })
      });
      refetchPermissions();
      onOpenChange(false);
    } catch (err) {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "An error occurred, please try again"
        })
      });
    }
  };
  return (0, import_jsx_runtime3.jsx)(Modal.Root, {
    open,
    onOpenChange,
    children: (0, import_jsx_runtime3.jsx)(Modal.Content, {
      children: (0, import_jsx_runtime3.jsxs)(Form, {
        method: "PUT",
        onSubmit: handleSubmit,
        initialValues: {
          code,
          name,
          isDefault
        },
        validationSchema: LOCALE_SCHEMA,
        children: [
          (0, import_jsx_runtime3.jsx)(Modal.Header, {
            children: (0, import_jsx_runtime3.jsx)(Modal.Title, {
              children: formatMessage({
                id: getTranslation("Settings.list.actions.edit"),
                defaultMessage: "Edit a locale"
              }, {
                name
              })
            })
          }),
          (0, import_jsx_runtime3.jsx)(Modal.Body, {
            children: (0, import_jsx_runtime3.jsxs)(Tabs.Root, {
              variant: "simple",
              defaultValue: "basic",
              children: [
                (0, import_jsx_runtime3.jsxs)(Flex, {
                  justifyContent: "space-between",
                  children: [
                    (0, import_jsx_runtime3.jsx)(Typography, {
                      tag: "h2",
                      variant: "beta",
                      id: titleId,
                      children: formatMessage({
                        id: getTranslation("Settings.locales.modal.title"),
                        defaultMessage: "Configuration"
                      })
                    }),
                    (0, import_jsx_runtime3.jsxs)(Tabs.List, {
                      "aria-labelledby": titleId,
                      children: [
                        (0, import_jsx_runtime3.jsx)(Tabs.Trigger, {
                          value: "basic",
                          children: formatMessage({
                            id: getTranslation("Settings.locales.modal.base"),
                            defaultMessage: "Basic settings"
                          })
                        }),
                        (0, import_jsx_runtime3.jsx)(Tabs.Trigger, {
                          value: "advanced",
                          children: formatMessage({
                            id: getTranslation("Settings.locales.modal.advanced"),
                            defaultMessage: "Advanced settings"
                          })
                        })
                      ]
                    })
                  ]
                }),
                (0, import_jsx_runtime3.jsx)(Divider, {}),
                (0, import_jsx_runtime3.jsxs)(Box, {
                  paddingTop: 7,
                  paddingBottom: 7,
                  children: [
                    (0, import_jsx_runtime3.jsx)(Tabs.Content, {
                      value: "basic",
                      children: (0, import_jsx_runtime3.jsx)(BaseForm, {
                        mode: "edit"
                      })
                    }),
                    (0, import_jsx_runtime3.jsx)(Tabs.Content, {
                      value: "advanced",
                      children: (0, import_jsx_runtime3.jsx)(AdvancedForm, {
                        isDefaultLocale: isDefault
                      })
                    })
                  ]
                })
              ]
            })
          }),
          (0, import_jsx_runtime3.jsxs)(Modal.Footer, {
            children: [
              (0, import_jsx_runtime3.jsx)(Modal.Close, {
                children: (0, import_jsx_runtime3.jsx)(Button, {
                  variant: "tertiary",
                  children: formatMessage({
                    id: "app.components.Button.cancel",
                    defaultMessage: "Cancel"
                  })
                })
              }),
              (0, import_jsx_runtime3.jsx)(SubmitButton, {})
            ]
          })
        ]
      })
    })
  });
};

// node_modules/@strapi/i18n/dist/admin/components/LocaleTable.mjs
var LocaleTable = ({ locales = [], canDelete, canUpdate }) => {
  const [editLocaleId, setEditLocaleId] = React4.useState();
  const { formatMessage } = useIntl();
  const handleClick = (localeId) => () => {
    if (canUpdate) {
      setEditLocaleId(localeId);
    }
  };
  return (0, import_jsx_runtime4.jsxs)(Table, {
    colCount: 4,
    rowCount: locales.length + 1,
    children: [
      (0, import_jsx_runtime4.jsx)(Thead, {
        children: (0, import_jsx_runtime4.jsxs)(Tr, {
          children: [
            (0, import_jsx_runtime4.jsx)(Th, {
              children: (0, import_jsx_runtime4.jsx)(Typography, {
                variant: "sigma",
                textColor: "neutral600",
                children: formatMessage({
                  id: getTranslation("Settings.locales.row.id"),
                  defaultMessage: "ID"
                })
              })
            }),
            (0, import_jsx_runtime4.jsx)(Th, {
              children: (0, import_jsx_runtime4.jsx)(Typography, {
                variant: "sigma",
                textColor: "neutral600",
                children: formatMessage({
                  id: getTranslation("Settings.locales.row.displayName"),
                  defaultMessage: "Display name"
                })
              })
            }),
            (0, import_jsx_runtime4.jsx)(Th, {
              children: (0, import_jsx_runtime4.jsx)(Typography, {
                variant: "sigma",
                textColor: "neutral600",
                children: formatMessage({
                  id: getTranslation("Settings.locales.row.default-locale"),
                  defaultMessage: "Default locale"
                })
              })
            }),
            (0, import_jsx_runtime4.jsx)(Th, {
              children: (0, import_jsx_runtime4.jsx)(VisuallyHidden, {
                children: "Actions"
              })
            })
          ]
        })
      }),
      (0, import_jsx_runtime4.jsx)(Tbody, {
        children: locales.map((locale) => (0, import_jsx_runtime4.jsxs)(React4.Fragment, {
          children: [
            (0, import_jsx_runtime4.jsxs)(Tr, {
              onClick: handleClick(locale.id),
              style: {
                cursor: canUpdate ? "pointer" : "default"
              },
              children: [
                (0, import_jsx_runtime4.jsx)(Td, {
                  children: (0, import_jsx_runtime4.jsx)(Typography, {
                    textColor: "neutral800",
                    children: locale.id
                  })
                }),
                (0, import_jsx_runtime4.jsx)(Td, {
                  children: (0, import_jsx_runtime4.jsx)(Typography, {
                    textColor: "neutral800",
                    children: locale.name
                  })
                }),
                (0, import_jsx_runtime4.jsx)(Td, {
                  children: (0, import_jsx_runtime4.jsx)(Typography, {
                    textColor: "neutral800",
                    children: locale.isDefault ? formatMessage({
                      id: getTranslation("Settings.locales.default"),
                      defaultMessage: "Default"
                    }) : null
                  })
                }),
                (0, import_jsx_runtime4.jsx)(Td, {
                  children: (0, import_jsx_runtime4.jsxs)(Flex, {
                    gap: 1,
                    justifyContent: "flex-end",
                    onClick: (e) => e.stopPropagation(),
                    children: [
                      canUpdate && (0, import_jsx_runtime4.jsx)(EditLocale, {
                        ...locale
                      }),
                      canDelete && !locale.isDefault && (0, import_jsx_runtime4.jsx)(DeleteLocale, {
                        ...locale
                      })
                    ]
                  })
                })
              ]
            }),
            (0, import_jsx_runtime4.jsx)(EditModal, {
              ...locale,
              onOpenChange: () => setEditLocaleId(void 0),
              open: editLocaleId === locale.id
            })
          ]
        }, locale.id))
      })
    ]
  });
};

// node_modules/@strapi/i18n/dist/admin/pages/SettingsPage.mjs
var SettingsPage = () => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const { data: locales, isLoading: isLoadingLocales, error } = useGetLocalesQuery();
  const { isLoading: isLoadingRBAC, allowedActions: { canUpdate, canCreate, canDelete } } = useRBAC(PERMISSIONS);
  React5.useEffect(() => {
    if (error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    }
  }, [
    error,
    formatAPIError,
    toggleNotification
  ]);
  const isLoading = isLoadingLocales || isLoadingRBAC;
  if (isLoading) {
    return (0, import_jsx_runtime5.jsx)(Page.Loading, {});
  }
  if (error || !Array.isArray(locales)) {
    return (0, import_jsx_runtime5.jsx)(Page.Error, {});
  }
  return (0, import_jsx_runtime5.jsxs)(Page.Main, {
    tabIndex: -1,
    children: [
      (0, import_jsx_runtime5.jsx)(Layouts.Header, {
        primaryAction: (0, import_jsx_runtime5.jsx)(CreateLocale, {
          disabled: !canCreate
        }),
        title: formatMessage({
          id: getTranslation("plugin.name"),
          defaultMessage: "Internationalization"
        }),
        subtitle: formatMessage({
          id: getTranslation("Settings.list.description"),
          defaultMessage: "Configure the settings"
        })
      }),
      (0, import_jsx_runtime5.jsx)(Layouts.Content, {
        children: locales.length > 0 ? (0, import_jsx_runtime5.jsx)(LocaleTable, {
          locales,
          canDelete,
          canUpdate
        }) : (0, import_jsx_runtime5.jsx)(EmptyStateLayout, {
          icon: (0, import_jsx_runtime5.jsx)(ForwardRef$J, {
            width: void 0,
            height: void 0
          }),
          content: formatMessage({
            id: getTranslation("Settings.list.empty.title"),
            defaultMessage: "There are no locales"
          }),
          action: (0, import_jsx_runtime5.jsx)(CreateLocale, {
            disabled: !canCreate,
            variant: "secondary"
          })
        })
      })
    ]
  });
};
var ProtectedSettingsPage = () => {
  return (0, import_jsx_runtime5.jsx)(Page.Protect, {
    permissions: PERMISSIONS.read,
    children: (0, import_jsx_runtime5.jsx)(SettingsPage, {})
  });
};
export {
  ProtectedSettingsPage,
  SettingsPage
};
//# sourceMappingURL=SettingsPage-4SZBJ4VR.js.map
