{"version": 3, "sources": ["../../../invariant/browser.js", "../../../lodash/_customDefaultsMerge.js", "../../../lodash/mergeWith.js", "../../../lodash/defaultsDeep.js", "../../../lodash/now.js", "../../../lodash/debounce.js", "../../../lodash/throttle.js", "../../../@strapi/admin/admin/src/core/apis/CustomFields.ts", "../../../@strapi/admin/admin/src/core/apis/Plugin.ts", "../../../@strapi/admin/admin/src/core/apis/rbac.ts", "../../../@strapi/admin/admin/src/components/LanguageProvider.tsx", "../../../@strapi/admin/admin/src/components/Theme.tsx", "../../../@strapi/admin/admin/src/components/Providers.tsx", "../../../@strapi/admin/admin/src/App.tsx", "../../../@strapi/admin/admin/src/components/ErrorElement.tsx", "../../../@strapi/admin/admin/src/pages/NotFoundPage.tsx", "../../../@strapi/admin/ee/admin/src/pages/SettingsPage/constants.ts", "../../../@strapi/admin/admin/src/pages/Auth/components/ForgotPassword.tsx", "../../../@strapi/admin/admin/src/pages/Auth/components/ForgotPasswordSuccess.tsx", "../../../@strapi/admin/admin/src/pages/Auth/components/Oops.tsx", "../../../@strapi/admin/admin/src/pages/Auth/components/Register.tsx", "../../../@strapi/admin/admin/src/pages/Auth/components/ResetPassword.tsx", "../../../@strapi/admin/admin/src/pages/Auth/constants.ts", "../../../@strapi/admin/admin/src/pages/Auth/AuthPage.tsx", "../../../@strapi/admin/admin/src/pages/Settings/constants.ts", "../../../@strapi/admin/admin/src/router.tsx", "../../../@strapi/admin/admin/src/core/apis/router.tsx", "../../../@strapi/admin/admin/src/core/apis/Widgets.ts", "../../../@strapi/admin/admin/src/core/store/configure.ts", "../../../@strapi/admin/admin/src/core/utils/createHook.ts", "../../../@strapi/admin/admin/src/translations/languageNativeNames.ts", "../../../@strapi/admin/admin/src/StrapiApp.tsx", "../../../@strapi/admin/admin/src/render.ts", "../../../@strapi/admin/admin/src/hooks/useIsMounted.ts", "../../../@strapi/admin/admin/src/hooks/useForceUpdate.ts", "../../../@strapi/admin/admin/src/hooks/useThrottledCallback.ts", "../../../@strapi/admin/admin/src/utils/shims.ts", "../../../@strapi/admin/admin/src/components/DescriptionComponentRenderer.tsx", "../../../@strapi/admin/admin/src/hooks/useInjectReducer.ts"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n/**\n * Use invariant() to assert state which your program assumes to be true.\n *\n * Provide sprintf-style format (only %s is supported) and arguments\n * to provide information about what broke and what you were\n * expecting.\n *\n * The invariant message will be stripped in production, but the invariant\n * will remain to ensure logic does not differ in production.\n */\n\nvar invariant = function(condition, format, a, b, c, d, e, f) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (format === undefined) {\n      throw new Error('invariant requires an error message argument');\n    }\n  }\n\n  if (!condition) {\n    var error;\n    if (format === undefined) {\n      error = new Error(\n        'Minified exception occurred; use the non-minified dev environment ' +\n        'for the full error message and additional helpful warnings.'\n      );\n    } else {\n      var args = [a, b, c, d, e, f];\n      var argIndex = 0;\n      error = new Error(\n        format.replace(/%s/g, function() { return args[argIndex++]; })\n      );\n      error.name = 'Invariant Violation';\n    }\n\n    error.framesToPop = 1; // we don't care about invariant's own frame\n    throw error;\n  }\n};\n\nmodule.exports = invariant;\n", "var baseMerge = require('./_baseMerge'),\n    isObject = require('./isObject');\n\n/**\n * Used by `_.defaultsDeep` to customize its `_.merge` use to merge source\n * objects into destination objects that are passed thru.\n *\n * @private\n * @param {*} objValue The destination value.\n * @param {*} srcValue The source value.\n * @param {string} key The key of the property to merge.\n * @param {Object} object The parent object of `objValue`.\n * @param {Object} source The parent object of `srcValue`.\n * @param {Object} [stack] Tracks traversed source values and their merged\n *  counterparts.\n * @returns {*} Returns the value to assign.\n */\nfunction customDefaultsMerge(objValue, srcValue, key, object, source, stack) {\n  if (isObject(objValue) && isObject(srcValue)) {\n    // Recursively merge objects and arrays (susceptible to call stack limits).\n    stack.set(srcValue, objValue);\n    baseMerge(objValue, srcValue, undefined, customDefaultsMerge, stack);\n    stack['delete'](srcValue);\n  }\n  return objValue;\n}\n\nmodule.exports = customDefaultsMerge;\n", "var baseMerge = require('./_baseMerge'),\n    createAssigner = require('./_createAssigner');\n\n/**\n * This method is like `_.merge` except that it accepts `customizer` which\n * is invoked to produce the merged values of the destination and source\n * properties. If `customizer` returns `undefined`, merging is handled by the\n * method instead. The `customizer` is invoked with six arguments:\n * (objValue, srcValue, key, object, source, stack).\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Object\n * @param {Object} object The destination object.\n * @param {...Object} sources The source objects.\n * @param {Function} customizer The function to customize assigned values.\n * @returns {Object} Returns `object`.\n * @example\n *\n * function customizer(objValue, srcValue) {\n *   if (_.isArray(objValue)) {\n *     return objValue.concat(srcValue);\n *   }\n * }\n *\n * var object = { 'a': [1], 'b': [2] };\n * var other = { 'a': [3], 'b': [4] };\n *\n * _.mergeWith(object, other, customizer);\n * // => { 'a': [1, 3], 'b': [2, 4] }\n */\nvar mergeWith = createAssigner(function(object, source, srcIndex, customizer) {\n  baseMerge(object, source, srcIndex, customizer);\n});\n\nmodule.exports = mergeWith;\n", "var apply = require('./_apply'),\n    baseRest = require('./_baseRest'),\n    customDefaultsMerge = require('./_customDefaultsMerge'),\n    mergeWith = require('./mergeWith');\n\n/**\n * This method is like `_.defaults` except that it recursively assigns\n * default properties.\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @memberOf _\n * @since 3.10.0\n * @category Object\n * @param {Object} object The destination object.\n * @param {...Object} [sources] The source objects.\n * @returns {Object} Returns `object`.\n * @see _.defaults\n * @example\n *\n * _.defaultsDeep({ 'a': { 'b': 2 } }, { 'a': { 'b': 1, 'c': 3 } });\n * // => { 'a': { 'b': 2, 'c': 3 } }\n */\nvar defaultsDeep = baseRest(function(args) {\n  args.push(undefined, customDefaultsMerge);\n  return apply(mergeWith, undefined, args);\n});\n\nmodule.exports = defaultsDeep;\n", "var root = require('./_root');\n\n/**\n * Gets the timestamp of the number of milliseconds that have elapsed since\n * the Unix epoch (1 January 1970 00:00:00 UTC).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Date\n * @returns {number} Returns the timestamp.\n * @example\n *\n * _.defer(function(stamp) {\n *   console.log(_.now() - stamp);\n * }, _.now());\n * // => Logs the number of milliseconds it took for the deferred invocation.\n */\nvar now = function() {\n  return root.Date.now();\n};\n\nmodule.exports = now;\n", "var isObject = require('./isObject'),\n    now = require('./now'),\n    toNumber = require('./toNumber');\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * Creates a debounced function that delays invoking `func` until after `wait`\n * milliseconds have elapsed since the last time the debounced function was\n * invoked. The debounced function comes with a `cancel` method to cancel\n * delayed `func` invocations and a `flush` method to immediately invoke them.\n * Provide `options` to indicate whether `func` should be invoked on the\n * leading and/or trailing edge of the `wait` timeout. The `func` is invoked\n * with the last arguments provided to the debounced function. Subsequent\n * calls to the debounced function return the result of the last `func`\n * invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the debounced function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.debounce` and `_.throttle`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to debounce.\n * @param {number} [wait=0] The number of milliseconds to delay.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=false]\n *  Specify invoking on the leading edge of the timeout.\n * @param {number} [options.maxWait]\n *  The maximum time `func` is allowed to be delayed before it's invoked.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new debounced function.\n * @example\n *\n * // Avoid costly calculations while the window size is in flux.\n * jQuery(window).on('resize', _.debounce(calculateLayout, 150));\n *\n * // Invoke `sendMail` when clicked, debouncing subsequent calls.\n * jQuery(element).on('click', _.debounce(sendMail, 300, {\n *   'leading': true,\n *   'trailing': false\n * }));\n *\n * // Ensure `batchLog` is invoked once after 1 second of debounced calls.\n * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });\n * var source = new EventSource('/stream');\n * jQuery(source).on('message', debounced);\n *\n * // Cancel the trailing debounced invocation.\n * jQuery(window).on('popstate', debounced.cancel);\n */\nfunction debounce(func, wait, options) {\n  var lastArgs,\n      lastThis,\n      maxWait,\n      result,\n      timerId,\n      lastCallTime,\n      lastInvokeTime = 0,\n      leading = false,\n      maxing = false,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  wait = toNumber(wait) || 0;\n  if (isObject(options)) {\n    leading = !!options.leading;\n    maxing = 'maxWait' in options;\n    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n\n  function invokeFunc(time) {\n    var args = lastArgs,\n        thisArg = lastThis;\n\n    lastArgs = lastThis = undefined;\n    lastInvokeTime = time;\n    result = func.apply(thisArg, args);\n    return result;\n  }\n\n  function leadingEdge(time) {\n    // Reset any `maxWait` timer.\n    lastInvokeTime = time;\n    // Start the timer for the trailing edge.\n    timerId = setTimeout(timerExpired, wait);\n    // Invoke the leading edge.\n    return leading ? invokeFunc(time) : result;\n  }\n\n  function remainingWait(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime,\n        timeWaiting = wait - timeSinceLastCall;\n\n    return maxing\n      ? nativeMin(timeWaiting, maxWait - timeSinceLastInvoke)\n      : timeWaiting;\n  }\n\n  function shouldInvoke(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime;\n\n    // Either this is the first call, activity has stopped and we're at the\n    // trailing edge, the system time has gone backwards and we're treating\n    // it as the trailing edge, or we've hit the `maxWait` limit.\n    return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||\n      (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));\n  }\n\n  function timerExpired() {\n    var time = now();\n    if (shouldInvoke(time)) {\n      return trailingEdge(time);\n    }\n    // Restart the timer.\n    timerId = setTimeout(timerExpired, remainingWait(time));\n  }\n\n  function trailingEdge(time) {\n    timerId = undefined;\n\n    // Only invoke if we have `lastArgs` which means `func` has been\n    // debounced at least once.\n    if (trailing && lastArgs) {\n      return invokeFunc(time);\n    }\n    lastArgs = lastThis = undefined;\n    return result;\n  }\n\n  function cancel() {\n    if (timerId !== undefined) {\n      clearTimeout(timerId);\n    }\n    lastInvokeTime = 0;\n    lastArgs = lastCallTime = lastThis = timerId = undefined;\n  }\n\n  function flush() {\n    return timerId === undefined ? result : trailingEdge(now());\n  }\n\n  function debounced() {\n    var time = now(),\n        isInvoking = shouldInvoke(time);\n\n    lastArgs = arguments;\n    lastThis = this;\n    lastCallTime = time;\n\n    if (isInvoking) {\n      if (timerId === undefined) {\n        return leadingEdge(lastCallTime);\n      }\n      if (maxing) {\n        // Handle invocations in a tight loop.\n        clearTimeout(timerId);\n        timerId = setTimeout(timerExpired, wait);\n        return invokeFunc(lastCallTime);\n      }\n    }\n    if (timerId === undefined) {\n      timerId = setTimeout(timerExpired, wait);\n    }\n    return result;\n  }\n  debounced.cancel = cancel;\n  debounced.flush = flush;\n  return debounced;\n}\n\nmodule.exports = debounce;\n", "var debounce = require('./debounce'),\n    isObject = require('./isObject');\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/**\n * Creates a throttled function that only invokes `func` at most once per\n * every `wait` milliseconds. The throttled function comes with a `cancel`\n * method to cancel delayed `func` invocations and a `flush` method to\n * immediately invoke them. Provide `options` to indicate whether `func`\n * should be invoked on the leading and/or trailing edge of the `wait`\n * timeout. The `func` is invoked with the last arguments provided to the\n * throttled function. Subsequent calls to the throttled function return the\n * result of the last `func` invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the throttled function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [<PERSON>'s article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.throttle` and `_.debounce`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to throttle.\n * @param {number} [wait=0] The number of milliseconds to throttle invocations to.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=true]\n *  Specify invoking on the leading edge of the timeout.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new throttled function.\n * @example\n *\n * // Avoid excessively updating the position while scrolling.\n * jQuery(window).on('scroll', _.throttle(updatePosition, 100));\n *\n * // Invoke `renewToken` when the click event is fired, but not more than once every 5 minutes.\n * var throttled = _.throttle(renewToken, 300000, { 'trailing': false });\n * jQuery(element).on('click', throttled);\n *\n * // Cancel the trailing throttled invocation.\n * jQuery(window).on('popstate', throttled.cancel);\n */\nfunction throttle(func, wait, options) {\n  var leading = true,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  if (isObject(options)) {\n    leading = 'leading' in options ? !!options.leading : leading;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n  return debounce(func, wait, {\n    'leading': leading,\n    'maxWait': wait,\n    'trailing': trailing\n  });\n}\n\nmodule.exports = throttle;\n", "/* eslint-disable check-file/filename-naming-convention */\nimport { ComponentType } from 'react';\n\nimport { Internal, Utils } from '@strapi/types';\nimport invariant from 'invariant';\n\nimport type { MessageDescriptor, PrimitiveType } from 'react-intl';\nimport type { AnySchema } from 'yup';\n\ntype CustomFieldUID = Utils.String.Suffix<\n  | Internal.Namespace.WithSeparator<Internal.Namespace.Plugin>\n  | Internal.Namespace.WithSeparator<Internal.Namespace.Global>,\n  string\n>;\n\ntype CustomFieldOptionInput =\n  | 'text'\n  | 'checkbox'\n  | 'checkbox-with-number-field'\n  | 'select-default-boolean'\n  | 'date'\n  | 'select'\n  | 'number'\n  | 'boolean-radio-group'\n  | 'select-date'\n  | 'text-area-enum'\n  | 'select-number'\n  | 'radio-group';\n\ntype CustomFieldOptionName =\n  | 'min'\n  | 'minLength'\n  | 'max'\n  | 'maxLength'\n  | 'required'\n  | 'regex'\n  | 'enum'\n  | 'unique'\n  | 'private'\n  | 'default';\n\ninterface CustomFieldOption {\n  intlLabel: MessageDescriptor & {\n    values?: Record<string, PrimitiveType>;\n  };\n  description: MessageDescriptor & {\n    values?: Record<string, PrimitiveType>;\n  };\n  name: CustomFieldOptionName;\n  type: CustomFieldOptionInput;\n  defaultValue?: string | number | boolean | Date;\n}\n\ninterface CustomFieldOptionSection {\n  sectionTitle:\n    | (MessageDescriptor & {\n        values?: Record<string, PrimitiveType>;\n      })\n    | null;\n  items: CustomFieldOption[];\n}\n\ninterface CustomFieldOptions {\n  base?: (CustomFieldOptionSection | CustomFieldOption)[];\n  advanced?: (CustomFieldOptionSection | CustomFieldOption)[];\n  validator?: () => Record<string, AnySchema>;\n}\n\ninterface CustomField {\n  name: string;\n  pluginId?: string;\n  type: (typeof ALLOWED_TYPES)[number];\n  intlLabel: MessageDescriptor & {\n    values?: Record<string, PrimitiveType>;\n  };\n  intlDescription: MessageDescriptor & {\n    values?: Record<string, PrimitiveType>;\n  };\n  icon?: ComponentType;\n  components: {\n    Input: () => Promise<{ default?: ComponentType }>;\n  };\n  options?: CustomFieldOptions;\n}\n\nconst ALLOWED_TYPES = [\n  'biginteger',\n  'boolean',\n  'date',\n  'datetime',\n  'decimal',\n  'email',\n  'enumeration',\n  'float',\n  'integer',\n  'json',\n  'password',\n  'richtext',\n  'string',\n  'text',\n  'time',\n  'uid',\n] as const;\n\nconst ALLOWED_ROOT_LEVEL_OPTIONS = [\n  'min',\n  'minLength',\n  'max',\n  'maxLength',\n  'required',\n  'regex',\n  'enum',\n  'unique',\n  'private',\n  'default',\n] as const;\n\nclass CustomFields {\n  customFields: Record<string, CustomField>;\n\n  constructor() {\n    this.customFields = {};\n  }\n\n  register = (customFields: CustomField | CustomField[]) => {\n    if (Array.isArray(customFields)) {\n      // If several custom fields are passed, register them one by one\n      customFields.forEach((customField) => {\n        this.register(customField);\n      });\n    } else {\n      // Handle individual custom field\n      const { name, pluginId, type, intlLabel, intlDescription, components, options } =\n        customFields;\n\n      // Ensure required attributes are provided\n      invariant(name, 'A name must be provided');\n      invariant(type, 'A type must be provided');\n      invariant(intlLabel, 'An intlLabel must be provided');\n      invariant(intlDescription, 'An intlDescription must be provided');\n      invariant(components, 'A components object must be provided');\n      invariant(components.Input, 'An Input component must be provided');\n\n      // Ensure the type is valid\n      invariant(\n        ALLOWED_TYPES.includes(type),\n        `Custom field type: '${type}' is not a valid Strapi type or it can't be used with a Custom Field`\n      );\n\n      // Ensure name has no special characters\n      const isValidObjectKey = /^(?![0-9])[a-zA-Z0-9$_-]+$/g;\n      invariant(\n        isValidObjectKey.test(name),\n        `Custom field name: '${name}' is not a valid object key`\n      );\n\n      // Ensure options have valid name paths\n      const allFormOptions = [...(options?.base || []), ...(options?.advanced || [])];\n\n      if (allFormOptions.length) {\n        const optionPathValidations = allFormOptions.reduce(optionsValidationReducer, []);\n\n        optionPathValidations.forEach(({ isValidOptionPath, errorMessage }) => {\n          invariant(isValidOptionPath, errorMessage);\n        });\n      }\n\n      // When no plugin is specified, default to the global namespace\n      const uid: CustomFieldUID = pluginId ? `plugin::${pluginId}.${name}` : `global::${name}`;\n\n      // Ensure the uid is unique\n      const uidAlreadyUsed = Object.prototype.hasOwnProperty.call(this.customFields, uid);\n      invariant(!uidAlreadyUsed, `Custom field: '${uid}' has already been registered`);\n\n      this.customFields[uid] = customFields;\n    }\n  };\n\n  getAll = () => {\n    return this.customFields;\n  };\n\n  get = (uid: string): CustomField | undefined => {\n    return this.customFields[uid];\n  };\n}\n\ninterface OptionValidation {\n  isValidOptionPath: boolean;\n  errorMessage: string;\n}\n\nconst optionsValidationReducer = (\n  acc: OptionValidation[],\n  option: CustomFieldOptionSection | CustomFieldOption\n): OptionValidation[] => {\n  if ('items' in option) {\n    return option.items.reduce(optionsValidationReducer, acc);\n  }\n\n  if (!option.name) {\n    acc.push({\n      isValidOptionPath: false,\n      errorMessage: \"The 'name' property is required on an options object\",\n    });\n  } else {\n    acc.push({\n      isValidOptionPath:\n        option.name.startsWith('options') || ALLOWED_ROOT_LEVEL_OPTIONS.includes(option.name),\n      errorMessage: `'${option.name}' must be prefixed with 'options.'`,\n    });\n  }\n\n  return acc;\n};\n\nexport { type CustomField, CustomFields };\n", "/* eslint-disable check-file/filename-naming-convention */\nimport * as React from 'react';\n\nimport { immerable } from 'immer';\n\nexport interface PluginConfig\n  extends Partial<Pick<Plugin, 'apis' | 'initializer' | 'injectionZones' | 'isReady'>> {\n  name: string;\n  id: string;\n}\n\nexport class Plugin {\n  [immerable] = true;\n\n  apis: Record<string, unknown>;\n  initializer: React.ComponentType<{ setPlugin(pluginId: string): void }> | null;\n  injectionZones: Record<\n    string,\n    Record<string, Array<{ name: string; Component: React.ComponentType }>>\n  >;\n  isReady: boolean;\n  name: string;\n  pluginId: PluginConfig['id'];\n\n  constructor(pluginConf: PluginConfig) {\n    this.apis = pluginConf.apis || {};\n    this.initializer = pluginConf.initializer || null;\n    this.injectionZones = pluginConf.injectionZones || {};\n    this.isReady = pluginConf.isReady !== undefined ? pluginConf.isReady : true;\n    this.name = pluginConf.name;\n    this.pluginId = pluginConf.id;\n  }\n\n  getInjectedComponents(containerName: string, blockName: string) {\n    try {\n      return this.injectionZones[containerName][blockName] || [];\n    } catch (err) {\n      console.error('Cannot get injected component', err);\n\n      return [];\n    }\n  }\n\n  injectComponent(\n    containerName: string,\n    blockName: string,\n    component: { name: string; Component: React.ComponentType }\n  ) {\n    try {\n      this.injectionZones[containerName][blockName].push(component);\n    } catch (err) {\n      console.error('Cannot inject component', err);\n    }\n  }\n}\n", "import { Location } from 'react-router-dom';\n\nimport type { Permission, User } from '../../features/Auth';\n\ninterface RBACContext extends Pick<Location, 'pathname' | 'search'> {\n  /**\n   * The current user.\n   */\n  user?: User;\n  /**\n   * The permissions of the current user.\n   */\n  permissions: Permission[];\n}\n\ninterface RBACMiddleware {\n  (\n    ctx: RBACContext\n  ): (\n    next: (permissions: Permission[]) => Promise<Permission[]> | Permission[]\n  ) => (permissions: Permission[]) => Promise<Permission[]> | Permission[];\n}\n\nclass RBAC {\n  private middlewares: RBACMiddleware[] = [];\n\n  constructor() {}\n\n  use(middleware: RBACMiddleware[]): void;\n  use(middleware: RBACMiddleware): void;\n  use(middleware: RBACMiddleware | RBACMiddleware[]): void {\n    if (Array.isArray(middleware)) {\n      this.middlewares.push(...middleware);\n    } else {\n      this.middlewares.push(middleware);\n    }\n  }\n\n  run = async (ctx: RBACContext, permissions: Permission[]): Promise<Permission[]> => {\n    let index = 0;\n\n    const middlewaresToRun = this.middlewares.map((middleware) => middleware(ctx));\n\n    const next = async (permissions: Permission[]) => {\n      if (index < this.middlewares.length) {\n        return middlewaresToRun[index++](next)(permissions);\n      }\n\n      return permissions;\n    };\n\n    return next(permissions);\n  };\n}\n\nexport { RBAC };\nexport type { RBACMiddleware, RBACContext };\n", "import * as React from 'react';\n\nimport defaultsDeep from 'lodash/defaultsDeep';\nimport { IntlProvider } from 'react-intl';\n\nimport { useTypedSelector } from '../core/store/hooks';\n\n/* -------------------------------------------------------------------------------------------------\n * LanguageProvider\n * -----------------------------------------------------------------------------------------------*/\n\ninterface LanguageProviderProps {\n  children: React.ReactNode;\n  messages: Record<string, Record<string, string>>;\n}\n\nconst LanguageProvider = ({ children, messages }: LanguageProviderProps) => {\n  const locale = useTypedSelector((state) => state.admin_app.language.locale);\n  const appMessages = defaultsDeep(messages[locale], messages.en);\n\n  return (\n    <IntlProvider locale={locale} defaultLocale=\"en\" messages={appMessages} textComponent=\"span\">\n      {children}\n    </IntlProvider>\n  );\n};\n\nexport { LanguageProvider };\nexport type { LanguageProviderProps };\n", "import * as React from 'react';\n\nimport { DesignSystemProvider } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { useDispatch } from 'react-redux';\nimport { DefaultTheme, createGlobalStyle } from 'styled-components';\n\nimport { useTypedSelector } from '../core/store/hooks';\nimport { setAvailableThemes } from '../reducer';\n\ninterface ThemeProps {\n  children: React.ReactNode;\n  themes: {\n    dark: DefaultTheme;\n    light: DefaultTheme;\n  };\n}\n\nconst Theme = ({ children, themes }: ThemeProps) => {\n  const { currentTheme } = useTypedSelector((state) => state.admin_app.theme);\n  const [systemTheme, setSystemTheme] = React.useState<'light' | 'dark'>();\n  const { locale } = useIntl();\n  const dispatch = useDispatch();\n\n  // Listen to changes in the system theme\n  React.useEffect(() => {\n    const themeWatcher = window.matchMedia('(prefers-color-scheme: dark)');\n    setSystemTheme(themeWatcher.matches ? 'dark' : 'light');\n\n    const listener = (event: MediaQueryListEvent) => {\n      setSystemTheme(event.matches ? 'dark' : 'light');\n    };\n    themeWatcher.addEventListener('change', listener);\n\n    // Remove listener on cleanup\n    return () => {\n      themeWatcher.removeEventListener('change', listener);\n    };\n  }, []);\n\n  React.useEffect(() => {\n    dispatch(setAvailableThemes(Object.keys(themes)));\n  }, [dispatch, themes]);\n\n  const computedThemeName = currentTheme === 'system' ? systemTheme : currentTheme;\n\n  return (\n    <DesignSystemProvider\n      locale={locale}\n      /**\n       * TODO: could we make this neater i.e. by setting up the context to throw\n       * if it can't find it, that way the type is always fully defined and we're\n       * not checking it all the time...\n       */\n      theme={themes?.[computedThemeName || 'light']}\n    >\n      {children}\n      <GlobalStyle />\n    </DesignSystemProvider>\n  );\n};\n\nconst GlobalStyle = createGlobalStyle`\n  body {\n    background: ${({ theme }) => theme.colors.neutral100};\n  }\n`;\n\nexport { Theme };\nexport type { ThemeProps };\n", "import * as React from 'react';\n\nimport { QueryClient, QueryClientProvider } from 'react-query';\nimport { Provider } from 'react-redux';\n\nimport { AuthProvider } from '../features/Auth';\nimport { HistoryProvider } from '../features/BackButton';\nimport { ConfigurationProvider } from '../features/Configuration';\nimport { NotificationsProvider } from '../features/Notifications';\nimport { StrapiAppProvider } from '../features/StrapiApp';\nimport { TrackingProvider } from '../features/Tracking';\n\nimport { GuidedTourProvider } from './GuidedTour/Provider';\nimport { LanguageProvider } from './LanguageProvider';\nimport { Theme } from './Theme';\n\nimport type { Store } from '../core/store/configure';\nimport type { StrapiApp } from '../StrapiApp';\n\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      refetchOnWindowFocus: false,\n    },\n  },\n});\n\ninterface ProvidersProps {\n  children: React.ReactNode;\n  strapi: StrapiApp;\n  store: Store;\n}\n\nconst Providers = ({ children, strapi, store }: ProvidersProps) => {\n  return (\n    <StrapiAppProvider\n      components={strapi.library.components}\n      customFields={strapi.customFields}\n      widgets={strapi.widgets}\n      fields={strapi.library.fields}\n      menu={strapi.router.menu}\n      getAdminInjectedComponents={strapi.getAdminInjectedComponents}\n      getPlugin={strapi.getPlugin}\n      plugins={strapi.plugins}\n      rbac={strapi.rbac}\n      runHookParallel={strapi.runHookParallel}\n      runHookWaterfall={(name, initialValue) => strapi.runHookWaterfall(name, initialValue, store)}\n      runHookSeries={strapi.runHookSeries}\n      settings={strapi.router.settings}\n    >\n      <Provider store={store}>\n        <QueryClientProvider client={queryClient}>\n          <AuthProvider>\n            <HistoryProvider>\n              <LanguageProvider messages={strapi.configurations.translations}>\n                <Theme themes={strapi.configurations.themes}>\n                  <NotificationsProvider>\n                    <TrackingProvider>\n                      <GuidedTourProvider>\n                        <ConfigurationProvider\n                          defaultAuthLogo={strapi.configurations.authLogo}\n                          defaultMenuLogo={strapi.configurations.menuLogo}\n                          showReleaseNotification={strapi.configurations.notifications.releases}\n                        >\n                          {children}\n                        </ConfigurationProvider>\n                      </GuidedTourProvider>\n                    </TrackingProvider>\n                  </NotificationsProvider>\n                </Theme>\n              </LanguageProvider>\n            </HistoryProvider>\n          </AuthProvider>\n        </QueryClientProvider>\n      </Provider>\n    </StrapiAppProvider>\n  );\n};\n\nexport { Providers };\n", "/**\n *\n * App.js\n *\n */\nimport { Suspense, useEffect } from 'react';\n\nimport { Outlet } from 'react-router-dom';\n\nimport { Page } from './components/PageHelpers';\nimport { Providers } from './components/Providers';\nimport { LANGUAGE_LOCAL_STORAGE_KEY } from './reducer';\n\nimport type { Store } from './core/store/configure';\nimport type { StrapiApp } from './StrapiApp';\n\ninterface AppProps {\n  strapi: StrapiApp;\n  store: Store;\n}\n\nconst App = ({ strapi, store }: AppProps) => {\n  useEffect(() => {\n    const language = localStorage.getItem(LANGUAGE_LOCAL_STORAGE_KEY) || 'en';\n\n    if (language) {\n      document.documentElement.lang = language;\n    }\n  }, []);\n\n  return (\n    <Providers strapi={strapi} store={store}>\n      <Suspense fallback={<Page.Loading />}>\n        <Outlet />\n      </Suspense>\n    </Providers>\n  );\n};\n\nexport { App };\nexport type { AppProps };\n", "import {\n  <PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  TypographyComponent,\n} from '@strapi/design-system';\nimport { Duplicate, WarningCircle } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { useRouteError } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { useClipboard } from '../hooks/useClipboard';\n\n/**\n * @description this stops the app from going white, and instead shows the error message.\n * But it could be improved for sure.\n */\nconst ErrorElement = () => {\n  const error = useRouteError();\n  const { formatMessage } = useIntl();\n  const { copy } = useClipboard();\n\n  if (error instanceof Error) {\n    console.error(error);\n\n    const handleClick = async () => {\n      await copy(`\n\\`\\`\\`\n${error.stack}\n\\`\\`\\`\n      `);\n    };\n\n    return (\n      <Main height=\"100%\">\n        <Flex alignItems=\"center\" height=\"100%\" justifyContent=\"center\">\n          <Flex\n            gap={7}\n            padding={7}\n            direction=\"column\"\n            width=\"35%\"\n            shadow=\"tableShadow\"\n            borderColor=\"neutral150\"\n            background=\"neutral0\"\n            hasRadius\n            maxWidth=\"512px\"\n          >\n            <Flex direction=\"column\" gap={2}>\n              <WarningCircle width=\"32px\" height=\"32px\" fill=\"danger600\" />\n              <Typography fontSize={4} fontWeight=\"bold\" textAlign=\"center\">\n                {formatMessage({\n                  id: 'app.error',\n                  defaultMessage: 'Something went wrong',\n                })}\n              </Typography>\n              <Typography variant=\"omega\" textAlign=\"center\">\n                {formatMessage(\n                  {\n                    id: 'app.error.message',\n                    defaultMessage: `It seems like there is a bug in your instance, but we've got you covered. Please notify your technical team so they can investigate the source of the problem and report the issue to us by opening a bug report on {link}.`,\n                  },\n                  {\n                    link: (\n                      <Link\n                        isExternal\n                        // hack to get rid of the current endIcon, which should be removable by using `null`.\n                        endIcon\n                        href=\"https://github.com/strapi/strapi/issues/new?assignees=&labels=&projects=&template=BUG_REPORT.md\"\n                      >{`Strapi's GitHub`}</Link>\n                    ),\n                  }\n                )}\n              </Typography>\n            </Flex>\n            {/* the Alert component needs to make its close button optional as well as the icon. */}\n            <Flex gap={4} direction=\"column\" width=\"100%\">\n              <StyledAlert onClose={() => {}} width=\"100%\" closeLabel=\"\" variant=\"danger\">\n                <ErrorType>{error.message}</ErrorType>\n              </StyledAlert>\n              <Button onClick={handleClick} variant=\"tertiary\" startIcon={<Duplicate />}>\n                {formatMessage({\n                  id: 'app.error.copy',\n                  defaultMessage: 'Copy to clipboard',\n                })}\n              </Button>\n            </Flex>\n          </Flex>\n        </Flex>\n      </Main>\n    );\n  }\n\n  throw error;\n};\n\nconst StyledAlert = styled(Alert)`\n  & > div:first-child {\n    display: none;\n  }\n\n  & > button {\n    display: none;\n  }\n`;\n\nconst ErrorType = styled<TypographyComponent>(Typography)`\n  word-break: break-all;\n  color: ${({ theme }) => theme.colors.danger600};\n`;\n\nexport { ErrorElement };\n", "/**\n * NotFoundPage\n *\n * This is the page we show when the user visits a url that doesn't have a route\n *\n */\nimport { LinkButton, EmptyStateLayout } from '@strapi/design-system';\nimport { ArrowRight } from '@strapi/icons';\nimport { EmptyPictures } from '@strapi/icons/symbols';\nimport { useIntl } from 'react-intl';\nimport { Link } from 'react-router-dom';\n\nimport { Layouts } from '../components/Layouts/Layout';\nimport { Page } from '../components/PageHelpers';\n\nexport const NotFoundPage = () => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Page.Main labelledBy=\"title\">\n      <Layouts.Header\n        id=\"title\"\n        title={formatMessage({\n          id: 'content-manager.pageNotFound',\n          defaultMessage: 'Page not found',\n        })}\n      />\n      <Layouts.Content>\n        <EmptyStateLayout\n          action={\n            <LinkButton tag={Link} variant=\"secondary\" endIcon={<ArrowRight />} to=\"/\">\n              {formatMessage({\n                id: 'app.components.NotFoundPage.back',\n                defaultMessage: 'Back to homepage',\n              })}\n            </LinkButton>\n          }\n          content={formatMessage({\n            id: 'app.page.not.found',\n            defaultMessage: \"Oops! We can't seem to find the page you're looging for...\",\n          })}\n          hasRadius\n          icon={<EmptyPictures width=\"16rem\" />}\n          shadow=\"tableShadow\"\n        />\n      </Layouts.Content>\n    </Page.Main>\n  );\n};\n", "import { RouteObject } from 'react-router-dom';\n\n/**\n * All these routes are relative to the `/admin/settings/*` route\n * as such their path should not start with a `/` or include the `/settings` prefix.\n */\nexport const getEERoutes = (): RouteObject[] => [\n  ...(window.strapi.features.isEnabled(window.strapi.features.AUDIT_LOGS)\n    ? [\n        {\n          path: 'audit-logs',\n          lazy: async () => {\n            const { ProtectedListPage } = await import('./pages/AuditLogs/ListPage');\n\n            return {\n              Component: ProtectedListPage,\n            };\n          },\n        },\n      ]\n    : []),\n  ...(window.strapi.features.isEnabled(window.strapi.features.SSO)\n    ? [\n        {\n          path: 'single-sign-on',\n          lazy: async () => {\n            const { ProtectedSSO } = await import('./pages/SingleSignOnPage');\n\n            return {\n              Component: ProtectedSSO,\n            };\n          },\n        },\n      ]\n    : []),\n];\n", "import { Box, Button, Flex, Main, Typo<PERSON>, Link } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { NavLink, useNavigate } from 'react-router-dom';\nimport * as yup from 'yup';\n\nimport { Form } from '../../../components/Form';\nimport { InputRenderer } from '../../../components/FormInputs/Renderer';\nimport { Logo } from '../../../components/UnauthenticatedLogo';\nimport { useAPIErrorHandler } from '../../../hooks/useAPIErrorHandler';\nimport {\n  Column,\n  LayoutContent,\n  UnauthenticatedLayout,\n} from '../../../layouts/UnauthenticatedLayout';\nimport { useForgotPasswordMutation } from '../../../services/auth';\nimport { isBaseQueryError } from '../../../utils/baseQuery';\nimport { translatedErrors } from '../../../utils/translatedErrors';\n\nimport type { ForgotPassword } from '../../../../../shared/contracts/authentication';\n\nconst ForgotPassword = () => {\n  const navigate = useNavigate();\n  const { formatMessage } = useIntl();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n\n  const [forgotPassword, { error }] = useForgotPasswordMutation();\n\n  return (\n    <UnauthenticatedLayout>\n      <Main>\n        <LayoutContent>\n          <Column>\n            <Logo />\n            <Box paddingTop={6} paddingBottom={7}>\n              <Typography tag=\"h1\" variant=\"alpha\">\n                {formatMessage({\n                  id: 'Auth.form.button.password-recovery',\n                  defaultMessage: 'Password Recovery',\n                })}\n              </Typography>\n            </Box>\n            {error ? (\n              <Typography id=\"global-form-error\" role=\"alert\" tabIndex={-1} textColor=\"danger600\">\n                {isBaseQueryError(error)\n                  ? formatAPIError(error)\n                  : formatMessage({\n                      id: 'notification.error',\n                      defaultMessage: 'An error occurred',\n                    })}\n              </Typography>\n            ) : null}\n          </Column>\n          <Form\n            method=\"POST\"\n            initialValues={{\n              email: '',\n            }}\n            onSubmit={async (body) => {\n              const res = await forgotPassword(body);\n\n              if (!('error' in res)) {\n                navigate('/auth/forgot-password-success');\n              }\n            }}\n            validationSchema={yup.object().shape({\n              email: yup\n                .string()\n                .email(translatedErrors.email)\n                .required({\n                  id: translatedErrors.required.id,\n                  defaultMessage: 'This field is required.',\n                })\n                .nullable(),\n            })}\n          >\n            <Flex direction=\"column\" alignItems=\"stretch\" gap={6}>\n              {[\n                {\n                  label: formatMessage({ id: 'Auth.form.email.label', defaultMessage: 'Email' }),\n                  name: 'email',\n                  placeholder: formatMessage({\n                    id: 'Auth.form.email.placeholder',\n                    defaultMessage: '<EMAIL>',\n                  }),\n                  required: true,\n                  type: 'string' as const,\n                },\n              ].map((field) => (\n                <InputRenderer key={field.name} {...field} />\n              ))}\n              <Button type=\"submit\" fullWidth>\n                {formatMessage({\n                  id: 'Auth.form.button.forgot-password',\n                  defaultMessage: 'Send Email',\n                })}\n              </Button>\n            </Flex>\n          </Form>\n        </LayoutContent>\n        <Flex justifyContent=\"center\">\n          <Box paddingTop={4}>\n            <Link tag={NavLink} to=\"/auth/login\">\n              {formatMessage({ id: 'Auth.link.ready', defaultMessage: 'Ready to sign in?' })}\n            </Link>\n          </Box>\n        </Flex>\n      </Main>\n    </UnauthenticatedLayout>\n  );\n};\n\nexport { ForgotPassword };\n", "import { Box, Flex, Main, Typography, Link } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { NavLink } from 'react-router-dom';\n\nimport { Logo } from '../../../components/UnauthenticatedLogo';\nimport {\n  Column,\n  LayoutContent,\n  UnauthenticatedLayout,\n} from '../../../layouts/UnauthenticatedLayout';\n\nconst ForgotPasswordSuccess = () => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <UnauthenticatedLayout>\n      <Main>\n        <LayoutContent>\n          <Column>\n            <Logo />\n            <Box paddingTop={6} paddingBottom={7}>\n              <Typography tag=\"h1\" variant=\"alpha\">\n                {formatMessage({\n                  id: 'app.containers.AuthPage.ForgotPasswordSuccess.title',\n                  defaultMessage: 'Email sent',\n                })}\n              </Typography>\n            </Box>\n            <Typography>\n              {formatMessage({\n                id: 'app.containers.AuthPage.ForgotPasswordSuccess.text.email',\n                defaultMessage: 'It can take a few minutes to receive your password recovery link.',\n              })}\n            </Typography>\n            <Box paddingTop={4}>\n              <Typography>\n                {formatMessage({\n                  id: 'app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin',\n                  defaultMessage:\n                    'If you do not receive this link, please contact your administrator.',\n                })}\n              </Typography>\n            </Box>\n          </Column>\n        </LayoutContent>\n        <Flex justifyContent=\"center\">\n          <Box paddingTop={4}>\n            <Link tag={NavLink} to=\"/auth/login\">\n              {formatMessage({ id: 'Auth.link.signin', defaultMessage: 'Sign in' })}\n            </Link>\n          </Box>\n        </Flex>\n      </Main>\n    </UnauthenticatedLayout>\n  );\n};\n\nexport { ForgotPasswordSuccess };\n", "import * as React from 'react';\n\nimport { Box, Flex, Main, Typography, Link } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { NavLink, useLocation } from 'react-router-dom';\n\nimport { Logo } from '../../../components/UnauthenticatedLogo';\nimport {\n  Column,\n  LayoutContent,\n  UnauthenticatedLayout,\n} from '../../../layouts/UnauthenticatedLayout';\n\nconst Oops = () => {\n  const { formatMessage } = useIntl();\n  const { search: searchString } = useLocation();\n  const query = React.useMemo(() => new URLSearchParams(searchString), [searchString]);\n\n  const message =\n    query.get('info') ||\n    formatMessage({\n      id: 'Auth.components.Oops.text',\n      defaultMessage: 'Your account has been suspended.',\n    });\n\n  return (\n    <UnauthenticatedLayout>\n      <Main>\n        <LayoutContent>\n          <Column>\n            <Logo />\n            <Box paddingTop={6} paddingBottom={7}>\n              <Typography tag=\"h1\" variant=\"alpha\">\n                {formatMessage({ id: 'Auth.components.Oops.title', defaultMessage: 'Oops...' })}\n              </Typography>\n            </Box>\n            <Typography>{message}</Typography>\n            <Box paddingTop={4}>\n              <Typography>\n                {formatMessage({\n                  id: 'Auth.components.Oops.text.admin',\n                  defaultMessage: 'If this is a mistake, please contact your administrator.',\n                })}\n              </Typography>\n            </Box>\n          </Column>\n        </LayoutContent>\n        <Flex justifyContent=\"center\">\n          <Box paddingTop={4}>\n            <Link tag={NavLink} to=\"/auth/login\">\n              {formatMessage({ id: 'Auth.link.signin', defaultMessage: 'Sign in' })}\n            </Link>\n          </Box>\n        </Flex>\n      </Main>\n    </UnauthenticatedLayout>\n  );\n};\n\nexport { Oops };\n", "import * as React from 'react';\n\nimport { Box, Button, Flex, Grid, Typography, Link } from '@strapi/design-system';\nimport omit from 'lodash/omit';\nimport { useIntl } from 'react-intl';\nimport { NavLink, Navigate, useNavigate, useMatch, useLocation } from 'react-router-dom';\nimport { styled } from 'styled-components';\nimport * as yup from 'yup';\nimport { ValidationError } from 'yup';\n\nimport {\n  Register as RegisterUser,\n  RegisterAdmin,\n} from '../../../../../shared/contracts/authentication';\nimport { Form, FormHelpers } from '../../../components/Form';\nimport { InputRenderer } from '../../../components/FormInputs/Renderer';\nimport { useGuidedTour } from '../../../components/GuidedTour/Provider';\nimport { useNpsSurveySettings } from '../../../components/NpsSurvey';\nimport { Logo } from '../../../components/UnauthenticatedLogo';\nimport { useTypedDispatch } from '../../../core/store/hooks';\nimport { useNotification } from '../../../features/Notifications';\nimport { useTracking } from '../../../features/Tracking';\nimport { useAPIErrorHandler } from '../../../hooks/useAPIErrorHandler';\nimport { LayoutContent, UnauthenticatedLayout } from '../../../layouts/UnauthenticatedLayout';\nimport { login } from '../../../reducer';\nimport {\n  useGetRegistrationInfoQuery,\n  useRegisterAdminMutation,\n  useRegisterUserMutation,\n} from '../../../services/auth';\nimport { isBaseQueryError } from '../../../utils/baseQuery';\nimport { getByteSize } from '../../../utils/strings';\nimport { translatedErrors } from '../../../utils/translatedErrors';\n\nconst REGISTER_USER_SCHEMA = yup.object().shape({\n  firstname: yup.string().trim().required(translatedErrors.required).nullable(),\n  lastname: yup.string().nullable(),\n  password: yup\n    .string()\n    .min(8, {\n      id: translatedErrors.minLength.id,\n      defaultMessage: 'Password must be at least 8 characters',\n      values: { min: 8 },\n    })\n    .test(\n      'max-bytes',\n      {\n        id: 'components.Input.error.contain.maxBytes',\n        defaultMessage: 'Password must be less than 73 bytes',\n      },\n      function (value) {\n        if (!value || typeof value !== 'string') return true; // validated elsewhere\n\n        const byteSize = getByteSize(value);\n        return byteSize <= 72;\n      }\n    )\n    .matches(/[a-z]/, {\n      message: {\n        id: 'components.Input.error.contain.lowercase',\n        defaultMessage: 'Password must contain at least 1 lowercase letter',\n      },\n    })\n    .matches(/[A-Z]/, {\n      message: {\n        id: 'components.Input.error.contain.uppercase',\n        defaultMessage: 'Password must contain at least 1 uppercase letter',\n      },\n    })\n    .matches(/\\d/, {\n      message: {\n        id: 'components.Input.error.contain.number',\n        defaultMessage: 'Password must contain at least 1 number',\n      },\n    })\n    .required({\n      id: translatedErrors.required.id,\n      defaultMessage: 'Password is required',\n    })\n    .nullable(),\n  confirmPassword: yup\n    .string()\n    .required({\n      id: translatedErrors.required.id,\n      defaultMessage: 'Confirm password is required',\n    })\n    .oneOf([yup.ref('password'), null], {\n      id: 'components.Input.error.password.noMatch',\n      defaultMessage: 'Passwords must match',\n    })\n    .nullable(),\n  registrationToken: yup.string().required({\n    id: translatedErrors.required.id,\n    defaultMessage: 'Registration token is required',\n  }),\n});\n\nconst REGISTER_ADMIN_SCHEMA = yup.object().shape({\n  firstname: yup\n    .string()\n    .trim()\n    .required({\n      id: translatedErrors.required.id,\n      defaultMessage: 'Firstname is required',\n    })\n    .nullable(),\n  lastname: yup.string().nullable(),\n  password: yup\n    .string()\n    .min(8, {\n      id: translatedErrors.minLength.id,\n      defaultMessage: 'Password must be at least 8 characters',\n      values: { min: 8 },\n    })\n    .test(\n      'max-bytes',\n      {\n        id: 'components.Input.error.contain.maxBytes',\n        defaultMessage: 'Password must be less than 73 bytes',\n      },\n      function (value) {\n        if (!value) return true;\n        return new TextEncoder().encode(value).length <= 72;\n      }\n    )\n    .matches(/[a-z]/, {\n      message: {\n        id: 'components.Input.error.contain.lowercase',\n        defaultMessage: 'Password must contain at least 1 lowercase letter',\n      },\n    })\n    .matches(/[A-Z]/, {\n      message: {\n        id: 'components.Input.error.contain.uppercase',\n        defaultMessage: 'Password must contain at least 1 uppercase letter',\n      },\n    })\n    .matches(/\\d/, {\n      message: {\n        id: 'components.Input.error.contain.number',\n        defaultMessage: 'Password must contain at least 1 number',\n      },\n    })\n    .required({\n      id: translatedErrors.required.id,\n      defaultMessage: 'Password is required',\n    })\n    .nullable(),\n  confirmPassword: yup\n    .string()\n    .required({\n      id: translatedErrors.required.id,\n      defaultMessage: 'Confirm password is required',\n    })\n    .nullable()\n    .oneOf([yup.ref('password'), null], {\n      id: 'components.Input.error.password.noMatch',\n      defaultMessage: 'Passwords must match',\n    }),\n  email: yup\n    .string()\n    .email({\n      id: translatedErrors.email.id,\n      defaultMessage: 'Not a valid email',\n    })\n    .strict()\n    .lowercase({\n      id: translatedErrors.lowercase.id,\n      defaultMessage: 'Email must be lowercase',\n    })\n    .required({\n      id: translatedErrors.required.id,\n      defaultMessage: 'Email is required',\n    })\n    .nullable(),\n});\n\ninterface RegisterProps {\n  hasAdmin?: boolean;\n}\n\ninterface RegisterFormValues {\n  firstname: string;\n  lastname: string;\n  email: string;\n  password: string;\n  confirmPassword: string;\n  registrationToken: string | undefined;\n  news: boolean;\n}\n\nconst Register = ({ hasAdmin }: RegisterProps) => {\n  const { toggleNotification } = useNotification();\n  const navigate = useNavigate();\n  const [submitCount, setSubmitCount] = React.useState(0);\n  const [apiError, setApiError] = React.useState<string>();\n  const { trackUsage } = useTracking();\n  const { formatMessage } = useIntl();\n  const setSkipped = useGuidedTour('Register', (state) => state.setSkipped);\n  const { search: searchString } = useLocation();\n  const query = React.useMemo(() => new URLSearchParams(searchString), [searchString]);\n  const match = useMatch('/auth/:authType');\n  const {\n    _unstableFormatAPIError: formatAPIError,\n    _unstableFormatValidationErrors: formatValidationErrors,\n  } = useAPIErrorHandler();\n  const { setNpsSurveySettings } = useNpsSurveySettings();\n\n  const registrationToken = query.get('registrationToken');\n\n  const { data: userInfo, error } = useGetRegistrationInfoQuery(registrationToken as string, {\n    skip: !registrationToken,\n  });\n\n  React.useEffect(() => {\n    if (error) {\n      const message: string = isBaseQueryError(error)\n        ? formatAPIError(error)\n        : (error.message ?? '');\n\n      toggleNotification({\n        type: 'danger',\n        message,\n      });\n\n      navigate(`/auth/oops?info=${encodeURIComponent(message)}`);\n    }\n  }, [error, formatAPIError, navigate, toggleNotification]);\n\n  const [registerAdmin] = useRegisterAdminMutation();\n  const [registerUser] = useRegisterUserMutation();\n  const dispatch = useTypedDispatch();\n\n  const handleRegisterAdmin = async (\n    { news, ...body }: RegisterAdmin.Request['body'] & { news: boolean },\n    setFormErrors: FormHelpers<RegisterFormValues>['setErrors']\n  ) => {\n    const res = await registerAdmin(body);\n\n    if ('data' in res) {\n      dispatch(login({ token: res.data.token }));\n\n      const { roles } = res.data.user;\n\n      if (roles) {\n        const isUserSuperAdmin = roles.find(({ code }) => code === 'strapi-super-admin');\n\n        if (isUserSuperAdmin) {\n          localStorage.setItem('GUIDED_TOUR_SKIPPED', JSON.stringify(false));\n          setSkipped(false);\n          trackUsage('didLaunchGuidedtour');\n        }\n      }\n\n      if (news) {\n        // Only enable EE survey if user accepted the newsletter\n        setNpsSurveySettings((s) => ({ ...s, enabled: true }));\n\n        navigate({\n          pathname: '/usecase',\n          search: `?hasAdmin=${true}`,\n        });\n      } else {\n        navigate('/');\n      }\n    } else {\n      if (isBaseQueryError(res.error)) {\n        trackUsage('didNotCreateFirstAdmin');\n\n        if (res.error.name === 'ValidationError') {\n          setFormErrors(formatValidationErrors(res.error));\n          return;\n        }\n\n        setApiError(formatAPIError(res.error));\n      }\n    }\n  };\n\n  const handleRegisterUser = async (\n    { news, ...body }: RegisterUser.Request['body'] & { news: boolean },\n    setFormErrors: FormHelpers<RegisterFormValues>['setErrors']\n  ) => {\n    const res = await registerUser(body);\n\n    if ('data' in res) {\n      dispatch(login({ token: res.data.token }));\n\n      if (news) {\n        // Only enable EE survey if user accepted the newsletter\n        setNpsSurveySettings((s) => ({ ...s, enabled: true }));\n\n        navigate({\n          pathname: '/usecase',\n          search: `?hasAdmin=${hasAdmin}`,\n        });\n      } else {\n        navigate('/');\n      }\n    } else {\n      if (isBaseQueryError(res.error)) {\n        trackUsage('didNotCreateFirstAdmin');\n\n        if (res.error.name === 'ValidationError') {\n          setFormErrors(formatValidationErrors(res.error));\n          return;\n        }\n\n        setApiError(formatAPIError(res.error));\n      }\n    }\n  };\n\n  if (\n    !match ||\n    (match.params.authType !== 'register' && match.params.authType !== 'register-admin')\n  ) {\n    return <Navigate to=\"/\" />;\n  }\n\n  const isAdminRegistration = match.params.authType === 'register-admin';\n\n  const schema = isAdminRegistration ? REGISTER_ADMIN_SCHEMA : REGISTER_USER_SCHEMA;\n\n  return (\n    <UnauthenticatedLayout>\n      <LayoutContent>\n        <Flex direction=\"column\" alignItems=\"center\" gap={3}>\n          <Logo />\n\n          <Typography tag=\"h1\" variant=\"alpha\" textAlign=\"center\">\n            {formatMessage({\n              id: 'Auth.form.welcome.title',\n              defaultMessage: 'Welcome to Strapi!',\n            })}\n          </Typography>\n          <Typography variant=\"epsilon\" textColor=\"neutral600\" textAlign=\"center\">\n            {formatMessage({\n              id: 'Auth.form.register.subtitle',\n              defaultMessage:\n                'Credentials are only used to authenticate in Strapi. All saved data will be stored in your database.',\n            })}\n          </Typography>\n          {apiError ? (\n            <Typography id=\"global-form-error\" role=\"alert\" tabIndex={-1} textColor=\"danger600\">\n              {apiError}\n            </Typography>\n          ) : null}\n        </Flex>\n        <Form\n          method=\"POST\"\n          initialValues={\n            {\n              firstname: userInfo?.firstname || '',\n              lastname: userInfo?.lastname || '',\n              email: userInfo?.email || '',\n              password: '',\n              confirmPassword: '',\n              registrationToken: registrationToken || undefined,\n              news: false,\n            } satisfies RegisterFormValues\n          }\n          onSubmit={async (data, helpers) => {\n            const normalizedData = normalizeData(data);\n\n            try {\n              await schema.validate(normalizedData, { abortEarly: false });\n\n              if (submitCount > 0 && isAdminRegistration) {\n                trackUsage('didSubmitWithErrorsFirstAdmin', { count: submitCount.toString() });\n              }\n\n              if (normalizedData.registrationToken) {\n                handleRegisterUser(\n                  {\n                    userInfo: omit(normalizedData, [\n                      'registrationToken',\n                      'confirmPassword',\n                      'email',\n                      'news',\n                    ]),\n                    registrationToken: normalizedData.registrationToken,\n                    news: normalizedData.news,\n                  },\n                  helpers.setErrors\n                );\n              } else {\n                await handleRegisterAdmin(\n                  omit(normalizedData, ['registrationToken', 'confirmPassword']),\n                  helpers.setErrors\n                );\n              }\n            } catch (err) {\n              if (err instanceof ValidationError) {\n                helpers.setErrors(\n                  err.inner.reduce<Record<string, string>>((acc, { message, path }) => {\n                    if (path && typeof message === 'object') {\n                      acc[path] = formatMessage(message);\n                    }\n                    return acc;\n                  }, {})\n                );\n              }\n              setSubmitCount(submitCount + 1);\n            }\n          }}\n        >\n          <Flex direction=\"column\" alignItems=\"stretch\" gap={6} marginTop={7}>\n            <Grid.Root gap={4}>\n              {[\n                {\n                  label: formatMessage({\n                    id: 'Auth.form.firstname.label',\n                    defaultMessage: 'Firstname',\n                  }),\n                  name: 'firstname',\n                  required: true,\n                  size: 6,\n                  type: 'string' as const,\n                },\n                {\n                  label: formatMessage({\n                    id: 'Auth.form.lastname.label',\n                    defaultMessage: 'Lastname',\n                  }),\n                  name: 'lastname',\n                  size: 6,\n                  type: 'string' as const,\n                },\n                {\n                  disabled: !isAdminRegistration,\n                  label: formatMessage({\n                    id: 'Auth.form.email.label',\n                    defaultMessage: 'Email',\n                  }),\n                  name: 'email',\n                  required: true,\n                  size: 12,\n                  type: 'email' as const,\n                },\n                {\n                  hint: formatMessage({\n                    id: 'Auth.form.password.hint',\n                    defaultMessage:\n                      'Must be at least 8 characters, 1 uppercase, 1 lowercase & 1 number',\n                  }),\n                  label: formatMessage({\n                    id: 'global.password',\n                    defaultMessage: 'Password',\n                  }),\n                  name: 'password',\n                  required: true,\n                  size: 12,\n                  type: 'password' as const,\n                },\n                {\n                  label: formatMessage({\n                    id: 'Auth.form.confirmPassword.label',\n                    defaultMessage: 'Confirm Password',\n                  }),\n                  name: 'confirmPassword',\n                  required: true,\n                  size: 12,\n                  type: 'password' as const,\n                },\n                {\n                  label: formatMessage(\n                    {\n                      id: 'Auth.form.register.news.label',\n                      defaultMessage:\n                        'Keep me updated about new features & upcoming improvements (by doing this you accept the {terms} and the {policy}).',\n                    },\n                    {\n                      terms: (\n                        <A target=\"_blank\" href=\"https://strapi.io/terms\" rel=\"noreferrer\">\n                          {formatMessage({\n                            id: 'Auth.privacy-policy-agreement.terms',\n                            defaultMessage: 'terms',\n                          })}\n                        </A>\n                      ),\n                      policy: (\n                        <A target=\"_blank\" href=\"https://strapi.io/privacy\" rel=\"noreferrer\">\n                          {formatMessage({\n                            id: 'Auth.privacy-policy-agreement.policy',\n                            defaultMessage: 'policy',\n                          })}\n                        </A>\n                      ),\n                    }\n                  ),\n                  name: 'news',\n                  size: 12,\n                  type: 'checkbox' as const,\n                },\n              ].map(({ size, ...field }) => (\n                <Grid.Item key={field.name} col={size} direction=\"column\" alignItems=\"stretch\">\n                  <InputRenderer {...field} />\n                </Grid.Item>\n              ))}\n            </Grid.Root>\n            <Button fullWidth size=\"L\" type=\"submit\">\n              {formatMessage({\n                id: 'Auth.form.button.register',\n                defaultMessage: \"Let's start\",\n              })}\n            </Button>\n          </Flex>\n        </Form>\n        {match?.params.authType === 'register' && (\n          <Box paddingTop={4}>\n            <Flex justifyContent=\"center\">\n              <Link tag={NavLink} to=\"/auth/login\">\n                {formatMessage({\n                  id: 'Auth.link.signin.account',\n                  defaultMessage: 'Already have an account?',\n                })}\n              </Link>\n            </Flex>\n          </Box>\n        )}\n      </LayoutContent>\n    </UnauthenticatedLayout>\n  );\n};\n\ninterface RegisterFormValues {\n  firstname: string;\n  lastname: string;\n  email: string;\n  password: string;\n  confirmPassword: string;\n  registrationToken: string | undefined;\n  news: boolean;\n}\n\ntype StringKeys<T> = {\n  [K in keyof T]: T[K] extends string | undefined ? K : never;\n}[keyof T];\n\n/**\n * @description Trims all values but the password & sets lastName to null if it's a falsey value.\n */\nfunction normalizeData(data: RegisterFormValues) {\n  return Object.entries(data).reduce(\n    (acc, [key, value]) => {\n      type PasswordKeys = Extract<keyof RegisterFormValues, 'password' | 'confirmPassword'>;\n      type RegisterFormStringValues = Exclude<\n        keyof Pick<RegisterFormValues, StringKeys<RegisterFormValues>>,\n        PasswordKeys\n      >;\n\n      if (!['password', 'confirmPassword'].includes(key) && typeof value === 'string') {\n        acc[key as RegisterFormStringValues] = value.trim();\n\n        if (key === 'lastname') {\n          acc[key] = value || undefined;\n        }\n      } else {\n        acc[key as PasswordKeys] = value;\n      }\n\n      return acc;\n    },\n    {} as {\n      firstname: string;\n      lastname: string | undefined;\n      email: string;\n      password: string;\n      confirmPassword: string;\n      registrationToken: string | undefined;\n      news: boolean;\n    }\n  );\n}\n\nconst A = styled.a`\n  color: ${({ theme }) => theme.colors.primary600};\n`;\n\nexport { Register };\nexport type { RegisterProps };\n", "import * as React from 'react';\n\nimport { Box, Button, Flex, Main, Typography, Link } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { NavLink, useNavigate, Navigate, useLocation } from 'react-router-dom';\nimport * as yup from 'yup';\n\nimport { ResetPassword } from '../../../../../shared/contracts/authentication';\nimport { Form } from '../../../components/Form';\nimport { InputRenderer } from '../../../components/FormInputs/Renderer';\nimport { Logo } from '../../../components/UnauthenticatedLogo';\nimport { useTypedDispatch } from '../../../core/store/hooks';\nimport { useAPIErrorHandler } from '../../../hooks/useAPIErrorHandler';\nimport {\n  Column,\n  LayoutContent,\n  UnauthenticatedLayout,\n} from '../../../layouts/UnauthenticatedLayout';\nimport { login } from '../../../reducer';\nimport { useResetPasswordMutation } from '../../../services/auth';\nimport { isBaseQueryError } from '../../../utils/baseQuery';\nimport { getByteSize } from '../../../utils/strings';\nimport { translatedErrors } from '../../../utils/translatedErrors';\n\nconst RESET_PASSWORD_SCHEMA = yup.object().shape({\n  password: yup\n    .string()\n    .min(8, {\n      id: translatedErrors.minLength.id,\n      defaultMessage: 'Password must be at least 8 characters',\n      values: { min: 8 },\n    })\n    // bcrypt has a max length of 72 bytes (not characters!)\n    .test(\n      'required-byte-size',\n      {\n        id: 'components.Input.error.contain.maxBytes',\n        defaultMessage: 'Password must be less than 73 bytes',\n      },\n      function (value) {\n        if (!value || typeof value !== 'string') return true; // validated elsewhere\n\n        const byteSize = getByteSize(value);\n        return byteSize <= 72;\n      }\n    )\n    .matches(/[a-z]/, {\n      message: {\n        id: 'components.Input.error.contain.lowercase',\n        defaultMessage: 'Password must contain at least 1 lowercase letter',\n      },\n    })\n    .matches(/[A-Z]/, {\n      message: {\n        id: 'components.Input.error.contain.uppercase',\n        defaultMessage: 'Password must contain at least 1 uppercase letter',\n      },\n    })\n    .matches(/\\d/, {\n      message: {\n        id: 'components.Input.error.contain.number',\n        defaultMessage: 'Password must contain at least 1 number',\n      },\n    })\n    .required({\n      id: translatedErrors.required.id,\n      defaultMessage: 'Password is required',\n    })\n    .nullable(),\n  confirmPassword: yup\n    .string()\n    .required({\n      id: translatedErrors.required.id,\n      defaultMessage: 'Confirm password is required',\n    })\n    .oneOf([yup.ref('password'), null], {\n      id: 'components.Input.error.password.noMatch',\n      defaultMessage: 'Passwords must match',\n    })\n    .nullable(),\n});\n\nconst ResetPassword = () => {\n  const { formatMessage } = useIntl();\n  const dispatch = useTypedDispatch();\n  const navigate = useNavigate();\n  const { search: searchString } = useLocation();\n  const query = React.useMemo(() => new URLSearchParams(searchString), [searchString]);\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n\n  const [resetPassword, { error }] = useResetPasswordMutation();\n\n  const handleSubmit = async (body: ResetPassword.Request['body']) => {\n    const res = await resetPassword(body);\n\n    if ('data' in res) {\n      dispatch(login({ token: res.data.token }));\n      navigate('/');\n    }\n  };\n  /**\n   * If someone doesn't have a reset password token\n   * then they should just be redirected back to the login page.\n   */\n  if (!query.get('code')) {\n    return <Navigate to=\"/auth/login\" />;\n  }\n\n  return (\n    <UnauthenticatedLayout>\n      <Main>\n        <LayoutContent>\n          <Column>\n            <Logo />\n            <Box paddingTop={6} paddingBottom={7}>\n              <Typography tag=\"h1\" variant=\"alpha\">\n                {formatMessage({\n                  id: 'global.reset-password',\n                  defaultMessage: 'Reset password',\n                })}\n              </Typography>\n            </Box>\n            {error ? (\n              <Typography id=\"global-form-error\" role=\"alert\" tabIndex={-1} textColor=\"danger600\">\n                {isBaseQueryError(error)\n                  ? formatAPIError(error)\n                  : formatMessage({\n                      id: 'notification.error',\n                      defaultMessage: 'An error occurred',\n                    })}\n              </Typography>\n            ) : null}\n          </Column>\n          <Form\n            method=\"POST\"\n            initialValues={{\n              password: '',\n              confirmPassword: '',\n            }}\n            onSubmit={(values) => {\n              // We know query.code is defined because we check for it above.\n              handleSubmit({ password: values.password, resetPasswordToken: query.get('code')! });\n            }}\n            validationSchema={RESET_PASSWORD_SCHEMA}\n          >\n            <Flex direction=\"column\" alignItems=\"stretch\" gap={6}>\n              {[\n                {\n                  hint: formatMessage({\n                    id: 'Auth.form.password.hint',\n                    defaultMessage:\n                      'Password must contain at least 8 characters, 1 uppercase, 1 lowercase and 1 number',\n                  }),\n                  label: formatMessage({\n                    id: 'global.password',\n                    defaultMessage: 'Password',\n                  }),\n                  name: 'password',\n                  required: true,\n                  type: 'password' as const,\n                },\n                {\n                  label: formatMessage({\n                    id: 'Auth.form.confirmPassword.label',\n                    defaultMessage: 'Confirm Password',\n                  }),\n                  name: 'confirmPassword',\n                  required: true,\n                  type: 'password' as const,\n                },\n              ].map((field) => (\n                <InputRenderer key={field.name} {...field} />\n              ))}\n              <Button fullWidth type=\"submit\">\n                {formatMessage({\n                  id: 'global.change-password',\n                  defaultMessage: 'Change password',\n                })}\n              </Button>\n            </Flex>\n          </Form>\n        </LayoutContent>\n        <Flex justifyContent=\"center\">\n          <Box paddingTop={4}>\n            <Link tag={NavLink} to=\"/auth/login\">\n              {formatMessage({ id: 'Auth.link.ready', defaultMessage: 'Ready to sign in?' })}\n            </Link>\n          </Box>\n        </Flex>\n      </Main>\n    </UnauthenticatedLayout>\n  );\n};\n\nexport { ResetPassword };\n", "import type { ComponentType } from 'react';\n\nimport { ForgotPassword } from './components/ForgotPassword';\nimport { ForgotPasswordSuccess } from './components/ForgotPasswordSuccess';\nimport { Oops } from './components/Oops';\nimport { Register, RegisterProps } from './components/Register';\nimport { ResetPassword } from './components/ResetPassword';\n\nexport type AuthType =\n  | 'login'\n  | 'register'\n  | 'register-admin'\n  | 'forgot-password'\n  | 'reset-password'\n  | 'forgot-password-success'\n  | 'oops'\n  | 'providers';\n\nexport type FormDictionary = Record<AuthType, ComponentType | ComponentType<RegisterProps>>;\n\nexport const FORMS = {\n  'forgot-password': ForgotPassword,\n  'forgot-password-success': ForgotPasswordSuccess,\n  // the `Component` attribute is set after all forms and CE/EE components are loaded, but since we\n  // are here outside of a React component we can not use the hook directly\n  login: () => null,\n  oops: Oops,\n  register: Register,\n  'register-admin': Register,\n  'reset-password': ResetPassword,\n  providers: () => null,\n} satisfies FormDictionary;\n", "import { Navigate, useLocation, useMatch } from 'react-router-dom';\n\nimport { useAuth } from '../../features/Auth';\nimport { useEnterprise } from '../../hooks/useEnterprise';\nimport { useInitQuery } from '../../services/admin';\n\nimport { Login as LoginCE } from './components/Login';\nimport { FORMS, FormDictionary } from './constants';\n\n/* -------------------------------------------------------------------------------------------------\n * AuthPage\n * -----------------------------------------------------------------------------------------------*/\n\nconst AuthPage = () => {\n  const { search } = useLocation();\n  const match = useMatch('/auth/:authType');\n  const authType = match?.params.authType;\n  const { data } = useInitQuery();\n  const { hasAdmin } = data ?? {};\n  const Login = useEnterprise(\n    LoginCE,\n    async () => (await import('../../../../ee/admin/src/pages/AuthPage/components/Login')).LoginEE\n  );\n  const forms = useEnterprise<FormDictionary, Partial<FormDictionary>>(\n    FORMS,\n    async () => (await import('../../../../ee/admin/src/pages/AuthPage/constants')).FORMS,\n    {\n      combine(ceForms, eeForms) {\n        return {\n          ...ceForms,\n          ...eeForms,\n        };\n      },\n      defaultValue: FORMS,\n    }\n  );\n\n  const { token } = useAuth('AuthPage', (auth) => auth);\n\n  if (!authType || !forms) {\n    return <Navigate to=\"/\" />;\n  }\n\n  const Component = forms[authType as keyof FormDictionary];\n\n  // Redirect the user to the login page if\n  // the endpoint does not exists\n  if (!Component) {\n    return <Navigate to=\"/\" />;\n  }\n\n  // User is already logged in\n  if (authType !== 'register-admin' && authType !== 'register' && token) {\n    return <Navigate to=\"/\" />;\n  }\n\n  // there is already an admin user oo\n  if (hasAdmin && authType === 'register-admin' && token) {\n    return <Navigate to=\"/\" />;\n  }\n\n  // Redirect the user to the register-admin if it is the first user\n  if (!hasAdmin && authType !== 'register-admin') {\n    return (\n      <Navigate\n        to={{\n          pathname: '/auth/register-admin',\n          // Forward the `?redirectTo` from /auth/login\n          // /abc => /auth/login?redirectTo=%2Fabc => /auth/register-admin?redirectTo=%2Fabc\n          search,\n        }}\n      />\n    );\n  }\n\n  if (Login && authType === 'login') {\n    // Assign the component to render for the login form\n    return <Login />;\n  } else if (authType === 'login' && !Login) {\n    // block rendering until the Login EE component is fully loaded\n    return null;\n  }\n\n  return <Component hasAdmin={hasAdmin} />;\n};\n\nexport { AuthPage };\n", "import type { RouteObject } from 'react-router-dom';\n\nexport const ROUTES_CE: RouteObject[] = [\n  {\n    lazy: async () => {\n      const { ProtectedListPage } = await import('./pages/Roles/ListPage');\n\n      return {\n        Component: ProtectedListPage,\n      };\n    },\n    path: 'roles',\n  },\n  {\n    lazy: async () => {\n      const { ProtectedCreatePage } = await import('./pages/Roles/CreatePage');\n\n      return {\n        Component: ProtectedCreatePage,\n      };\n    },\n    path: 'roles/duplicate/:id',\n  },\n  {\n    lazy: async () => {\n      const { ProtectedCreatePage } = await import('./pages/Roles/CreatePage');\n\n      return {\n        Component: ProtectedCreatePage,\n      };\n    },\n    path: 'roles/new',\n  },\n  {\n    lazy: async () => {\n      const { ProtectedEditPage } = await import('./pages/Roles/EditPage');\n\n      return {\n        Component: ProtectedEditPage,\n      };\n    },\n    path: 'roles/:id',\n  },\n  {\n    lazy: async () => {\n      const { ProtectedListPage } = await import('./pages/Users/<USER>');\n\n      return {\n        Component: ProtectedListPage,\n      };\n    },\n    path: 'users',\n  },\n  {\n    lazy: async () => {\n      const { ProtectedEditPage } = await import('./pages/Users/<USER>');\n\n      return {\n        Component: ProtectedEditPage,\n      };\n    },\n    path: 'users/:id',\n  },\n  {\n    lazy: async () => {\n      const { ProtectedCreatePage } = await import('./pages/Webhooks/CreatePage');\n\n      return {\n        Component: ProtectedCreatePage,\n      };\n    },\n    path: 'webhooks/create',\n  },\n  {\n    lazy: async () => {\n      const editWebhook = await import('./pages/Webhooks/EditPage');\n\n      return {\n        Component: editWebhook.ProtectedEditPage,\n      };\n    },\n    path: 'webhooks/:id',\n  },\n  {\n    lazy: async () => {\n      const { ProtectedListPage } = await import('./pages/Webhooks/ListPage');\n\n      return {\n        Component: ProtectedListPage,\n      };\n    },\n    path: 'webhooks',\n  },\n  {\n    lazy: async () => {\n      const { ProtectedListView } = await import('./pages/ApiTokens/ListView');\n\n      return {\n        Component: ProtectedListView,\n      };\n    },\n    path: 'api-tokens',\n  },\n  {\n    lazy: async () => {\n      const { ProtectedCreateView } = await import('./pages/ApiTokens/CreateView');\n\n      return {\n        Component: ProtectedCreateView,\n      };\n    },\n    path: 'api-tokens/create',\n  },\n  {\n    lazy: async () => {\n      const { ProtectedEditView } = await import('./pages/ApiTokens/EditView/EditViewPage');\n\n      return {\n        Component: ProtectedEditView,\n      };\n    },\n    path: 'api-tokens/:id',\n  },\n  {\n    lazy: async () => {\n      const { ProtectedCreateView } = await import('./pages/TransferTokens/CreateView');\n\n      return {\n        Component: ProtectedCreateView,\n      };\n    },\n    path: 'transfer-tokens/create',\n  },\n  {\n    lazy: async () => {\n      const { ProtectedListView } = await import('./pages/TransferTokens/ListView');\n\n      return {\n        Component: ProtectedListView,\n      };\n    },\n    path: 'transfer-tokens',\n  },\n  {\n    lazy: async () => {\n      const { ProtectedEditView } = await import('./pages/TransferTokens/EditView');\n\n      return {\n        Component: ProtectedEditView,\n      };\n    },\n    path: 'transfer-tokens/:id',\n  },\n  {\n    lazy: async () => {\n      const { ProtectedInstalledPlugins } = await import('./pages/InstalledPlugins');\n\n      return {\n        Component: ProtectedInstalledPlugins,\n      };\n    },\n    path: 'list-plugins',\n  },\n\n  {\n    lazy: async () => {\n      const { PurchaseAuditLogs } = await import('./pages/PurchaseAuditLogs');\n\n      return {\n        Component: PurchaseAuditLogs,\n      };\n    },\n    path: 'purchase-audit-logs',\n  },\n  {\n    lazy: async () => {\n      const { PurchaseSingleSignOn } = await import('./pages/PurchaseSingleSignOn');\n\n      return {\n        Component: PurchaseSingleSignOn,\n      };\n    },\n    path: 'purchase-single-sign-on',\n  },\n  {\n    lazy: async () => {\n      const { PurchaseContentHistory } = await import('./pages/PurchaseContentHistory');\n\n      return {\n        Component: PurchaseContentHistory,\n      };\n    },\n    path: 'purchase-content-history',\n  },\n];\n", "/* eslint-disable check-file/filename-naming-convention */\n\nimport { RouteObject } from 'react-router-dom';\n\nimport { getEERoutes as getBaseEERoutes } from '../../ee/admin/src/constants';\nimport { getEERoutes as getSettingsEERoutes } from '../../ee/admin/src/pages/SettingsPage/constants';\n\nimport { AuthPage } from './pages/Auth/AuthPage';\nimport { ROUTES_CE } from './pages/Settings/constants';\n\n/**\n * These are routes we don't want to be able to be changed by plugins.\n */\nconst getImmutableRoutes = (): RouteObject[] => [\n  {\n    path: 'usecase',\n    lazy: async () => {\n      const { PrivateUseCasePage } = await import('./pages/UseCasePage');\n\n      return {\n        Component: PrivateUseCasePage,\n      };\n    },\n  },\n  // this needs to go before auth/:authType because otherwise it won't match the route\n  ...getBaseEERoutes(),\n  {\n    path: 'auth/:authType',\n    element: <AuthPage />,\n  },\n];\n\nconst getInitialRoutes = (): RouteObject[] => [\n  {\n    index: true,\n    lazy: async () => {\n      const { HomePage } = await import('./pages/Home/HomePage');\n\n      return {\n        Component: HomePage,\n      };\n    },\n  },\n  {\n    path: 'me',\n    lazy: async () => {\n      const { ProfilePage } = await import('./pages/ProfilePage');\n\n      return {\n        Component: ProfilePage,\n      };\n    },\n  },\n  {\n    path: 'marketplace',\n    lazy: async () => {\n      const { ProtectedMarketplacePage } = await import('./pages/Marketplace/MarketplacePage');\n\n      return {\n        Component: ProtectedMarketplacePage,\n      };\n    },\n  },\n  {\n    path: 'settings/*',\n    lazy: async () => {\n      const { Layout } = await import('./pages/Settings/Layout');\n\n      return {\n        Component: Layout,\n      };\n    },\n    children: [\n      {\n        path: 'application-infos',\n        lazy: async () => {\n          const { ApplicationInfoPage } = await import(\n            './pages/Settings/pages/ApplicationInfo/ApplicationInfoPage'\n          );\n\n          return {\n            Component: ApplicationInfoPage,\n          };\n        },\n      },\n      // ...Object.values(this.settings).flatMap(({ links }) =>\n      //   links.map(({ to, Component }) => ({\n      //     path: `${to}/*`,\n      //     element: (\n      //       <React.Suspense fallback={<Page.Loading />}>\n      //         <Component />\n      //       </React.Suspense>\n      //     ),\n      //   }))\n      // ),\n      ...[...getSettingsEERoutes(), ...ROUTES_CE].filter(\n        (route, index, refArray) => refArray.findIndex((obj) => obj.path === route.path) === index\n      ),\n    ],\n  },\n];\n\nexport { getImmutableRoutes, getInitialRoutes };\n", "/* eslint-disable check-file/filename-naming-convention */\nimport * as React from 'react';\n\nimport invariant from 'invariant';\nimport { MessageDescriptor, PrimitiveType } from 'react-intl';\nimport { Provider } from 'react-redux';\nimport { createBrowserRouter, createMemoryRouter, RouteObject } from 'react-router-dom';\n\nimport { App } from '../../App';\nimport { ErrorElement } from '../../components/ErrorElement';\nimport { LanguageProvider } from '../../components/LanguageProvider';\nimport { Theme } from '../../components/Theme';\nimport { Permission } from '../../features/Auth';\nimport { NotFoundPage } from '../../pages/NotFoundPage';\nimport { getImmutableRoutes } from '../../router';\nimport { StrapiApp } from '../../StrapiApp';\n\ntype IRouter = ReturnType<typeof createBrowserRouter> | ReturnType<typeof createMemoryRouter>;\n\ntype Reducer<Config extends object> = (prev: Config[]) => Config[];\n\ninterface MenuItem {\n  to: string;\n  icon: React.ElementType;\n  intlLabel: MessageDescriptor & { values?: Record<string, PrimitiveType> };\n  permissions: Permission[];\n  notificationsCount?: number;\n  Component?: React.LazyExoticComponent<React.ComponentType>;\n  exact?: boolean;\n  position?: number;\n  licenseOnly?: boolean;\n}\n\ninterface StrapiAppSettingLink extends Omit<MenuItem, 'icon' | 'notificationCount'> {\n  id: string;\n}\n\ninterface UnloadedSettingsLink extends Omit<StrapiAppSettingLink, 'Component'> {\n  Component?: () => Promise<{ default: React.ComponentType }>;\n}\n\ninterface StrapiAppSetting {\n  id: string;\n  intlLabel: MessageDescriptor & {\n    values?: Record<string, PrimitiveType>;\n  };\n  links: Omit<StrapiAppSettingLink, 'Component'>[];\n}\n\ninterface RouterOptions {\n  basename?: string;\n  memory?: boolean;\n}\n\nclass Router {\n  private _routes: RouteObject[] = [];\n  private router: IRouter | null = null;\n  private _menu: Omit<MenuItem, 'Component'>[] = [];\n  private _settings: Record<string, StrapiAppSetting> = {\n    global: {\n      id: 'global',\n      intlLabel: {\n        id: 'Settings.global',\n        defaultMessage: 'Global Settings',\n      },\n      links: [],\n    },\n  };\n\n  constructor(initialRoutes: RouteObject[]) {\n    this._routes = initialRoutes;\n  }\n\n  get routes() {\n    return this._routes;\n  }\n\n  get menu() {\n    return this._menu;\n  }\n\n  get settings() {\n    return this._settings;\n  }\n\n  /**\n   * @internal This method is used internally by Strapi to create the router.\n   * It should not be used by plugins, doing so will likely break the application.\n   */\n  createRouter(strapi: StrapiApp, { memory, ...opts }: RouterOptions = {}) {\n    const routes = [\n      {\n        path: '/*',\n        errorElement: (\n          <Provider store={strapi.store!}>\n            <LanguageProvider messages={strapi.configurations.translations}>\n              <Theme themes={strapi.configurations.themes}>\n                <ErrorElement />\n              </Theme>\n            </LanguageProvider>\n          </Provider>\n        ),\n        element: <App strapi={strapi} store={strapi.store!} />,\n        children: [\n          ...getImmutableRoutes(),\n          {\n            path: '/*',\n            lazy: async () => {\n              const { PrivateAdminLayout } = await import('../../layouts/AuthenticatedLayout');\n\n              return {\n                Component: PrivateAdminLayout,\n              };\n            },\n            children: [\n              ...this.routes,\n              {\n                path: '*',\n                element: <NotFoundPage />,\n              },\n            ],\n          },\n        ],\n      },\n    ];\n\n    if (memory) {\n      this.router = createMemoryRouter(routes, opts);\n    } else {\n      this.router = createBrowserRouter(routes, opts);\n    }\n\n    return this.router;\n  }\n\n  public addMenuLink = (\n    link: Omit<MenuItem, 'Component'> & {\n      Component: () => Promise<{ default: React.ComponentType }>;\n    }\n  ) => {\n    invariant(link.to, `[${link.intlLabel.defaultMessage}]: link.to should be defined`);\n    invariant(\n      typeof link.to === 'string',\n      `[${\n        link.intlLabel.defaultMessage\n      }]: Expected link.to to be a string instead received ${typeof link.to}`\n    );\n    invariant(\n      link.intlLabel?.id && link.intlLabel?.defaultMessage,\n      `[${link.intlLabel.defaultMessage}]: link.intlLabel.id & link.intlLabel.defaultMessage should be defined`\n    );\n    invariant(\n      !link.Component || (link.Component && typeof link.Component === 'function'),\n      `[${link.intlLabel.defaultMessage}]: link.Component must be a function returning a Promise that returns a default component. Please use: \\`Component: () => import(path)\\` instead.`\n    );\n\n    if (\n      !link.Component ||\n      (link.Component &&\n        typeof link.Component === 'function' &&\n        // @ts-expect-error – shh\n        link.Component[Symbol.toStringTag] === 'AsyncFunction')\n    ) {\n      console.warn(\n        `\n      [${link.intlLabel.defaultMessage}]: [deprecated] addMenuLink() was called with an async Component from the plugin \"${link.intlLabel.defaultMessage}\". This will be removed in the future. Please use: \\`Component: () => import(path)\\` ensuring you return a default export instead.\n      `.trim()\n      );\n    }\n\n    if (link.to.startsWith('/')) {\n      console.warn(\n        `[${link.intlLabel.defaultMessage}]: the \\`to\\` property of your menu link is an absolute path, it should be relative to the root of the application. This has been corrected for you but will be removed in a future version of Strapi.`\n      );\n\n      link.to = link.to.slice(1);\n    }\n\n    const { Component, ...restLink } = link;\n\n    if (Component) {\n      this._routes.push({\n        path: `${link.to}/*`,\n        lazy: async () => {\n          const mod = await Component();\n\n          if ('default' in mod) {\n            return { Component: mod.default };\n          } else {\n            return { Component: mod };\n          }\n        },\n      });\n    }\n\n    this.menu.push(restLink);\n  };\n\n  public addSettingsLink(\n    section: Pick<StrapiAppSetting, 'id' | 'intlLabel'> & { links: UnloadedSettingsLink[] },\n    links?: never\n  ): void;\n  public addSettingsLink(\n    sectionId: string | Pick<StrapiAppSetting, 'id' | 'intlLabel'>,\n    link: UnloadedSettingsLink\n  ): void;\n  public addSettingsLink(\n    sectionId: string | Pick<StrapiAppSetting, 'id' | 'intlLabel'>,\n    link: UnloadedSettingsLink[]\n  ): void;\n  public addSettingsLink(\n    section:\n      | string\n      | Pick<StrapiAppSetting, 'id' | 'intlLabel'>\n      | (Pick<StrapiAppSetting, 'id' | 'intlLabel'> & { links: UnloadedSettingsLink[] }),\n    link?: UnloadedSettingsLink | UnloadedSettingsLink[]\n  ): void {\n    if (typeof section === 'object' && 'links' in section) {\n      /**\n       * Someone has passed an entire pre-configured section object\n       */\n      invariant(section.id, 'section.id should be defined');\n      invariant(\n        section.intlLabel?.id && section.intlLabel?.defaultMessage,\n        'section.intlLabel should be defined'\n      );\n      invariant(this.settings[section.id] === undefined, 'A similar section already exists');\n      invariant(Array.isArray(section.links), 'TypeError expected links to be an array');\n\n      this.settings[section.id] = { ...section, links: [] };\n\n      section.links.forEach((link) => {\n        this.createSettingsLink(section.id, link);\n      });\n    } else if (typeof section === 'object' && link) {\n      /**\n       * we need to create the section first\n       */\n      invariant(section.id, 'section.id should be defined');\n      invariant(\n        section.intlLabel?.id && section.intlLabel?.defaultMessage,\n        'section.intlLabel should be defined'\n      );\n      invariant(this.settings[section.id] === undefined, 'A similar section already exists');\n\n      this.settings[section.id] = { ...section, links: [] };\n\n      if (Array.isArray(link)) {\n        link.forEach((l) => this.createSettingsLink(section.id, l));\n      } else {\n        this.createSettingsLink(section.id, link);\n      }\n    } else if (typeof section === 'string' && link) {\n      if (Array.isArray(link)) {\n        link.forEach((l) => this.createSettingsLink(section, l));\n      } else {\n        this.createSettingsLink(section, link);\n      }\n    } else {\n      throw new Error(\n        'Invalid arguments provided to addSettingsLink, at minimum a sectionId and link are required.'\n      );\n    }\n  }\n\n  private createSettingsLink = (sectionId: string, link: UnloadedSettingsLink) => {\n    invariant(this._settings[sectionId], 'The section does not exist');\n\n    invariant(link.id, `[${link.intlLabel.defaultMessage}]: link.id should be defined`);\n    invariant(\n      link.intlLabel?.id && link.intlLabel?.defaultMessage,\n      `[${link.intlLabel.defaultMessage}]: link.intlLabel.id & link.intlLabel.defaultMessage`\n    );\n    invariant(link.to, `[${link.intlLabel.defaultMessage}]: link.to should be defined`);\n    invariant(\n      !link.Component || (link.Component && typeof link.Component === 'function'),\n      `[${link.intlLabel.defaultMessage}]: link.Component must be a function returning a Promise. Please use: \\`Component: () => import(path)\\` instead.`\n    );\n\n    if (\n      !link.Component ||\n      (link.Component &&\n        typeof link.Component === 'function' &&\n        // @ts-expect-error – shh\n        link.Component[Symbol.toStringTag] === 'AsyncFunction')\n    ) {\n      console.warn(\n        `\n      [${link.intlLabel.defaultMessage}]: [deprecated] addSettingsLink() was called with an async Component from the plugin \"${link.intlLabel.defaultMessage}\". This will be removed in the future. Please use: \\`Component: () => import(path)\\` ensuring you return a default export instead.\n      `.trim()\n      );\n    }\n\n    if (link.to.startsWith('/')) {\n      console.warn(\n        `[${link.intlLabel.defaultMessage}]: the \\`to\\` property of your settings link is an absolute path. It should be relative to \\`/settings\\`. This has been corrected for you but will be removed in a future version of Strapi.`\n      );\n\n      link.to = link.to.slice(1);\n    }\n\n    if (link.to.split('/')[0] === 'settings') {\n      console.warn(\n        `[${link.intlLabel.defaultMessage}]: the \\`to\\` property of your settings link has \\`settings\\` as the first part of it's path. It should be relative to \\`settings\\` and therefore, not include it. This has been corrected for you but will be removed in a future version of Strapi.`\n      );\n\n      link.to = link.to.split('/').slice(1).join('/');\n    }\n\n    const { Component, ...restLink } = link;\n\n    const settingsIndex = this._routes.findIndex((route) => route.path === 'settings/*');\n\n    /**\n     * This shouldn't happen unless someone has removed the settings section completely.\n     * Print a warning if this is the case though.\n     */\n    if (!settingsIndex) {\n      console.warn(\n        'A third party plugin has removed the settings section, the settings link cannot be added.'\n      );\n      return;\n    } else if (!this._routes[settingsIndex].children) {\n      this._routes[settingsIndex].children = [];\n    }\n\n    if (Component) {\n      this._routes[settingsIndex].children!.push({\n        path: `${link.to}/*`,\n        lazy: async () => {\n          const mod = await Component();\n\n          if ('default' in mod) {\n            return { Component: mod.default };\n          } else {\n            return { Component: mod };\n          }\n        },\n      });\n    }\n\n    this._settings[sectionId].links.push(restLink);\n  };\n\n  /**\n   * @alpha\n   * @description Adds a route or an array of routes to the router.\n   * Otherwise, pass a function that receives the current routes and\n   * returns the new routes in a reducer like fashion.\n   */\n  addRoute(route: RouteObject | RouteObject[] | Reducer<RouteObject>) {\n    if (Array.isArray(route)) {\n      this._routes = [...this._routes, ...route];\n    } else if (typeof route === 'object' && route !== null) {\n      this._routes.push(route);\n    } else if (typeof route === 'function') {\n      this._routes = route(this._routes);\n    } else {\n      throw new Error(\n        `Expected the \\`route\\` passed to \\`addRoute\\` to be an array or a function, but received ${getPrintableType(\n          route\n        )}`\n      );\n    }\n  }\n}\n\n/* -------------------------------------------------------------------------------------------------\n * getPrintableType\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * @internal\n * @description Gets the human-friendly printable type name for the given value, for instance it will yield\n * `array` instead of `object`, as the native `typeof` operator would do.\n */\nconst getPrintableType = (value: unknown): string => {\n  const nativeType = typeof value;\n\n  if (nativeType === 'object') {\n    if (value === null) return 'null';\n    if (Array.isArray(value)) return 'array';\n    if (value instanceof Object && value.constructor.name !== 'Object') {\n      return value.constructor.name;\n    }\n  }\n\n  return nativeType;\n};\n\nexport { Router };\nexport type { MenuItem, StrapiAppSettingLink, UnloadedSettingsLink, StrapiAppSetting, RouteObject };\n", "/* eslint-disable check-file/filename-naming-convention */\n\nimport invariant from 'invariant';\nimport { To } from 'react-router-dom';\n\nimport { Permission } from '../../../../shared/contracts/shared';\n\nimport type { Internal, Utils } from '@strapi/types';\nimport type { MessageDescriptor } from 'react-intl';\n\ntype WidgetUID = Utils.String.Suffix<\n  | Internal.Namespace.WithSeparator<Internal.Namespace.Plugin>\n  | Internal.Namespace.WithSeparator<Internal.Namespace.Global>,\n  string\n>;\n\ntype WidgetArgs = {\n  icon?: typeof import('@strapi/icons').PuzzlePiece;\n  title: MessageDescriptor;\n  link?: {\n    label: MessageDescriptor;\n    href: To;\n  };\n  component: () => Promise<React.ComponentType>;\n  pluginId?: string;\n  id: string;\n  permissions?: Permission[];\n};\n\ntype Widget = Omit<WidgetArgs, 'id' | 'pluginId'> & { uid: WidgetUID };\n\nclass Widgets {\n  widgets: Record<string, Widget>;\n\n  constructor() {\n    this.widgets = {};\n  }\n\n  register = (widget: WidgetArgs | WidgetArgs[]) => {\n    if (Array.isArray(widget)) {\n      widget.forEach((newWidget) => {\n        this.register(newWidget);\n      });\n    } else {\n      invariant(widget.id, 'An id must be provided');\n      invariant(widget.component, 'A component must be provided');\n      invariant(widget.title, 'A title must be provided');\n      invariant(widget.icon, 'An icon must be provided');\n\n      // Replace id and pluginId with computed uid\n      const { id, pluginId, ...widgetToStore } = widget;\n      const uid: WidgetUID = pluginId ? `plugin::${pluginId}.${id}` : `global::${id}`;\n\n      this.widgets[uid] = { ...widgetToStore, uid };\n    }\n  };\n\n  getAll = () => {\n    return Object.values(this.widgets);\n  };\n}\n\nexport { Widgets };\nexport type { WidgetArgs, Widget };\n", "import {\n  configureStore,\n  StoreEnhancer,\n  Middleware,\n  Reducer,\n  combineReducers,\n  MiddlewareAPI,\n  isRejected,\n} from '@reduxjs/toolkit';\n\nimport { reducer as appReducer, AppState, logout } from '../../reducer';\nimport { adminApi } from '../../services/api';\n\n/**\n * @description Static reducers are ones we know, they live in the admin package.\n */\nconst staticReducers = {\n  [adminApi.reducerPath]: adminApi.reducer,\n  admin_app: appReducer,\n} as const;\n\nconst injectReducerStoreEnhancer: (appReducers: Record<string, Reducer>) => StoreEnhancer =\n  (appReducers) =>\n  (next) =>\n  (...args) => {\n    const store = next(...args);\n\n    const asyncReducers: Record<string, Reducer> = {};\n\n    return {\n      ...store,\n      asyncReducers,\n      injectReducer: (key: string, asyncReducer: Reducer) => {\n        asyncReducers[key] = asyncReducer;\n        store.replaceReducer(\n          // @ts-expect-error we dynamically add reducers which makes the types uncomfortable.\n          combineReducers({\n            ...appReducers,\n            ...asyncReducers,\n          })\n        );\n      },\n    };\n  };\n\ntype PreloadState = Partial<{\n  admin_app: AppState;\n}>;\n\n/**\n * @description This is the main store configuration function, injected Reducers use our legacy app.addReducer API,\n * which we're trying to phase out. App Middlewares could potentially be improved...?\n */\nconst configureStoreImpl = (\n  preloadedState: PreloadState = {},\n  appMiddlewares: Array<() => Middleware> = [],\n  injectedReducers: Record<string, Reducer> = {}\n) => {\n  const coreReducers = { ...staticReducers, ...injectedReducers } as const;\n\n  const defaultMiddlewareOptions = {} as any;\n\n  // These are already disabled in 'production' env but we also need to disable it in test environments\n  // However, we want to leave them on for development so any issues can still be caught\n  if (process.env.NODE_ENV === 'test') {\n    defaultMiddlewareOptions.serializableCheck = false;\n    defaultMiddlewareOptions.immutableCheck = false;\n  }\n\n  const store = configureStore({\n    preloadedState: {\n      admin_app: preloadedState.admin_app,\n    },\n    reducer: coreReducers,\n    devTools: process.env.NODE_ENV !== 'production',\n    middleware: (getDefaultMiddleware) => [\n      ...getDefaultMiddleware(defaultMiddlewareOptions),\n      rtkQueryUnauthorizedMiddleware,\n      adminApi.middleware,\n      ...appMiddlewares.map((m) => m()),\n    ],\n    enhancers: [injectReducerStoreEnhancer(coreReducers)],\n  });\n\n  return store;\n};\n\nconst rtkQueryUnauthorizedMiddleware: Middleware =\n  ({ dispatch }: MiddlewareAPI) =>\n  (next) =>\n  (action) => {\n    // isRejectedWithValue Or isRejected\n    if (isRejected(action) && action.payload?.status === 401) {\n      dispatch(logout());\n      window.location.href = '/admin/auth/login';\n      return;\n    }\n\n    return next(action);\n  };\n\ntype Store = ReturnType<typeof configureStoreImpl> & {\n  asyncReducers: Record<string, Reducer>;\n  injectReducer: (key: string, asyncReducer: Reducer) => void;\n};\n\ntype RootState = ReturnType<Store['getState']>;\n\ntype Dispatch = Store['dispatch'];\n\nexport { configureStoreImpl as configureStore };\nexport type { RootState, Dispatch, AppState, Store, PreloadState };\n", "/* eslint-disable no-await-in-loop */\n/* eslint-disable no-restricted-syntax */\n\nimport type { Store } from '../store/configure';\n\nexport type Handler = (...args: any[]) => any;\n\nexport const createHook = () => {\n  const _handlers: Handler[] = [];\n\n  return {\n    register(fn: Handler) {\n      _handlers.push(fn);\n    },\n    delete(handler: Handler) {\n      _handlers.splice(_handlers.indexOf(handler), 1);\n    },\n    runWaterfall<T>(args: T, store?: Store) {\n      return _handlers.reduce((acc, fn) => fn(acc, store), args);\n    },\n    async runWaterfallAsync<T>(args: T, store?: Store) {\n      let result = args;\n\n      for (const fn of _handlers) {\n        result = await fn(result, store);\n      }\n\n      return result;\n    },\n    runSeries<T extends any[]>(...args: T) {\n      return _handlers.map((fn) => fn(...args));\n    },\n    async runSeriesAsync<T extends any[]>(...args: T) {\n      const result = [];\n\n      for (const fn of _handlers) {\n        result.push(await fn(...args));\n      }\n\n      return result;\n    },\n    runParallel<T extends any[]>(...args: T) {\n      return Promise.all(\n        _handlers.map((fn) => {\n          return fn(...args);\n        })\n      );\n    },\n  };\n};\n", "export const languageNativeNames = {\n  ar: 'العربية',\n  ca: 'Català',\n  cs: '<PERSON><PERSON><PERSON><PERSON>',\n  de: 'Deutsch',\n  dk: 'Dansk',\n  en: 'English',\n  'en-GB': 'English (United Kingdom)',\n  es: 'Español',\n  eu: 'Euskara',\n  uz: 'O`z<PERSON><PERSON>',\n  ro: 'Român<PERSON>',\n  fr: 'Français',\n  gu: 'Gujarati',\n  he: 'עברית',\n  hu: 'Magyar',\n  id: 'Indonesian',\n  it: 'Italiano',\n  ja: '日本語',\n  ko: '한국어',\n  ml: 'Malayalam',\n  ms: 'Melayu',\n  nl: 'Nederlands',\n  no: 'Norwegian',\n  pl: 'Polski',\n  'pt-BR': 'Português (Brasil)',\n  pt: 'Português (Portugal)',\n  ru: 'Русский',\n  sk: 'Slovenč<PERSON>',\n  sv: 'Swedish',\n  th: 'ไทย',\n  tr: 'Türkçe',\n  uk: 'Українська',\n  vi: 'Tiếng Vi<PERSON>',\n  'zh-<PERSON>': '中文 (简体)',\n  zh: '中文 (繁體)',\n  sa: 'संस्कृत',\n  hi: 'हिन्दी',\n} as const;\n", "import * as React from 'react';\n\nimport { darkTheme, lightTheme } from '@strapi/design-system';\nimport invariant from 'invariant';\nimport isFunction from 'lodash/isFunction';\nimport merge from 'lodash/merge';\nimport pick from 'lodash/pick';\nimport { RouterProvider } from 'react-router-dom';\nimport { DefaultTheme } from 'styled-components';\n\nimport { ADMIN_PERMISSIONS_EE } from '../../ee/admin/src/constants';\n\nimport Logo from './assets/images/logo-strapi-2022.svg';\nimport { ADMIN_PERMISSIONS_CE, HOOKS } from './constants';\nimport { CustomFields } from './core/apis/CustomFields';\nimport { Plugin, PluginConfig } from './core/apis/Plugin';\nimport { RBAC, RBACMiddleware } from './core/apis/rbac';\nimport { Router, StrapiAppSetting, UnloadedSettingsLink } from './core/apis/router';\nimport { Widgets } from './core/apis/Widgets';\nimport { RootState, Store, configureStore } from './core/store/configure';\nimport { getBasename } from './core/utils/basename';\nimport { Handler, createHook } from './core/utils/createHook';\nimport {\n  THEME_LOCAL_STORAGE_KEY,\n  LANGUAGE_LOCAL_STORAGE_KEY,\n  ThemeName,\n  getStoredToken,\n} from './reducer';\nimport { getInitialRoutes } from './router';\nimport { languageNativeNames } from './translations/languageNativeNames';\n\nimport type { ReducersMapObject, Middleware } from '@reduxjs/toolkit';\n\nconst {\n  INJECT_COLUMN_IN_TABLE,\n  MUTATE_COLLECTION_TYPES_LINKS,\n  MUTATE_EDIT_VIEW_LAYOUT,\n  MUTATE_SINGLE_TYPES_LINKS,\n} = HOOKS;\n\ninterface StrapiAppConstructorArgs extends Partial<Pick<StrapiApp, 'appPlugins'>> {\n  config?: {\n    auth?: { logo: string };\n    head?: { favicon: string };\n    locales?: string[];\n    menu?: { logo: string };\n    notifications?: { releases: boolean };\n    theme?: { light: DefaultTheme; dark: DefaultTheme };\n    translations?: Record<string, Record<string, string>>;\n    tutorials?: boolean;\n  };\n}\n\ntype Translation = { data: Record<string, string>; locale: string };\ntype Translations = Array<Translation>;\n\ninterface StrapiAppPlugin {\n  bootstrap?: (\n    args: Pick<StrapiApp, 'addSettingsLink' | 'addSettingsLinks' | 'getPlugin' | 'registerHook'>\n  ) => void;\n  register: (app: StrapiApp) => void;\n  registerTrads?: (args: { locales: string[] }) => Promise<Translations>;\n}\n\ninterface InjectionZoneComponent {\n  Component: React.ComponentType;\n  name: string;\n  // TODO: in theory this could receive and forward any React component prop\n  // but in practice there only seems to be once instance, where `slug` is\n  // forwarded. The type needs to become either more generic or we disallow\n  // prop spreading and offer a different way to access context data.\n  slug: string;\n}\n\ninterface Component {\n  name: string;\n  Component: React.ComponentType;\n}\n\ninterface Field {\n  type: string;\n  Component: React.ComponentType;\n}\n\ninterface Library {\n  fields: Record<Field['type'], Field['Component']>;\n  components: Record<Component['name'], Component['Component']>;\n}\n\nclass StrapiApp {\n  appPlugins: Record<string, StrapiAppPlugin>;\n  plugins: Record<string, Plugin> = {};\n  hooksDict: Record<string, ReturnType<typeof createHook>> = {};\n\n  admin = {\n    injectionZones: {},\n  };\n\n  translations: StrapiApp['configurations']['translations'] = {};\n\n  configurations = {\n    authLogo: Logo,\n    head: { favicon: '' },\n    locales: ['en'],\n    menuLogo: Logo,\n    notifications: { releases: true },\n    themes: { light: lightTheme, dark: darkTheme },\n    translations: {},\n    tutorials: true,\n  };\n\n  /**\n   * APIs\n   */\n  rbac = new RBAC();\n  router: Router;\n  library: Library = {\n    components: {},\n    fields: {},\n  };\n  middlewares: Array<() => Middleware<object, RootState>> = [];\n  reducers: ReducersMapObject = {};\n  store: Store | null = null;\n  customFields = new CustomFields();\n  widgets = new Widgets();\n\n  constructor({ config, appPlugins }: StrapiAppConstructorArgs = {}) {\n    this.appPlugins = appPlugins || {};\n\n    this.createCustomConfigurations(config ?? {});\n\n    this.createHook(INJECT_COLUMN_IN_TABLE);\n    this.createHook(MUTATE_COLLECTION_TYPES_LINKS);\n    this.createHook(MUTATE_SINGLE_TYPES_LINKS);\n    this.createHook(MUTATE_EDIT_VIEW_LAYOUT);\n\n    this.router = new Router(getInitialRoutes());\n  }\n\n  addComponents = (components: Component | Component[]) => {\n    if (Array.isArray(components)) {\n      components.map((comp) => {\n        invariant(comp.Component, 'A Component must be provided');\n        invariant(comp.name, 'A type must be provided');\n\n        this.library.components[comp.name] = comp.Component;\n      });\n    } else {\n      invariant(components.Component, 'A Component must be provided');\n      invariant(components.name, 'A type must be provided');\n\n      this.library.components[components.name] = components.Component;\n    }\n  };\n\n  addFields = (fields: Field | Field[]) => {\n    if (Array.isArray(fields)) {\n      fields.map((field) => {\n        invariant(field.Component, 'A Component must be provided');\n        invariant(field.type, 'A type must be provided');\n\n        this.library.fields[field.type] = field.Component;\n      });\n    } else {\n      invariant(fields.Component, 'A Component must be provided');\n      invariant(fields.type, 'A type must be provided');\n\n      this.library.fields[fields.type] = fields.Component;\n    }\n  };\n\n  addMiddlewares = (middlewares: StrapiApp['middlewares']) => {\n    middlewares.forEach((middleware) => {\n      this.middlewares.push(middleware);\n    });\n  };\n\n  addRBACMiddleware = (m: RBACMiddleware | RBACMiddleware[]) => {\n    if (Array.isArray(m)) {\n      this.rbac.use(m);\n    } else {\n      this.rbac.use(m);\n    }\n  };\n\n  addReducers = (reducers: ReducersMapObject) => {\n    /**\n     * TODO: when we upgrade to redux-toolkit@2 and we can also dynamically add middleware,\n     * we'll deprecate these two APIs in favour of their hook counterparts.\n     */\n    Object.entries(reducers).forEach(([name, reducer]) => {\n      this.reducers[name] = reducer;\n    });\n  };\n\n  addMenuLink = (link: Parameters<typeof this.router.addMenuLink>[0]) =>\n    this.router.addMenuLink(link);\n\n  /**\n   * @deprecated use `addSettingsLink` instead, it internally supports\n   * adding multiple links at once.\n   */\n  addSettingsLinks = (sectionId: string, links: UnloadedSettingsLink[]) => {\n    invariant(Array.isArray(links), 'TypeError expected links to be an array');\n\n    this.router.addSettingsLink(sectionId, links);\n  };\n\n  /**\n   * @deprecated use `addSettingsLink` instead, you can pass a section object to\n   * create the section and links at the same time.\n   */\n  createSettingSection = (\n    section: Pick<StrapiAppSetting, 'id' | 'intlLabel'>,\n    links: UnloadedSettingsLink[]\n  ) => this.router.addSettingsLink(section, links);\n\n  addSettingsLink = (\n    sectionId: string | Pick<StrapiAppSetting, 'id' | 'intlLabel'>,\n    link: UnloadedSettingsLink\n  ) => {\n    this.router.addSettingsLink(sectionId, link);\n  };\n\n  async bootstrap(customBootstrap?: unknown) {\n    Object.keys(this.appPlugins).forEach((plugin) => {\n      const bootstrap = this.appPlugins[plugin].bootstrap;\n\n      if (bootstrap) {\n        bootstrap({\n          addSettingsLink: this.addSettingsLink,\n          addSettingsLinks: this.addSettingsLinks,\n          getPlugin: this.getPlugin,\n          registerHook: this.registerHook,\n        });\n      }\n    });\n\n    if (isFunction(customBootstrap)) {\n      customBootstrap({\n        addComponents: this.addComponents,\n        addFields: this.addFields,\n        addMenuLink: this.addMenuLink,\n        addReducers: this.addReducers,\n        addSettingsLink: this.addSettingsLink,\n        addSettingsLinks: this.addSettingsLinks,\n        getPlugin: this.getPlugin,\n        registerHook: this.registerHook,\n      });\n    }\n  }\n\n  createCustomConfigurations = (customConfig: NonNullable<StrapiAppConstructorArgs['config']>) => {\n    if (customConfig.locales) {\n      this.configurations.locales = [\n        'en',\n        ...(customConfig.locales?.filter((loc) => loc !== 'en') || []),\n      ];\n    }\n\n    if (customConfig.auth?.logo) {\n      this.configurations.authLogo = customConfig.auth.logo;\n    }\n\n    if (customConfig.menu?.logo) {\n      this.configurations.menuLogo = customConfig.menu.logo;\n    }\n\n    if (customConfig.head?.favicon) {\n      this.configurations.head.favicon = customConfig.head.favicon;\n    }\n\n    if (customConfig.theme) {\n      const darkTheme = customConfig.theme.dark;\n      const lightTheme = customConfig.theme.light;\n\n      if (!darkTheme && !lightTheme) {\n        console.warn(\n          `[deprecated] In future versions, Strapi will stop supporting this theme customization syntax. The theme configuration accepts a light and a dark key to customize each theme separately. See https://docs.strapi.io/developer-docs/latest/development/admin-customization.html#theme-extension.`.trim()\n        );\n        merge(this.configurations.themes.light, customConfig.theme);\n      }\n\n      if (lightTheme) merge(this.configurations.themes.light, lightTheme);\n\n      if (darkTheme) merge(this.configurations.themes.dark, darkTheme);\n    }\n\n    if (customConfig.notifications?.releases !== undefined) {\n      this.configurations.notifications.releases = customConfig.notifications.releases;\n    }\n\n    if (customConfig.tutorials !== undefined) {\n      this.configurations.tutorials = customConfig.tutorials;\n    }\n  };\n\n  createHook = (name: string) => {\n    this.hooksDict[name] = createHook();\n  };\n\n  getAdminInjectedComponents = (\n    moduleName: string,\n    containerName: string,\n    blockName: string\n  ): InjectionZoneComponent[] => {\n    try {\n      // @ts-expect-error – we have a catch block so if you don't pass it correctly we still return an array.\n      return this.admin.injectionZones[moduleName][containerName][blockName] || [];\n    } catch (err) {\n      console.error('Cannot get injected component', err);\n\n      return [];\n    }\n  };\n\n  getPlugin = (pluginId: PluginConfig['id']) => this.plugins[pluginId];\n\n  async register(customRegister?: unknown) {\n    Object.keys(this.appPlugins).forEach((plugin) => {\n      this.appPlugins[plugin].register(this);\n    });\n\n    if (isFunction(customRegister)) {\n      customRegister(this);\n    }\n  }\n\n  async loadAdminTrads() {\n    const adminLocales = await Promise.all(\n      this.configurations.locales.map(async (locale) => {\n        try {\n          const { default: data } = await import(`./translations/${locale}.js`);\n\n          return { data, locale };\n        } catch {\n          try {\n            const { default: data } = await import(`./translations/${locale}.json`);\n            return { data, locale };\n          } catch {\n            return { data: null, locale };\n          }\n        }\n      })\n    );\n\n    return adminLocales.reduce<{ [locale: string]: Record<string, string> }>((acc, current) => {\n      if (current.data) {\n        acc[current.locale] = current.data;\n      }\n\n      return acc;\n    }, {});\n  }\n\n  /**\n   * Load the application's translations and merged the custom translations\n   * with the default ones.\n   */\n  async loadTrads(customTranslations: Record<string, Record<string, string>> = {}) {\n    const adminTranslations = await this.loadAdminTrads();\n\n    const arrayOfPromises = Object.keys(this.appPlugins)\n      .map((plugin) => {\n        const registerTrads = this.appPlugins[plugin].registerTrads;\n\n        if (registerTrads) {\n          return registerTrads({ locales: this.configurations.locales });\n        }\n\n        return null;\n      })\n      .filter((a) => a);\n\n    const pluginsTrads = (await Promise.all(arrayOfPromises)) as Array<Translation[]>;\n    const mergedTrads = pluginsTrads.reduce<{ [locale: string]: Translation['data'] }>(\n      (acc, currentPluginTrads) => {\n        const pluginTrads = currentPluginTrads.reduce<{ [locale: string]: Translation['data'] }>(\n          (acc1, current) => {\n            acc1[current.locale] = current.data;\n\n            return acc1;\n          },\n          {}\n        );\n\n        Object.keys(pluginTrads).forEach((locale) => {\n          acc[locale] = { ...acc[locale], ...pluginTrads[locale] };\n        });\n\n        return acc;\n      },\n      {}\n    );\n\n    const translations = this.configurations.locales.reduce<{\n      [locale: string]: Translation['data'];\n    }>((acc, current) => {\n      acc[current] = {\n        ...adminTranslations[current],\n        ...(mergedTrads[current] || {}),\n        ...(customTranslations[current] ?? {}),\n      };\n\n      return acc;\n    }, {});\n\n    this.configurations.translations = translations;\n\n    return Promise.resolve();\n  }\n\n  registerHook = (name: string, fn: Handler) => {\n    invariant(\n      this.hooksDict[name],\n      `The hook ${name} is not defined. You are trying to register a hook that does not exist in the application.`\n    );\n    this.hooksDict[name].register(fn);\n  };\n\n  registerPlugin = (pluginConf: PluginConfig) => {\n    const plugin = new Plugin(pluginConf);\n\n    this.plugins[plugin.pluginId] = plugin;\n  };\n\n  runHookSeries = (name: string, asynchronous = false) =>\n    asynchronous ? this.hooksDict[name].runSeriesAsync() : this.hooksDict[name].runSeries();\n\n  runHookWaterfall = <T,>(name: string, initialValue: T, store?: Store) => {\n    return this.hooksDict[name].runWaterfall(initialValue, store);\n  };\n\n  runHookParallel = (name: string) => this.hooksDict[name].runParallel();\n\n  render() {\n    const localeNames = pick(languageNativeNames, this.configurations.locales || []);\n    const locale = (localStorage.getItem(LANGUAGE_LOCAL_STORAGE_KEY) ||\n      'en') as keyof typeof localeNames;\n\n    this.store = configureStore(\n      {\n        admin_app: {\n          permissions: merge({}, ADMIN_PERMISSIONS_CE, ADMIN_PERMISSIONS_EE),\n          theme: {\n            availableThemes: [],\n            currentTheme: (localStorage.getItem(THEME_LOCAL_STORAGE_KEY) || 'system') as ThemeName,\n          },\n          language: {\n            locale: localeNames[locale] ? locale : 'en',\n            localeNames,\n          },\n          token: getStoredToken(),\n        },\n      },\n      this.middlewares,\n      this.reducers\n    ) as Store;\n\n    const router = this.router.createRouter(this, {\n      basename: getBasename(),\n    });\n\n    return <RouterProvider router={router} />;\n  }\n}\n\nexport { StrapiApp };\nexport type { StrapiAppPlugin, StrapiAppConstructorArgs, InjectionZoneComponent };\n", "/* eslint-disable no-undef */\nimport { createRoot } from 'react-dom/client';\n\nimport { StrapiApp, StrapiAppConstructorArgs } from './StrapiApp';\nimport { getFetchClient } from './utils/getFetchClient';\nimport { createAbsoluteUrl } from './utils/urls';\n\nimport type { Modules } from '@strapi/types';\n\ninterface RenderAdminArgs {\n  customisations: {\n    register?: (app: StrapiApp) => Promise<void> | void;\n    bootstrap?: (app: StrapiApp) => Promise<void> | void;\n    config?: StrapiAppConstructorArgs['config'];\n  };\n  plugins: StrapiAppConstructorArgs['appPlugins'];\n  features?: Modules.Features.FeaturesService['config'];\n}\n\nconst renderAdmin = async (\n  mountNode: HTMLElement | null,\n  { plugins, customisations, features }: RenderAdminArgs\n) => {\n  if (!mountNode) {\n    throw new Error('[@strapi/admin]: Could not find the root element to mount the admin app');\n  }\n\n  window.strapi = {\n    /**\n     * This ENV variable is passed from the strapi instance, by default no url is set\n     * in the config and therefore the instance returns you an empty string so URLs are relative.\n     *\n     * To ensure that the backendURL is always set, we use the window.location.origin as a fallback.\n     */\n    backendURL: createAbsoluteUrl(process.env.STRAPI_ADMIN_BACKEND_URL),\n    isEE: false,\n    isTrial: false,\n    telemetryDisabled: process.env.STRAPI_TELEMETRY_DISABLED === 'true',\n    future: {\n      isEnabled: (name: keyof NonNullable<Modules.Features.FeaturesConfig['future']>) => {\n        return features?.future?.[name] === true;\n      },\n    },\n    // @ts-expect-error – there's pollution from the global scope of Node.\n    features: {\n      SSO: 'sso',\n      AUDIT_LOGS: 'audit-logs',\n      REVIEW_WORKFLOWS: 'review-workflows',\n      /**\n       * If we don't get the license then we know it's not EE\n       * so no feature is enabled.\n       */\n      isEnabled: () => false,\n    },\n    projectType: 'Community',\n    flags: {\n      nps: false,\n      promoteEE: true,\n    },\n  };\n\n  const { get } = getFetchClient();\n\n  interface ProjectType extends Pick<Window['strapi'], 'flags'> {\n    isEE: boolean;\n    isTrial: boolean;\n    features: {\n      name: string;\n    }[];\n  }\n\n  try {\n    const {\n      data: {\n        data: { isEE, isTrial, features, flags },\n      },\n    } = await get<{ data: ProjectType }>('/admin/project-type');\n\n    window.strapi.isEE = isEE;\n    window.strapi.isTrialLicense = isTrial;\n    window.strapi.flags = flags;\n    window.strapi.features = {\n      ...window.strapi.features,\n      isEnabled: (featureName) => features.some((feature) => feature.name === featureName),\n    };\n    window.strapi.projectType = isEE ? 'Enterprise' : 'Community';\n  } catch (err) {\n    /**\n     * If this fails, we simply don't activate any EE features.\n     * Should we warn clearer in the UI?\n     */\n    console.error(err);\n  }\n\n  const app = new StrapiApp({\n    config: customisations?.config,\n    appPlugins: plugins,\n  });\n\n  await app.register(customisations?.register);\n  await app.bootstrap(customisations?.bootstrap);\n  await app.loadTrads(customisations?.config?.translations);\n\n  createRoot(mountNode).render(app.render());\n\n  if (\n    typeof module !== 'undefined' &&\n    module &&\n    'hot' in module &&\n    typeof module.hot === 'object' &&\n    module.hot !== null &&\n    'accept' in module.hot &&\n    typeof module.hot.accept === 'function'\n  ) {\n    module.hot.accept();\n  }\n\n  if (typeof import.meta.hot?.accept === 'function') {\n    import.meta.hot.accept();\n  }\n};\n\nexport { renderAdmin };\nexport type { RenderAdminArgs };\n", "import * as React from 'react';\n\nconst useIsMounted = () => {\n  const isMounted = React.useRef(false);\n\n  React.useLayoutEffect(() => {\n    isMounted.current = true;\n\n    return () => {\n      isMounted.current = false;\n    };\n  }, []);\n\n  return isMounted;\n};\n\nexport { useIsMounted };\n", "import * as React from 'react';\n\nimport { useIsMounted } from './useIsMounted';\n\n/**\n * @internal\n * @description Return a function that re-renders this component, if still mounted\n * @warning DO NOT USE EXCEPT SPECIAL CASES.\n */\nconst useForceUpdate = () => {\n  const [tick, update] = React.useState<number>();\n  const isMounted = useIsMounted();\n\n  const forceUpdate = React.useCallback(() => {\n    if (isMounted.current) {\n      update(Math.random());\n    }\n  }, [isMounted, update]);\n\n  return [tick, forceUpdate] as const;\n};\n\nexport { useForceUpdate };\n", "import { useMemo } from 'react';\n\nimport throttle from 'lodash/throttle';\n\ntype ThrottleSettings = Parameters<typeof throttle>[2];\n\n/**\n * @internal\n * @description Create a throttled version of a callback\n * @example\n * ```tsx\n * // First create a callback using <PERSON><PERSON>’s `useCallback` hook\n * const myCallback = useCallback(() => {\n *   // this is not throttled\n * }, [])\n *\n * // Then make a throttled version using the `useThrottledCallback` hook\n * const myThrottledCallback = useThrottledCallback(myCallback, 100)\n *\n * // Call the throttled callback\n * <Button onClick={myThrottledCallback} />\n * ```\n */\nconst useThrottledCallback = <T extends (...args: any[]) => any>(\n  callback: T,\n  wait: number,\n  options: ThrottleSettings\n): T => {\n  const throttledCallback = useMemo(\n    () => throttle(callback, wait, options) as unknown as T,\n    [callback, options, wait]\n  );\n\n  return throttledCallback;\n};\n\nexport { useThrottledCallback };\n", "/* -------------------------------------------------------------------------------------------------\n * requestIdleCallbackShim\n * -----------------------------------------------------------------------------------------------*/\nconst requestIdleCallbackShim: Window['requestIdleCallback'] = (callback) => {\n  const start = Date.now();\n\n  return setTimeout(() => {\n    callback({\n      didTimeout: false,\n      timeRemaining() {\n        return Math.max(0, Date.now() - start);\n      },\n    });\n  }, 1) as unknown as ReturnType<Window['requestIdleCallback']>;\n};\n\nconst _requestIdleCallback =\n  typeof requestIdleCallback === 'undefined' ? requestIdleCallbackShim : requestIdleCallback;\n\n/* -------------------------------------------------------------------------------------------------\n * cancelIdleCallbackShim\n * -----------------------------------------------------------------------------------------------*/\nconst cancelIdleCallbackShim: Window['cancelIdleCallback'] = (handle: unknown) => {\n  return clearTimeout(handle as any);\n};\n\nconst _cancelIdleCallback =\n  typeof cancelIdleCallback === 'undefined' ? cancelIdleCallbackShim : cancelIdleCallback;\n\nexport { _requestIdleCallback as requestIdleCallback };\nexport { _cancelIdleCallback as cancelIdleCallback };\n", "/**\n * This component will render DescriptionComponents that return objects e.g. `cm.apis.addEditViewPanel`\n * these descriptions are still treated like components because users can use react hooks in them.\n *\n * Rendering them normally by mapping etc. causes mutliple render issues.\n */\n\nimport * as React from 'react';\n\nimport isEqual from 'lodash/isEqual';\n\nimport { useForceUpdate } from '../hooks/useForceUpdate';\nimport { useThrottledCallback } from '../hooks/useThrottledCallback';\nimport { cancelIdleCallback, requestIdleCallback } from '../utils/shims';\n\ninterface DescriptionComponent<Props, Description> {\n  (props: Props): Description | null;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * DescriptionComponentRenderer\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DescriptionComponentRendererProps<Props = any, Description = any> {\n  children: (descriptions: Array<Description & { id: string }>) => React.ReactNode;\n  descriptions: DescriptionComponent<Props, Description>[];\n  props: Props;\n}\n\n/**\n * @internal\n *\n * @description This component takes an array of DescriptionComponents, which are react components that return objects as opposed to JSX.\n * We render these in their own isolated memoized component and use an update function to push the data back out to the parent.\n * Saving it in a ref, and then \"forcing\" an update of the parent component to render the children of this component with the new data.\n *\n * The DescriptionCompoonents can take props and use react hooks hence why we render them as if they were a component. The update\n * function is throttled and managed to avoid erroneous updates where we could wait a single tick to update the entire UI, which\n * creates less \"popping\" from functions being called in rapid succession.\n */\nconst DescriptionComponentRenderer = <Props, Description>({\n  children,\n  props,\n  descriptions,\n}: DescriptionComponentRendererProps<Props, Description>) => {\n  const statesRef = React.useRef<Record<string, { value: Description & { id: string } }>>({});\n  const [tick, forceUpdate] = useForceUpdate();\n\n  const requestHandle = React.useRef<number | null>(null);\n  const requestUpdate = React.useCallback(() => {\n    if (requestHandle.current) {\n      cancelIdleCallback(requestHandle.current);\n    }\n\n    requestHandle.current = requestIdleCallback(() => {\n      requestHandle.current = null;\n\n      forceUpdate();\n    });\n  }, [forceUpdate]);\n\n  /**\n   * This will avoid us calling too many react updates in a short space of time.\n   */\n  const throttledRequestUpdate = useThrottledCallback(requestUpdate, 60, { trailing: true });\n\n  const update = React.useCallback<DescriptionProps<Props, Description>['update']>(\n    (id, description) => {\n      if (description === null) {\n        delete statesRef.current[id];\n      } else {\n        const current = statesRef.current[id];\n        statesRef.current[id] = { ...current, value: { ...description, id } };\n      }\n\n      throttledRequestUpdate();\n    },\n    [throttledRequestUpdate]\n  );\n\n  const ids = React.useMemo(\n    () => descriptions.map((description) => getCompId(description)),\n    [descriptions]\n  );\n\n  const states = React.useMemo(\n    () =>\n      ids\n        .map((id) => statesRef.current[id]?.value)\n        .filter((state) => state !== null && state !== undefined),\n    /**\n     * we leave tick in the deps to ensure the memo is recalculated when the `update` function  is called.\n     * the `ids` will most likely be stable unless we get new actions, but we can't respond to the Description\n     * Component changing the ref data in any other way.\n     */\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [ids, tick]\n  );\n\n  return (\n    <>\n      {descriptions.map((description) => {\n        const key = getCompId(description);\n        return (\n          <Description key={key} id={key} description={description} props={props} update={update} />\n        );\n      })}\n      {children(states)}\n    </>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Description\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DescriptionProps<Props, Description> {\n  description: DescriptionComponent<Props, Description>;\n  id: string;\n  props: Props;\n  update: (id: string, value: Description | null) => void;\n}\n\n/**\n * Descriptions are objects, but to create the object, we allow users to create components,\n * this means they can use react hooks in them. It also means we need to render them\n * within a component, however because they return an object of data we can't add that\n * to the react tree, instead we push it back out to the parent.\n */\nconst Description = React.memo(\n  ({ description, id, props, update }: DescriptionProps<any, any>) => {\n    const comp = description(props);\n\n    useShallowCompareEffect(() => {\n      update(id, comp);\n\n      return () => {\n        update(id, null);\n      };\n    }, comp);\n\n    return null;\n  },\n  (prev, next) => isEqual(prev.props, next.props)\n);\n\n/* -------------------------------------------------------------------------------------------------\n * Helpers\n * -----------------------------------------------------------------------------------------------*/\n\nconst ids = new WeakMap<DescriptionComponent<any, any>, string>();\n\nlet counter = 0;\n\nfunction getCompId<T, K>(comp: DescriptionComponent<T, K>): string {\n  const cachedId = ids.get(comp);\n\n  if (cachedId) return cachedId;\n\n  const id = `${comp.name || (comp as any).displayName || '<anonymous>'}-${counter++}`;\n\n  ids.set(comp, id);\n\n  return id;\n}\n\nconst useShallowCompareMemoize = <T,>(value: T): Array<T | undefined> => {\n  const ref = React.useRef<T | undefined>(undefined);\n\n  if (!isEqual(value, ref.current)) {\n    ref.current = value;\n  }\n\n  return [ref.current];\n};\n\nconst useShallowCompareEffect = (callback: React.EffectCallback, dependencies?: unknown) => {\n  // eslint-disable-next-line react-hooks/exhaustive-deps -- the linter isn't able to see that deps are properly handled here\n  React.useEffect(callback, useShallowCompareMemoize(dependencies));\n};\n\nexport { DescriptionComponentRenderer };\nexport type { DescriptionComponentRendererProps, DescriptionComponent };\n", "import { useEffect } from 'react';\n\nimport { Reducer } from '@reduxjs/toolkit';\n\nimport { useTypedStore } from '../core/store/hooks';\n\n/**\n * @public\n * @description Inject a new reducer into the global redux-store.\n * @example\n * ```tsx\n * import { reducer } from './local-store';\n *\n * const MyPlugin = () => {\n *  useInjectReducer(\"plugin\", reducer);\n * }\n * ```\n */\nexport function useInjectReducer(namespace: string, reducer: Reducer) {\n  const store = useTypedStore();\n\n  useEffect(() => {\n    store.injectReducer(namespace, reducer);\n  }, [store, namespace, reducer]);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA,+CAAAA,SAAA;AAAA;AAoBA,QAAIC,aAAY,SAAS,WAAW,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAC5D,UAAI,MAAuC;AACzC,YAAI,WAAW,QAAW;AACxB,gBAAM,IAAI,MAAM,8CAA8C;AAAA,QAChE;AAAA,MACF;AAEA,UAAI,CAAC,WAAW;AACd,YAAI;AACJ,YAAI,WAAW,QAAW;AACxB,kBAAQ,IAAI;AAAA,YACV;AAAA,UAEF;AAAA,QACF,OAAO;AACL,cAAI,OAAO,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC5B,cAAI,WAAW;AACf,kBAAQ,IAAI;AAAA,YACV,OAAO,QAAQ,OAAO,WAAW;AAAE,qBAAO,KAAK,UAAU;AAAA,YAAG,CAAC;AAAA,UAC/D;AACA,gBAAM,OAAO;AAAA,QACf;AAEA,cAAM,cAAc;AACpB,cAAM;AAAA,MACR;AAAA,IACF;AAEA,IAAAD,QAAO,UAAUC;AAAA;AAAA;;;AChDjB;AAAA,yDAAAC,SAAA;AAAA,QAAI,YAAY;AAAhB,QACI,WAAW;AAgBf,aAAS,oBAAoB,UAAU,UAAU,KAAK,QAAQ,QAAQ,OAAO;AAC3E,UAAI,SAAS,QAAQ,KAAK,SAAS,QAAQ,GAAG;AAE5C,cAAM,IAAI,UAAU,QAAQ;AAC5B,kBAAU,UAAU,UAAU,QAAW,qBAAqB,KAAK;AACnE,cAAM,QAAQ,EAAE,QAAQ;AAAA,MAC1B;AACA,aAAO;AAAA,IACT;AAEA,IAAAA,QAAO,UAAU;AAAA;AAAA;;;AC3BjB;AAAA,8CAAAC,SAAA;AAAA,QAAI,YAAY;AAAhB,QACI,iBAAiB;AAiCrB,QAAI,YAAY,eAAe,SAAS,QAAQ,QAAQ,UAAU,YAAY;AAC5E,gBAAU,QAAQ,QAAQ,UAAU,UAAU;AAAA,IAChD,CAAC;AAED,IAAAA,QAAO,UAAU;AAAA;AAAA;;;ACtCjB;AAAA,iDAAAC,SAAA;AAAA,QAAI,QAAQ;AAAZ,QACI,WAAW;AADf,QAEI,sBAAsB;AAF1B,QAGI,YAAY;AAqBhB,QAAIC,gBAAe,SAAS,SAAS,MAAM;AACzC,WAAK,KAAK,QAAW,mBAAmB;AACxC,aAAO,MAAM,WAAW,QAAW,IAAI;AAAA,IACzC,CAAC;AAED,IAAAD,QAAO,UAAUC;AAAA;AAAA;;;AC7BjB;AAAA,wCAAAC,SAAA;AAAA,QAAI,OAAO;AAkBX,QAAI,MAAM,WAAW;AACnB,aAAO,KAAK,KAAK,IAAI;AAAA,IACvB;AAEA,IAAAA,QAAO,UAAU;AAAA;AAAA;;;ACtBjB;AAAA,6CAAAC,SAAA;AAAA,QAAI,WAAW;AAAf,QACI,MAAM;AADV,QAEI,WAAW;AAGf,QAAI,kBAAkB;AAGtB,QAAI,YAAY,KAAK;AAArB,QACI,YAAY,KAAK;AAwDrB,aAAS,SAAS,MAAM,MAAM,SAAS;AACrC,UAAI,UACA,UACA,SACA,QACA,SACA,cACA,iBAAiB,GACjB,UAAU,OACV,SAAS,OACT,WAAW;AAEf,UAAI,OAAO,QAAQ,YAAY;AAC7B,cAAM,IAAI,UAAU,eAAe;AAAA,MACrC;AACA,aAAO,SAAS,IAAI,KAAK;AACzB,UAAI,SAAS,OAAO,GAAG;AACrB,kBAAU,CAAC,CAAC,QAAQ;AACpB,iBAAS,aAAa;AACtB,kBAAU,SAAS,UAAU,SAAS,QAAQ,OAAO,KAAK,GAAG,IAAI,IAAI;AACrE,mBAAW,cAAc,UAAU,CAAC,CAAC,QAAQ,WAAW;AAAA,MAC1D;AAEA,eAAS,WAAW,MAAM;AACxB,YAAI,OAAO,UACP,UAAU;AAEd,mBAAW,WAAW;AACtB,yBAAiB;AACjB,iBAAS,KAAK,MAAM,SAAS,IAAI;AACjC,eAAO;AAAA,MACT;AAEA,eAAS,YAAY,MAAM;AAEzB,yBAAiB;AAEjB,kBAAU,WAAW,cAAc,IAAI;AAEvC,eAAO,UAAU,WAAW,IAAI,IAAI;AAAA,MACtC;AAEA,eAAS,cAAc,MAAM;AAC3B,YAAI,oBAAoB,OAAO,cAC3B,sBAAsB,OAAO,gBAC7B,cAAc,OAAO;AAEzB,eAAO,SACH,UAAU,aAAa,UAAU,mBAAmB,IACpD;AAAA,MACN;AAEA,eAAS,aAAa,MAAM;AAC1B,YAAI,oBAAoB,OAAO,cAC3B,sBAAsB,OAAO;AAKjC,eAAQ,iBAAiB,UAAc,qBAAqB,QACzD,oBAAoB,KAAO,UAAU,uBAAuB;AAAA,MACjE;AAEA,eAAS,eAAe;AACtB,YAAI,OAAO,IAAI;AACf,YAAI,aAAa,IAAI,GAAG;AACtB,iBAAO,aAAa,IAAI;AAAA,QAC1B;AAEA,kBAAU,WAAW,cAAc,cAAc,IAAI,CAAC;AAAA,MACxD;AAEA,eAAS,aAAa,MAAM;AAC1B,kBAAU;AAIV,YAAI,YAAY,UAAU;AACxB,iBAAO,WAAW,IAAI;AAAA,QACxB;AACA,mBAAW,WAAW;AACtB,eAAO;AAAA,MACT;AAEA,eAAS,SAAS;AAChB,YAAI,YAAY,QAAW;AACzB,uBAAa,OAAO;AAAA,QACtB;AACA,yBAAiB;AACjB,mBAAW,eAAe,WAAW,UAAU;AAAA,MACjD;AAEA,eAAS,QAAQ;AACf,eAAO,YAAY,SAAY,SAAS,aAAa,IAAI,CAAC;AAAA,MAC5D;AAEA,eAAS,YAAY;AACnB,YAAI,OAAO,IAAI,GACX,aAAa,aAAa,IAAI;AAElC,mBAAW;AACX,mBAAW;AACX,uBAAe;AAEf,YAAI,YAAY;AACd,cAAI,YAAY,QAAW;AACzB,mBAAO,YAAY,YAAY;AAAA,UACjC;AACA,cAAI,QAAQ;AAEV,yBAAa,OAAO;AACpB,sBAAU,WAAW,cAAc,IAAI;AACvC,mBAAO,WAAW,YAAY;AAAA,UAChC;AAAA,QACF;AACA,YAAI,YAAY,QAAW;AACzB,oBAAU,WAAW,cAAc,IAAI;AAAA,QACzC;AACA,eAAO;AAAA,MACT;AACA,gBAAU,SAAS;AACnB,gBAAU,QAAQ;AAClB,aAAO;AAAA,IACT;AAEA,IAAAA,QAAO,UAAU;AAAA;AAAA;;;AC9LjB;AAAA,6CAAAC,SAAA;AAAA,QAAI,WAAW;AAAf,QACI,WAAW;AAGf,QAAI,kBAAkB;AA8CtB,aAASC,UAAS,MAAM,MAAM,SAAS;AACrC,UAAI,UAAU,MACV,WAAW;AAEf,UAAI,OAAO,QAAQ,YAAY;AAC7B,cAAM,IAAI,UAAU,eAAe;AAAA,MACrC;AACA,UAAI,SAAS,OAAO,GAAG;AACrB,kBAAU,aAAa,UAAU,CAAC,CAAC,QAAQ,UAAU;AACrD,mBAAW,cAAc,UAAU,CAAC,CAAC,QAAQ,WAAW;AAAA,MAC1D;AACA,aAAO,SAAS,MAAM,MAAM;AAAA,QAC1B,WAAW;AAAA,QACX,WAAW;AAAA,QACX,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AAEA,IAAAD,QAAO,UAAUC;AAAA;AAAA;;;;;;;;;;;;;;;ACiBjB,IAAMC,gBAAgB;EACpB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACD;AAED,IAAMC,6BAA6B;EACjC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACD;AAED,IAAMC,eAAN,MAAMA;EAGJC,cAAc;AAIdC,SAAAA,WAAW,CAACC,iBAAAA;AACV,UAAIC,MAAMC,QAAQF,YAAe,GAAA;AAE/BA,qBAAaG,QAAQ,CAACC,gBAAAA;AACpB,eAAKL,SAASK,WAAAA;QAChB,CAAA;aACK;AAEL,cAAM,EAAEC,MAAMC,UAAUC,MAAMC,WAAWC,iBAAiBC,YAAYC,QAAO,IAC3EX;AAGFY,6BAAAA,SAAUP,MAAM,yBAAA;AAChBO,6BAAAA,SAAUL,MAAM,yBAAA;AAChBK,6BAAAA,SAAUJ,WAAW,+BAAA;AACrBI,6BAAAA,SAAUH,iBAAiB,qCAAA;AAC3BG,6BAAAA,SAAUF,YAAY,sCAAA;AACtBE,6BAAAA,SAAUF,WAAWG,OAAO,qCAAA;AAG5BD,6BAAAA,SACEjB,cAAcmB,SAASP,IAAAA,GACvB,uBAAuBA,IAAK,sEAAqE;AAInG,cAAMQ,mBAAmB;AACzBH,6BAAAA,SACEG,iBAAiBC,KAAKX,IAAAA,GACtB,uBAAuBA,IAAK,6BAA4B;AAI1D,cAAMY,iBAAiB;UAAKN,IAAAA,mCAASO,SAAQ,CAAA;UAASP,IAAAA,mCAASQ,aAAY,CAAA;QAAI;AAE/E,YAAIF,eAAeG,QAAQ;AACzB,gBAAMC,wBAAwBJ,eAAeK,OAAOC,0BAA0B,CAAA,CAAE;AAEhFF,gCAAsBlB,QAAQ,CAAC,EAAEqB,mBAAmBC,aAAY,MAAE;AAChEb,iCAAAA,SAAUY,mBAAmBC,YAAAA;UAC/B,CAAA;QACF;AAGA,cAAMC,MAAsBpB,WAAW,WAAWA,QAAS,IAAGD,IAAAA,KAAS,WAAWA,IAAAA;AAGlF,cAAMsB,iBAAiBC,OAAOC,UAAUC,eAAeC,KAAK,KAAK/B,cAAc0B,GAAAA;AAC/Ed,6BAAAA,SAAU,CAACe,gBAAgB,kBAAkBD,GAAAA,+BAAkC;AAE/E,aAAK1B,aAAa0B,GAAAA,IAAO1B;MAC3B;IACF;SAEAgC,SAAS,MAAA;AACP,aAAO,KAAKhC;IACd;AAEAiC,SAAAA,MAAM,CAACP,QAAAA;AACL,aAAO,KAAK1B,aAAa0B,GAAI;IAC/B;AA/DE,SAAK1B,eAAe,CAAA;EACtB;AA+DF;AAOA,IAAMuB,2BAA2B,CAC/BW,KACAC,WAAAA;AAEA,MAAI,WAAWA,QAAQ;AACrB,WAAOA,OAAOC,MAAMd,OAAOC,0BAA0BW,GAAAA;EACvD;AAEA,MAAI,CAACC,OAAO9B,MAAM;AAChB6B,QAAIG,KAAK;MACPb,mBAAmB;MACnBC,cAAc;IAChB,CAAA;SACK;AACLS,QAAIG,KAAK;MACPb,mBACEW,OAAO9B,KAAKiC,WAAW,SAAA,KAAc1C,2BAA2BkB,SAASqB,OAAO9B,IAAI;MACtFoB,cAAc,IAAIU,OAAO9B,IAAI;IAC/B,CAAA;EACF;AAEA,SAAO6B;AACT;;;;AC3MO,IAAMK,SAAN,MAAMA;EAsBXC,sBAAsBC,eAAuBC,WAAmB;AAC9D,QAAI;AACF,aAAO,KAAKC,eAAeF,aAAAA,EAAeC,SAAAA,KAAc,CAAA;IAC1D,SAASE,KAAK;AACZC,cAAQC,MAAM,iCAAiCF,GAAAA;AAE/C,aAAO,CAAA;IACT;EACF;EAEAG,gBACEN,eACAC,WACAM,WACA;AACA,QAAI;AACF,WAAKL,eAAeF,aAAAA,EAAeC,SAAU,EAACO,KAAKD,SAAAA;IACrD,SAASJ,KAAK;AACZC,cAAQC,MAAM,2BAA2BF,GAAAA;IAC3C;EACF;EA7BAM,YAAYC,YAA0B;AAZtC,SAACC,CAAAA,IAAa;AAaZ,SAAKC,OAAOF,WAAWE,QAAQ,CAAA;AAC/B,SAAKC,cAAcH,WAAWG,eAAe;AAC7C,SAAKX,iBAAiBQ,WAAWR,kBAAkB,CAAA;AACnD,SAAKY,UAAUJ,WAAWI,YAAYC,SAAYL,WAAWI,UAAU;AACvE,SAAKE,OAAON,WAAWM;AACvB,SAAKC,WAAWP,WAAWQ;EAC7B;AAuBF;;;AC/BA,IAAMC,OAAN,MAAMA;EAOJC,IAAIC,YAAqD;AACvD,QAAIC,MAAMC,QAAQF,UAAa,GAAA;AAC7B,WAAKG,YAAYC,KAAQJ,GAAAA,UAAAA;WACpB;AACL,WAAKG,YAAYC,KAAKJ,UAAAA;IACxB;EACF;EAVAK,cAAc;AAFNF,SAAAA,cAAgC,CAAA;AAcxCG,SAAAA,MAAM,OAAOC,KAAkBC,gBAAAA;AAC7B,UAAIC,QAAQ;AAEZ,YAAMC,mBAAmB,KAAKP,YAAYQ,IAAI,CAACX,eAAeA,WAAWO,GAAAA,CAAAA;AAEzE,YAAMK,OAAO,OAAOJ,iBAAAA;AAClB,YAAIC,QAAQ,KAAKN,YAAYU,QAAQ;AACnC,iBAAOH,iBAAiBD,OAAQ,EAACG,IAAMJ,EAAAA,YAAAA;QACzC;AAEA,eAAOA;MACT;AAEA,aAAOI,KAAKJ,WAAAA;IACd;EA1Be;AA2BjB;;;;;;;;;;;;;;;;;;;ACrCA,IAAMM,mBAAmB,CAAC,EAAEC,UAAUC,SAAQ,MAAyB;AACrE,QAAMC,SAASC,iBAAiB,CAACC,UAAUA,MAAMC,UAAUC,SAASJ,MAAM;AAC1E,QAAMK,kBAAcC,oBAAAA,SAAaP,SAASC,MAAO,GAAED,SAASQ,EAAE;AAE9D,aACEC,wBAACC,kBAAAA;IAAaT;IAAgBU,eAAc;IAAKX,UAAUM;IAAaM,eAAc;IACnFb;;AAGP;;;;;ACPA,IAAMc,QAAQ,CAAC,EAAEC,UAAUC,OAAM,MAAc;AAC7C,QAAM,EAAEC,aAAY,IAAKC,iBAAiB,CAACC,UAAUA,MAAMC,UAAUC,KAAK;AAC1E,QAAM,CAACC,aAAaC,cAAe,IAASC,eAAQ;AACpD,QAAM,EAAEC,OAAM,IAAKC,QAAAA;AACnB,QAAMC,WAAWC,YAAAA;AAGjBC,EAAMC,gBAAU,MAAA;AACd,UAAMC,eAAeC,OAAOC,WAAW,8BAAA;AACvCV,mBAAeQ,aAAaG,UAAU,SAAS,OAAA;AAE/C,UAAMC,WAAW,CAACC,UAAAA;AAChBb,qBAAea,MAAMF,UAAU,SAAS,OAAA;IAC1C;AACAH,iBAAaM,iBAAiB,UAAUF,QAAAA;AAGxC,WAAO,MAAA;AACLJ,mBAAaO,oBAAoB,UAAUH,QAAAA;IAC7C;EACF,GAAG,CAAA,CAAE;AAELN,EAAMC,gBAAU,MAAA;AACdH,aAASY,mBAAmBC,OAAOC,KAAKzB,MAAAA,CAAAA,CAAAA;KACvC;IAACW;IAAUX;EAAO,CAAA;AAErB,QAAM0B,oBAAoBzB,iBAAiB,WAAWK,cAAcL;AAEpE,aACE0B,0BAACC,sBAAAA;IACCnB;;;;;;IAMAJ,OAAOL,iCAAS0B,qBAAqB;;MAEpC3B;UACD8B,yBAACC,aAAAA,CAAAA,CAAAA;;;AAGP;AAEA,IAAMA,cAAcC;;kBAEF,CAAC,EAAE1B,MAAK,MAAOA,MAAM2B,OAAOC,UAAU;;;;;AC7CxD,IAAMC,cAAc,IAAIC,YAAY;EAClCC,gBAAgB;IACdC,SAAS;MACPC,sBAAsB;IACxB;EACF;AACF,CAAA;AAQMC,IAAAA,YAAY,CAAC,EAAEC,UAAUC,QAAQC,MAAK,MAAkB;AAC5D,aACEC,yBAACC,mBAAAA;IACCC,YAAYJ,OAAOK,QAAQD;IAC3BE,cAAcN,OAAOM;IACrBC,SAASP,OAAOO;IAChBC,QAAQR,OAAOK,QAAQG;IACvBC,MAAMT,OAAOU,OAAOD;IACpBE,4BAA4BX,OAAOW;IACnCC,WAAWZ,OAAOY;IAClBC,SAASb,OAAOa;IAChBC,MAAMd,OAAOc;IACbC,iBAAiBf,OAAOe;IACxBC,kBAAkB,CAACC,MAAMC,iBAAiBlB,OAAOgB,iBAAiBC,MAAMC,cAAcjB,KAAAA;IACtFkB,eAAenB,OAAOmB;IACtBC,UAAUpB,OAAOU,OAAOU;IAExB,cAAAlB,yBAACmB,kBAAAA;MAASpB;MACR,cAAAC,yBAACoB,qBAAAA;QAAoBC,QAAQ9B;QAC3B,cAAAS,yBAACsB,cAAAA;UACC,cAAAtB,yBAACuB,iBAAAA;YACC,cAAAvB,yBAACwB,kBAAAA;cAAiBC,UAAU3B,OAAO4B,eAAeC;cAChD,cAAA3B,yBAAC4B,OAAAA;gBAAMC,QAAQ/B,OAAO4B,eAAeG;gBACnC,cAAA7B,yBAAC8B,uBAAAA;kBACC,cAAA9B,yBAAC+B,kBAAAA;oBACC,cAAA/B,yBAACgC,oBAAAA;sBACC,cAAAhC,yBAACiC,uBAAAA;wBACCC,iBAAiBpC,OAAO4B,eAAeS;wBACvCC,iBAAiBtC,OAAO4B,eAAeW;wBACvCC,yBAAyBxC,OAAO4B,eAAea,cAAcC;wBAE5D3C;;;;;;;;;;;;AAa3B;;;ACxDA,IAAM4C,MAAM,CAAC,EAAEC,QAAQC,MAAK,MAAY;AACtCC,+BAAU,MAAA;AACR,UAAMC,WAAWC,aAAaC,QAAQC,0BAA+B,KAAA;AAErE,QAAIH,UAAU;AACZI,eAASC,gBAAgBC,OAAON;IAClC;EACF,GAAG,CAAA,CAAE;AAEL,aACEO,yBAACC,WAAAA;IAAUX;IAAgBC;IACzB,cAAAS,yBAACE,wBAAAA;MAASC,cAAUH,yBAACI,KAAKC,SAAO,CAAA,CAAA;MAC/B,cAAAL,yBAACM,QAAAA,CAAAA,CAAAA;;;AAIT;;;;AClBC,IACKC,eAAe,MAAA;AACnB,QAAMC,QAAQC,cAAAA;AACd,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,KAAI,IAAKC,aAAAA;AAEjB,MAAIL,iBAAiBM,OAAO;AAC1BC,YAAQP,MAAMA,KAAAA;AAEd,UAAMQ,cAAc,YAAA;AAClB,YAAMJ,KAAK;;EAEfJ,MAAMS,KAAK;;OAEN;IACH;AAEA,eACEC,yBAACC,MAAAA;MAAKC,QAAO;MACX,cAAAF,yBAACG,MAAAA;QAAKC,YAAW;QAASF,QAAO;QAAOG,gBAAe;QACrD,cAAAC,0BAACH,MAAAA;UACCI,KAAK;UACLC,SAAS;UACTC,WAAU;UACVC,OAAM;UACNC,QAAO;UACPC,aAAY;UACZC,YAAW;UACXC,WAAS;UACTC,UAAS;;gBAETT,0BAACH,MAAAA;cAAKM,WAAU;cAASF,KAAK;;oBAC5BP,yBAACgB,cAAAA;kBAAcN,OAAM;kBAAOR,QAAO;kBAAOe,MAAK;;oBAC/CjB,yBAACkB,YAAAA;kBAAWC,UAAU;kBAAGC,YAAW;kBAAOC,WAAU;4BAClD7B,cAAc;oBACb8B,IAAI;oBACJC,gBAAgB;kBAClB,CAAA;;oBAEFvB,yBAACkB,YAAAA;kBAAWM,SAAQ;kBAAQH,WAAU;4BACnC7B,cACC;oBACE8B,IAAI;oBACJC,gBAAgB;qBAElB;oBACEE,UACEzB,yBAAC0B,OAAAA;sBACCC,YAAU;;sBAEVC,SAAO;sBACPC,MAAK;sBACL,UAAA;;kBAEN,CAAA;;;;gBAKNvB,0BAACH,MAAAA;cAAKI,KAAK;cAAGE,WAAU;cAASC,OAAM;;oBACrCV,yBAAC8B,aAAAA;kBAAYC,SAAS,MAAO;kBAAA;kBAAGrB,OAAM;kBAAOsB,YAAW;kBAAGR,SAAQ;kBACjE,cAAAxB,yBAACiC,WAAAA;oBAAW3C,UAAAA,MAAM4C;;;oBAEpBlC,yBAACmC,QAAAA;kBAAOC,SAAStC;kBAAa0B,SAAQ;kBAAWa,eAAWrC,yBAACsC,eAAAA,CAAAA,CAAAA;4BAC1D9C,cAAc;oBACb8B,IAAI;oBACJC,gBAAgB;kBAClB,CAAA;;;;;;;;EAOd;AAEA,QAAMjC;AACR;AAEA,IAAMwC,cAAcS,GAAOC,KAAAA;;;;;;;;;AAU3B,IAAMP,YAAYM,GAA4BrB,UAAAA;;WAEnC,CAAC,EAAEuB,MAAK,MAAOA,MAAMC,OAAOC,SAAS;;;;;IC/FnCC,eAAe,MAAA;AAC1B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEC,0BAACC,KAAKC,MAAI;IAACC,YAAW;;UACpBC,yBAACC,QAAQC,QAAM;QACbC,IAAG;QACHC,OAAOV,cAAc;UACnBS,IAAI;UACJE,gBAAgB;QAClB,CAAA;;UAEFL,yBAACC,QAAQK,SAAO;QACd,cAAAN,yBAACO,kBAAAA;UACCC,YACER,yBAACS,YAAAA;YAAWC,KAAKC;YAAMC,SAAQ;YAAYC,aAASb,yBAACc,eAAAA,CAAAA,CAAAA;YAAeC,IAAG;sBACpErB,cAAc;cACbS,IAAI;cACJE,gBAAgB;YAClB,CAAA;;UAGJW,SAAStB,cAAc;YACrBS,IAAI;YACJE,gBAAgB;UAClB,CAAA;UACAY,WAAS;UACTC,UAAMlB,yBAACmB,cAAAA;YAAcC,OAAM;;UAC3BC,QAAO;;;;;AAKjB;;;;;;AC1CaC,IAAAA,eAAc,MAAqB;EAC1CC,GAAAA,OAAOC,OAAOC,SAASC,UAAUH,OAAOC,OAAOC,SAASE,UAAU,IAClE;IACE;MACEC,MAAM;MACNC,MAAM,YAAA;AACJ,cAAM,EAAEC,kBAAiB,IAAK,MAAM,OAAO,wBAAA;AAE3C,eAAO;UACLC,WAAWD;QACb;MACF;IACF;EACD,IACD,CAAA;EACAP,GAAAA,OAAOC,OAAOC,SAASC,UAAUH,OAAOC,OAAOC,SAASO,GAAG,IAC3D;IACE;MACEJ,MAAM;MACNC,MAAM,YAAA;AACJ,cAAM,EAAEI,aAAY,IAAK,MAAM,OAAO,gCAAA;AAEtC,eAAO;UACLF,WAAWE;QACb;MACF;IACF;EACD,IACD,CAAA;;;;;;;;ACdN,IAAMC,iBAAiB,MAAA;AACrB,QAAMC,WAAWC,YAAAA;AACjB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,yBAAyBC,eAAc,IAAKC,mBAAAA;AAEpD,QAAM,CAACC,gBAAgB,EAAEC,MAAK,CAAE,IAAIC,0BAAAA;AAEpC,aACEC,yBAACC,uBAAAA;IACC,cAAAC,0BAACC,MAAAA;;YACCD,0BAACE,eAAAA;;gBACCF,0BAACG,QAAAA;;oBACCL,yBAACM,MAAAA,CAAAA,CAAAA;oBACDN,yBAACO,KAAAA;kBAAIC,YAAY;kBAAGC,eAAe;kBACjC,cAAAT,yBAACU,YAAAA;oBAAWC,KAAI;oBAAKC,SAAQ;8BAC1BpB,cAAc;sBACbqB,IAAI;sBACJC,gBAAgB;oBAClB,CAAA;;;gBAGHhB,YACCE,yBAACU,YAAAA;kBAAWG,IAAG;kBAAoBE,MAAK;kBAAQC,UAAU;kBAAIC,WAAU;4BACrEC,iBAAiBpB,KAAAA,IACdH,eAAeG,KAAAA,IACfN,cAAc;oBACZqB,IAAI;oBACJC,gBAAgB;kBAClB,CAAA;gBAEJ,CAAA,IAAA;;;gBAENd,yBAACmB,MAAAA;cACCC,QAAO;cACPC,eAAe;gBACbC,OAAO;cACT;cACAC,UAAU,OAAOC,SAAAA;AACf,sBAAMC,MAAM,MAAM5B,eAAe2B,IAAAA;AAEjC,oBAAI,EAAE,WAAWC,MAAM;AACrBnC,2BAAS,+BAAA;gBACX;cACF;cACAoC,kBAAsBC,QAAM,EAAGC,MAAM;gBACnCN,OACGO,QAAM,EACNP,MAAMQ,YAAiBR,KAAK,EAC5BS,SAAS;kBACRlB,IAAIiB,YAAiBC,SAASlB;kBAC9BC,gBAAgB;gBAClB,CAAA,EACCkB,SAAQ;cACb,CAAA;cAEA,cAAA9B,0BAAC+B,MAAAA;gBAAKC,WAAU;gBAASC,YAAW;gBAAUC,KAAK;;kBAChD;oBACC;sBACEC,OAAO7C,cAAc;wBAAEqB,IAAI;wBAAyBC,gBAAgB;sBAAQ,CAAA;sBAC5EwB,MAAM;sBACNC,aAAa/C,cAAc;wBACzBqB,IAAI;wBACJC,gBAAgB;sBAClB,CAAA;sBACAiB,UAAU;sBACVS,MAAM;oBACR;kBACD,EAACC,IAAI,CAACC,cACL1C,yBAAC2C,uBAAAA;oBAAgC,GAAGD;kBAAhBA,GAAAA,MAAMJ,IAAI,CAAA;sBAEhCtC,yBAAC4C,QAAAA;oBAAOJ,MAAK;oBAASK,WAAS;8BAC5BrD,cAAc;sBACbqB,IAAI;sBACJC,gBAAgB;oBAClB,CAAA;;;;;;;YAKRd,yBAACiC,MAAAA;UAAKa,gBAAe;UACnB,cAAA9C,yBAACO,KAAAA;YAAIC,YAAY;YACf,cAAAR,yBAAC+C,OAAAA;cAAKpC,KAAKqC;cAASC,IAAG;wBACpBzD,cAAc;gBAAEqB,IAAI;gBAAmBC,gBAAgB;cAAoB,CAAA;;;;;;;AAO1F;;;;AClGA,IAAMoC,wBAAwB,MAAA;AAC5B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEC,yBAACC,uBAAAA;IACC,cAAAC,0BAACC,MAAAA;;YACCH,yBAACI,eAAAA;UACC,cAAAF,0BAACG,QAAAA;;kBACCL,yBAACM,MAAAA,CAAAA,CAAAA;kBACDN,yBAACO,KAAAA;gBAAIC,YAAY;gBAAGC,eAAe;gBACjC,cAAAT,yBAACU,YAAAA;kBAAWC,KAAI;kBAAKC,SAAQ;4BAC1Bd,cAAc;oBACbe,IAAI;oBACJC,gBAAgB;kBAClB,CAAA;;;kBAGJd,yBAACU,YAAAA;0BACEZ,cAAc;kBACbe,IAAI;kBACJC,gBAAgB;gBAClB,CAAA;;kBAEFd,yBAACO,KAAAA;gBAAIC,YAAY;gBACf,cAAAR,yBAACU,YAAAA;4BACEZ,cAAc;oBACbe,IAAI;oBACJC,gBACE;kBACJ,CAAA;;;;;;YAKRd,yBAACe,MAAAA;UAAKC,gBAAe;UACnB,cAAAhB,yBAACO,KAAAA;YAAIC,YAAY;YACf,cAAAR,yBAACiB,OAAAA;cAAKN,KAAKO;cAASC,IAAG;wBACpBrB,cAAc;gBAAEe,IAAI;gBAAoBC,gBAAgB;cAAU,CAAA;;;;;;;AAOjF;;;;;AC1CA,IAAMM,OAAO,MAAA;AACX,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,QAAQC,aAAY,IAAKC,YAAAA;AACjC,QAAMC,QAAcC,eAAQ,MAAM,IAAIC,gBAAgBJ,YAAe,GAAA;IAACA;EAAa,CAAA;AAEnF,QAAMK,UACJH,MAAMI,IAAI,MAAA,KACVT,cAAc;IACZU,IAAI;IACJC,gBAAgB;EAClB,CAAA;AAEF,aACEC,yBAACC,uBAAAA;IACC,cAAAC,0BAACC,MAAAA;;YACCH,yBAACI,eAAAA;UACC,cAAAF,0BAACG,QAAAA;;kBACCL,yBAACM,MAAAA,CAAAA,CAAAA;kBACDN,yBAACO,KAAAA;gBAAIC,YAAY;gBAAGC,eAAe;gBACjC,cAAAT,yBAACU,YAAAA;kBAAWC,KAAI;kBAAKC,SAAQ;4BAC1BxB,cAAc;oBAAEU,IAAI;oBAA8BC,gBAAgB;kBAAU,CAAA;;;kBAGjFC,yBAACU,YAAAA;gBAAYd,UAAAA;;kBACbI,yBAACO,KAAAA;gBAAIC,YAAY;gBACf,cAAAR,yBAACU,YAAAA;4BACEtB,cAAc;oBACbU,IAAI;oBACJC,gBAAgB;kBAClB,CAAA;;;;;;YAKRC,yBAACa,MAAAA;UAAKC,gBAAe;UACnB,cAAAd,yBAACO,KAAAA;YAAIC,YAAY;YACf,cAAAR,yBAACe,OAAAA;cAAKJ,KAAKK;cAASC,IAAG;wBACpB7B,cAAc;gBAAEU,IAAI;gBAAoBC,gBAAgB;cAAU,CAAA;;;;;;;AAOjF;;;;;;ACvBA,IAAMmB,uBAA2BC,QAAM,EAAGC,MAAM;EAC9CC,WAAeC,QAAM,EAAGC,KAAI,EAAGC,SAASC,YAAiBD,QAAQ,EAAEE,SAAQ;EAC3EC,UAAcL,QAAM,EAAGI,SAAQ;EAC/BE,UACGN,QAAM,EACNO,IAAI,GAAG;IACNC,IAAIL,YAAiBM,UAAUD;IAC/BE,gBAAgB;IAChBC,QAAQ;MAAEJ,KAAK;IAAE;GAElBK,EAAAA,KACC,aACA;IACEJ,IAAI;IACJE,gBAAgB;EAClB,GACA,SAAUG,OAAK;AACb,QAAI,CAACA,SAAS,OAAOA,UAAU,SAAU,QAAO;AAEhD,UAAMC,WAAWC,YAAYF,KAAAA;AAC7B,WAAOC,YAAY;GAGtBE,EAAAA,QAAQ,SAAS;IAChBC,SAAS;MACPT,IAAI;MACJE,gBAAgB;IAClB;GAEDM,EAAAA,QAAQ,SAAS;IAChBC,SAAS;MACPT,IAAI;MACJE,gBAAgB;IAClB;GAEDM,EAAAA,QAAQ,MAAM;IACbC,SAAS;MACPT,IAAI;MACJE,gBAAgB;IAClB;EACF,CAAA,EACCR,SAAS;IACRM,IAAIL,YAAiBD,SAASM;IAC9BE,gBAAgB;EAClB,CAAA,EACCN,SAAQ;EACXc,iBACGlB,QAAM,EACNE,SAAS;IACRM,IAAIL,YAAiBD,SAASM;IAC9BE,gBAAgB;EAClB,CAAA,EACCS,MAAM;IAAKC,OAAI,UAAA;IAAa;KAAO;IAClCZ,IAAI;IACJE,gBAAgB;EAClB,CAAA,EACCN,SAAQ;EACXiB,mBAAuBrB,QAAM,EAAGE,SAAS;IACvCM,IAAIL,YAAiBD,SAASM;IAC9BE,gBAAgB;EAClB,CAAA;AACF,CAAA;AAEA,IAAMY,wBAA4BzB,QAAM,EAAGC,MAAM;EAC/CC,WACGC,QAAM,EACNC,KAAI,EACJC,SAAS;IACRM,IAAIL,YAAiBD,SAASM;IAC9BE,gBAAgB;EAClB,CAAA,EACCN,SAAQ;EACXC,UAAcL,QAAM,EAAGI,SAAQ;EAC/BE,UACGN,QAAM,EACNO,IAAI,GAAG;IACNC,IAAIL,YAAiBM,UAAUD;IAC/BE,gBAAgB;IAChBC,QAAQ;MAAEJ,KAAK;IAAE;GAElBK,EAAAA,KACC,aACA;IACEJ,IAAI;IACJE,gBAAgB;EAClB,GACA,SAAUG,OAAK;AACb,QAAI,CAACA,MAAO,QAAO;AACnB,WAAO,IAAIU,YAAcC,EAAAA,OAAOX,KAAAA,EAAOY,UAAU;GAGpDT,EAAAA,QAAQ,SAAS;IAChBC,SAAS;MACPT,IAAI;MACJE,gBAAgB;IAClB;GAEDM,EAAAA,QAAQ,SAAS;IAChBC,SAAS;MACPT,IAAI;MACJE,gBAAgB;IAClB;GAEDM,EAAAA,QAAQ,MAAM;IACbC,SAAS;MACPT,IAAI;MACJE,gBAAgB;IAClB;EACF,CAAA,EACCR,SAAS;IACRM,IAAIL,YAAiBD,SAASM;IAC9BE,gBAAgB;EAClB,CAAA,EACCN,SAAQ;EACXc,iBACGlB,QAAM,EACNE,SAAS;IACRM,IAAIL,YAAiBD,SAASM;IAC9BE,gBAAgB;GAEjBN,EAAAA,SAAQ,EACRe,MAAM;IAAKC,OAAI,UAAA;IAAa;KAAO;IAClCZ,IAAI;IACJE,gBAAgB;EAClB,CAAA;EACFgB,OACG1B,QAAM,EACN0B,MAAM;IACLlB,IAAIL,YAAiBuB,MAAMlB;IAC3BE,gBAAgB;GAEjBiB,EAAAA,OAAM,EACNC,UAAU;IACTpB,IAAIL,YAAiByB,UAAUpB;IAC/BE,gBAAgB;EAClB,CAAA,EACCR,SAAS;IACRM,IAAIL,YAAiBD,SAASM;IAC9BE,gBAAgB;EAClB,CAAA,EACCN,SAAQ;AACb,CAAA;AAgBA,IAAMyB,WAAW,CAAC,EAAEC,SAAQ,MAAiB;AAC3C,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAMC,WAAWC,YAAAA;AACjB,QAAM,CAACC,aAAaC,cAAAA,IAAwBC,gBAAS,CAAA;AACrD,QAAM,CAACC,UAAUC,WAAY,IAASF,gBAAQ;AAC9C,QAAM,EAAEG,WAAU,IAAKC,YAAAA;AACvB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAMC,aAAaC,cAAc,YAAY,CAACC,UAAUA,MAAMF,UAAU;AACxE,QAAM,EAAEG,QAAQC,aAAY,IAAKC,YAAAA;AACjC,QAAMC,QAAcC,eAAQ,MAAM,IAAIC,gBAAgBJ,YAAe,GAAA;IAACA;EAAa,CAAA;AACnF,QAAMK,QAAQC,SAAS,iBAAA;AACvB,QAAM,EACJC,yBAAyBC,gBACzBC,iCAAiCC,uBAAsB,IACrDC,mBAAAA;AACJ,QAAM,EAAEC,qBAAoB,IAAKC,qBAAAA;AAEjC,QAAMxC,oBAAoB6B,MAAMY,IAAI,mBAAA;AAEpC,QAAM,EAAEC,MAAMC,UAAUC,MAAK,IAAKC,4BAA4B7C,mBAA6B;IACzF8C,MAAM,CAAC9C;EACT,CAAA;AAEA+C,EAAMC,iBAAU,MAAA;AACd,QAAIJ,OAAO;AACT,YAAMhD,UAAkBqD,iBAAiBL,KAAAA,IACrCT,eAAeS,KACdA,IAAAA,MAAMhD,WAAW;AAEtBc,yBAAmB;QACjBwC,MAAM;QACNtD;MACF,CAAA;AAEAgB,eAAS,mBAAmBuC,mBAAmBvD,OAAAA,CAAAA,EAAU;IAC3D;KACC;IAACgD;IAAOT;IAAgBvB;IAAUF;EAAmB,CAAA;AAExD,QAAM,CAAC0C,aAAAA,IAAiBC,yBAAAA;AACxB,QAAM,CAACC,YAAAA,IAAgBC,wBAAAA;AACvB,QAAMC,WAAWC,iBAAAA;AAEjB,QAAMC,sBAAsB,OAC1B,EAAEC,MAAM,GAAGC,KAAAA,GACXC,kBAAAA;AAEA,UAAMC,MAAM,MAAMV,cAAcQ,IAAAA;AAEhC,QAAI,UAAUE,KAAK;AACjBN,eAASO,MAAM;QAAEC,OAAOF,IAAIpB,KAAKsB;MAAM,CAAA,CAAA;AAEvC,YAAM,EAAEC,MAAK,IAAKH,IAAIpB,KAAKwB;AAE3B,UAAID,OAAO;AACT,cAAME,mBAAmBF,MAAMG,KAAK,CAAC,EAAEC,KAAI,MAAOA,SAAS,oBAAA;AAE3D,YAAIF,kBAAkB;AACpBG,uBAAaC,QAAQ,uBAAuBC,KAAKC,UAAU,KAAA,CAAA;AAC3DlD,qBAAW,KAAA;AACXJ,qBAAW,qBAAA;QACb;MACF;AAEA,UAAIwC,MAAM;AAERpB,6BAAqB,CAACmC,OAAO;UAAE,GAAGA;UAAGC,SAAS;UAAK;AAEnD/D,iBAAS;UACPgE,UAAU;UACVlD,QAAQ,aAAa,IAAA;QACvB,CAAA;aACK;AACLd,iBAAS,GAAA;MACX;WACK;AACL,UAAIqC,iBAAiBa,IAAIlB,KAAK,GAAG;AAC/BzB,mBAAW,wBAAA;AAEX,YAAI2C,IAAIlB,MAAMiC,SAAS,mBAAmB;AACxChB,wBAAcxB,uBAAuByB,IAAIlB,KAAK,CAAA;AAC9C;QACF;AAEA1B,oBAAYiB,eAAe2B,IAAIlB,KAAK,CAAA;MACtC;IACF;EACF;AAEA,QAAMkC,qBAAqB,OACzB,EAAEnB,MAAM,GAAGC,KAAAA,GACXC,kBAAAA;AAEA,UAAMC,MAAM,MAAMR,aAAaM,IAAAA;AAE/B,QAAI,UAAUE,KAAK;AACjBN,eAASO,MAAM;QAAEC,OAAOF,IAAIpB,KAAKsB;MAAM,CAAA,CAAA;AAEvC,UAAIL,MAAM;AAERpB,6BAAqB,CAACmC,OAAO;UAAE,GAAGA;UAAGC,SAAS;UAAK;AAEnD/D,iBAAS;UACPgE,UAAU;UACVlD,QAAQ,aAAajB,QAAAA;QACvB,CAAA;aACK;AACLG,iBAAS,GAAA;MACX;WACK;AACL,UAAIqC,iBAAiBa,IAAIlB,KAAK,GAAG;AAC/BzB,mBAAW,wBAAA;AAEX,YAAI2C,IAAIlB,MAAMiC,SAAS,mBAAmB;AACxChB,wBAAcxB,uBAAuByB,IAAIlB,KAAK,CAAA;AAC9C;QACF;AAEA1B,oBAAYiB,eAAe2B,IAAIlB,KAAK,CAAA;MACtC;IACF;EACF;AAEA,MACE,CAACZ,SACAA,MAAM+C,OAAOC,aAAa,cAAchD,MAAM+C,OAAOC,aAAa,kBACnE;AACA,eAAOC,0BAACC,UAAAA;MAASC,IAAG;;EACtB;AAEA,QAAMC,sBAAsBpD,MAAM+C,OAAOC,aAAa;AAEtD,QAAMK,SAASD,sBAAsBnF,wBAAwB1B;AAE7D,aACE0G,0BAACK,uBAAAA;IACC,cAAAC,2BAACC,eAAAA;;YACCD,2BAACE,MAAAA;UAAKC,WAAU;UAASC,YAAW;UAASC,KAAK;;gBAChDX,0BAACY,MAAAA,CAAAA,CAAAA;gBAEDZ,0BAACa,YAAAA;cAAWC,KAAI;cAAKC,SAAQ;cAAQC,WAAU;wBAC5C5E,cAAc;gBACblC,IAAI;gBACJE,gBAAgB;cAClB,CAAA;;gBAEF4F,0BAACa,YAAAA;cAAWE,SAAQ;cAAUE,WAAU;cAAaD,WAAU;wBAC5D5E,cAAc;gBACblC,IAAI;gBACJE,gBACE;cACJ,CAAA;;YAED4B,eACCgE,0BAACa,YAAAA;cAAW3G,IAAG;cAAoBgH,MAAK;cAAQC,UAAU;cAAIF,WAAU;cACrEjF,UAAAA;YAED,CAAA,IAAA;;;YAENgE,0BAACoB,MAAAA;UACCC,QAAO;UACPC,eACE;YACE7H,YAAWiE,qCAAUjE,cAAa;YAClCM,WAAU2D,qCAAU3D,aAAY;YAChCqB,QAAOsC,qCAAUtC,UAAS;YAC1BpB,UAAU;YACVY,iBAAiB;YACjBG,mBAAmBA,qBAAqBwG;YACxC7C,MAAM;UACR;UAEF8C,UAAU,OAAO/D,MAAMgE,YAAAA;AACrB,kBAAMC,iBAAiBC,cAAclE,IAAAA;AAErC,gBAAI;AACF,oBAAM2C,OAAOwB,SAASF,gBAAgB;gBAAEG,YAAY;cAAM,CAAA;AAE1D,kBAAIhG,cAAc,KAAKsE,qBAAqB;AAC1CjE,2BAAW,iCAAiC;kBAAE4F,OAAOjG,YAAYkG,SAAQ;gBAAG,CAAA;cAC9E;AAEA,kBAAIL,eAAe3G,mBAAmB;AACpC8E,mCACE;kBACEnC,cAAUsE,YAAAA,SAAKN,gBAAgB;oBAC7B;oBACA;oBACA;oBACA;kBACD,CAAA;kBACD3G,mBAAmB2G,eAAe3G;kBAClC2D,MAAMgD,eAAehD;gBACvB,GACA+C,QAAQQ,SAAS;qBAEd;AACL,sBAAMxD,wBACJuD,YAAAA,SAAKN,gBAAgB;kBAAC;kBAAqB;gBAAkB,CAAA,GAC7DD,QAAQQ,SAAS;cAErB;YACF,SAASC,KAAK;AACZ,kBAAIA,eAAeC,iBAAiB;AAClCV,wBAAQQ,UACNC,IAAIE,MAAMC,OAA+B,CAACC,KAAK,EAAE3H,SAAS4H,KAAI,MAAE;AAC9D,sBAAIA,QAAQ,OAAO5H,YAAY,UAAU;AACvC2H,wBAAIC,IAAK,IAAGnG,cAAczB,OAAAA;kBAC5B;AACA,yBAAO2H;gBACT,GAAG,CAAA,CAAC,CAAA;cAER;AACAxG,6BAAeD,cAAc,CAAA;YAC/B;UACF;UAEA,cAAAyE,2BAACE,MAAAA;YAAKC,WAAU;YAASC,YAAW;YAAUC,KAAK;YAAG6B,WAAW;;kBAC/DxC,0BAACyC,KAAKC,MAAI;gBAAC/B,KAAK;gBACb,UAAA;kBACC;oBACEgC,OAAOvG,cAAc;sBACnBlC,IAAI;sBACJE,gBAAgB;oBAClB,CAAA;oBACAwF,MAAM;oBACNhG,UAAU;oBACVgJ,MAAM;oBACN3E,MAAM;kBACR;kBACA;oBACE0E,OAAOvG,cAAc;sBACnBlC,IAAI;sBACJE,gBAAgB;oBAClB,CAAA;oBACAwF,MAAM;oBACNgD,MAAM;oBACN3E,MAAM;kBACR;kBACA;oBACE4E,UAAU,CAAC1C;oBACXwC,OAAOvG,cAAc;sBACnBlC,IAAI;sBACJE,gBAAgB;oBAClB,CAAA;oBACAwF,MAAM;oBACNhG,UAAU;oBACVgJ,MAAM;oBACN3E,MAAM;kBACR;kBACA;oBACE6E,MAAM1G,cAAc;sBAClBlC,IAAI;sBACJE,gBACE;oBACJ,CAAA;oBACAuI,OAAOvG,cAAc;sBACnBlC,IAAI;sBACJE,gBAAgB;oBAClB,CAAA;oBACAwF,MAAM;oBACNhG,UAAU;oBACVgJ,MAAM;oBACN3E,MAAM;kBACR;kBACA;oBACE0E,OAAOvG,cAAc;sBACnBlC,IAAI;sBACJE,gBAAgB;oBAClB,CAAA;oBACAwF,MAAM;oBACNhG,UAAU;oBACVgJ,MAAM;oBACN3E,MAAM;kBACR;kBACA;oBACE0E,OAAOvG,cACL;sBACElC,IAAI;sBACJE,gBACE;uBAEJ;sBACE2I,WACE/C,0BAACgD,GAAAA;wBAAEC,QAAO;wBAASC,MAAK;wBAA0BC,KAAI;kCACnD/G,cAAc;0BACblC,IAAI;0BACJE,gBAAgB;wBAClB,CAAA;;sBAGJgJ,YACEpD,0BAACgD,GAAAA;wBAAEC,QAAO;wBAASC,MAAK;wBAA4BC,KAAI;kCACrD/G,cAAc;0BACblC,IAAI;0BACJE,gBAAgB;wBAClB,CAAA;;oBAGN,CAAA;oBAEFwF,MAAM;oBACNgD,MAAM;oBACN3E,MAAM;kBACR;kBACAoF,IAAI,CAAC,EAAET,MAAM,GAAGU,MAAO,UACvBtD,0BAACyC,KAAKc,MAAI;kBAAkBC,KAAKZ;kBAAMnC,WAAU;kBAASC,YAAW;kBACnE,cAAAV,0BAACyD,uBAAAA;oBAAe,GAAGH;;gBADLA,GAAAA,MAAM1D,IAAI,CAAA;;kBAK9BI,0BAAC0D,QAAAA;gBAAOC,WAAS;gBAACf,MAAK;gBAAI3E,MAAK;0BAC7B7B,cAAc;kBACblC,IAAI;kBACJE,gBAAgB;gBAClB,CAAA;;;;;SAIL2C,+BAAO+C,OAAOC,cAAa,kBAC1BC,0BAAC4D,KAAAA;UAAIC,YAAY;UACf,cAAA7D,0BAACQ,MAAAA;YAAKsD,gBAAe;YACnB,cAAA9D,0BAAC+D,OAAAA;cAAKjD,KAAKkD;cAAS9D,IAAG;wBACpB9D,cAAc;gBACblC,IAAI;gBACJE,gBAAgB;cAClB,CAAA;;;;;;;AAQhB;AAmBA,SAASuH,cAAclE,MAAwB;AAC7C,SAAOwG,OAAOC,QAAQzG,IAAM4E,EAAAA,OAC1B,CAACC,KAAK,CAAC6B,KAAK5J,KAAM,MAAA;AAOhB,QAAI,CAAC;MAAC;MAAY;IAAkB,EAAC6J,SAASD,GAAQ,KAAA,OAAO5J,UAAU,UAAU;AAC/E+H,UAAI6B,GAAAA,IAAmC5J,MAAMZ,KAAI;AAEjD,UAAIwK,QAAQ,YAAY;AACtB7B,YAAI6B,GAAI,IAAG5J,SAASgH;MACtB;WACK;AACLe,UAAI6B,GAAAA,IAAuB5J;IAC7B;AAEA,WAAO+H;EACT,GACA,CAAA,CAAC;AAUL;AAEA,IAAMU,IAAIqB,GAAOC;WACN,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAOC,UAAU;;;;;;ACziBjD,IAAMC,wBAA4BC,QAAM,EAAGC,MAAM;EAC/CC,UACGC,QAAM,EACNC,IAAI,GAAG;IACNC,IAAIC,YAAiBC,UAAUF;IAC/BG,gBAAgB;IAChBC,QAAQ;MAAEL,KAAK;IAAE;EACnB,CAAA,EAECM,KACC,sBACA;IACEL,IAAI;IACJG,gBAAgB;EAClB,GACA,SAAUG,OAAK;AACb,QAAI,CAACA,SAAS,OAAOA,UAAU,SAAU,QAAO;AAEhD,UAAMC,WAAWC,YAAYF,KAAAA;AAC7B,WAAOC,YAAY;GAGtBE,EAAAA,QAAQ,SAAS;IAChBC,SAAS;MACPV,IAAI;MACJG,gBAAgB;IAClB;GAEDM,EAAAA,QAAQ,SAAS;IAChBC,SAAS;MACPV,IAAI;MACJG,gBAAgB;IAClB;GAEDM,EAAAA,QAAQ,MAAM;IACbC,SAAS;MACPV,IAAI;MACJG,gBAAgB;IAClB;EACF,CAAA,EACCQ,SAAS;IACRX,IAAIC,YAAiBU,SAASX;IAC9BG,gBAAgB;EAClB,CAAA,EACCS,SAAQ;EACXC,iBACGf,QAAM,EACNa,SAAS;IACRX,IAAIC,YAAiBU,SAASX;IAC9BG,gBAAgB;EAClB,CAAA,EACCW,MAAM;IAAKC,OAAI,UAAA;IAAa;KAAO;IAClCf,IAAI;IACJG,gBAAgB;EAClB,CAAA,EACCS,SAAQ;AACb,CAAA;AAEA,IAAMI,gBAAgB,MAAA;AACpB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAMC,WAAWC,iBAAAA;AACjB,QAAMC,WAAWC,YAAAA;AACjB,QAAM,EAAEC,QAAQC,aAAY,IAAKC,YAAAA;AACjC,QAAMC,QAAcC,eAAQ,MAAM,IAAIC,gBAAgBJ,YAAe,GAAA;IAACA;EAAa,CAAA;AACnF,QAAM,EAAEK,yBAAyBC,eAAc,IAAKC,mBAAAA;AAEpD,QAAM,CAACC,eAAe,EAAEC,MAAK,CAAE,IAAIC,yBAAAA;AAEnC,QAAMC,eAAe,OAAOC,SAAAA;AAC1B,UAAMC,MAAM,MAAML,cAAcI,IAAAA;AAEhC,QAAI,UAAUC,KAAK;AACjBlB,eAASmB,MAAM;QAAEC,OAAOF,IAAIG,KAAKD;MAAM,CAAA,CAAA;AACvClB,eAAS,GAAA;IACX;EACF;AAKA,MAAI,CAACK,MAAMe,IAAI,MAAS,GAAA;AACtB,eAAOC,0BAACC,UAAAA;MAASC,IAAG;;EACtB;AAEA,aACEF,0BAACG,uBAAAA;IACC,cAAAC,2BAACC,MAAAA;;YACCD,2BAACE,eAAAA;;gBACCF,2BAACG,QAAAA;;oBACCP,0BAACQ,MAAAA,CAAAA,CAAAA;oBACDR,0BAACS,KAAAA;kBAAIC,YAAY;kBAAGC,eAAe;kBACjC,cAAAX,0BAACY,YAAAA;oBAAWC,KAAI;oBAAKC,SAAQ;8BAC1BvC,cAAc;sBACbjB,IAAI;sBACJG,gBAAgB;oBAClB,CAAA;;;gBAGH8B,YACCS,0BAACY,YAAAA;kBAAWtD,IAAG;kBAAoByD,MAAK;kBAAQC,UAAU;kBAAIC,WAAU;4BACrEC,iBAAiB3B,KAAAA,IACdH,eAAeG,KAAAA,IACfhB,cAAc;oBACZjB,IAAI;oBACJG,gBAAgB;kBAClB,CAAA;gBAEJ,CAAA,IAAA;;;gBAENuC,0BAACmB,MAAAA;cACCC,QAAO;cACPC,eAAe;gBACblE,UAAU;gBACVgB,iBAAiB;cACnB;cACAmD,UAAU,CAAC5D,WAAAA;AAET+B,6BAAa;kBAAEtC,UAAUO,OAAOP;kBAAUoE,oBAAoBvC,MAAMe,IAAI,MAAA;gBAAS,CAAA;cACnF;cACAyB,kBAAkBxE;cAElB,cAAAoD,2BAACqB,MAAAA;gBAAKC,WAAU;gBAASC,YAAW;gBAAUC,KAAK;;kBAChD;oBACC;sBACEC,MAAMtD,cAAc;wBAClBjB,IAAI;wBACJG,gBACE;sBACJ,CAAA;sBACAqE,OAAOvD,cAAc;wBACnBjB,IAAI;wBACJG,gBAAgB;sBAClB,CAAA;sBACAsE,MAAM;sBACN9D,UAAU;sBACV+D,MAAM;oBACR;oBACA;sBACEF,OAAOvD,cAAc;wBACnBjB,IAAI;wBACJG,gBAAgB;sBAClB,CAAA;sBACAsE,MAAM;sBACN9D,UAAU;sBACV+D,MAAM;oBACR;kBACD,EAACC,IAAI,CAACC,cACLlC,0BAACmC,uBAAAA;oBAAgC,GAAGD;kBAAhBA,GAAAA,MAAMH,IAAI,CAAA;sBAEhC/B,0BAACoC,QAAAA;oBAAOC,WAAS;oBAACL,MAAK;8BACpBzD,cAAc;sBACbjB,IAAI;sBACJG,gBAAgB;oBAClB,CAAA;;;;;;;YAKRuC,0BAACyB,MAAAA;UAAKa,gBAAe;UACnB,cAAAtC,0BAACS,KAAAA;YAAIC,YAAY;YACf,cAAAV,0BAACuC,OAAAA;cAAK1B,KAAK2B;cAAStC,IAAG;wBACpB3B,cAAc;gBAAEjB,IAAI;gBAAmBG,gBAAgB;cAAoB,CAAA;;;;;;;AAO1F;;;IC5KagF,QAAQ;EACnB,mBAAmBC;EACnB,2BAA2BC;;;EAG3BC,OAAO,MAAM;EACbC,MAAMC;EACNC,UAAUC;EACV,kBAAkBA;EAClB,kBAAkBC;EAClBC,WAAW,MAAM;AACnB;;;ACpBkG,IAE5FC,WAAW,MAAA;AACf,QAAM,EAAEC,OAAM,IAAKC,YAAAA;AACnB,QAAMC,QAAQC,SAAS,iBAAA;AACvB,QAAMC,WAAWF,+BAAOG,OAAOD;AAC/B,QAAM,EAAEE,KAAI,IAAKC,aAAAA;AACjB,QAAM,EAAEC,SAAQ,IAAKF,QAAQ,CAAA;AAC7B,QAAMG,UAAQC,cACZC,OACA,aAAa,MAAM,OAAO,qBAA0D,GAAGC,OAAO;AAEhG,QAAMC,QAAQH,cACZI,OACA,aAAa,MAAM,OAAO,yBAAA,GAAsDA,OAChF;IACEC,QAAQC,SAASC,SAAO;AACtB,aAAO;QACL,GAAGD;QACH,GAAGC;MACL;IACF;IACAC,cAAcJ;EAChB,CAAA;AAGF,QAAM,EAAEK,MAAK,IAAKC,QAAQ,YAAY,CAACC,SAASA,IAAAA;AAEhD,MAAI,CAACjB,YAAY,CAACS,OAAO;AACvB,eAAOS,0BAACC,UAAAA;MAASC,IAAG;;EACtB;AAEA,QAAMC,YAAYZ,MAAMT,QAAiC;AAIzD,MAAI,CAACqB,WAAW;AACd,eAAOH,0BAACC,UAAAA;MAASC,IAAG;;EACtB;AAGA,MAAIpB,aAAa,oBAAoBA,aAAa,cAAce,OAAO;AACrE,eAAOG,0BAACC,UAAAA;MAASC,IAAG;;EACtB;AAGA,MAAIhB,YAAYJ,aAAa,oBAAoBe,OAAO;AACtD,eAAOG,0BAACC,UAAAA;MAASC,IAAG;;EACtB;AAGA,MAAI,CAAChB,YAAYJ,aAAa,kBAAkB;AAC9C,eACEkB,0BAACC,UAAAA;MACCC,IAAI;QACFE,UAAU;;;QAGV1B;MACF;;EAGN;AAEA,MAAIS,WAASL,aAAa,SAAS;AAEjC,eAAOkB,0BAACb,SAAAA,CAAAA,CAAAA;EACV,WAAWL,aAAa,WAAW,CAACK,SAAO;AAEzC,WAAO;EACT;AAEA,aAAOa,0BAACG,WAAAA;IAAUjB;;AACpB;;;IClFamB,YAA2B;EACtC;IACEC,MAAM,YAAA;AACJ,YAAM,EAAEC,kBAAiB,IAAK,MAAM,OAAO,wBAAA;AAE3C,aAAO;QACLC,WAAWD;MACb;IACF;IACAE,MAAM;EACR;EACA;IACEH,MAAM,YAAA;AACJ,YAAM,EAAEI,oBAAmB,IAAK,MAAM,OAAO,0BAAA;AAE7C,aAAO;QACLF,WAAWE;MACb;IACF;IACAD,MAAM;EACR;EACA;IACEH,MAAM,YAAA;AACJ,YAAM,EAAEI,oBAAmB,IAAK,MAAM,OAAO,0BAAA;AAE7C,aAAO;QACLF,WAAWE;MACb;IACF;IACAD,MAAM;EACR;EACA;IACEH,MAAM,YAAA;AACJ,YAAM,EAAEK,kBAAiB,IAAK,MAAM,OAAO,wBAAA;AAE3C,aAAO;QACLH,WAAWG;MACb;IACF;IACAF,MAAM;EACR;EACA;IACEH,MAAM,YAAA;AACJ,YAAM,EAAEC,kBAAiB,IAAK,MAAM,OAAO,wBAAA;AAE3C,aAAO;QACLC,WAAWD;MACb;IACF;IACAE,MAAM;EACR;EACA;IACEH,MAAM,YAAA;AACJ,YAAM,EAAEK,kBAAiB,IAAK,MAAM,OAAO,wBAAA;AAE3C,aAAO;QACLH,WAAWG;MACb;IACF;IACAF,MAAM;EACR;EACA;IACEH,MAAM,YAAA;AACJ,YAAM,EAAEI,oBAAmB,IAAK,MAAM,OAAO,0BAAA;AAE7C,aAAO;QACLF,WAAWE;MACb;IACF;IACAD,MAAM;EACR;EACA;IACEH,MAAM,YAAA;AACJ,YAAMM,cAAc,MAAM,OAAO,wBAAA;AAEjC,aAAO;QACLJ,WAAWI,YAAYD;MACzB;IACF;IACAF,MAAM;EACR;EACA;IACEH,MAAM,YAAA;AACJ,YAAM,EAAEC,kBAAiB,IAAK,MAAM,OAAO,wBAAA;AAE3C,aAAO;QACLC,WAAWD;MACb;IACF;IACAE,MAAM;EACR;EACA;IACEH,MAAM,YAAA;AACJ,YAAM,EAAEO,kBAAiB,IAAK,MAAM,OAAO,wBAAA;AAE3C,aAAO;QACLL,WAAWK;MACb;IACF;IACAJ,MAAM;EACR;EACA;IACEH,MAAM,YAAA;AACJ,YAAM,EAAEQ,oBAAmB,IAAK,MAAM,OAAO,0BAAA;AAE7C,aAAO;QACLN,WAAWM;MACb;IACF;IACAL,MAAM;EACR;EACA;IACEH,MAAM,YAAA;AACJ,YAAM,EAAES,kBAAiB,IAAK,MAAM,OAAO,4BAAA;AAE3C,aAAO;QACLP,WAAWO;MACb;IACF;IACAN,MAAM;EACR;EACA;IACEH,MAAM,YAAA;AACJ,YAAM,EAAEQ,oBAAmB,IAAK,MAAM,OAAO,0BAAA;AAE7C,aAAO;QACLN,WAAWM;MACb;IACF;IACAL,MAAM;EACR;EACA;IACEH,MAAM,YAAA;AACJ,YAAM,EAAEO,kBAAiB,IAAK,MAAM,OAAO,wBAAA;AAE3C,aAAO;QACLL,WAAWK;MACb;IACF;IACAJ,MAAM;EACR;EACA;IACEH,MAAM,YAAA;AACJ,YAAM,EAAES,kBAAiB,IAAK,MAAM,OAAO,wBAAA;AAE3C,aAAO;QACLP,WAAWO;MACb;IACF;IACAN,MAAM;EACR;EACA;IACEH,MAAM,YAAA;AACJ,YAAM,EAAEU,0BAAyB,IAAK,MAAM,OAAO,gCAAA;AAEnD,aAAO;QACLR,WAAWQ;MACb;IACF;IACAP,MAAM;EACR;EAEA;IACEH,MAAM,YAAA;AACJ,YAAM,EAAEW,kBAAiB,IAAK,MAAM,OAAO,iCAAA;AAE3C,aAAO;QACLT,WAAWS;MACb;IACF;IACAR,MAAM;EACR;EACA;IACEH,MAAM,YAAA;AACJ,YAAM,EAAEY,qBAAoB,IAAK,MAAM,OAAO,oCAAA;AAE9C,aAAO;QACLV,WAAWU;MACb;IACF;IACAT,MAAM;EACR;EACA;IACEH,MAAM,YAAA;AACJ,YAAM,EAAEa,uBAAsB,IAAK,MAAM,OAAO,sCAAA;AAEhD,aAAO;QACLX,WAAWW;MACb;IACF;IACAV,MAAM;EACR;;;;ACpLF,IAAMW,qBAAqB,MAAqB;EAC9C;IACEC,MAAM;IACNC,MAAM,YAAA;AACJ,YAAM,EAAEC,mBAAkB,IAAK,MAAM,OAAO,2BAAA;AAE5C,aAAO;QACLC,WAAWD;MACb;IACF;EACF;;EAEGE,GAAAA,YAAAA;EACH;IACEJ,MAAM;IACNK,aAASC,0BAACC,UAAAA,CAAAA,CAAAA;EACZ;AACD;AAED,IAAMC,mBAAmB,MAAqB;EAC5C;IACEC,OAAO;IACPR,MAAM,YAAA;AACJ,YAAM,EAAES,SAAQ,IAAK,MAAM,OAAO,wBAAA;AAElC,aAAO;QACLP,WAAWO;MACb;IACF;EACF;EACA;IACEV,MAAM;IACNC,MAAM,YAAA;AACJ,YAAM,EAAEU,YAAW,IAAK,MAAM,OAAO,2BAAA;AAErC,aAAO;QACLR,WAAWQ;MACb;IACF;EACF;EACA;IACEX,MAAM;IACNC,MAAM,YAAA;AACJ,YAAM,EAAEW,yBAAwB,IAAK,MAAM,OAAO,+BAAA;AAElD,aAAO;QACLT,WAAWS;MACb;IACF;EACF;EACA;IACEZ,MAAM;IACNC,MAAM,YAAA;AACJ,YAAM,EAAEY,OAAM,IAAK,MAAM,OAAO,sBAAA;AAEhC,aAAO;QACLV,WAAWU;MACb;IACF;IACAC,UAAU;MACR;QACEd,MAAM;QACNC,MAAM,YAAA;AACJ,gBAAM,EAAEc,oBAAmB,IAAK,MAAM,OACpC,mCAAA;AAGF,iBAAO;YACLZ,WAAWY;UACb;QACF;MACF;;;;;;;;;;;MAWG,GAAA;QAAIC,GAAAA,aAAAA;QAA0BC,GAAAA;MAAU,EAACC,OAC1C,CAACC,OAAOV,OAAOW,aAAaA,SAASC,UAAU,CAACC,QAAQA,IAAItB,SAASmB,MAAMnB,IAAI,MAAMS,KAAAA;IAExF;EACH;AACD;;;AC9CD,IAAMc,SAAN,MAAMA;EAmBJ,IAAIC,SAAS;AACX,WAAO,KAAKC;EACd;EAEA,IAAIC,OAAO;AACT,WAAO,KAAKC;EACd;EAEA,IAAIC,WAAW;AACb,WAAO,KAAKC;EACd;;;;;EAMAC,aAAaC,QAAmB,EAAEC,QAAQ,GAAGC,KAAqB,IAAG,CAAA,GAAI;AACvE,UAAMT,SAAS;MACb;QACEU,MAAM;QACNC,kBACEC,0BAACC,kBAAAA;UAASC,OAAOP,OAAOO;UACtB,cAAAF,0BAACG,kBAAAA;YAAiBC,UAAUT,OAAOU,eAAeC;YAChD,cAAAN,0BAACO,OAAAA;cAAMC,QAAQb,OAAOU,eAAeG;cACnC,cAAAR,0BAACS,cAAAA,CAAAA,CAAAA;;;;QAKTC,aAASV,0BAACW,KAAAA;UAAIhB;UAAgBO,OAAOP,OAAOO;;QAC5CU,UAAU;UACLC,GAAAA,mBAAAA;UACH;YACEf,MAAM;YACNgB,MAAM,YAAA;AACJ,oBAAM,EAAEC,mBAAkB,IAAK,MAAM,OAAO,mCAAA;AAE5C,qBAAO;gBACLC,WAAWD;cACb;YACF;YACAH,UAAU;cACL,GAAA,KAAKxB;cACR;gBACEU,MAAM;gBACNY,aAASV,0BAACiB,cAAAA,CAAAA,CAAAA;cACZ;YACD;UACH;QACD;MACH;IACD;AAED,QAAIrB,QAAQ;AACV,WAAKsB,SAASC,mBAAmB/B,QAAQS,IAAAA;WACpC;AACL,WAAKqB,SAASE,oBAAoBhC,QAAQS,IAAAA;IAC5C;AAEA,WAAO,KAAKqB;EACd;EA6EOG,gBACLC,SAIAC,MACM;;AACN,QAAI,OAAOD,YAAY,YAAY,WAAWA,SAAS;AAIrDE,4BAAAA,SAAUF,QAAQG,IAAI,8BAAA;AACtBD,4BAAAA,WACEF,aAAQI,cAARJ,mBAAmBG,SAAMH,aAAQI,cAARJ,mBAAmBK,iBAC5C,qCAAA;AAEFH,4BAAAA,SAAU,KAAKhC,SAAS8B,QAAQG,EAAE,MAAMG,QAAW,kCAAA;AACnDJ,4BAAAA,SAAUK,MAAMC,QAAQR,QAAQS,KAAK,GAAG,yCAAA;AAExC,WAAKvC,SAAS8B,QAAQG,EAAE,IAAI;QAAE,GAAGH;QAASS,OAAO,CAAA;MAAG;AAEpDT,cAAQS,MAAMC,QAAQ,CAACT,UAAAA;AACrB,aAAKU,mBAAmBX,QAAQG,IAAIF,KAAAA;MACtC,CAAA;IACF,WAAW,OAAOD,YAAY,YAAYC,MAAM;AAI9CC,4BAAAA,SAAUF,QAAQG,IAAI,8BAAA;AACtBD,4BAAAA,WACEF,aAAQI,cAARJ,mBAAmBG,SAAMH,aAAQI,cAARJ,mBAAmBK,iBAC5C,qCAAA;AAEFH,4BAAAA,SAAU,KAAKhC,SAAS8B,QAAQG,EAAE,MAAMG,QAAW,kCAAA;AAEnD,WAAKpC,SAAS8B,QAAQG,EAAE,IAAI;QAAE,GAAGH;QAASS,OAAO,CAAA;MAAG;AAEpD,UAAIF,MAAMC,QAAQP,IAAO,GAAA;AACvBA,aAAKS,QAAQ,CAACE,MAAM,KAAKD,mBAAmBX,QAAQG,IAAIS,CAAAA,CAAAA;aACnD;AACL,aAAKD,mBAAmBX,QAAQG,IAAIF,IAAAA;MACtC;IACF,WAAW,OAAOD,YAAY,YAAYC,MAAM;AAC9C,UAAIM,MAAMC,QAAQP,IAAO,GAAA;AACvBA,aAAKS,QAAQ,CAACE,MAAM,KAAKD,mBAAmBX,SAASY,CAAAA,CAAAA;aAChD;AACL,aAAKD,mBAAmBX,SAASC,IAAAA;MACnC;WACK;AACL,YAAM,IAAIY,MACR,8FAAA;IAEJ;EACF;;;;;;;EAuFAC,SAASC,OAA2D;AAClE,QAAIR,MAAMC,QAAQO,KAAQ,GAAA;AACxB,WAAKhD,UAAU;QAAI,GAAA,KAAKA;QAAYgD,GAAAA;MAAM;IAC5C,WAAW,OAAOA,UAAU,YAAYA,UAAU,MAAM;AACtD,WAAKhD,QAAQiD,KAAKD,KAAAA;eACT,OAAOA,UAAU,YAAY;AACtC,WAAKhD,UAAUgD,MAAM,KAAKhD,OAAO;WAC5B;AACL,YAAM,IAAI8C,MACR,4FAA4FI,iBAC1FF,KAAAA,CAAAA,EACC;IAEP;EACF;EAvSAG,YAAYC,eAA8B;AAdlCpD,SAAAA,UAAyB,CAAA;SACzB6B,SAAyB;AACzB3B,SAAAA,QAAuC,CAAA;SACvCE,YAA8C;MACpDiD,QAAQ;QACNjB,IAAI;QACJC,WAAW;UACTD,IAAI;UACJE,gBAAgB;QAClB;QACAI,OAAO,CAAA;MACT;IACF;AAoEOY,SAAAA,cAAc,CACnBpB,SAAAA;;AAIAC,4BAAAA,SAAUD,KAAKqB,IAAI,IAAIrB,KAAKG,UAAUC,cAAc,8BAA8B;AAClFH,4BAAAA,SACE,OAAOD,KAAKqB,OAAO,UACnB,IACErB,KAAKG,UAAUC,cAAc,uDACwB,OAAOJ,KAAKqB,EAAE,EAAE;AAEzEpB,4BAAAA,WACED,UAAKG,cAALH,mBAAgBE,SAAMF,UAAKG,cAALH,mBAAgBI,iBACtC,IAAIJ,KAAKG,UAAUC,cAAc,wEAAwE;AAE3GH,4BAAAA,SACE,CAACD,KAAKP,aAAcO,KAAKP,aAAa,OAAOO,KAAKP,cAAc,YAChE,IAAIO,KAAKG,UAAUC,cAAc,mJAAmJ;AAGtL,UACE,CAACJ,KAAKP,aACLO,KAAKP,aACJ,OAAOO,KAAKP,cAAc;MAE1BO,KAAKP,UAAU6B,OAAOC,WAAW,MAAM,iBACzC;AACAC,gBAAQC,KACN;SACCzB,KAAKG,UAAUC,cAAc,qFAAqFJ,KAAKG,UAAUC,cAAc;QAChJsB,KAAI,CAAA;MAER;AAEA,UAAI1B,KAAKqB,GAAGM,WAAW,GAAM,GAAA;AAC3BH,gBAAQC,KACN,IAAIzB,KAAKG,UAAUC,cAAc,wMAAwM;AAG3OJ,aAAKqB,KAAKrB,KAAKqB,GAAGO,MAAM,CAAA;MAC1B;AAEA,YAAM,EAAEnC,WAAW,GAAGoC,SAAAA,IAAa7B;AAEnC,UAAIP,WAAW;AACb,aAAK3B,QAAQiD,KAAK;UAChBxC,MAAM,GAAGyB,KAAKqB,EAAE;UAChB9B,MAAM,YAAA;AACJ,kBAAMuC,MAAM,MAAMrC,UAAAA;AAElB,gBAAI,aAAaqC,KAAK;AACpB,qBAAO;gBAAErC,WAAWqC,IAAIC;cAAQ;mBAC3B;AACL,qBAAO;gBAAEtC,WAAWqC;cAAI;YAC1B;UACF;QACF,CAAA;MACF;AAEA,WAAK/D,KAAKgD,KAAKc,QAAAA;IACjB;AAqEQnB,SAAAA,qBAAqB,CAACsB,WAAmBhC,SAAAA;;AAC/CC,4BAAAA,SAAU,KAAK/B,UAAU8D,SAAAA,GAAY,4BAAA;AAErC/B,4BAAAA,SAAUD,KAAKE,IAAI,IAAIF,KAAKG,UAAUC,cAAc,8BAA8B;AAClFH,4BAAAA,WACED,UAAKG,cAALH,mBAAgBE,SAAMF,UAAKG,cAALH,mBAAgBI,iBACtC,IAAIJ,KAAKG,UAAUC,cAAc,sDAAsD;AAEzFH,4BAAAA,SAAUD,KAAKqB,IAAI,IAAIrB,KAAKG,UAAUC,cAAc,8BAA8B;AAClFH,4BAAAA,SACE,CAACD,KAAKP,aAAcO,KAAKP,aAAa,OAAOO,KAAKP,cAAc,YAChE,IAAIO,KAAKG,UAAUC,cAAc,kHAAkH;AAGrJ,UACE,CAACJ,KAAKP,aACLO,KAAKP,aACJ,OAAOO,KAAKP,cAAc;MAE1BO,KAAKP,UAAU6B,OAAOC,WAAW,MAAM,iBACzC;AACAC,gBAAQC,KACN;SACCzB,KAAKG,UAAUC,cAAc,yFAAyFJ,KAAKG,UAAUC,cAAc;QACpJsB,KAAI,CAAA;MAER;AAEA,UAAI1B,KAAKqB,GAAGM,WAAW,GAAM,GAAA;AAC3BH,gBAAQC,KACN,IAAIzB,KAAKG,UAAUC,cAAc,8LAA8L;AAGjOJ,aAAKqB,KAAKrB,KAAKqB,GAAGO,MAAM,CAAA;MAC1B;AAEA,UAAI5B,KAAKqB,GAAGY,MAAM,GAAI,EAAC,CAAE,MAAK,YAAY;AACxCT,gBAAQC,KACN,IAAIzB,KAAKG,UAAUC,cAAc,uPAAuP;AAG1RJ,aAAKqB,KAAKrB,KAAKqB,GAAGY,MAAM,GAAA,EAAKL,MAAM,CAAGM,EAAAA,KAAK,GAAA;MAC7C;AAEA,YAAM,EAAEzC,WAAW,GAAGoC,SAAAA,IAAa7B;AAEnC,YAAMmC,gBAAgB,KAAKrE,QAAQsE,UAAU,CAACtB,UAAUA,MAAMvC,SAAS,YAAA;AAMvE,UAAI,CAAC4D,eAAe;AAClBX,gBAAQC,KACN,2FAAA;AAEF;iBACS,CAAC,KAAK3D,QAAQqE,aAAAA,EAAe9C,UAAU;AAChD,aAAKvB,QAAQqE,aAAAA,EAAe9C,WAAW,CAAA;MACzC;AAEA,UAAII,WAAW;AACb,aAAK3B,QAAQqE,aAAAA,EAAe9C,SAAU0B,KAAK;UACzCxC,MAAM,GAAGyB,KAAKqB,EAAE;UAChB9B,MAAM,YAAA;AACJ,kBAAMuC,MAAM,MAAMrC,UAAAA;AAElB,gBAAI,aAAaqC,KAAK;AACpB,qBAAO;gBAAErC,WAAWqC,IAAIC;cAAQ;mBAC3B;AACL,qBAAO;gBAAEtC,WAAWqC;cAAI;YAC1B;UACF;QACF,CAAA;MACF;AAEA,WAAK5D,UAAU8D,SAAAA,EAAWxB,MAAMO,KAAKc,QAAAA;IACvC;AAhRE,SAAK/D,UAAUoD;EACjB;AAsSF;AAWA,IAAMF,mBAAmB,CAACqB,UAAAA;AACxB,QAAMC,aAAa,OAAOD;AAE1B,MAAIC,eAAe,UAAU;AAC3B,QAAID,UAAU,KAAM,QAAO;AAC3B,QAAI/B,MAAMC,QAAQ8B,KAAAA,EAAQ,QAAO;AACjC,QAAIA,iBAAiBE,UAAUF,MAAMpB,YAAYuB,SAAS,UAAU;AAClE,aAAOH,MAAMpB,YAAYuB;IAC3B;EACF;AAEA,SAAOF;AACT;;;;ACrWA,IAAMG,UAAN,MAAMA;EAGJC,cAAc;AAIdC,SAAAA,WAAW,CAACC,WAAAA;AACV,UAAIC,MAAMC,QAAQF,MAAS,GAAA;AACzBA,eAAOG,QAAQ,CAACC,cAAAA;AACd,eAAKL,SAASK,SAAAA;QAChB,CAAA;aACK;AACLC,8BAAAA,SAAUL,OAAOM,IAAI,wBAAA;AACrBD,8BAAAA,SAAUL,OAAOO,WAAW,8BAAA;AAC5BF,8BAAAA,SAAUL,OAAOQ,OAAO,0BAAA;AACxBH,8BAAAA,SAAUL,OAAOS,MAAM,0BAAA;AAGvB,cAAM,EAAEH,IAAII,UAAU,GAAGC,cAAAA,IAAkBX;AAC3C,cAAMY,MAAiBF,WAAW,WAAWA,QAAS,IAAGJ,EAAAA,KAAO,WAAWA,EAAAA;AAE3E,aAAKO,QAAQD,GAAAA,IAAO;UAAE,GAAGD;UAAeC;QAAI;MAC9C;IACF;SAEAE,SAAS,MAAA;AACP,aAAOC,OAAOC,OAAO,KAAKH,OAAO;IACnC;AAxBE,SAAKA,UAAU,CAAA;EACjB;AAwBF;;;AC5CA,IAAMI,iBAAiB;EACrB,CAACC,SAASC,WAAW,GAAGD,SAASE;EACjCC,WAAWC;AACb;AAEA,IAAMC,6BACJ,CAACC,gBACD,CAACC,SACD,IAAIC,SAAAA;AACF,QAAMC,QAAQF,KAAQC,GAAAA,IAAAA;AAEtB,QAAME,gBAAyC,CAAA;AAE/C,SAAO;IACL,GAAGD;IACHC;IACAC,eAAe,CAACC,KAAaC,iBAAAA;AAC3BH,oBAAcE,GAAAA,IAAOC;AACrBJ,YAAMK;;QAEJC,gBAAgB;UACd,GAAGT;UACH,GAAGI;QACL,CAAA;MAAA;IAEJ;EACF;AACF;AAUIM,IAAAA,qBAAqB,CACzBC,iBAA+B,CAAA,GAC/BC,iBAA0C,CAAA,GAC1CC,mBAA4C,CAAA,MAAE;AAE9C,QAAMC,eAAe;IAAE,GAAGrB;IAAgB,GAAGoB;EAAiB;AAE9D,QAAME,2BAA2B,CAAA;AAIjC,MAAIC,OAAiC;AACnCD,6BAAyBE,oBAAoB;AAC7CF,6BAAyBG,iBAAiB;EAC5C;AAEA,QAAMf,QAAQgB,eAAe;IAC3BR,gBAAgB;MACdd,WAAWc,eAAed;IAC5B;IACAD,SAASkB;IACTM,UAAUJ;IACVK,YAAY,CAACC,yBAAyB;SACjCA,qBAAqBP,wBAAAA;MACxBQ;MACA7B,SAAS2B;SACNT,eAAeY,IAAI,CAACC,MAAMA,EAAAA,CAAAA;IAC9B;IACDC,WAAW;MAAC3B,2BAA2Be,YAAAA;IAAc;EACvD,CAAA;AAEA,SAAOX;AACT;AAEA,IAAMoB,iCACJ,CAAC,EAAEI,SAAQ,MACX,CAAC1B,SACD,CAAC2B,WAAAA;;AAEC,MAAIC,WAAWD,MAAWA,OAAAA,YAAOE,YAAPF,mBAAgBG,YAAW,KAAK;AACxDJ,aAASK,OAAAA,CAAAA;AACTC,WAAOC,SAASC,OAAO;AACvB;EACF;AAEA,SAAOlC,KAAK2B,MAAAA;AACd;;;ACnGF,IAOaQ,aAAa,MAAA;AACxB,QAAMC,YAAuB,CAAA;AAE7B,SAAO;IACLC,SAASC,IAAW;AAClBF,gBAAUG,KAAKD,EAAAA;IACjB;IACAE,OAAOC,SAAgB;AACrBL,gBAAUM,OAAON,UAAUO,QAAQF,OAAU,GAAA,CAAA;IAC/C;IACAG,aAAgBC,MAASC,OAAa;AACpC,aAAOV,UAAUW,OAAO,CAACC,KAAKV,OAAOA,GAAGU,KAAKF,KAAQD,GAAAA,IAAAA;IACvD;IACA,MAAMI,kBAAqBJ,MAASC,OAAa;AAC/C,UAAII,SAASL;AAEb,iBAAWP,MAAMF,WAAW;AAC1Bc,iBAAS,MAAMZ,GAAGY,QAAQJ,KAAAA;MAC5B;AAEA,aAAOI;IACT;IACAC,aAA8BN,MAAO;AACnC,aAAOT,UAAUgB,IAAI,CAACd,OAAOA,GAAMO,GAAAA,IAAAA,CAAAA;IACrC;IACA,MAAMQ,kBAAmCR,MAAO;AAC9C,YAAMK,SAAS,CAAA;AAEf,iBAAWZ,MAAMF,WAAW;AAC1Bc,eAAOX,KAAK,MAAMD,GAAMO,GAAAA,IAAAA,CAAAA;MAC1B;AAEA,aAAOK;IACT;IACAI,eAAgCT,MAAO;AACrC,aAAOU,QAAQC,IACbpB,UAAUgB,IAAI,CAACd,OAAAA;AACb,eAAOA,GAAMO,GAAAA,IAAAA;MACf,CAAA,CAAA;IAEJ;EACF;AACF;;;ICjDaY,sBAAsB;EACjCC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJ,SAAS;EACTC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJ,SAAS;EACTC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJ,WAAW;EACXC,IAAI;EACJC,IAAI;EACJC,IAAI;AACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACLA,IAAM,EACJC,wBACAC,+BACAC,yBACAC,0BAAyB,IACvBC;AAmDJ,IAAMC,YAAN,MAAMA;EAuIJ,MAAMC,UAAUC,iBAA2B;AACzCC,WAAOC,KAAK,KAAKC,UAAU,EAAEC,QAAQ,CAACC,WAAAA;AACpC,YAAMN,YAAY,KAAKI,WAAWE,MAAAA,EAAQN;AAE1C,UAAIA,WAAW;AACbA,kBAAU;UACRO,iBAAiB,KAAKA;UACtBC,kBAAkB,KAAKA;UACvBC,WAAW,KAAKA;UAChBC,cAAc,KAAKA;QACrB,CAAA;MACF;IACF,CAAA;AAEA,YAAIC,kBAAAA,SAAWV,eAAkB,GAAA;AAC/BA,sBAAgB;QACdW,eAAe,KAAKA;QACpBC,WAAW,KAAKA;QAChBC,aAAa,KAAKA;QAClBC,aAAa,KAAKA;QAClBR,iBAAiB,KAAKA;QACtBC,kBAAkB,KAAKA;QACvBC,WAAW,KAAKA;QAChBC,cAAc,KAAKA;MACrB,CAAA;IACF;EACF;EAoEA,MAAMM,SAASC,gBAA0B;AACvCf,WAAOC,KAAK,KAAKC,UAAU,EAAEC,QAAQ,CAACC,WAAAA;AACpC,WAAKF,WAAWE,MAAAA,EAAQU,SAAS,IAAI;IACvC,CAAA;AAEA,YAAIL,kBAAAA,SAAWM,cAAiB,GAAA;AAC9BA,qBAAe,IAAI;IACrB;EACF;EAEA,MAAMC,iBAAiB;AACrB,UAAMC,eAAe,MAAMC,QAAQC,IACjC,KAAKC,eAAeC,QAAQC,IAAI,OAAOC,WAAAA;AACrC,UAAI;AACF,cAAM,EAAEC,SAASC,KAAI,IAAK,MAAM,kCAAO,kBAAkBF,MAAAA,KAAW;AAEpE,eAAO;UAAEE;UAAMF;QAAO;MACxB,QAAQ;AACN,YAAI;AACF,gBAAM,EAAEC,SAASC,KAAI,IAAK,MAAM,kCAAO,kBAAkBF,MAAAA,OAAa;AACtE,iBAAO;YAAEE;YAAMF;UAAO;QACxB,QAAQ;AACN,iBAAO;YAAEE,MAAM;YAAMF;UAAO;QAC9B;MACF;IACF,CAAA,CAAA;AAGF,WAAON,aAAaS,OAAqD,CAACC,KAAKC,YAAAA;AAC7E,UAAIA,QAAQH,MAAM;AAChBE,YAAIC,QAAQL,MAAM,IAAIK,QAAQH;MAChC;AAEA,aAAOE;IACT,GAAG,CAAA,CAAC;EACN;;;;;EAMA,MAAME,UAAUC,qBAA6D,CAAA,GAAI;AAC/E,UAAMC,oBAAoB,MAAM,KAAKf,eAAc;AAEnD,UAAMgB,kBAAkBhC,OAAOC,KAAK,KAAKC,UAAU,EAChDoB,IAAI,CAAClB,WAAAA;AACJ,YAAM6B,gBAAgB,KAAK/B,WAAWE,MAAAA,EAAQ6B;AAE9C,UAAIA,eAAe;AACjB,eAAOA,cAAc;UAAEZ,SAAS,KAAKD,eAAeC;QAAQ,CAAA;MAC9D;AAEA,aAAO;KAERa,EAAAA,OAAO,CAACC,MAAMA,CAAAA;AAEjB,UAAMC,eAAgB,MAAMlB,QAAQC,IAAIa,eAAAA;AACxC,UAAMK,cAAcD,aAAaV,OAC/B,CAACC,KAAKW,uBAAAA;AACJ,YAAMC,cAAcD,mBAAmBZ,OACrC,CAACc,MAAMZ,YAAAA;AACLY,aAAKZ,QAAQL,MAAM,IAAIK,QAAQH;AAE/B,eAAOe;MACT,GACA,CAAA,CAAC;AAGHxC,aAAOC,KAAKsC,WAAapC,EAAAA,QAAQ,CAACoB,WAAAA;AAChCI,YAAIJ,MAAAA,IAAU;UAAE,GAAGI,IAAIJ,MAAO;UAAE,GAAGgB,YAAYhB,MAAO;QAAC;MACzD,CAAA;AAEA,aAAOI;IACT,GACA,CAAA,CAAC;AAGH,UAAMc,eAAe,KAAKrB,eAAeC,QAAQK,OAE9C,CAACC,KAAKC,YAAAA;AACPD,UAAIC,OAAAA,IAAW;QACb,GAAGG,kBAAkBH,OAAQ;QAC7B,GAAIS,YAAYT,OAAQ,KAAI,CAAA;QAC5B,GAAIE,mBAAmBF,OAAQ,KAAI,CAAA;MACrC;AAEA,aAAOD;IACT,GAAG,CAAA,CAAC;AAEJ,SAAKP,eAAeqB,eAAeA;AAEnC,WAAOvB,QAAQwB,QAAO;EACxB;EAyBAC,SAAS;AACP,UAAMC,kBAAcC,YAAAA,SAAKC,qBAAqB,KAAK1B,eAAeC,WAAW,CAAA,CAAE;AAC/E,UAAME,SAAUwB,aAAaC,QAAQC,0BACnC,KAAA;AAEF,SAAKC,QAAQC,mBACX;MACEC,WAAW;QACTC,iBAAaC,aAAAA,SAAM,CAAA,GAAIC,sBAAsBC,oBAAAA;QAC7CC,OAAO;UACLC,iBAAiB,CAAA;UACjBC,cAAeZ,aAAaC,QAAQY,uBAA4B,KAAA;QAClE;QACAC,UAAU;UACRtC,QAAQqB,YAAYrB,MAAO,IAAGA,SAAS;UACvCqB;QACF;QACAkB,OAAOC,eAAAA;MACT;IACF,GACA,KAAKC,aACL,KAAKC,QAAQ;AAGf,UAAMC,SAAS,KAAKA,OAAOC,aAAa,MAAM;MAC5CC,UAAUC,YAAAA;IACZ,CAAA;AAEA,eAAOC,0BAACC,gBAAAA;MAAeL;;EACzB;EAlVAM,YAAY,EAAEC,QAAQvE,WAAU,IAA+B,CAAA,GAAI;AAnCnEwE,SAAAA,UAAkC,CAAA;AAClCC,SAAAA,YAA2D,CAAA;SAE3DC,QAAQ;MACNC,gBAAgB,CAAA;IAClB;AAEApC,SAAAA,eAA4D,CAAA;SAE5DrB,iBAAiB;MACf0D,UAAUC;MACVC,MAAM;QAAEC,SAAS;MAAG;MACpB5D,SAAS;QAAC;MAAK;MACf6D,UAAUH;MACVI,eAAe;QAAEC,UAAU;MAAK;MAChCC,QAAQ;QAAEC,OAAOC;QAAYC,MAAMC;MAAU;MAC7ChD,cAAc,CAAA;MACdiD,WAAW;IACb;AAIC,SACDC,OAAO,IAAIC,KAAAA;SAEXC,UAAmB;MACjBC,YAAY,CAAA;MACZC,QAAQ,CAAA;IACV;AACA/B,SAAAA,cAA0D,CAAA;AAC1DC,SAAAA,WAA8B,CAAA;SAC9Bf,QAAsB;AACtB8C,SAAAA,eAAe,IAAIC,aAAAA;AACnBC,SAAAA,UAAU,IAAIC,QAAAA;AAedzF,SAAAA,gBAAgB,CAACoF,eAAAA;AACf,UAAIM,MAAMC,QAAQP,UAAa,GAAA;AAC7BA,mBAAWxE,IAAI,CAACgF,SAAAA;AACdC,gCAAAA,SAAUD,KAAKE,WAAW,8BAAA;AAC1BD,gCAAAA,SAAUD,KAAKG,MAAM,yBAAA;AAErB,eAAKZ,QAAQC,WAAWQ,KAAKG,IAAI,IAAIH,KAAKE;QAC5C,CAAA;aACK;AACLD,8BAAAA,SAAUT,WAAWU,WAAW,8BAAA;AAChCD,8BAAAA,SAAUT,WAAWW,MAAM,yBAAA;AAE3B,aAAKZ,QAAQC,WAAWA,WAAWW,IAAI,IAAIX,WAAWU;MACxD;IACF;AAEA7F,SAAAA,YAAY,CAACoF,WAAAA;AACX,UAAIK,MAAMC,QAAQN,MAAS,GAAA;AACzBA,eAAOzE,IAAI,CAACoF,UAAAA;AACVH,gCAAAA,SAAUG,MAAMF,WAAW,8BAAA;AAC3BD,gCAAAA,SAAUG,MAAMC,MAAM,yBAAA;AAEtB,eAAKd,QAAQE,OAAOW,MAAMC,IAAI,IAAID,MAAMF;QAC1C,CAAA;aACK;AACLD,8BAAAA,SAAUR,OAAOS,WAAW,8BAAA;AAC5BD,8BAAAA,SAAUR,OAAOY,MAAM,yBAAA;AAEvB,aAAKd,QAAQE,OAAOA,OAAOY,IAAI,IAAIZ,OAAOS;MAC5C;IACF;AAEAI,SAAAA,iBAAiB,CAAC5C,gBAAAA;AAChBA,kBAAY7D,QAAQ,CAAC0G,eAAAA;AACnB,aAAK7C,YAAY8C,KAAKD,UAAAA;MACxB,CAAA;IACF;AAEAE,SAAAA,oBAAoB,CAACC,MAAAA;AACnB,UAAIZ,MAAMC,QAAQW,CAAI,GAAA;AACpB,aAAKrB,KAAKsB,IAAID,CAAAA;aACT;AACL,aAAKrB,KAAKsB,IAAID,CAAAA;MAChB;IACF;AAEAnG,SAAAA,cAAc,CAACoD,aAAAA;AAKbjE,aAAOkH,QAAQjD,QAAAA,EAAU9D,QAAQ,CAAC,CAACsG,MAAMU,QAAQ,MAAA;AAC/C,aAAKlD,SAASwC,IAAAA,IAAQU;MACxB,CAAA;IACF;AAEAvG,SAAAA,cAAc,CAACwG,SACb,KAAKlD,OAAOtD,YAAYwG,IAAAA;AAM1B9G,SAAAA,mBAAmB,CAAC+G,WAAmBC,UAAAA;AACrCf,4BAAAA,SAAUH,MAAMC,QAAQiB,KAAQ,GAAA,yCAAA;AAEhC,WAAKpD,OAAO7D,gBAAgBgH,WAAWC,KAAAA;IACzC;AAMAC,SAAAA,uBAAuB,CACrBC,SACAF,UACG,KAAKpD,OAAO7D,gBAAgBmH,SAASF,KAAAA;AAE1CjH,SAAAA,kBAAkB,CAChBgH,WACAD,SAAAA;AAEA,WAAKlD,OAAO7D,gBAAgBgH,WAAWD,IAAAA;IACzC;AA8BAK,SAAAA,6BAA6B,CAACC,iBAAAA;;AAC5B,UAAIA,aAAarG,SAAS;AACxB,aAAKD,eAAeC,UAAU;UAC5B;UACIqG,KAAAA,kBAAarG,YAAbqG,mBAAsBxF,OAAO,CAACyF,QAAQA,QAAQ,UAAS,CAAA;QAC5D;MACH;AAEA,WAAID,kBAAaE,SAAbF,mBAAmBG,MAAM;AAC3B,aAAKzG,eAAe0D,WAAW4C,aAAaE,KAAKC;MACnD;AAEA,WAAIH,kBAAaI,SAAbJ,mBAAmBG,MAAM;AAC3B,aAAKzG,eAAe8D,WAAWwC,aAAaI,KAAKD;MACnD;AAEA,WAAIH,kBAAa1C,SAAb0C,mBAAmBzC,SAAS;AAC9B,aAAK7D,eAAe4D,KAAKC,UAAUyC,aAAa1C,KAAKC;MACvD;AAEA,UAAIyC,aAAajE,OAAO;AACtB,cAAMgC,aAAYiC,aAAajE,MAAM+B;AACrC,cAAMD,cAAamC,aAAajE,MAAM6B;AAEtC,YAAI,CAACG,cAAa,CAACF,aAAY;AAC7BwC,kBAAQC,KACN,kSAAkSC,KAAI,CAAA;AAExS3E,2BAAAA,SAAM,KAAKlC,eAAeiE,OAAOC,OAAOoC,aAAajE,KAAK;QAC5D;AAEA,YAAI8B,YAAYjC,kBAAAA,SAAM,KAAKlC,eAAeiE,OAAOC,OAAOC,WAAAA;AAExD,YAAIE,WAAWnC,kBAAAA,SAAM,KAAKlC,eAAeiE,OAAOG,MAAMC,UAAAA;MACxD;AAEA,YAAIiC,kBAAavC,kBAAbuC,mBAA4BtC,cAAa8C,QAAW;AACtD,aAAK9G,eAAe+D,cAAcC,WAAWsC,aAAavC,cAAcC;MAC1E;AAEA,UAAIsC,aAAahC,cAAcwC,QAAW;AACxC,aAAK9G,eAAesE,YAAYgC,aAAahC;MAC/C;IACF;AAEAyC,SAAAA,aAAa,CAAC1B,SAAAA;AACZ,WAAK9B,UAAU8B,IAAAA,IAAQ0B,WAAAA;IACzB;SAEAC,6BAA6B,CAC3BC,YACAC,eACAC,cAAAA;AAEA,UAAI;AAEF,eAAO,KAAK3D,MAAMC,eAAewD,UAAW,EAACC,aAAc,EAACC,SAAU,KAAI,CAAA;MAC5E,SAASC,KAAK;AACZT,gBAAQU,MAAM,iCAAiCD,GAAAA;AAE/C,eAAO,CAAA;MACT;IACF;AAEAjI,SAAAA,YAAY,CAACmI,aAAiC,KAAKhE,QAAQgE,QAAS;AAgGpElI,SAAAA,eAAe,CAACiG,MAAckC,OAAAA;AAC5BpC,4BAAAA,SACE,KAAK5B,UAAU8B,IAAAA,GACf,YAAYA,IAAK,4FAA2F;AAE9G,WAAK9B,UAAU8B,IAAK,EAAC3F,SAAS6H,EAAAA;IAChC;AAEAC,SAAAA,iBAAiB,CAACC,eAAAA;AAChB,YAAMzI,SAAS,IAAI0I,OAAOD,UAAAA;AAE1B,WAAKnE,QAAQtE,OAAOsI,QAAQ,IAAItI;IAClC;SAEA2I,gBAAgB,CAACtC,MAAcuC,eAAe,UAC5CA,eAAe,KAAKrE,UAAU8B,IAAAA,EAAMwC,eAAc,IAAK,KAAKtE,UAAU8B,IAAAA,EAAMyC,UAAS;SAEvFC,mBAAmB,CAAK1C,MAAc2C,cAAiBlG,UAAAA;AACrD,aAAO,KAAKyB,UAAU8B,IAAAA,EAAM4C,aAAaD,cAAclG,KAAAA;IACzD;SAEAoG,kBAAkB,CAAC7C,SAAiB,KAAK9B,UAAU8B,IAAAA,EAAM8C,YAAW;AAlTlE,SAAKrJ,aAAaA,cAAc,CAAA;AAEhC,SAAKuH,2BAA2BhD,UAAU,CAAA,CAAC;AAE3C,SAAK0D,WAAW3I,sBAAAA;AAChB,SAAK2I,WAAW1I,6BAAAA;AAChB,SAAK0I,WAAWxI,yBAAAA;AAChB,SAAKwI,WAAWzI,uBAAAA;AAEhB,SAAKwE,SAAS,IAAIsF,OAAOC,iBAAAA,CAAAA;EAC3B;AAwUF;;;AC9bMC,IAAAA,cAAc,OAClBC,WACA,EAAEC,SAASC,gBAAgBC,SAAQ,MAAmB;;AAEtD,MAAI,CAACH,WAAW;AACd,UAAM,IAAII,MAAM,yEAAA;EAClB;AAEAC,SAAOC,SAAS;;;;;;;IAOdC,YAAYC,kBAAkBC,QAAQC,IAAIC,wBAAwB;IAClEC,MAAM;IACNC,SAAS;IACTC,mBAAmBL,QAAQC,IAAIK,8BAA8B;IAC7DC,QAAQ;MACNC,WAAW,CAACC,SAAAA;;AACV,iBAAOf,MAAAA,qCAAUa,WAAVb,gBAAAA,IAAmBe,WAAU;MACtC;IACF;;IAEAf,UAAU;MACRgB,KAAK;MACLC,YAAY;MACZC,kBAAkB;;;;;MAKlBJ,WAAW,MAAM;IACnB;IACAK,aAAa;IACbC,OAAO;MACLC,KAAK;MACLC,WAAW;IACb;EACF;AAEA,QAAM,EAAEC,IAAG,IAAKC,eAAAA;AAUhB,MAAI;AACF,UAAM,EACJC,MAAM,EACJA,MAAM,EAAEhB,MAAMC,SAASV,UAAAA,WAAUoB,MAAK,EAAE,EACzC,IACC,MAAMG,IAA2B,qBAAA;AAErCrB,WAAOC,OAAOM,OAAOA;AACrBP,WAAOC,OAAOuB,iBAAiBhB;AAC/BR,WAAOC,OAAOiB,QAAQA;AACtBlB,WAAOC,OAAOH,WAAW;MACvB,GAAGE,OAAOC,OAAOH;MACjBc,WAAW,CAACa,gBAAgB3B,UAAS4B,KAAK,CAACC,YAAYA,QAAQd,SAASY,WAAAA;IAC1E;AACAzB,WAAOC,OAAOgB,cAAcV,OAAO,eAAe;EACpD,SAASqB,KAAK;AAKZC,YAAQC,MAAMF,GAAAA;EAChB;AAEA,QAAMG,MAAM,IAAIC,UAAU;IACxBC,QAAQpC,iDAAgBoC;IACxBC,YAAYtC;EACd,CAAA;AAEA,QAAMmC,IAAII,SAAStC,iDAAgBsC,QAAAA;AACnC,QAAMJ,IAAIK,UAAUvC,iDAAgBuC,SAAAA;AACpC,QAAML,IAAIM,WAAUxC,sDAAgBoC,WAAhBpC,mBAAwByC,YAAAA;AAE5CC,gCAAW5C,SAAW6C,EAAAA,OAAOT,IAAIS,OAAM,CAAA;AAEvC,MACE,OAAOC,WAAW,eAClBA,UACA,SAASA,UACT,OAAOA,OAAOC,QAAQ,YACtBD,OAAOC,QAAQ,QACf,YAAYD,OAAOC,OACnB,OAAOD,OAAOC,IAAIC,WAAW,YAC7B;AACAF,WAAOC,IAAIC,OAAM;EACnB;AAEA,MAAI,SAAO,iBAAYD,QAAZ,mBAAiBC,YAAW,YAAY;AACjD,gBAAYD,IAAIC,OAAM;EACxB;AACF;;;;;;;;;;;;ACtHA,IAAMC,eAAe,MAAA;AACnB,QAAMC,YAAkBC,cAAO,KAAA;AAE/BC,EAAMC,uBAAgB,MAAA;AACpBH,cAAUI,UAAU;AAEpB,WAAO,MAAA;AACLJ,gBAAUI,UAAU;IACtB;EACF,GAAG,CAAA,CAAE;AAEL,SAAOJ;AACT;;;ACNC,IACKK,iBAAiB,MAAA;AACrB,QAAM,CAACC,MAAMC,MAAO,IAASC,gBAAQ;AACrC,QAAMC,YAAYC,aAAAA;AAElB,QAAMC,cAAoBC,mBAAY,MAAA;AACpC,QAAIH,UAAUI,SAAS;AACrBN,aAAOO,KAAKC,OAAM,CAAA;IACpB;KACC;IAACN;IAAWF;EAAO,CAAA;AAEtB,SAAO;IAACD;IAAMK;EAAY;AAC5B;;;;;ACGMK,IAAAA,uBAAuB,CAC3BC,UACAC,MACAC,YAAAA;AAEA,QAAMC,wBAAoBC,uBACxB,UAAMC,gBAAAA,SAASL,UAAUC,MAAMC,OAC/B,GAAA;IAACF;IAAUE;IAASD;EAAK,CAAA;AAG3B,SAAOE;AACT;;;AC/BA,IAAMG,0BAAyD,CAACC,aAAAA;AAC9D,QAAMC,QAAQC,KAAKC,IAAG;AAEtB,SAAOC,WAAW,MAAA;AAChBJ,aAAS;MACPK,YAAY;MACZC,gBAAAA;AACE,eAAOC,KAAKC,IAAI,GAAGN,KAAKC,IAAG,IAAKF,KAAAA;MAClC;IACF,CAAA;KACC,CAAA;AACL;AAEA,IAAMQ,uBACJ,OAAOC,wBAAwB,cAAcX,0BAA0BW;AAKzE,IAAMC,yBAAuD,CAACC,WAAAA;AAC5D,SAAOC,aAAaD,MAAAA;AACtB;AAEA,IAAME,sBACJ,OAAOC,uBAAuB,cAAcJ,yBAAyBI;;;ACavE,IAAMC,+BAA+B,CAAqB,EACxDC,UACAC,OACAC,aAAY,MAC0C;AACtD,QAAMC,YAAkBC,cAAgE,CAAA,CAAC;AACzF,QAAM,CAACC,MAAMC,WAAAA,IAAeC,eAAAA;AAE5B,QAAMC,gBAAsBJ,cAAsB,IAAA;AAClD,QAAMK,gBAAsBC,mBAAY,MAAA;AACtC,QAAIF,cAAcG,SAAS;AACzBC,0BAAmBJ,cAAcG,OAAO;IAC1C;AAEAH,kBAAcG,UAAUE,qBAAoB,MAAA;AAC1CL,oBAAcG,UAAU;AAExBL,kBAAAA;IACF,CAAA;KACC;IAACA;EAAY,CAAA;AAKhB,QAAMQ,yBAAyBC,qBAAqBN,eAAe,IAAI;IAAEO,UAAU;EAAK,CAAA;AAExF,QAAMC,SAAeP,mBACnB,CAACQ,IAAIC,gBAAAA;AACH,QAAIA,gBAAgB,MAAM;AACxB,aAAOhB,UAAUQ,QAAQO,EAAG;WACvB;AACL,YAAMP,UAAUR,UAAUQ,QAAQO,EAAG;AACrCf,gBAAUQ,QAAQO,EAAAA,IAAM;QAAE,GAAGP;QAASS,OAAO;UAAE,GAAGD;UAAaD;QAAG;MAAE;IACtE;AAEAJ,2BAAAA;KAEF;IAACA;EAAuB,CAAA;AAG1B,QAAMO,OAAYC,eAChB,MAAMpB,aAAaqB,IAAI,CAACJ,gBAAgBK,UAAUL,WAClD,CAAA,GAAA;IAACjB;EAAa,CAAA;AAGhB,QAAMuB,SAAeH;IACnB,MACED,KACGE,IAAI,CAACL,OAAOf;;AAAAA,6BAAUQ,QAAQO,EAAAA,MAAlBf,mBAAuBiB;KAAAA,EACnCM,OAAO,CAACC,UAAUA,UAAU,QAAQA,UAAUC,MACnD;;;;;;;IAMA;MAACP;MAAKhB;IAAK;EAAA;AAGb,aACEwB,2BAAAC,+BAAA;;MACG5B,aAAaqB,IAAI,CAACJ,gBAAAA;AACjB,cAAMY,MAAMP,UAAUL,WAAAA;AACtB,mBACEa,0BAACC,aAAAA;UAAsBf,IAAIa;UAAKZ;UAA0BlB;UAAcgB;QAAtDc,GAAAA,GAAAA;MAEtB,CAAA;MACC/B,SAASyB,MAAAA;;;AAGhB;AAmBA,IAAMQ,cAAoBC,YACxB,CAAC,EAAEf,aAAaD,IAAIjB,OAAOgB,OAAM,MAA8B;AAC7D,QAAMkB,OAAOhB,YAAYlB,KAAAA;AAEzBmC,0BAAwB,MAAA;AACtBnB,WAAOC,IAAIiB,IAAAA;AAEX,WAAO,MAAA;AACLlB,aAAOC,IAAI,IAAA;IACb;KACCiB,IAAAA;AAEH,SAAO;AACT,GACA,CAACE,MAAMC,aAASC,eAAAA,SAAQF,KAAKpC,OAAOqC,KAAKrC,KAAK,CAAA;AAOhD,IAAMoB,MAAM,oBAAImB,QAAAA;AAEhB,IAAIC,UAAU;AAEd,SAASjB,UAAgBW,MAAgC;AACvD,QAAMO,WAAWrB,IAAIsB,IAAIR,IAAAA;AAEzB,MAAIO,SAAU,QAAOA;AAErB,QAAMxB,KAAK,GAAGiB,KAAKS,QAAST,KAAaU,eAAe,aAAA,IAAiBJ,SAAAA;AAEzEpB,MAAIyB,IAAIX,MAAMjB,EAAAA;AAEd,SAAOA;AACT;AAEA,IAAM6B,2BAA2B,CAAK3B,UAAAA;AACpC,QAAM4B,MAAY5C,cAAsBwB,MAAAA;AAExC,MAAI,KAACW,eAAAA,SAAQnB,OAAO4B,IAAIrC,OAAO,GAAG;AAChCqC,QAAIrC,UAAUS;EAChB;AAEA,SAAO;IAAC4B,IAAIrC;EAAQ;AACtB;AAEA,IAAMyB,0BAA0B,CAACa,UAAgCC,iBAAAA;AAE/DC,EAAMC,iBAAUH,UAAUF,yBAAyBG,YAAAA,CAAAA;AACrD;;;;ACjKO,SAASG,iBAAiBC,WAAmBC,UAAgB;AAClE,QAAMC,QAAQC,cAAAA;AAEdC,+BAAU,MAAA;AACRF,UAAMG,cAAcL,WAAWC,QAAAA;KAC9B;IAACC;IAAOF;IAAWC;EAAQ,CAAA;AAChC;", "names": ["module", "invariant", "module", "module", "module", "defaultsDeep", "module", "module", "module", "throttle", "ALLOWED_TYPES", "ALLOWED_ROOT_LEVEL_OPTIONS", "CustomFields", "constructor", "register", "customFields", "Array", "isArray", "for<PERSON>ach", "customField", "name", "pluginId", "type", "intlLabel", "intlDescription", "components", "options", "invariant", "Input", "includes", "isValidObjectKey", "test", "allFormOptions", "base", "advanced", "length", "optionPathValidations", "reduce", "optionsValidationReducer", "isValidOptionPath", "errorMessage", "uid", "uidAlreadyUsed", "Object", "prototype", "hasOwnProperty", "call", "getAll", "get", "acc", "option", "items", "push", "startsWith", "Plugin", "getInjectedComponents", "containerName", "blockName", "injectionZones", "err", "console", "error", "injectComponent", "component", "push", "constructor", "pluginConf", "immerable", "apis", "initializer", "isReady", "undefined", "name", "pluginId", "id", "RBAC", "use", "middleware", "Array", "isArray", "middlewares", "push", "constructor", "run", "ctx", "permissions", "index", "middlewaresToRun", "map", "next", "length", "LanguageProvider", "children", "messages", "locale", "useTypedSelector", "state", "admin_app", "language", "appMessages", "defaultsDeep", "en", "_jsx", "IntlProvider", "defaultLocale", "textComponent", "Theme", "children", "themes", "currentTheme", "useTypedSelector", "state", "admin_app", "theme", "systemTheme", "setSystemTheme", "useState", "locale", "useIntl", "dispatch", "useDispatch", "React", "useEffect", "themeWatcher", "window", "matchMedia", "matches", "listener", "event", "addEventListener", "removeEventListener", "setAvailableThemes", "Object", "keys", "computedThemeName", "_jsxs", "DesignSystemProvider", "_jsx", "GlobalStyle", "createGlobalStyle", "colors", "neutral100", "queryClient", "QueryClient", "defaultOptions", "queries", "refetchOnWindowFocus", "Providers", "children", "strapi", "store", "_jsx", "StrapiAppProvider", "components", "library", "customFields", "widgets", "fields", "menu", "router", "getAdminInjectedComponents", "getPlugin", "plugins", "rbac", "runHookParallel", "runHookWaterfall", "name", "initialValue", "runHookSeries", "settings", "Provider", "QueryClientProvider", "client", "<PERSON>th<PERSON><PERSON><PERSON>", "HistoryProvider", "LanguageProvider", "messages", "configurations", "translations", "Theme", "themes", "NotificationsProvider", "TrackingProvider", "GuidedTourProvider", "ConfigurationProvider", "defaultAuthLogo", "auth<PERSON><PERSON>", "defaultMenuLogo", "menuLogo", "showReleaseNotification", "notifications", "releases", "App", "strapi", "store", "useEffect", "language", "localStorage", "getItem", "LANGUAGE_LOCAL_STORAGE_KEY", "document", "documentElement", "lang", "_jsx", "Providers", "Suspense", "fallback", "Page", "Loading", "Outlet", "Error<PERSON><PERSON>", "error", "useRouteError", "formatMessage", "useIntl", "copy", "useClipboard", "Error", "console", "handleClick", "stack", "_jsx", "Main", "height", "Flex", "alignItems", "justifyContent", "_jsxs", "gap", "padding", "direction", "width", "shadow", "borderColor", "background", "hasRadius", "max<PERSON><PERSON><PERSON>", "WarningCircle", "fill", "Typography", "fontSize", "fontWeight", "textAlign", "id", "defaultMessage", "variant", "link", "Link", "isExternal", "endIcon", "href", "<PERSON>d<PERSON><PERSON><PERSON>", "onClose", "<PERSON><PERSON><PERSON><PERSON>", "ErrorType", "message", "<PERSON><PERSON>", "onClick", "startIcon", "Duplicate", "styled", "<PERSON><PERSON>", "theme", "colors", "danger600", "NotFoundPage", "formatMessage", "useIntl", "_jsxs", "Page", "Main", "labelledBy", "_jsx", "Layouts", "Header", "id", "title", "defaultMessage", "Content", "EmptyStateLayout", "action", "LinkButton", "tag", "Link", "variant", "endIcon", "ArrowRight", "to", "content", "hasRadius", "icon", "EmptyPictures", "width", "shadow", "getEERoutes", "window", "strapi", "features", "isEnabled", "AUDIT_LOGS", "path", "lazy", "ProtectedListPage", "Component", "SSO", "ProtectedSSO", "ForgotPassword", "navigate", "useNavigate", "formatMessage", "useIntl", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "forgotPassword", "error", "useForgotPasswordMutation", "_jsx", "UnauthenticatedLayout", "_jsxs", "Main", "LayoutContent", "Column", "Logo", "Box", "paddingTop", "paddingBottom", "Typography", "tag", "variant", "id", "defaultMessage", "role", "tabIndex", "textColor", "isBaseQueryError", "Form", "method", "initialValues", "email", "onSubmit", "body", "res", "validationSchema", "object", "shape", "string", "translatedErrors", "required", "nullable", "Flex", "direction", "alignItems", "gap", "label", "name", "placeholder", "type", "map", "field", "InputR<PERSON><PERSON>", "<PERSON><PERSON>", "fullWidth", "justifyContent", "Link", "NavLink", "to", "ForgotPasswordSuccess", "formatMessage", "useIntl", "_jsx", "UnauthenticatedLayout", "_jsxs", "Main", "LayoutContent", "Column", "Logo", "Box", "paddingTop", "paddingBottom", "Typography", "tag", "variant", "id", "defaultMessage", "Flex", "justifyContent", "Link", "NavLink", "to", "Oops", "formatMessage", "useIntl", "search", "searchString", "useLocation", "query", "useMemo", "URLSearchParams", "message", "get", "id", "defaultMessage", "_jsx", "UnauthenticatedLayout", "_jsxs", "Main", "LayoutContent", "Column", "Logo", "Box", "paddingTop", "paddingBottom", "Typography", "tag", "variant", "Flex", "justifyContent", "Link", "NavLink", "to", "REGISTER_USER_SCHEMA", "object", "shape", "firstname", "string", "trim", "required", "translatedErrors", "nullable", "lastname", "password", "min", "id", "<PERSON><PERSON><PERSON><PERSON>", "defaultMessage", "values", "test", "value", "byteSize", "getByteSize", "matches", "message", "confirmPassword", "oneOf", "ref", "registrationToken", "REGISTER_ADMIN_SCHEMA", "TextEncoder", "encode", "length", "email", "strict", "lowercase", "Register", "has<PERSON>dmin", "toggleNotification", "useNotification", "navigate", "useNavigate", "submitCount", "setSubmitCount", "useState", "apiError", "setApiError", "trackUsage", "useTracking", "formatMessage", "useIntl", "setSkipped", "useGuidedTour", "state", "search", "searchString", "useLocation", "query", "useMemo", "URLSearchParams", "match", "useMatch", "_unstableFormatAPIError", "formatAPIError", "_unstableFormatValidationErrors", "formatValidationErrors", "useAPIErrorHandler", "setNpsSurveySettings", "useNpsSurveySettings", "get", "data", "userInfo", "error", "useGetRegistrationInfoQuery", "skip", "React", "useEffect", "isBaseQueryError", "type", "encodeURIComponent", "registerAdmin", "useRegisterAdminMutation", "registerUser", "useRegisterUserMutation", "dispatch", "useTypedDispatch", "handleRegisterAdmin", "news", "body", "setFormErrors", "res", "login", "token", "roles", "user", "isUserSuperAdmin", "find", "code", "localStorage", "setItem", "JSON", "stringify", "s", "enabled", "pathname", "name", "handleRegisterUser", "params", "authType", "_jsx", "Navigate", "to", "isAdminRegistration", "schema", "UnauthenticatedLayout", "_jsxs", "LayoutContent", "Flex", "direction", "alignItems", "gap", "Logo", "Typography", "tag", "variant", "textAlign", "textColor", "role", "tabIndex", "Form", "method", "initialValues", "undefined", "onSubmit", "helpers", "normalizedData", "normalizeData", "validate", "abort<PERSON><PERSON><PERSON>", "count", "toString", "omit", "setErrors", "err", "ValidationError", "inner", "reduce", "acc", "path", "marginTop", "Grid", "Root", "label", "size", "disabled", "hint", "terms", "A", "target", "href", "rel", "policy", "map", "field", "<PERSON><PERSON>", "col", "InputR<PERSON><PERSON>", "<PERSON><PERSON>", "fullWidth", "Box", "paddingTop", "justifyContent", "Link", "NavLink", "Object", "entries", "key", "includes", "styled", "a", "theme", "colors", "primary600", "RESET_PASSWORD_SCHEMA", "object", "shape", "password", "string", "min", "id", "translatedErrors", "<PERSON><PERSON><PERSON><PERSON>", "defaultMessage", "values", "test", "value", "byteSize", "getByteSize", "matches", "message", "required", "nullable", "confirmPassword", "oneOf", "ref", "ResetPassword", "formatMessage", "useIntl", "dispatch", "useTypedDispatch", "navigate", "useNavigate", "search", "searchString", "useLocation", "query", "useMemo", "URLSearchParams", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "resetPassword", "error", "useResetPasswordMutation", "handleSubmit", "body", "res", "login", "token", "data", "get", "_jsx", "Navigate", "to", "UnauthenticatedLayout", "_jsxs", "Main", "LayoutContent", "Column", "Logo", "Box", "paddingTop", "paddingBottom", "Typography", "tag", "variant", "role", "tabIndex", "textColor", "isBaseQueryError", "Form", "method", "initialValues", "onSubmit", "resetPasswordToken", "validationSchema", "Flex", "direction", "alignItems", "gap", "hint", "label", "name", "type", "map", "field", "InputR<PERSON><PERSON>", "<PERSON><PERSON>", "fullWidth", "justifyContent", "Link", "NavLink", "FORMS", "ForgotPassword", "ForgotPasswordSuccess", "login", "oops", "Oops", "register", "Register", "ResetPassword", "providers", "AuthPage", "search", "useLocation", "match", "useMatch", "authType", "params", "data", "useInitQuery", "has<PERSON>dmin", "<PERSON><PERSON>", "useEnterprise", "LoginCE", "LoginEE", "forms", "FORMS", "combine", "ceForms", "eeForms", "defaultValue", "token", "useAuth", "auth", "_jsx", "Navigate", "to", "Component", "pathname", "ROUTES_CE", "lazy", "ProtectedListPage", "Component", "path", "ProtectedCreatePage", "ProtectedEditPage", "editWebhook", "ProtectedListView", "ProtectedCreateView", "ProtectedEditView", "ProtectedInstalledPlugins", "PurchaseAuditLogs", "PurchaseSingleSignOn", "PurchaseContentHistory", "getImmutableRoutes", "path", "lazy", "PrivateUseCasePage", "Component", "getBaseEERoutes", "element", "_jsx", "AuthPage", "getInitialRoutes", "index", "HomePage", "ProfilePage", "ProtectedMarketplacePage", "Layout", "children", "ApplicationInfoPage", "getSettingsEERoutes", "ROUTES_CE", "filter", "route", "refArray", "findIndex", "obj", "Router", "routes", "_routes", "menu", "_menu", "settings", "_settings", "createRouter", "strapi", "memory", "opts", "path", "errorElement", "_jsx", "Provider", "store", "LanguageProvider", "messages", "configurations", "translations", "Theme", "themes", "Error<PERSON><PERSON>", "element", "App", "children", "getImmutableRoutes", "lazy", "PrivateAdminLayout", "Component", "NotFoundPage", "router", "createMemoryRouter", "createBrowserRouter", "addSettingsLink", "section", "link", "invariant", "id", "intlLabel", "defaultMessage", "undefined", "Array", "isArray", "links", "for<PERSON>ach", "createSettingsLink", "l", "Error", "addRoute", "route", "push", "getPrintableType", "constructor", "initialRoutes", "global", "addMenuLink", "to", "Symbol", "toStringTag", "console", "warn", "trim", "startsWith", "slice", "restLink", "mod", "default", "sectionId", "split", "join", "settingsIndex", "findIndex", "value", "nativeType", "Object", "name", "Widgets", "constructor", "register", "widget", "Array", "isArray", "for<PERSON>ach", "newWidget", "invariant", "id", "component", "title", "icon", "pluginId", "widgetToStore", "uid", "widgets", "getAll", "Object", "values", "staticReducers", "adminApi", "reducerPath", "reducer", "admin_app", "appReducer", "injectReducerStoreEnhancer", "appReducers", "next", "args", "store", "asyncReducers", "injectReducer", "key", "asyncReducer", "replaceReducer", "combineReducers", "configureStoreImpl", "preloadedState", "appMiddlewares", "injectedReducers", "coreReducers", "defaultMiddlewareOptions", "process", "serializableCheck", "immutableCheck", "configureStore", "devTools", "middleware", "getDefaultMiddleware", "rtkQueryUnauthorizedMiddleware", "map", "m", "enhancers", "dispatch", "action", "isRejected", "payload", "status", "logout", "window", "location", "href", "createHook", "_handlers", "register", "fn", "push", "delete", "handler", "splice", "indexOf", "runWaterfall", "args", "store", "reduce", "acc", "runWaterfallAsync", "result", "runSeries", "map", "runSeriesAsync", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Promise", "all", "languageNativeNames", "ar", "ca", "cs", "de", "dk", "en", "es", "eu", "uz", "ro", "fr", "gu", "he", "hu", "id", "it", "ja", "ko", "ml", "ms", "nl", "no", "pl", "pt", "ru", "sk", "sv", "th", "tr", "uk", "vi", "zh", "sa", "hi", "INJECT_COLUMN_IN_TABLE", "MUTATE_COLLECTION_TYPES_LINKS", "MUTATE_EDIT_VIEW_LAYOUT", "MUTATE_SINGLE_TYPES_LINKS", "HOOKS", "StrapiApp", "bootstrap", "customBootstrap", "Object", "keys", "appPlugins", "for<PERSON>ach", "plugin", "addSettingsLink", "addSettingsLinks", "getPlugin", "registerHook", "isFunction", "addComponents", "addFields", "addMenuLink", "addReducers", "register", "customRegister", "loadAdminTrads", "adminLocales", "Promise", "all", "configurations", "locales", "map", "locale", "default", "data", "reduce", "acc", "current", "loadTrads", "customTranslations", "adminTranslations", "arrayOfPromises", "registerTrads", "filter", "a", "pluginsTrads", "mergedTrads", "currentPluginTrads", "pluginTrads", "acc1", "translations", "resolve", "render", "localeNames", "pick", "languageNativeNames", "localStorage", "getItem", "LANGUAGE_LOCAL_STORAGE_KEY", "store", "configureStore", "admin_app", "permissions", "merge", "ADMIN_PERMISSIONS_CE", "ADMIN_PERMISSIONS_EE", "theme", "availableThemes", "currentTheme", "THEME_LOCAL_STORAGE_KEY", "language", "token", "getStoredToken", "middlewares", "reducers", "router", "createRouter", "basename", "getBasename", "_jsx", "RouterProvider", "constructor", "config", "plugins", "hooksDict", "admin", "injectionZones", "auth<PERSON><PERSON>", "Logo", "head", "favicon", "menuLogo", "notifications", "releases", "themes", "light", "lightTheme", "dark", "darkTheme", "tutorials", "rbac", "RBAC", "library", "components", "fields", "customFields", "CustomFields", "widgets", "Widgets", "Array", "isArray", "comp", "invariant", "Component", "name", "field", "type", "addMiddlewares", "middleware", "push", "addRBACMiddleware", "m", "use", "entries", "reducer", "link", "sectionId", "links", "createSettingSection", "section", "createCustomConfigurations", "customConfig", "loc", "auth", "logo", "menu", "console", "warn", "trim", "undefined", "createHook", "getAdminInjectedComponents", "moduleName", "containerName", "blockName", "err", "error", "pluginId", "fn", "registerPlugin", "pluginConf", "Plugin", "runHookSeries", "asynchronous", "runSeriesAsync", "runSeries", "runHookWaterfall", "initialValue", "runWaterfall", "runHookParallel", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "getInitialRoutes", "renderAdmin", "mountNode", "plugins", "customisations", "features", "Error", "window", "strapi", "backendURL", "createAbsoluteUrl", "process", "env", "STRAPI_ADMIN_BACKEND_URL", "isEE", "isTrial", "telemetryDisabled", "STRAPI_TELEMETRY_DISABLED", "future", "isEnabled", "name", "SSO", "AUDIT_LOGS", "REVIEW_WORKFLOWS", "projectType", "flags", "nps", "promoteEE", "get", "getFetchClient", "data", "isTrialLicense", "featureName", "some", "feature", "err", "console", "error", "app", "StrapiApp", "config", "appPlugins", "register", "bootstrap", "loadTrads", "translations", "createRoot", "render", "module", "hot", "accept", "useIsMounted", "isMounted", "useRef", "React", "useLayoutEffect", "current", "useForceUpdate", "tick", "update", "useState", "isMounted", "useIsMounted", "forceUpdate", "useCallback", "current", "Math", "random", "useThrottledCallback", "callback", "wait", "options", "throttled<PERSON><PERSON><PERSON>", "useMemo", "throttle", "requestIdleCallbackShim", "callback", "start", "Date", "now", "setTimeout", "didTimeout", "timeRemaining", "Math", "max", "_requestIdleCallback", "requestIdleCallback", "cancelIdleCallbackShim", "handle", "clearTimeout", "_cancelIdleCallback", "cancelIdleCallback", "DescriptionComponent<PERSON><PERSON><PERSON>", "children", "props", "descriptions", "statesRef", "useRef", "tick", "forceUpdate", "useForceUpdate", "requestHandle", "requestUpdate", "useCallback", "current", "cancelIdleCallback", "requestIdleCallback", "throttledRequestUpdate", "useThrottledCallback", "trailing", "update", "id", "description", "value", "ids", "useMemo", "map", "getCompId", "states", "filter", "state", "undefined", "_jsxs", "_Fragment", "key", "_jsx", "Description", "memo", "comp", "useShallowCompareEffect", "prev", "next", "isEqual", "WeakMap", "counter", "cachedId", "get", "name", "displayName", "set", "useShallowCompareMemoize", "ref", "callback", "dependencies", "React", "useEffect", "useInjectReducer", "namespace", "reducer", "store", "useTypedStore", "useEffect", "injectReducer"]}