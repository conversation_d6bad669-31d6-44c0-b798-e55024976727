{"version": 3, "sources": ["../../../@strapi/admin/admin/src/components/WidgetHelpers.tsx"], "sourcesContent": ["import { Flex, Loader, Typography } from '@strapi/design-system';\nimport { WarningCircle } from '@strapi/icons';\nimport { EmptyDocuments, EmptyPermissions } from '@strapi/icons/symbols';\nimport { useIntl } from 'react-intl';\n\n/* -------------------------------------------------------------------------------------------------\n * Loading\n * -----------------------------------------------------------------------------------------------*/\n\ninterface LoadingProps {\n  children?: string;\n}\n\nconst Loading = ({ children }: LoadingProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Flex height=\"100%\" justifyContent=\"center\" alignItems=\"center\">\n      <Loader>\n        {children ??\n          formatMessage({\n            id: 'HomePage.widget.loading',\n            defaultMessage: 'Loading widget content',\n          })}\n      </Loader>\n    </Flex>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Error\n * -----------------------------------------------------------------------------------------------*/\n\ninterface ErrorProps {\n  children?: string;\n}\n\nconst Error = ({ children }: ErrorProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Flex height=\"100%\" direction=\"column\" justifyContent=\"center\" alignItems=\"center\" gap={2}>\n      <WarningCircle width=\"3.2rem\" height=\"3.2rem\" fill=\"danger600\" />\n      <Typography variant=\"delta\">\n        {formatMessage({\n          id: 'global.error',\n          defaultMessage: 'Something went wrong',\n        })}\n      </Typography>\n      <Typography textColor=\"neutral600\">\n        {children ??\n          formatMessage({\n            id: 'HomePage.widget.error',\n            defaultMessage: \"Couldn't load widget content.\",\n          })}\n      </Typography>\n    </Flex>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * NoData\n * -----------------------------------------------------------------------------------------------*/\n\ninterface NoDataProps {\n  children?: string;\n}\n\nconst NoData = ({ children }: NoDataProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Flex height=\"100%\" direction=\"column\" justifyContent=\"center\" alignItems=\"center\" gap={6}>\n      <EmptyDocuments width=\"16rem\" height=\"8.8rem\" />\n      <Typography textColor=\"neutral600\">\n        {children ??\n          formatMessage({\n            id: 'HomePage.widget.no-data',\n            defaultMessage: 'No content found.',\n          })}\n      </Typography>\n    </Flex>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * NoPermissions\n * -----------------------------------------------------------------------------------------------*/\n\ninterface NoPermissionsProps {\n  children?: string;\n}\n\nconst NoPermissions = ({ children }: NoPermissionsProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Flex height=\"100%\" direction=\"column\" justifyContent=\"center\" alignItems=\"center\" gap={6}>\n      <EmptyPermissions width=\"16rem\" height=\"8.8rem\" />\n      <Typography textColor=\"neutral600\">\n        {children ??\n          formatMessage({\n            id: 'HomePage.widget.no-permissions',\n            defaultMessage: 'You don’t have the permission to see this widget',\n          })}\n      </Typography>\n    </Flex>\n  );\n};\n\nconst Widget = {\n  Loading,\n  Error,\n  NoData,\n  NoPermissions,\n};\n\nexport { Widget };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAaA,IAAMA,UAAU,CAAC,EAAEC,SAAQ,MAAgB;AACzC,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEC,wBAACC,MAAAA;IAAKC,QAAO;IAAOC,gBAAe;IAASC,YAAW;IACrD,cAAAJ,wBAACK,QAAAA;MACER,UAAAA,YACCC,cAAc;QACZQ,IAAI;QACJC,gBAAgB;MAClB,CAAA;;;AAIV;AAUA,IAAMC,QAAQ,CAAC,EAAEX,SAAQ,MAAc;AACrC,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEU,yBAACR,MAAAA;IAAKC,QAAO;IAAOQ,WAAU;IAASP,gBAAe;IAASC,YAAW;IAASO,KAAK;;UACtFX,wBAACY,cAAAA;QAAcC,OAAM;QAASX,QAAO;QAASY,MAAK;;UACnDd,wBAACe,YAAAA;QAAWC,SAAQ;kBACjBlB,cAAc;UACbQ,IAAI;UACJC,gBAAgB;QAClB,CAAA;;UAEFP,wBAACe,YAAAA;QAAWE,WAAU;QACnBpB,UAAAA,YACCC,cAAc;UACZQ,IAAI;UACJC,gBAAgB;QAClB,CAAA;;;;AAIV;AAUA,IAAMW,SAAS,CAAC,EAAErB,SAAQ,MAAe;AACvC,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEU,yBAACR,MAAAA;IAAKC,QAAO;IAAOQ,WAAU;IAASP,gBAAe;IAASC,YAAW;IAASO,KAAK;;UACtFX,wBAACmB,cAAAA;QAAeN,OAAM;QAAQX,QAAO;;UACrCF,wBAACe,YAAAA;QAAWE,WAAU;QACnBpB,UAAAA,YACCC,cAAc;UACZQ,IAAI;UACJC,gBAAgB;QAClB,CAAA;;;;AAIV;AAUA,IAAMa,gBAAgB,CAAC,EAAEvB,SAAQ,MAAsB;AACrD,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEU,yBAACR,MAAAA;IAAKC,QAAO;IAAOQ,WAAU;IAASP,gBAAe;IAASC,YAAW;IAASO,KAAK;;UACtFX,wBAACqB,cAAAA;QAAiBR,OAAM;QAAQX,QAAO;;UACvCF,wBAACe,YAAAA;QAAWE,WAAU;QACnBpB,UAAAA,YACCC,cAAc;UACZQ,IAAI;UACJC,gBAAgB;QAClB,CAAA;;;;AAIV;AAEA,IAAMe,SAAS;EACb1B;EACAY;EACAU;EACAE;AACF;", "names": ["Loading", "children", "formatMessage", "useIntl", "_jsx", "Flex", "height", "justifyContent", "alignItems", "Loader", "id", "defaultMessage", "Error", "_jsxs", "direction", "gap", "WarningCircle", "width", "fill", "Typography", "variant", "textColor", "NoData", "EmptyDocuments", "NoPermissions", "EmptyPermissions", "Widget"]}