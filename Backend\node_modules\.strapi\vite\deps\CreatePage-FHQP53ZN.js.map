{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/Webhooks/CreatePage.tsx"], "sourcesContent": ["import { Page } from '../../../../components/PageHelpers';\nimport { useTypedSelector } from '../../../../core/store/hooks';\n\nimport { EditPage } from './EditPage';\n\nconst ProtectedCreatePage = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.webhooks.create\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <EditPage />\n    </Page.Protect>\n  );\n};\n\nexport { ProtectedCreatePage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,IAAMA,sBAAsB,MAAA;AAC1B,QAAMC,cAAcC,iBAClB,CAACC,UAAUA;;AAAAA,uBAAMC,UAAUH,YAAYI,aAA5BF,mBAAsCG,SAASC;GAAAA;AAG5D,aACEC,wBAACC,KAAKC,SAAO;IAACT;IACZ,cAAAO,wBAACG,UAAAA,CAAAA,CAAAA;;AAGP;", "names": ["ProtectedCreatePage", "permissions", "useTypedSelector", "state", "admin_app", "settings", "webhooks", "create", "_jsx", "Page", "Protect", "EditPage"]}