{"version": 3, "sources": ["../../../@strapi/admin/admin/src/hooks/useClipboard.ts", "../../../@strapi/admin/admin/src/components/ContentBox.tsx"], "sourcesContent": ["import { useCallback } from 'react';\n\nconst useClipboard = () => {\n  const copy = useCallback(async (value: string | number) => {\n    try {\n      // only strings and numbers casted to strings can be copied to clipboard\n      if (typeof value !== 'string' && typeof value !== 'number') {\n        throw new Error(\n          `Cannot copy typeof ${typeof value} to clipboard, must be a string or number`\n        );\n      }\n      // empty strings are also considered invalid\n      else if (value === '') {\n        throw new Error(`Cannot copy empty string to clipboard.`);\n      }\n\n      const stringifiedValue = value.toString();\n\n      await navigator.clipboard.writeText(stringifiedValue);\n\n      return true;\n    } catch (error) {\n      /**\n       * Realistically this isn't useful in production as there's nothing the user can do.\n       */\n      if (process.env.NODE_ENV === 'development') {\n        console.warn('Copy failed', error);\n      }\n\n      return false;\n    }\n  }, []);\n\n  return { copy };\n};\n\nexport { useClipboard };\n", "import {\n  Flex,\n  FlexComponent,\n  FlexProps,\n  Typography,\n  TypographyComponent,\n} from '@strapi/design-system';\nimport { styled } from 'styled-components';\n\ninterface ContentBoxProps {\n  title?: string;\n  subtitle?: string;\n  icon?: FlexProps['children'];\n  iconBackground?: FlexProps['background'];\n  endAction?: FlexProps['children'];\n  titleEllipsis?: boolean;\n}\n\nconst ContentBox = ({\n  title,\n  subtitle,\n  icon,\n  iconBackground,\n  endAction,\n  titleEllipsis = false,\n}: ContentBoxProps) => {\n  if (title && title.length > 70 && titleEllipsis) {\n    title = `${title.substring(0, 70)}...`;\n  }\n\n  return (\n    <Flex shadow=\"tableShadow\" hasRadius padding={6} background=\"neutral0\">\n      <IconWrapper background={iconBackground} hasRadius padding={3}>\n        {icon}\n      </IconWrapper>\n      <Flex direction=\"column\" alignItems=\"stretch\" gap={endAction ? 0 : 1}>\n        <Flex>\n          <TypographyWordBreak fontWeight=\"semiBold\" variant=\"pi\">\n            {title}\n          </TypographyWordBreak>\n          {endAction}\n        </Flex>\n        <Typography textColor=\"neutral600\">{subtitle}</Typography>\n      </Flex>\n    </Flex>\n  );\n};\n\nconst IconWrapper = styled<FlexComponent>(Flex)`\n  margin-right: ${({ theme }) => theme.spaces[6]};\n\n  svg {\n    width: 3.2rem;\n    height: 3.2rem;\n  }\n`;\n\nconst TypographyWordBreak = styled<TypographyComponent>(Typography)`\n  color: ${({ theme }) => theme.colors.neutral800};\n  word-break: break-all;\n`;\n\nexport { ContentBox };\nexport type { ContentBoxProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAEA,IAAMA,eAAe,MAAA;AACnB,QAAMC,WAAOC,0BAAY,OAAOC,UAAAA;AAC9B,QAAI;AAEF,UAAI,OAAOA,UAAU,YAAY,OAAOA,UAAU,UAAU;AAC1D,cAAM,IAAIC,MACR,sBAAsB,OAAOD,KAAAA,2CAAgD;iBAIxEA,UAAU,IAAI;AACrB,cAAM,IAAIC,MAAM,wCAAwC;MAC1D;AAEA,YAAMC,mBAAmBF,MAAMG,SAAQ;AAEvC,YAAMC,UAAUC,UAAUC,UAAUJ,gBAAAA;AAEpC,aAAO;IACT,SAASK,OAAO;AAId,UAAIC,MAAwC;AAC1CC,gBAAQC,KAAK,eAAeH,KAAAA;MAC9B;AAEA,aAAO;IACT;EACF,GAAG,CAAA,CAAE;AAEL,SAAO;IAAET;EAAK;AAChB;;;;AChBA,IAAMa,aAAa,CAAC,EAClBC,OACAC,UACAC,MACAC,gBACAC,WACAC,gBAAgB,MAAK,MACL;AAChB,MAAIL,SAASA,MAAMM,SAAS,MAAMD,eAAe;AAC/CL,YAAQ,GAAGA,MAAMO,UAAU,GAAG,EAAA,CAAA;EAChC;AAEA,aACEC,yBAACC,MAAAA;IAAKC,QAAO;IAAcC,WAAS;IAACC,SAAS;IAAGC,YAAW;;UAC1DC,wBAACC,aAAAA;QAAYF,YAAYV;QAAgBQ,WAAS;QAACC,SAAS;QACzDV,UAAAA;;UAEHM,yBAACC,MAAAA;QAAKO,WAAU;QAASC,YAAW;QAAUC,KAAKd,YAAY,IAAI;;cACjEI,yBAACC,MAAAA;;kBACCK,wBAACK,qBAAAA;gBAAoBC,YAAW;gBAAWC,SAAQ;gBAChDrB,UAAAA;;cAEFI;;;cAEHU,wBAACQ,YAAAA;YAAWC,WAAU;YAActB,UAAAA;;;;;;AAI5C;AAEA,IAAMc,cAAcS,GAAsBf,IAAAA;kBACxB,CAAC,EAAEgB,MAAK,MAAOA,MAAMC,OAAO,CAAA,CAAE;;;;;;;AAQhD,IAAMP,sBAAsBK,GAA4BF,UAAAA;WAC7C,CAAC,EAAEG,MAAK,MAAOA,MAAME,OAAOC,UAAU;;;", "names": ["useClipboard", "copy", "useCallback", "value", "Error", "stringifiedValue", "toString", "navigator", "clipboard", "writeText", "error", "process", "console", "warn", "ContentBox", "title", "subtitle", "icon", "iconBackground", "endAction", "title<PERSON><PERSON><PERSON>", "length", "substring", "_jsxs", "Flex", "shadow", "hasRadius", "padding", "background", "_jsx", "IconWrapper", "direction", "alignItems", "gap", "TypographyWordBreak", "fontWeight", "variant", "Typography", "textColor", "styled", "theme", "spaces", "colors", "neutral800"]}