{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/admin/src/components/FormModal/Input/index.jsx", "../../../@strapi/plugin-users-permissions/admin/src/components/FormModal/index.jsx", "../../../@strapi/plugin-users-permissions/admin/src/pages/Providers/utils/forms.js", "../../../@strapi/plugin-users-permissions/admin/src/pages/Providers/index.jsx"], "sourcesContent": ["/**\n *\n * Input\n *\n */\n\nimport * as React from 'react';\n\nimport { TextInput, Toggle, Field } from '@strapi/design-system';\nimport PropTypes from 'prop-types';\nimport { useIntl } from 'react-intl';\n\nconst Input = ({\n  description,\n  disabled,\n  intlLabel,\n  error,\n  name,\n  onChange,\n  placeholder,\n  providerToEditName,\n  type,\n  value,\n}) => {\n  const { formatMessage } = useIntl();\n  const inputValue =\n    name === 'noName'\n      ? `${window.strapi.backendURL}/api/connect/${providerToEditName}/callback`\n      : value;\n\n  const label = formatMessage(\n    { id: intlLabel.id, defaultMessage: intlLabel.defaultMessage },\n    { provider: providerToEditName, ...intlLabel.values }\n  );\n  const hint = description\n    ? formatMessage(\n        { id: description.id, defaultMessage: description.defaultMessage },\n        { provider: providerToEditName, ...description.values }\n      )\n    : '';\n\n  if (type === 'bool') {\n    return (\n      <Field.Root hint={hint} name={name}>\n        <Field.Label>{label}</Field.Label>\n        <Toggle\n          aria-label={name}\n          checked={value}\n          disabled={disabled}\n          offLabel={formatMessage({\n            id: 'app.components.ToggleCheckbox.off-label',\n            defaultMessage: 'Off',\n          })}\n          onLabel={formatMessage({\n            id: 'app.components.ToggleCheckbox.on-label',\n            defaultMessage: 'On',\n          })}\n          onChange={(e) => {\n            onChange({ target: { name, value: e.target.checked } });\n          }}\n        />\n        <Field.Hint />\n      </Field.Root>\n    );\n  }\n\n  const formattedPlaceholder = placeholder\n    ? formatMessage(\n        { id: placeholder.id, defaultMessage: placeholder.defaultMessage },\n        { ...placeholder.values }\n      )\n    : '';\n\n  const errorMessage = error ? formatMessage({ id: error, defaultMessage: error }) : '';\n\n  return (\n    <Field.Root error={errorMessage} name={name}>\n      <Field.Label>{label}</Field.Label>\n      <TextInput\n        disabled={disabled}\n        onChange={onChange}\n        placeholder={formattedPlaceholder}\n        type={type}\n        value={inputValue}\n      />\n      <Field.Error />\n    </Field.Root>\n  );\n};\n\nInput.defaultProps = {\n  description: null,\n  disabled: false,\n  error: '',\n  placeholder: null,\n  value: '',\n};\n\nInput.propTypes = {\n  description: PropTypes.shape({\n    id: PropTypes.string.isRequired,\n    defaultMessage: PropTypes.string.isRequired,\n    values: PropTypes.object,\n  }),\n  disabled: PropTypes.bool,\n  error: PropTypes.string,\n  intlLabel: PropTypes.shape({\n    id: PropTypes.string.isRequired,\n    defaultMessage: PropTypes.string.isRequired,\n    values: PropTypes.object,\n  }).isRequired,\n  name: PropTypes.string.isRequired,\n  onChange: PropTypes.func.isRequired,\n  placeholder: PropTypes.shape({\n    id: PropTypes.string.isRequired,\n    defaultMessage: PropTypes.string.isRequired,\n    values: PropTypes.object,\n  }),\n  providerToEditName: PropTypes.string.isRequired,\n  type: PropTypes.string.isRequired,\n  value: PropTypes.oneOfType([PropTypes.bool, PropTypes.string]),\n};\n\nexport default Input;\n", "/**\n *\n * FormModal\n *\n */\n\nimport * as React from 'react';\n\nimport { Button, Flex, Grid, Modal, Breadcrumbs, Crumb } from '@strapi/design-system';\nimport { Form, Formik } from 'formik';\nimport PropTypes from 'prop-types';\nimport { useIntl } from 'react-intl';\n\nimport Input from './Input';\n\nconst FormModal = ({\n  headerBreadcrumbs,\n  initialData,\n  isSubmiting,\n  layout,\n  isOpen,\n  onSubmit,\n  onToggle,\n  providerToEditName,\n}) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Modal.Root open={isOpen} onOpenChange={onToggle}>\n      <Modal.Content>\n        <Modal.Header>\n          <Breadcrumbs label={headerBreadcrumbs.join(', ')}>\n            {headerBreadcrumbs.map((crumb, index, arr) => (\n              <Crumb isCurrent={index === arr.length - 1} key={crumb}>\n                {crumb}\n              </Crumb>\n            ))}\n          </Breadcrumbs>\n        </Modal.Header>\n        <Formik\n          onSubmit={(values) => onSubmit(values)}\n          initialValues={initialData}\n          validationSchema={layout.schema}\n          validateOnChange={false}\n        >\n          {({ errors, handleChange, values }) => {\n            return (\n              <Form>\n                <Modal.Body>\n                  <Flex direction=\"column\" alignItems=\"stretch\" gap={1}>\n                    <Grid.Root gap={5}>\n                      {layout.form.map((row) => {\n                        return row.map((input) => {\n                          return (\n                            <Grid.Item\n                              key={input.name}\n                              col={input.size}\n                              xs={12}\n                              direction=\"column\"\n                              alignItems=\"stretch\"\n                            >\n                              <Input\n                                {...input}\n                                error={errors[input.name]}\n                                onChange={handleChange}\n                                value={values[input.name]}\n                                providerToEditName={providerToEditName}\n                              />\n                            </Grid.Item>\n                          );\n                        });\n                      })}\n                    </Grid.Root>\n                  </Flex>\n                </Modal.Body>\n                <Modal.Footer>\n                  <Button variant=\"tertiary\" onClick={onToggle} type=\"button\">\n                    {formatMessage({\n                      id: 'app.components.Button.cancel',\n                      defaultMessage: 'Cancel',\n                    })}\n                  </Button>\n                  <Button type=\"submit\" loading={isSubmiting}>\n                    {formatMessage({ id: 'global.save', defaultMessage: 'Save' })}\n                  </Button>\n                </Modal.Footer>\n              </Form>\n            );\n          }}\n        </Formik>\n      </Modal.Content>\n    </Modal.Root>\n  );\n};\n\nFormModal.defaultProps = {\n  initialData: null,\n  providerToEditName: null,\n};\n\nFormModal.propTypes = {\n  headerBreadcrumbs: PropTypes.arrayOf(PropTypes.string).isRequired,\n  initialData: PropTypes.object,\n  layout: PropTypes.shape({\n    form: PropTypes.arrayOf(PropTypes.array),\n    schema: PropTypes.object,\n  }).isRequired,\n  isOpen: PropTypes.bool.isRequired,\n  isSubmiting: PropTypes.bool.isRequired,\n  onSubmit: PropTypes.func.isRequired,\n  onToggle: PropTypes.func.isRequired,\n  providerToEditName: PropTypes.string,\n};\n\nexport default FormModal;\n", "import { translatedErrors } from '@strapi/strapi/admin';\nimport * as yup from 'yup';\n\nimport { getTrad } from '../../../utils';\n\nconst callbackLabel = {\n  id: getTrad('PopUpForm.Providers.redirectURL.front-end.label'),\n  defaultMessage: 'The redirect URL to your front-end app',\n};\nconst callbackPlaceholder = {\n  id: 'http://www.client-app.com',\n  defaultMessage: 'http://www.client-app.com',\n};\nconst enabledDescription = {\n  id: getTrad('PopUpForm.Providers.enabled.description'),\n  defaultMessage: \"If disabled, users won't be able to use this provider.\",\n};\nconst enabledLabel = {\n  id: getTrad('PopUpForm.Providers.enabled.label'),\n  defaultMessage: 'Enable',\n};\nconst keyLabel = { id: getTrad('PopUpForm.Providers.key.label'), defaultMessage: 'Client ID' };\nconst hintLabel = {\n  id: getTrad('PopUpForm.Providers.redirectURL.label'),\n  defaultMessage: 'The redirect URL to add in your {provider} application configurations',\n};\nconst textPlaceholder = {\n  id: getTrad('PopUpForm.Providers.key.placeholder'),\n  defaultMessage: 'TEXT',\n};\n\nconst secretLabel = {\n  id: getTrad('PopUpForm.Providers.secret.label'),\n  defaultMessage: 'Client Secret',\n};\n\nconst CALLBACK_REGEX = /^$|^[a-z][a-z0-9+.-]*:\\/\\/[^\\s/$.?#](?:[^\\s]*[^\\s/$.?#])?$/i;\nconst SUBDOMAIN_REGEX = /^(([a-zA-Z0-9-]+\\.)*[a-zA-Z0-9-]+)(:\\d+)?(\\/\\S*)?$/i;\n\nconst forms = {\n  email: {\n    form: [\n      [\n        {\n          intlLabel: enabledLabel,\n          name: 'enabled',\n          type: 'bool',\n          description: enabledDescription,\n          size: 6,\n          // TODO check if still needed\n          // validations: {\n          //   required: true,\n          // },\n        },\n      ],\n    ],\n    schema: yup.object().shape({\n      enabled: yup.bool().required(translatedErrors.required.id),\n    }),\n  },\n  providers: {\n    form: [\n      [\n        {\n          intlLabel: enabledLabel,\n          name: 'enabled',\n          type: 'bool',\n          description: enabledDescription,\n          size: 6,\n          validations: {\n            required: true,\n          },\n        },\n      ],\n      [\n        {\n          intlLabel: keyLabel,\n          name: 'key',\n          type: 'text',\n          placeholder: textPlaceholder,\n          size: 12,\n          validations: {\n            required: true,\n          },\n        },\n      ],\n      [\n        {\n          intlLabel: secretLabel,\n          name: 'secret',\n          type: 'text',\n          placeholder: textPlaceholder,\n          size: 12,\n          validations: {\n            required: true,\n          },\n        },\n      ],\n      [\n        {\n          intlLabel: callbackLabel,\n          placeholder: callbackPlaceholder,\n          name: 'callback',\n          type: 'text',\n          size: 12,\n          validations: {\n            required: true,\n          },\n        },\n      ],\n      [\n        {\n          intlLabel: hintLabel,\n          name: 'noName',\n          type: 'text',\n          validations: {},\n          size: 12,\n          disabled: true,\n        },\n      ],\n    ],\n    schema: yup.object().shape({\n      enabled: yup.bool().required(translatedErrors.required.id),\n      key: yup.string().when('enabled', {\n        is: true,\n        then: yup.string().required(translatedErrors.required.id),\n        otherwise: yup.string(),\n      }),\n      secret: yup.string().when('enabled', {\n        is: true,\n        then: yup.string().required(translatedErrors.required.id),\n        otherwise: yup.string(),\n      }),\n      callback: yup.string().when('enabled', {\n        is: true,\n        then: yup\n          .string()\n          .matches(CALLBACK_REGEX, translatedErrors.regex.id)\n          .required(translatedErrors.required.id),\n        otherwise: yup.string(),\n      }),\n    }),\n  },\n  providersWithSubdomain: {\n    form: [\n      [\n        {\n          intlLabel: enabledLabel,\n          name: 'enabled',\n          type: 'bool',\n          description: enabledDescription,\n          size: 6,\n          validations: {\n            required: true,\n          },\n        },\n      ],\n      [\n        {\n          intlLabel: keyLabel,\n          name: 'key',\n          type: 'text',\n          placeholder: textPlaceholder,\n          size: 12,\n          validations: {\n            required: true,\n          },\n        },\n      ],\n      [\n        {\n          intlLabel: secretLabel,\n          name: 'secret',\n          type: 'text',\n          placeholder: textPlaceholder,\n          size: 12,\n          validations: {\n            required: true,\n          },\n        },\n      ],\n      [\n        {\n          intlLabel: {\n            id: getTrad({ id: 'PopUpForm.Providers.jwksurl.label' }),\n            defaultMessage: 'JWKS URL',\n          },\n          name: 'jwksurl',\n          type: 'text',\n          placeholder: textPlaceholder,\n          size: 12,\n          validations: {\n            required: false,\n          },\n        },\n      ],\n\n      [\n        {\n          intlLabel: {\n            id: getTrad('PopUpForm.Providers.subdomain.label'),\n            defaultMessage: 'Host URI (Subdomain)',\n          },\n          name: 'subdomain',\n          type: 'text',\n          placeholder: {\n            id: getTrad('PopUpForm.Providers.subdomain.placeholder'),\n            defaultMessage: 'my.subdomain.com',\n          },\n          size: 12,\n          validations: {\n            required: true,\n          },\n        },\n      ],\n      [\n        {\n          intlLabel: callbackLabel,\n          placeholder: callbackPlaceholder,\n          name: 'callback',\n          type: 'text',\n          size: 12,\n          validations: {\n            required: true,\n          },\n        },\n      ],\n      [\n        {\n          intlLabel: hintLabel,\n          name: 'noName',\n          type: 'text',\n          validations: {},\n          size: 12,\n          disabled: true,\n        },\n      ],\n    ],\n    schema: yup.object().shape({\n      enabled: yup.bool().required(translatedErrors.required.id),\n      key: yup.string().when('enabled', {\n        is: true,\n        then: yup.string().required(translatedErrors.required.id),\n        otherwise: yup.string(),\n      }),\n      secret: yup.string().when('enabled', {\n        is: true,\n        then: yup.string().required(translatedErrors.required.id),\n        otherwise: yup.string(),\n      }),\n      subdomain: yup.string().when('enabled', {\n        is: true,\n        then: yup\n          .string()\n          .matches(SUBDOMAIN_REGEX, translatedErrors.regex.id)\n          .required(translatedErrors.required.id),\n        otherwise: yup.string(),\n      }),\n      callback: yup.string().when('enabled', {\n        is: true,\n        then: yup\n          .string()\n          .matches(CALLBACK_REGEX, translatedErrors.regex.id)\n          .required(translatedErrors.required.id),\n        otherwise: yup.string(),\n      }),\n    }),\n  },\n};\n\nexport default forms;\n", "import * as React from 'react';\n\nimport { useTracking, Layouts } from '@strapi/admin/strapi-admin';\nimport {\n  IconButton,\n  Table,\n  Tbody,\n  Td,\n  Th,\n  Thead,\n  Tr,\n  Typography,\n  VisuallyHidden,\n  useCollator,\n} from '@strapi/design-system';\nimport { Pencil } from '@strapi/icons';\nimport {\n  Page,\n  useAPIErrorHandler,\n  useNotification,\n  useFetchClient,\n  useRBAC,\n} from '@strapi/strapi/admin';\nimport upperFirst from 'lodash/upperFirst';\nimport { useIntl } from 'react-intl';\nimport { useMutation, useQuery, useQueryClient } from 'react-query';\n\nimport FormModal from '../../components/FormModal';\nimport { PERMISSIONS } from '../../constants';\nimport { getTrad } from '../../utils';\n\nimport forms from './utils/forms';\n\nexport const ProvidersPage = () => {\n  const { formatMessage, locale } = useIntl();\n  const queryClient = useQueryClient();\n  const { trackUsage } = useTracking();\n  const [isOpen, setIsOpen] = React.useState(false);\n  const [providerToEditName, setProviderToEditName] = React.useState(null);\n  const { toggleNotification } = useNotification();\n  const { get, put } = useFetchClient();\n  const { formatAPIError } = useAPIErrorHandler();\n  const formatter = useCollator(locale, {\n    sensitivity: 'base',\n  });\n\n  const {\n    isLoading: isLoadingPermissions,\n    allowedActions: { canUpdate },\n  } = useRBAC({ update: PERMISSIONS.updateProviders });\n\n  const { isLoading: isLoadingData, data } = useQuery(\n    ['users-permissions', 'get-providers'],\n    async () => {\n      const { data } = await get('/users-permissions/providers');\n\n      return data;\n    },\n    {\n      initialData: {},\n    }\n  );\n\n  const submitMutation = useMutation((body) => put('/users-permissions/providers', body), {\n    async onSuccess() {\n      await queryClient.invalidateQueries(['users-permissions', 'get-providers']);\n\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({ id: getTrad('notification.success.submit') }),\n      });\n\n      trackUsage('didEditAuthenticationProvider');\n\n      handleToggleModal();\n    },\n    onError(error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    },\n    refetchActive: false,\n  });\n\n  const providers = Object.entries(data)\n    .reduce((acc, [name, provider]) => {\n      const { icon, enabled, subdomain } = provider;\n\n      acc.push({\n        name,\n        icon: icon === 'envelope' ? ['fas', 'envelope'] : ['fab', icon],\n        enabled,\n        subdomain,\n      });\n\n      return acc;\n    }, [])\n    .sort((a, b) => formatter.compare(a.name, b.name));\n\n  const isLoading = isLoadingData || isLoadingPermissions;\n\n  const isProviderWithSubdomain = React.useMemo(() => {\n    if (!providerToEditName) {\n      return false;\n    }\n\n    const providerToEdit = providers.find((obj) => obj.name === providerToEditName);\n\n    return !!providerToEdit?.subdomain;\n  }, [providers, providerToEditName]);\n\n  const layoutToRender = React.useMemo(() => {\n    if (providerToEditName === 'email') {\n      return forms.email;\n    }\n\n    if (isProviderWithSubdomain) {\n      return forms.providersWithSubdomain;\n    }\n\n    return forms.providers;\n  }, [providerToEditName, isProviderWithSubdomain]);\n\n  const handleToggleModal = () => {\n    setIsOpen((prev) => !prev);\n  };\n\n  const handleClickEdit = (provider) => {\n    if (canUpdate) {\n      setProviderToEditName(provider.name);\n      handleToggleModal();\n    }\n  };\n\n  const handleSubmit = async (values) => {\n    trackUsage('willEditAuthenticationProvider');\n\n    submitMutation.mutate({ providers: { ...data, [providerToEditName]: values } });\n  };\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Layouts.Root>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: formatMessage({\n              id: getTrad('HeaderNav.link.providers'),\n              defaultMessage: 'Providers',\n            }),\n          }\n        )}\n      </Page.Title>\n      <Page.Main>\n        <Layouts.Header\n          title={formatMessage({\n            id: getTrad('HeaderNav.link.providers'),\n            defaultMessage: 'Providers',\n          })}\n        />\n        <Layouts.Content>\n          <Table colCount={3} rowCount={providers.length + 1}>\n            <Thead>\n              <Tr>\n                <Th>\n                  <Typography variant=\"sigma\" textColor=\"neutral600\">\n                    {formatMessage({ id: 'global.name', defaultMessage: 'Name' })}\n                  </Typography>\n                </Th>\n                <Th>\n                  <Typography variant=\"sigma\" textColor=\"neutral600\">\n                    {formatMessage({ id: getTrad('Providers.status'), defaultMessage: 'Status' })}\n                  </Typography>\n                </Th>\n                <Th>\n                  <Typography variant=\"sigma\">\n                    <VisuallyHidden>\n                      {formatMessage({\n                        id: 'global.settings',\n                        defaultMessage: 'Settings',\n                      })}\n                    </VisuallyHidden>\n                  </Typography>\n                </Th>\n              </Tr>\n            </Thead>\n            <Tbody>\n              {providers.map((provider) => (\n                <Tr\n                  key={provider.name}\n                  onClick={() => (canUpdate ? handleClickEdit(provider) : undefined)}\n                >\n                  <Td width=\"45%\">\n                    <Typography fontWeight=\"semiBold\" textColor=\"neutral800\">\n                      {provider.name}\n                    </Typography>\n                  </Td>\n                  <Td width=\"65%\">\n                    <Typography\n                      textColor={provider.enabled ? 'success600' : 'danger600'}\n                      data-testid={`enable-${provider.name}`}\n                    >\n                      {provider.enabled\n                        ? formatMessage({\n                            id: 'global.enabled',\n                            defaultMessage: 'Enabled',\n                          })\n                        : formatMessage({\n                            id: 'global.disabled',\n                            defaultMessage: 'Disabled',\n                          })}\n                    </Typography>\n                  </Td>\n                  <Td onClick={(e) => e.stopPropagation()}>\n                    {canUpdate && (\n                      <IconButton\n                        onClick={() => handleClickEdit(provider)}\n                        variant=\"ghost\"\n                        label=\"Edit\"\n                      >\n                        <Pencil />\n                      </IconButton>\n                    )}\n                  </Td>\n                </Tr>\n              ))}\n            </Tbody>\n          </Table>\n        </Layouts.Content>\n      </Page.Main>\n      <FormModal\n        initialData={data[providerToEditName]}\n        isOpen={isOpen}\n        isSubmiting={submitMutation.isLoading}\n        layout={layoutToRender}\n        headerBreadcrumbs={[\n          formatMessage({\n            id: getTrad('PopUpForm.header.edit.providers'),\n            defaultMessage: 'Edit Provider',\n          }),\n          upperFirst(providerToEditName),\n        ]}\n        onToggle={handleToggleModal}\n        onSubmit={handleSubmit}\n        providerToEditName={providerToEditName}\n      />\n    </Layouts.Root>\n  );\n};\n\nconst ProtectedProvidersPage = () => (\n  <Page.Protect permissions={PERMISSIONS.readProviders}>\n    <ProvidersPage />\n  </Page.Protect>\n);\n\nexport default ProtectedProvidersPage;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYMA,IAAAA,QAAQ,CAAC,EACbC,aACAC,UACAC,WACAC,OACAC,MACAC,UACAC,aACAC,oBACAC,MACAC,MAAK,MACN;AACC,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAMC,aACJR,SAAS,WACL,GAAGS,OAAOC,OAAOC,UAAU,gBAAgBR,kBAAmB,cAC9DE;AAEN,QAAMO,QAAQN,cACZ;IAAEO,IAAIf,UAAUe;IAAIC,gBAAgBhB,UAAUgB;KAC9C;IAAEC,UAAUZ;IAAoB,GAAGL,UAAUkB;EAAO,CAAA;AAEtD,QAAMC,OAAOrB,cACTU,cACE;IAAEO,IAAIjB,YAAYiB;IAAIC,gBAAgBlB,YAAYkB;KAClD;IAAEC,UAAUZ;IAAoB,GAAGP,YAAYoB;GAEjD,IAAA;AAEJ,MAAIZ,SAAS,QAAQ;AACnB,eACEc,yBAACC,MAAMC,MAAI;MAACH;MAAYjB;;YACtBqB,wBAACF,MAAMG,OAAK;UAAEV,UAAAA;;YACdS,wBAACE,QAAAA;UACCC,cAAYxB;UACZyB,SAASpB;UACTR;UACA6B,UAAUpB,cAAc;YACtBO,IAAI;YACJC,gBAAgB;UAClB,CAAA;UACAa,SAASrB,cAAc;YACrBO,IAAI;YACJC,gBAAgB;UAClB,CAAA;UACAb,UAAU,CAAC2B,MAAAA;AACT3B,qBAAS;cAAE4B,QAAQ;gBAAE7B;gBAAMK,OAAOuB,EAAEC,OAAOJ;cAAQ;YAAE,CAAA;UACvD;;YAEFJ,wBAACF,MAAMW,MAAI,CAAA,CAAA;;;EAGjB;AAEA,QAAMC,uBAAuB7B,cACzBI,cACE;IAAEO,IAAIX,YAAYW;IAAIC,gBAAgBZ,YAAYY;KAClD;IAAE,GAAGZ,YAAYc;GAEnB,IAAA;AAEJ,QAAMgB,eAAejC,QAAQO,cAAc;IAAEO,IAAId;IAAOe,gBAAgBf;GAAW,IAAA;AAEnF,aACEmB,yBAACC,MAAMC,MAAI;IAACrB,OAAOiC;IAAchC;;UAC/BqB,wBAACF,MAAMG,OAAK;QAAEV,UAAAA;;UACdS,wBAACY,WAAAA;QACCpC;QACAI;QACAC,aAAa6B;QACb3B;QACAC,OAAOG;;UAETa,wBAACF,MAAMe,OAAK,CAAA,CAAA;;;AAGlB;AAEAvC,MAAMwC,eAAe;EACnBvC,aAAa;EACbC,UAAU;EACVE,OAAO;EACPG,aAAa;EACbG,OAAO;AACT;AAEAV,MAAMyC,YAAY;EAChBxC,aAAayC,kBAAAA,QAAUC,MAAM;IAC3BzB,IAAIwB,kBAAAA,QAAUE,OAAOC;IACrB1B,gBAAgBuB,kBAAAA,QAAUE,OAAOC;IACjCxB,QAAQqB,kBAAAA,QAAUI;EACpB,CAAA;EACA5C,UAAUwC,kBAAAA,QAAUK;EACpB3C,OAAOsC,kBAAAA,QAAUE;EACjBzC,WAAWuC,kBAAAA,QAAUC,MAAM;IACzBzB,IAAIwB,kBAAAA,QAAUE,OAAOC;IACrB1B,gBAAgBuB,kBAAAA,QAAUE,OAAOC;IACjCxB,QAAQqB,kBAAAA,QAAUI;EACpB,CAAA,EAAGD;EACHxC,MAAMqC,kBAAAA,QAAUE,OAAOC;EACvBvC,UAAUoC,kBAAAA,QAAUM,KAAKH;EACzBtC,aAAamC,kBAAAA,QAAUC,MAAM;IAC3BzB,IAAIwB,kBAAAA,QAAUE,OAAOC;IACrB1B,gBAAgBuB,kBAAAA,QAAUE,OAAOC;IACjCxB,QAAQqB,kBAAAA,QAAUI;EACpB,CAAA;EACAtC,oBAAoBkC,kBAAAA,QAAUE,OAAOC;EACrCpC,MAAMiC,kBAAAA,QAAUE,OAAOC;EACvBnC,OAAOgC,kBAAAA,QAAUO,UAAU;IAACP,kBAAAA,QAAUK;IAAML,kBAAAA,QAAUE;EAAO,CAAA;AAC/D;;;AC1GA,IAAMM,YAAY,CAAC,EACjBC,mBACAC,aACAC,aACAC,QACAC,QACAC,UACAC,UACAC,mBAAkB,MACnB;AACC,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEC,yBAACC,MAAMC,MAAI;IAACC,MAAMT;IAAQU,cAAcR;kBACtCS,0BAACJ,MAAMK,SAAO;;YACZN,yBAACC,MAAMM,QAAM;UACX,cAAAP,yBAACQ,aAAAA;YAAYC,OAAOnB,kBAAkBoB,KAAK,IAAA;YACxCpB,UAAAA,kBAAkBqB,IAAI,CAACC,OAAOC,OAAOC,YACpCd,yBAACe,OAAAA;cAAMC,WAAWH,UAAUC,IAAIG,SAAS;cACtCL,UAAAA;YAD8CA,GAAAA,KAAAA,CAAAA;;;YAMvDZ,yBAACkB,QAAAA;UACCvB,UAAU,CAACwB,WAAWxB,SAASwB,MAAAA;UAC/BC,eAAe7B;UACf8B,kBAAkB5B,OAAO6B;UACzBC,kBAAkB;UAEjB,UAAA,CAAC,EAAEC,QAAQC,cAAcN,OAAM,MAAE;AAChC,uBACEd,0BAACqB,MAAAA;;oBACC1B,yBAACC,MAAM0B,MAAI;kBACT,cAAA3B,yBAAC4B,MAAAA;oBAAKC,WAAU;oBAASC,YAAW;oBAAUC,KAAK;kCACjD/B,yBAACgC,KAAK9B,MAAI;sBAAC6B,KAAK;sBACbtC,UAAAA,OAAOwC,KAAKtB,IAAI,CAACuB,QAAAA;AAChB,+BAAOA,IAAIvB,IAAI,CAACwB,UAAAA;AACd,qCACEnC,yBAACgC,KAAKI,MAAI;4BAERC,KAAKF,MAAMG;4BACXC,IAAI;4BACJV,WAAU;4BACVC,YAAW;4BAEX,cAAA9B,yBAACwC,OAAAA;8BACE,GAAGL;8BACJM,OAAOjB,OAAOW,MAAMO,IAAI;8BACxBC,UAAUlB;8BACVmB,OAAOzB,OAAOgB,MAAMO,IAAI;8BACxB7C;;0BAXGsC,GAAAA,MAAMO,IAAI;wBAerB,CAAA;sBACF,CAAA;;;;oBAINrC,0BAACJ,MAAM4C,QAAM;;wBACX7C,yBAAC8C,QAAAA;sBAAOC,SAAQ;sBAAWC,SAASpD;sBAAUqD,MAAK;gCAChDnD,cAAc;wBACboD,IAAI;wBACJC,gBAAgB;sBAClB,CAAA;;wBAEFnD,yBAAC8C,QAAAA;sBAAOG,MAAK;sBAASG,SAAS5D;gCAC5BM,cAAc;wBAAEoD,IAAI;wBAAeC,gBAAgB;sBAAO,CAAA;;;;;;UAKrE;;;;;AAKV;AAEA9D,UAAUgE,eAAe;EACvB9D,aAAa;EACbM,oBAAoB;AACtB;AAEAR,UAAUiE,YAAY;EACpBhE,mBAAmBiE,mBAAAA,QAAUC,QAAQD,mBAAAA,QAAUE,MAAM,EAAEC;EACvDnE,aAAagE,mBAAAA,QAAUI;EACvBlE,QAAQ8D,mBAAAA,QAAUK,MAAM;IACtB3B,MAAMsB,mBAAAA,QAAUC,QAAQD,mBAAAA,QAAUM,KAAK;IACvCvC,QAAQiC,mBAAAA,QAAUI;EACpB,CAAA,EAAGD;EACHhE,QAAQ6D,mBAAAA,QAAUO,KAAKJ;EACvBlE,aAAa+D,mBAAAA,QAAUO,KAAKJ;EAC5B/D,UAAU4D,mBAAAA,QAAUQ,KAAKL;EACzB9D,UAAU2D,mBAAAA,QAAUQ,KAAKL;EACzB7D,oBAAoB0D,mBAAAA,QAAUE;AAChC;;;;;;;AC3GA,IAAMO,gBAAgB;EACpBC,IAAIC,QAAQ,iDAAA;EACZC,gBAAgB;AAClB;AACA,IAAMC,sBAAsB;EAC1BH,IAAI;EACJE,gBAAgB;AAClB;AACA,IAAME,qBAAqB;EACzBJ,IAAIC,QAAQ,yCAAA;EACZC,gBAAgB;AAClB;AACA,IAAMG,eAAe;EACnBL,IAAIC,QAAQ,mCAAA;EACZC,gBAAgB;AAClB;AACA,IAAMI,WAAW;EAAEN,IAAIC,QAAQ,+BAAA;EAAkCC,gBAAgB;AAAY;AAC7F,IAAMK,YAAY;EAChBP,IAAIC,QAAQ,uCAAA;EACZC,gBAAgB;AAClB;AACA,IAAMM,kBAAkB;EACtBR,IAAIC,QAAQ,qCAAA;EACZC,gBAAgB;AAClB;AAEA,IAAMO,cAAc;EAClBT,IAAIC,QAAQ,kCAAA;EACZC,gBAAgB;AAClB;AAEA,IAAMQ,iBAAiB;AACvB,IAAMC,kBAAkB;AAExB,IAAMC,QAAQ;EACZC,OAAO;IACLC,MAAM;MACJ;QACE;UACEC,WAAWV;UACXW,MAAM;UACNC,MAAM;UACNC,aAAad;UACbe,MAAM;QAKR;MACD;IACF;IACDC,QAAYC,QAAM,EAAGC,MAAM;MACzBC,SAAaC,OAAI,EAAGC,SAASC,YAAiBD,SAASzB,EAAE;IAC3D,CAAA;EACF;EACA2B,WAAW;IACTb,MAAM;MACJ;QACE;UACEC,WAAWV;UACXW,MAAM;UACNC,MAAM;UACNC,aAAad;UACbe,MAAM;UACNS,aAAa;YACXH,UAAU;UACZ;QACF;MACD;MACD;QACE;UACEV,WAAWT;UACXU,MAAM;UACNC,MAAM;UACNY,aAAarB;UACbW,MAAM;UACNS,aAAa;YACXH,UAAU;UACZ;QACF;MACD;MACD;QACE;UACEV,WAAWN;UACXO,MAAM;UACNC,MAAM;UACNY,aAAarB;UACbW,MAAM;UACNS,aAAa;YACXH,UAAU;UACZ;QACF;MACD;MACD;QACE;UACEV,WAAWhB;UACX8B,aAAa1B;UACba,MAAM;UACNC,MAAM;UACNE,MAAM;UACNS,aAAa;YACXH,UAAU;UACZ;QACF;MACD;MACD;QACE;UACEV,WAAWR;UACXS,MAAM;UACNC,MAAM;UACNW,aAAa,CAAA;UACbT,MAAM;UACNW,UAAU;QACZ;MACD;IACF;IACDV,QAAYC,QAAM,EAAGC,MAAM;MACzBC,SAAaC,OAAI,EAAGC,SAASC,YAAiBD,SAASzB,EAAE;MACzD+B,KAASC,QAAM,EAAGC,KAAK,WAAW;QAChCC,IAAI;QACJC,MAAUH,QAAM,EAAGP,SAASC,YAAiBD,SAASzB,EAAE;QACxDoC,WAAeJ,QAAM;MACvB,CAAA;MACAK,QAAYL,QAAM,EAAGC,KAAK,WAAW;QACnCC,IAAI;QACJC,MAAUH,QAAM,EAAGP,SAASC,YAAiBD,SAASzB,EAAE;QACxDoC,WAAeJ,QAAM;MACvB,CAAA;MACAM,UAAcN,QAAM,EAAGC,KAAK,WAAW;QACrCC,IAAI;QACJC,MACGH,QAAM,EACNO,QAAQ7B,gBAAgBgB,YAAiBc,MAAMxC,EAAE,EACjDyB,SAASC,YAAiBD,SAASzB,EAAE;QACxCoC,WAAeJ,QAAM;MACvB,CAAA;IACF,CAAA;EACF;EACAS,wBAAwB;IACtB3B,MAAM;MACJ;QACE;UACEC,WAAWV;UACXW,MAAM;UACNC,MAAM;UACNC,aAAad;UACbe,MAAM;UACNS,aAAa;YACXH,UAAU;UACZ;QACF;MACD;MACD;QACE;UACEV,WAAWT;UACXU,MAAM;UACNC,MAAM;UACNY,aAAarB;UACbW,MAAM;UACNS,aAAa;YACXH,UAAU;UACZ;QACF;MACD;MACD;QACE;UACEV,WAAWN;UACXO,MAAM;UACNC,MAAM;UACNY,aAAarB;UACbW,MAAM;UACNS,aAAa;YACXH,UAAU;UACZ;QACF;MACD;MACD;QACE;UACEV,WAAW;YACTf,IAAIC,QAAQ;cAAED,IAAI;YAAoC,CAAA;YACtDE,gBAAgB;UAClB;UACAc,MAAM;UACNC,MAAM;UACNY,aAAarB;UACbW,MAAM;UACNS,aAAa;YACXH,UAAU;UACZ;QACF;MACD;MAED;QACE;UACEV,WAAW;YACTf,IAAIC,QAAQ,qCAAA;YACZC,gBAAgB;UAClB;UACAc,MAAM;UACNC,MAAM;UACNY,aAAa;YACX7B,IAAIC,QAAQ,2CAAA;YACZC,gBAAgB;UAClB;UACAiB,MAAM;UACNS,aAAa;YACXH,UAAU;UACZ;QACF;MACD;MACD;QACE;UACEV,WAAWhB;UACX8B,aAAa1B;UACba,MAAM;UACNC,MAAM;UACNE,MAAM;UACNS,aAAa;YACXH,UAAU;UACZ;QACF;MACD;MACD;QACE;UACEV,WAAWR;UACXS,MAAM;UACNC,MAAM;UACNW,aAAa,CAAA;UACbT,MAAM;UACNW,UAAU;QACZ;MACD;IACF;IACDV,QAAYC,QAAM,EAAGC,MAAM;MACzBC,SAAaC,OAAI,EAAGC,SAASC,YAAiBD,SAASzB,EAAE;MACzD+B,KAASC,QAAM,EAAGC,KAAK,WAAW;QAChCC,IAAI;QACJC,MAAUH,QAAM,EAAGP,SAASC,YAAiBD,SAASzB,EAAE;QACxDoC,WAAeJ,QAAM;MACvB,CAAA;MACAK,QAAYL,QAAM,EAAGC,KAAK,WAAW;QACnCC,IAAI;QACJC,MAAUH,QAAM,EAAGP,SAASC,YAAiBD,SAASzB,EAAE;QACxDoC,WAAeJ,QAAM;MACvB,CAAA;MACAU,WAAeV,QAAM,EAAGC,KAAK,WAAW;QACtCC,IAAI;QACJC,MACGH,QAAM,EACNO,QAAQ5B,iBAAiBe,YAAiBc,MAAMxC,EAAE,EAClDyB,SAASC,YAAiBD,SAASzB,EAAE;QACxCoC,WAAeJ,QAAM;MACvB,CAAA;MACAM,UAAcN,QAAM,EAAGC,KAAK,WAAW;QACrCC,IAAI;QACJC,MACGH,QAAM,EACNO,QAAQ7B,gBAAgBgB,YAAiBc,MAAMxC,EAAE,EACjDyB,SAASC,YAAiBD,SAASzB,EAAE;QACxCoC,WAAeJ,QAAM;MACvB,CAAA;IACF,CAAA;EACF;AACF;;;IC3OaW,gBAAgB,MAAA;AAC3B,QAAM,EAAEC,eAAeC,OAAM,IAAKC,QAAAA;AAClC,QAAMC,cAAcC,eAAAA;AACpB,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAM,CAACC,QAAQC,SAAAA,IAAmBC,eAAS,KAAA;AAC3C,QAAM,CAACC,oBAAoBC,qBAAAA,IAA+BF,eAAS,IAAA;AACnE,QAAM,EAAEG,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,KAAKC,IAAG,IAAKC,eAAAA;AACrB,QAAM,EAAEC,eAAc,IAAKC,mBAAAA;AAC3B,QAAMC,YAAYC,YAAYnB,QAAQ;IACpCoB,aAAa;EACf,CAAA;AAEA,QAAM,EACJC,WAAWC,sBACXC,gBAAgB,EAAEC,UAAS,EAAE,IAC3BC,QAAQ;IAAEC,QAAQC,YAAYC;EAAgB,CAAA;AAElD,QAAM,EAAEP,WAAWQ,eAAeC,KAAI,IAAKC,SACzC;IAAC;IAAqB;KACtB,YAAA;AACE,UAAM,EAAED,MAAAA,MAAI,IAAK,MAAMjB,IAAI,8BAAA;AAE3B,WAAOiB;KAET;IACEE,aAAa,CAAA;EACf,CAAA;AAGF,QAAMC,iBAAiBC,YAAY,CAACC,SAASrB,IAAI,gCAAgCqB,IAAO,GAAA;IACtF,MAAMC,YAAAA;AACJ,YAAMlC,YAAYmC,kBAAkB;QAAC;QAAqB;MAAgB,CAAA;AAE1E1B,yBAAmB;QACjB2B,MAAM;QACNC,SAASxC,cAAc;UAAEyC,IAAIC,QAAQ,6BAAA;QAA+B,CAAA;MACtE,CAAA;AAEArC,iBAAW,+BAAA;AAEXsC,wBAAAA;IACF;IACAC,QAAQC,OAAK;AACXjC,yBAAmB;QACjB2B,MAAM;QACNC,SAASvB,eAAe4B,KAAAA;MAC1B,CAAA;IACF;IACAC,eAAe;EACjB,CAAA;AAEA,QAAMC,YAAYC,OAAOC,QAAQlB,IAAAA,EAC9BmB,OAAO,CAACC,KAAK,CAACC,MAAMC,QAAS,MAAA;AAC5B,UAAM,EAAEC,MAAMC,SAASC,UAAS,IAAKH;AAErCF,QAAIM,KAAK;MACPL;MACAE,MAAMA,SAAS,aAAa;QAAC;QAAO;UAAc;QAAC;QAAOA;MAAK;MAC/DC;MACAC;IACF,CAAA;AAEA,WAAOL;EACT,GAAG,CAAA,CAAE,EACJO,KAAK,CAACC,GAAGC,MAAMzC,UAAU0C,QAAQF,EAAEP,MAAMQ,EAAER,IAAI,CAAA;AAElD,QAAM9B,YAAYQ,iBAAiBP;AAEnC,QAAMuC,0BAAgCC,cAAQ,MAAA;AAC5C,QAAI,CAACrD,oBAAoB;AACvB,aAAO;IACT;AAEA,UAAMsD,iBAAiBjB,UAAUkB,KAAK,CAACC,QAAQA,IAAId,SAAS1C,kBAAAA;AAE5D,WAAO,CAAC,EAACsD,iDAAgBR;KACxB;IAACT;IAAWrC;EAAmB,CAAA;AAElC,QAAMyD,iBAAuBJ,cAAQ,MAAA;AACnC,QAAIrD,uBAAuB,SAAS;AAClC,aAAO0D,MAAMC;IACf;AAEA,QAAIP,yBAAyB;AAC3B,aAAOM,MAAME;IACf;AAEA,WAAOF,MAAMrB;KACZ;IAACrC;IAAoBoD;EAAwB,CAAA;AAEhD,QAAMnB,oBAAoB,MAAA;AACxBnC,cAAU,CAAC+D,SAAS,CAACA,IAAAA;EACvB;AAEA,QAAMC,kBAAkB,CAACnB,aAAAA;AACvB,QAAI5B,WAAW;AACbd,4BAAsB0C,SAASD,IAAI;AACnCT,wBAAAA;IACF;EACF;AAEA,QAAM8B,eAAe,OAAOC,WAAAA;AAC1BrE,eAAW,gCAAA;AAEX6B,mBAAeyC,OAAO;MAAE5B,WAAW;QAAE,GAAGhB;QAAM,CAACrB,kBAAAA,GAAqBgE;MAAO;IAAE,CAAA;EAC/E;AAEA,MAAIpD,WAAW;AACb,eAAOsD,yBAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,aACEC,0BAACC,QAAQC,MAAI;;UACXL,yBAACC,KAAKK,OAAK;kBACRlF,cACC;UAAEyC,IAAI;UAAsB0C,gBAAgB;WAC5C;UACE/B,MAAMpD,cAAc;YAClByC,IAAIC,QAAQ,0BAAA;YACZyC,gBAAgB;UAClB,CAAA;QACF,CAAA;;UAGJJ,0BAACF,KAAKO,MAAI;;cACRR,yBAACI,QAAQK,QAAM;YACbC,OAAOtF,cAAc;cACnByC,IAAIC,QAAQ,0BAAA;cACZyC,gBAAgB;YAClB,CAAA;;cAEFP,yBAACI,QAAQO,SAAO;YACd,cAAAR,0BAACS,OAAAA;cAAMC,UAAU;cAAGC,UAAU3C,UAAU4C,SAAS;;oBAC/Cf,yBAACgB,OAAAA;kBACC,cAAAb,0BAACc,IAAAA;;0BACCjB,yBAACkB,IAAAA;wBACC,cAAAlB,yBAACmB,YAAAA;0BAAWC,SAAQ;0BAAQC,WAAU;oCACnCjG,cAAc;4BAAEyC,IAAI;4BAAe0C,gBAAgB;0BAAO,CAAA;;;0BAG/DP,yBAACkB,IAAAA;wBACC,cAAAlB,yBAACmB,YAAAA;0BAAWC,SAAQ;0BAAQC,WAAU;oCACnCjG,cAAc;4BAAEyC,IAAIC,QAAQ,kBAAA;4BAAqByC,gBAAgB;0BAAS,CAAA;;;0BAG/EP,yBAACkB,IAAAA;wBACC,cAAAlB,yBAACmB,YAAAA;0BAAWC,SAAQ;0BAClB,cAAApB,yBAACsB,gBAAAA;sCACElG,cAAc;8BACbyC,IAAI;8BACJ0C,gBAAgB;4BAClB,CAAA;;;;;;;oBAMVP,yBAACuB,OAAAA;kBACEpD,UAAAA,UAAUqD,IAAI,CAAC/C,iBACd0B,0BAACc,IAAAA;oBAECQ,SAAS,MAAO5E,YAAY+C,gBAAgBnB,QAAYiD,IAAAA;;0BAExD1B,yBAAC2B,IAAAA;wBAAGC,OAAM;wBACR,cAAA5B,yBAACmB,YAAAA;0BAAWU,YAAW;0BAAWR,WAAU;0BACzC5C,UAAAA,SAASD;;;0BAGdwB,yBAAC2B,IAAAA;wBAAGC,OAAM;wBACR,cAAA5B,yBAACmB,YAAAA;0BACCE,WAAW5C,SAASE,UAAU,eAAe;0BAC7CmD,eAAa,UAAUrD,SAASD,IAAI;oCAEnCC,SAASE,UACNvD,cAAc;4BACZyC,IAAI;4BACJ0C,gBAAgB;0BAClB,CAAA,IACAnF,cAAc;4BACZyC,IAAI;4BACJ0C,gBAAgB;0BAClB,CAAA;;;0BAGRP,yBAAC2B,IAAAA;wBAAGF,SAAS,CAACM,MAAMA,EAAEC,gBAAe;wBAClCnF,UAAAA,iBACCmD,yBAACiC,YAAAA;0BACCR,SAAS,MAAM7B,gBAAgBnB,QAAAA;0BAC/B2C,SAAQ;0BACRc,OAAM;0BAEN,cAAAlC,yBAACmC,eAAAA,CAAAA,CAAAA;;;;kBA/BF1D,GAAAA,SAASD,IAAI,CAAA;;;;;;;UAyC9BwB,yBAACoC,WAAAA;QACC/E,aAAaF,KAAKrB,kBAAmB;QACrCH;QACA0G,aAAa/E,eAAeZ;QAC5B4F,QAAQ/C;QACRgD,mBAAmB;UACjBnH,cAAc;YACZyC,IAAIC,QAAQ,iCAAA;YACZyC,gBAAgB;UAClB,CAAA;cACAiC,kBAAAA,SAAW1G,kBAAAA;QACZ;QACD2G,UAAU1E;QACV2E,UAAU7C;QACV/D;;;;AAIR;AAEA,IAAM6G,yBAAyB,UAC7B3C,yBAACC,KAAK2C,SAAO;EAACC,aAAa7F,YAAY8F;EACrC,cAAA9C,yBAAC7E,eAAAA,CAAAA,CAAAA;;", "names": ["Input", "description", "disabled", "intlLabel", "error", "name", "onChange", "placeholder", "providerToEditName", "type", "value", "formatMessage", "useIntl", "inputValue", "window", "strapi", "backendURL", "label", "id", "defaultMessage", "provider", "values", "hint", "_jsxs", "Field", "Root", "_jsx", "Label", "Toggle", "aria-label", "checked", "offLabel", "onLabel", "e", "target", "Hint", "formattedPlaceholder", "errorMessage", "TextInput", "Error", "defaultProps", "propTypes", "PropTypes", "shape", "string", "isRequired", "object", "bool", "func", "oneOfType", "FormModal", "headerBreadcrumbs", "initialData", "isSubmiting", "layout", "isOpen", "onSubmit", "onToggle", "providerToEditName", "formatMessage", "useIntl", "_jsx", "Modal", "Root", "open", "onOpenChange", "_jsxs", "Content", "Header", "Breadcrumbs", "label", "join", "map", "crumb", "index", "arr", "Crumb", "isCurrent", "length", "<PERSON><PERSON>", "values", "initialValues", "validationSchema", "schema", "validateOnChange", "errors", "handleChange", "Form", "Body", "Flex", "direction", "alignItems", "gap", "Grid", "form", "row", "input", "<PERSON><PERSON>", "col", "size", "xs", "Input", "error", "name", "onChange", "value", "Footer", "<PERSON><PERSON>", "variant", "onClick", "type", "id", "defaultMessage", "loading", "defaultProps", "propTypes", "PropTypes", "arrayOf", "string", "isRequired", "object", "shape", "array", "bool", "func", "callback<PERSON><PERSON><PERSON>", "id", "getTrad", "defaultMessage", "callbackPlaceholder", "enabledDescription", "enabledLabel", "<PERSON><PERSON><PERSON><PERSON>", "hintLabel", "textPlaceholder", "secretLabel", "CALLBACK_REGEX", "SUBDOMAIN_REGEX", "forms", "email", "form", "intlLabel", "name", "type", "description", "size", "schema", "object", "shape", "enabled", "bool", "required", "translatedErrors", "providers", "validations", "placeholder", "disabled", "key", "string", "when", "is", "then", "otherwise", "secret", "callback", "matches", "regex", "providersWithSubdomain", "subdomain", "ProvidersPage", "formatMessage", "locale", "useIntl", "queryClient", "useQueryClient", "trackUsage", "useTracking", "isOpen", "setIsOpen", "useState", "providerToEditName", "setProviderToEditName", "toggleNotification", "useNotification", "get", "put", "useFetchClient", "formatAPIError", "useAPIErrorHandler", "formatter", "useCollator", "sensitivity", "isLoading", "isLoadingPermissions", "allowedActions", "canUpdate", "useRBAC", "update", "PERMISSIONS", "updateProviders", "isLoadingData", "data", "useQuery", "initialData", "submitMutation", "useMutation", "body", "onSuccess", "invalidateQueries", "type", "message", "id", "getTrad", "handleToggleModal", "onError", "error", "refetchActive", "providers", "Object", "entries", "reduce", "acc", "name", "provider", "icon", "enabled", "subdomain", "push", "sort", "a", "b", "compare", "isProviderWithSubdomain", "useMemo", "providerToEdit", "find", "obj", "layoutToRender", "forms", "email", "providersWithSubdomain", "prev", "handleClickEdit", "handleSubmit", "values", "mutate", "_jsx", "Page", "Loading", "_jsxs", "Layouts", "Root", "Title", "defaultMessage", "Main", "Header", "title", "Content", "Table", "col<PERSON>ount", "rowCount", "length", "<PERSON><PERSON>", "Tr", "Th", "Typography", "variant", "textColor", "VisuallyHidden", "Tbody", "map", "onClick", "undefined", "Td", "width", "fontWeight", "data-testid", "e", "stopPropagation", "IconButton", "label", "Pencil", "FormModal", "isSubmiting", "layout", "headerBreadcrumbs", "upperFirst", "onToggle", "onSubmit", "ProtectedProvidersPage", "Protect", "permissions", "readProviders"]}